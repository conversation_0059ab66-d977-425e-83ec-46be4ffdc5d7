<!--
 * @Description: 零件列表 
 * @Autor: shh
 * @Date: 2022-11-16 16:42:14
 * @LastEditors: shan<PERSON>hong <EMAIL>
 * @LastEditTime: 2024-02-04 11:54:38
-->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      show-selection
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="350"
      :query-param="queryParam"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #picsUrl="slotProps">
        <img
          style="max-width: 100px; max-height: 100px"
          :src="getPicsUrlImg(slotProps.row)"
        />
      </template>
      <!-- <template #saleStatus="slotProps">
        
      </template> -->
    </ProTable>
  </div>
</template>
<script>
import { itemListApi, classifyListApi } from "@/api/goods";
import { isEmpty, cloneDeep } from "lodash";
import { brandListApi } from "@/api/brand";

export default {
  name: "Goods",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      // 列表
      deviceProductTree: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        lastIds: [],
      },
      columns: [
        {
          dataIndex: "categoryId",
          title: "商品分类",
          isSearch: true,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          clearable: true,
          isSearch: true,
          formSpan: 8,
          valueType: "select",
          option: [
            { label: "上架", value: "ON_SALE" },
            { label: "下架", value: "NO_SALE" },
          ],
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          clearable: true,
          isSearch: true,
          formSpan: 16,
          valueType: "input",
        },

        {
          dataIndex: "code",
          title: "商品编号",
          isTable: true,
          isSearch: true,
          formSpan: 16,
          valueType: "input",
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          width: 120,
          tableSlot: "picsUrl",
        },
        {
          dataIndex: "name",
          title: "商品名称",
          isTable: true,
        },
        // {
        //   dataIndex: "商品类型",
        //   title: "商品类型",
        //   isTable: true,
        // },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
        },
        {
          dataIndex: "saleStatus1",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
        },
        {
          dataIndex: "soldOutNum",
          title: "已售卖数量",
          isTable: true,
        },
      ],
      formcolumns: {
        brandList: [],
        categoryList: [],
        saleAttrValsList: [],
        saleStatusList: [
          { label: "上架", value: "ON_SALE" },
          { label: "下架", value: "NO_SALE" },
        ],
      },
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    this.$refs.ProTable.refresh();
    this.init();
  },
  methods: {
    init() {
      brandListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.formcolumns.brandList = (res.data.rows || []).map((item) => ({
          label: item.brandName,
          value: item.id,
        }));
      });
      classifyListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.formcolumns.categoryList = this.columns[0].option = (
          res.data.rows || []
        ).map((item) => ({
          label: item.name,
          value: item.id,
        }));
      });
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    handleSelect(item) {
      this.queryParam.lastIds = [];
      item.map((el) => {
        this.queryParam.lastIds.push(el[el.length - 1]);
      });
    },
    handleChange(item) {
      this.$set(this.form, "productIds", []);
      // console.log(this.$refs.ProductIds.getCheckedNodes(true))
      item.map((el) => {
        this.form.productIds.push(el[el.length - 1]);
      });
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign({}, parameter);
      itemListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows.map((item) => ({
            ...item,
            saleStatus: item.saleStatus === "ON_SALE",
          }));
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },

    handleSelectionChange(row) {
      console.log(row);
      this.$emit("chooseOem", row);
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
