<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-03 14:10:55
 * @Description: 仓库盘点
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="耗材盘点" name="耗材盘点">
        <Consumable />
      </el-tab-pane>
      <el-tab-pane label="机器盘点" name="机器盘点">
        <Machine />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Consumable from "@/views/store/components/inventory/consumableInventory.vue";
import Machine from "@/views/store/components/inventory/machineInventory.vue";
export default {
  name: "Inventory",
  components: { Consumable, Machine },
  data() {
    return {
      activeName: "耗材盘点",
    };
  },
};
</script>

<style scoped lang="scss"></style>
