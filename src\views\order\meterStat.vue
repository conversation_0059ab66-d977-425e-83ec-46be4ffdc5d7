<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="按机型分类" name="first" lazy>
        <meters-stat type="model" :columns="modelColumns" />
      </el-tab-pane>
      <el-tab-pane label="按地区分类" name="second" lazy>
        <meters-stat type="area" :columns="areaColumns" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MetersStat from "@/views/order/components/metersStat.vue";
export default {
  name: "MeterStat",
  components: { MetersStat },
  data() {
    return {
      activeName: "first",
      modelColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "fullIdPath",
          title: "系列",
          isSearch: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "series",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "printCount",
          title: "总印量",
          isTable: true,
        },
        {
          dataIndex: "printCount0",
          title: "总抄表费",
          isTable: true,
        },
        {
          dataIndex: "printCount1",
          title: "全包印量",
          isTable: true,
        },
        {
          dataIndex: "printCount2",
          title: "全包抄表费",
          isTable: true,
        },
        {
          dataIndex: "printCount3",
          title: "半包印量",
          isTable: true,
        },
        {
          dataIndex: "printCount4",
          title: "半包抄表费",
          isTable: true,
        },
        {
          dataIndex: "printCount5",
          title: "租赁印量",
          isTable: true,
        },
        {
          dataIndex: "printCount6",
          title: "租赁抄表费",
          isTable: true,
        },
      ],
      areaColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "printCount",
          title: "总印量",
          isTable: true,
        },
        {
          dataIndex: "printCount0",
          title: "总抄表费",
          isTable: true,
        },
        {
          dataIndex: "printCount1",
          title: "全包印量",
          isTable: true,
        },
        {
          dataIndex: "printCount2",
          title: "全包抄表费",
          isTable: true,
        },
        {
          dataIndex: "printCount3",
          title: "半包印量",
          isTable: true,
        },
        {
          dataIndex: "printCount4",
          title: "半包抄表费",
          isTable: true,
        },
        {
          dataIndex: "printCount5",
          title: "租赁印量",
          isTable: true,
        },
        {
          dataIndex: "printCount6",
          title: "租赁抄表费",
          isTable: true,
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss"></style>
