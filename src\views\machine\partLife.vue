<!--
 * @Author: wskg
 * @Date: 2024-08-12 09:49:37
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 机器 - 寿命统计
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="寿命记录" name="first" lazy>
        <LifeRecord />
      </el-tab-pane>
      <el-tab-pane label="寿命统计" name="second" lazy>
        <LifeStatistics />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import LifeRecord from "@/views/machine/components/lifeRecord.vue";
import LifeStatistics from "@/views/machine/components/lifeStatistics.vue";
export default {
  name: "PartLife",
  components: {
    LifeRecord,
    LifeStatistics,
  },
  data() {
    return {
      activeName: "first",
    };
  },
};
</script>

<style scoped lang="scss"></style>
