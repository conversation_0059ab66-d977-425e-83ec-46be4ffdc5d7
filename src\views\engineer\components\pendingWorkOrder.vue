<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-24 16:03:56
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-05-27 13:49:57
 * @Description: 可接、转派工单列表
 -->
<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :height="380"
      show-rule
      :data="tableData"
      @loadData="loadData"
    >
      <template #rule>
        <div class="rules-tips">
          <h3 class="rule-title">工单指派规则</h3>
          <div class="rule-item">
            <div class="rule-condition">
              <span class="rule-number">1.</span>
              <span class="rule-text">
                当<span class="highlight">工单状态为"待接单"</span>且<span
                  class="highlight"
                  >未分配工程师</span
                >时， 点击<span class="action">"确认指派"</span>按钮将工单<span
                  class="warning"
                  >指派</span
                >给当前工程师
              </span>
            </div>
          </div>

          <div class="rule-item">
            <div class="rule-condition">
              <span class="rule-number">2.</span>
              <span class="rule-text">
                当工单处于
                <span class="highlight">
                  "待接单"、"工程师接单"或"工程师到达"
                </span>
                状态且
                <span class="highlight">已分配工程师</span>时， 点击
                <span class="action">"确认指派"</span>
                按钮将工单
                <span class="warning">转派</span>给当前工程师
              </span>
            </div>
          </div>
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row)">
            查看
          </el-button>
          <el-button
            icon="el-icon-circle-check"
            @click="handleConfirmTransfer(row)"
          >
            确认指派
          </el-button>
        </div>
      </template>
    </ProTable>
    <WorkOrderDetail
      ref="workOrderDetail"
      @refreshWorkOrder="refreshWorkOrder"
    />
  </div>
</template>

<script>
import WorkOrderDetail from "@/views/engineer/components/workOrderDetail.vue";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  changeEngineerApi,
  changeOrderApi,
  getWorkOrderByPageApi,
} from "@/api/repair";

export default {
  name: "PendingWorkOrder",
  components: {
    WorkOrderDetail,
  },
  props: {
    engineerInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 可转排工单列表
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "工单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "customerName",
          title: "客户名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        // {
        //   dataIndex: "customerSeq",
        //   title: "客户编号",
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          dataIndex: "phone",
          title: "手机号码",
          isTable: true,
          width: 110,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组",
          formatter: (row) => row.deviceGroup?.label,
          isTable: true,
        },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isTable: true,
          minWidth: 140,
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => row.serType?.label,
          width: 80,
        },
        {
          dataIndex: "serTypes",
          title: "服务类型",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "散修",
              value: "SCATTERED",
            },
            {
              label: "全保",
              value: "ALL",
            },
            {
              label: "半保",
              value: "HALF",
            },
          ],
        },
        {
          dataIndex: "errorCode",
          title: "故障代码",
          isTable: true,
          // isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "status",
          title: "工单状态",
          valueType: "select",
          isTable: true,
          // isSearch: true,
          multiple: true,
          formatter: (row) =>
            row.cancelStatus && row.status?.value === "close"
              ? "客户取消"
              : row.status?.label,
          option: [
            { label: "待接单", value: "pending_orders" },
            { label: "工程师接单", value: "engineer_receive" },
            { label: "工程师出发", value: "engineer_departure" },
            { label: "工程师到达", value: "engineer_arrive" },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },

        {
          dataIndex: "productIds",
          title: "品牌/机型",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "engineerId",
          title: "工程师",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [],
          formatter: (row) => row.engineerId.name,
        },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          width: 150,
          isTable: true,
          valueType: "date-picker",
          pickerType: "date",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
        },
        // {
        //   dataIndex: "createTimeStartDate",
        //   title: "下单时间",
        //   width: 150,
        //   isSearch: true,
        //   valueType: "date-picker",
        //   pickerType: "daterange",
        //   pickerFormat: "yyyy-MM-dd",
        //   valueFormat: "yyyy-MM-dd",
        // },
        // {
        //   dataIndex: "sendReportTime",
        //   title: "报告提交时间",
        //   width: 150,
        //   isSearch: true,
        //   valueType: "date-picker",
        //   pickerType: "daterange",
        //   pickerFormat: "yyyy-MM-dd",
        //   valueFormat: "yyyy-MM-dd",
        // },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 165,
        },
      ],
      tableData: [],
      // engineerInfo: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    show(info) {
      this.dialogVisible = true;
      // this.engineerInfo = cloneDeep(info);
      this.queryParam = {};
      this.localPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.$nextTick(() => {
        this.refresh();
      });
    },
    /**
     * 加载列表数据
     * @param parameter
     */
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const paramRange = [
        {
          createTimeStart: null,
          createTimeEnd: null,
          data: parameter.createTimeStartDate,
        },
        {
          sendReportTimeStart: null,
          sendReportTimeEnd: null,
          data: parameter.sendReportTime,
        },
      ];
      filterParamRange(this, this.queryParam, paramRange);
      const requestParameters = cloneDeep(this.queryParam);
      requestParameters.status = [
        "pending_orders",
        "engineer_receive",
        "engineer_departure",
        "engineer_arrive",
      ];
      delete requestParameters.createTimeStartDate;
      delete requestParameters.sendReportTime;
      getWorkOrderByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
    },
    handleEdit(row) {
      this.$refs.workOrderDetail.show(row.code);
    },
    handleConfirmTransfer(row) {
      // 等于0没有人接单
      if (Object.keys(row.engineerId).length === 0) {
        this.handleCustomerWorkOrderStatus(row.id, "allot");
      } else {
        this.handleCustomerWorkOrderStatus(row.id, "transfer");
      }
    },
    handleCustomerWorkOrderStatus(workOrderId, type) {
      const confirmText = type === "allot" ? "指派" : "转派";
      this.$confirm(
        `此操作会将工单${confirmText}给${this.engineerInfo?.name}，是否继续？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        const args = {
          engineerId: this.engineerInfo?.id,
          id: workOrderId,
        };
        const editApi = type === "allot" ? changeEngineerApi : changeOrderApi;
        editApi(args).then(() => {
          this.$message.success("操作成功");
          this.refreshWorkOrder();
        });
      });
    },
    refreshWorkOrder() {
      this.refresh();
      this.$emit("refreshWorkOrder");
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
