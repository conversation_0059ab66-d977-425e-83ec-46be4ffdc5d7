<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 11:18:12
 * @Description: 
 -->
<!--
 * @Author: s<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-01-03 12:20:25
 * @LastEditors: shanhaihong <EMAIL>
 * @LastEditTime: 2024-01-29 11:23:40
 * @FilePath: /operate_web/src/views/repair/components/modelFee.vue
 -->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :layout="{ labelWidth: '120px' }"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="523"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </template>
      <template #machine="slotProps">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in slotProps.row.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <el-button slot="reference" size="mini">适用机型</el-button>
        </el-popover>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleChange"
        ></el-cascader>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            v-if="slotProps.row.auditStatus == 'WAIT_AUDIT'"
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            审核
          </el-button>
          <!---->
          <el-button
            v-if="slotProps.row.auditStatus == 'WAIT_AUDIT'"
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>

          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>

    <!-- 新增、编辑、详情框  -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType === 'info'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '160px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
        <!-- <template #startRegionObj>
          {{ form.startRegionObj.arriveRegionFullName }}
        </template>
        <template #arriveRegionObj>
          {{ form.arriveRegionObj.name }}
        </template> -->
        <template #regionAddress>
          <div class="location">
            <el-cascader
              v-model="addressCode"
              :value="addressCode"
              :disabled="methodType === 'info'"
              style="width: 100%"
              filterable
              clearable
              collapse-tags
              :options="options1"
              :props="{
                label: 'name',
                value: 'code',
                children: 'children',
                expandTrigger: 'click',
              }"
              leaf-only
              @change="handleReginChange"
            ></el-cascader>
            <span></span>
          </div>
        </template>
        <template #regionAddress1>
          <div class="location">
            <el-cascader
              v-model="addressCode1"
              :value="addressCode1"
              :disabled="methodType === 'info'"
              style="width: 100%"
              filterable
              clearable
              collapse-tags
              :options="options1"
              :props="{
                label: 'name',
                value: 'code',
                children: 'children',
                expandTrigger: 'click',
              }"
              leaf-only
              @change="handleReginChange1"
            ></el-cascader>
            <span></span>
          </div>
        </template>
      </ProForm>
      <div v-if="methodType === 'info'" class="btn-box">
        <div class="success-button" @click="handleOk('APPROVE', form.id)">
          同意
        </div>
        <div class="cancel-button m-l-40" @click="handleOk('REFUSE', form.id)">
          拒绝
        </div>
      </div>
    </ProDialog>
  </div>
</template>
<script>
import {
  visitPriceByPageApi,
  visitPriceAddApi,
  visitPriceDelApi,
  visitPriceEditApi,
  visitPriceInfoApi,
  visitPriceAuditApi,
} from "@/api/repair";

import { isEmpty, cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
import { productAllApi } from "@/api/dispose";
import { filterName, getAllParentArr } from "@/utils";
import { regionTreeApi } from "@/api/store";

export default {
  name: "DistanceFee",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      options: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      addressCode: [],
      addressCode1: [],
      options1: [],
      queryParam: {},
      productIdName: null,
      columns: [
        {
          dataIndex: "startRegionFullName",
          title: "出发省/市/区",
          width: 180,
          isTable: true,
        },
        {
          dataIndex: "arriveRegionFullName",
          title: "终点省/市/区",
          width: 180,
          isTable: true,
        },

        {
          dataIndex: "priceStart",
          title: "最低远程上门费",
          isSearch: true,
          span: 4,
          valueType: "input",
          inputType: "number",
        },
        {
          dataIndex: "priceEnd",
          title: "最高远程上门费",
          isSearch: true,
          span: 4,
          valueType: "input",
          inputType: "number",
        },
        {
          dataIndex: "price",
          title: "远程上门费",
          isTable: true,
        },
        {
          dataIndex: "auditStatus",
          title: "审核状态",
          width: 140,
          isTable: true,
          clearable: true,
          valueType: "select",
          isSearch: true,
          formatter: (row) =>
            row.auditStatus === "WAIT_AUDIT"
              ? "待审核"
              : row.auditStatus === "APPROVE"
              ? "已通过"
              : "已拒绝",

          span: 4,
          option: [
            {
              label: "待审核",
              value: "WAIT_AUDIT",
            },
            {
              label: "已通过",
              value: "APPROVE",
            },
            {
              label: "已拒绝",
              value: "REFUSE",
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },

        {
          dataIndex: "editBy",
          title: "编辑人",
          width: 140,
          formatter: (row) => row.editBy.name,
          isTable: true,
        },
        {
          dataIndex: "updatedAt",
          title: "最近编辑时间",
          width: 200,
          isTable: true,
        },
        {
          dataIndex: "auditBy",
          title: "审核人",
          width: 140,
          formatter: (row) => row.auditBy.name,
          isTable: true,
        },
        {
          dataIndex: "auditTime",
          title: "审核时间",
          width: 200,
          isTable: true,
        },
        {
          dataIndex: "Actions",
          width: 200,
          title: "操作",
          align: "left",
          tooltip: false,
          isTable: true,
          tableSlot: "actions",
        },
      ],

      //新增
      methodType: "add",
      confirmLoading: false,

      form: {},
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      formcolumns: [],
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
      dictTreeByCodeApi(2100).then((res) => {
        this.goodsTypeOptions = res.data;
      });
      regionTreeApi().then((res) => {
        this.options1 = res.data;
      });
      this.$refs.ProTable.refresh();
    },

    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign({}, parameter);
      if (
        !requestParameters.auditStatus ||
        requestParameters.auditStatus == ""
      ) {
        delete requestParameters.auditStatus;
      }
      if (!requestParameters.priceStart || requestParameters.priceStart == "") {
        delete requestParameters.priceStart;
      }
      if (!requestParameters.priceEnd || requestParameters.priceEnd == "") {
        delete requestParameters.priceEnd;
      }
      visitPriceByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.addressCode = [];
      this.addressCode1 = [];
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.dialogTitle = "新增费用";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;
      this.formcolumns = [
        {
          dataIndex: "startRegion",
          isForm: true,
          title: "出发省/市/区",
          valueType: "input",
          formSlot: "regionAddress",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请选择出发省/市/区",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "arriveRegion",
          isForm: true,
          title: "终点省/市/区",
          formSlot: "regionAddress1",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请选择终点省/市/区",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "price",
          isForm: true,
          title: "远程上门费",
          valueType: "input-number",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入远程上门费",
              trigger: "change",
            },
          ],
        },
      ];
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应新增
    create() {
      visitPriceAddApi(this.form)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑费用";
      this.resetFrom();

      const arr = row.startRegionObj.path.split("/");
      const arr1 = row.arriveRegionObj.path.split("/");
      arr.shift();
      arr1.shift();
      this.addressCode = arr.map((item) => {
        return +item;
      });
      this.addressCode1 = arr1.map((item) => {
        return +item;
      });

      console.log(this.addressCode);
      console.log(this.addressCode1);
      this.form = cloneDeep(row);
      this.form.editBy = this.form.editBy.name;
      this.methodType = "edit";
      this.formcolumns = [
        {
          dataIndex: "startRegion",
          isForm: true,
          title: "出发省/市/区",
          valueType: "input",
          formSlot: "regionAddress",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请选择出发省/市/区",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "arriveRegion",
          isForm: true,
          title: "终点省/市/区",
          formSlot: "regionAddress1",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请选择终点省/市/区",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "price",
          isForm: true,
          title: "远程上门费",
          valueType: "input-number",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入远程上门费",
              trigger: "change",
            },
          ],
        },
      ];
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应编辑
    update() {
      visitPriceEditApi(this.form)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "费用审核";
      this.resetFrom();
      this.form = cloneDeep(row);
      this.form.editBy = this.form.editBy.name;
      this.formcolumns = [
        {
          dataIndex: "startRegionFullName",
          isForm: true,
          title: "出发省/市/区",
          valueType: "text",
          clearable: true,
          formSpan: 24,
        },
        {
          dataIndex: "arriveRegionFullName",
          isForm: true,
          title: "终点省/市/区",
          valueType: "text",
          clearable: true,
          formSpan: 24,
        },
        {
          dataIndex: "price",
          isForm: true,
          title: "远程上门费",
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "editBy",
          title: "编辑人",
          formSpan: 24,
          valueType: "text",
          isForm: true,
        },
        {
          dataIndex: "updatedAt",
          title: "最近编辑时间",
          formSpan: 24,
          valueType: "text",
          isForm: true,
        },
      ];
      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    handleOk(type, id) {
      visitPriceAuditApi({ auditStatus: type, id: id })
        .then(() => {
          this.$message.success("审核成功");
        })
        .finally(() => {
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        visitPriceDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
    handleChange(item) {
      console.log(item);
      this.queryParam.productTreeId = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productTreeId.push(id);
      });
    },
    handleReginChange(val) {
      this.$set(this.form, "startRegion", val[val.length - 1]);
    },
    handleReginChange1(val) {
      this.$set(this.form, "arriveRegion", val[val.length - 1]);
    },
  },
};
</script>
<style>
.el-checkbox {
  line-height: 40px;
}

.el-collapse {
  border: none;
}

.el-collapse-item__header {
  border: none;
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-item__content {
  padding: 0;
}

.el-collapse-item:last-child {
  margin: auto;
}
</style>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tree-li {
  list-style: none;
  margin: 0;

  li {
    list-style: none;
    display: inline-block;
    margin-left: 20px;
  }
}
</style>
