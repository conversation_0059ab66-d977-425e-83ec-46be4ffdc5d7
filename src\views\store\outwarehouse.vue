<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-24 18:24:00
 * @Description:  出库单管理
 -->

<template>
  <div class=" ">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      show-selection
      row-key="outWarehouseId"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="400"
      default-expand-all
      :query-param="queryParam"
      :show-index="false"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #logisticsProvider="{ row }">
        <div style="display: flex; align-items: center">
          <el-select
            v-model="row.logisticsProvider"
            size="mini"
            style="width: 150px"
            :disabled="row.isEditing"
            @change="handleLogisticsChange(row)"
          >
            <el-option
              v-for="item in logisticsProviders"
              :key="item.value"
              :label="item.label"
              :value="item"
            >
            </el-option>
          </el-select>

          <el-button
            v-if="row.isEditing"
            type="text"
            size="mini"
            icon="el-icon-edit"
            @click="startEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="!row.isEditing"
            type="primary"
            size="mini"
            icon="el-icon-check"
            style="margin-left: 5px"
            @click="saveLogistics(row)"
          >
          </el-button>
          <el-button
            v-if="!row.isEditing"
            type="info"
            size="mini"
            icon="el-icon-close"
            style="margin-left: 5px"
            @click="cancelEdit(row)"
          >
          </el-button>
        </div>
      </template>
    </ProTable>
  </div>
</template>
<script>
import { selectOutWarehouse } from "@/api/store";

export default {
  name: "OutWarehouse",
  components: {},
  mixins: [],
  props: {
    chooseModelSelection: {
      type: Array,
      default: () => {
        return [];
      },
    },
    warehouseId: {
      type: String,
      default: null,
    },
  },

  data() {
    return {
      productIdName: [],
      // 列表
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      queryParam: {
        fullIdPath: null,
        name: null,
      },
      columns: [
        {
          dataIndex: "outWarehouseId",
          title: "出库单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },

        {
          dataIndex: "shopWaybill",
          title: "关联单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "receiverName",
          title: "收货人",
          isTable: true,
          // isSearch: true,
          // valueType: "input",
        },
        {
          dataIndex: "consigneePhone",
          title: "收货人手机号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "outType",
          title: "出库方式",
          isTable: true,
          formatter: (row) => row.outType?.label,
        },
        {
          dataIndex: "logisticsProvider",
          title: "配送方式",
          isTable: true,
          tableSlot: "logisticsProvider",
          width: 240,
        },
      ],
      logisticsProviders: [
        { label: "京东快递", value: "jdl" },
        { label: "闪送", value: "iss" },
        { label: "自提", value: "self" },
        { label: "工程师带", value: "passing" },
      ],
      editRows: new Set(),
    };
  },

  methods: {
    handleSelect(item) {
      this.form.parentId = item[item.length - 1];
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter, {
        warehouseId: this.warehouseId,
      });
      if (this.productIdName) {
        requestParameters.fullIdPath =
          this.productIdName[this.productIdName.length - 1];
      } else {
        requestParameters.fullIdPath = null;
      }
      selectOutWarehouse({
        ...requestParameters,
        partId: this.chooseModelSelection.id || null,
      })
        .then((res) => {
          this.tableData = res.data.rows;
          this.tableData.forEach((row) => {
            // 为每项添加编辑状态标识
            // row.isEditing = true;
            this.$set(row, "isEditing", true);
            row._originalLogisticsProvider = null;

            if (
              row.logisticsProvider &&
              typeof row.logisticsProvider === "string"
            ) {
              // 如果是字符串，转换为对象格式
              const provider = this.logisticsProviders.find(
                (p) => p.value === row.logisticsProvider
              );
              row.logisticsProvider = provider || {
                label: row.logisticsProvider,
                value: row.logisticsProvider,
              };
            } else if (row.logisticsProvider && row.logisticsProvider.value) {
              // 已经是对象格式，无需处理
            } else {
              // 默认值处理
              row.logisticsProvider = { label: "", value: "" };
            }
          });
          console.log(this.tableData);
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    startEdit(row) {
      // 保存原始值以便取消时恢复
      row._originalLogisticsProvider = { ...row.logisticsProvider };
      row.isEditing = false;
      console.log(this.tableData);
    },
    handleLogisticsChange(row) {},
    saveLogistics(row) {
      this.$message.success("保存成功");
      row.isEditing = true;
      row._originalLogisticsProvider = null;
    },
    cancelEdit(row) {
      // 恢复原始值
      if (row._originalLogisticsProvider) {
        row.logisticsProvider = { ...row._originalLogisticsProvider };
        row._originalLogisticsProvider = null;
      }
      row.isEditing = true;
    },
    handleSelectionChange(row) {
      this.$emit("choose", row);
    },
  },
};
</script>

<style scoped lang="scss">
.logistics-edit-container {
  display: flex;
  align-items: center;

  .el-select {
    margin-right: 5px;
  }

  .el-button + .el-button {
    margin-left: 5px;
  }
}
</style>
