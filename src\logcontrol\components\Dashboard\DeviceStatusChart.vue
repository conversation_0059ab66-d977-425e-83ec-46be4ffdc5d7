<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 设备状态分布图表组件
-->
<template>
  <div class="device-status-chart">
    <div ref="chart" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'DeviceStatusChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    data: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    loading(val) {
      if (this.chart) {
        if (val) {
          this.chart.showLoading()
        } else {
          this.chart.hideLoading()
        }
      }
    }
  },
  mounted() {
    // 延迟初始化，确保容器尺寸已确定
    this.$nextTick(() => {
      setTimeout(() => {
        this.initChart()
      }, 100)
    })
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart()
      
      if (this.loading) {
        this.chart.showLoading()
      }
    },
    
    updateChart() {
      if (!this.chart) return

      // 确保图表尺寸正确
      this.chart.resize()

      const statusColors = {
        'online': '#67C23A',
        'offline': '#F56C6C',
        'inactive': '#E6A23C'
      }
      
      const statusNames = {
        'online': '在线',
        'offline': '离线',
        'inactive': '不活跃'
      }
      
      const chartData = this.data.map(item => ({
        name: statusNames[item.status] || item.status,
        value: item.count,
        itemStyle: {
          color: statusColors[item.status] || '#909399'
        }
      }))
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: chartData.map(item => item.name)
        },
        series: [
          {
            name: '设备状态',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      }
      
      this.chart.setOption(option)
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.device-status-chart {
  width: 100%;
  height: 100%;

  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 220px;
    max-height: 260px;
  }
}
</style>
