<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:58:43
 * @Description: 客户管理
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :reserve-selection="true"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #btn>
        <div class="btn_container">
          <div class="btn_left">
            <el-button
              type="success"
              class="add-btn"
              size="mini"
              icon="el-icon-plus"
              @click="handleEdit(null, 'add')"
            >
              新增客户
            </el-button>
          </div>
          <div class="btn_right">
            <div>客户数量：{{ totalInfo?.totalCustomerNum || 0 }}</div>
            <div>机器数量：{{ totalInfo?.totalDeviceNum || 0 }}</div>
          </div>
        </div>
        <!-- <el-button
          plain
          type="primary"
          class="add-btn"
          size="mini"
          icon="el-icon-share"
          @click="exportCustom"
          >
          导出
          </el-button
        > -->
      </template>

      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 100%"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>

      <template #keyword1>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>

      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleEdit(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            v-auth="['@ums:manage:customer:delete']"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <EditCustom ref="editCustom" @refresh="refresh" />
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import {
  deleteCustomerApi,
  getCustomerByPageApi,
  memberLevelApi,
  getCustomerTotalApi,
} from "@/api/customer";
import { dictTreeByCodeApi, roleMemberApi } from "@/api/user";
import { Message, MessageBox } from "element-ui";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { exportExcel } from "@/utils/exportExcel";
import EditCustom from "@/views/custom/editCustom/index.vue";
import { productAllApi, productThirdApi } from "@/api/dispose";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
export default {
  name: "Custom",
  components: { ProTable, EditCustom },
  data() {
    const self = this;
    return {
      tableData: [],
      columns: [
        {
          dataIndex: "seqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          isExport: true,
          valueType: "input",
          clearable: true,
          minWidth: 150,
        },
        {
          dataIndex: "name",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        // {
        //   dataIndex: "shopRecruitment",
        //   title: "店铺名称",
        //   isTable: true,
        //   formatter: (row) =>
        //     row.subbranch
        //       ? `${row.shopRecruitment}-${row.subbranch}`
        //       : row.shopRecruitment,
        //   isSearch: false,
        //   isExport: true,
        //   valueType: "input",
        //   clearable: true,
        //   minWidth: 180,
        // },
        {
          dataIndex: "licence",
          title: "营业执照名称",
          isTable: false,
          isSearch: true,
          valueType: "input",
          span: 16,
        },

        {
          dataIndex: "source",
          title: "客户来源",
          isTable: true,
          isSearch: true,
          isExport: true,
          width: 120,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(2300),
          formatter: (row) => row.source?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "businessStatus",
          title: "经营状态",
          // formatter: (row) => row.businessStatus?.label,
          // isTable: true,
          clearable: true,
          isExport: true,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "status",
          title: "客户状态",
          // formatter: (row) => row.status?.label,
          clearable: true,
          isExport: true,
          valueType: "select",
          isSearch: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(100),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "industryAttr",
          title: "行业属性",
          // formatter: (row) => row.industryAttr?.label,
          // isTable: true,
          isSearch: true,
          // isExport: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(300),
          optionskey: {
            label: "label",
            value: "value",
          },
          clearable: true,
        },
        {
          dataIndex: "province",
          title: "省份",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市区",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区县",
          isTable: true,
        },

        {
          dataIndex: "groupName",
          title: "集团名称",
          isTable: true,
          formatter: (row) => row.groupName || "/",
          isSearch: true,
          isExport: true,
          valueType: "input",
          clearable: true,
          minWidth: 150,
        },
        // {
        //   dataIndex: "deviceSeqId",
        //   title: "机器编号",
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          dataIndex: "membershipLevel",
          title: "客户等级",
          formatter: (row) => row.membershipLevel?.label,
          isTable: true,
          isSearch: true,
          clearable: true,
          isExport: true,
          valueType: "select",
          option: [],
          optionMth: () => memberLevelApi(),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "businessStatus",
          title: "经营状态",
          isTable: true,
          formatter: (row) => row.businessStatus?.label,
        },
        {
          dataIndex: "industryAttr",
          title: "行业属性",
          isTable: true,
          formatter: (row) => row.industryAttr?.label,
        },
        {
          dataIndex: "keyword1",
          title: "所属省市区",
          isSearch: true,
          searchSlot: "keyword1",
        },
        {
          dataIndex: "address",
          title: "地址",
          // isTable: false,
          isSearch: true,
          valueType: "input",
          span: 16,
        },

        // {
        //   dataIndex: "machineNum",
        //   title: "机器数量",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        // },
        // {
        //   dataIndex: "cliMechineNum",
        //   title: "客户端安装数",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "blackWhitePoint",
        //   title: "上月黑白印量",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "colorPoint",
        //   title: "上月彩色印量",
        //   isTable: true,
        // },

        // {
        //   dataIndex: "longitude",
        //   title: "经度",
        //   isTable: false,
        //   isSearch: true,
        //   valueType: "input",
        //   span: 16,
        //   disabled: true,
        // },
        // {
        //   dataIndex: "latitude",
        //   title: "维度",
        //   isTable: false,
        //   isSearch: true,
        //   valueType: "input",
        //   span: 16,
        //   disabled: true,
        // },

        // {
        //   dataIndex: "fullIdPath",
        //   isSearch: true,
        //   clearable: true,
        //   searchSlot: "fullIdPath",
        //   title: "品牌/产品树/系列",
        //   valueType: "select",
        // },
        // {
        //   dataIndex: "cliMechineNum",
        //   title: "客户端安装数",
        //   isSearch: true,
        //   valueType: "inputRange",
        // },
        // {
        //   dataIndex: "businessman",
        //   title: "技术",
        //   isTable: true,
        // },

        // {
        //   dataIndex: "salesman",
        //   title: "销售",
        //   isTable: true,
        // },

        // {
        //   dataIndex: "updatedByName",
        //   title: "编辑人",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "blackWhitePoint",
        //   title: "黑白印量",
        //   isSearch: true,
        //   valueType: "inputRange",
        // },
        // {
        //   dataIndex: "colorPoint",
        //   title: "彩色印量",
        //   isSearch: true,
        //   valueType: "inputRange",
        // },
        {
          dataIndex: "createdAt",
          title: "入驻时间",
          isTable: true,
          isSearch: true,
          isExport: false,
          width: 150,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          // attrs: {
          //   change(val) {
          //     if (!val) {
          //       self.queryParam.startDate = null;
          //       self.queryParam.endDate = null;
          //       self.queryParam.updatedAt = null;
          //       return;
          //     }
          //     self.queryParam.startDate = val[0];
          //     self.queryParam.endDate = val[1];
          //     self.queryParam.updatedAt = val;
          //   },
          // },
          clearable: true,
        },
        {
          dataIndex: "phone",
          title: "手机号",
          isTable: false,
          isSearch: true,
          valueType: "input",
          inputType: "number",
        },
        {
          dataIndex: "vxNikeName",
          title: "微信昵称",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "vxGroupName",
          title: "答疑群",
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(2600),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "salesmanId",
          title: "销售",
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
        },
        {
          dataIndex: "businessmanId",
          title: "技术",
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
        },
        {
          dataIndex: "settleMethod",
          title: "结算方式",
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [
            {
              value: 1,
              label: "现结",
            },
            {
              value: 2,
              label: "月付",
            },
            {
              value: 3,
              label: "第三方支付",
            },
            {
              value: 4,
              label: "预充抵扣",
            },
          ],
        },
        {
          dataIndex: "regCliState",
          title: "安装客户端",
          isSearch: true,
          placeholder: "是否安装客户端",
          valueType: "select",
          clearable: true,
          option: [
            {
              value: "1",
              label: "是",
            },
            {
              value: "0",
              label: "否",
            },
          ],
        },
        {
          dataIndex: "serTypes",
          title: "服务类型",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "散修",
              value: "SCATTERED",
            },
            {
              label: "购机不保",
              value: "NO_WARRANTY",
            },
            {
              label: "购机质保",
              value: "WARRANTY",
            },
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "质保服务",
              value: "QA",
            },
            {
              label: "质保含部件",
              value: "QA_COMPONENT",
            },
            {
              label: "维保服务",
              value: "MAINTENANCE",
            },
            {
              label: "其它",
              value: "OTHER",
            },
          ],
        },
        {
          dataIndex: "action",
          tableSlot: "action",
          width: 200,
          title: "操作",
          tooltip: false,
          isTable: true,
          isSearch: false,
          fixed: "right",
        },
      ],
      productIdName: [],
      queryParam: {
        lastIds: [],
        // businessStatus: "201",
        // industryAttr: "301",
      },
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      selection: [],
      options: [],
      totalInfo: {},
      saleList: [],
      workerList: [],
    };
  },

  mounted() {
    this.getProductThird();
    this.refresh();
    this.opratortList();
  },
  methods: {
    async loadData(params) {
      try {
        this.queryParam = filterParam(
          Object.assign({}, this.queryParam, params)
        );
        const res = [
          {
            startDate: null,
            endDate: null,
            data: params.updatedAt,
          },
          {
            beginMachineNum: null,
            endMachineNum: null,
            data: params.machineNum,
          },
          {
            beginCliMachineNum: null,
            endCliMachineNum: null,
            data: params.cliMechineNum,
          },
        ];
        filterParamRange(this, this.queryParam, res);
        const requestParameters = cloneDeep(this.queryParam);

        delete requestParameters.updatedAt;
        delete requestParameters.machineNum;
        delete requestParameters.cliMechineNum;
        const result = await getCustomerByPageApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
        await this.getCustomerTotal(requestParameters);
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    opratortList() {
      roleMemberApi("1787125965588070402", {
        pageNumber: 1,
        pageSize: 10000,
      }).then((res) => {
        res.data.rows.map((item) => {
          this.saleList.push({
            value: item.id,
            label: item.name,
          });
        });
      });
      this.columns.forEach((item) => {
        if (item.dataIndex === "salesmanId") {
          item.option = this.saleList;
        }
      });
      // 技术支持
      roleMemberApi("1002", { pageNumber: 1, pageSize: 9999 }).then((res) => {
        res.data.rows.map((item) => {
          this.workerList.push({
            value: item.id,
            label: item.name,
          });
        });
      });
      this.columns.forEach((item) => {
        if (item.dataIndex === "businessmanId") {
          item.option = this.workerList;
        }
      });
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    handleDelete(row) {
      MessageBox.confirm("客户删除后，所有数据将不可恢复。确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const result = await deleteCustomerApi(row.id);
          if (result.code === 200) {
            Message.success("删除成功");
            this.refresh();
          }
        })
        .catch(() => {
          console.log("取消删除");
        });
    },
    handleEdit(row, type) {
      this.$refs.editCustom.open(type, row || null);
    },
    handleRecord(row) {},
    refresh() {
      this.$refs.ProTable.refresh();
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },
    /**
     * @description 获取客户及机器数量
     * @returns {Promise<void>}
     */
    async getCustomerTotal(params) {
      try {
        const result = await getCustomerTotalApi(params);
        if (result.code === 200 && result.data) {
          this.totalInfo = result.data;
        }
      } catch (e) {
        Message.error(e.message);
      }
    },
    /**
     * @description 获取省市区区域数据
     * @param node
     * @param {Function} resolve
     * @returns {Promise<void>}
     */
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 处理省市区数据
     * @param list
     * @returns {*}
     */
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    handleSelectionChange(val) {
      this.selection = val;
    },
    exportCustom() {
      if (this.selection.length === 0) {
        Message.warning("请选择要导出的客户数据");
        return;
      }
      const fieldMap = {};
      this.columns
        .filter((item) => item.isExport === true)
        .map((item) => {
          fieldMap[item.dataIndex] = item.title;
        });
      exportExcel(this.selection, fieldMap, "客户列表");
    },
    handleSelect(arr) {
      this.queryParam.lastIds = arr.map((item) => item[item.length - 1]);
    },
  },
};
</script>

<style lang="scss" scoped>
.custom {
  width: 100%;
}
.btn_container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .btn_right {
    font-size: 16px;
    color: #6488cf;
    display: flex;
    gap: 20px;
    margin-right: 20px;
  }
}
</style>
