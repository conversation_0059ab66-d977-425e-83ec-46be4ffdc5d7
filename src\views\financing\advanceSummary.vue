<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-08-01 16:56:01
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-01 17:25:38
 * @Description: 预付账款汇总
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #periodAdjust="{ row }">
        <el-input-number
          v-model="row.periodAdjust"
          placeholder="调整金额"
          size="small"
          :controls="false"
          :disabled="!row.isedit"
          style="width: 90px"
          @click="row.isedit = true"
        ></el-input-number>

        <el-link
          v-if="!row.isedit"
          style="margin-left: 10px"
          icon="el-icon-edit"
          :underline="false"
          @click="editAdjust(row)"
        >
          编辑
        </el-link>
        <el-link
          v-if="row.isedit"
          style="margin-left: 10px"
          :underline="false"
          @click="handleAdjust(row)"
        >
          保存
        </el-link>
        <el-link
          v-if="row.isedit"
          style="margin-left: 10px"
          :underline="false"
          type="info"
          @click="handleAdjust()"
        >
          取消
        </el-link>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import {
  consumableAdvanceSummaryApi,
  payableSummaryAdjustApi,
} from "@/api/finance";

export default {
  name: "AdvanceSummary",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "manufacturerCode",
          title: "供应商编号",
          isSearch: true,
          isTable: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 200,
        },
        {
          dataIndex: "periodAmount",
          title: "期初余额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "periodAdjust",
          title: "期初调整金额",
          isTable: true,
          tableSlot: "periodAdjust",
          width: 200,
        },
        {
          dataIndex: "periodAmountAdd",
          title: "预付增加",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "periodAmountDis",
          title: "预付减少",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "terminalAmount",
          title: "预付余额",
          isTable: true,
          minWidth: 100,
        },
      ],
      tableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameter = cloneDeep(this.queryParam);
      consumableAdvanceSummaryApi(requestParameter)
        .then((res) => {
          this.tableData = res.data.rows;
          this.tableData.map((ele) => {
            ele.isedit = ele.id === this.editId;
          });
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    editAdjust(row) {
      this.editId = row.id;
      this.$refs.ProTable.refresh();
    },
    handleAdjust(row) {
      if (row) {
        const currentMonth = new Date().getMonth() + 1;
        if (
          row.yearMonths !==
          `${new Date().getFullYear()}-${
            currentMonth < 10 ? "0" + currentMonth : currentMonth
          }`
        ) {
          this.$message.error("只能调整当前月份的期初余额");
          return;
        }
        payableSummaryAdjustApi({
          id: row.id,
          periodAdjust: row.periodAdjust,
        }).then(() => {
          this.$message.success("修改成功");
          this.editId = null;
          this.refresh();
        });
      } else {
        this.editId = null;
        this.refresh();
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
