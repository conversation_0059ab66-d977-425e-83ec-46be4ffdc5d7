<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 13:58:21
 * @Description: 
 -->
<template>
  <ProDrawer
    :value="showDrawer"
    :title="'申请退货'"
    size="85%"
    :destroy-on-close="true"
    :confirm-text="'确认退货'"
    @ok="handleDrawerOk"
    @cancel="handleCloseDrawer"
  >
    <ProForm
      ref="ProForm"
      :form-param="form"
      :form-list="formColumns"
      :confirm-loading="formLoading"
      :layout="{ formWidth: '100%', labelWidth: '140px' }"
      :open-type="editType"
      @proSubmit="proSubmit"
    >
      <template #returnParams>
        <div class="title-box" style="margin: 0">采购明细</div>
        <ProTable
          :columns="columns"
          :data="tableData"
          :show-search="false"
          :show-setting="false"
          :show-loading="false"
          :height="480"
        >
          <template #currNum="{ row }">
            <el-input-number
              v-model="row.currNum"
              size="small"
              :min="0"
              style="width: 100%"
              controls-position="right"
              @change="(e) => handleRefundNumChange(e, row)"
            ></el-input-number>
          </template>
          <template #reason="{ row }">
            <el-input
              v-model="row.reason"
              type="textarea"
              :rows="1"
              placeholder="请输入退货原因"
              size="small"
              style="width: 100%"
            ></el-input>
          </template>
        </ProTable>
      </template>
    </ProForm>
  </ProDrawer>
</template>

<script>
import { applyReturnApi, getReturnDetailInfoApi } from "@/api/manufacturer";
import { mulAmount } from "@/utils";
import { Message } from "element-ui";

export default {
  name: "ReturnGoods",
  data() {
    return {
      showDrawer: false,
      drawerTitle: "",
      editType: "add",
      form: {},
      formColumns: [
        {
          dataIndex: "purchaseCode",
          title: "采购申请单编号",
          isForm: true,
          formSpan: 24,
          valueType: "text",
        },
        {
          dataIndex: "warehouseName",
          title: "退货仓库",
          isForm: true,
          formSpan: 24,
          valueType: "text",
        },
        {
          dataIndex: "account",
          title: "退款账户名",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入退款账户名",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "bank",
          title: "退款开户行",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入退款开户行",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "bankClient",
          title: "退款开户网点",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入退款开户网点",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "bankAccount",
          title: "退款银行账号",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入退款银行账号",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "refundType",
          title: "退款类型",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          option: [
            {
              label: "现金退款",
              value: "CASH",
            },
            {
              label: "冲抵货款",
              value: "GOODS",
            },
            // {
            //   label: "无需退款",
            //   value: "NO_REFUND",
            // },
          ],
          prop: [
            {
              required: true,
              message: "请选择退款类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "returnAmount",
          title: "退货金额",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "returnParams",
          title: "商品详情",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "returnParams",
        },
      ],
      formLoading: false,
      columns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          width: 160,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "receiptCode",
          title: "电子回单号",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "采购数量",
          isTable: true,
        },
        {
          dataIndex: "deliveryNum",
          title: "发货数量",
          isTable: true,
        },
        {
          dataIndex: "receiveNum",
          title: "已收数量",
          isTable: true,
        },
        {
          dataIndex: "refundNum",
          title: "已退数量",
          isTable: true,
        },
        {
          dataIndex: "currNum",
          title: "退货数量",
          isTable: true,
          tableSlot: "currNum",
          width: 120,
        },
        {
          dataIndex: "refundAmount",
          title: "退货金额",
          isTable: true,
        },
        {
          dataIndex: "reason",
          title: "退货原因",
          isTable: true,
          tableSlot: "reason",
          width: 200,
        },
      ],
      tableData: [],
    };
  },
  methods: {
    show(row) {
      this.form = {};
      this.showDrawer = true;
      getReturnDetailInfoApi(row.id).then((res) => {
        this.form = res.data;
        this.tableData = res.data.returnParams;
      });
    },
    handleDrawerOk() {
      this.$refs.ProForm.handleSubmit();
    },
    proSubmit(val) {
      const data = this.tableData.filter((item) => item.currNum !== 0);
      if (data.length === 0) {
        return Message.warning("退货数量不能为0");
      }
      this.$confirm("是否确认退货", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const params = {
          ...this.form,
          returnParams: data,
        };
        applyReturnApi(params).then((res) => {
          Message.success("申请退货成功");
          this.handleCloseDrawer();
        });
      });
    },
    handleCloseDrawer() {
      this.tableData = [];
      this.showDrawer = false;
    },
    handleRefundNumChange(val, row) {
      row.refundAmount = mulAmount(val, row.price);
      let total = 0;
      this.tableData.forEach((item) => {
        total += mulAmount(item.price, item.currNum);
      });
      if (!this.form.refundAmount) {
        this.$set(this.form, "returnAmount", total.toFixed(2));
      }
      this.form.returnAmount = total;
    },
  },
};
</script>

<style scoped lang="scss"></style>
