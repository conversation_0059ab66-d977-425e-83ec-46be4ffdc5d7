<!--
 * @Author: wskg
 * @Date: 2024-08-15 09:22:56
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 15:55:21
 * @Description: 退货明细查询
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>退货总数量：{{ details?.returnAmount || 0 }}</div>
          <div>退货总金额：{{ details?.returnNum || 0 }}</div>
        </div>
      </template>
      <template #machine="slotProps">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in slotProps.row.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-button slot="reference" type="text" size="mini">
            适用机型
          </el-button>
        </el-popover>
      </template>
      <!-- <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            icon="el-icon-view"
            size="mini"
            @click="handleEdit(row, 'edit')"
            >查看</el-button
          >
        </div>
      </template> -->
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { returnDetailListApi, returnDetailTotalApi } from "@/api/manufacturer";

export default {
  name: "ReturnDetail",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 200,
        },
        {
          dataIndex: "machine",
          title: "适用机型",
          isTable: true,
          tableSlot: "machine",
          width: 100,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "buyNumber",
          title: "采购数量",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "金额",
          isTable: true,
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "createdAt",
          title: "退货时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          width: 150,
        },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   tableSlot: "actions",
        // },
      ],
      tableData: [],
      details: {},
    };
  },
  mounted() {
    this.refresh();
    // 退货统计
    returnDetailTotalApi({ pageSize: 10, pageNumber: 1 }).then((res) => {
      this.details = res.data;
    });
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          returnTimeStart: null,
          returnTimeEnd: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      returnDetailListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.statistics {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex: 1;
  gap: 20px;
  margin-right: 20px;
  div {
    text-wrap: nowrap;
    font-weight: bold;
    font-size: 16px;
    color: red;
    text-align: left;
  }
}
</style>
