<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-14 17:55:38
 * @Description: 
 -->
<!-- 通用Form -->
<template>
  <el-form
    v-if="showForm"
    ref="ProForm"
    v-loading="confirmLoading"
    :model="originalFormParams"
    :label-position="labelPosition"
    :label-width="layout.labelWidth || '120px'"
    :rules="rules"
    :style="{
      width: layout.formWidth || '560px',
      paddingBottom: layout.formPaddingBottom || '0px',
    }"
    style="margin: 0 auto"
  >
    <el-row
      v-for="(row, j) in formRows"
      :key="j"
      :class="{ 'card-row': formRows.length > 1 }"
    >
      <el-col v-if="formRows.length > 1" :span="24" class="card-title">
        <slot :name="'title' + j"></slot>
      </el-col>

      <el-row
        class="card-body"
        :gutter="layout.gutter || 20"
        style="width: 100%"
      >
        <el-col
          v-for="(item, index) in row.filter((i) => i.isForm)"
          :key="index"
          :style="{ clear: item.clearboth ? 'both' : '' }"
          :span="item.formSpan || 24"
          :xs="item.form_xs || item.formSpan"
          :sm="item.form_sm || item.formSpan"
          :md="item.form_md || item.formSpan"
          :lg="item.form_lg || item.formSpan"
          :xl="item.form_xl || item.formSpan"
        >
          <template v-if="item.isCut">
            <div v-if="item.isCut" class="el-tit">
              <span>{{ item.title }}</span>
            </div>
          </template>

          <template v-if="!item.isCut">
            <template v-if="item.formOtherSlot">
              <slot :name="item.formOtherSlot" :item="item"></slot>
            </template>
            <template v-else-if="item.formSlot">
              <!--<el-form-item-->
              <!--  :prop="-->
              <!--    openType === 'info' ? '' : item.prop ? item.dataIndex : ''-->
              <!--  "-->
              <!--  :label="item.title + ' : '"-->
              <!--&gt;-->
              <!--  <slot :name="item.formSlot" :item="item"></slot>-->
              <!--</el-form-item>-->
              <el-form-item
                :prop="
                  openType === 'info' ? '' : item.prop ? item.dataIndex : ''
                "
                :label="item.title + ' : '"
              >
                <template #label>
                  <span>
                    {{
                      item.title +
                      (item.tooltipContent || item.tooltipSlot ? "" : " : ")
                    }}
                    <el-tooltip
                      v-if="item.tooltipContent || item.tooltipSlot"
                      :effect="item.effect || 'dark'"
                      :placement="item.placement || 'bottom-start'"
                    >
                      <i
                        :class="item.tooltipIcon || 'el-icon-question'"
                        style="font-size: 16px; vertical-align: middle"
                      >
                        :
                      </i>
                      <div slot="content">
                        <slot name="tooltipSlot">{{
                          item.tooltipContent
                        }}</slot>
                      </div>
                    </el-tooltip>
                  </span>
                </template>
                <slot :name="item.formSlot" :item="item"></slot>
              </el-form-item>
            </template>
            <el-form-item
              v-else
              :prop="
                openType === 'info' || openType === 'audit'
                  ? ''
                  : item.prop
                  ? item.dataIndex
                  : ''
              "
              :label="item.title + ' : '"
            >
              <template #label>
                <span
                  style="
                    display: inline-flex;
                    justify-content: flex-end;
                    align-items: center;
                    flex-wrap: nowrap;
                  "
                >
                  {{
                    item.title +
                    (item.tooltipContent || item.tooltipSlot ? "" : " : ")
                  }}
                  <el-tooltip
                    v-if="item.tooltipContent || item.tooltipSlot"
                    :effect="item.effect || 'dark'"
                    :placement="item.placement || 'bottom-start'"
                  >
                    <i
                      :class="item.tooltipIcon || 'el-icon-question'"
                      style="
                        font-size: 16px;
                        vertical-align: middle;
                        margin-left: 2px;
                      "
                    >
                      :
                    </i>
                    <div slot="content">
                      <slot name="tooltipSlot">{{ item.tooltipContent }}</slot>
                    </div>
                  </el-tooltip>
                </span>
              </template>
              <div
                v-if="
                  originalFormParams[item.dataIndex] &&
                  item.valueType === 'text'
                "
                :class="
                  item.atext
                    ? 'info-atext'
                    : item.isWrap
                    ? 'info-text-wrap'
                    : 'info-text'
                "
                v-on="item.fun"
              >
                {{ originalFormParams[item.dataIndex] }}
              </div>
              <div
                v-if="
                  !originalFormParams[item.dataIndex] &&
                  item.valueType === 'text'
                "
                :class="
                  item.atext
                    ? 'info-atext'
                    : item.isWrap
                    ? 'info-text-wrap'
                    : 'info-text'
                "
                v-on="item.fun"
              ></div>
              <el-input
                v-if="item.valueType === 'input'"
                v-model="originalFormParams[item.dataIndex]"
                v-bind="item.attrs"
                :disabled="
                  item.disabled || openType === 'info' || openType === 'audit'
                "
                :min="item.min"
                :show-word-limit="
                  item.inputType == 'textarea' || item.wordlimit != null
                "
                :type="item.inputType || 'text'"
                minlength="20"
                :maxlength="item.wordlimit || 500"
                :autosize="item.autosize"
                :placeholder="item.placeholder || '请输入' + item.title"
                :show-password="item.inputType === 'password'"
                clearable
                auto-complete="off"
              >
                <!-- { minRows:2,maxRows:4 } -->
                <template v-if="item.attrs && item.attrs.prepend" #prepend>
                  {{ item.attrs.prepend }}
                </template>
                <template v-if="item.attrs && item.attrs.append" #append>
                  {{ item.attrs.append }}
                </template>
                <template v-if="item.attrs && item.attrs.suffix" #suffix>
                  {{ item.attrs.suffix }}
                </template>
              </el-input>
              <el-input-number
                v-if="item.valueType === 'input-number'"
                v-model="originalFormParams[item.dataIndex]"
                :min="item.step || 0"
                controls-position="right"
                v-bind="item.attrs"
                :step="item.step"
                :disabled="
                  item.disabled || openType === 'info' || openType === 'audit'
                "
                :placeholder="item.placeholder || '请输入' + item.title"
                style="width: 100%"
                clearable
                @input.native="
                  item.isnumtop ? handleEdit : eventDisposalRangeChange
                "
              />

              <el-switch
                v-else-if="item.valueType === 'switch'"
                v-model="originalFormParams[item.dataIndex]"
                :active-color="item.activeColor || '#13ce66'"
                :inactive-color="item.inactiveColor || '#ff4949'"
                :active-value="item.activeValue"
                :inactive-value="item.inactiveValue"
                :disabled="
                  item.disabled || openType === 'info' || openType === 'audit'
                "
              />
              <el-date-picker
                v-else-if="item.valueType === 'date-picker'"
                v-model="originalFormParams[item.dataIndex]"
                v-bind="item.attrs"
                :type="item.pickerType"
                :disabled="
                  item.disabled || openType === 'info' || openType === 'audit'
                "
                :placeholder="item.placeholder || '请选择' + item.title"
                clearable
                style="width: 100%"
                :format="item.pickerFormat"
                :value-format="item.valueFormat"
              />
              <el-time-select
                v-else-if="item.valueType === 'time-select'"
                v-model="originalFormParams[item.dataIndex]"
                v-bind="item.attrs"
                :type="item.pickerType"
                :disabled="
                  item.disabled || openType === 'info' || openType === 'audit'
                "
                :placeholder="item.placeholder || '请选择' + item.title"
                clearable
                style="width: 100%"
                :format="item.pickerFormat"
                :value-format="item.valueFormat"
              />
              <el-time-picker
                v-else-if="item.valueType === 'time-picker'"
                v-model="originalFormParams[item.dataIndex]"
                is-range
                v-bind="item.attrs"
                :type="item.pickerType"
                :disabled="
                  item.disabled || openType === 'info' || openType === 'audit'
                "
                :placeholder="item.placeholder || '请选择' + item.title"
                clearable
                style="width: 100%"
                :format="item.pickerFormat"
                :value-format="item.valueFormat"
              />
              <el-select
                v-else-if="item.valueType === 'select'"
                v-model="originalFormParams[item.dataIndex]"
                :disabled="
                  item.disabled || openType === 'info' || openType === 'audit'
                "
                :placeholder="item.placeholder || '请选择' + item.title"
                filterable
                :multiple="item.multiple"
                clearable
                style="width: 100%"
                v-on="item.fun"
                @change="$forceUpdate()"
                @visible-change="$forceUpdate()"
              >
                <el-option
                  v-for="s in item.option.filter((i) => i.value !== '')"
                  :key="'select' + s.value"
                  :value="s.value"
                  :label="s.label"
                  >{{ s.label }}</el-option
                >
              </el-select>

              <el-checkbox-group
                v-else-if="item.valueType === 'checkbox'"
                v-model="originalFormParams[item.dataIndex]"
                size="small"
              >
                <el-checkbox
                  v-for="s in item.option"
                  :key="s.value"
                  border
                  :label="s.value"
                  :disabled="
                    item.disabled || openType === 'info' || openType === 'audit'
                  "
                  >{{ s.label }}</el-checkbox
                >
              </el-checkbox-group>
              <el-checkbox-group
                v-else-if="item.valueType === 'checkbutton'"
                v-model="originalFormParams[item.dataIndex]"
                size="small"
              >
                <el-checkbox-button
                  v-for="s in item.option"
                  :key="s.value"
                  :label="s.value"
                  :disabled="
                    item.disabled || openType === 'info' || openType === 'audit'
                  "
                  >{{ s.label }}</el-checkbox-button
                >
              </el-checkbox-group>
              <el-radio-group
                v-else-if="item.valueType === 'radio'"
                v-model="originalFormParams[item.dataIndex]"
                @input="item.fun"
              >
                <el-radio
                  v-for="(s, i) in item.option"
                  :key="i"
                  :label="s.value"
                  :disabled="
                    item.disabled || openType === 'info' || openType === 'audit'
                  "
                  >{{ s.label }}
                </el-radio>
              </el-radio-group>
              <InputRange
                v-else-if="item.valueType === 'inputRange'"
                v-model="originalFormParams[item.dataIndex]"
                style="width: 100%"
                :disabled="
                  item.disabled || openType === 'info' || openType === 'audit'
                "
                :start-placeholder="item.startPlaceholder || item.title"
                :end-placeholder="item.endPlaceholder || item.title"
                @update:values="(e) => updateRange(e, item.dataIndex)"
              />
              <ProductTree
                v-else-if="item.valueType === 'product'"
                v-model="originalFormParams[item.dataIndex]"
                style="width: 100%"
                :placeholder="item.placeholder || item.title"
                :multiple="item.multiple"
                :collapse-tags="item.collapseTags"
                :clearable="item.clearable || true"
                @change="(e) => handleProductChange(e, item.dataIndex)"
              />
              <template v-if="item.unit">
                <span
                  v-if="
                    item.valueType == 'text' &&
                    originalFormParams[item.dataIndex]
                  "
                  class="unit1"
                >
                  {{ item.unit }}
                </span>
                <span v-if="item.valueType !== 'text'" class="unit">
                  {{ item.unit }}
                </span>
              </template>
            </el-form-item>
          </template>
        </el-col>
      </el-row>
    </el-row>

    <el-form-item
      v-if="$slots && $slots.footerBtn"
      label-width="0"
      style="margin-top: 24px; text-align: center"
    >
      <slot name="footerBtn"></slot>
    </el-form-item>
  </el-form>
</template>

<script>
import { cloneDeep } from "lodash";
import InputRange from "@/components/InputRange/index.vue";
import ProductTree from "@/components/ProductTree/index.vue";

export default {
  name: "ProForm",
  components: { ProductTree, InputRange },
  props: {
    formParam: {
      type: Object,
      default: () => {
        return {};
      },
    },
    layout: {
      type: Object,
      default: () => {
        return {};
      },
    },
    confirmLoading: {
      type: Boolean,
      default: false,
    },
    labelPosition: {
      type: String,
      default: "right",
    },
    openType: {
      type: String,
      default: "add",
    },
    formList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      loading: false,
      showForm: false,
      rules: {},
      formRows: [],
      formListAll: [],
      originalFormParams: [],
      checkList: [],
    };
  },
  computed: {},
  watch: {
    formList: {
      handler: function (val) {
        this.formListAll = cloneDeep(val);
        this.init();
      },
      immediate: true,
      deep: true,
    },
    formParam: {
      handler: function (val) {
        this.resetFormParam();
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    init() {
      this.originalFormParams = cloneDeep(this.formParam);
      this.formListAll.forEach((element) => {
        // 判断是否需要显示该表单项
        if (element.isShowFormItem) {
          element.isForm = element.isShowFormItem(this.formParam);
        }
        // 如果不需要显示该表单项，则跳过
        if (!element.isForm) return;

        // 如果该表单项是下拉框
        if (element.valueType && element.valueType === "select") {
          // 如果需要异步获取下拉框选项
          if (element.optionMth) {
            if (element.option.length === 0) {
              this.initMthOption(element);
            }
          }
          // 如果下拉框选项已经存在
          if (!element.optionMth && element.optionskey) {
            if (element.option.length > 0) {
              this.initOption(element);
            }
          }
        }
      });

      // 初始化表单行
      this.formRows = [this.formListAll];
      // 初始化表单验证规则
      this.initRules();
      this.showForm = true;
    },
    initMthOption(element) {
      element.optionMth().then((res) => {
        if (!res) return;
        const { data } = res;
        let arr = [];
        if (element.optionskey) {
          arr = data.map((i) => {
            const obj = {};
            obj.label = element.optionskey.label2
              ? i[element.optionskey.label] + "_" + i[element.optionskey.label2]
              : i[element.optionskey.label];
            obj.value = i[element.optionskey.value];
            return obj;
          });
        } else {
          arr = data.map((i) => {
            const obj = {};
            obj.label = i;
            obj.value = i;
            return obj;
          });
        }
        element.defaultOption = element.defaultOption || [];
        element.option = [...element.defaultOption, ...arr];
      });
    },
    initOption(element) {
      const { option, optionskey } = element;

      if (optionskey) {
        element.option = option.map((i) => {
          const { label, value } = i[optionskey];
          return { label, value };
        });
      } else {
        element.option = option.map((i) => {
          return { label: i, value: i };
        });
      }
    },
    initRules() {
      this.rules = {};
      // 遍历表单列表
      this.formListAll.forEach((i) => {
        // 如果该表单项有prop属性，则将其加入到验证规则中
        if (i.prop) {
          this.rules[i.dataIndex] = i.prop;
        }
      });
    },
    linkageForm() {
      if (this.formListAll.filter((i) => i.isShowFormItem).length === 0) return;
      for (const list of this.formRows) {
        for (const item of list) {
          if (item.isShowFormItem) {
            item.isForm = item.isShowFormItem(this.formParam);
          }
        }
      }
    },
    resetFormParam() {
      // 将 this.formParam 的值赋给 this.originalFormParams
      if (Object.keys(this.formParam).length === 0) {
        this.originalFormParams = this.formParam;
      } else {
        this.originalFormParams = Object.assign(
          this.formParam,
          this.originalFormParams
        );
      }
      this.$nextTick(() => {
        if (this.$refs["ProForm"]) {
          // 清除表单验证
          this.$refs["ProForm"].clearValidate();
        }
      });
    },
    handleSubmit() {
      // 返回一个 Promise 对象
      return new Promise((resolve, reject) => {
        this.$refs["ProForm"].validate((valid) => {
          if (valid) {
            resolve();
            // 触发 proSubmit 事件，传入表单参数和一个回调函数
            this.$emit("proSubmit", this.originalFormParams, (states) => {
              // 如果回调函数的参数为 "fulfilled"，则调用 resetFormParam 方法
              if (states === "fulfilled") this.resetFormParam();
            });
          } else {
            reject();
          }
        });
      });
    },
    updateRange(val, dataIndex) {
      this.$set(this.originalFormParams, dataIndex, val);
    },
    handleProductChange(val, dataIndex) {
      this.$set(this.originalFormParams, dataIndex, val);
    },
    // 只能输入正整数（包括0）
    handleEdit(e) {
      this.eventDisposalRangeChange(e);
      let value = e.replace(/[^\d]/g, "");
      value = value.replace(/^0+(\d)/, "$1");
      value = value.replace(/(\d{15})\d*/, "$1");
      return value;
    },
    eventDisposalRangeChange(value) {
      if (value !== undefined) {
        this.$refs.form.clearValidate("eventDisposalRange");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.card-row {
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  border-radius: 4px;
  border: 1px solid #ebeef5;
  background-color: #fff;
  overflow: hidden;
  color: #303133;
  transition: 0.3s;

  .card-title {
    padding: 18px 20px;
    border-bottom: 1px solid #ebeef5;
    box-sizing: border-box;
  }

  .card-body {
    padding: 20px;
  }
}

.card-row + .card-row {
  margin-top: 20px;
}

.info-text {
  width: 100%;
  display: inline;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  // border-bottom: 1px solid #dcdfe6;
}
.info-atext {
  width: 100%;
  color: #457dec;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  text-decoration: underline;
}
.info-text-wrap {
  width: 100%;
  display: inline;
  height: 40px;
  white-space: wrap;
}
.unit {
  position: absolute;
  font-size: 12px;
  color: #6c6e72;
  width: 30px;
}
.unit1 {
  font-size: 12px;
  color: #6c6e72;
}

.el-tit {
  width: 100%;
  line-height: 40px;
  margin: 10px 0;

  span {
    margin-left: 30px;
    line-height: 40px;
    display: inline-block;
    color: #1a51e2;
    font-family: ShiShangZhongHeiJianTi;
    font-size: 16px;
  }
}

::v-deep .el-textarea__inner {
  font-family: Arial;
}
</style>
