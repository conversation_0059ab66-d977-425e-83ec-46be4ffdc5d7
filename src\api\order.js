import { get, post, put, del } from "@/utils/request";
// ===========================  销售统计  ===========================
//月度销售统计
export const getMonthlySalesStatApi = (data) =>
  post("/statisics/monthlyStatisPage", data);
// 月度销售统计汇总
export const getMonthlySalesStatSummaryApi = (data) =>
  post("/statisics/monthlyStatisSummary", data);
// 月耗材销售统计
export const getMonthlyConsumableStatApi = (data) =>
  post("/operator/trade-order/salesSummaryPage", data);
// 月耗材销售汇总
export const getMonthlyConsumableStatSummaryApi = (data) =>
  post("/operator/trade-order/salesSummaryTotal", data);
// 月度维修统计
export const getMonthlyRepairStatApi = (data) =>
  post("/work-order-pc/monthRepairPage", data);
// 月度维修汇总
export const getMonthlyRepairStatSummaryApi = (data) =>
  post("/work-order-pc/monthRepairSummary", data);
// 月度抄表统计
export const getMonthlyMeterStatApi = (data) =>
  post("/iot-receipt/monthReceiptList", data);
// 月度抄表统计汇总
export const getMonthlyMeterStatSummaryApi = (data) =>
  post("/iot-receipt/monthReceiptSummary", data);
// 月度机器统计
export const getMonthlyMachineStatApi = (data) =>
  post("/operator/trade-order/monthlyMechineSalePage", data);
// 月度机器销售汇总
export const getMonthlyMachineStatSummaryApi = (data) =>
  post("/operator/trade-order/monthlyMechineSaleSummary", data);

// ===========================  耗材统计  ============================
// 按物品统计销售
export const getItemSalesStatApi = (data) =>
  post("/report/quota/monthlyArticleSalePage", data);
// 按机型统计销售
export const getModelSalesStatApi = (data) =>
  post("/report/quota/monthlyMechineSalePage", data);
// 机器销售汇总
export const getMachineSalesStatApi = (data) =>
  post("/report/quota/monthlyStatisData", data);
// 按地区统计销售
export const getAreaSalesStatApi = (data) =>
  post("/report/quota/monthlyDistributeSalePage", data);

// ===========================  耗材退货  ===========================

// 退货明细
export const getReturnListApi = (data) =>
  post("/reverseManager/detailPage", data);
// 按物品统计退货
export const getItemReturnStatApi = (data) =>
  post("/reverse-detail/articleStatisPage", data);
// 汇总
export const getReturnSummaryStatApi = (data) =>
  post("/reverse-detail/articleStatisSummary", data);
// 按供应商统计退货
export const getSupplierReturnStatApi = (data) =>
  post("/reverse-detail/manufacturerPage", data);
// 汇总
// 按客户退货统计
export const getCustomerReturnStatApi = (data) =>
  post("/reverse-detail/customerPage", data);
// 汇总
export const getCustomerReturnSummaryStatApi = (data) =>
  post("/reverse-detail/customerSummary", data);
export const getSupplierReturnSummaryStatApi = (data) =>
  post("/reverse-detail/manufacturerSummary", data);
// 按系列统计退货
export const getSeriesReturnStatApi = (data) =>
  post("/reverse-detail/reverseMechinePage", data);

// ===========================  机器统计  ===========================
// 月度机器销售统计分页查询
export const getMonthlyMachineSalesStatApi = (data) =>
  post("/operator/trade-order/mechineSalePage", data);
// 月度统计机器销售汇总
export const getMonthlyMachineSalesSummaryStatApi = (data) =>
  post("/operator/trade-order/mechineSaleSummary", data);
// 月度机器销售按地区分类
export const getMonthlyMachineSalesAreaStatApi = (data) =>
  post("/operator/trade-order/mechineDistributionPage", data);
// 月度机器销售按地区分类汇总
export const getMonthlyMachineSalesAreaSummaryStatApi = (data) =>
  post("/operator/trade-order/mechineDistributionSummary", data);
// 月度机器销售分布分页查询
export const getMonthlyMachineSalesDistributionStatApi = (data) =>
  post("/operator/trade-order/mechineBrandDistributionPage", data);
// 分布汇总
export const getMonthlyMachineSalesDistributionSummaryStatApi = (data) =>
  post("/operator/trade-order/brandDistributionSummary", data);

// ============================  维修统计  ==========================
//按机型统计
export const getModelRepairStatApi = (data) =>
  post("/work-order-pc/monthRepairBrandPage", data);
// 汇总
export const getModelRepairStatSummaryApi = (data) =>
  post("/work-order-pc/monthRepairBrandSummary", data);
// 按地区统计
export const getAreaRepairStatApi = (data) =>
  post("/work-order-pc/monthRepairDistributionPage", data);
// 汇总
export const getAreaRepairStatSummaryApi = (data) =>
  post("/work-order-pc/monthRepairDistributionSummary", data);
// 按服务统计
export const getServiceRepairStatApi = (data) =>
  post("/work-order-pc/monthRepairTreatyPage", data);
// 汇总
export const getServiceRepairStatSummaryApi = (data) =>
  post("/work-order-pc/monthRepairTreatySummary", data);
// 按问题分类
export const getQuestionRepairStatApi = (data) =>
  post("/work-order-pc/monthRepairProblemPage", data);
// 按频次分类
export const getFrequencyRepairStatApi = (data) =>
  post("/work-order-pc/repairBrandListPage", data);
