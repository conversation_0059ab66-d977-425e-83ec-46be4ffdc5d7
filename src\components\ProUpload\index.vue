<!--
 * @Author: yangzhong
 * @Date: 2023-11-02 11:32:05
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 14:43:07
 * @Description: 图片上传组件
-->

<template>
  <div class="upload" :class="objClass">
    <div class="file-list">
      <draggable
        :list="fileList"
        handle=".draggable-btn"
        :animation="200"
        filter=".un-draggable"
      >
        <transition-group>
          <div
            v-for="file in fileList"
            :key="file.key"
            class="file-item"
            :class="draggableSort ? 'draggable' : 'un-draggable'"
          >
            <img
              class="el-upload-list__item-thumbnail"
              :src="file.url"
              alt=""
            />
            <span class="el-upload-list__item-actions">
              <span
                class="el-upload-list__item-preview"
                @click="handlePictureCardPreview(file)"
              >
                <i class="el-icon-zoom-in"></i>
              </span>
              <span
                v-if="type !== 'info' && draggableSort"
                class="el-upload-list__item-preview draggable-btn"
              >
                <i class="el-icon-rank"></i>
              </span>
              <span
                v-if="type !== 'info'"
                class="el-upload-list__item-preview"
                @click="handleRemove(file)"
              >
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
        </transition-group>
      </draggable>
    </div>
    <el-upload
      ref="upload"
      action=""
      :http-request="uploadImg"
      :show-file-list="false"
      list-type="picture-card"
      :on-preview="handlePictureCardPreview"
      :file-list="fileList"
      :limit="limit"
      :accept="accept"
      :drag="drag"
      :multiple="multiple"
      :disabled="disabled ?? (type === 'info' || type === 'audit')"
      :on-exceed="handleExceed"
      :before-remove="handleRemove"
      :before-upload="checkFileType"
      :class="{
        'disable-upload': type === 'info' || fileList.length === limit,
      }"
    >
      <i class="el-icon-plus"></i>
    </el-upload>
    <el-dialog :visible.sync="showPreview" top="5vh" :modal="false">
      <img class="preview-img" width="80%" :src="previewImgUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import { uploadFile } from "@/api/upload";
import { Message, MessageBox } from "element-ui";
import draggable from "vuedraggable";
export default {
  name: "ProUpload",
  components: { draggable },
  props: {
    type: {
      type: String,
      default: "add",
    },
    fileList: {
      type: Array,
      default: () => [],
    },
    limit: {
      type: Number,
      default: 5,
    },
    multiple: {
      type: Boolean,
      default: false,
    },

    accept: {
      type: String,
      default: "image/jpg,image/jpeg,image/png",
    },
    fileType: {
      type: Array,
      default: () => ["image/jpeg", "image/png", "image/jpg"],
    },
    fileSize: {
      type: Number,
      default: 10,
    },
    draggableSort: {
      type: Boolean,
      default: false,
    },
    drag: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showPreview: false,
      previewImgUrl: "",
      imgList: [],
      objClass: {
        upLoadShow: true,
        upLoadHide: false,
      },
    };
  },
  methods: {
    async uploadImg(val) {
      try {
        const { file } = val;
        const result = await uploadFile(file);
        this.imgList.push(result);
        if (this.imgList.length === this.limit) {
          this.objClass.upLoadHide = true; //上传图片后置upLoadHide为真，隐藏上传框
          this.objClass.upLoadShow = false;
        } else {
          this.objClass.upLoadHide = false; //上传图片后置upLoadHide为真，隐藏上传框
          this.objClass.upLoadShow = true;
        }
        this.$emit("uploadSuccess", result);
        Message.success("上传成功");
      } catch (error) {
        Message.error(error.message);
      }
    },
    handlePictureCardPreview(file) {
      this.showPreview = true;
      this.previewImgUrl = file.url;
    },
    handleExceed() {
      Message.warning("最多只能上传" + this.limit + "张图片");
    },
    handleRemove(file) {
      this.imgList = this.imgList.filter((item) => item.key !== file.key);
      this.objClass.upLoadShow = true; //删除图片后显示上传框
      this.objClass.upLoadHide = false;
      this.$emit("uploadRemove", file);
    },
    checkFileType(file) {
      const { type, size } = file;
      let errorMessage = "";
      if (!this.fileType.includes(type)) {
        errorMessage = "仅支持上传" + this.fileType.join("、") + "格式的图片";
      }

      if (size > this.fileSize * 1024 * 1024) {
        if (!errorMessage) {
          errorMessage = "图片大小不能超过" + this.fileSize + "M";
        } else {
          errorMessage += "，且图片大小不能超过" + this.fileSize + "M";
        }
      }
      if (errorMessage) {
        Message.error(errorMessage);
        return false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.disable-upload {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
</style>

<style>
.preview-img {
  display: block;
  margin: 0 auto;
}

/* .upLoadShow .el-upload {
  width: 30rem !important;
  height: 30rem !important;
  line-height: 30rem !important;
} */

/*当upLoadHide为true时，启用如下样式，即缩略图的样式，若为false则不启用该样式*/
/* .upLoadHide .el-upload-list--picture-card .el-upload-list__item {
  width: 30rem !important;
  height: 30rem !important;
  line-height: 30rem !important;
} */

/*当upLoadHide为true时，启用如下样式，即上传框的样式，若为false则不启用该样式*/
.upLoadHide .el-upload {
  display: none;
}

.upload {
  display: flex;
  padding-left: 15px;
  .el-upload--picture-card {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    /* 拖拽样式 */
    .el-upload-dragger {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fbfdff;
      border: none;
    }
  }
}
.file-item {
  position: relative;
  display: inline-block;
}
.el-upload-list__item-thumbnail {
  width: 120px;
  height: 120px;
  border-radius: 0.375rem;
  margin: 0 15px 0 0;
  user-select: none;
  -webkit-user-select: none;
}
.el-upload-list__item-actions {
  position: absolute;
  top: 0;
  left: 0;
  height: 120px;
  width: 120px;
  border-radius: 0.375rem;
  opacity: 0;
  &:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.3);
    cursor: pointer;
  }
}
.el-upload-list__item-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  user-select: none;
  -webkit-user-select: none;
  .el-upload-list__item-preview {
    text-align: center;
    flex: 1;
    font-size: 16px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
