<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-01 13:17:35
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-09 13:58:31
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :data="tableData"
      :columns="columns"
      @loadData="loadData"
    ></ProTable>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";

export default {
  name: "DepositReceived",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      columns: [
        {
          dataIndex: "code",
          title: "业务单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "type",
          title: "业务类型",
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [],
          minWidth: 100,
        },
        {
          dataIndex: "customerSeqId",
          title: "付款方编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "customerName",
          title: "收付款方名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "consignee",
          title: "联系人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "consigneePhone",
          title: "联系电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "amount",
          title: "预收金额",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "createdAt",
          title: "付款时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "signName",
          title: "经办人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          minWidth: 100,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 120,
        },
      ],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const requestParameters = cloneDeep(this.queryParam);
      this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
