<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-09 13:59:32
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-21 17:42:37
 * @Description: 机器应收汇总
 -->
<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      show-selection
      @handleSelectionChange="handleSelectionChange"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="primary"
          icon="el-icon-edit"
          size="mini"
          @click="handleBulkEdit"
        >
          账款批量核销
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-check"
          @click="clearSelection"
        >
          取消全选
        </el-button>
        <el-button
          v-auth="['@ums:manage:finance:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>应收总金额：{{ totalData?.totalAmount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'info')"
          >
            详情
          </el-button>
          <el-button icon="el-icon-edit" @click="handleEdit(row, 'audit')">
            账款核销
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      :confirm-text="'确认核销'"
      :confirm-btn-loading="formLoading"
      width="30%"
      top="2%"
      @ok="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParams"
        :form-list="formColumns"
        :open-type="'edit'"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        @proSubmit="proSubmit"
      >
        <template #type>
          {{ formParams.type == 0 ? "购机尾款" : formParams.type == 1 ? "分期款" : formParams.type == 2 ? "定金款" : "/", }}
        </template>
        <template #voucherImg>
          <ProUpload
            :file-list="formParams.voucherImg"
            :type="'edit'"
            :limit="3"
            style="padding-left: 0"
            @uploadSuccess="handleUploadSuccess"
            @uploadRemove="handleUploadRemove"
          />
        </template>
      </ProForm>
    </ProDialog>
    <!--购机、租赁、融资合约-->
    <BuyRentFinanceContract
      ref="buyRentFinanceContract"
      :contract-type="contractType"
    />
    <!-- 抄表合约 -->
    <InsureContract ref="insureContract" :contract-type="contractType" />
    <!-- 维保合约 -->
    <SafeguardContract ref="safeguardContract" :contract-type="contractType" />
    <BulkEdit
      ref="bulkEdit"
      :columns="bulkEditColumns"
      type="equipment"
      @refresh="refresh(), clearSelection()"
    />
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { handleExcelExport } from "@/utils/exportExcel";
import {
  receivableMachineSummaryListApi,
  receivableBaseSummaryStatisticsApi,
  receivableMachineSummaryExportApi,
  receivableWriteOffApi,
} from "@/api/finance";
import {
  getCustomerContractApi,
  getCustomerContractDetailApi,
} from "@/api/customer";
import ProUpload from "@/components/ProUpload/index.vue";
import BulkEdit from "@/views/financing/components/BulkEdit.vue";

export default {
  name: "Equipment",
  components: {
    BulkEdit,
    ProUpload,
    InsureContract: () =>
      import(
        "@/views/custom/editCustom/components/contract/insureContract.vue"
      ),
    SafeguardContract: () =>
      import(
        "@/views/custom/editCustom/components/contract/safeguardContract.vue"
      ),
    BuyRentFinanceContract: () =>
      import(
        "@/views/custom/editCustom/components/contract/buyRentFinanceContract.vue"
      ),
  },
  data() {
    const _this = this;
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "type",
          title: "收款类型",
          isTable: true,
          formatter: (row) =>
            row.type == 0
              ? "购机尾款"
              : row.type == 1
              ? "分期款"
              : row.type == 2
              ? "定金款"
              : "/",
          isSearch: true,
          valueType: "select",
          option: [
            { label: "购机尾款", value: 0 },
            { label: "分期款", value: 1 },
            { label: "定金款", value: 2 },
          ],
          minWidth: 80,
        },
        {
          dataIndex: "contractCode",
          title: "合同编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "customerCode",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "license",
          title: "营业执照名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "contractType",
          title: "合约类型",
          isTable: true,
          formatter: (row) => row.contractType?.label,
          minWidth: 80,
        },
        {
          dataIndex: "signTime",
          title: "合约开始时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          minWidth: 100,
        },
        // {
        //   dataIndex: "productIds",
        //   title: "机器型号",
        //   isSearch: true,
        //   valueType: "product",
        // },
        // {
        //   dataIndex: "num",
        //   title: "数量",
        //   isTable: true,
        //   minWidth: 60,
        // },
        {
          dataIndex: "price",
          title: "机器总价",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "depositAmount",
          title: "应收金额",
          isTable: true,
          minWidth: 100,
        },
        // {
        //   dataIndex: "verifyDiscountAmount",
        //   title: "折扣金额",
        //   isTable: true,
        //   minWidth: 120,
        // },
        // {
        //   dataIndex: "installAmount",
        //   title: "安装费用",
        //   isTable: true,
        //   minWidth: 100,
        // },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          isTable: true,
          width: 150,
        },
        // {
        //   dataIndex: "payTime",
        //   title: "支付时间",
        //   isTable: true,
        //   width: 150,
        // },
        // {
        //   dataIndex: "settleStatus",
        //   title: "结算状态",
        //   isTable: true,
        //   formatter: (row) => row.settleStatus?.label,
        //   minWidth: 80,
        // },
        // {
        //   dataIndex: "settleMethod",
        //   title: "结算方式",
        //   isTable: true,
        //   formatter: (row) => row.settleMethod?.label,
        //   minWidth: 80,
        // },
        // {
        //   dataIndex: "totalAmount",
        //   title: "收款单总金额",
        //   isTable: true,
        //   minWidth: 120,
        // },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          fixed: "right",
          tableSlot: "actions",
          width: 160,
        },
      ],
      contractType: "",
      tableData: [],
      totalData: {},
      requestParameters: {},
      statLoading: false,
      exportLoading: false,
      // 账款核销
      dialogVisible: false,
      dialogTitle: "账款核销",
      formParams: {},
      formColumns: [
        {
          dataIndex: "license",
          title: "营业执照名称",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "contractCode",
          title: "合同编号",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "type",
          title: "收款类型",
          isForm: true,
          formSlot: "type",
          formSpan: 24,
        },
        {
          dataIndex: "price",
          title: "机器总价",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "verifyReceiveAmount",
          title: "应收金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入应收金额",
              trigger: "change",
              validator: (rule, value, callback) => {
                if (!value) {
                  callback(new Error("请输入应收金额"));
                } else if (+value < 0) {
                  callback(new Error("应收金额不能为负数"));
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "verifyDiscountAmount",
          title: "折扣金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入折扣金额",
          //     trigger: "change",
          //     validator: (rule, value, callback) => {
          //       if (+value < 0) {
          //         callback(new Error("折扣金额不能小于0"));
          //       }
          //       callback();
          //     },
          //   },
          // ],
        },
        {
          dataIndex: "verifyActualAmount",
          title: "实收金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          prop: [
            {
              required: true,
              trigger: "change",
              validator: (rule, value, callback) => {
                console.log("value", value);
                if (!value) {
                  callback(new Error("请输入实收金额"));
                } else if (+value < 0) {
                  callback(new Error("实收金额不能小于0"));
                } else {
                  const deposit = parseFloat(
                    _this.formParams.verifyReceiveAmount
                  );
                  const discount = parseFloat(
                    _this.formParams.verifyDiscountAmount
                  );
                  const payable = deposit - (isNaN(discount) ? 0 : discount);

                  if (+value > payable) {
                    callback(new Error("实收金额不能大于应收金额减去折扣金额"));
                  }
                  if (+value < payable) {
                    callback(new Error("实收金额不能小于应收金额减去折扣金额"));
                  }
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "voucherImg",
          title: "上传凭证",
          isForm: true,
          formSlot: "voucherImg",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请上传凭证",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "remark",
          title: "备注",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          wordlimit: 255,
          formSpan: 24,
        },
      ],
      formLoading: false,
      bulkEditColumns: [
        {
          dataIndex: "type",
          title: "收款类型",
          isTable: true,
          formatter: (row) =>
            row.type == 0
              ? "购机尾款"
              : row.type == 1
              ? "分期款"
              : row.type == 2
              ? "定金款"
              : "/",
          isSearch: true,
          valueType: "select",
          option: [
            { label: "购机尾款", value: 0 },
            { label: "分期款", value: 1 },
            { label: "定金款", value: 2 },
          ],
          minWidth: 80,
        },
        {
          dataIndex: "contractCode",
          title: "合同编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        // {
        //   dataIndex: "customerName",
        //   title: "店铺名称",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 120,
        // },
        // {
        //   dataIndex: "customerCode",
        //   title: "客户编号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 140,
        // },
        // {
        //   dataIndex: "license",
        //   title: "营业执照名称",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 140,
        // },
        {
          dataIndex: "contractType",
          title: "合约类型",
          isTable: true,
          formatter: (row) => row.contractType?.label,
          minWidth: 80,
        },
        {
          dataIndex: "signTime",
          title: "合约开始时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          minWidth: 100,
        },
        // {
        //   dataIndex: "productIds",
        //   title: "机器型号",
        //   isSearch: true,
        //   valueType: "product",
        // },
        // {
        //   dataIndex: "num",
        //   title: "数量",
        //   isTable: true,
        //   minWidth: 60,
        // },
        {
          dataIndex: "price",
          title: "机器总价",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "depositAmount",
          title: "应收金额",
          isTable: true,
          minWidth: 100,
        },
        // {
        //   dataIndex: "verifyDiscountAmount",
        //   title: "折扣金额",
        //   isTable: true,
        //   minWidth: 120,
        // },
        // {
        //   dataIndex: "installAmount",
        //   title: "安装费用",
        //   isTable: true,
        //   minWidth: 100,
        // },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "verifyReceiveAmount",
          title: "应收金额",
          isTable: true,
          fixed: "right",
          tableSlot: "verifyReceiveAmount",
          width: 150,
        },
        {
          dataIndex: "verifyDiscountAmount",
          title: "折扣金额",
          isTable: true,
          fixed: "right",
          tableSlot: "verifyDiscountAmount",
          width: 150,
        },
        {
          dataIndex: "verifyActualAmount",
          title: "实收金额",
          isTable: true,
          fixed: "right",
          tableSlot: "verifyActualAmount",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          fixed: "right",
          tooltip: false,
          tableSlot: "actions",
          width: 120,
        },
      ],
      selectionData: [],
      tipLock: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startDate: null,
          endData: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      this.requestParameters = requestParameters;
      receivableMachineSummaryListApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      receivableBaseSummaryStatisticsApi({
        ...this.requestParameters,
        collectionType: 1,
      })
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    handleBulkEdit() {
      if (!this.selectionData.length) {
        this.$message.warning("请先选择要核销的订单");
        return;
      }
      this.$refs.bulkEdit.show(this.selectionData);
    },
    clearSelection() {
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },
    handleSelectionChange(rows) {
      this.$nextTick(() => {
        const firstRow = rows[0];
        const validRows = rows.filter((row) => {
          if (row.customerCode !== firstRow.customerCode) {
            this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(row, false);
            return true;
          }
          return false;
        });
        if (validRows.length > 0 && !this.tipLock) {
          this.$message.warning("请选择同一客户的收款单");
          this.tipLock = true;
          setTimeout(() => {
            this.tipLock = false;
          }, 2000);
        }
        this.selectionData = cloneDeep(rows);
      });
    },
    async handleEdit(row, type) {
      if (type === "info") {
        getCustomerContractApi(row.contractId).then((res) => {
          const formParam = {
            ...res.data,
            customerName: row.customerName,
            customerSeqId: row.customerCode,
            customerId: row.customerId,
          };
          if (row.contractType.value === "1201") {
            this.contractType = "1201";
            this.$refs.buyRentFinanceContract.visible(formParam, type);
          }
          if (row.contractType.value === "1202") {
            this.contractType = "1202";
            this.$refs.insureContract.visible(formParam, type);
          }
          if (row.contractType.value === "1265") {
            this.contractType = "1265";
            this.$refs.buyRentFinanceContract.visible(formParam, type);
          }
          if (row.contractType.value === "1230") {
            this.contractType = "1230";
            this.$refs.buyRentFinanceContract.visible(formParam, type);
          }
          if (row.contractType.value === "1220") {
            this.contractType = "1220";
            this.$refs.safeguardContract.visible(formParam, type);
          }
        });
      } else if (type === "audit") {
        this.formParams = cloneDeep(row);
        this.dialogVisible = true;
        this.dialogTitle = `核销 【${row.customerName}】 账款`;
      }
    },
    handleDialogConfirm() {
      this.$refs.ProForm.handleSubmit();
    },
    proSubmit(val) {
      this.$confirm("此操作将核销当前客户的账单, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.formLoading = true;
        const args = {
          customerId: this.formParams.customerId,
          type: this.formParams.type,
          tradeOrderOrigin:
            this.formParams.type == 1 ? "INSTALLMENT" : "CONTRACT_ARR", // CONTRACT_PRE  FQ2503210026
          tradeOrderNumber:
            this.formParams.type == 1
              ? this.formParams.installmentCode
              : this.formParams.contractCode,
          verifyReceiveAmount: this.formParams.verifyReceiveAmount, // 应收金额
          verifyDiscountAmount: this.formParams.verifyDiscountAmount, // 折扣金额
          verifyActualAmount: this.formParams.verifyActualAmount, // 实收金额
          voucherImg: this.formParams.voucherImg, // 实收金额
          remark: this.formParams.remark,
        };
        receivableWriteOffApi(filterParam(args))
          .then((res) => {
            this.$message.success("核销成功");
            this.handleDialogCancel();
            this.refresh();
          })
          .finally(() => {
            this.formLoading = false;
          });
      });
    },
    handleDialogCancel() {
      this.dialogVisible = false;
      this.formParams = {};
    },
    handleUploadSuccess(result) {
      if (!this.formParams.voucherImg) {
        this.$set(this.formParams, "voucherImg", []);
      }
      this.formParams.voucherImg.push(result);
    },
    handleUploadRemove(file) {
      const index = this.formParams.voucherImg.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParams.voucherImg.splice(index, 1);
    },
    handleExport() {
      this.$confirm("此操作将导出机器应收款明细, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          receivableMachineSummaryExportApi,
          this.requestParameters,
          "机器应收款明细",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
