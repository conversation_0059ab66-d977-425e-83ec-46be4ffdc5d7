<!--
 * @Author: AI Assistant
 * @Date: 2025-01-29
 * @Description: 发布版本对话框组件
-->
<template>
  <el-dialog
    title="发布新版本"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    destroy-on-close
  >
    <el-form 
      ref="publishForm" 
      :model="formData" 
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="版本名称" prop="versionName">
        <el-input
          v-model="formData.versionName"
          placeholder="例如: 1.3.0, 1.0-debug, 1.0.1-debug"
          @blur="validateVersionName"
        />
        <div class="form-tip">格式：x.y 或 x.y.z，可选后缀（如：1.3.0, 1.0-debug, 1.0.1-alpha）</div>
      </el-form-item>
      
      <el-form-item label="版本号" prop="versionCode">
        <el-input-number 
          v-model="formData.versionCode"
          :min="1"
          :max="999999"
          placeholder="例如: 13"
          style="width: 100%"
        />
        <div class="form-tip">版本号必须大于当前最新版本号，用于版本比较</div>
      </el-form-item>
      
      <el-form-item label="更新说明（可选）" prop="updateLog">
        <ProWangeEditor
          ref="ProWangeEditorRef"
          :content="formData.updateLog"
          :height="400"
          @onchange="onUpdateLogChange"
        />
      </el-form-item>

      <el-form-item label="发布类型" prop="releaseType">
        <el-radio-group v-model="formData.releaseType">
          <el-radio label="GLOBAL">全局发布</el-radio>
          <el-radio label="TARGETED">定向发布</el-radio>
        </el-radio-group>
        <div class="form-tip">
          全局发布：所有用户都可以接收更新；定向发布：仅指定的用户/设备/用户组可以接收更新
        </div>
      </el-form-item>

      <!-- 定向发布目标选择 -->
      <template v-if="formData.releaseType === 'TARGETED'">
        <el-form-item label="目标类型" prop="targetType">
          <el-radio-group v-model="formData.targetType">
            <el-radio label="USER">用户</el-radio>
            <el-radio label="DEVICE">设备</el-radio>
            <el-radio label="GROUP">用户组</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 用户选择 -->
        <el-form-item
          v-if="formData.targetType === 'USER'"
          label="目标用户"
          prop="selectedUsers"
        >
          <div class="target-selection">
            <div class="selection-header">
              <el-input
                v-model="userSearchKeyword"
                placeholder="搜索用户..."
                prefix-icon="el-icon-search"
                size="small"
                style="width: 200px"
              />
              <el-button size="small" @click="selectAllUsers">全选</el-button>
              <el-button size="small" @click="clearAllUsers">清空</el-button>
            </div>

            <div class="selection-content">
              <div class="target-list" v-loading="loadingUsers">
                <template v-if="filteredUsers.length > 0">
                  <el-checkbox
                    v-for="user in filteredUsers"
                    :key="user.id"
                    :value="isUserSelected(user)"
                    @change="handleUserChange(user, $event)"
                    class="target-item"
                  >
                    <span>{{ user.displayName || user.name || '未知用户' }}</span>
                  </el-checkbox>
                </template>
                <div v-else class="empty-state">
                  <el-empty :image-size="60" description="暂无用户数据" />
                </div>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 设备选择 -->
        <el-form-item
          v-if="formData.targetType === 'DEVICE'"
          label="目标设备"
          prop="selectedDevices"
        >
          <div class="target-selection">
            <div class="selection-header">
              <el-input
                v-model="deviceSearchKeyword"
                placeholder="搜索设备..."
                prefix-icon="el-icon-search"
                size="small"
                style="width: 200px"
              />
              <el-button size="small" @click="selectAllDevices">全选</el-button>
              <el-button size="small" @click="clearAllDevices">清空</el-button>
            </div>

            <div class="selection-content">
              <div class="target-list" v-loading="loadingDevices">
                <template v-if="filteredDevices.length > 0">
                  <el-checkbox
                    v-for="device in filteredDevices"
                    :key="device.id"
                    :value="isDeviceSelected(device)"
                    @change="handleDeviceChange(device, $event)"
                    class="target-item"
                  >
                    <span>{{ device.displayName || device.deviceId || '未知设备' }}</span>
                  </el-checkbox>
                </template>
                <div v-else class="empty-state">
                  <el-empty :image-size="60" description="暂无设备数据" />
                </div>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 用户组选择 -->
        <el-form-item
          v-if="formData.targetType === 'GROUP'"
          label="目标用户组"
          prop="selectedGroups"
        >
          <div class="target-selection">
            <div class="selection-header">
              <el-input
                v-model="groupSearchKeyword"
                placeholder="搜索用户组..."
                prefix-icon="el-icon-search"
                size="small"
                style="width: 200px"
              />
              <el-button size="small" @click="selectAllGroups">全选</el-button>
              <el-button size="small" @click="clearAllGroups">清空</el-button>
            </div>

            <div class="selection-content">
              <div class="target-list" v-loading="loadingGroups">
                <template v-if="filteredGroups.length > 0">
                  <el-checkbox
                    v-for="group in filteredGroups"
                    :key="group.id"
                    :value="isGroupSelected(group)"
                    @change="handleGroupChange(group, $event)"
                    class="target-item"
                  >
                    <span>{{ group.displayName || group.name || '未知用户组' }}</span>
                  </el-checkbox>
                </template>
                <div v-else class="empty-state">
                  <el-empty :image-size="60" description="暂无用户组数据" />
                </div>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 目标统计 -->
        <el-form-item label="已选目标">
          <div class="target-stats">
            <el-tag v-if="formData.targetType === 'USER' && formData.selectedUsers.length > 0" type="primary">
              用户: {{ formData.selectedUsers.length }}
            </el-tag>
            <el-tag v-if="formData.targetType === 'DEVICE' && formData.selectedDevices.length > 0" type="success">
              设备: {{ formData.selectedDevices.length }}
            </el-tag>
            <el-tag v-if="formData.targetType === 'GROUP' && formData.selectedGroups.length > 0" type="warning">
              用户组: {{ formData.selectedGroups.length }}
            </el-tag>
            <el-tag v-if="formData.targetType === 'GROUP' && groupMemberUsers.length > 0" type="info">
              组内用户: {{ groupMemberUsers.length }}
            </el-tag>
            <span v-if="getSelectedCount() === 0" class="text-muted">
              请选择目标对象
            </span>
          </div>
          <div v-if="formData.targetType === 'GROUP'" class="form-tip">
            <i class="el-icon-info"></i>
            选择用户组时，系统将自动获取组内所有用户进行定向发布
          </div>

          <!-- 显示用户组中的用户列表 -->
          <div v-if="formData.targetType === 'GROUP' && groupMemberUsers.length > 0" class="group-members">
            <div class="group-members-header">
              <span>用户组成员 ({{ groupMemberUsers.length }}人):</span>
              <el-button size="mini" type="text" @click="showGroupMembers = !showGroupMembers">
                {{ showGroupMembers ? '收起' : '展开' }}
              </el-button>
            </div>
            <div v-if="showGroupMembers" class="group-members-list">
              <el-tag
                v-for="user in groupMemberUsers"
                :key="user.id"
                size="mini"
                class="member-tag"
              >
                {{ user.displayName }}
              </el-tag>
            </div>
          </div>
        </el-form-item>
      </template>
      
      <el-form-item label="APK文件" required>
        <div class="upload-container">
          <el-upload
            ref="apkUpload"
            action=""
            :http-request="handleFileUpload"
            :before-upload="beforeUpload"
            :on-remove="onFileRemove"
            :file-list="fileList"
            :limit="1"
            accept=".zip"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将ZIP文件拖到此处，或<em>点击上传</em>
            </div>
            <div slot="tip" class="el-upload__tip">
              暂时只支持上传.zip文件，且不超过100MB
            </div>
          </el-upload>
          
          <!-- 上传进度 -->
          <el-progress
            v-if="uploadProgress > 0"
            :percentage="uploadProgress"
            :status="uploadProgress === 100 ? 'success' : (uploadStatus === 'exception' ? 'exception' : null)"
            class="mt-2"
          />
          
          <!-- 文件信息显示 -->
          <div v-if="uploadedFile" class="file-info mt-2">
            <el-alert
              :title="`文件上传成功: ${uploadedFile.name}`"
              type="success"
              :closable="false"
              show-icon
            >
              <div slot="description">
                <p>文件大小: {{ formatFileSize(uploadedFile.size) }}</p>
                <p>MD5校验: {{ uploadedFile.md5 }}</p>
              </div>
            </el-alert>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="发布设置">
        <div class="publish-settings">
          <el-checkbox v-model="formData.isForce">
            强制更新
            <el-tooltip content="启用后，用户必须更新到此版本才能继续使用应用" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-checkbox>
          
          <el-checkbox v-model="formData.isActive" class="ml-4">
            立即启用
            <el-tooltip content="启用后，此版本将立即对用户可见" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-checkbox>
        </div>
      </el-form-item>
    </el-form>

    <!-- 发布条件提示 -->
    <div v-if="!canPublish" class="publish-tips">
      <el-alert
        title="发布条件检查"
        type="info"
        :closable="false"
        show-icon
      >
        <div slot="default">
          <p>请完成以下条件后才能发布：</p>
          <ul>
            <li :class="{ 'completed': uploadedFile }">
              <i :class="uploadedFile ? 'el-icon-check' : 'el-icon-close'"></i>
              上传APK文件
            </li>
            <li :class="{ 'completed': formData.versionName }">
              <i :class="formData.versionName ? 'el-icon-check' : 'el-icon-close'"></i>
              填写版本名称
            </li>
            <li :class="{ 'completed': formData.versionCode }">
              <i :class="formData.versionCode ? 'el-icon-check' : 'el-icon-close'"></i>
              填写版本号
            </li>
          </ul>
        </div>
      </el-alert>
    </div>

    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        :loading="publishing"
        :disabled="!canPublish"
        @click="debouncedSubmit"
      >
        {{ publishing ? '发布中...' : '发布版本' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  publishVersion,
  validateVersionName,
  formatFileSize,
  getUserList,
  getDeviceList,
  getRoleList,
  getRoleMembers,
  getGroupMembersUserIds
} from '@/appupdate/api/appVersion';
import { uploadApkFile, validateApkFile } from '@/appupdate/api/apkUpload';
import ProWangeEditor from '@/components/ProWangeEditor/index.vue';
import CryptoJS from 'crypto-js';
import { debounce } from 'lodash';

export default {
  name: 'PublishDialog',
  components: {
    ProWangeEditor
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: false,
      publishing: false,
      
      // 表单数据
      formData: {
        versionName: '',
        versionCode: null,
        updateLog: '',
        isForce: false,
        isActive: true,
        releaseType: 'GLOBAL',
        targetType: 'USER',
        selectedUsers: [],
        selectedDevices: [],
        selectedGroups: [],
      },
      
      // 文件上传相关
      fileList: [],
      uploadedFile: null,
      uploadProgress: 0,
      uploadStatus: '',

      // 定向发布相关
      loadingUsers: false,
      loadingDevices: false,
      loadingGroups: false,
      userSearchKeyword: '',
      deviceSearchKeyword: '',
      groupSearchKeyword: '',
      allUsers: [],
      allDevices: [],
      allGroups: [],
      groupMemberUsers: [],  // 当前选中用户组的成员用户列表
      showGroupMembers: false,  // 是否显示用户组成员列表
      
      // 表单验证规则
      formRules: {
        versionName: [
          { required: true, message: '请输入版本名称', trigger: 'blur' },
          { validator: this.validateVersionNameRule, trigger: 'blur' },
        ],
        versionCode: [
          { required: true, message: '请输入版本号', trigger: 'blur' },
          { type: 'number', min: 1, message: '版本号必须大于0', trigger: 'blur' },
        ],
        updateLog: [
          // 允许更新说明为空，不进行必填和长度验证
        ],
      },
    };
  },
  computed: {
    canPublish() {
      const hasFile = !!this.uploadedFile;
      const hasBasicInfo = this.formData.versionName && this.formData.versionCode;

      if (this.formData.releaseType === 'GLOBAL') {
        return hasFile && hasBasicInfo;
      } else {
        // 定向发布需要选择目标
        return hasFile && hasBasicInfo && this.getSelectedCount() > 0;
      }
    },

    filteredUsers() {
      if (!this.userSearchKeyword) return this.allUsers;

      return this.allUsers.filter(user => {
        const name = user.name || user.username || '';
        const code = user.code || '';
        const displayName = user.displayName || '';
        return name.toLowerCase().includes(this.userSearchKeyword.toLowerCase()) ||
               code.toLowerCase().includes(this.userSearchKeyword.toLowerCase()) ||
               displayName.toLowerCase().includes(this.userSearchKeyword.toLowerCase());
      });
    },

    filteredDevices() {
      if (!this.deviceSearchKeyword) return this.allDevices;

      return this.allDevices.filter(device => {
        const deviceId = device.deviceId || '';
        const brand = device.brand || '';
        const model = device.model || '';
        const displayName = device.displayName || '';
        return deviceId.toLowerCase().includes(this.deviceSearchKeyword.toLowerCase()) ||
               brand.toLowerCase().includes(this.deviceSearchKeyword.toLowerCase()) ||
               model.toLowerCase().includes(this.deviceSearchKeyword.toLowerCase()) ||
               displayName.toLowerCase().includes(this.deviceSearchKeyword.toLowerCase());
      });
    },

    filteredGroups() {
      if (!this.groupSearchKeyword) return this.allGroups;

      return this.allGroups.filter(group => {
        const name = group.name || '';
        const code = group.code || '';
        const displayName = group.displayName || '';
        return name.toLowerCase().includes(this.groupSearchKeyword.toLowerCase()) ||
               code.toLowerCase().includes(this.groupSearchKeyword.toLowerCase()) ||
               displayName.toLowerCase().includes(this.groupSearchKeyword.toLowerCase());
      });
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.loadUsers();
        this.loadDevices();
        this.loadGroups();
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    },
    'formData.targetType'() {
      // 切换类型时清空已选择的目标
      this.formData.selectedUsers = [];
      this.formData.selectedDevices = [];
      this.formData.selectedGroups = [];
    }
  },
  created() {
    // 创建防抖版本的提交函数，防止用户快速点击
    this.debouncedSubmit = debounce(this.handleSubmit, 1000, {
      leading: true,  // 立即执行第一次点击
      trailing: false // 不执行延迟后的调用
    });
  },

  beforeDestroy() {
    // 组件销毁前取消防抖函数
    if (this.debouncedSubmit) {
      this.debouncedSubmit.cancel();
    }
  },
  methods: {
    /**
     * 版本名称验证规则
     */
    validateVersionNameRule(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      if (!validateVersionName(value)) {
        callback(new Error('版本名称格式不正确，支持格式：x.y 或 x.y.z，可选后缀（如：1.0, 1.0.1, 1.0-debug, 1.0.1-alpha）'));
        return;
      }

      callback();
    },

    /**
     * 验证版本名称
     */
    validateVersionName() {
      this.$refs.publishForm.validateField('versionName');
    },

    /**
     * 文件上传前验证
     */
    beforeUpload(file) {
      const validation = validateApkFile(file);
      if (!validation.valid) {
        this.$message.error(validation.message);
        return false;
      }
      
      this.uploadProgress = 0;
      this.uploadStatus = '';
      return true;
    },

    /**
     * 自定义文件上传
     */
    async handleFileUpload(options) {
      const { file } = options;
      
      try {
        this.uploadProgress = 10;
        this.uploadStatus = 'uploading';

        // 计算文件MD5
        const md5Hash = await this.calculateMD5(file);
        this.uploadProgress = 30;

        // 使用专用的APK上传API（保留.apk扩展名）
        const uploadResult = await uploadApkFile(file);
        this.uploadProgress = 90;

        // 构建上传结果
        const result = {
          name: file.name,
          size: file.size,
          md5: md5Hash,
          cosKey: uploadResult.key,
          cosUrl: uploadResult.url,
        };

        this.uploadProgress = 100;
        this.uploadStatus = 'success';
        
        // 直接设置上传结果
        this.uploadedFile = result;
        this.$message.success('文件上传成功');

      } catch (error) {
        this.uploadProgress = 0;
        this.uploadStatus = 'exception';
        this.uploadedFile = null;
        this.$message.error('文件上传失败，请重试');
      }
    },

    /**
     * 上传成功回调
     */
    onUploadSuccess(response) {
      this.uploadedFile = response;
      this.$message.success('文件上传成功');
    },

    /**
     * 上传失败回调
     */
    onUploadError() {
      this.uploadedFile = null;
      this.$message.error('文件上传失败，请重试');
    },

    /**
     * 文件移除回调
     */
    onFileRemove() {
      this.uploadedFile = null;
      this.uploadProgress = 0;
      this.uploadStatus = '';
    },

    /**
     * 计算文件MD5
     */
    calculateMD5(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const hash = CryptoJS.MD5(CryptoJS.lib.WordArray.create(e.target.result));
            resolve(hash.toString());
          } catch (error) {
            reject(error);
          }
        };
        reader.onerror = reject;
        reader.readAsArrayBuffer(file);
      });
    },

    /**
     * 提交表单
     */
    async handleSubmit() {
      try {
        // 防止重复提交
        if (this.publishing) {
          return;
        }

        // 表单验证
        const valid = await this.$refs.publishForm.validate();
        if (!valid) return;

        if (!this.uploadedFile) {
          this.$message.error('请先上传ZIP文件');
          return;
        }

        // 定向发布验证
        if (this.formData.releaseType === 'TARGETED' && this.getSelectedCount() === 0) {
          this.$message.error('定向发布需要选择目标用户、设备或用户组');
          return;
        }

        this.publishing = true;

        // 构建提交数据
        const submitData = {
          ...this.formData,
          apkFileName: this.uploadedFile.name,
          cosKey: this.uploadedFile.cosKey,
          cosUrl: this.uploadedFile.cosUrl,
          fileSize: this.uploadedFile.size,
          fileMd5: this.uploadedFile.md5,
        };

        // 如果是定向发布，添加目标数据
        if (this.formData.releaseType === 'TARGETED') {
          let userIds = [];
          let deviceIds = [];
          let groupIds = [];

          if (this.formData.targetType === 'USER') {
            userIds = this.formData.selectedUsers.map(user => user.id);
          } else if (this.formData.targetType === 'DEVICE') {
            deviceIds = this.formData.selectedDevices.map(device => device.id);
          } else if (this.formData.targetType === 'GROUP') {
            groupIds = this.formData.selectedGroups.map(group => group.id);
            // 直接使用已获取的用户组成员用户ID
            userIds = this.groupMemberUsers.map(user => user.id);
          }

          submitData.targetData = {
            deviceIds,
            groupIds: [], // 保留字段但设为空数组
            // 添加完整的用户/设备信息
            users: this.formData.targetType === 'USER' ?
              this.formData.selectedUsers :
              (this.formData.targetType === 'GROUP' ? this.groupMemberUsers : []),
            devices: this.formData.targetType === 'DEVICE' ? this.formData.selectedDevices : []
            // 移除userIds字段，移除groups字段
          };
        }

        // 调用发布API
        await publishVersion(submitData);

        this.$message.success('版本发布成功！');

        // 短暂延迟后关闭对话框，让用户看到成功状态
        setTimeout(() => {
          this.$emit('success');
          this.resetForm();
        }, 1000);

      } catch (error) {
        this.$message.error('版本发布失败，请重试');
      } finally {
        this.publishing = false;
      }
    },

    /**
     * 富文本内容变化处理
     */
    onUpdateLogChange(content) {
      this.formData.updateLog = content;
    },

    /**
     * 获取已选择的目标数量
     */
    getSelectedCount() {
      switch (this.formData.targetType) {
        case 'USER':
          return this.formData.selectedUsers.length;
        case 'DEVICE':
          return this.formData.selectedDevices.length;
        case 'GROUP':
          return this.formData.selectedGroups.length;
        default:
          return 0;
      }
    },

    // 加载用户列表
    async loadUsers() {
      this.loadingUsers = true;
      try {
        this.allUsers = await getUserList();
      } catch (error) {
        this.$message.error('加载用户列表失败');
        this.allUsers = [];
      } finally {
        this.loadingUsers = false;
      }
    },

    // 加载设备列表
    async loadDevices() {
      this.loadingDevices = true;
      try {
        this.allDevices = await getDeviceList();
      } catch (error) {
        this.$message.error('加载设备列表失败');
        this.allDevices = [];
      } finally {
        this.loadingDevices = false;
      }
    },

    // 加载用户组列表
    async loadGroups() {
      this.loadingGroups = true;
      try {
        this.allGroups = await getRoleList();
      } catch (error) {
        this.$message.error('加载用户组列表失败');
        this.allGroups = [];
      } finally {
        this.loadingGroups = false;
      }
    },

    // 用户选择相关方法
    isUserSelected(user) {
      return this.formData.selectedUsers.some(selected => selected.id === user.id);
    },

    handleUserChange(user, checked) {
      if (checked) {
        this.formData.selectedUsers.push({
          id: user.id,
          name: user.displayName || user.name || '未知用户'
        });
      } else {
        this.formData.selectedUsers = this.formData.selectedUsers.filter(
          selected => selected.id !== user.id
        );
      }
    },

    selectAllUsers() {
      this.formData.selectedUsers = this.filteredUsers.map(user => ({
        id: user.id,
        name: user.displayName || user.name || '未知用户'
      }));
    },

    clearAllUsers() {
      this.formData.selectedUsers = [];
    },

    // 设备选择相关方法
    isDeviceSelected(device) {
      return this.formData.selectedDevices.some(selected => selected.id === device.id);
    },

    handleDeviceChange(device, checked) {
      if (checked) {
        this.formData.selectedDevices.push({
          id: device.id,
          deviceId: device.deviceId,
          brand: device.brand,
          model: device.model,
          osVersion: device.osVersion,
          name: device.displayName || device.deviceId || '未知设备'
        });
      } else {
        this.formData.selectedDevices = this.formData.selectedDevices.filter(
          selected => selected.id !== device.id
        );
      }
    },

    selectAllDevices() {
      this.formData.selectedDevices = this.filteredDevices.map(device => ({
        id: device.id,
        deviceId: device.deviceId,
        brand: device.brand,
        model: device.model,
        osVersion: device.osVersion,
        name: device.displayName || device.deviceId || '未知设备'
      }));
    },

    clearAllDevices() {
      this.formData.selectedDevices = [];
    },

    // 用户组选择相关方法
    isGroupSelected(group) {
      return this.formData.selectedGroups.some(selected => selected.id === group.id);
    },

    async handleGroupChange(group, checked) {
      if (checked) {
        this.formData.selectedGroups.push({
          id: group.id,
          name: group.displayName || group.name || '未知用户组'
        });
      } else {
        this.formData.selectedGroups = this.formData.selectedGroups.filter(
          selected => selected.id !== group.id
        );
      }

      // 实时获取用户组成员
      await this.loadGroupMembers();
    },

    selectAllGroups() {
      this.formData.selectedGroups = this.filteredGroups.map(group => ({
        id: group.id,
        name: group.displayName || group.name || '未知用户组'
      }));
    },

    clearAllGroups() {
      this.formData.selectedGroups = [];
      this.groupMemberUsers = [];  // 清空用户组成员
    },

    // 加载用户组成员
    async loadGroupMembers() {
      if (this.formData.selectedGroups.length === 0) {
        this.groupMemberUsers = [];
        return;
      }

      try {
        const groupIds = this.formData.selectedGroups.map(group => group.id);
        const memberPromises = groupIds.map(groupId => getRoleMembers(groupId));
        const memberResults = await Promise.all(memberPromises);

        // 合并所有成员并去重
        const allMembers = memberResults.flat();
        const uniqueMembers = allMembers.filter((member, index, self) =>
          index === self.findIndex(m => m.id === member.id)
        );

        this.groupMemberUsers = uniqueMembers;
      } catch (error) {
        this.$message.error('获取用户组成员失败');
        this.groupMemberUsers = [];
      }
    },

    /**
     * 关闭对话框
     */
    handleClose() {
      if (this.publishing) {
        this.$message.warning('正在发布中，请稍候...');
        return;
      }
      
      this.dialogVisible = false;
      this.resetForm();
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.formData = {
        versionName: '',
        versionCode: null,
        updateLog: '',
        isForce: false,
        isActive: true,
        releaseType: 'GLOBAL',
        targetType: 'USER',
        selectedUsers: [],
        selectedDevices: [],
        selectedGroups: [],
      };

      this.fileList = [];
      this.uploadedFile = null;
      this.uploadProgress = 0;
      this.uploadStatus = '';

      // 重置搜索关键词
      this.userSearchKeyword = '';
      this.deviceSearchKeyword = '';
      this.groupSearchKeyword = '';

      // 重置用户组成员相关状态
      this.groupMemberUsers = [];
      this.showGroupMembers = false;

      this.$nextTick(() => {
        this.$refs.publishForm?.resetFields();
        // 重置富文本编辑器内容
        if (this.$refs.ProWangeEditorRef) {
          this.$refs.ProWangeEditorRef.echo('');
        }
      });
    },

    /**
     * 格式化文件大小
     */
    formatFileSize,
  },
};
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.upload-container {
  .file-info {
    margin-top: 8px;
  }
}

.publish-settings {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  
  .el-checkbox {
    margin-bottom: 8px;
  }
}

.ml-4 {
  margin-left: 16px;
}

.mt-2 {
  margin-top: 8px;
}

// 上传组件样式调整
:deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
}

:deep(.el-upload__tip) {
  margin-top: 8px;
  font-size: 12px;
}

:deep(.el-progress) {
  margin-top: 8px;
}

.publish-tips {
  margin: 16px 0;

  ul {
    margin: 8px 0 0 0;
    padding-left: 0;
    list-style: none;

    li {
      display: flex;
      align-items: center;
      margin: 4px 0;
      font-size: 14px;

      i {
        margin-right: 8px;
        font-size: 16px;
      }

      .el-icon-check {
        color: #67c23a;
      }

      .el-icon-close {
        color: #f56c6c;
      }

      &.completed {
        color: #67c23a;
      }
    }
  }
}

// 定向发布相关样式
.target-selection {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8px;
}

.selection-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.selection-content {
  max-height: 300px;
  overflow-y: auto;
}

.target-list {
  padding: 10px;
}

.target-item {
  display: block;
  width: 100%;
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  transition: all 0.3s;
}

.target-item:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
}

.empty-state {
  padding: 40px;
  text-align: center;
}

.target-stats {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.group-members {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.group-members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.group-members-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 120px;
  overflow-y: auto;
}

.member-tag {
  margin: 0;
}
</style>
