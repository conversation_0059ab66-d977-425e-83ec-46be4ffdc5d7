# 日志控制模块

## 📋 功能概述

日志控制模块提供了完整的后台日志查看和管理功能，包括：

- 📊 实时统计数据展示
- 🔍 多维度搜索过滤
- 📋 分页日志列表
- 🔍 详细日志信息查看

## 🏗️ 模块结构

```
src/views/logcontrol/
├── logAnalysis.vue              # 主页面组件
├── components/
│   ├── LogStatistics.vue        # 统计卡片组件
│   ├── LogFilter.vue           # 过滤搜索组件
│   ├── LogTable.vue            # 日志表格组件
│   └── LogDetail.vue           # 详情弹窗组件
└── README.md                   # 说明文档
```

## 🚀 使用方法

### 1. 访问入口
- 在左侧菜单中找到"日志控制" -> "日志查看"
- 或直接访问路径：`/logAnalysis`

### 2. 功能操作

**查看统计信息**
- 页面顶部显示4个统计卡片：日志总数、今日日志、错误日志、活跃设备

**搜索过滤**
- 时间范围：选择查看特定时间段的日志
- 日志级别：筛选DEBUG/INFO/WARN/ERROR级别
- 设备筛选：按设备ID过滤日志
- 用户筛选：按用户ID过滤日志
- 关键词搜索：在日志内容中搜索关键词

**查看详情**
- 点击表格行或"查看详情"按钮打开详情弹窗
- 详情包含完整的日志信息和格式化的详细数据

## 🔧 API接口

模块使用以下API接口（位于 `src/logcontrol/api/logApi.js`）：

- `getLogListWithPagination(params)` - 获取分页日志列表
- `getLogStatistics()` - 获取统计数据
- `getDeviceList()` - 获取设备列表

## 📝 开发说明

### 当前状态
- ✅ 前端界面完全实现
- ✅ 模拟数据正常工作
- ⏳ 等待后端API接口对接

### 后端对接
当后端API准备就绪时，需要：
1. 取消 `src/logcontrol/api/logApi.js` 中的模拟数据代码注释
2. 启用真实的API调用代码
3. 根据实际API响应格式调整数据处理逻辑

### 扩展功能
未来可以添加的功能：
- 实时日志监控
- 日志图表分析
- 更多过滤维度
- 批量操作功能

## 🎨 样式说明

模块遵循Element UI设计规范：
- 使用统一的色彩方案
- 响应式布局设计
- 与现有系统风格保持一致

## 🐛 问题排查

如果遇到问题，请检查：
1. 路由配置是否正确
2. 组件文件是否完整
3. API接口是否正常响应
4. 浏览器控制台是否有错误信息
