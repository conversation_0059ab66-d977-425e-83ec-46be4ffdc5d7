<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:58:43
 * @Description: 退货单
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>总退货数量：{{ totalData?.orderNum || 0 }}</div>
          <div>总退货商品金额：{{ totalData?.itemAmount || 0 }}</div>
          <div>总退运费：{{ totalData?.shippingFeeAmount || 0 }}</div>
          <div>总退订单金额：{{ totalData?.totalAmount || 0 }}</div>
        </div>
      </template>
      <!-- <template #consigneeAddress="searchSlot">
        <el-select v-model="a">
          <el-option label="北京" value="北京"></el-option>
        </el-select>
      </template> -->
      <template #type="slotProps">
        {{ slotProps.row.type.label }}
      </template>
      <template #actions="slotProps">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleInfo(slotProps.row)"
          >
            查看
          </el-button>
          <el-button
            v-if="slotProps.row.auditStatus == 'WAIT_AUDIT'"
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleAudit(slotProps.row)"
          >
            审核
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      v-loading="infoLoading"
      :value="dialogVisible"
      :title="dialogTitle"
      size="95%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <!-- <el-timeline>
        <el-timeline-item v-for="(activity, index) in activities" :key="index" :icon="activity.icon" :type="activity.type"
          :color="activity.color" :size="activity.size" :timestamp="activity.timestamp">
          {{ activity.content }}
        </el-timeline-item>
      </el-timeline> -->
      <div v-if="orderInfo" class="order-fix">
        <!-- <p class="title-p m-b-12">售后信息</p> -->
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="售后信息"
            :column="3"
            border
          >
            <el-descriptions-item label="退货状态">{{
              statusList.filter((ele) => {
                return ele.value == orderInfo.processStatus;
              })[0].label
            }}</el-descriptions-item>
            <el-descriptions-item label="退货单编号">{{
              orderInfo.code
            }}</el-descriptions-item>
            <el-descriptions-item label="关联订单编号">{{
              orderInfo.tradeOrderNum
            }}</el-descriptions-item>
            <el-descriptions-item label="支付金额（元）">{{
              orderInfo.tradeOrder.actualAmount
            }}</el-descriptions-item>
            <el-descriptions-item label="申请时间">{{
              orderInfo.applyTime
            }}</el-descriptions-item>
            <el-descriptions-item label="申请退款金额">{{
              orderInfo.refundAmount
            }}</el-descriptions-item>
            <el-descriptions-item label="关联物流单号">{{
              orderInfo.reverseReturnOrder?.logisticsCode
            }}</el-descriptions-item>
            <el-descriptions-item label="申请原因">{{
              orderInfo.reason
            }}</el-descriptions-item>
            <el-descriptions-item label="下单时间">{{
              orderInfo.tradeOrder.createdAt
            }}</el-descriptions-item>
            <el-descriptions-item label="下单手机号">{{
              orderInfo.tradeOrder.consigneePhone
            }}</el-descriptions-item>
            <el-descriptions-item label="店铺名称">{{
              orderInfo.tradeOrder.consigneeName
            }}</el-descriptions-item>
            <el-descriptions-item label="订单金额（元）">{{
              orderInfo.tradeOrder.paidAmount
            }}</el-descriptions-item>
            <el-descriptions-item label="收货详细地址">{{
              orderInfo.tradeOrder.consigneeAddress
            }}</el-descriptions-item>
          </el-descriptions>
          <p v-if="orderInfo?.evidenceFiles?.length > 0" class="text-p">
            <label class="p-label">凭证：</label>
            <el-image
              v-for="(item, index) in orderInfo?.evidenceFiles?.split(',')"
              :key="index"
              style="width: 100px; height: 100px; margin-left: 5px"
              :src="item"
            ></el-image>
          </p>
        </div>
        <!-- 商品信息 -->
        <div class="m-t-8">
          <p class="title-p m-b-12">订单详情</p>
          <!-- <ProTable ref="ProSPXXTable" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="spxxColumns" :show-pagination="false" :showLoading="false"
            :data="orderInfo.tradeOrder.tradeOrderDetailList || []" :showSetting="false" :showSearch="false"
            :showTableOperator="false" sticky :height="200">
          </ProTable> -->
          <DataTable
            :columns="spxxColumns"
            :show-setting="false"
            :show-pagination="false"
            :show-search="false"
            row-key="id"
            :data="orderInfo.reverseOrderDetailList"
            sticky
            :height="250"
          >
          </DataTable>
        </div>
        <!-- 物流信息 -->
        <div class="m-t-8">
          <p class="title-p m-b-12">物流信息</p>
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in orderInfo.reverseOrderRecordList"
              :key="index"
              :timestamp="activity.createdAt"
            >
              {{ activity.title }}{{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </ProDrawer>
    <!-- 审核 -->
    <ProDialog
      :value="dialogAuditVisible"
      :title="'审核'"
      width="600px"
      :top="'10%'"
      :no-footer="false"
      @ok="handleAuditDialogOk"
      @cancel="dialogAuditVisible = false"
    >
      <el-form ref="auditFormRef" :model="auditForm" label-width="120px">
        <el-form-item
          prop="audit"
          label="审核状态:"
          :rules="[
            { required: true, message: '请选择审核状态', trigger: 'blur' },
          ]"
        >
          <el-radio-group v-model="auditForm.audit">
            <el-radio label="REFUSE">驳回</el-radio>
            <el-radio label="APPROVE">同意</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="auditForm.audit == 'APPROVE'"
          prop="refundShippingFee"
          label="是否退运费:"
          :rules="[
            { required: true, message: '请选择是否退运费', trigger: 'blur' },
          ]"
        >
          <el-radio-group v-model="auditForm.refundShippingFee">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
          <!-- <el-input v-model="auditForm.remarks" type="textarea"></el-input> -->
        </el-form-item>
      </el-form>
    </ProDialog>
  </div>
</template>
<script>
import {
  reverseOrderPcPageApi,
  reverseOrderAuditApi,
  reverseOrderOneApi,
  reverseOrderStatisticsApi,
} from "@/api/reverseOrder";
export default {
  name: "Return",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      active: 3,
      orderInfo: null,
      // 列表
      spareiTypeList: [],
      totalData: {},
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        aduitState: null,
        name: null,
      },
      statusList: [
        {
          label: "待审核",
          value: "PENDING_AUDIT",
        },
        { label: "平台驳回", value: "REJECT" },
        { label: "待用户寄回", value: "WAIT_SEND_BACK" },
        { label: "待卖家入库", value: "WAIT_SELLER_RECEIVED" },
        { label: "待买家签收", value: "WAIT_BUYER_SIGNED" },
        { label: "退款中", value: "REFUNDING" },
        { label: "售后成功", value: "SUCCESS" },
        { label: "售后关闭", value: "CLOSED" },
      ],
      columns: [
        {
          dataIndex: "auditStatus",
          title: "退货状态",
          isSearch: true,
          formSpan: 6,
          valueType: "select",
          clearable: true,
          option: [
            { label: "待审核", value: "PENDING_AUDIT" },
            { label: "平台驳回", value: "REJECT" },
            { label: "待用户寄回", value: "WAIT_SEND_BACK" },
            { label: "待卖家入库", value: "WAIT_SELLER_RECEIVED" },
            { label: "待买家签收", value: "WAIT_BUYER_SIGNED" },
            { label: "退款中", value: "REFUNDING" },
            { label: "售后成功", value: "SUCCESS" },
            { label: "售后关闭", value: "CLOSED" },
          ],
          optionskey: { label: "label", value: "value" },
        },
        {
          dataIndex: "reverseType",
          title: "审核状态",
          isSearch: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [
            { label: "待审核", value: "WAIT_AUDIT" },
            { label: "通过", value: "APPROVE" },
            { label: "拒绝", value: "REFUSE" },
          ],
          optionskey: { label: "label", value: "value" },
        },
        {
          dataIndex: "orderType",
          title: "订单类型",
          isTable: true,
          formatter: (row) => row.orderType?.label,
          isSearch: true,
          valueType: "select",
          option: [
            { label: "销售订单", value: "SALE" },
            { label: "领料单", value: "APPLY" },
          ],
          width: 100,
        },

        {
          dataIndex: "code",
          title: "售后单编号",
          isTable: true,
          isSearch: true,
          formSpan: 6,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "tradeOrderNum",
          title: "关联订单编号",
          isTable: true,
          isSearch: true,
          formSpan: 6,
          width: 200,
          valueType: "input",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          formSpan: 6,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          formSpan: 6,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "buyPhone",
          title: "下单手机号",
          isTable: true,
          isSearch: true,
          formSpan: 6,
          valueType: "input",
          minWidth: 100,
        },

        {
          dataIndex: "refundAmount",
          title: "申请退款金额",
          isTable: true,
        },
        {
          dataIndex: "refundShippingFee",
          title: "实退运费",
          isTable: true,
          formatter: (row) => row.refundShippingFee || 0,
        },
        // {
        //   dataIndex: "reverseTypeData",
        //   title: "售后类型",
        //   isSearch: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "select",
        //   option: [
        //     {
        //       label: "仅退款",
        //       value: "REFUND_MONEY",
        //     },
        //     { label: "退货退款", value: "RETURN_MONEY_GOODS" },
        //   ],
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        // {
        //   dataIndex: "reverseType",
        //   title: "售后类型",
        //   isTable: true,
        //   formatter: (row) => {
        //     let text = "";
        //     const arr = [
        //       {
        //         label: "仅退款",
        //         value: "REFUND_MONEY",
        //       },
        //       { label: "退货退款", value: "RETURN_MONEY_GOODS" },
        //     ];
        //     arr.map((ele) => {
        //       if (ele.value === row.reverseType) {
        //         text = ele.label;
        //       }
        //     });
        //     return text;
        //   },
        // },

        {
          dataIndex: "processStatus",
          title: "退货状态",
          isTable: true,
          formatter: (row) => {
            let text = "";
            const arr = [
              {
                label: "待审核",
                value: "PENDING_AUDIT",
              },
              { label: "平台驳回", value: "REJECT" },
              { label: "待用户寄回", value: "WAIT_SEND_BACK" },
              { label: "待卖家入库", value: "WAIT_SELLER_RECEIVED" },
              { label: "待买家签收", value: "WAIT_BUYER_SIGNED" },
              { label: "退款中", value: "REFUNDING" },
              { label: "售后成功", value: "SUCCESS" },
              { label: "售后关闭", value: "CLOSED" },
            ];
            arr.map((ele) => {
              if (ele.value === row.processStatus) {
                text = ele.label;
              }
            });
            return text;
          },
        },
        // {
        //   dataIndex: "isRefundShippingFee",
        //   title: "是否退运费",
        //   isTable: true,
        //   formatter: (row) => {
        //     return row.isRefundShippingFee ? "是" : "否";
        //   },
        // },
        {
          dataIndex: "auditStatus",
          title: "审核状态",
          isTable: true,
          formatter: (row) => {
            let text = "";
            const arr = [
              {
                label: "待审核",
                value: "WAIT_AUDIT",
              },
              { label: "通过", value: "APPROVE" },
              { label: "拒绝", value: "REFUSE" },
            ];
            arr.map((ele) => {
              if (ele.value === row.auditStatus) {
                text = ele.label;
              }
            });
            return text;
          },
        },
        {
          dataIndex: "applyTime",
          title: "申请时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      //新增
      methodType: "add",
      confirmLoading: false,
      dialogTitle: "",
      dialogVisible: false,
      dialogAuditVisible: false,
      auditForm: {
        audit: "REFUSE",
        refundShippingFee: false,
      },
      spxxColumns: [
        {
          dataIndex: "itemName",
          isTable: true,
          title: "商品名称",
        },
        {
          dataIndex: "itemId",
          isTable: true,
          title: "商品编号",
          width: 180,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
        },
        {
          dataIndex: "saleUnitPrice",
          isTable: true,
          title: "商品单价（元）",
          formatter: (row) => row.tradeOrderDetail.saleUnitPrice,
        },

        {
          dataIndex: "itemNum",
          isTable: true,
          title: "购买数量",
        },
        {
          dataIndex: "reverseItemNum",
          isTable: true,
          title: "退货数量",
        },
        {
          dataIndex: "discountAmount",
          isTable: true,
          title: "优惠金额（元）",
          formatter: (row) => "-" + row.discountAmount,
        },
        {
          dataIndex: "refundAmount",
          isTable: true,
          title: "退款金额（元）",
        },
        {
          dataIndex: "actualPayAmount",
          isTable: true,
          title: "实付金额（元）",
        },
      ],
      rowInfo: null,
      infoLoading: false,
    };
  },
  mounted() {
    this.$refs.ProTable?.refresh();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign({}, parameter);
      reverseOrderPcPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getOrderStatisticsFn(requestParameters);
    },
    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看" + row.code;
      this.infoLoading = true;
      reverseOrderOneApi(row.id)
        .then((res) => {
          this.orderInfo = res.data;
          this.dialogVisible = true;
        })
        .finally((_) => {
          this.infoLoading = false;
        });
    },
    // 退货数据统计
    getOrderStatisticsFn(parameter) {
      reverseOrderStatisticsApi(parameter).then((res) => {
        this.totalData = res.data;
      });
    },
    handleAudit(row) {
      this.dialogAuditVisible = true;
      this.rowInfo = row;

      this.auditForm.audit = "REFUSE";
      this.auditForm.refundShippingFee = false;
    },
    handleAuditDialogOk() {
      this.$refs["auditFormRef"].validate((valid) => {
        if (valid) {
          this.dialogAuditVisible = false;
          reverseOrderAuditApi({
            ...this.auditForm,
            reverseOrderId: this.rowInfo.id,
          })
            .then((res) => {
              this.$message.success(res.message);
            })
            .finally((_) => {
              this.$refs.ProTable?.refresh();
              this.dialogVisible = false;
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-upload--picture-card,
::v-deep .el-upload-list__item {
  width: 120px;
  height: 120px;
}

.sp-content {
  margin-top: 20px;
}

.add-sp-box {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translate(-50%, 0);
}

.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-fix {
  margin-left: 20px;
  font-size: 14px;

  .red {
    color: #d14b50;
  }

  .text-p {
    &.right {
      text-align: right;
    }

    color: #606266;

    .p-label {
      color: #606266;
      font-weight: 700;
    }

    margin-top: 15px;
  }

  .content-fixed {
    display: flex;
    justify-content: space-between;
  }

  .btn-p {
    margin-top: 15px;

    .el-button {
      padding: 8px 29px;
    }
  }

  .order-border-box {
    border: dashed 1px #ccc;
    padding: 10px;
  }

  .title-p {
    width: 100%;
    padding: 5px 10px;
    color: #409eff;
    position: relative;
    margin: 20px auto;
    font-size: 16px;
    font-weight: 800;
    border-bottom: 1px solid #dcdfe6;

    &::before {
      content: "";
      width: 5px;
      height: 20px;
      background: #409eff;
      display: inline-block;
      position: absolute;
      left: -1px;
      top: 4px;
    }
  }
}
</style>
