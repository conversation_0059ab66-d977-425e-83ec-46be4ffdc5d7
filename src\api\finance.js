/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-01 17:08:25
 * @Description:
 */
import { get, post, put, del, postDown } from "@/utils/request";

// ================================  报销管理  ================================
// 分页查询
export const expenseListApi = (data) => post("/expense/page", data);
// 明细
export const expenseDetailApi = (id) => get(`/expense/${id}`);
// 审核
export const expenseAuditApi = (data) => put("/expense", data);

// ================================  抄表维修价格管理  ================================
//  分页查询
export const meterRepairPricePageApi = (data) =>
  post("/repairMonthlyPrice/page", data);
// 添加
export const meterRepairPriceAddApi = (data) =>
  post("/repairMonthlyPrice/add", data);
// 编辑
export const meterRepairPriceEditApi = (data) =>
  post("/repairMonthlyPrice/edit", data);
// 删除
export const meterRepairPriceDelApi = (id) => del(`/repairMonthlyPrice/${id}`);

// ================================  采购单明细  ================================
// 列表
export const purchaseOrderDetailListApi = (data) =>
  post("/finance/pageFinancePurchase", data);
// 导出采购单明细
export const purchaseOrderDetailExportApi = (data) =>
  postDown("/finance/exportFinancePurchase", data);
// 统计数据
export const purchaseOrderDetailStatisticsApi = (data) =>
  post("/finance/financePurchaseSummary", data);

// ================================  销售单明细  ================================
// 列表
export const saleOrderDetailListApi = (data) =>
  post("/finance/pageFinanceSales", data);
// 导出销售单明细
export const saleOrderDetailExportApi = (data) =>
  postDown("/finance/exportFinanceSales", data);
// 统计数据
export const saleOrderDetailStatisticsApi = (data) =>
  post("/finance/financeSalesSummary", data);

// ================================  入库单明细  ================================
// 列表
export const purchaseInboundDetailListApi = (data) =>
  post("/finance/pageFinanceInstore", data);
// 导出入库单明细
export const purchaseInboundDetailExportApi = (data) =>
  postDown("/finance/exportFinanceInstore", data);
// 统计数据
export const purchaseInboundDetailStatisticsApi = (data) =>
  post("/finance/financeInstoreSummary", data);

// ================================  领料出库明细  ================================
// 列表
export const claimOrderOutboundDetailListApi = (data) =>
  post("/finance/pageFinanceApply", data);
// 导出领料出库明细
export const claimOrderOutboundDetailExportApi = (data) =>
  postDown("/finance/exportFinanceApply", data);
// 统计
export const claimOrderOutboundDetailStatisticsApi = (data) =>
  post("/finance/financeApplySummary", data);
// ================================  机器采购明细  ================================
// 列表
export const purchaseMachineDetailListApi = (data) =>
  post("/finance/getFinanceMachinePurchase", data);
// 导出机器采购明细
export const purchaseMachineDetailExportApi = (data) =>
  postDown("/finance/exportFinanceMachinePurchase", data);
// 统计数据
export const purchaseMachineDetailStatisticsApi = (data) =>
  post("/finance/financeMachinePurchaseSummary", data);

// ================================  机器销售明细  ================================
// 列表
export const saleMachineDetailListApi = (data) =>
  post("/finance/getFinanceMachineSalse", data);
// 导出机器销售明细
export const saleMachineDetailExportApi = (data) =>
  postDown("/finance/exportFinanceMachineSalse", data);
// 统计数据
export const saleMachineDetailStatisticsApi = (data) =>
  post("/finance/financeMachineSalesSummary", data);

// ================================  机器出库明细  ================================
// 列表
export const machineOutboundDetailListApi = (data) =>
  post("/finance/getFinanceMachineOut", data);
// 导出机器出库明细
export const machineOutboundDetailExportApi = (data) =>
  postDown("/finance/exportFinanceMachineOut", data);
// 统计数据
export const machineOutboundDetailStatisticsApi = (data) =>
  post("/finance/summaryMachineOut", data);

// ================================  机器入库明细  ================================
// 列表
export const machineInboundDetailListApi = (data) =>
  post("/finance/getFinanceMachineInstore", data);
// 导出机器出库明细
export const machineInboundDetailExportApi = (data) =>
  postDown("/finance/exportFinanceMachineInstore", data);
// 统计数据
export const machineInboundDetailStatisticsApi = (data) =>
  post("/finance/summaryMachineInstore", data);

// ================================  应收账款汇总  ================================
// 分页查询
export const receivableSummaryListApi = (data) =>
  post("/finance/collection/pageList", data);
// 汇总数据
export const receivableSummaryStatisticsApi = (data) =>
  post("/finance/collection/summary", data);
// 导出应收汇总表
export const receivableSummaryExportApi = (data) =>
  postDown("/finance/collection/exportFinanceCollection", data);
// 调整期初金额
export const receivableSummaryAdjustApi = (data) =>
  post("/finance/collection/adjust", data);

// ================================  耗材应收账款明细  ================================
//分页查询
export const receivableConsumableSummaryListApi = (data) =>
  post("/finance/collection/materialPageList", data);
// 统计数据
export const receivableBaseSummaryStatisticsApi = (data) =>
  post("/finance/collection/detail/summary", data);
// 导出耗材应收款明细
export const receivableConsumableSummaryExportApi = (data) =>
  postDown("/finance/collection/exportFinanceMaterial", data);

// ================================  机器应收账款明细  ================================
//分页查询
export const receivableMachineSummaryListApi = (data) =>
  post("/finance/collection/machinePageList", data);
// 导出机器应收款明细
export const receivableMachineSummaryExportApi = (data) =>
  postDown("/finance/collection/exportFinanceMachine", data);

// ================================  维修应收账款明细  ================================
//分页查询
export const receivableRepairSummaryListApi = (data) =>
  post("/finance/collection/repairPageList", data);
// 导出维修应收款明细
export const receivableRepairSummaryExportApi = (data) =>
  postDown("/finance/collection/exportFinanceRepair", data);

// ================================  抄表应收账款明细  ================================
//分页查询
export const receivableMeterSummaryListApi = (data) =>
  post("/finance/collection/operationPageList", data);
// 导出抄表应收款明细
export const receivableMeterSummaryExportApi = (data) =>
  postDown("/finance/collection/exportFinanceOperation", data);

// ================================  应收账款核销  ================================
export const receivableWriteOffApi = (data) =>
  post("/finance/collection/receiveCancelVerify", data);
// 批量核销
export const receivableWriteOffBatchApi = (data) =>
  post("/finance/collection/receiveCancelVerifyBatch", data);

// ================================  应付账款汇总  ================================
// 分页查询
export const payableSummaryListApi = (data) =>
  post("/finance/payment/pageList", data);
// 汇总数据
export const payableSummaryStatisticsApi = (data) =>
  post("/finance/payment/summary", data);
// 导出应付款汇总表
export const payableSummaryExportApi = (data) =>
  postDown("/finance/payment/exportFinancePayment", data);
// 调整期初金额
export const payableSummaryAdjustApi = (data) =>
  post("/finance/payment/adjust", data);

// ================================  耗材应付账款明细  ================================
// 分页查询
export const payableConsumableSummaryListApi = (data) =>
  post("/finance/payment/materialPageList", data);
// 应付数据统计
export const payableBaseSummaryStatisticsApi = (data) =>
  post("/finance/payment/detail/summary", data);
// 导出耗材应付款明细
export const payableConsumableSummaryExportApi = (data) =>
  postDown("/finance/payment/exportFinanceMaterial", data);
// 批量核销付款账单
export const payableConsumableSummaryVerifyApi = (data) =>
  post("/finance/collection/paymentCancelVerifyBatch", data);

// ================================  机器应付账款明细  ================================
// 分页查询
export const payableMachineSummaryListApi = (data) =>
  post("/finance/payment/machinePageList", data);
// 导出机器应付款明细
export const payableMachineSummaryExportApi = (data) =>
  postDown("/finance/payment/exportFinanceMachine", data);

// ================================  应付账款核销  ================================
export const payableWriteOffApi = (data) =>
  post("/finance/collection/paymentCancelVerify", data);

// ================================  耗材预付账款汇总  ================================
// 分页查询
export const consumableAdvanceSummaryApi = (data) =>
  post("/finance/collection/consumableAdvanceSummary", data);

// ================================  耗材预付账款明细  ================================
// 分页查询
export const prepayConsumableSummaryListApi = (data) =>
  post("/finance/payment/materialPrePageList", data);
// 导出耗材预付款明细
export const prepayConsumableSummaryExportApi = (data) =>
  postDown("/finance/payment/exportFinancePreMaterial", data);

// ================================  机器预付账款明细  ================================
// 分页查询
export const prepayMachineSummaryListApi = (data) =>
  post("/finance/payment/machinePrePageList", data);
// 导出机器预付款明细
export const prepayMachineSummaryExportApi = (data) =>
  postDown("/finance/payment/exportFinancePreMachine", data);

// ================================  付款清单 ================================
// 分页查询
export const paymentListApi = (data) => post("/pay-order-list/page", data);
// ================================  月度毛利  ================================
// 分页查询
export const prepayMonthSummaryListApi = (data) =>
  post("/statistics/queryMonthGrossProfitList", data);
// 月度毛利汇总数据
export const prepayMonthSummaryApi = (data) =>
  post("/statistics/queryMonthGrossProfit", data);
// 明细
export const prepayMonthSummaryDetailApi = (data) =>
  post("/statistics/queryMonthCustomerGrossProfitList", data);
// 明细汇总
export const prepayMonthSummaryDetailSummaryApi = (data) =>
  post("/statistics/queryAllMonthCustomerGrossProfit", data);
