/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-01 17:15:21
 * @Description: 路由管理
 */
import Vue from "vue";
import Router from "vue-router";
import Layout from "@/layout/layout-system.vue";

Vue.use(Router);

// 解决路由重复点击报错
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};
export const constantRoutes = [
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/404Page",
    name: "404Page",
    component: () => import("@/views/errorPage/404.vue"),
  },
  {
    path: "/401Page",
    name: "401Page",
    component: () => import("@/views/errorPage/401.vue"),
  },
  {
    path: "/",
    component: Layout,
    redirect: "/index",
    name: "Root",
    meta: {
      title: "首页",
      icon: "icon-home",
    },
    children: [
      {
        path: "/index",
        name: "Index",
        component: () => import("../views/home/<USER>"),
        meta: {
          title: "首页",
          icon: "icon-house",
          affix: true,
          noKeepAlive: true,
        },
      },
    ],
  },
  {
    path: "/provider",
    component: Layout,
    name: "Provider",
    meta: {
      title: "供应商",
      icon: "icon-user",
    },
    children: [
      {
        path: "/providerSale",
        name: "providerSale",
        component: () => import("@/views/provider/providerSale.vue"),
        meta: {
          title: "销售统计",
          icon: "icon-code",
        },
      },
      {
        path: "/providerOrder",
        name: "providerOrder",
        component: () => import("@/views/provider/providerOrder.vue"),
        meta: {
          title: "销售单",
          icon: "icon-code",
        },
      },
      {
        path: "/consignment",
        name: "consignment",
        component: () => import("@/views/provider/consignment.vue"),
        meta: {
          title: "发货单",
          icon: "icon-code",
        },
      },
      {
        path: "/providerReturn",
        name: "providerReturn",
        component: () => import("@/views/provider/providerReturn.vue"),
        meta: {
          title: "退货单",
          icon: "icon-code",
        },
      },
      {
        path: "/checkBill",
        name: "checkBill",
        component: () => import("@/views/provider/checkBill.vue"),
        meta: {
          title: "对账单",
          icon: "icon-code",
        },
      },
    ],
  },
  {
    path: "/engineer",
    name: "Engineer",
    component: Layout,
    meta: {
      title: "工程师",
      icon: "icon-link-cloud-faild",
    },
    children: [
      {
        path: "/work-map",
        name: "WorkMap",
        component: () => import("@/views/engineer/workMap.vue"),
        meta: {
          title: "工单管理",
          icon: "icon-code",
        },
      },
      {
        path: "/engineerTeam",
        name: "EngineerTeam",
        component: () => import("@/views/engineer/engineerTeam.vue"),
        meta: {
          title: "工程师管理",
          icon: "icon-code",
        },
      },
      {
        path: "/fee",
        name: "fee",
        component: () => import("@/views/engineer/fee.vue"),
        meta: {
          title: "维修费用管理",
          icon: "icon-code",
        },
      },
      {
        path: "/repository",
        name: "repository",
        component: () => import("@/views/engineer/repository.vue"),
        meta: {
          title: "知识库管理",
          icon: "icon-code",
        },
      },
      {
        path: "/cycleReset",
        name: "cycleReset",
        component: () => import("@/views/engineer/cycleReset.vue"),
        meta: {
          title: "周期修正",
          icon: "icon-like",
        },
      },
      {
        path: "/enReceive",
        name: "enReceive",
        component: () => import("@/views/engineer/enReceive.vue"),
        meta: {
          title: "领料管理",
          icon: "icon-code",
        },
      },
      {
        path: "/reback",
        name: "reback",
        component: () => import("@/views/engineer/reback.vue"),
        meta: {
          title: "退料管理",
          icon: "icon-code",
        },
      },
      {
        path: "/apply",
        name: "apply",
        component: () => import("@/views/engineer/apply.vue"),
        meta: {
          title: "客户领料",
          icon: "icon-code",
        },
      },
      {
        path: "/registerInstall",
        name: "registerInstall",
        component: () => import("@/views/engineer/registerInstall"),
        meta: {
          title: "登记安装",
          icon: "icon-code",
        },
      },
      {
        path: "/visiting",
        name: "visiting",
        component: () => import("@/views/engineer/visiting"),
        meta: {
          title: "拜访数据",
          icon: "icon-code",
        },
      },
      {
        path: "/buy",
        name: "Buy",
        component: () => import("@/views/engineer/buy"),
        meta: {
          title: "购买意向",
          icon: "icon-code",
        },
      },

      {
        path: "/engineerStore",
        name: "engineerStore",
        component: () => import("@/views/engineer/engineerStore.vue"),
        meta: {
          title: "工程师仓库",
          icon: "icon-like",
        },
      },
      {
        path: "/imperfect",
        name: "imperfect",
        component: () => import("@/views/engineer/imperfect.vue"),
        meta: {
          title: "毛机维修",
          icon: "icon-like",
        },
      },
      {
        path: "/partRepair",
        name: "partRepair",
        component: () => import("@/views/engineer/partRepair.vue"),
        meta: {
          title: "翻新组件",
          icon: "icon-like",
        },
      },
      {
        path: "/disassembly",
        name: "disassembly",
        component: () => import("@/views/engineer/disassembly.vue"),
        meta: {
          title: "拆机记录",
          icon: "icon-like",
        },
      },
      {
        path: "/engineer-mon-sum",
        name: "EngineerMonSum",
        component: () => import("@/views/engineer/engineerMonSum.vue"),
        meta: {
          title: "月度统计",
          icon: "icon-like",
        },
      },
      {
        path: "/aging",
        name: "Aging",
        component: () => import("@/views/engineer/aging.vue"),
        meta: {
          title: "时效统计",
          icon: "icon-like",
        },
      },
      {
        path: "/gross-margin",
        name: "GrossMargin",
        component: () => import("@/views/engineer/grossMargin.vue"),
        meta: {
          title: "毛利统计",
          icon: "icon-like",
        },
      },
    ],
  },
  {
    path: "/goods",
    component: Layout,
    name: "goods",
    meta: {
      title: "商品",
      icon: "icon-code",
    },
    children: [
      {
        path: "/classify",
        name: "classify",
        component: () => import("@/views/goods/classify.vue"),
        meta: {
          title: "分类管理",
          icon: "icon-like",
        },
      },
      {
        path: "/wares",
        name: "wares",
        component: () => import("@/views/goods/wares.vue"),
        meta: {
          title: "商品管理",
          icon: "icon-code",
        },
      },
      {
        path: "/itemConfig",
        name: "itemConfig",
        component: () => import("@/views/goods/itemConfig.vue"),
        meta: {
          title: "组合售价",
          icon: "icon-code",
        },
      },
      {
        path: "/sku",
        name: "sku",
        component: () => import("@/views/goods/sku"),
        meta: {
          title: "SKU查询",
        },
      },
      {
        path: "/deviceGoods",
        name: "deviceGoods",
        component: () => import("@/views/goods/deviceGoods.vue"),
        meta: {
          title: "设备商品管理",
          icon: "icon-code",
        },
      },

      {
        path: "/wholeGoods",
        name: "wholeGoods",
        component: () => import("@/views/goods/wholeGoods.vue"),
        meta: {
          title: "成套商品关联",
          icon: "icon-code",
        },
      },
      {
        path: "/brand",
        name: "brand",
        component: () => import("@/views/goods/brand.vue"),
        meta: {
          title: "品牌管理",
          icon: "icon-like",
        },
      },
      {
        path: "/freight",
        name: "freight",
        component: () => import("@/views/goods/freight.vue"),
        meta: {
          title: "运费模版",
          icon: "icon-like",
        },
      },
      {
        path: "/wrong",
        name: "wrong",
        component: () => import("@/views/goods/wrong.vue"),
        meta: {
          title: "问题商品",
          icon: "icon-like",
        },
      },
    ],
  },
  {
    path: "/procure",
    name: "procure",
    component: Layout,
    meta: {
      title: "采购",
      icon: "icon-user",
    },
    children: [
      {
        path: "/pmParts",
        name: "pmParts",
        component: () => import("@/views/procure/pmParts"),
        meta: {
          title: "PM商品查询",
          icon: "icon-like",
        },
      },
      {
        path: "/management",
        name: "Management",
        component: () => import("@/views/procure/management.vue"),
        meta: {
          title: "供应商管理",
          icon: "icon-code",
        },
      },
      {
        path: "/thing",
        name: "thing",
        component: () => import("@/views/procure/thing.vue"),
        meta: {
          title: "物品管理",
          icon: "icon-code",
        },
      },
      {
        path: "/inquiry",
        name: "inquiry",
        component: () => import("@/views/procure/inquiry"),
        meta: {
          title: "询价记录",
          icon: "icon-code",
        },
      },
      {
        path: "/supplySource",
        name: "supplySource",
        component: () => import("@/views/procure/supplySource"),
        meta: {
          title: "供应源",
          icon: "icon-code",
        },
      },
      {
        path: "/partApply",
        name: "partApply",
        component: () => import("@/views/procure/partApply"),
        meta: {
          title: "零件申请",
          icon: "icon-code",
        },
      },
      {
        path: "/purchaseOrder",
        name: "purchaseOrder",
        component: () => import("@/views/procure/purchaseOrder"),
        meta: {
          title: "计划采购",
          icon: "icon-like",
        },
      },
      {
        path: "/machinePurchase",
        name: "machinePurchase",
        component: () => import("@/views/procure/machinePurchase"),
        meta: {
          title: "机器采购",
          icon: "icon-like",
        },
      },
      // {
      //   path: "/providerOrder",
      //   name: "providerOrder",
      //   component: () => import("@/views/procure/providerOrder"),
      //   meta: {
      //     title: "采购单",
      //     icon: "icon-like",
      //   },
      // },
      {
        path: "/paymentOrder",
        name: "paymentOrder",
        component: () => import("@/views/procure/paymentOrder"),
        meta: {
          title: "采购付款单",
          icon: "icon-like",
        },
      },
      {
        path: "/earlyOrder",
        name: "earlyOrder",
        component: () => import("@/views/procure/earlyOrder"),
        meta: {
          title: "采购需求",
          icon: "icon-like",
        },
      },
      {
        path: "/refund",
        name: "Refund",
        component: () => import("@/views/procure/refund"),
        meta: {
          title: "采购退货",
          icon: "icon-like",
        },
      },
      // 机器退货
      {
        path: "/machineRefund",
        name: "machineRefund",
        component: () => import("@/views/procure/machineRefund"),
        meta: {
          title: "机器退货",
          icon: "icon-like",
        },
      },
      {
        path: "/procureBill",
        name: "procureBill",
        component: () => import("@/views/procure/procureBill"),
        meta: {
          title: "采购发票",
          icon: "icon-like",
        },
      },
      {
        path: "/procureDetail",
        name: "procureDetail",
        component: () => import("@/views/procure/procureDetail"),
        meta: {
          title: "采购查询",
          icon: "icon-like",
        },
      },
      {
        path: "/returnDetail",
        name: "returnDetail",
        component: () => import("@/views/procure/returnDetail"),
        meta: {
          title: "退货查询",
          icon: "icon-like",
        },
      },
      {
        path: "/procureStat",
        name: "procureStat",
        component: () => import("@/views/procure/procureStat.vue"),
        meta: {
          title: "采购统计",
          icon: "icon-like",
        },
      },
      {
        path: "/returnStat",
        name: "returnStat",
        component: () => import("@/views/procure/returnStat.vue"),
        meta: {
          title: "退货统计",
          icon: "icon-like",
        },
      },
    ],
  },
  {
    path: "/store",
    component: Layout,
    name: "store",
    meta: {
      title: "库管",
      icon: "icon-code",
    },
    children: [
      {
        path: "/idle",
        name: "idle",
        component: () => import("@/views/store/idle.vue"),
        meta: {
          title: "呆滞统计",
          icon: "icon-like",
        },
      },
      {
        path: "/warehouse",
        name: "warehouse",
        component: () => import("@/views/store/warehouse.vue"),
        meta: {
          title: "仓库管理",
          icon: "icon-like",
        },
      },
      {
        path: "/inventory",
        name: "Inventory",
        component: () => import("@/views/store/inventory.vue"),
        meta: {
          title: "仓库盘点",
          icon: "icon-like",
        },
      },
      {
        path: "/losses",
        name: "losses",
        component: () => import("@/views/store/losses.vue"),
        meta: {
          title: "报损报溢",
          icon: "icon-like",
        },
      },
      {
        path: "/machineStore",
        name: "machineStore",
        component: () => import("@/views/store/machineStore.vue"),
        meta: {
          title: "机器仓库",
          icon: "icon-code",
        },
      },

      // TODO: 待合并
      {
        path: "/stock",
        name: "stock",
        component: () => import("@/views/store/stock.vue"),
        meta: {
          title: "耗材仓库",
          icon: "icon-code",
        },
      },
      {
        path: "/machineManage",
        name: "machineManage",
        component: () => import("@/views/store/machineManage.vue"),
        meta: {
          title: "机器仓库",
          icon: "icon-code",
        },
      },
      {
        path: "/repertory",
        name: "Repertory",
        component: () => import("@/views/store/repertory.vue"),
        meta: {
          title: "月末库存",
          icon: "icon-code",
        },
      },
      {
        path: "/applyOrder",
        name: "applyOrder",
        component: () => import("@/views/store/applyOrder.vue"),
        meta: {
          title: "耗材领料",
          icon: "icon-code",
        },
      },
      {
        path: "/receiving",
        name: "Receiving",
        component: () => import("@/views/store/receiving.vue"),
        meta: {
          title: "耗材收货",
          icon: "icon-code",
        },
      },
      {
        path: "/warehousing",
        name: "warehousing",
        component: () => import("@/views/store/warehousing.vue"),
        meta: {
          title: "耗材入库",
          icon: "icon-code",
        },
      },
      {
        path: "/outbound",
        name: "outbound",
        component: () => import("@/views/store/outbound.vue"),
        meta: {
          title: "耗材出库",
          icon: "icon-like",
        },
      },
      {
        path: "/sendgoods",
        name: "sendgoods",
        component: () => import("@/views/store/sendgoods.vue"),
        meta: {
          title: "耗材发货",
          icon: "icon-like",
        },
      },
      {
        path: "/shipment",
        name: "shipment",
        component: () => import("@/views/store/shipment.vue"),
        meta: {
          title: "机器发货",
          icon: "icon-like",
        },
      },
    ],
  },
  {
    path: "/customer",
    component: Layout,
    name: "Custom",
    redirect: "/custom",
    meta: {
      title: "客户",
      icon: "icon-user",
    },
    children: [
      {
        path: "/group",
        name: "group",
        component: () => import("@/views/custom/group.vue"),
        meta: {
          title: "集团管理",
          icon: "icon-like",
        },
      },
      {
        path: "/custom",
        name: "customCustom",
        component: () => import("@/views/custom/custom.vue"),
        meta: {
          title: "客户管理",
          icon: "icon-code",
        },
      },
      {
        path: "/tag",
        name: "Tag",
        component: () => import("@/views/custom/tag.vue"),
        meta: {
          title: "标签查询",
          icon: "icon-code",
        },
      },
      {
        path: "/contractInquire",
        name: "contractInquire",
        component: () => import("@/views/custom/contractInquire.vue"),
        meta: {
          title: "合约查询",
          icon: "icon-code",
        },
      },
      {
        path: "/integralInquire",
        name: "integralInquire",
        component: () => import("@/views/custom/integralInquire.vue"),
        meta: {
          title: "积分查询",
          icon: "icon-code",
        },
      },
      {
        path: "/coupon",
        name: "coupon",
        component: () => import("@/views/custom/coupon.vue"),
        meta: {
          title: "优惠券查询",
          icon: "icon-code",
        },
      },
      {
        path: "/visit",
        name: "visit",
        component: () => import("@/views/custom/visit.vue"),
        meta: {
          title: "访问查询",
          icon: "icon-code",
        },
      },
      {
        path: "/used",
        name: "used",
        component: () => import("@/views/custom/used.vue"),
        meta: {
          title: "用料记录",
          icon: "icon-like",
        },
      },
      // {
      //   path: "/searchInfo",
      //   name: "searchInfo",
      //   component: () => import("@/views/custom/components/searchInfo.vue"),
      //   meta: {
      //     title: "搜索记录",
      //     icon: "icon-like",
      //   },
      // },
      {
        path: "/searchResult",
        name: "searchResult",
        component: () => import("@/views/custom/searchResult.vue"),
        meta: {
          title: "搜索查询",
          icon: "icon-like",
        },
      },
      // 原耗材仓库
      {
        path: "/customerStore",
        name: "customerStore",
        component: () => import("@/views/custom/customerStore.vue"),
        meta: {
          title: "客户仓库",
          icon: "icon-code",
        },
      },
      {
        path: "/receive",
        name: "receive",
        component: () => import("@/views/custom/receive.vue"),
        meta: {
          title: "领料查询",
          icon: "icon-code",
        },
      },
      {
        path: "/orderInquire",
        name: "orderInquire",
        component: () => import("@/views/custom/orderInquire.vue"),
        meta: {
          title: "订单查询",
          icon: "icon-like",
        },
      },
      {
        path: "/activities",
        name: "activities",
        component: () => import("@/views/custom/activities.vue"),
        meta: {
          title: "活动记录",
          icon: "icon-like",
        },
      },
      {
        path: "/costInquire",
        name: "costInquire",
        component: () => import("@/views/custom/costInquire.vue"),
        meta: {
          title: "价值查询",
          icon: "icon-like",
        },
      },
      {
        path: "/distribute",
        name: "distribute",
        component: () => import("@/views/custom/distribute.vue"),
        meta: {
          title: "分布统计",
          icon: "icon-like",
        },
      },
      {
        path: "/growth",
        name: "growth",
        component: () => import("@/views/custom/growth.vue"),
        meta: {
          title: "增长统计",
          icon: "icon-like",
        },
      },
      {
        path: "/viscosity",
        name: "viscosity",
        component: () => import("@/views/custom/viscosity.vue"),
        meta: {
          title: "粘度分析",
          icon: "icon-like",
        },
      },
      {
        path: "/convert",
        name: "convert",
        component: () => import("@/views/custom/convert.vue"),
        meta: {
          title: "转化分析",
          icon: "icon-like",
        },
      },
      {
        path: "/profit",
        name: "Profit",
        component: () => import("@/views/custom/profit.vue"),
        meta: {
          title: "客户毛利",
          icon: "icon-like",
        },
      },
    ],
  },
  {
    path: "/machine",
    name: "Machine",
    component: Layout,
    meta: {
      title: "机器",
      icon: "icon-connection",
    },
    children: [
      {
        path: "/counter",
        name: "Counter",
        component: () => import("@/views/machine/counter"),
        meta: {
          title: "计数器上报",
          icon: "icon-like",
        },
      },
      {
        path: "/status",
        name: "Status",
        component: () => import("@/views/machine/status"),
        meta: {
          title: "状态上报",
          icon: "icon-like",
        },
      },
      {
        path: "/powder",
        name: "Powder",
        component: () => import("@/views/machine/powder"),
        meta: {
          title: "粉量上报",
          icon: "icon-like",
        },
      },
      {
        path: "/deviceBlock",
        name: "DeviceBlock",
        component: () => import("@/views/machine/deviceBlock.vue"),
        meta: {
          title: "机器查询",
          icon: "icon-like",
        },
      },
      {
        path: "/monitor",
        name: "monitor",
        component: () => import("@/views/machine/monitor.vue"),
        meta: {
          title: "机器监控",
          icon: "icon-like",
        },
      },
      {
        path: "/offerSheet",
        name: "offerSheet",
        component: () => import("@/views/machine/offerSheet.vue"),
        meta: {
          title: "成本预算",
          icon: "icon-like",
        },
      },
      {
        path: "/printStat",
        name: "printStat",
        component: () => import("@/views/machine/printStat.vue"),
        meta: {
          title: "印量统计",
          icon: "icon-like",
        },
      },
      // {
      //   path: "/PM",
      //   name: "PM",
      //   component: () => import("@/views/engineer/components/PM.vue"),
      //   meta: {
      //     title: "零件PM管理",
      //     icon: "icon-like",
      //   },
      // },

      {
        path: "/partLife",
        name: "partLife",
        component: () => import("@/views/machine/partLife"),
        meta: {
          title: "寿命统计",
          icon: "icon-like",
        },
      },
      {
        path: "/faultStat",
        name: "faultStat",
        component: () => import("@/views/machine/faultStat"),
        meta: {
          title: "故障统计",
          icon: "icon-like",
        },
      },
      // {
      //   path: "/dayAccount",
      //   name: "dayAccount",
      //   component: () => import("@/views/machine/components/dayAccount.vue"),
      //   meta: {
      //     title: "日印量统计表",
      //     icon: "icon-like",
      //   },
      // },
      // {
      //   path: "/monthAccount",
      //   name: "monthAccount",
      //   component: () => import("@/views/machine/components/monthAccount.vue"),
      //   meta: {
      //     title: "设备运行统计表",
      //     icon: "icon-like",
      //   },
      // },
      // {
      //   path: "/maintenanceOrder",
      //   name: "maintenanceOrder",
      //   component: () =>
      //       import("@/views/machine/components/maintenanceOrder.vue"),
      //   meta: {
      //     title: "维修记录统计表",
      //     icon: "icon-like",
      //   },
      // },
      // {
      //   path: "/partAccount",
      //   name: "partAccount",
      //   component: () => import("@/views/machine/components/partAccount.vue"),
      //   meta: {
      //     title: "零件更换统计表",
      //     icon: "icon-like",
      //   },
      // },
      {
        path: "/distributeStat",
        name: "distributeStat",
        component: () => import("@/views/machine/distributeStat.vue"),
        meta: {
          title: "分布统计",
          icon: "icon-like",
        },
      },
      {
        path: "/growthStat",
        name: "growthStat",
        component: () => import("@/views/machine/growthStat.vue"),
        meta: {
          title: "增长统计",
          icon: "icon-like",
        },
      },
      {
        path: "/maori-stat",
        name: "MaoriStat",
        component: () => import("@/views/machine/maoriStat.vue"),
        meta: {
          title: "机器毛利",
          icon: "icon-like",
        },
      },
    ],
  },
  {
    path: "/order",
    component: Layout,
    name: "Order",
    meta: {
      title: "订单",
      icon: "icon-memo",
    },
    children: [
      {
        path: "/saleOrder",
        name: "saleOrder",
        component: () => import("@/views/order/saleOrder.vue"),
        meta: {
          title: "耗材销售",
          icon: "icon-code",
        },
      },
      {
        path: "/machineOrder",
        name: "machineOrder",
        component: () => import("@/views/order/machineOrder.vue"),
        meta: {
          title: "机器销售",
          icon: "icon-code",
        },
      },
      {
        path: "/workOrder",
        name: "workOrder",
        component: () => import("@/views/order/workOrder.vue"),
        meta: {
          title: "维修工单",
          icon: "icon-code",
        },
      },
      // {
      //   path: "/installOrder",
      //   name: "installOrder",
      //   component: () => import("@/views/order/installOrder.vue"),
      //   meta: {
      //     title: "安装工单",
      //     icon: "icon-code",
      //   },
      // },
      // {
      //   path: "/return",
      //   name: "return",
      //   component: () => import("@/views/order/components/return.vue"),
      //   meta: {
      //     title: "退货单",
      //     icon: "icon-like",
      //   },
      // },

      // {
      //   path: "/appeal",
      //   name: "appeal",
      //   component: () => import("@/views/order/components/appeal.vue"),
      //   meta: {
      //     title: "申诉单",
      //   },
      // },
      {
        path: "/saleReturn",
        name: "saleReturn",
        component: () => import("@/views/order/saleReturn.vue"),
        meta: {
          title: "耗材退货",
          icon: "icon-like",
        },
      },
      {
        path: "/repairStat",
        name: "repairStat",
        component: () => import("@/views/order/repairStat.vue"),
        meta: {
          title: "维修统计",
          icon: "icon-like",
        },
      },
      {
        path: "/meterStat",
        name: "meterStat",
        component: () => import("@/views/order/meterStat.vue"),
        meta: {
          title: "抄表统计",
          icon: "icon-like",
        },
      },
      {
        path: "/reports",
        name: "Reports",
        component: () => import("@/views/order/reports.vue"),
        meta: {
          title: "抄表对账",
          icon: "icon-like",
        },
      },
      {
        path: "/machineStat",
        name: "machineStat",
        component: () => import("@/views/order/machineStat.vue"),
        meta: {
          title: "机器统计",
          icon: "icon-like",
        },
      },
      {
        path: "/saleStat",
        name: "saleStat",
        component: () => import("@/views/order/saleStat.vue"),
        meta: {
          title: "销售统计",
          icon: "icon-like",
        },
      },
      {
        path: "/itemStat",
        name: "itemStat",
        component: () => import("@/views/order/itemStat.vue"),
        meta: {
          title: "耗材统计",
          icon: "icon-like",
        },
      },
      // {
      //   path: "/exhibition",
      //   name: "exhibition",
      //   component: () => import("@/views/order/components/exhibition.vue"),
      //   meta: {
      //     title: "客户抄表记录表",
      //     icon: "icon-like",
      //   },
      // },
      // {
      //   path: "/receipt",
      //   name: "receipt",
      //   component: () => import("@/views/order/components/receipt.vue"),
      //   meta: {
      //     title: "客户抄表收款单",
      //     icon: "icon-like",
      //   },
      // },
    ],
  },
  {
    path: "/financing",
    component: Layout,
    name: "Financing",
    meta: {
      title: "财务",
      icon: "icon-memo",
    },
    children: [
      {
        path: "/orderReview",
        name: "OrderReview",
        component: () => import("@/views/financing/orderReview.vue"),
        meta: {
          title: "收款清单",
          icon: "icon-code",
        },
      },
      {
        path: "/pay-review",
        name: "PayReview",
        component: () => import("@/views/financing/payReview.vue"),
        meta: {
          title: "付款清单",
          icon: "icon-code",
        },
      },
      {
        path: "/bill",
        name: "bill",
        component: () => import("@/views/financing/bill.vue"),
        meta: {
          title: "报销清单",
          icon: "icon-code",
        },
      },
      // {
      //   path: "/expense",
      //   name: "meterExpense",
      //   component: () => import("@/views/financing/expense.vue"),
      //   meta: {
      //     title: "抄表价格",
      //     icon: "icon-like",
      //   },
      // },
      {
        path: "/summary",
        name: "summary",
        component: () => import("@/views/financing/summary.vue"),
        meta: {
          title: "应收帐款汇总",
          icon: "icon-like",
        },
      },
      {
        path: "/receivables",
        name: "Receivables",
        component: () => import("@/views/financing/receivables.vue"),
        meta: {
          title: "应收帐款明细",
          icon: "icon-like",
        },
      },
      {
        path: "/paySummary",
        name: "paySummary",
        component: () => import("@/views/financing/paySummary.vue"),
        meta: {
          title: "应付帐款汇总",
          icon: "icon-like",
        },
      },
      {
        path: "/due",
        name: "Due",
        component: () => import("@/views/financing/due.vue"),
        meta: {
          title: "应付账款明细",
          icon: "icon-like",
        },
      },
      {
        path: "/advance-summary",
        name: "AdvanceSummary",
        component: () => import("@/views/financing/advanceSummary.vue"),
        meta: {
          title: "预付账款汇总",
          icon: "icon-like",
        },
      },
      {
        path: "/advance",
        name: "Advance",
        component: () => import("@/views/financing/advance.vue"),
        meta: {
          title: "预付账款明细",
          icon: "icon-like",
        },
      },
      // {
      //   path: "/depositReceived",
      //   name: "depositReceived",
      //   component: () => import("@/views/financing/depositReceived.vue"),
      //   meta: {
      //     title: "预收账款",
      //     icon: "icon-like",
      //   },
      // },
      //  采购单明细
      {
        path: "/purchase",
        name: "purchase",
        component: () => import("@/views/financing/purchase.vue"),
        meta: {
          title: "耗材采购明细",
          icon: "icon-like",
        },
      },
      //   销售单明细
      {
        path: "/sale",
        name: "sale",
        component: () => import("@/views/financing/sale.vue"),
        meta: {
          title: "耗材销售明细",
          icon: "icon-like",
        },
      },
      //   入库明细
      {
        path: "/inbound",
        name: "inbound",
        component: () => import("@/views/financing/inbound.vue"),
        meta: {
          title: "耗材入库明细",
          icon: "icon-like",
        },
      },
      // 领料出库明细
      {
        path: "/claim",
        name: "claim",
        component: () => import("@/views/financing/claim.vue"),
        meta: {
          title: "领料出库明细",
          icon: "icon-like",
        },
      },
      // 机器采购明细
      {
        path: "/machPurchase",
        name: "machPurchase",
        component: () => import("@/views/financing/machPurchase.vue"),
        meta: {
          title: "机器采购明细",
          icon: "icon-like",
        },
      },
      // 机器销售明细
      {
        path: "/machSale",
        name: "machSale",
        component: () => import("@/views/financing/machSale.vue"),
        meta: {
          title: "机器销售明细",
          icon: "icon-like",
        },
      },
      // 机器出库明细
      {
        path: "/machOut",
        name: "machOut",
        component: () => import("@/views/financing/machOut.vue"),
        meta: {
          title: "机器出库明细",
          icon: "icon-like",
        },
      },
      // 机器入库明细
      {
        path: "/machIn",
        name: "machIn",
        component: () => import("@/views/financing/machIn.vue"),
        meta: {
          title: "机器入库明细",
          icon: "icon-like",
        },
      },
      {
        path: "/other-revenue",
        name: "OtherRevenue",
        component: () => import("@/views/financing/otherRevenue.vue"),
        meta: {
          title: "其他收入",
          icon: "icon-like",
        },
      },
      {
        path: "/other-expense",
        name: "OtherExpense",
        component: () => import("@/views/financing/otherExpense.vue"),
        meta: {
          title: "其他支出",
          icon: "icon-like",
        },
      },
      {
        path: "/monthly-revenue",
        name: "MonthlyRevenue",
        component: () => import("@/views/financing/monthlyRevenue.vue"),
        meta: {
          title: "月度收入",
          icon: "icon-like",
        },
      },
      {
        path: "/monthly-expense",
        name: "MonthlyExpense",
        component: () => import("@/views/financing/monthlyExpense.vue"),
        meta: {
          title: "月度支出",
          icon: "icon-like",
        },
      },
      {
        path: "/gross-profit",
        name: "GrossProfit",
        component: () => import("@/views/financing/grossProfit.vue"),
        meta: {
          title: "月度毛利",
          icon: "icon-like",
        },
      },
    ],
  },
  {
    path: "/dispose",
    component: Layout,
    name: "dispose",
    meta: {
      title: "配置",
      icon: "icon-code",
    },
    children: [
      {
        path: "/IOT",
        name: "IOT",
        component: () => import("@/views/dispose/IOT.vue"),
        meta: {
          title: "物联网配置",
          icon: "icon-code",
        },
      },
      {
        path: "/product",
        name: "product",
        component: () => import("@/views/dispose/product.vue"),
        meta: {
          title: "品牌产品树",
          icon: "icon-code",
        },
      },
      {
        path: "/equip",
        name: "equip",
        component: () => import("@/views/dispose/equip.vue"),
        meta: {
          title: "设备和零件",
          icon: "icon-like",
        },
      },
      {
        path: "/bom",
        name: "bom",
        component: () => import("@/views/dispose/bom.vue"),
        meta: {
          title: "零件BOM",
          icon: "icon-like",
        },
      },
      {
        path: "/assoc",
        name: "assoc",
        component: () => import("@/views/dispose/assoc.vue"),
        meta: {
          title: "零件关联",
          icon: "icon-like",
        },
      },
      {
        path: "/integral",
        name: "integral",
        component: () => import("@/views/dispose/integral.vue"),
        meta: {
          title: "积分配置",
          icon: "icon-like",
        },
      },
      {
        path: "/base-info",
        name: "BaseInfo",
        component: () => import("@/views/dispose/info.vue"),
        meta: {
          title: "基础配置",
          icon: "icon-like",
        },
      },
      {
        path: "/websiteManagement",
        name: "websiteManagement",
        component: () =>
            import("@/websites/views/dispose/websiteManagement.vue"),
        meta: {
          title: "网站管理",
          icon: "icon-global",
        },
      },
      {
        path: "/logcontrol",
        name: "LogControlManagement",
        component: () => import("@/logcontrol/views/logcontrol/logControlManagement.vue"),
        meta: {
          title: "日志控制",
          icon: "icon-document",
        },
      },
      {
        path: "/appupdate",
        name: "AppUpdateManagement",
        component: () => import("@/appupdate/views/appupdate/index.vue"),
        meta: {
          title: "应用更新管理",
          icon: "el-icon-mobile-phone",
        },
      },
    ],
  },
  {
    path: "/system",
    name: "System",
    component: Layout,
    meta: {
      title: "系统",
      icon: "icon-link-cloud-faild",
    },
    children: [
      {
        path: "/Menu",
        name: "Menu",
        component: () => import("@/views/system/Menu"),
        meta: {
          title: "菜单管理",
        },
      },
      {
        path: "/User",
        name: "User",
        component: () => import("@/views/system/User"),
        meta: {
          title: "用户管理",
        },
      },
      {
        path: "/Role",
        name: "Role",
        component: () => import("@/views/system/Role"),
        meta: {
          title: "角色管理",
        },
      },
      {
        path: "/Depart",
        name: "Depart",
        component: () => import("@/views/system/Depart"),
        meta: {
          title: "部门管理",
        },
      },
      {
        path: "/Dict",
        name: "Dict",
        component: () => import("@/views/system/Dict"),
        meta: {
          title: "字典管理",
        },
      },
      {
        path: "/Log",
        name: "Log",
        component: () => import("@/views/system/Log"),
        meta: {
          title: "操作日志管理",
        },
      },
      {
        path: "/purpose",
        name: "Purpose",
        component: () => import("@/views/system/purpose.vue"),
        meta: {
          title: "意向用户",
        },
      },
      // {
      //   path: "/Set",
      //   name: "Set",
      //   component: () => import("@/views/system/Setting"),
      //   meta: {
      //     title: "平台属性",
      //   },
      // },
    ],
  },
];
export const asyncRoutes = [];

const createRouter = () =>
  new Router({
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: constantRoutes,
  });
const router = createRouter();
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}
export default router;
