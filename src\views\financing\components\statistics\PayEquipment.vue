<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-11 16:41:19
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-29 17:22:07
 * @Description: 
 -->
<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      show-selection
      @handleSelectionChange="handleSelectionChange"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="primary"
          icon="el-icon-edit"
          size="mini"
          @click="handleBulkEdit"
        >
          账款批量核销
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-check"
          @click="clearSelection"
        >
          取消全选
        </el-button>
        <el-button
          v-auth="['@ums:manage:finance:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>应付总金额：{{ totalData?.amount || 0 }}</div>
          <div>不含税金额：{{ totalData?.noTaxAmount || 0 }}</div>
          <div>税额：{{ totalData?.taxAmount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'info')"
          >
            详情
          </el-button>
          <el-button icon="el-icon-edit" @click="handleEdit(row, 'audit')">
            账款核销
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      :confirm-text="'确认核销'"
      :confirm-btn-loading="formLoading"
      width="30%"
      top="3%"
      @ok="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParams"
        :form-list="formColumns"
        :open-type="'edit'"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        @proSubmit="proSubmit"
      >
        <template #voucherImg>
          <ProUpload
            :file-list="formParams.voucherImg"
            :type="'edit'"
            :limit="3"
            style="padding-left: 0"
            @uploadSuccess="handleUploadSuccess"
            @uploadRemove="handleUploadRemove"
          />
        </template>
      </ProForm>
    </ProDialog>
    <!-- 机器收货确认 -->
    <MachineReceiving ref="machineReceiving" />
    <PayBulkEdit
      ref="bulkEdit"
      :columns="bulkEditColumns"
      type="equipment"
      @refresh="refresh(), clearSelection()"
    />
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { handleExcelExport } from "@/utils/exportExcel";
import {
  payableMachineSummaryListApi,
  payableBaseSummaryStatisticsApi,
  payableMachineSummaryExportApi,
  payableWriteOffApi,
} from "@/api/finance";
import ProUpload from "@/components/ProUpload/index.vue";
import PayBulkEdit from "@/views/financing/components/PayBulkEdit.vue";

export default {
  name: "PayEquipment",
  components: {
    ProUpload,
    MachineReceiving: () => import("@/views/procure/cpns/machineReceiving.vue"),
    PayBulkEdit,
  },
  data() {
    const _this = this;
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "purchaseCode",
          title: "采购单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "manufacturerCode",
          title: "供应商编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        // {
        //   dataIndex: "machineNum",
        //   title: "机器编号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 100,
        // },
        {
          dataIndex: "num",
          title: "采购数量",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "amount",
          title: "采购金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "noTaxAmount",
          title: "不含税金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "taxAmount",
          title: "税额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "tax",
          title: "税率",
          isTable: true,
          formatter: (row) => (row.tax ? row.tax + "%" : ""),
          minWidth: 100,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 160,
        },
      ],
      tableData: [],
      totalData: {},
      requestParameters: {},
      statLoading: false,
      exportLoading: false,
      // 账款核销
      dialogVisible: false,
      dialogTitle: "账款核销",
      formParams: {},
      formColumns: [
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "purchaseCode",
          title: "采购单号",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "amount",
          title: "采购金额",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "verifyReceiveAmount",
          title: "应付金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入应付金额",
              trigger: "change",
              validator: (rule, value, callback) => {
                if (!value) {
                  callback(new Error("请输入应付金额"));
                } else if (+value < 0) {
                  callback(new Error("应付金额不能为负数"));
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "verifyDiscountAmount",
          title: "折扣金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入折扣金额",
          //     trigger: "change",
          //     validator: (rule, value, callback) => {
          //       if (+value < 0) {
          //         callback(new Error("折扣金额不能小于0"));
          //       }
          //       callback();
          //     },
          //   },
          // ],
        },
        {
          dataIndex: "verifyActualAmount",
          title: "实付金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          prop: [
            {
              required: true,
              trigger: "change",
              validator: (rule, value, callback) => {
                console.log("value", value);
                if (!value) {
                  callback(new Error("请输入实付金额"));
                } else if (+value < 0) {
                  callback(new Error("实付金额不能小于0"));
                } else {
                  const deposit = parseFloat(
                    _this.formParams.verifyReceiveAmount
                  );
                  const discount = parseFloat(
                    _this.formParams.verifyDiscountAmount
                  );
                  const payable = deposit - (isNaN(discount) ? 0 : discount);

                  if (+value > payable) {
                    callback(new Error("实付金额不能大于应付金额减去折扣金额"));
                  }
                  if (+value < payable) {
                    callback(new Error("实付金额不能小于应付金额减去折扣金额"));
                  }
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "voucherImg",
          title: "上传凭证",
          isForm: true,
          formSlot: "voucherImg",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请上传凭证",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "remark",
          title: "备注",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          wordlimit: 255,
          formSpan: 24,
        },
      ],
      formLoading: false,
      bulkEditColumns: [
        {
          dataIndex: "purchaseCode",
          title: "采购单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },

        // {
        //   dataIndex: "machineNum",
        //   title: "机器编号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 100,
        // },
        {
          dataIndex: "num",
          title: "采购数量",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "amount",
          title: "采购金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "noTaxAmount",
          title: "不含税金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "taxAmount",
          title: "税额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "tax",
          title: "税率",
          isTable: true,
          formatter: (row) => (row.tax ? row.tax + "%" : ""),
          minWidth: 100,
        },
        {
          dataIndex: "verifyReceiveAmount",
          title: "应付金额",
          isTable: true,
          // fixed: "right",
          tableSlot: "verifyReceiveAmount",
          width: 150,
        },
        {
          dataIndex: "verifyDiscountAmount",
          title: "折扣金额",
          isTable: true,
          // fixed: "right",
          tableSlot: "verifyDiscountAmount",
          width: 150,
        },
        {
          dataIndex: "verifyActualAmount",
          title: "实付金额",
          isTable: true,
          // fixed: "right",
          tableSlot: "verifyActualAmount",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          // fixed: "right",
          tooltip: false,
          tableSlot: "actions",
          width: 100,
        },
      ],
      selectionData: [],
      tipLock: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      this.requestParameters = requestParameters;
      payableMachineSummaryListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      payableBaseSummaryStatisticsApi({
        ...this.requestParameters,
        paymentType: 1,
      })
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    handleBulkEdit() {
      if (!this.selectionData.length) {
        this.$message.warning("请先选择要核销的订单");
        return;
      }
      this.$refs.bulkEdit.show(this.selectionData);
    },
    clearSelection() {
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },
    handleSelectionChange(rows) {
      this.$nextTick(() => {
        const firstRow = rows[0];
        const validRows = rows.filter((row) => {
          if (row.manufacturerCode !== firstRow.manufacturerCode) {
            this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(row, false);
            return true;
          }
          return false;
        });
        if (validRows.length > 0 && !this.tipLock) {
          this.$message.warning("请选择同一供应商的付款单");
          this.tipLock = true;
          setTimeout(() => {
            this.tipLock = false;
          }, 2000);
        }
        this.selectionData = cloneDeep(rows);
      });
    },
    handleEdit(row, type) {
      if (type === "info") {
        if (!row.id) {
          return;
        }
        this.$refs.machineReceiving.show(row, type, false);
      } else if (type === "audit") {
        this.formParams = cloneDeep(row);
        this.dialogVisible = true;
        this.dialogTitle = `核销采购单账款`;
      }
    },
    handleDialogConfirm() {
      this.$refs.ProForm.handleSubmit();
    },
    proSubmit(val) {
      this.$confirm("此操作将核销当前账单, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.formLoading = true;
        const args = {
          customerId: this.formParams.customerId,
          tradeOrderOrigin: "MACHINE_PURCHASE",
          tradeOrderNumber: this.formParams.purchaseCode,
          verifyReceiveAmount: this.formParams.verifyReceiveAmount, // 应收金额
          verifyDiscountAmount: this.formParams.verifyDiscountAmount, // 折扣金额
          verifyActualAmount: this.formParams.verifyActualAmount, // 实收金额
          voucherImg: this.formParams.voucherImg, // 实收金额
          remark: this.formParams.remark,
          manufacturerCode: this.formParams.manufacturerCode,
          manufacturerName: this.formParams.manufacturerName,
        };
        payableWriteOffApi(filterParam(args))
          .then((res) => {
            this.$message.success("核销成功");
            this.handleDialogCancel();
            this.refresh();
          })
          .finally(() => {
            this.formLoading = false;
          });
      });
    },
    handleDialogCancel() {
      this.dialogVisible = false;
      this.formParams = {};
    },
    handleUploadSuccess(result) {
      if (!this.formParams.voucherImg) {
        this.$set(this.formParams, "voucherImg", []);
      }
      this.formParams.voucherImg.push(result);
    },
    handleUploadRemove(file) {
      const index = this.formParams.voucherImg.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParams.voucherImg.splice(index, 1);
    },
    handleExport() {
      this.$confirm("此操作将导出机器应付款明细数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          payableMachineSummaryExportApi,
          this.requestParameters,
          "机器应付款明细数据",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
