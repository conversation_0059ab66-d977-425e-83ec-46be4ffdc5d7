<!--
 * @Author: wskg
 * @Date: 2025-02-17 14:29:59
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:23
 * @Description: 活动 - 消费订单数统计
 -->
<template>
  <div class="container">
    <ProDialog
      :value="dialogVisible"
      title="客户消费明细"
      width="70%"
      top="1%"
      :no-footer="true"
      @cancel="handleDialogCancel"
    >
      <ProTable
        ref="ProTable"
        :columns="columns"
        :data="tableData"
        :height="450"
        :query-param="queryParam"
        :local-pagination="localPagination"
        @loadData="loadData"
      >
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { getActivityShareOrderApi } from "@/api/customer";

export default {
  name: "StatisticOrder",
  data() {
    return {
      dialogVisible: false,
      columns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "orderNum",
          title: "订单号",
          isTable: true,
        },
        {
          dataIndex: "orderOrigin",
          title: "订单类型",
          isTable: true,
          formatter: (row) => row.orderOrigin?.label,
        },
        {
          dataIndex: "orderAmount",
          title: "订单消费金额",
          isTable: true,
        },
        {
          dataIndex: "createdAt",
          title: "消费时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
      ],
      tableData: [],
      queryParam: {
        activityId: "", // 活动Id
        customerId: "", // 分享客户Id
      },
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  mounted() {},
  methods: {
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const requestParameters = cloneDeep(this.queryParam);
      getActivityShareOrderApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    show(row) {
      this.queryParam = {
        activityId: "",
        customerId: "",
      };
      this.queryParam.activityId = row.activityId;
      this.queryParam.customerId = row.customerId;
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.refresh();
      });
    },
    handleDialogCancel() {
      this.dialogVisible = false;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
