<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 14:24:54
 * @Description: 
 -->
<template>
  <div v-show="value" class="ProDialogWrap">
    <el-dialog
      ref="ProDialog"
      :loading="dialogLoading"
      :visible="value"
      v-bind="$attrs"
      :append-to-body="
        $attrs['append-to-body'] === undefined ? true : $attrs['append-to-body']
      "
      :close-on-click-modal="false"
      custom-class="ProDialog"
      @close="handleClose"
    >
      <template #title>
        <slot name="title"></slot>
      </template>
      <slot></slot>
      <template v-if="!noFooter" #footer>
        <div class="dialog-footer">
          <slot name="footer"></slot>
          <div v-if="!noconfirm" class="btn-box">
            <el-button
              type="primary"
              :loading="confirmBtnLoading"
              @click="handleOk"
            >
              {{ confirmButtonText }}
            </el-button>
            <el-button class="m-l-20" @click="handleClose">
              {{ cancelText }}
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "ProDialog",
  props: {
    value: {
      type: Boolean,
      default: true,
    },
    dialogLoading: {
      type: Boolean,
      default: false,
    },
    confirmLoading: {
      type: Boolean,
      default: true,
    },
    confirmBtnLoading: {
      type: Boolean,
      default: false,
    },
    noFooter: {
      type: Boolean,
      default: false,
    },
    noconfirm: {
      type: Boolean,
      default: false,
    },
    confirmText: {
      type: String,
      default: "保 存",
    },
    cancelText: {
      type: String,
      default: "取 消",
    },
    openType: {
      type: String,
      default: "",
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    drag: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isFullscreen: false,
    };
  },
  computed: {
    // ...mapState({
    // 'routes': state => state.routes.routes
    // }),
    icon() {
      return this.isFullscreen ? "fullscreen-exit" : "fullscreen";
    },
    confirmButtonText() {
      const lowerOpenType = this.openType.toLowerCase() || "";
      return lowerOpenType.includes("add")
        ? "确定"
        : this.confirmText || "保存";
    },
  },
  created() {},
  // watch: {
  //   fullscreen: {
  //     handler: function () {
  //       if (attrs.fullscreen || attrs.fullscreen === '')
  //         this.isFullscreen = true
  //     },
  //     immediate: true
  //   },
  //   isFullscreen:{
  //      handler: function () {
  //       if (this.fullscreen || attrs.fullscreen === '')
  //         this.isFullscreen = true
  //     },
  //     immediate: true
  //   }
  // },

  // watchEffect(() => {
  //   if (isFullscreen.value) return
  //   if (props.value && ProDialog.value) {
  //     nextTick(() => {
  //       draggable(ProDialog.value.dialogRef)
  //     })
  //   }
  // })
  methods: {
    handleClose() {
      this.$emit("cancel");
      this.isFullscreen = false;
      // this.$emit('update:value', false)
    },
    handleOk() {
      this.$emit("ok");
      this.isFullscreen = false;
    },
    changeFullscreen() {
      this.isFullscreen = !this.isFullscreen;
      // if (this.isFullscreen) {
      //   this.$refs.ProDialog.dialogRef.style.cssText += ';top:0px;left:0px;'
      // }
    },
    closeFullscreen() {
      this.isFullscreen = false;
      // if (this.isFullscreen) {
      //   this.$refs.ProDialog.dialogRef.style.cssText += ';top:0px;left:0px;'
      // }
    },
  },
  //  setup(props, { emit, attrs }) {
  // watch(
  //   () => attrs.fullscreen,
  //   () => {
  //     if (attrs.fullscreen || attrs.fullscreen === '')
  //       isFullscreen.value = true
  //   },
  //   { immediate: true }
  // )

  // watchEffect(() => {
  //   if (isFullscreen.value) return
  //   if (props.value && ProDialog.value) {
  //     nextTick(() => {
  //       draggable(ProDialog.value.dialogRef)
  //     })
  //   }
  // })
  // },
};
</script>
