/**
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 日志管理API接口 - 基于后端已实现接口
 */

import { get, post, put, del } from '@/utils/request'

export const logApi = {
  // 获取日志列表（改进后的/all接口，支持可选分页参数）
  getLogList(params = {}) {
    return get('/logcontrol/log/all', params)
  },

  // 获取分页日志列表（新增的/page接口，提供完整的分页和筛选功能）
  async getLogListWithPagination(params = {}) {
    try {
      return await get('/logcontrol/log/page', params)
    } catch (error) {

      // 返回模拟分页数据，匹配后端接口格式
      const { pageNum = 1, pageSize = 10, deviceId, logType, logLevel, userId, startTime, endTime } = params

      // 模拟日志数据
      const mockLogs = this.generateMockLogs(100) // 生成100条模拟数据

      // 根据筛选条件过滤数据
      let filteredLogs = mockLogs
      if (deviceId) {
        filteredLogs = filteredLogs.filter(log => log.deviceId === deviceId)
      }
      if (logType) {
        filteredLogs = filteredLogs.filter(log => log.logType === logType)
      }
      if (logLevel) {
        filteredLogs = filteredLogs.filter(log => log.level === logLevel) // 注意：使用level字段
      }
      if (userId) {
        filteredLogs = filteredLogs.filter(log => log.userId === userId) // 使用userId作为唯一标识筛选
      }
      if (startTime && endTime) {
        filteredLogs = filteredLogs.filter(log => {
          const logTime = new Date(log.createdAt)
          return logTime >= new Date(startTime) && logTime <= new Date(endTime)
        })
      }

      // 分页处理
      const total = filteredLogs.length
      const pages = Math.ceil(total / pageSize)
      const startIndex = (pageNum - 1) * pageSize
      const endIndex = startIndex + pageSize
      const list = filteredLogs.slice(startIndex, endIndex)

      return {
        code: 200,
        message: "ok",
        data: {
          list,
          total: total.toString(),        // 后端返回字符串类型
          pages: pages.toString(),        // 后端返回字符串类型
          pageSize: pageSize,             // 后端返回数字类型
          pageNum: pageNum                // 添加pageNum字段
        }
      }
    }
  },

  // 生成模拟日志数据（匹配真实后端数据结构）
  generateMockLogs(count = 100) {
    const logs = []
    const devices = ['cf7f6ce27817ef1a', 'device002', 'device003', 'device004']
    const logTypes = ['LOCATION', 'BUSINESS', 'SYSTEM', 'ERROR']
    const levels = ['INFO', 'DEBUG', 'WARN', 'ERROR']
    const tags = ['LocationUpdateService', 'LogTestActivity', 'SystemService', 'NetworkService']
    const users = [
      { userId: '1730200832705589250', userCode: '********', userName: '王季春' },
      { userId: '1730200832705589251', userCode: 'B0000002', userName: '李明' },
      { userId: '1730200832705589252', userCode: 'B0000003', userName: '张三' }
    ]
    const messages = [
      '位置获取成功: 精度=2250.0米',
      '手动触发日志上传失败',
      '系统启动成功',
      '用户登录验证',
      '数据库连接建立',
      '配置文件加载完成',
      '网络连接异常',
      '内存使用率过高'
    ]

    for (let i = 1; i <= count; i++) {
      const now = new Date()
      const timestamp = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000)
      const createAt = new Date(now.getTime() - Math.random() * 6 * 24 * 60 * 60 * 1000)
      const user = users[Math.floor(Math.random() * users.length)]

      logs.push({
        id: (2000 + i).toString(),
        deviceId: devices[Math.floor(Math.random() * devices.length)],
        userId: user.userId,
        userCode: user.userCode,
        userName: user.userName,
        logType: logTypes[Math.floor(Math.random() * logTypes.length)],
        level: levels[Math.floor(Math.random() * levels.length)],
        timestamp: timestamp.toISOString().slice(0, 19).replace('T', ' '),
        tag: tags[Math.floor(Math.random() * tags.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        extraData: JSON.stringify({
          accuracy: Math.floor(Math.random() * 5000),
          latitude: 37.422 + (Math.random() - 0.5) * 0.1,
          longitude: -122.084073 + (Math.random() - 0.5) * 0.1
        }),
        appVersion: '1.0-debug',
        isUploaded: Math.random() > 0.5,
        deleted: false,
        createdAt: createAt.toISOString().slice(0, 19).replace('T', ' ')
      })
    }

    // 按时间倒序排列
    return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
  },

  // 根据设备ID查询日志
  getLogsByDeviceId(deviceId) {
    return get('/logcontrol/log/list-by-device', { deviceId })
  },

  // 根据日志类型查询日志
  getLogsByType(logType) {
    return get('/logcontrol/log/list-by-type', { logType })
  },

  // 获取所有日志（兼容旧接口）
  getAllLogs(params = {}) {
    return get('/logcontrol/log/all', params)
  },

  // 获取日志总数
  getLogCount() {
    return get('/logcontrol/log/count')
  },

  // 获取日志类型统计
  getLogTypeStatistics() {
    return get('/logcontrol/log/stats/type')
  },

  // 获取日志级别统计
  getLogLevelStatistics() {
    return get('/logcontrol/log/stats/level')
  },

  // 删除日志（扩展功能）
  deleteLog(id) {
    return del(`/logcontrol/log/${id}`)
  },




}

export default logApi
