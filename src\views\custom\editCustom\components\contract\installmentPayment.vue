<!--
 * @Author: wskg
 * @Date: 2025-01-15 15:29:40
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-01-23 14:45:00
 * @Description: 购机合约分期付款
 -->
<template>
  <div class="billing-method">
    <div class="title-box" style="margin-top: 0">分期付款</div>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="分期期数：" prop="installmentNum">
          <el-input-number
            v-model="localInstallmentNum"
            type="number"
            :min="2"
            :precision="0"
            controls-position="right"
            placeholder="请输入分期期数"
            :disabled="editType === 'info'"
            @change="updateInstallments"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="installmentField === 'tradeOrderInstallments'" :span="12">
        <el-form-item label="是否强制执行停保：" prop="forceStopBuy">
          <el-radio-group
            v-model="installmentData.forceStopBuy"
            :disabled="editType === 'info'"
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 分期付款明细表格 -->
    <div v-if="installmentNum > 0" class="installment-details">
      <el-table
        :data="installmentData[installmentField]"
        border
        style="width: 100%; margin-top: 20px; margin-bottom: 20px"
      >
        <el-table-column label="期数" width="80" align="center">
          <template slot-scope="scope"> 第 {{ scope.$index + 1 }} 期 </template>
        </el-table-column>

        <el-table-column label="付款日期" align="center">
          <template slot-scope="scope">
            <!--:rules="dateRules"-->
            <el-form-item
              :prop="`${installmentField}.${scope.$index}.planPayDate`"
              class="mb-0"
            >
              <el-date-picker
                v-model="scope.row.planPayDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择付款日期"
                style="width: 100%"
                :disabled="editType === 'info'"
                @change="(val) => handleDateChange(val, scope.$index)"
              />
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column label="付款金额" align="center">
          <template slot-scope="scope">
            <el-form-item
              :prop="`${installmentField}.${scope.$index}.amount`"
              :rules="amountRules"
              class="mb-0"
            >
              <el-input-number
                v-model="scope.row.amount"
                :precision="2"
                :step="0.1"
                :min="0"
                placeholder="请输入付款金额"
                style="width: 100%"
                :disabled="editType === 'info'"
                @change="(val) => handleAmountChange(val, scope.$index)"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column v-if="editType === 'info'" label="状态" align="center">
          <template slot-scope="scope">
            {{ scope.row.status.label }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "InstallmentPayment",
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    editType: {
      type: String,
      default: "add",
    },
    installmentField: {
      type: String,
      default: "tradeOrderInstallments",
    },
    forceStopBuyField: {
      type: String,
      default: "forceStopBuy",
    },
    installmentNum: {
      type: Number,
      default: 2,
    },
  },
  data() {
    return {
      // dateRules: [
      //   { required: true, message: "请选择付款日期", trigger: "change" },
      // ],
      amountRules: [
        { required: true, message: "请输入付款金额", trigger: "change" },
        {
          type: "number",
          min: 0,
          message: "付款金额必须大于等于0",
          trigger: "change",
        },
      ],
      localInstallmentNum: 0,
    };
  },
  computed: {
    installmentData() {
      const value = this.value;
      if (!value[this.installmentField]) {
        value[this.installmentField] = [];
      }
      return value;
    },
  },
  watch: {
    installmentNum(newVal) {
      this.localInstallmentNum = newVal;
      this.updateInstallments();
    },
    value: {
      handler(newVal) {
        if (!newVal[this.installmentField]) {
          newVal[this.installmentField] = [];
        }
        this.localInstallmentNum = newVal[this.installmentField].length;
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    if (!this.installmentData[this.installmentField].length) {
      this.installmentData[this.installmentField] = this.generateInstallments(
        this.installmentNum
      );
    }
    this.$emit("input", this.installmentData);
  },
  methods: {
    generateInstallments(num) {
      return Array.from({ length: num }, (_, index) => ({
        installmentIndex: index + 1,
        planPayDate: null,
        amount: null,
        installmentNum: this.localInstallmentNum,
      }));
    },
    updateInstallments() {
      this.installmentData[this.installmentField] = this.generateInstallments(
        this.localInstallmentNum
      );
      this.$emit("input", this.installmentData);
    },

    handleDateChange(date, index) {
      if (index === 0 && date) {
        const baseDate = new Date(date);
        this.installmentData[this.installmentField].forEach((item, i) => {
          if (i > 0) {
            const newDate = new Date(baseDate);
            newDate.setMonth(baseDate.getMonth() + i);
            item.planPayDate = this.formatDate(newDate);
          }
        });
      }
    },

    handleAmountChange(amount, index) {
      if (index === 0 && amount !== null) {
        this.installmentData[this.installmentField].forEach((item, i) => {
          if (i > 0) {
            item.amount = amount;
          }
        });
      }
    },

    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
  },
};
</script>

<style scoped lang="scss">
.billing-method {
  .mb-0 {
    margin-bottom: 0;
  }

  .installment-details {
    .el-input-number {
      width: 100%;
    }
  }
}
</style>
