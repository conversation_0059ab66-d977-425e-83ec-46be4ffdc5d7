<!--
 * @Author: wskg
 * @Date: 2024-08-15 11:33:22
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 采购开票
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            icon="el-icon-view"
            size="mini"
            @click="handleEdit(row, 'edit')"
            >查看</el-button
          >
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";

export default {
  name: "ProcureBill",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          dataIndex: "paymentCode",
          title: "付款单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "procureCode",
          title: "采购单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "buyNumber",
          title: "采购数量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "采购金额",
          isTable: true,
        },
        {
          dataIndex: "refundAmount",
          title: "退货金额",
          isTable: true,
        },
        {
          dataIndex: "billAmount",
          title: "应开票金额",
          isTable: true,
        },
        {
          dataIndex: "initiatorName",
          title: "采购发起人",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "paymentName",
          title: "付款人",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "paymentAt",
          title: "付款时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          width: 150,
        },
        {
          dataIndex: "createdAt",
          title: "开票时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "invoiceStatus",
          title: "开票状态",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formatter: (row) => (row.invoiceStatus === 1 ? "已开票" : "未开票"),
          option: [
            {
              label: "未开票",
              value: 0,
            },
            {
              label: "已开票",
              value: 1,
            },
          ],
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "actions",
        },
      ],
      tableData: [],
      details: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          returnTimeStart: null,
          returnTimeEnd: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      // returnDetailListApi(requestParameters)
      //     .then((res) => {
      //       this.tableData = res.data.rows;
      //       this.localPagination.total = Number(res.data.total);
      //     })
      //     .finally(() => {
      //       this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      //     });
    },
    refresh() {
      this.$refs.ProTable.listLoading = false;
      // this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
