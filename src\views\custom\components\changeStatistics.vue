<!--
 * @Author: wskg
 * @Date: 2024-08-31 10:10:07
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:21
 * @Description: 客户 - 订单查询 - 变化统计
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right" style="font-size: 14px; gap: 16px">
          <div v-for="(item, index) in totalData" :key="index">
            {{ item.title }}客户数：{{ item.number }}
          </div>
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getCustomerOrderByPageApi,
  getCustomerOrderTitleApi,
} from "@/api/customer";

export default {
  name: "ChangeStatistics",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 110,
        },
        {
          dataIndex: "total",
          title: "合计",
          isTable: true,
        },
        // {
        //   dataIndex: "total",
        //   title: "订单总金额",
        //   isSearch: true,
        //   valueType: "inputRange",
        // },
      ],
      tableData: [],
      totalData: [],
    };
  },
  async mounted() {
    await this.getTableTitle();
    await this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      getCustomerOrderByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
    },
    getTableTitle() {
      getCustomerOrderTitleApi({ pageNumber: 1, pageSize: 99 }).then((res) => {
        this.columns = [...this.columns, ...res.data];
        this.totalData = res.data;
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.title-box-right {
  justify-content: flex-start;
}
</style>
