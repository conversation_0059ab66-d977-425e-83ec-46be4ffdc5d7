<!--
 * @Author: wskg
 * @Date: 2025-02-13 15:49:57
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 补录合同附件
 -->
<template>
  <div class="container">
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <PDFFiles
        :file-list="rowData.attachments"
        :limit="5"
        @uploadSuccess="handleLogFileUploadSuccess"
        @uploadRemove="handleFileUploadRemove"
      />
    </ProDialog>
  </div>
</template>

<script>
import PDFFiles from "@/components/ProUpload/pdfFiles.vue";
import { cloneDeep } from "lodash";
import { customerMachineContractAttachmentApi } from "@/api/customer";

export default {
  name: "PatchAttachment",
  components: { PDFFiles },
  data() {
    return {
      dialogVisible: false,
      rowData: {},
      dialogTitle: "",
    };
  },
  methods: {
    show(row) {
      this.rowData = [];
      this.rowData = cloneDeep(row);
      this.dialogTitle = `补录【${row.contractName}】合约附件`;
      this.dialogVisible = true;
    },
    handleOk() {
      const args = {
        id: this.rowData.id,
        attachments: this.rowData.attachments,
      };
      customerMachineContractAttachmentApi(args).then((res) => {
        this.$message.success("操作成功");
        this.dialogVisible = false;
        this.$emit("refresh");
      });
    },
    handleCancel() {
      this.dialogVisible = false;
    },
    handleLogFileUploadSuccess(result) {
      if (!this.rowData.attachments) {
        this.$set(this.rowData, "attachments", []);
      }
      this.rowData.attachments.push(result);
    },
    handleFileUploadRemove(data) {
      this.rowData.attachments = this.rowData.attachments.filter(
        (item) => item.uid !== data.uid
      );
    },
  },
};
</script>

<style scoped lang="scss"></style>
