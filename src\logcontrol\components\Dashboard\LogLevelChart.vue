<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 日志级别分布图表组件
-->
<template>
  <div class="log-level-chart">
    <div ref="chart" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'LogLevelChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    data: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    loading(val) {
      if (this.chart) {
        if (val) {
          this.chart.showLoading()
        } else {
          this.chart.hideLoading()
        }
      }
    }
  },
  mounted() {
    // 延迟初始化，确保容器尺寸已确定
    this.$nextTick(() => {
      setTimeout(() => {
        this.initChart()
      }, 100)
    })
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart()
      
      if (this.loading) {
        this.chart.showLoading()
      }
    },
    
    updateChart() {
      if (!this.chart) return

      // 确保图表尺寸正确
      this.chart.resize()

      const levelColors = {
        'DEBUG': '#909399',
        'INFO': '#409EFF',
        'WARN': '#E6A23C',
        'ERROR': '#F56C6C'
      }
      
      const levels = this.data.map(item => item.level)
      const counts = this.data.map(item => item.count)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: levels
        },
        yAxis: {
          type: 'value',
          name: '数量'
        },
        series: [
          {
            name: '日志数量',
            type: 'bar',
            data: counts.map((count, index) => ({
              value: count,
              itemStyle: {
                color: levelColors[levels[index]] || '#409EFF'
              }
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      this.chart.setOption(option)
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.log-level-chart {
  width: 100%;
  height: 100%;

  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 220px;
    max-height: 260px;
  }
}
</style>
