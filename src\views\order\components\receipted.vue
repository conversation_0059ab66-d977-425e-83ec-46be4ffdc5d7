<!--
 * @Author: wskg
 * @Date: 2025-02-18 15:51:50
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:49
 * @Description: 抄表对账单
 -->
<template>
  <div class="container">
    <!-- 按月展示 -->
    <!--<ProTable-->
    <!--  ref="ProTable"-->
    <!--  :query-param="queryParam"-->
    <!--  :local-pagination="localPagination"-->
    <!--  :columns="columns"-->
    <!--  :data="tableData"-->
    <!--  @loadData="loadData"-->
    <!--&gt;-->
    <!--  <template #action="{ row }">-->
    <!--    <div class="fixed-width">-->
    <!--      <el-button-->
    <!--        icon="el-icon-warning-outline"-->
    <!--        @click="handleEdit(row, 'info')"-->
    <!--      >-->
    <!--        详情-->
    <!--      </el-button>-->
    <!--      <el-button icon="el-icon-edit" @click="handleEdit(row, 'edit')">-->
    <!--        编辑-->
    <!--      </el-button>-->
    <!--    </div>-->
    <!--  </template>-->
    <!--</ProTable>-->
    <!--  -->
    <ProTable
      ref="infoTableData"
      :query-param="infoQueryParam"
      :local-pagination="infoLocalPagination"
      :columns="infoColumns"
      :data="infoTableData"
      @loadData="loadInfoData"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:meter:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            icon="el-icon-warning-outline"
            @click="handleInfoEdit(row, 'info')"
          >
            详情
          </el-button>
          <el-button
            v-if="row.status?.value !== '604'"
            v-auth="['@ums:manage:meter:edit']"
            icon="el-icon-edit"
            @click="handleInfoEdit(row, 'edit')"
          >
            修改
          </el-button>
        </div>
      </template>
    </ProTable>
    <!--<ProDrawer-->
    <!--  :value="drawerVisible"-->
    <!--  :title="drawerTitle"-->
    <!--  size="80%"-->
    <!--  :no-footer="true"-->
    <!--  @cancel="drawerVisible = false"-->
    <!--&gt;-->
    <!--  <ProTable-->
    <!--    ref="infoTableData"-->
    <!--    :query-param="infoQueryParam"-->
    <!--    :columns="infoColumns"-->
    <!--    :data="infoTableData"-->
    <!--    @loadData="loadInfoData"-->
    <!--  >-->
    <!--    <template #action="{ row }">-->
    <!--      <div class="fixed-width">-->
    <!--        <el-button-->
    <!--          icon="el-icon-warning-outline"-->
    <!--          @click="handleInfoEdit(row, 'info')"-->
    <!--        >-->
    <!--          详情-->
    <!--        </el-button>-->
    <!--        <el-button icon="el-icon-edit" @click="handleInfoEdit(row, 'info')">-->
    <!--          修改-->
    <!--        </el-button>-->
    <!--      </div>-->
    <!--    </template>-->
    <!--  </ProTable>-->
    <!--</ProDrawer>-->
    <ReceiptDetails ref="receiptDetails" />
  </div>
</template>

<script>
import ReceiptDetails from "@/views/order/components/receiptDetails.vue";
import { cloneDeep } from "lodash";
import { divideAmount, filterParam, filterParamRange } from "@/utils";
import { dictTreeByCodeApi } from "@/api/user";
import {
  meterExportDetailApi,
  printReceiveListApi,
  showMeterDetailApi,
} from "@/api/statisics";
import { handleExcelExport } from "@/utils/exportExcel";

export default {
  name: "Receipted",
  components: {
    ReceiptDetails,
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "cycle",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "blackWhiteExclude",
          title: "黑白废张",
          isTable: true,
        },
        {
          dataIndex: "colorExclude",
          title: "彩色废张",
          isTable: true,
        },
        {
          dataIndex: "fiveExclude",
          title: "第五色废张",
          isTable: true,
        },
        {
          dataIndex: "blackWhitePoint",
          title: "黑白总印量",
          isTable: true,
        },
        {
          dataIndex: "colorPoint",
          title: "彩色总印量",
          isTable: true,
        },
        {
          dataIndex: "fiveColourPoint",
          title: "第五色总印量",
          isTable: true,
        },
        {
          dataIndex: "totalAmount",
          title: "抄表总金额",
          isTable: true,
        },
        {
          dataIndex: "derateAmount",
          title: "减免总金额",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "应收总金额",
          isTable: true,
        },
        {
          dataIndex: "paidAmount",
          title: "实收总金额",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 120,
        },
      ],
      tableData: [],
      editType: "info",
      // drawer
      drawerVisible: false,
      drawerTitle: "",
      // 明细数据
      infoQueryParam: {},
      infoColumns: [
        {
          dataIndex: "code",
          title: "收款单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        // {
        //   dataIndex: "number",
        //   title: "机器数量",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        //   minWidth: 100,
        // },
        // {
        //   dataIndex: "totalPoint",
        //   title: "总印量",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        //   minWidth: 100,
        // },
        // {
        //   dataIndex: "totalExclude",
        //   title: "扣减废张",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        //   minWidth: 100,
        // },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isTable: true,
          // formatter: (row) => divideAmount(row.totalAmount, 100),
          isSearch: true,
          valueType: "inputRange",
          minWidth: 120,
        },
        {
          dataIndex: "derateAmount",
          title: "减免金额",
          isTable: true,
          // formatter: (row) => divideAmount(row.derateAmount, 100),
          // isSearch: true,
          // valueType: "inputRange",
          minWidth: 100,
        },
        {
          dataIndex: "amount",
          title: "应付金额",
          isTable: true,
          // formatter: (row) => divideAmount(row.amount, 100),
          // isSearch: true,
          // valueType: "inputRange",
          minWidth: 120,
        },
        {
          dataIndex: "paidAmount",
          title: "实付金额",
          isTable: true,
          // formatter: (row) => divideAmount(row.paidAmount, 100),
          isSearch: true,
          valueType: "inputRange",
          minWidth: 120,
        },
        {
          dataIndex: "status",
          title: "付款状态",
          isTable: true,
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(600),
          formatter: (row) => row.status?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 80,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          fixed: "right",
          width: 150,
        },
      ],
      infoTableData: [],
      infoLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      exportLoading: false,
      requestParameters: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    /**
     * 加载列表数据
     * @param parameter
     */
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const requestParameters = cloneDeep(this.queryParam);
    },
    /**
     * 加载月份对账单明细数据
     * @param parameter
     */
    loadInfoData(parameter) {
      this.infoQueryParam = filterParam(
        Object.assign({}, this.infoQueryParam, parameter)
      );
      const searchRange = [
        {
          data: parameter.totalAmount,
        },
        {
          data: parameter.paidAmount,
        },
      ];
      filterParamRange(this, this.infoQueryParam, searchRange);
      const requestParameters = cloneDeep(this.infoQueryParam);
      ["totalAmount", "paidAmount"].forEach(
        (key) => delete requestParameters[key]
      );
      this.requestParameters = requestParameters;
      printReceiveListApi(requestParameters)
        .then((res) => {
          this.infoTableData = res.data.rows;
          this.infoLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.infoTableData
            ? (this.$refs.infoTableData.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    // 查看当前月份对账明细
    handleEdit(row, type) {
      this.editType = type;
      this.drawerTitle = `抄表对账收款 - ${row.cycle}`;
      this.drawerVisible = true;
    },
    handleExport() {
      this.$confirm("此操作将导出抄表对账数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          meterExportDetailApi,
          this.requestParameters,
          "抄表对账单",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    // 查看客户抄表明细
    handleInfoEdit(row, type) {
      this.$refs.receiptDetails.show(row, type);
    },
    refresh() {
      this.$refs.infoTableData.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
