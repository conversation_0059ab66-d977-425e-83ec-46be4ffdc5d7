<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-30 16:02:32
 * @Description: 收款清单
 -->
<template>
  <div class="app-container">
    <ProDrawer
      :value="drawerVisible"
      title="支付登记"
      size="70%"
      :confirm-button-disabled="confirmButLoading"
      :confirm-text="'确认登记'"
      @ok="handleDrawerOk"
      @cancel="handleDrawerCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="editForm"
        :form-list="formColumns"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="editType"
        :confirm-loading="confirmButLoading"
        @proSubmit="proSubmit"
      >
        <template #customerName>
          <div class="tit-boxs">
            <el-button type="success" @click="showDialogFn">选择客户</el-button>
          </div>
          <el-form
            ref="proFormChild"
            :model="editForm"
            :rules="editFormRules"
            :disabled="editType === 'info'"
            label-width="100px"
            class="demo-ruleForm"
          >
            <el-row>
              <el-col :span="24" style="display: flex; flex-wrap: wrap">
                <el-form-item label="客户编码 : " prop="seqId">
                  <el-input
                    v-model="editForm.seqId"
                    placeholder="请选择客户信息"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
                <el-form-item label="店铺名称 : " prop="customerName">
                  <el-input
                    v-model="editForm.customerName"
                    placeholder="请选择客户信息"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
                <el-form-item label="订单类型 : " prop="tradeOrderOrigin">
                  <el-select
                    v-model="editForm.tradeOrderOrigin"
                    placeholder="请选择订单类型"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="item in tradeOrderOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <!--<el-input-->
                  <!--  v-model="editForm.tradeOrderOrigin"-->
                  <!--  placeholder="请选择"-->
                  <!--  :disabled="true"-->
                  <!--&gt;</el-input>-->
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
        <template #orderList>
          <div class="tit-boxs">
            <el-button type="success" @click="showOrderDialogFn">
              选择订单
            </el-button>
          </div>
          <ProTable
            ref="OrderList"
            :show-loading="false"
            :show-search="false"
            :show-setting="false"
            :height="300"
            :columns="orderListColumns"
            :data="orderListTableData"
          >
            <template #btn>
              <div v-if="totalAmount" class="title-box-right">
                <div>总金额 : {{ totalAmount }}</div>
              </div>
            </template>
            <template #actions="{ row }">
              <div class="fixed-width">
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDeleteOrderList(row)"
                >
                  移除
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>
        <template #voucherImg>
          <ProUpload
            :file-list="editForm.voucherImg"
            :type="editType"
            :limit="3"
            @uploadSuccess="handleLicenseImgUploadSuccess"
            @uploadRemove="handleLicenseImgUploadRemove"
          />
          <!--<span v-if="editType !== 'info'">-->
          <!--  仅支持上传png、jpg格式且10M大小内的图片。</span-->
          <!--&gt;-->
        </template>
      </ProForm>
    </ProDrawer>
    <ProDialog
      :value="showDialog"
      title="客户信息"
      width="80%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <ProTable
        ref="ProTables"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="creatColumns"
        row-key="id"
        :local-pagination="localPagination"
        :data="tableData"
        sticky
        :query-param="queryParams"
        :height="410"
        :show-setting="false"
        @loadData="loadData"
      >
        <template #actions="{ row }">
          <span class="fixed-width">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-circle-check"
              @click="sureSelectCustom(row)"
            >
              确定
            </el-button>
          </span>
        </template>
      </ProTable>
    </ProDialog>
    <ProDialog
      :value="showOrderDialog"
      title="订单列表"
      width="80%"
      :confirm-loading="false"
      :confirm-text="'确定'"
      top="50px"
      @ok="handleOrderDialog"
      @cancel="showOrderDialog = false"
    >
      <ProTable
        ref="OrderProTables"
        :query-param="orderQueryParams"
        :local-pagination="orderLocalPagination"
        show-selection
        :height="410"
        :columns="orderColumns"
        :data="orderTableData"
        @loadData="loadOrderData"
        @handleSelectionChange="changeSelectionOrder"
      >
        <!--<template #actions="{ row }">-->
        <!--  <span class="fixed-width">-->
        <!--    <el-button-->
        <!--      size="mini"-->
        <!--      type="primary"-->
        <!--      icon="el-icon-circle-check"-->
        <!--      @click="sureSelectCustom(row)"-->
        <!--    >-->
        <!--      确定-->
        <!--    </el-button>-->
        <!--  </span>-->
        <!--</template>-->
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { getCustomerByPageApi } from "@/api/customer";
import { addAmount, filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getSaleCycleOrderPageApi,
  operatorTradeOrderPageApi,
} from "@/api/operator";
import { getWorkOrderByPageApi } from "@/api/repair";
import { mergeCycleOrderPayApi } from "@/api/pay";
import ProUpload from "@/components/ProUpload/index.vue";

export default {
  name: "RegisterPayInfo",
  components: { ProUpload },
  data() {
    return {
      drawerVisible: false,
      editForm: {},
      tradeOrderOptions: [
        {
          label: "销售订单",
          value: "SALES_ORDER",
        },
        {
          label: "维修工单",
          value: "REPAIR_ORDER",
        },
      ],
      formColumns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isForm: true,
          formOtherSlot: "customerName",
          formSpan: 24,
        },
        // {
        //   dataIndex: "tradeOrderOrigin",
        //   title: "订单类型",
        //   isForm: true,
        //   valueType: "select",
        //   option: [
        //     { label: "销售订单", value: "SALES_ORDER" },
        //     { label: "维修订单", value: "REPAIR_ORDER" },
        //   ],
        //   formSpan: 6,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择订单类型",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "orderList",
          title: "订单列表",
          isForm: true,
          formOtherSlot: "orderList",
          formSpan: 24,
        },
        {
          dataIndex: "voucherImg",
          title: "上传凭证",
          isForm: true,
          formSlot: "voucherImg",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请上传凭证",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "remark",
          title: "备注",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
        },
      ],
      editFormRules: {
        // seqId: [
        //   {
        //     required: true,
        //     message: "请点击选择客户",
        //     trigger: "blur",
        //   },
        // ],
        // customerName: [
        //   {
        //     required: true,
        //     message: "请点击选择客户",
        //     trigger: "blur",
        //   },
        // ],
        tradeOrderOrigin: [
          {
            required: true,
            message: "请选择订单类型",
            trigger: "change",
          },
        ],
      },
      editType: "add",
      // 客户信息
      showDialog: false,
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      queryParams: {},
      creatColumns: [
        // {
        //   dataIndex: "shopRecruitment",
        //   title: "店铺名称",
        //   isTable: true,
        //   // isSearch: true,
        //   // valueType: "input",
        // },
        {
          dataIndex: "name",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "seqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },

        {
          dataIndex: "legalPersonTel",
          title: "法人电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tableSlot: "actions",
        },
      ],
      // 订单列表
      showOrderDialog: false,
      orderLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      orderTableData: [],
      orderQueryParams: {},
      orderColumns: [
        {
          dataIndex: "code",
          title: "订单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerSeq",
          title: "客户编码",
          isTable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
        },
        {
          dataIndex: "totalPay",
          title: "订单金额",
          isTable: true,
        },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",

          width: 150,
        },
        {
          dataIndex: "payTime",
          title: "支付时间",
          isTable: true,
          width: 150,
          formatter: (row) => (row.payTime ? row.payTime : "/"),
        },
      ],
      selectedOrderList: [],
      // 订单列表
      orderListColumns: [
        {
          dataIndex: "code",
          title: "订单编号",
          isTable: true,
        },
        {
          dataIndex: "customerSeq",
          title: "客户编码",
          isTable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
        },
        {
          dataIndex: "totalPay",
          title: "订单金额",
          isTable: true,
        },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "payTime",
          title: "支付时间",
          isTable: true,
          width: 150,
          formatter: (row) => (row.payTime ? row.payTime : "/"),
        },
        {
          dataIndex: "Actions",
          isTable: true,
          title: "操作",
          align: "left",
          fixed: "right",
          width: 100,
          tableSlot: "actions",
        },
      ],
      orderListTableData: [],
      totalAmount: 0,
      confirmButLoading: false,
    };
  },
  watch: {
    orderListTableData: {
      handler(val) {
        this.totalAmount = val.reduce((pre, cur) => {
          return addAmount(pre, cur.totalPay);
        }, 0);
      },
    },
  },
  methods: {
    show() {
      this.editForm = {};
      this.orderListTableData = [];
      this.selectedOrderList = [];
      this.$set(this.editForm, "voucherImg", []);
      this.drawerVisible = true;
    },
    handleDrawerOk() {
      this.$refs.ProForm.handleSubmit();
    },
    async proSubmit(val) {
      try {
        if (this.orderListTableData.length === 0) {
          return this.$message.error("请选择订单");
        }
        if (val.voucherImg.length === 0) {
          return this.$message.error("请上传凭证");
        }
        this.confirmButLoading = true;
        const codes = this.extractIds(this.orderListTableData);
        const args = {
          ...val,
          tradeOrderNumber: codes,
        };
        const result = await mergeCycleOrderPayApi(args);
        if (result.code === 200) {
          this.$message.success("操作成功");
          this.$emit("refresh");
          this.orderListTableData = [];
          this.selectedOrderList = [];
          this.orderQueryParams = {};
          this.drawerVisible = false;
        }
      } finally {
        this.confirmButLoading = false;
      }
    },
    extractIds(data) {
      const ids = [];
      function recurse(items) {
        items.forEach((item) => {
          ids.push(item.code);
          if (item.children && Array.isArray(item.children)) {
            recurse(item.children);
          }
        });
      }
      recurse(data);
      return ids;
    },
    handleDrawerCancel() {
      this.drawerVisible = false;
    },
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParams, parameter);
      getCustomerByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTables
            ? (this.$refs.ProTables.listLoading = false)
            : null;
        });
    },
    loadOrderData(parameter) {
      this.orderQueryParams = filterParam(
        Object.assign({}, this.orderQueryParams, parameter)
      );
      const searchRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.orderQueryParams, searchRange);
      const requestParameters = cloneDeep(this.orderQueryParams);
      delete requestParameters.status;
      if (this.editForm.tradeOrderOrigin === "REPAIR_ORDER") {
        requestParameters.status = ["to_be_settled"];
      }
      const editApi =
        this.editForm.tradeOrderOrigin === "SALES_ORDER"
          ? getSaleCycleOrderPageApi
          : this.editForm.tradeOrderOrigin === "REPAIR_ORDER"
          ? getWorkOrderByPageApi
          : "";
      if (!editApi) {
        this.$message.warning("请选择订单类型");
        return;
      }
      editApi(requestParameters)
        .then((res) => {
          this.orderTableData = res.data.rows;
          this.orderLocalPagination.total = +res.data.total;
          const temp = cloneDeep(this.orderListTableData);
          if (temp.length > 0) {
            this.$refs.OrderProTables.$refs.ProElTable.clearSelection();
            this.orderTableData.forEach((row) => {
              const target = temp.find((item) => item.id === row.id);

              this.$refs.OrderProTables.$refs.ProElTable.toggleRowSelection(
                row,
                !!target
              );
            });
          }
        })
        .finally(() => {
          this.$refs.OrderProTables
            ? (this.$refs.OrderProTables.listLoading = false)
            : null;
        });
    },
    showDialogFn() {
      this.showDialog = true;
      this.$nextTick((e) => {
        this.queryParams = {};
        this.$refs.ProTables.refresh();
      });
    },
    // 选择订单
    showOrderDialogFn() {
      if (!this.editForm.customerId) {
        this.$message.warning("请选择客户信息");
        return;
      }
      this.$refs.proFormChild.validate((valid) => {
        if (valid) {
          this.showOrderDialog = true;
          this.orderLocalPagination = {
            pageNumber: 1,
            pageSize: 10,
            total: 0,
          };
          this.$nextTick((e) => {
            // this.$refs.OrderProTables.$refs.ProElTable.clearSelection();
            // this.selectedOrderList = cloneDeep(this.orderListTableData);
            this.$refs.OrderProTables.refresh();
          });
        }
      });
    },
    handleDeleteOrderList(row) {
      this.orderListTableData = this.orderListTableData.filter(
        (item) => item.id !== row.id
      );
      this.selectedOrderList = this.orderListTableData;
    },
    // 确认选择订单
    handleOrderDialog() {
      // this.orderListTableData = cloneDeep(this.selectedOrderList);
      const addOrderData = this.addOrderData();
      this.orderListTableData = [...this.orderListTableData, ...addOrderData];
      this.showOrderDialog = false;
    },
    // 选择订单
    changeSelectionOrder(val) {
      this.selectedOrderList = val;
    },
    addOrderData() {
      return (this.selectedOrderList = this.selectedOrderList.filter(
        (item) => !this.orderListTableData.some((order) => order.id === item.id)
      ));
    },
    // 确认用户
    async sureSelectCustom(row) {
      this.orderQueryParams = {};
      this.editForm.seqId = row.seqId;
      this.editForm.customerName = row.name;
      this.editForm.customerId = row.id;
      // this.orderQueryParams.customerSeqId = row.seqId;
      this.orderQueryParams.customerId = row.id;
      this.showDialog = false;
    },
    handleLicenseImgUploadSuccess(result) {
      this.editForm.voucherImg.push(result);
    },
    handleLicenseImgUploadRemove(file) {
      const index = this.editForm.voucherImg.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.editForm.voucherImg.splice(index, 1);
    },
  },
};
</script>

<style scoped lang="scss">
.tit-boxs {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px 7px;
  font-size: 16px;
  font-weight: 800;
}
</style>
