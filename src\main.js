/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-04 09:18:46
 * @Description:
 */

import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";

import Element from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import "@/assets/styles/index.scss";

import VueResource from "vue-resource";
import { VueJsonp } from "vue-jsonp";
import VueRulerTool from "vue-ruler-tool";
import VueDragResize from "vue-drag-resize";

// import * as echarts from "echarts";
import moment from "moment";
import intro from "intro.js";
import "intro.js/introjs.css";
import "intro.js/themes/introjs-modern.css";

import "./config/permission";
import "@/directives";
import "@/utils/use";
import { listen, dispatch } from "./config/eventBus.js";
import filters from "@/utils/globalFilters";

// Stagewise 集成
// import StagewisePlugin from "@/websites/untils/stagewise";
// import { getStagewiseConfig } from "@/websites/config/stagewise.config";

Vue.component("VueDragResize", VueDragResize);
Vue.component("VueRulerTool", VueRulerTool);

Vue.use(Element);
Vue.use(VueResource);
Vue.use(VueJsonp);

// 使用Stagewise插件（仅在开发环境）
// if (process.env.NODE_ENV === 'development') {
//   Vue.use(StagewisePlugin, getStagewiseConfig());
// }

// Vue.prototype.$echarts = echarts;
Vue.prototype.$moment = moment;
Vue.prototype.$intro = intro;
Vue.config.productionTip = false;

Object.keys(filters).forEach((k) => Vue.filter(k, filters[k]));

window.eventBus = new Vue();

Vue.mixin({
  props: {
    eventKey: String,
  },
  created() {
    const name = this.$options.name;
    if (name) {
      this._listenEvents(name);
    }
  },
  methods: {
    // 全局事件分发
    globalDispatch(target, params) {
      return new Promise((resolve) => {
        if (typeof target === "object") {
          dispatch(target.target, {
            payload: { eventKey: target.eventKey || "", params },
            callback(data) {
              resolve(data);
            },
          });
        } else {
          dispatch(target, {
            payload: { params },
            callback(data) {
              resolve(data);
            },
          });
        }
      });
    },
    // 事件监听处理
    _listenEvents(prefix) {
      const _this = this;
      let closeActions = [];

      // 获取所有可用的处理函数
      const handlers = Object.keys(_this)
        .filter((key) => {
          const handler = _this[key];
          return !(
            key.startsWith("_") ||
            key.startsWith("$") ||
            typeof handler !== "function" ||
            key === "globalDispatch"
          );
        })
        .map((key) => ({ key, handler: _this[key] }));

      // 注册事件监听
      handlers.forEach(({ key, handler }) => {
        closeActions.push(
          listen(
            `${prefix}/${key}`,
            ({ payload = {}, callback = () => {} } = {}) => {
              const params = payload.params || [];
              if (payload.eventKey || _this.eventKey) {
                if (payload.eventKey !== _this.eventKey) {
                  return;
                }
              }
              const result = handler.apply(_this, params);
              if (result instanceof Promise && result.then) {
                result.then((data) => callback(data));
              } else {
                callback(result);
              }
            }
          )
        );
      });

      // 组件销毁时清理事件监听
      _this.$once("hook:destroyed", () => {
        closeActions.forEach((close) => close());
        closeActions = [];
      });
    },
  },
});

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app");
