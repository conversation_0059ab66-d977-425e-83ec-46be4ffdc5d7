<!--
 * @Author: wskg
 * @Date: 2025-01-15 11:59:00
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-26 15:28:28
 * @Description: 购机合同明细
 -->
<template>
  <div class="app-container">
    <ProDialog
      :value="dialogVisible"
      title="维保合约明细"
      width="75%"
      top="1%"
      :confirm-btn-loading="confirmLoading"
      :no-footer="editType === 'info'"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="ProForm"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :form-param="form"
        :form-list="formColumns"
        :open-type="editType"
        @proSubmit="formSubmit"
      >
        <template #deviceGroup>
          {{ form.deviceGroup?.label }}
        </template>
        <template #serviceInfo>
          <!-- 分期付款 -->
          <!--<InstallmentPayment-->
          <!--  v-if="form.settleMethod === 'INSTALLMENT'"-->
          <!--  v-model="form"-->
          <!--/>-->
          <!-- 服务类型 -->
          <!-- 服务类型：质保 -->
          <SustainService
            v-if="getServiceType(form.serType)"
            ref="sustainRef"
            v-model="form"
            :edit-type="editType"
          />
          <!-- 服务类型：全/半保 -->
          <!--<FullHalfGuaranteed-->
          <!--  v-if="getServiceType(form.serType)"-->
          <!--  ref="serviceRef"-->
          <!--  v-model="form"-->
          <!--  :service-type="form.serType"-->
          <!--  :edit-type="editType"-->
          <!--/>-->
          <!-- 服务类型: 其它 -->
          <OtherService
            v-if="form.serType === 'OTHER'"
            ref="otherRef"
            v-model="form"
            :edit-type="editType"
          />
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
// import InstallmentPayment from "@/views/custom/editCustom/components/contract/installmentPayment.vue";
// import WarrantyContract from "@/views/custom/editCustom/components/contract/warrantyContract.vue";
// import FullHalfGuaranteed from "@/views/custom/editCustom/components/contract/fullHalfGuaranteed.vue";
import SustainService from "@/views/custom/editCustom/components/contract/sustainService.vue";
import OtherService from "@/views/custom/editCustom/components/contract/otherService.vue";
import { addAmount, filterParam, subtractAmount } from "@/utils";
import { cloneDeep } from "lodash";

export default {
  name: "ByuMachineInfo",
  components: { SustainService, OtherService },
  props: {
    isSupplement: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: false,
      // form
      confirmLoading: false,
      form: {},
      defaultForm: {
        contractBuyGive: {}, // 购机明细
        contractServeGive: {}, // 服务明细
      },
      editType: "add",
      formColumns: [
        {
          dataIndex: "deviceSeqId",
          title: "设备组编号",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isForm: true,
          formSlot: "deviceGroup",
          formSpan: 8,
        },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "depositAmount",
          title: "金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 6,
          attrs: {
            suffix: "元",
          },
          prop: [
            {
              required: true,
              message: "请输入金额",
              trigger: "blur",
            },
            {
              validator(rule, value, callback) {
                if (value < 0) {
                  callback(new Error("金额不能小于0"));
                } else {
                  callback();
                }
              },
            },
          ],
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          effect: "light",
          tooltipContent:
            "服务类型的切换可能会清除一些影响程序运行的数据，因此请谨慎操作，避免随意更改服务类型。",
          option: [
            {
              label: "质保服务",
              value: "QA",
            },
            {
              label: "质保含部件",
              value: "QA_COMPONENT",
            },
            {
              label: "维保服务",
              value: "MAINTENANCE",
            },
            {
              label: "其它",
              value: "OTHER",
            },
          ],
          prop: [
            {
              required: true,
              message: "请选择服务类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "serviceInfo",
          title: "服务信息",
          isForm: true,
          formOtherSlot: "serviceInfo",
          formSpan: 24,
        },
      ],
    };
  },
  watch: {
    // isSupplement: {
    //   handler(val) {
    //     if (val) {
    //       this.updateIsFormColumn(this.formColumns, ["addMachin"], false);
    //     } else {
    //       this.updateIsFormColumn(this.formColumns, ["addMachin"], true);
    //     }
    //   },
    //   immediate: true,
    // },
    // 总销售金额
    "form.fullAmount": {
      handler(val) {
        this.calculateArrearsAmount();
      },
    },
    // 定金
    "form.depositAmount": {
      handler(val) {
        this.calculateArrearsAmount();
      },
    },
    // 折扣金额
    "form.discountAmount": {
      handler(val) {
        this.calculateArrearsAmount();
      },
    },
  },
  methods: {
    visible(val, type) {
      this.editType = type;
      this.resetForm().then(() => {
        const formParams = {
          ...this.defaultForm,
          ...val,
        };
        Object.keys(formParams).forEach((key) => {
          if (key === "deviceGroup") {
            return;
          }
          formParams[key] = formParams[key]?.label
            ? formParams[key].value
            : formParams[key];
        });
        this.form = formParams;
        this.dialogVisible = true;
      });
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    formSubmit(val) {
      const validationPromises = [];
      if (this.$refs.sustainRef && this.$refs.sustainRef.$refs.formRef) {
        validationPromises.push(
          new Promise((resolve) => {
            this.$refs.sustainRef.$refs.formRef.validate((valid) => {
              resolve(valid);
            });
          })
        );
      }
      if (this.$refs.otherRef && this.$refs.otherRef.$refs.formRef) {
        validationPromises.push(
          new Promise((resolve) => {
            this.$refs.otherRef.$refs.formRef.validate((valid) => {
              resolve(valid);
            });
          })
        );
      }
      // 等待所有验证完成
      Promise.all(validationPromises).then((results) => {
        if (results.every((result) => result)) {
          const result = cloneDeep(this.form);
          if (result.settleMethod === "INSTALLMENT") {
            if (!result.tradeOrderInstallments.length) {
              this.$message.error("请填写分期信息");
              return;
            }
            // 检查分期列表的总金额是否等于欠款金额
            const totalAmount = result.tradeOrderInstallments.reduce(
              (acc, cur) => {
                return addAmount(acc, cur.amount);
              },
              0
            );
            if (totalAmount !== result.arrearsAmount) {
              this.$message.error("分期金额总和必须等于欠款金额");
              return;
            }
          }
          // if (result.priceType === "LADDER") {
          //   if (!result.repairMonthlyPrices.length) {
          //     return this.$message.error("请填写阶梯价格");
          //   }
          // }
          this.$emit("confirmContractInfo", filterParam(result));
          this.dialogVisible = false;
        } else {
          this.$message.error("请将合约信息填写完整");
        }
      });
    },
    submitForm() {
      const result = cloneDeep(this.form);
      if (result.settleMethod === "INSTALLMENT") {
        if (!result.tradeOrderInstallments.length) {
          this.$message.error("请填写分期信息");
          return;
        }
        // 检查分期列表的总金额是否等于欠款金额
        const totalAmount = result.tradeOrderInstallments.reduce((acc, cur) => {
          return addAmount(acc, cur.amount);
        }, 0);
        if (totalAmount !== result.arrearsAmount) {
          this.$message.error("分期金额总和必须等于欠款金额");
          return;
        }
      }
      this.$emit("confirmContractInfo", result);
      this.dialogVisible = false;
    },
    handleDialogCancel() {
      this.dialogVisible = false;
    },
    getServiceType(type) {
      return ["QA", "QA_COMPONENT", "MAINTENANCE"].includes(type);
    },
    calculateArrearsAmount() {
      const fullAmount = this.form.fullAmount || 0;
      const depositAmount = this.form.depositAmount || 0;
      const discountAmount = this.form.discountAmount || 0;
      if (fullAmount) {
        let arrearsAmount = fullAmount;

        if (depositAmount) {
          arrearsAmount = subtractAmount(arrearsAmount, depositAmount);
        }
        if (discountAmount) {
          arrearsAmount = subtractAmount(arrearsAmount, discountAmount);
        }
        this.form.arrearsAmount = Math.max(arrearsAmount, 0);
      } else {
        this.form.arrearsAmount = 0;
      }
    },
    updateIsFormColumn(columns, keys, isForm) {
      columns.forEach((item) => {
        if (keys.includes(item.dataIndex)) {
          item.isForm = isForm;
        }
      });
    },
    resetForm() {
      return new Promise((resolve) => {
        Object.keys(this.form).forEach((key) => {
          delete this.form[key];
        });
        resolve();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.billing-method {
  .mb-0 {
    margin-bottom: 0;
  }

  .installment-details {
    .el-input-number {
      width: 100%;
    }
  }
}
</style>
