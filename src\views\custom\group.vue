<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:53
 * @Description: 集团管理
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-search
      show-loading
      show-pagination
      :tree-props="{ children: 'customers', hasChildren: 'hasChildren' }"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleEdit(null, 'add')"
        >
          新增集团
        </el-button>
      </template>

      <template #customers="{ row }">
        {{ Array.isArray(row.customers) ? row.customers.length : "/" }}
      </template>
      <template #seq="{ row }">
        {{ Array.isArray(row.customers) ? "集团号" : "/" }}
      </template>
      <template #license="{ row }">
        {{ Array.isArray(row.customers) ? "营业执照名称" : "/" }}
      </template>

      <template #action="{ row }">
        <div v-if="row.customers" class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleEdit(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDialog
      :value="showDialog"
      :title="dialogTitle"
      width="600px"
      :confirm-loading="dialogLoading"
      top="10%"
      :no-footer="editType === 'info'"
      @ok="handleDialogOk"
      @cancel="closeDialog"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParam"
        :form-list="columns"
        :confirm-loading="dialogLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="editType"
        :no-footer="true"
        @proSubmit="formSubmit"
      >
        <template #customerIds>
          <el-select
            ref="select"
            v-model="formParam.customerIds"
            v-selectLoadMore="() => selectLoadMore()"
            multiple
            filterable
            remote
            :disabled="editType === 'info'"
            reserve-keyword
            placeholder="请输入关联客户"
            :remote-method="remoteMethod"
            :loading="dialogLoading"
          >
            <el-option
              v-for="item in customersOptions"
              :key="item.id"
              :label="item.shopRecruitment"
              :value="item.id"
            >
            </el-option>
            <el-option
              v-for="item in formParam.customers"
              :key="item.id"
              :label="item.shopRecruitment"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import ProDialog from "@/components/ProDialog/index.vue";
import ProForm from "@/components/ProForm/index.vue";
import {
  getGroupByPageApi,
  deleteGroupApi,
  getCustomerListApi,
  addGroupApi,
  updateGroupApi,
  getCustomerByPageApi,
} from "@/api/customer";
import { Message, MessageBox } from "element-ui";
export default {
  name: "Group",
  components: { ProTable, ProDialog, ProForm },
  data() {
    return {
      tableData: [],
      columns: [
        {
          dataIndex: "name",
          title: "集团名称",
          isTable: true,
          isSearch: true,
          isForm: true,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请完善必填项信息",
              trigger: "change",
            },
          ],
        },
        // {
        //   dataIndex: "seq",
        //   title: "集团编号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   tableSlot: "seq",
        // },
        {
          dataIndex: "customers",
          title: "关联客户数",
          isTable: true,
          tableSlot: "customers",
        },
        // {
        //   dataIndex: "license",
        //   title: "营业执照名称",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   tableSlot: "license",
        // },
        {
          dataIndex: "customerIds",
          title: "关联客户",
          isForm: "true",
          formSlot: "customerIds",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 220,
        },
      ],
      queryParam: {},
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      showDialog: false,
      dialogTitle: "",
      dialogLoading: false,
      formParam: {},
      editType: "add",
      customersOptions: [],
      localCustomerPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 99,
      },
    };
  },
  mounted() {
    this.refresh();
    // this.remoteMethod();
  },
  methods: {
    async loadData(params) {
      try {
        const result = await getGroupByPageApi(params);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.tableData.map((item) => {
            item.customerIds = item.customers.map((item) => {
              // item.name = item.shopRecruitment;
              item.name = item.subbranch
                ? `${item.shopRecruitment}-${item.subbranch}`
                : item.shopRecruitment;
              return item.id;
            });
          });
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    handleEdit(row, type) {
      const titleMap = {
        add: "新增集团",
        edit: "编辑集团",
        info: "集团详情",
      };
      this.dialogLoading = false;
      this.formParam = JSON.parse(JSON.stringify(row)) || {};

      this.remoteMethod();
      this.editType = type;
      this.dialogTitle = titleMap[type];
      this.showDialog = true;
    },
    handleDelete(row) {
      MessageBox.confirm(
        "删除集团，将取消与下级各客户的关联。确认删除？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(async () => {
          const result = await deleteGroupApi(row.id);
          if (result.code === 200) {
            Message.success("删除成功");
            this.refresh();
          }
        })
        .catch(() => {
          console.log("取消删除");
        });
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    closeDialog() {
      this.showDialog = false;
      this.dialogLoading = false;
      this.formParam = {};
      this.editType = "";
    },
    async formSubmit(val) {
      try {
        this.dialogLoading = true;
        const editApi = this.editType === "add" ? addGroupApi : updateGroupApi;
        const result = await editApi(val);
        if (result.code === 200) {
          Message.success("保存成功");
          this.closeDialog();
          this.refresh();
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.dialogLoading = false;
      }
    },
    // async remoteMethod(query) {
    //   try {
    //     this.dialogLoading = true;
    //     const result = await getCustomerListApi({ name: query });
    //     if (result.code === 200 && result.data) {
    //       this.customersOptions = result.data;
    //     }
    //   } catch (error) {
    //     Message.error(error.message);
    //   } finally {
    //     this.dialogLoading = false;
    //   }
    // },
    async remoteMethod(query) {
      try {
        if (query) {
          this.localCustomerPagination.pageNumber = 1;
          this.customersOptions = [];
        }
        const result = await getCustomerByPageApi({
          ...this.localCustomerPagination,
          name: query || "",
        });
        if (result.code === 200 && result.data) {
          this.customersOptions = [
            ...this.customersOptions,
            ...result.data.rows,
          ];
          this.localCustomerPagination.pageNumber++;
          this.localCustomerPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    selectLoadMore() {
      if (this.customersOptions.length === this.localCustomerPagination.total) {
        return;
      }
      this.remoteMethod();
    },
  },
};
</script>
