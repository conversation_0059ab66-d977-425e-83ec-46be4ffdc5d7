<!--
 * @Author: wskg
 * @Date: 2024-09-04 15:52:47
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 订单 - 耗材统计
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="按物品统计销售" name="按物品统计销售" lazy>
        <ItemsStat type="item" :columns="itemColumns" />
      </el-tab-pane>
      <el-tab-pane label="按机型统计销售" name="按机型统计销售" lazy>
        <ItemsStat type="machine" :columns="machineColumns" />
      </el-tab-pane>
      <el-tab-pane label="按地区统计销售" name="按地区统计销售" lazy>
        <ItemsStat type="area" :columns="areaColumns" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ItemsStat from "@/views/order/components/itemsStat.vue";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "ConsumableStat",
  components: { ItemsStat },
  data() {
    return {
      activeName: "按物品统计销售",
      itemColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
          minWidth: 50,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "type",
          title: "物品种类",
          isTable: true,
          formatter: (row) => row.type?.label,
          isSearch: true,
          valueType: "select",
          searchSlot: "searchType",
        },
        // {
        //   dataIndex: "unit",
        //   title: "所属单元",
        //   isTable: true,
        //   formatter: (row) => row.unit?.label,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(3200),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "number",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "金额",
          isTable: true,
        },
        // {
        //   dataIndex: "",
        //   title: "渠道",
        //   isTable: true,
        //   isSearch: true,
        // },
      ],
      machineColumns: [
        {
          dataIndex: "productIds",
          title: "适用机型",
          isSearch: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
        {
          dataIndex: "series",
          title: "机型",
          isTable: true,
          // tableSlot: "machine",
        },
        // {
        //   dataIndex: "unit",
        //   title: "单位",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "price",
        //   title: "单价",
        //   isTable: true,
        // },
        {
          dataIndex: "number",
          title: "销售数量",
          isTable: true,
        },
        {
          dataIndex: "refundNum",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "销售金额",
          isTable: true,
        },
        {
          dataIndex: "refundAmount",
          title: "退货金额",
          isTable: true,
        },
        {
          dataIndex: "actureAmount",
          title: "实付金额",
          isTable: true,
        },
        {
          dataIndex: "createdAt",
          title: "销售时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
        },
      ],
      areaColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
          minWidth: 50,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "销售数量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "销售金额",
          isTable: true,
        },
        {
          dataIndex: "returnAmount",
          title: "退货金额",
          isTable: true,
        },
        {
          dataIndex: "actureAmount",
          title: "实付金额",
          isTable: true,
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss"></style>
