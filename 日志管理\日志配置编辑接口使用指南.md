# 日志配置编辑接口使用指南

## 📋 概述

本文档为前端开发人员提供日志配置编辑功能的接口使用指导，包括配置的查询、创建、更新、激活和删除操作。

## 🔧 主要配置编辑接口

### 1. 更新配置接口 (推荐)

**接口地址**：`POST /logcontrol/config/update`

**功能说明**：用于更新现有配置或创建新配置

**限流规则**：每分钟最多10次调用

**请求体参数**：
```json
{
  "id": 1,                          // 配置ID (更新时必须提供，新建时设为null)
  "configName": "default",          // 配置名称 (必填)
  "logLevel": "INFO",               // 日志级别 (必填): DEBUG/INFO/WARN/ERROR
  "enableLocationLog": true,        // 是否启用位置日志 (必填)
  "locationLogInterval": 3000,      // 位置日志间隔，单位毫秒 (必填，>0)
  "logUploadInterval": 3600,        // 上传间隔，单位毫秒 (必填，>0)
  "maxLogFiles": 5,                 // 最大日志文件数量 (必填，>0)
  "configVersion": "1.0.0",         // 配置版本 (必填)
  "isActive": true                  // 是否激活
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "ok",
  "data": null
}
```

**使用场景**：
- 编辑现有配置：提供id字段
- 创建新配置：id设为null

### 2. 从模板创建配置接口

**接口地址**：`POST /logcontrol/config/create-from-template`

**功能说明**：基于现有模板快速创建新配置

**请求体参数**：
```json
{
  "templateName": "debug",          // 模板名称
  "configName": "new_debug_config"  // 新配置名称
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "ok",
  "data": {
    "id": 4,
    "configName": "new_debug_config",
    "logLevel": "DEBUG",
    "enableLocationLog": true,
    "locationLogInterval": 1000,
    "logUploadInterval": 1800,
    "maxLogFiles": 10,
    "configVersion": "1.0.1642752600",
    "isActive": true
  }
}
```

### 3. 激活配置接口

**接口地址**：`POST /logcontrol/config/activate/{id}`

**功能说明**：激活指定配置，同时停用其他所有配置

**路径参数**：
- `id`: 配置ID (必填)

**响应示例**：
```json
{
  "code": 200,
  "message": "ok",
  "data": null
}
```

### 4. 删除配置接口

**接口地址**：`DELETE /logcontrol/config/{id}`

**功能说明**：删除指定配置

**路径参数**：
- `id`: 配置ID (必填)

**响应示例**：
```json
{
  "code": 200,
  "message": "ok",
  "data": null
}
```

## 🔍 辅助查询接口

### 5. 获取所有配置列表

**接口地址**：`GET /logcontrol/config/list`

**功能说明**：获取所有配置的列表

**响应示例**：
```json
{
  "code": 200,
  "message": "ok",
  "data": [
    {
      "id": 1,
      "configName": "default",
      "logLevel": "INFO",
      "enableLocationLog": true,
      "locationLogInterval": 3000,
      "logUploadInterval": 3600,
      "maxLogFiles": 5,
      "configVersion": "1.0.0",
      "isActive": true
    },
    {
      "id": 2,
      "configName": "debug",
      "logLevel": "DEBUG",
      "enableLocationLog": true,
      "locationLogInterval": 1000,
      "logUploadInterval": 1800,
      "maxLogFiles": 10,
      "configVersion": "1.1.0",
      "isActive": false
    }
  ]
}
```

### 6. 获取配置模板列表

**接口地址**：`GET /logcontrol/config/templates`

**功能说明**：获取可用的配置模板列表（从数据库读取）

**响应示例**：
```json
{
  "code": 200,
  "message": "ok",
  "data": [
    {
      "templateName": "default",
      "displayName": "default",
      "logLevel": "INFO",
      "enableLocationLog": true,
      "locationLogInterval": 3000,
      "logUploadInterval": 3600,
      "maxLogFiles": 5,
      "description": "配置版本: 1.0.0"
    },
    {
      "templateName": "debug",
      "displayName": "debug",
      "logLevel": "DEBUG",
      "enableLocationLog": true,
      "locationLogInterval": 1000,
      "logUploadInterval": 1800,
      "maxLogFiles": 10,
      "description": "配置版本: 1.1.0"
    }
  ]
}
```

## 💡 前端开发建议

### 编辑现有配置的流程

1. **获取配置列表**
   ```javascript
   const response = await fetch('/logcontrol/config/list');
   const configs = response.data;
   ```

2. **选择要编辑的配置**
   ```javascript
   const configToEdit = configs.find(config => config.id === selectedId);
   ```

3. **修改配置字段**
   ```javascript
   const updatedConfig = {
     ...configToEdit,
     logLevel: 'DEBUG',  // 修改日志级别
     locationLogInterval: 2000  // 修改位置日志间隔
   };
   ```

4. **提交更新**
   ```javascript
   const response = await fetch('/logcontrol/config/update', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify(updatedConfig)
   });
   ```

### 创建新配置的流程

**方式一：从模板创建**
```javascript
const response = await fetch('/logcontrol/config/create-from-template', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    templateName: 'debug',
    configName: 'my_custom_config'
  })
});
```

**方式二：直接创建**
```javascript
const newConfig = {
  id: null,  // 新建时设为null
  configName: 'my_new_config',
  logLevel: 'INFO',
  enableLocationLog: true,
  locationLogInterval: 5000,
  logUploadInterval: 7200,
  maxLogFiles: 8,
  configVersion: '1.0.0',
  isActive: false
};

const response = await fetch('/logcontrol/config/update', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(newConfig)
});
```

## ⚠️ 重要注意事项

### 字段验证规则
- `configName`: 不能为空
- `logLevel`: 不能为空，只能是 DEBUG/INFO/WARN/ERROR
- `enableLocationLog`: 不能为空，布尔值
- `locationLogInterval`: 不能为空，必须大于0
- `logUploadInterval`: 不能为空，必须大于0
- `maxLogFiles`: 不能为空，必须大于0
- `configVersion`: 不能为空

### 业务逻辑
- 更新配置时会自动生成新的版本号
- 激活配置时会自动停用其他所有配置
- 每个系统只能有一个激活的配置
- 删除配置是软删除，数据仍保留在数据库中

### 错误处理
- 接口调用失败时，检查返回的错误信息
- 注意限流规则，避免频繁调用更新接口
- 验证失败时会返回具体的错误提示

### 性能优化建议
- 配置列表数据可以适当缓存
- 避免不必要的频繁更新操作
- 批量操作时考虑使用批量接口

## 📞 技术支持

如有接口使用问题，请联系后端开发团队或查看完整的API文档。
