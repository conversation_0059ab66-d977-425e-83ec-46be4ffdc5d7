/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-18 15:52:36
 * @Description: 请求地址配置
 */
window.config = {
  api: {
    //  =====================  需根据部署环境进行配置 start  =====================
    // baseURL: "http://*************:8080/api", //功能接口地址
    // uploadURL: "http://*************:8080/api", //上传地址
    baseURL: "http://*************:8080/api", //功能接口地址
    uploadURL: "http://*************:8080/api", //上传地址
    // baseURL: "http://*************:8080/api", //功能接口地址
    // uploadURL: "http://*************:8080/api", //上传地址
    // baseURL: "http://*************:8080/api", //功能接口地址
    // uploadURL: "http://*************:8080/api", //上传地址
    // baseURL: "https://plat.sczjzy.com.cn/api", //功能接口地址
    // uploadURL: "https://plat.sczjzy.com.cn/api", //上传地址
    // baseURL: "https://whole.benyin.ltd/api", //功能接口地址
    // uploadURL: "https://whole.benyin.ltd/api", //上传地址
    //  =====================  需根据部署环境进行配置 end  =====================
  },
};
