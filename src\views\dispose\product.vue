<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 17:16:38
 * @Description: 品牌产品树
 -->

<template>
  <div class="view app-container">
    <el-tabs v-model="activeName" @tab-click="change">
      <el-tab-pane label="机型管理" name="model" lazy>
        <model ref="model"></model>
      </el-tab-pane>
      <el-tab-pane label="品牌管理" name="pbrand" lazy>
        <pbrand></pbrand>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import {
  productListApi,
  productAddApi,
  productDelApi,
  productEditApi,
  productAllApi,
} from "@/api/dispose";

import { isEmpty, cloneDeep } from "lodash";
import pbrand from "./pbrand.vue";
import model from "./model.vue";

export default {
  name: "Product",
  components: {
    model,
    pbrand,
  },
  mixins: [],
  props: {},
  data() {
    return {
      activeName: "model",
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {},
  methods: {
    change(data) {
      if (data.index == "0") {
        this.$refs.model.init();
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
