/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-07-04 16:56:08
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-04 18:20:26
 * @Description: 检测版本更新
 */
import { Notification } from "element-ui";
export class Updater {
  oldScript = [];
  intervalId = null;

  constructor() {
    this.init();
  }

  static async getScriptList() {
    const html = await fetch("/").then((res) => res.text());
    const reg = /<script(?:\s+[^>]*)?>(.*?)<\/script\s*>/gi;
    return html.match(reg) || [];
  }

  static compare(oldArr, newArr) {
    const isSame =
      oldArr.length === newArr.length &&
      oldArr.every((v, i) => v === newArr[i]);
    if (!isSame && newArr.length) {
      Notification.warning({
        id: "updater",
        title: "新版本可用",
        message:
          "已发布新版本，包含功能改进和性能优化。点击左上角刷新按钮立即体验！",
        duration: 0,
      });
    }
  }

  async init() {
    this.oldScript = await Updater.getScriptList();
  }

  start() {
    const time = 1000 * 60 * 60; // 周期为 1h  1000 * 60 * 60
    this.intervalId = window.setInterval(async () => {
      const list = await Updater.getScriptList();
      Updater.compare(this.oldScript, list);
      this.oldScript = list; // 始终更新缓存
    }, time);
  }
  // 手动触发检测
  async check() {
    console.log("手动触发");
    this.stop();
    this.start();
    const list = await Updater.getScriptList();
    Updater.compare(this.oldScript, list);
  }
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
}
