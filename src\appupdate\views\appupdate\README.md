# Android应用版本管理功能

## 📋 功能概述

Android应用版本管理功能为benyin-web项目提供了完整的移动应用版本管理解决方案，包括版本发布、更新控制、统计监控等核心功能。

## 🚀 主要功能

### 1. 版本管理
- ✅ 版本列表展示（分页、搜索、排序）
- ✅ 版本信息编辑（更新说明、强制更新、启用状态）
- ✅ 版本删除（逻辑删除）
- ✅ 版本详情查看

### 2. 版本发布
- ✅ APK文件上传（拖拽上传、文件校验）
- ✅ 版本信息填写（版本名称、版本号、更新说明）
- ✅ 发布配置（强制更新、启用状态）
- ✅ 文件MD5校验

### 3. 数据管理
- ✅ 版本数据导出
- ✅ 搜索和筛选
- ✅ 分页展示

### 4. 紧急操作
- ✅ 版本回退
- ✅ 暂停所有更新
- ✅ 恢复更新推送
- ✅ 管理员强制更新

## 📁 文件结构

```
src/views/appupdate/
├── index.vue                    # 主页面组件
├── components/
│   ├── VersionList.vue          # 版本列表表格
│   ├── PublishDialog.vue        # 发布版本对话框
│   ├── EditDialog.vue           # 编辑版本对话框
│   └── EmergencyDialog.vue      # 紧急操作对话框
└── README.md                    # 说明文档

src/api/
└── appVersion.js                # API接口封装
```

## 🔌 API接口

### 管理端接口
- `POST /api/admin/app-version/page` - 版本列表分页查询
- `POST /api/admin/app-version/publish` - 发布新版本
- `PUT /api/admin/app-version/{id}` - 更新版本信息
- `POST /api/admin/app-version/{id}/force` - 设置/取消强制更新
- `POST /api/admin/app-version/emergency/{action}` - 紧急操作
- `DELETE /api/admin/app-version/{id}` - 删除版本

### 客户端接口
- `GET /api/app/update` - 检查应用更新（供Android客户端使用）

## 🛠️ 使用方法

### 1. 访问功能
启动项目后，在左侧菜单找到"应用更新管理"，或直接访问：
```
http://localhost:3000/#/appupdate
```

### 2. 发布新版本
1. 点击"发布新版本"按钮
2. 填写版本信息（版本名称、版本号、更新说明）
3. 上传APK文件（支持拖拽上传）
4. 配置发布设置（强制更新、立即启用）
5. 点击"发布版本"完成发布

### 3. 管理现有版本
1. 在版本列表中查看所有版本
2. 使用搜索功能筛选版本
3. 点击"编辑"修改版本信息
4. 点击"设为强制/取消强制"控制强制更新
5. 点击"详情"查看完整版本信息

### 4. 紧急操作
1. 点击"紧急操作"下拉菜单
2. 选择操作类型：
   - **版本回退**：回退到指定版本并设为强制更新
   - **暂停所有更新**：暂停所有版本的更新推送
   - **恢复更新推送**：恢复正常的版本更新推送
3. 确认操作并执行

## 🔒 权限要求

此功能需要管理员权限才能访问。请确保：
1. 用户已登录系统
2. 用户具有应用更新管理权限
3. 用户具有文件上传权限

## 📊 数据结构

### 版本信息对象
```javascript
{
  id: 1,                          // 版本ID
  versionName: "1.3.0",           // 版本名称
  versionCode: 13,                // 版本号
  apkFileName: "app-v1.3.0.apk",  // APK文件名
  cosUrl: "https://...",          // 下载链接
  fileSize: 15728640,             // 文件大小（字节）
  fileMd5: "d41d8cd98...",        // 文件MD5校验值
  updateLog: "更新说明",           // 更新说明
  isForce: false,                 // 是否强制更新
  adminForce: false,              // 管理员强制标志
  isActive: true,                 // 是否启用
  downloadCount: 1500,            // 下载次数
  createdAt: "2023-07-01T16:45:00" // 创建时间
}
```

## 🎨 UI组件说明

### 1. VersionList（版本列表）
- 基于ProTable组件实现
- 支持分页、搜索、排序
- 提供版本操作按钮

### 2. PublishDialog（发布对话框）
- 集成文件上传功能
- 表单验证和数据校验
- 上传进度显示

### 3. EditDialog（编辑对话框）
- 版本信息编辑
- 只读字段保护
- 修改状态检测

### 4. EmergencyDialog（紧急操作）
- 多种操作类型支持
- 操作确认和风险提示
- 参数配置界面

## 🔧 技术特点

1. **完全兼容**：基于现有Vue 2 + Element UI架构
2. **组件复用**：使用项目标准的ProTable、ProUpload组件
3. **文件上传**：集成腾讯云COS上传功能
4. **数据校验**：前端表单验证 + 文件安全检查
5. **用户体验**：加载状态、操作反馈、错误处理
6. **响应式设计**：适配不同屏幕尺寸

## 🚨 注意事项

1. **文件安全**：只允许上传.apk文件，最大100MB
2. **版本号规则**：版本号必须递增，不能重复
3. **强制更新**：管理员强制更新优先级最高
4. **紧急操作**：操作前会有多重确认，请谨慎使用
5. **数据备份**：重要操作前建议备份数据

## 🐛 故障排除

### 常见问题

1. **文件上传失败**
   - 检查文件格式是否为.apk
   - 检查文件大小是否超过100MB
   - 检查网络连接是否正常

2. **版本发布失败**
   - 检查版本号是否已存在
   - 检查版本名称格式是否正确
   - 检查更新说明是否填写

3. **页面加载异常**
   - 检查后端API是否正常
   - 检查用户权限是否足够
   - 查看浏览器控制台错误信息

### 调试方法

1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 查看Network面板的API请求状态
4. 检查Vue DevTools中的组件状态

## 📞 技术支持

如遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查后端API接口状态
3. 联系技术支持团队

---

**开发完成时间**：2025-01-29  
**版本**：v1.0.0  
**技术栈**：Vue 2.6 + Element UI + ProTable
