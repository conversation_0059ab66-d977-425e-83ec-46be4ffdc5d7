/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-07-18 16:34:28
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-18 16:44:06
 * @Description:
 */
/**
 * 网站配置字段定义
 * 与React项目的CONFIG_FIELD_INFO保持完全一致
 */

// 预定义的配置字段信息
export const CONFIG_FIELD_INFO = {
  siteTitle: {
    label: "网站标题",
    description: "显示在浏览器标签页和网站Logo处",
    type: "text",
    placeholder: "请输入网站标题",
    required: true,
    maxLength: 50,
  },
  companyName: {
    label: "公司名称",
    description: "完整的公司或机构名称",
    type: "text",
    placeholder: "请输入公司名称",
    required: true,
    maxLength: 100,
  },
  contactPhone: {
    label: "联系电话",
    description: "主要联系电话，支持座机和手机格式",
    type: "phone",
    placeholder: "请输入联系电话",
    pattern: /^(\d{3,4}-?\d{7,8}|\d{11})$/,
    patternMessage: "请输入正确的电话号码格式",
  },
  servicePhone: {
    label: "客服电话",
    description: "客户服务专线电话",
    type: "phone",
    placeholder: "请输入客服电话",
    pattern: /^(\d{3,4}-?\d{7,8}|\d{11})$/,
    patternMessage: "请输入正确的电话号码格式",
  },
  contactEmail: {
    label: "联系邮箱",
    description: "主要联系邮箱地址",
    type: "email",
    placeholder: "请输入联系邮箱",
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    patternMessage: "请输入正确的邮箱格式",
  },
  companyAddress: {
    label: "公司地址",
    description: "公司或门店的详细地址",
    type: "textarea",
    placeholder: "请输入公司地址",
    maxLength: 200,
  },
  businessHours: {
    label: "营业时间",
    description: "营业时间说明",
    type: "text",
    placeholder: "例如：周一至周五 9:00-18:00",
    maxLength: 100,
  },
  businessType: {
    label: "业务类型",
    description: "主要业务范围和服务项目",
    type: "textarea",
    placeholder: "请输入业务类型描述",
    maxLength: 500,
  },
  copyrightNotice: {
    label: "版权信息",
    description: "网站底部显示的版权声明",
    type: "text",
    placeholder: "请输入版权信息",
    maxLength: 200,
  },
  icpNumber: {
    label: "ICP备案号",
    description: "ICP备案号码",
    type: "text",
    placeholder: "请输入ICP备案号",
    maxLength: 50,
  },
  icpLink: {
    label: "ICP备案链接",
    description: "ICP备案查询链接",
    type: "url",
    placeholder: "请输入ICP备案链接",
    pattern: /^https?:\/\/.+/,
    patternMessage: "请输入有效的URL地址",
  },
  policeNumber: {
    label: "公安备案号",
    description: "公安备案号码",
    type: "text",
    placeholder: "请输入公安备案号",
    maxLength: 50,
  },
  policeLink: {
    label: "公安备案链接",
    description: "公安备案查询链接",
    type: "url",
    placeholder: "请输入公安备案链接",
    pattern: /^https?:\/\/.+/,
    patternMessage: "请输入有效的URL地址",
  },
  companyProfile: {
    label: "公司简介",
    description: "公司详细介绍",
    type: "textarea",
    placeholder: "请输入公司简介",
    maxLength: 1000,
  },
  servicePhilosophy: {
    label: "服务理念",
    description: "公司服务理念和宗旨",
    type: "text",
    placeholder: "请输入服务理念",
    maxLength: 200,
  },
  coreAdvantages: {
    label: "核心优势",
    description: "公司核心竞争优势",
    type: "textarea",
    placeholder: "请输入核心优势",
    maxLength: 500,
  },
  headerLogoUrl: {
    label: "头部Logo",
    description: "网站头部Logo图片",
    type: "image",
    placeholder: "请上传头部Logo",
  },
  footerLogoUrl: {
    label: "底部Logo",
    description: "网站底部Logo图片",
    type: "image",
    placeholder: "请上传底部Logo",
  },
  tencentMapKey: {
    label: "腾讯地图Key",
    description: "腾讯地图API密钥，用于地图功能",
    type: "text",
    placeholder: "请输入腾讯地图API Key",
    sensitive: true,
    maxLength: 100,
  },
  websiteDomain: {
    label: "网站域名",
    description: "前端展示网站的域名地址，用于预览功能",
    type: "url",
    placeholder: "请输入网站域名，如：https://example.com",
    pattern: /^https?:\/\/[^\s/$.?#].[^\s]*$/,
    patternMessage: "请输入有效的域名格式，如：https://example.com",
    maxLength: 200,
  },
  // QQ联系方式
  qqNumber: {
    label: "QQ号码",
    description: "QQ客服号码",
    type: "text",
    placeholder: "请输入QQ号码",
    maxLength: 20,
  },
  qqIcon: {
    label: "QQ图标",
    description: "QQ联系方式图标",
    type: "image",
    placeholder: "请上传QQ图标",
  },
  qqQrCode: {
    label: "QQ二维码",
    description: "QQ联系方式二维码",
    type: "image",
    placeholder: "请上传QQ二维码",
  },
  // 微信联系方式
  wechatNumber: {
    label: "微信号",
    description: "微信客服号码",
    type: "text",
    placeholder: "请输入微信号",
    maxLength: 50,
  },
  wechatIcon: {
    label: "微信图标",
    description: "微信联系方式图标",
    type: "image",
    placeholder: "请上传微信图标",
  },
  wechatQrCode: {
    label: "微信二维码",
    description: "微信联系方式二维码",
    type: "image",
    placeholder: "请上传微信二维码",
  },
  // 支付宝联系方式
  alipayNumber: {
    label: "支付宝账号",
    description: "支付宝联系账号",
    type: "text",
    placeholder: "请输入支付宝账号",
    maxLength: 50,
  },
  alipayIcon: {
    label: "支付宝图标",
    description: "支付宝联系方式图标",
    type: "image",
    placeholder: "请上传支付宝图标",
  },
  alipayQrCode: {
    label: "支付宝二维码",
    description: "支付宝联系方式二维码",
    type: "image",
    placeholder: "请上传支付宝二维码",
  },
  // 钉钉联系方式
  dingdingNumber: {
    label: "钉钉号",
    description: "钉钉联系号码",
    type: "text",
    placeholder: "请输入钉钉号",
    maxLength: 50,
  },
  dingdingIcon: {
    label: "钉钉图标",
    description: "钉钉联系方式图标",
    type: "image",
    placeholder: "请上传钉钉图标",
  },
  dingdingQrCode: {
    label: "钉钉二维码",
    description: "钉钉联系方式二维码",
    type: "image",
    placeholder: "请上传钉钉二维码",
  },
  // 抖音联系方式
  douyinNumber: {
    label: "抖音号",
    description: "抖音联系号码",
    type: "text",
    placeholder: "请输入抖音号",
    maxLength: 50,
  },
  douyinIcon: {
    label: "抖音图标",
    description: "抖音联系方式图标",
    type: "image",
    placeholder: "请上传抖音图标",
  },
  douyinQrCode: {
    label: "抖音二维码",
    description: "抖音联系方式二维码",
    type: "image",
    placeholder: "请上传抖音二维码",
  },
  // 小红书联系方式
  xiaohongshuNumber: {
    label: "小红书号",
    description: "小红书联系号码",
    type: "text",
    placeholder: "请输入小红书号",
    maxLength: 50,
  },
  xiaohongshuIcon: {
    label: "小红书图标",
    description: "小红书联系方式图标",
    type: "image",
    placeholder: "请上传小红书图标",
  },
  xiaohongshuQrCode: {
    label: "小红书二维码",
    description: "小红书联系方式二维码",
    type: "image",
    placeholder: "请上传小红书二维码",
  },
};

// 配置字段分组
export const CONFIG_FIELD_GROUPS = {
  basic: {
    label: "基本信息",
    fields: [
      "siteTitle",
      "companyName",
      "businessType",
      "companyProfile",
      "servicePhilosophy",
      "coreAdvantages",
    ],
  },
  contact: {
    label: "联系信息",
    fields: [
      "contactPhone",
      "servicePhone",
      "contactEmail",
      "companyAddress",
      "businessHours",
    ],
  },
  branding: {
    label: "品牌形象",
    fields: ["headerLogoUrl", "footerLogoUrl", "copyrightNotice"],
  },
  legal: {
    label: "法律信息",
    fields: ["icpNumber", "icpLink", "policeNumber", "policeLink"],
  },
  services: {
    label: "第三方服务",
    fields: ["tencentMapKey", "websiteDomain"],
  },
  social: {
    label: "社交联系方式",
    fields: [
      "qqNumber",
      "qqIcon",
      "qqQrCode",
      "wechatNumber",
      "wechatIcon",
      "wechatQrCode",
      "alipayNumber",
      "alipayIcon",
      "alipayQrCode",
      "dingdingNumber",
      "dingdingIcon",
      "dingdingQrCode",
      "douyinNumber",
      "douyinIcon",
      "douyinQrCode",
      "xiaohongshuNumber",
      "xiaohongshuIcon",
      "xiaohongshuQrCode",
    ],
  },
};

// 敏感配置字段列表
export const SENSITIVE_CONFIG_FIELDS = ["tencentMapKey"];

// 获取字段信息
export function getFieldInfo(fieldKey) {
  return CONFIG_FIELD_INFO[fieldKey] || null;
}

// 获取字段分组
export function getFieldGroup(fieldKey) {
  for (const [groupKey, group] of Object.entries(CONFIG_FIELD_GROUPS)) {
    if (group.fields.includes(fieldKey)) {
      return groupKey;
    }
  }
  return null;
}

// 检查是否为敏感字段
export function isSensitiveField(fieldKey) {
  return SENSITIVE_CONFIG_FIELDS.includes(fieldKey);
}

// 验证字段值
export function validateFieldValue(fieldKey, value) {
  const fieldInfo = getFieldInfo(fieldKey);
  if (!fieldInfo) {
    return { valid: false, message: "未知字段" };
  }

  // 必填验证
  if (fieldInfo.required && (!value || value.trim() === "")) {
    return { valid: false, message: `${fieldInfo.label}不能为空` };
  }

  // 长度验证
  if (fieldInfo.maxLength && value && value.length > fieldInfo.maxLength) {
    return {
      valid: false,
      message: `${fieldInfo.label}不能超过${fieldInfo.maxLength}个字符`,
    };
  }

  // 格式验证
  if (fieldInfo.pattern && value && !fieldInfo.pattern.test(value)) {
    return {
      valid: false,
      message: fieldInfo.patternMessage || `${fieldInfo.label}格式不正确`,
    };
  }

  return { valid: true };
}

export default {
  CONFIG_FIELD_INFO,
  CONFIG_FIELD_GROUPS,
  SENSITIVE_CONFIG_FIELDS,
  getFieldInfo,
  getFieldGroup,
  isSensitiveField,
  validateFieldValue,
};
