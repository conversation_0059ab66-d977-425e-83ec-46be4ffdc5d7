<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-04 17:50:13
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :height="510"
      :data="tableData"
      @loadData="loadData"
    >
      <!--<template #btn>-->
      <!--  <el-button-->
      <!--    type="success"-->
      <!--    icon="el-icon-plus"-->
      <!--    size="mini"-->
      <!--    @click="handleEvent(undefined, 'add')"-->
      <!--  >-->
      <!--    新增-->
      <!--  </el-button>-->
      <!--</template>-->
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleView(row, 'info')">
            查看
          </el-button>
          <!--<el-button-->
          <!--  v-if="row.deliveryStatus === 0 && row.status?.value !== 'CANCEL'"-->
          <!--  icon="el-icon-present"-->
          <!--  @click="handleMachineDispatch(row, 'edit')"-->
          <!--&gt;-->
          <!--  发货-->
          <!--</el-button>-->
          <!--<el-button-->
          <!--  v-if="row.deliveryStatus === 1 && row.status?.value !== 'CANCEL'"-->
          <!--  icon="el-icon-present"-->
          <!--  @click="handleMachineDispatch(row, 'info')"-->
          <!--&gt;-->
          <!--  发货详情-->
          <!--</el-button>-->
          <!--<el-button-->
          <!--  v-if="-->
          <!--    (row.contractType.value === '1265' ||-->
          <!--      row.contractType.value === '1230') &&-->
          <!--    row.status?.value === 'RETURN' &&-->
          <!--    row.isReturn-->
          <!--  "-->
          <!--  icon="el-icon-circle-check"-->
          <!--  @click="handleReturnMachineReceive(row, 'edit')"-->
          <!--&gt;-->
          <!--  退机收货-->
          <!--</el-button>-->
          <!--<el-button-->
          <!--  v-if="-->
          <!--    (row.contractType.value === '1265' ||-->
          <!--      row.contractType.value === '1230') &&-->
          <!--    row.status?.value === 'UNEFFECT' &&-->
          <!--    row.isReturn-->
          <!--  "-->
          <!--  icon="el-icon-circle-check"-->
          <!--  @click="handleReturnMachineReceive(row, 'info')"-->
          <!--&gt;-->
          <!--  退机收货详情-->
          <!--</el-button>-->
        </div>
      </template>
      <template #attachments="{ row }">
        <el-popover
          v-if="row.attachments?.length > 0"
          placement="bottom"
          title="合约文件"
          width="400"
          trigger="hover"
        >
          <div class="attachments">
            <el-link
              v-for="item in row.attachments"
              :key="item.key"
              :disabled="!hasDownloadPermission"
              :href="item.url"
              icon="el-icon-folder-opened"
            >
              {{ item.name }}
            </el-link>
          </div>

          <el-button slot="reference" type="text" size="mini"> 查看 </el-button>
        </el-popover>
      </template>
    </ProTable>
    <!-- 新增、查看、编辑 -->
    <ProDrawer
      :value="showDrawer"
      :title="title"
      size="65%"
      :confirm-button-disabled="confirmLoading"
      :method-type="methodType"
      :no-footer="methodType === 'info'"
      @ok="handleSubmit"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="formSubmit"
      >
        <template #customerName>
          <div
            style="
              width: 100%;
              display: flex;
              justify-content: space-between;
              gap: 20px;
            "
          >
            <el-input
              v-model="form.customerName"
              disabled
              placeholder="请选择客户名称"
            />
            <el-button
              v-if="methodType === 'add'"
              type="primary"
              size="mini"
              @click="handleSelectCustomer"
            >
              选择客户
            </el-button>
          </div>
        </template>
        <template #deviceGroupId>
          <el-select
            v-model="form.deviceGroupId"
            style="width: 100%"
            placeholder="请选择设备组名称"
            :disabled="methodType !== 'add'"
          >
            <el-option
              v-for="item in deviceGroupOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <template #signId>
          <el-select
            v-model="form.signId"
            style="width: 100%"
            :disabled="methodType === 'info'"
          >
            <el-option
              v-for="item in userList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <template #agentId>
          <el-select
            v-model="form.agentId"
            style="width: 100%"
            :disabled="methodType === 'info'"
          >
            <el-option
              v-for="item in userList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 选择客户 -->
    <ProDialog
      :value="showDialog"
      title="客户信息"
      width="80%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <ProTable
        ref="ProTables"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="createColumns"
        :local-pagination="customerLocalPagination"
        :data="tableData1"
        sticky
        :query-param="customerQueryParams"
        :height="430"
        :show-setting="false"
        @loadData="loadData1"
      >
        <template #actions="slotProps">
          <span class="fixed-width">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-edit-outline"
              @click="sureSelectCustom(slotProps.row)"
            >
              确定
            </el-button>
          </span>
        </template>
      </ProTable>
    </ProDialog>
    <!-- 机器发货 -->
    <!--<DispatchMachine ref="dispatchMachine" @refresh="refresh" />-->
    <!--购机、租赁、融资合约-->
    <BuyRentFinanceContract
      ref="buyRentFinanceContract"
      :contract-type="contractType"
    />
    <!-- 抄表合约 -->
    <InsureContract ref="insureContract" :contract-type="contractType" />
    <!-- 维保合约 -->
    <SafeguardContract ref="safeguardContract" :contract-type="contractType" />
    <!-- 退机 -->
    <!--<ReturnMachineReceive ref="returnMachineReceive" @refresh="refresh" />-->
  </div>
</template>

<script>
// import ReturnMachineReceive from "@/views/custom/components/returnMachineReceive.vue";
import { mapGetters } from "vuex";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
import {
  addCustomerContractApi,
  deleteCustomerContractApi,
  getCustomerByPageApi,
  getCustomerContractApi,
  getCustomerContractByPageApi,
  getCustomerDeviceListApi,
  getCustomerUserListApi,
  updateCustomerContractApi,
} from "@/api/customer";
import { Message } from "element-ui";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";

export default {
  name: "ContractInquire",
  components: {
    InsureContract: () =>
      import(
        "@/views/custom/editCustom/components/contract/insureContract.vue"
      ),
    SafeguardContract: () =>
      import(
        "@/views/custom/editCustom/components/contract/safeguardContract.vue"
      ),
    BuyRentFinanceContract: () =>
      import(
        "@/views/custom/editCustom/components/contract/buyRentFinanceContract.vue"
      ),
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "合同编号",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isTable: true,
          formatter: (row) =>
            `${row.province ?? ""}${row.city ?? ""}${row.area ?? ""}`,
          isSearch: true,
          searchSlot: "regionPath",
          minWidth: 100,
        },
        // {
        //   dataIndex: "province",
        //   title: "省",
        //   isTable: true,
        //   minWidth: 80,
        // },
        // {
        //   dataIndex: "city",
        //   title: "市",
        //   isTable: true,
        //   minWidth: 80,
        // },
        // {
        //   dataIndex: "area",
        //   title: "区",
        //   isTable: true,
        //   minWidth: 80,
        // },

        // {
        //   dataIndex: "customerSeqId",
        //   title: "客户编号",
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          dataIndex: "contractType",
          title: "合约类型",
          isTable: true,
          formatter: (row) => row.contractType?.label,
          width: 100,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "code",
          title: "合同编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "machineNums",
          title: "机器编号",
          isTable: true,
          formatter: (row) =>
            row.machineNums && Array.isArray(row.machineNums)
              ? row.machineNums.join("、")
              : row.machineNums ?? "",
          minWidth: 140,
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "attachments",
          title: "合同附件",
          isTable: true,
          tableSlot: "attachments",
          width: 120,
        },

        {
          dataIndex: "signName",
          title: "签约人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 100,
        },
        {
          dataIndex: "expectInstallTime",
          title: "安装时间",
          // isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "status",
          title: "合同状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          width: 100,
        },
        {
          dataIndex: "statusList",
          title: "合同状态",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "待确认",
              value: "WAIT_CONFIRM",
            },
            {
              label: "待支付",
              value: "WAIT_PAY",
            },
            {
              label: "支付待审核",
              value: "WAIT_AUDIT",
            },
            {
              label: "支付未通过",
              value: "REJECT",
            },
            {
              label: "待生效",
              value: "WAIT_EFFECT",
            },
            {
              label: "已生效",
              value: "EFFECTED",
            },
            {
              label: "退机中",
              value: "RETURN",
            },
            {
              label: "已失效",
              value: "UNEFFECT",
            },
            {
              label: "已作废",
              value: "CANCEL",
            },
          ],
        },
        {
          dataIndex: "deliveryStatus",
          title: "是否发货",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "待发货",
              value: 0,
            },
            {
              label: "已发货",
              value: 1,
            },
          ],
        },
        {
          dataIndex: "agentName",
          title: "经办人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 100,
        },
        // {
        //   dataIndex: "prepayment",
        //   title: "预付金额",
        //   isTable: true,
        //   width: 100,
        // },
        // {
        //   dataIndex: "status",
        //   title: "支付状态",
        //   isTable: true,
        //   width: 100,
        //   formatter: (row) => row.status?.label,
        // },
        {
          dataIndex: "signTime",
          title: "合约开始时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "expiredDay",
          title: "合约剩余天数",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "30天",
              value: 30,
            },
            {
              label: "15天",
              value: 15,
            },
            {
              label: "7天",
              value: 7,
            },
          ],
        },
        // {
        //   dataIndex: "endTime",
        //   title: "合同截至时间",
        //   isTable: true,
        //   width: 150,
        // },
        // {
        //   dataIndex: "startTime",
        //   title: "计数开始时间",
        //   isTable: true,
        //   width: 150,
        // },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          fixed: "right",
          tableSlot: "actions",
          width: 120,
        },
      ],
      tableData: [],
      title: "",
      showDrawer: false,
      methodType: "add",
      confirmLoading: false,
      form: {},
      formLoading: false,
      formColumns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isForm: true,
          formSpan: 12,
          formSlot: "customerName",
          prop: [
            {
              required: true,
              message: "请选择客户",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isForm: true,
          formSpan: 24,
          valueType: "input",
        },
        {
          dataIndex: "remark",
          title: "摘要",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
        },
        {
          dataIndex: "deviceGroupId",
          title: "设备组名称",
          isForm: true,
          multiple: false,
          formSpan: 8,
          valueType: "select",
          formSlot: "deviceGroupId",
          option: [],
          prop: [
            {
              required: true,
              message: "请选择设备组名称",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "contractType",
          title: "合约类型",
          isForm: true,
          disabled: this.methodType !== "add",
          formSpan: 8,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1200),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择合约类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "paperType",
          title: "计数方式",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6800),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择计数方式",
              trigger: "change",
            },
          ],
        },
      ],
      userList: [],
      showDialog: false,
      customerLocalPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      createColumns: [
        {
          dataIndex: "shopRecruitment",
          title: "店铺名称",
          isTable: true,
        },
        {
          dataIndex: "seqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "legalPersonTel",
          title: "法人电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          tooltip: false,
          align: "left",
          isTable: true,
          tableSlot: "actions",
        },
      ],
      customerQueryParams: {},
      tableData1: [],
      ladderList: [],
      deviceGroupList: [],
      deviceGroupOptions: [],
      signOptions: [],
      contractType: "",
    };
  },

  computed: {
    ...mapGetters({ permits: "permits" }),
    hasDownloadPermission() {
      return this.permits.some(
        (item) => item.permit === "@ums:manage:contract:download"
      );
    },
  },
  watch: {},
  mounted() {
    this.refresh();
    this.operatList();
    this.form.attachments ||= this.$set(this.form, "attachments", []);
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const rangeParams = [
        {
          installStartDate: null,
          installEndDate: null,
          data: parameter.expectInstallTime,
        },
        {
          contractStartDate: null,
          contractEndDate: null,
          data: parameter.signTime,
        },
      ];
      filterParamRange(this, this.queryParam, rangeParams);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.expectInstallTime;
      delete requestParameters.signTime;
      getCustomerContractByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    loadData1(parameter) {
      this.customerQueryParams = filterParam(
        Object.assign({}, this.customerQueryParams, parameter)
      );
      const requestParameters = cloneDeep(this.customerQueryParams);
      getCustomerByPageApi(requestParameters)
        .then((res) => {
          this.tableData1 = res.data.rows;
          this.customerLocalPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTables
            ? (this.$refs.ProTables.listLoading = false)
            : null;
        });
    },
    updateIsForm(columns, keys, isForm) {
      columns.forEach((item) => {
        if (keys.includes(item.dataIndex)) {
          item.isForm = isForm;
        }
      });
    },
    /**
     * 机器发货
     * @param row
     * @param type
     */
    handleMachineDispatch(row, type) {
      this.$refs.dispatchMachine.visible(row, type);
    },
    /**
     * 退机收货
     * @param row
     * @param type
     */
    handleReturnMachineReceive(row, type) {
      this.$refs.returnMachineReceive.visible(row, type);
    },
    handleSelectCustomer() {
      this.showDialog = true;
      this.customerQueryParams = {};
      this.$nextTick(() => {
        this.$refs.ProTables.refresh();
      });
    },
    async sureSelectCustom(row) {
      this.$set(this.form, "customerSeqId", row.seqId);
      this.$set(this.form, "customerName", row.name);
      this.$set(this.form, "customerId", row.id);
      this.showDialog = false;
    },
    // 员工列表处理
    operatList() {
      this.userTureList = [];
      this.userList = [];
      getCustomerUserListApi().then((res) => {
        this.userTureList = res.data;
        res.data.map((item) => {
          this.userList.push({
            value: item.id,
            label: item.name,
          });
        });
      });
    },
    handleView(row, type) {
      if (row) {
        getCustomerContractApi(row.id).then((res) => {
          const formParam = {
            ...res.data,
            customerName: row.customerName,
            customerSeqId: row.customerSeqId,
            customerId: row.customerId,
          };
          if (row.contractType.value === "1201") {
            this.contractType = "1201";
            this.$refs.buyRentFinanceContract.visible(formParam, type);
          }
          if (row.contractType.value === "1202") {
            this.contractType = "1202";
            this.$refs.insureContract.visible(formParam, type);
          }
          if (row.contractType.value === "1265") {
            this.contractType = "1265";
            this.$refs.buyRentFinanceContract.visible(formParam, type);
          }
          if (row.contractType.value === "1230") {
            this.contractType = "1230";
            this.$refs.buyRentFinanceContract.visible(formParam, type);
          }
          if (row.contractType.value === "1220") {
            this.contractType = "1220";
            this.$refs.safeguardContract.visible(formParam, type);
          }
        });
      }
    },
    handleEvent(row, type) {
      this.methodType = type;
      this.ladderList = [];
      this.deviceGroupOptions = [];
      this.deviceGroupList = [];
      this.$set(this.form, "attachments", []);
      this.title = this.titleMap(type);
      if (row) {
        getCustomerContractApi(row.id).then((res) => {
          const formParam = res.data;
          Object.keys(formParam).forEach((key) => {
            formParam[key] = formParam[key].label
              ? formParam[key].value
              : formParam[key];
          });
          this.form = formParam;
          if (formParam.repairMonthlyPrices) {
            this.ladderList = formParam.repairMonthlyPrices || [];
          }
        });
      } else {
        this.form = {
          priceType: "FIXED",
        };
      }
      this.showDrawer = true;
    },

    handleDelete(row) {
      this.$confirm("此操作将会删除该条购买意向记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteCustomerContractApi(row.id).then((res) => {
          this.refresh();
          this.closeDrawer();
          Message.success("购买意向删除成功");
        });
      });
    },
    titleMap(type) {
      const data = {
        add: "新增合约记录",
        edit: "编辑合约记录",
        info: "查看合约记录",
      };
      return data[type];
    },
    handleSubmit() {
      this.$refs.ProForm.handleSubmit();
    },
    formSubmit(val) {
      try {
        if (val.priceType === "LADDER" && !this.ladderList.length) {
          return this.$message.warning("请完善阶梯价格");
        }
        this.confirmLoading = true;
        this.formLoading = true;
        const params = {
          ...this.form,
        };
        if (this.ladderList.length && this.ladderList.length > 0) {
          params.repairMonthlyPrices = this.ladderList;
        }
        const editApi =
          this.methodType === "add"
            ? addCustomerContractApi
            : updateCustomerContractApi;
        editApi(params).then((res) => {
          this.refresh();
          this.closeDrawer();
          Message.success("操作成功");
        });
      } finally {
        this.confirmLoading = false;
        this.formLoading = false;
      }
    },
    handleAddLadder() {
      this.ladderList.push({
        startCount: null,
        endCount: null,
        blackWhitePrice: null,
        colorPrice: null,
        fiveColorPrice: null,
        deviceGroupId: this.form.id,
      });
    },
    handleDeleteLadder(index) {
      this.ladderList.splice(index, 1);
    },
    // 获取客户设备组列表
    getCustomerProductList(customerId) {
      if (!customerId) return;
      getCustomerDeviceListApi(customerId).then((res) => {
        this.deviceGroupList = res.data;
        if (!this.form.color && this.form.deviceGroupId) {
          const findDeviceGroup = this.deviceGroupList.find(
            (item) => item.id === this.form.deviceGroupId
          );
          if (findDeviceGroup) {
            this.$set(this.form, "color", findDeviceGroup.color);
          }
        }
        this.deviceGroupOptions = res.data.map((item) => {
          return {
            label: item.deviceGroup.label + " / " + item.productInfo,
            value: item.id,
          };
        });
      });
    },
    handleLogFileUploadSuccess(result) {
      // if (!this.formParam.attachments) {
      //   this.$set(this.formParam, "attachments", []);
      // }
      this.form.attachments.push(result);
    },
    handleLogFileUploadRemove() {
      this.form.attachments = [];
    },
    handleReviewFile(type) {
      if (type === "naramFile") {
        this.showNaramDialog = true;
      } else {
        this.showLogFileDialog = true;
      }
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    closeDrawer() {
      this.form = {};
      this.showDrawer = false;
    },
    refresh() {
      // this.$refs.ProTable.listLoading = false;
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep.attachments {
  width: 100%;
  white-space: wrap !important;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  a {
    color: #409eff;
    display: flex;
    align-items: center;
    .el-icon-folder-opened {
      font-size: 24px !important;
    }
  }
}
.ladder {
  display: flex;
  flex-direction: column;
  gap: 10px;
  .ladder-list {
    display: flex;
    align-items: center;
    //justify-content: space-between;

    .ladder-item {
      width: 350px;
      display: flex;
      flex-wrap: nowrap;
      gap: 20px;
      margin-right: 20px;
      span {
        text-wrap: nowrap;
      }
    }
  }
}
</style>
