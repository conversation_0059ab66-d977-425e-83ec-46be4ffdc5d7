<!--
 * @Author: wskg
 * @Date: 2025-01-21 14:25:23
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:48
 * @Description: 主机关联的选配件
 -->
<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :local-pagination="localPagination"
      :show-search="false"
      :columns="columns"
      :data="tableData"
      :show-loading="false"
      :show-setting="false"
      :height="350"
    >
      <template #btn>
        <el-button
          v-if="editType !== 'info'"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleAddMeter"
        >
          添加选配件
        </el-button>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            v-if="editType !== 'info'"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row.id)"
          >
            移除
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDialog
      v-model="dialogVisible"
      title="关联选配件"
      width="75%"
      top="1%"
      @ok="handleOk"
      @cancel="dialogVisible = false"
    >
      <ProTable
        ref="PartProTable"
        :local-pagination="partLocalPagination"
        :row-key="(row) => row.machineNum"
        :query-param="queryParam"
        :columns="partColumns"
        :data="partTableData"
        :show-selection="true"
        :height="400"
        @loadData="loadPartsData"
        @handleSelectionChange="handlePartSelectionChange"
      ></ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { getMachinePageApi } from "@/api/store";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "HostParts",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    editType: {
      type: String,
      default: "add",
    },
    machineNum: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "originCode",
          title: "原机器编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "hostType",
          title: "选配件类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,

          minWidth: 100,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 80,
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          minWidth: 80,
        },
        {
          dataIndex: "actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 100,
        },
      ],
      // tableData: [],
      // 选择关联选配件
      dialogVisible: false,
      partLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      queryParam: {},
      partColumns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "originCode",
          title: "原机器编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,

          minWidth: 100,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1100),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 80,
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6600),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 80,
        },
      ],
      partTableData: [],
      selectedPartData: [],
    };
  },
  computed: {
    tableData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    // loadData(parameter) {},
    handleAddMeter() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.PartProTable.refresh();
      });
    },
    handleOk() {
      this.tableData = cloneDeep(this.selectedPartData);
      this.dialogVisible = false;
    },
    handlePartSelectionChange(val) {
      this.selectedPartData = val;
    },
    loadPartsData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const requestParameters = cloneDeep(this.queryParam);
      requestParameters.isBind = false; // 为绑定
      requestParameters.existType = "2008"; // 排除主机类型
      getMachinePageApi(requestParameters)
        .then((res) => {
          this.partTableData = res.data.rows;
          this.partLocalPagination.total = +res.data.total;
          if (this.selectedPartData.length) {
            this.$refs.PartProTable.$refs.ProElTable.clearSelection();
            this.tableData.forEach((row) => {
              this.$refs.PartProTable.$refs.ProElTable.toggleRowSelection(
                row,
                true
              );
            });
          }
        })
        .finally(() => {
          this.$refs.PartProTable &&
            (this.$refs.PartProTable.listLoading = false);
        });
    },
    handleDelete(id) {
      this.$confirm("是否确认移除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.tableData = this.tableData.filter((item) => item.id !== id);
        // this.selectedPartData = this.selectedPartData.filter(
        //   (item) => item.id !== id
        // );
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
