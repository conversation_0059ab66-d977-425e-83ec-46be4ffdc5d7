export default {
  bind(el, binding, vnode, oldVnode) {
    setTimeout(() => {
      if (binding.value) {
        const dragDom = el.parentNode.parentNode;
        el.style.cursor = "se-resize";
        el.onmousedown = (e) => {
          // 鼠标按下，在原来页面上增加透明遮罩，防止部分元素例如iframe监听不到鼠标事件
          const mask = document.createElement("div");
          mask.setAttribute(
            "style",
            "position:fixed;top:0px;bottom:0px;left:0px;right:0px;background:rgba(0,0,0,0)"
          );
          document.body.appendChild(mask);
          // 计算当前元素距离可视区的距离
          const disX = e.clientX - el.offsetLeft;
          const disY = e.clientY - el.offsetTop;
          document.body.onmousemove = function (e) {
            e.preventDefault(); // 移动时禁用默认事件

            // 通过事件委托，计算移动的距离
            const l = e.clientX - disX;
            const h = e.clientY - disY;
            dragDom.style.width = `${l}px`;
            // 判断弹窗高度，防止用于拖动的点移出可视区
            dragDom.style.height = `${
              h > document.body.offsetHeight ? document.body.offsetHeight : h
            }px`;
          };
          document.body.onmouseup = function (e) {
            document.body.removeChild(mask); // 移除mask遮罩
            document.body.onmousemove = null;
            document.body.onmouseup = null;
          };
        };
      }
    }, 400);
  },
};
