/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-21 11:00:01
 * @Description:
 */
import * as gm from "sm-crypto";
import { Decimal } from "decimal.js";
import setting from "@/config/setting.js";
const { title } = setting;
const SM2 = gm.sm2;

//密码令牌加密设置
export function encodeFun(data, second) {
  //偏方：在所有加密前的内容加字符“0”，用以解决无法加密空字符的问题
  data = SM2.doEncrypt(data ? "0" + data : "0", second);
  return data;
}

export const getPageTitle = (pageTitle) => {
  if (pageTitle) {
    return `${pageTitle}-${title}`;
  }
  return `${title}`;
};
export const formatDate = (date) => {
  const d = new Date(date);
  const y = d.getFullYear(); // 年份
  const m = (d.getMonth() + 1).toString().padStart(2, "0"); // 月份
  const r = d.getDate().toString().padStart(2, "0"); // 日子
  const hh = d.getHours().toString().padStart(2, "0"); // 小时
  const mm = d.getMinutes().toString().padStart(2, "0"); // 分钟
  const ss = d.getSeconds().toString().padStart(2, "0"); // 秒
  return `${y}-${m}-${r} ${hh}:${mm}:${ss}`;
};
/**
 * @param {Function|string} formatter
 * @param {string} format 日期时间的格式字符串
 * @returns {Function}
 * @throws {Error}
 */
const _formatNormalize = (formatter, format) => {
  if (typeof formatter === "function") {
    return formatter;
  }
  if (typeof formatter !== "string") {
    throw new Error("formatter must be a function or string");
  }
  switch (formatter) {
    case "date":
      formatter = format.replace(/\s.*$/, "");
      break;
    case "time":
      formatter = format.replace(/^(.*\s)/, "");
      break;
    case "dateTime":
      formatter = format;
      break;
    default:
      throw new Error("formatter string must be 'date','time','dateTime'");
  }
  return (dataInfo) => {
    const { year, month, day, hour, minute, second } = dataInfo;
    return formatter
      .replace("YYYY", year)
      .replace("MM", month)
      .replace("DD", day)
      .replace("HH", hour)
      .replace("mm", minute)
      .replace("ss", second);
  };
};
/**
 * @description 时间格式化
 * @param { Date } date
 * @param { String|Function } formatter - date | time | dateTime | Function
 * @param { String } [format='YYYY-MM-DD HH:mm:ss'] - 格式字符串
 * @param { Boolean } [isPad=true] - 个位是否补0
 * @returns { String } 格式化后的时间字符串
 */
export function formatterDate(
  date,
  formatter,
  format = "YYYY-MM-DD HH:mm:ss",
  isPad = true
) {
  formatter = _formatNormalize(formatter, format);
  const dataInfo = {
    year: date.getFullYear(),
    month: date.getMonth() + 1,
    day: date.getDate(),
    hour: date.getHours(),
    minute: date.getMinutes(),
    second: date.getSeconds(),
  };
  function _pad(prop, len) {
    return prop.toString().padStart(len, "0");
  }
  if (isPad) {
    dataInfo.year = _pad(dataInfo.year, 4);
    dataInfo.month = _pad(dataInfo.month, 2);
    dataInfo.day = _pad(dataInfo.day, 2);
    dataInfo.hour = _pad(dataInfo.hour, 2);
    dataInfo.minute = _pad(dataInfo.minute, 2);
    dataInfo.second = _pad(dataInfo.second, 2);
  }
  return formatter(dataInfo);
}

/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string") {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time);
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), "/");
      }
    }

    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    return value.toString().padStart(2, "0");
  });
  return time_str;
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (("" + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    time = +time;
  }
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return "刚刚";
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + "分钟前";
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + "小时前";
  } else if (diff < 3600 * 24 * 2) {
    return "1天前";
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      "月" +
      d.getDate() +
      "日" +
      d.getHours() +
      "时" +
      d.getMinutes() +
      "分"
    );
  }
}
/**
 * @description 数字金额转换为人民币大写
 * @param num
 * @returns {string}
 */
export function convertToChinese(num) {
  if (!num) return "";
  // 将字符串转换为数字
  const amount = parseFloat(num);
  if (isNaN(amount)) return "";

  // 定义数字对应的中文
  const chineseNum = [
    "零",
    "壹",
    "贰",
    "叁",
    "肆",
    "伍",
    "陆",
    "柒",
    "捌",
    "玖",
  ];
  const chineseUnit = ["", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿"];
  const chineseDecimal = ["角", "分"];

  // 处理整数部分
  const integerPart = Math.floor(amount);
  const decimalPart = Math.round((amount - integerPart) * 100);

  let result = "";

  // 处理整数部分
  if (integerPart > 0) {
    const str = integerPart.toString();
    let zeroCount = 0;
    let unitIndex = str.length - 1;
    for (let i = 0; i < str.length; i++) {
      const digit = parseInt(str[i]);
      const unit = chineseUnit[unitIndex];
      if (digit === 0) {
        zeroCount++;
      } else {
        if (zeroCount > 0) {
          result += chineseNum[0];
          zeroCount = 0;
        }
        result += chineseNum[digit] + unit;
      }
      unitIndex--;
    }
    result += "元";
  }

  // 处理小数部分
  if (decimalPart > 0) {
    const jiao = Math.floor(decimalPart / 10);
    const fen = decimalPart % 10;

    if (jiao > 0) {
      result += chineseNum[jiao] + chineseDecimal[0];
    }
    if (fen > 0) {
      result += chineseNum[fen] + chineseDecimal[1];
    }
  } else {
    result += "整";
  }
  // 处理连续的零
  result = result
    .replace(/零+/g, "零")
    .replace(/零+$/, "")
    .replace(/零元$/, "元整");
  return result;
}

/**
 * @description 获取时间差
 * @param startTime - 开始时间
 * @param endTime - 结束时间
 * @returns {string}
 */
export function getTimeDiff(startTime, endTime) {
  if (!startTime || !endTime) return "";
  const start = new Date(startTime).getTime();
  const end = new Date(endTime).getTime();
  const diffMinutes = Math.floor((end - start) / 1000 / 60);
  if (diffMinutes < 60) {
    return `${diffMinutes}分钟`;
  } else if (diffMinutes < 60 * 24) {
    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    return minutes > 0 ? `${hours}小时${diffMinutes % 60}分钟` : `${hours}小时`;
  } else {
    const days = Math.floor(diffMinutes / (60 * 24));
    const remainingMinutes = diffMinutes % (60 * 24); // 剩余分钟
    const hours = Math.floor(remainingMinutes / 60);
    const minutes = remainingMinutes % 60;
    let result = `${days}天`;
    if (hours > 0) result += `${hours}小时`;
    if (minutes > 0) result += `${minutes}分钟`;
    return result;
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url;
  const search = url.substring(url.lastIndexOf("?") + 1);
  const obj = {};
  const reg = /([^?&=]+)=([^?&=]*)/g;
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1);
    let val = decodeURIComponent($2);
    val = String(val);
    obj[name] = val;
    return rs;
  });
  return obj;
}

/**
 * @param {string} str value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length;
  for (let i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i);
    if (code > 0x7f && code <= 0x7ff) s++;
    else if (code > 0x7ff && code <= 0xffff) s += 2;
    if (code >= 0xdc00 && code <= 0xdfff) i--;
  }
  return s;
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = [];
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i]);
    }
  }
  return newArray;
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return "";
  return cleanArray(
    Object.keys(json).map((key) => {
      if (json[key] === undefined) return "";
      return encodeURIComponent(key) + "=" + encodeURIComponent(json[key]);
    })
  ).join("&");
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split("?")[1]).replace(/\+/g, " ");
  if (!search) {
    return {};
  }
  const obj = {};
  const searchArr = search.split("&");
  searchArr.forEach((v) => {
    const index = v.indexOf("=");
    if (index !== -1) {
      const name = v.substring(0, index);
      const val = v.substring(index + 1, v.length);
      obj[name] = val;
    }
  });
  return obj;
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement("div");
  div.innerHTML = val;
  return div.textContent || div.innerText;
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== "object") {
    target = {};
  }
  if (Array.isArray(source)) {
    return source.slice();
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property];
    if (typeof sourceProperty === "object") {
      target[property] = objectMerge(target[property], sourceProperty);
    } else {
      target[property] = sourceProperty;
    }
  });
  return target;
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === "start") {
    return new Date().getTime() - 3600 * 1000 * 24 * 90;
  } else {
    return new Date(new Date().toDateString());
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function (...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== "object") {
    throw new Error("error arguments", "deepClone");
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === "object") {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr));
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + "";
  const randomNum = parseInt((1 + Math.random()) * 65536) + "";
  return (+(randomNum + timestamp)).toString(32);
}

export const hasClass = function (obj, cls) {
  return obj.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"));
};

export const addClass = function (obj, cls) {
  if (!hasClass(obj, cls)) obj.className += " " + cls;
};

export const removeClass = function (obj, cls) {
  if (hasClass(obj, cls)) {
    const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)");
    obj.className = obj.className.replace(reg, "");
  }
};

export const toggleClass = function (obj, cls) {
  if (hasClass(obj, cls)) {
    removeClass(obj, cls);
  } else {
    addClass(obj, cls);
  }
};

export function getScrollBarWidth() {
  let scrollBarWidth;

  const outer = document.createElement("div");
  outer.className = "el-scrollbar__wrap";
  outer.style.visibility = "hidden";
  outer.style.width = "100px";
  outer.style.position = "absolute";
  outer.style.top = "-9999px";
  document.body.appendChild(outer);

  const widthNoScroll = outer.offsetWidth;
  outer.style.overflow = "scroll";

  const inner = document.createElement("div");
  inner.style.width = "100%";
  outer.appendChild(inner);

  const widthWithScroll = inner.offsetWidth;
  outer.parentNode.removeChild(outer);
  scrollBarWidth = widthNoScroll - widthWithScroll;

  return scrollBarWidth;
}

export function copyValue(oldObj, newObj) {
  for (const key in oldObj) {
    oldObj[key] = newObj ? newObj[key] : null;
  }

  return oldObj;
}

export function getCharCode() {
  const codearr = [];
  for (var i = 0; i < 26; i++) {
    codearr.push(String.fromCharCode(65 + i));
  }
  return codearr;
}
export function randomString(length = 10) {
  const t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
  const a = t.length;
  let n = "";
  for (let i = 0; i < length; i++) n += t.charAt(Math.floor(Math.random() * a));
  return n;
}
export function changeTwoDecimal_f(f) {
  var f_x = parseFloat(f);
  if (isNaN(f_x)) {
    return "0.00";
  }
  var f_x = Math.round(f_x * 100) / 100;
  var s_x = f_x.toString();
  // var pos_decimal = s_x.indexOf('.');
  // if (pos_decimal < 0){
  // 	pos_decimal = s_x.length;
  // 	s_x += '.';
  // }
  // while (s_x.length <= pos_decimal + 2){
  // 	s_x += '0';
  // }
  return s_x;
}

export function loadFile(name) {
  // name为文件所在位置
  const xhr = new XMLHttpRequest();
  const okStatus = document.location.protocol === "file:" ? 0 : 200;
  xhr.open("GET", name, false);
  xhr.overrideMimeType("text/html;charset=utf-8"); //默认为utf-8
  xhr.send(null);
  return xhr.status === okStatus ? xhr.responseText : null;
}
export function treeToArray(tree) {
  if (!tree) {
    return;
  }
  var res = [];
  for (const item of tree) {
    const { children, ...i } = item;
    if (children && children.length) {
      res = res.concat(treeToArray(children));
    }
    if (i.value) {
      res.push(i.value);
    }
  }
  return res;
}

/**
 * @description 过滤空字符串、空数组
 * @param {*} obj
 * @returns
 */
export function filterParam(obj) {
  for (const key in obj) {
    if (typeof obj[key] === "string") {
      obj[key] = obj[key].trim();
    }

    if (
      obj[key] === "" ||
      obj[key] === null ||
      (Array.isArray(obj[key]) && obj[key].length === 0)
    ) {
      delete obj[key];
    }
  }
  return obj;
}

/**
 * @description 过滤请求范围参数
 * @param that
 * @param {Object} queryParam
 * @param {Array} data
 */
export function filterParamRange(that, queryParam, data) {
  if (!Array.isArray(data)) {
    throw new Error("data must be array");
  }
  data.forEach((el) => {
    if (Array.isArray(el.data)) {
      const keys = Object.keys(el).filter((key) => key !== "data");
      if (keys.length >= 2) {
        that.$set(queryParam, keys[0], el.data[0] !== null ? el.data[0] : null);
        that.$set(queryParam, keys[1], el.data[1] !== null ? el.data[1] : null);
      }
    } else if (el.data === null) {
      // 当el.data为null,如果queryParam中的key与el中的key存在相同时，就将其从queryParam中删除
      for (const key in el) {
        if (queryParam[key]) {
          delete queryParam[key];
        }
      }
    }
  });
}

/**
 * @description 将form表单对象参数转换 key: {label: '', value: ''} => key: value
 * @param formParams params
 * @param whiteList 白名单
 * @returns {*}
 */
export function transformFormParams(formParams, whiteList = []) {
  for (const key in formParams) {
    if (Object.prototype.hasOwnProperty.call(formParams, key)) {
      // 白名单
      if (
        Array.isArray(whiteList) &&
        whiteList.length > 0 &&
        whiteList.includes(key)
      ) {
        continue;
      }
      const value = formParams[key];
      if (
        typeof value === "object" &&
        value !== null &&
        Object.keys(value).length === 0
      ) {
        delete formParams[key];
      } else {
        formParams[key] = value?.label ? value.value : value;
      }
    }
  }
  return formParams;
}

export function exportFile(data, fileName, _this) {
  if (!data) return;
  const blob = new Blob([data], {
    type: "application/vnd.ms-excel;charset=utf-8",
  });
  const url = window.URL.createObjectURL(blob);
  const aLink = document.createElement("a");
  aLink.style.display = "none";
  aLink.href = url;
  // 替换文件名中的特殊字符
  const refileName = fileName.replace(/[\/:]/g, "_");
  aLink.setAttribute("download", refileName);
  document.body.appendChild(aLink);
  aLink.click();
  document.body.removeChild(aLink);
  window.URL.revokeObjectURL(url);
}
export function exportZip(data, fileName, _this) {
  if (!data) return;
  const blob = new Blob([data], {
    type: "application/zip",
  });
  // let url = window.URL.createObjectURL(data);
  const url = window.URL.createObjectURL(blob);
  const aLink = document.createElement("a");
  aLink.style.display = "none";
  aLink.target = "hrefTemplate";
  aLink.href = url;
  // 替换文件名中的特殊字符
  const refileName = fileName.replace(/[\/:]/g, "_");
  aLink.setAttribute("download", refileName);
  document.body.appendChild(aLink);
  aLink.click();
  document.body.removeChild(aLink);
  window.URL.revokeObjectURL(url);
}

export function exportFiles(data, fileName, _this) {
  if (!data) return;
  const blob = new Blob([data], {
    type: "application/octet-stream",
  });
  const url = window.URL.createObjectURL(blob);
  const aLink = document.createElement("a");
  aLink.style.display = "none";
  aLink.href = url;
  // 替换文件名中的特殊字符
  const refileName = fileName.replace(/[\/:]/g, "_");
  aLink.setAttribute("download", refileName);
  document.body.appendChild(aLink);
  aLink.click();
  document.body.removeChild(aLink);
  window.URL.revokeObjectURL(url);
}

/**
 * 递归整合用户列表数据
 */
export function integrationUserListData(data) {
  return data.map((itemA) => {
    if (itemA.users) {
      // 有角色的情况下
      let usersList = [];
      usersList = itemA.users.map((itemB) => ({
        // 将角色修改成树形结构
        ...itemB,
        name: itemB.name,
        id: itemA.id + itemB.id, // 唯一Id
        userId: itemB.id,
        isUser: true,
        parentId: itemA.id,
      }));
      if (!itemA.children) {
        itemA.children = [];
      }
      itemA.children.push(...usersList);
    }
    if (itemA.children) {
      integrationUserListData(itemA.children);
    }
    return itemA;
  });
}

//两个数组的 n*n 组合成新数组
export function combineArrays(arr1, arr2) {
  const result = [];
  for (let i = 0; i < arr1.length; i++) {
    for (let j = 0; j < arr2.length; j++) {
      result.push({ a1: arr1[i], a2: arr2[j] });
    }
  }
  return result;
}

/**
 * @description 除
 * @param amount
 * @param numbers
 * @returns {number}
 */

export function divideAmount(amount, ...numbers) {
  amount = Number(amount) || 0;
  let result = new Decimal(amount);

  numbers.forEach((number) => {
    if (number !== 0) {
      // 避免除以零的情况
      result = result.div(number);
    } else {
      console.error("Division by zero is not allowed");
      return 0; // 或者你可以选择抛出错误
    }
  });

  return result.toNumber();
}

/**
 * @description 乘
 * @param {Number} amount
 * @param numbers
 * @returns {number}
 */
export function mulAmount(amount, ...numbers) {
  const safeAmount = Number(amount ?? 0);
  let result = new Decimal(safeAmount);

  numbers.forEach((number) => {
    const safeNum = Number(number ?? 0);
    result = result.mul(safeNum);
  });

  return result.toNumber();
}
/**
 * @description 加
 * @param {Number} amount
 * @param numbers
 * @returns {number}
 */
export function addAmount(amount, ...numbers) {
  const safeAmount = Number(amount ?? 0);
  let result = new Decimal(safeAmount);
  numbers.forEach((number) => {
    const safeNum = Number(number ?? 0);
    result = result.add(safeNum);
  });

  return result.toNumber();
}

/**
 * 相减
 * @param amount
 * @param numbers
 * @returns {number}
 */
export function subtractAmount(amount, ...numbers) {
  // amount = Number(amount) || 0;
  const safeAmount = Number(amount ?? 0);
  let result = new Decimal(safeAmount);
  // let result = new Decimal(amount);
  numbers.forEach((number) => {
    const safeNum = Number(number ?? 0);
    result = result.minus(safeNum);
  });

  return result.toNumber();
}
