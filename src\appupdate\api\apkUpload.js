/*
 * @Author: AI Assistant
 * @Date: 2025-01-29
 * @Description: APK文件专用上传API - 保留文件扩展名
 */

import { get } from "@/utils/request";
import cos from "@/utils/cos";
import { uuid } from "vue-uuid";

let Bucket, Region;

/**
 * 获取COS配置信息
 */
export const getBucket = () => get("/cos/bucket");

/**
 * 获取COS上传凭证
 */
export const getCredentials = () => get("/cos/credentials");

/**
 * ZIP文件专用上传函数 - 保留文件扩展名
 * @param {File} file ZIP文件对象
 * @returns {Promise} 上传结果
 */
export const uploadApkFile = async (file) => {
  if (!Bucket || !Region) {
    const res = await getBucket();
    Bucket = res.data.bucket;
    Region = res.data.region;
    // 不使用后端返回的Prefix，直接指定app文件夹
  }

  return new Promise((resolve, reject) => {
    const key = uuid.v1();

    // 获取文件扩展名
    const fileExtension = getFileExtension(file.name);

    // 为ZIP文件构建专用的COS Key，直接上传到app文件夹
    let cosKey;
    // 暂时只支持ZIP文件，APK文件类型已注释
    if (fileExtension === 'zip') {
      cosKey = `app/${key}.${fileExtension}`;
    }
    // APK文件类型暂时注释
    // else if (fileExtension === 'apk') {
    //   cosKey = `app/${key}.${fileExtension}`;
    // }
    else {
      // 如果不是支持的文件类型，仍然保留原扩展名，但也放在app文件夹
      cosKey = fileExtension ? `app/${key}.${fileExtension}` : `app/${key}`;
    }

    console.log('文件上传信息:', {
      originalName: file.name,
      fileExtension: fileExtension,
      cosKey: cosKey,
      fileSize: file.size,
      fileType: file.type
    });
    
    cos.uploadFile(
      {
        Bucket,
        Region,
        Key: cosKey,
        Body: file,
        SliceSize: 1024 * 1024 * 5, // 5MB分片
        AsyncLimit: 5,
        onProgress: (progressData) => {
          console.log('文件上传进度:', Math.round(progressData.percent * 100) + '%');
        },
      },
      (err, data) => {
        if (err) {
          console.error('文件上传失败:', err);
          reject(err);
        } else {
          console.log('文件上传成功:', data);

          const result = {
            name: file.name,
            key: cosKey,
            type: file.type,
            url: "https://" + data.Location,
            originalExtension: fileExtension,
            preservedExtension: true
          };

          console.log('文件上传结果:', result);
          resolve(result);
        }
      }
    );
  });
};

/**
 * 获取文件扩展名
 * @param {string} filename 文件名
 * @returns {string} 扩展名（不包含点号，小写）
 */
function getFileExtension(filename) {
  if (!filename || typeof filename !== 'string') {
    return '';
  }
  
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === filename.length - 1) {
    return '';
  }
  
  return filename.substring(lastDotIndex + 1).toLowerCase();
}

/**
 * 验证是否为支持的文件类型（暂时只支持ZIP）
 * @param {File} file 文件对象
 * @returns {boolean} 是否为支持的文件类型
 */
export const isApkFile = (file) => {
  if (!file) return false;

  // 检查文件扩展名 - 暂时只支持ZIP
  const extension = getFileExtension(file.name);
  if (extension !== 'zip') return false;

  // APK文件类型暂时注释
  // if (!['apk', 'zip'].includes(extension)) return false;

  // 检查MIME类型（可选，因为不同浏览器可能不一致）
  const validMimeTypes = [
    // 'application/vnd.android.package-archive', // APK - 暂时注释
    'application/zip',                         // ZIP
    'application/x-zip-compressed',            // ZIP (alternative)
    'application/octet-stream'                 // 通用二进制文件
  ];

  return validMimeTypes.includes(file.type) || file.type === '';
};

/**
 * 获取文件类型描述
 * @param {File} file 文件对象
 * @returns {string} 文件类型描述
 */
export const getFileTypeDescription = (file) => {
  if (!file) return '未知文件';

  const extension = getFileExtension(file.name);
  switch (extension) {
    // case 'apk':  // APK类型暂时注释
    //   return 'Android安装包';
    case 'zip':
      return 'ZIP压缩包';
    default:
      return '未知文件类型';
  }
};

/**
 * 文件上传前验证（暂时只支持ZIP）
 * @param {File} file 文件对象
 * @param {number} maxSize 最大文件大小（字节），默认100MB
 * @returns {Object} 验证结果 {valid: boolean, message: string}
 */
export const validateApkFile = (file, maxSize = 100 * 1024 * 1024) => {
  if (!file) {
    return { valid: false, message: '请选择文件' };
  }

  // 验证文件扩展名 - 暂时只支持ZIP
  if (!isApkFile(file)) {
    return { valid: false, message: '请选择有效的ZIP文件' };
  }

  // 验证文件大小
  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
    const fileSizeMB = Math.round(file.size / (1024 * 1024));
    const fileType = getFileTypeDescription(file);
    return {
      valid: false,
      message: `${fileType}过大，当前大小：${fileSizeMB}MB，最大允许：${maxSizeMB}MB`
    };
  }

  // 验证文件不为空
  if (file.size === 0) {
    const fileType = getFileTypeDescription(file);
    return { valid: false, message: `${fileType}不能为空` };
  }

  const fileType = getFileTypeDescription(file);
  return { valid: true, message: `${fileType}验证通过` };
};

/**
 * 格式化文件大小显示
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小字符串
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
