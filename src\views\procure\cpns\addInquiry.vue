<!--
 * @Author: yangzhong
 * @Date: 2023-12-06 16:59:39
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:11:07
 * @Description: 新增询价单
-->
<template>
  <ProDrawer
    :value="showDrawer"
    :title="drawerTitle"
    size="98%"
    :destroy-on-close="true"
    :no-footer="false"
    :confirm-button-disabled="confirmLoading"
    confirm-text="确认询价单"
    @ok="handleDrawerOk"
    @cancel="handleCloseDrawer"
  >
    <div class="add-inquiry-container">
      <!-- 物品表单 -->
      <div
        style="
          width: 68%;
          float: left;
          border: 1px solid #ddd;
          margin-bottom: 20px;
        "
      >
        <ProTable
          ref="articleTable"
          row-key="id"
          :data="articleData"
          :columns="[...articleColumns1, ...addArticleColumns]"
          :height="400"
          show-index
          :show-search="false"
          :show-loading="false"
          @rowClick="handelArticleRowClick"
        >
          <template #btn>
            <div v-if="inquiryData.length == 0">
              选择物品：
              <el-button
                type="success"
                class="add-btn"
                size="mini"
                icon="el-icon-plus"
                @click="handleAddArticle"
              >
                选择
              </el-button>
              <span style="font-size: 12px; margin-left: 10px">
                请确认好物品后再选择供应商，选择供应商后不可再修改物品
              </span>
            </div>
          </template>
          <template #num="{ row }">
            <el-input-number
              v-if="inquiryData.length == 0"
              v-model="row.num"
              style="width: 100px"
              :controls="false"
            ></el-input-number>
            <div v-if="inquiryData.length > 0">{{ row.num }}</div>
          </template>
          <template #action="{ row }">
            <div v-if="inquiryData.length == 0" class="fixed-width">
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                @click="handleDeleteArticle(row)"
              >
                删除
              </el-button>
            </div>
            <div v-if="inquiryData.length > 0">/</div>
          </template>
        </ProTable>
      </div>
      <!-- 适用机型 -->
      <div style="width: 30%; float: right; border: 1px solid #ddd">
        <ProTable
          ref="modelTable"
          row-key="id"
          :data="modelData"
          :columns="modelColumns"
          :height="400"
          :local-pagination="modelLocalPagination"
          show-index
          :show-search="false"
          show-loading
          @loadData="loadModelData"
        >
          <template #btn>
            <div>物品适用机型</div>
          </template>
        </ProTable>
      </div>

      <!-- 询价单 -->
      <div style="clear: both; border: 1px solid #ddd">
        <ProTable
          ref="inquiryTable"
          row-key="id"
          :data="inquiryData"
          :columns="inquiryColumns"
          :height="400"
          show-index
          :show-search="false"
          :show-loading="false"
        >
          <template #btn>
            <div v-if="articleData.length > 0">
              询价记录：
              <el-button
                type="success"
                class="add-btn"
                size="mini"
                icon="el-icon-plus"
                @click="handleAddSupplier"
              >
                选择供应商
              </el-button>
            </div>
          </template>
          <template #action="{ row }">
            <div class="fixed-width">
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                @click="handleDeleteInquiry(row)"
              >
                删除
              </el-button>
            </div>
          </template>
          <template #price="{ row }">
            <el-input-number
              v-model="row.price"
              style="width: 100px"
              :controls="false"
            ></el-input-number>
          </template>
          <template #expiresTime="{ row }">
            <el-date-picker
              v-model="row.expiresTime"
              style="width: 150px"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="date"
              placeholder="选择日期"
            ></el-date-picker>
          </template>
          <template #comment="{ row }">
            <el-input
              v-model="row.comment"
              type="textarea"
              :controls="false"
            ></el-input>
          </template>
        </ProTable>
      </div>

      <!-- 物品选择弹窗 -->
      <ProDialog
        :value="articleDialog"
        title="选择物品"
        width="90%"
        :confirm-loading="articleDialogLoading"
        top="10px"
        @ok="handleArticleDialogOk"
        @cancel="closeArticleDialog"
      >
        <ProTable
          ref="chooseArticleTable"
          row-key="id"
          :data="chooseArticleData"
          :columns="articleColumns"
          :height="400"
          :local-pagination="articleLocalPagination"
          :query-param="articleQueryParam"
          show-selection
          :reserve-selection="true"
          @loadData="loadArticleData"
          @handleSelectionChange="handleSelectionArticle"
        ></ProTable>
      </ProDialog>

      <!-- 选择供应商 -->
      <ProDialog
        :value="supplierDialog"
        title="选择供应商"
        width="1200px"
        :confirm-loading="supplierDialogLoading"
        confirm-text="确认选择"
        top="10px"
        @ok="handleSupplierDialogOk"
        @cancel="closeSupplierDialog"
      >
        <ProTable
          ref="chooseSupplierTable"
          row-key="id"
          :data="supplierData"
          :columns="chooseSupplierColumns"
          :height="400"
          :local-pagination="chooseSupplierLocalPagination"
          show-index
          show-search
          show-loading
          show-selection
          :reserve-selection="true"
          @loadData="loadSupplierData"
          @handleSelectionChange="handleSelectionSupplier"
        ></ProTable>
      </ProDialog>
    </div>
  </ProDrawer>
</template>

<script>
import ProDrawer from "@/components/ProDrawer/index.vue";
import ProTable from "@/components/ProTable/index.vue";
import ProDialog from "@/components/ProDialog/index.vue";
import {
  articleInquiryOrderPageApi,
  getByArticleIdApi,
  addInquiryApi,
} from "@/api/procure";
import { manufacturerListApi } from "@/api/manufacturer";
import { Message, MessageBox } from "element-ui";
import { isEmpty, cloneDeep } from "lodash";
import { filterParam } from "@/utils";

export default {
  name: "AddInquiry",
  components: { ProDrawer, ProTable, ProDialog },
  data() {
    return {
      showDrawer: false,
      drawerTitle: "新增询价单",
      // 物品表单相关
      articleColumns: [
        {
          dataIndex: "code",
          title: "物品编号",
          width: 180,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          width: 180,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "OEM中文名称",
          width: 120,
          isTable: true,
        },
        {
          dataIndex: "manufacturerGoodsCode",
          title: "制造商物品编号",
          width: 180,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          width: 120,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerName",
          title: "制造商名称",
          width: 120,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "id",
          title: "制造渠道",
          width: 120,
          isTable: true,
          formatter: (row) => row.manufacturerChannel.label,
        },
        {
          dataIndex: "type",
          title: "物品小类",
          width: 120,
          isTable: true,
          formatter: (row) => row.type.label,
        },
        {
          dataIndex: "colorBox",
          title: "颜色/纸盒",
          width: 120,
          isTable: true,
        },
        {
          dataIndex: "inCarrier",
          title: "含载体",
          isTable: true,
        },
        {
          dataIndex: "inChip",
          title: "含芯片",
          width: 120,
          isTable: true,
        },
        {
          dataIndex: "suttle",
          title: "净重",
          width: 120,
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          width: 120,
          isTable: true,
        },
      ],
      articleColumns1: [
        {
          dataIndex: "code",
          title: "物品编号",
          width: 120,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          width: 120,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "OEM中文名称",
          width: 120,
          isTable: true,
        },
        {
          dataIndex: "manufacturerGoodsCode",
          title: "制造商物品编号",
          width: 120,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          width: 120,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerName",
          title: "制造商名称",
          width: 120,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "id",
          title: "制造渠道",
          width: 120,
          isTable: true,
          formatter: (row) => row.manufacturerChannel.label,
        },
        // {
        //   dataIndex: "type",
        //   title: "物品小类",
        //   width: 120,
        //   isTable: true,
        //   formatter: (row) => row.type.label,
        // },
        // {
        //   dataIndex: "colorBox",
        //   title: "颜色/纸盒",
        //   width: 120,
        //   isTable: true,
        // },
        // {
        //   dataIndex: "inCarrier",
        //   title: "含载体",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "inChip",
        //   title: "含芯片",
        //   width: 120,
        //   isTable: true,
        // },
        // {
        //   dataIndex: "suttle",
        //   title: "净重",
        //   width: 120,
        //   isTable: true,
        // },
        {
          dataIndex: "unit",
          title: "单位",
          width: 120,
          isTable: true,
        },
      ],
      addArticleColumns: [
        {
          dataIndex: "num",
          title: "需求数量",
          isTable: true,
          tableSlot: "num",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
        },
      ],
      articleData: [],
      articleLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      articleQueryParam: {},
      chooseArticleData: [],
      articleDialog: false,
      articleDialogLoading: false,
      articleSelection: [],
      // 机型
      modelColumns: [
        {
          dataIndex: "code",
          title: "物品编码",
          isTable: true,
          width: 200,

          // formatter: (row) => row.storageArticle.code,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          width: 150,
          // formatter: (row) =>
          //   row.productTreeList ? row.productTreeList[0].brand : "",
        },
        {
          dataIndex: "tree",
          title: "产品树",
          width: 150,
          isTable: true,
          // formatter: (row) =>
          //   row.productTreeList ? row.productTreeList[0].tree : "",
        },
        {
          dataIndex: "serial",
          title: "系列",
          width: 150,
          isTable: true,
          // formatter: (row) =>
          //   row.productTreeList ? row.productTreeList[0].serial : "",
        },
        {
          dataIndex: "machine",
          title: "机型",
          width: 150,
          isTable: true,
          // formatter: (row) =>
          //   row.productTreeList ? row.productTreeList[0].machine : "",
        },
      ],
      modelData: [],
      modelLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      // 询价单
      inquiryData: [],
      inquiryColumns: [
        {
          dataIndex: "manufacturerCode",
          title: "供应商编号",
          isTable: true,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          width: 250,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          sortable: true,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          tableSlot: "price",
        },
        {
          dataIndex: "expiresTime",
          title: "有效期截至",
          isTable: true,
          tableSlot: "expiresTime",
        },
        {
          dataIndex: "comment",
          title: "备注",
          isTable: true,
          tableSlot: "comment",
          width: 200,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
        },
      ],
      // 供应商
      supplierDialog: false,
      supplierDialogLoading: false,
      supplierData: [],
      chooseSupplierData: [],
      chooseSupplierColumns: [
        {
          dataIndex: "name",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "code",
          title: "供应商编号",
          isTable: true,
        },
      ],
      chooseSupplierLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      activeArticleId: null, // 当前查询机型的物品id
      confirmLoading: false,
    };
  },
  created() {},
  methods: {
    refresh() {
      this.inquiryData = [];
      this.articleData = [];
      this.articleSelection = [];
      this.supplierData = [];
      this.chooseArticleData = [];
      this.modelData = [];
      this.chooseSupplierData = [];
    },
    async handleDrawerOk() {
      if (this.inquiryData.length === 0) {
        Message.error("请添加询价单");
        return;
      }
      try {
        this.inquiryData.forEach((item, index) => {
          if (!item.price || !item.expiresTime) {
            throw new Error("第" + (index + 1) + "条询价单未完善");
          }
        });
        this.$confirm("是否确认新增询价单?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          this.confirmLoading = true;
          const result = await addInquiryApi({
            inquiryParamList: this.inquiryData.map((item) => {
              delete item.id;
              item.num = this.articleData.find(
                (v) => v.id === item.articleId
              ).num;
              return item;
            }),
          });
          if (result.code === 200) {
            this.$emit("refresh");
            this.handleCloseDrawer();
            Message.success("添加成功");
            this.inquiryData = [];
            this.articleData = [];
            this.modelData = [];
            this.supplierData = [];
            this.confirmLoading = false;
          }
        });
      } catch (err) {
        Message.error(err.message);
      } finally {
        this.confirmLoading = false;
      }
    },
    handleCloseDrawer() {
      this.showDrawer = false;
    },
    show() {
      this.showDrawer = true;
      this.$nextTick(() => {
        this.$refs.modelTable && (this.$refs.modelTable.listLoading = false);
      });
      this.refresh();
    },
    handleAddArticle() {
      this.articleLocalPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.articleQueryParam = {};
      this.articleDialog = true;
      this.articleSelection = cloneDeep(this.articleData);
      this.$nextTick(() => {
        this.$refs.chooseArticleTable.$refs.ProElTable.clearSelection();
        this.articleData.map((row) => {
          // const target = this.articleSelection.find(
          //   (part) => part.id === item.id
          // );
          this.$refs.chooseArticleTable.$refs.ProElTable.toggleRowSelection(
            row
          );
        });
        this.$refs.chooseArticleTable.refresh();
      });
    },
    handleDeleteArticle(row) {
      MessageBox.confirm("确定移除该物品吗？", "移除物品", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        const index = this.articleData.findIndex((item) => item.id === row.id);
        index !== -1 && this.articleData.splice(index, 1);
        if (row.id === this.activeArticleId) {
          this.activeArticleId = null;
          this.modelData = [];
        }
      });
    },
    handelArticleRowClick(row) {
      this.activeArticleId = row.id;
      this.modelTableRefresh();
    },
    async loadArticleData(params) {
      this.articleQueryParam = filterParam(
        Object.assign({}, this.articleQueryParam, params)
      );
      const requestParameters = cloneDeep(this.articleQueryParam);
      try {
        const result = await articleInquiryOrderPageApi(requestParameters);
        if (result.code === 200) {
          this.chooseArticleData = result.data.rows;
          this.articleLocalPagination = {
            pageNumber: params.pageNumber,
            pageSize: params.pageSize,
            total: +result.data.total,
          };
        }
      } catch (err) {
        Message.error(err.message);
      } finally {
        this.$refs.chooseArticleTable &&
          (this.$refs.chooseArticleTable.listLoading = false);
      }
    },
    //物品框确认
    handleArticleDialogOk() {
      this.articleSelection = this.articleSelection.map((item) => {
        return {
          ...item,
          num: 0,
        };
      });
      this.articleData = cloneDeep(this.articleSelection); //物品表格数据
      this.closeArticleDialog();
      this.activeArticleId = this.articleData[0].id;
      this.modelTableRefresh();
    },
    closeArticleDialog() {
      this.articleDialog = false;
    },
    //多选物品赋值
    handleSelectionArticle(val) {
      // const difference = val.filter(
      //   (v) => !this.articleSelection.some((i) => i.id === v.id)
      // );
      // this.articleSelection = [...this.articleSelection, ...difference];
      this.articleSelection = cloneDeep(val);
    },
    async loadModelData() {
      try {
        if (!this.activeArticleId) return;
        this.modelData = [];
        const result = await getByArticleIdApi({
          articleIdList: [this.activeArticleId],
        });
        if (result.code === 200) {
          result.data.map((item) => {
            if (item.productTreeList && item.productTreeList.length > 0) {
              item.productTreeList.map((row) => {
                this.modelData.push({
                  code: item.storageArticle.code,
                  ...row,
                });
              });
            } else {
              this.modelData.push({
                code: item.storageArticle.code,
              });
            }
          });
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.modelTable && (this.$refs.modelTable.listLoading = false);
      }
    },
    modelTableRefresh() {
      this.$refs.modelTable.refresh();
    },
    handleAddSupplier() {
      this.supplierDialog = true;
      // this.articleSelection = this.articleData
      this.$nextTick(() => {
        this.$refs.chooseSupplierTable.refresh();

        this.$refs.chooseSupplierTable.$refs.ProElTable.clearSelection();
        this.chooseSupplierData.map((row) => {
          this.$refs.chooseSupplierTable.$refs.ProElTable.toggleRowSelection(
            row
          );
        });
      });
    },
    // 选择供应商
    handleSupplierDialogOk() {
      this.supplierData = cloneDeep(this.chooseArticleData);
      this.inquiryData = this.computedInquiry();
      this.closeSupplierDialog();
    },
    closeSupplierDialog() {
      this.supplierDialog = false;
    },
    async loadSupplierData(params) {
      try {
        const result = await manufacturerListApi(params);
        if (result.code === 200) {
          this.supplierData = result.data.rows;
          this.chooseSupplierLocalPagination = {
            pageNumber: params.pageNumber,
            pageSize: params.pageSize,
            total: +result.data.total,
          };
        }
      } catch (err) {
        Message.error(err.message);
      } finally {
        this.$refs.chooseSupplierTable &&
          (this.$refs.chooseSupplierTable.listLoading = false);
      }
    },
    //多选供应商赋值
    handleSelectionSupplier(val) {
      this.chooseSupplierData = cloneDeep(val);
      // val.forEach((item) => {
      //   const target = this.chooseSupplierData.find(
      //     (part) => part.id === item.id
      //   );
      //   if (!target) {
      //     this.chooseSupplierData.push(item);
      //   }
      // });
    },
    handleDeleteInquiry(row) {
      MessageBox.confirm("确定删除该询价单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        this.inquiryData = this.inquiryData.filter(
          (item) => item.id !== row.id
        );
        this.chooseSupplierData = this.chooseSupplierData.filter(
          (item) => item.id !== row.manufacturerId
        );

        this.$message.success("删除成功");
      });
    },
    computedInquiry() {
      const articleList = this.articleData.map((item) => {
        return {
          partId: item.partId,
          articleCode: item.code,
          articleId: item.id,
          articleName: item.name,
          numberOem: item.numberOem,
          manufacturerChannel: item.manufacturerChannel.label,
          num: item.num,
        };
      });
      const supplierList = this.chooseSupplierData.map((item) => {
        return {
          manufacturerId: item.id,
          manufacturerName: item.name,
          manufacturerCode: item.code,
        };
      });
      return Array.from(supplierList, (y) =>
        Array.from(articleList, (x) => {
          const id = `${
            "article" + x.articleId + "supplier" + y.manufacturerId
          }`;
          const target = this.inquiryData.find((item) => item.id === id);
          return {
            ...x,
            ...y,
            id,

            price: target ? target.price : 0,
            expiresTime: target ? target.expiresTime : "",
            comment: target ? target.comment : "",
          };
        })
      ).flatMap((item) => item);
    },
  },
};
</script>

<style lang="scss" scoped>
.add-inquiry-container {
  padding-bottom: 50px;
}
</style>
