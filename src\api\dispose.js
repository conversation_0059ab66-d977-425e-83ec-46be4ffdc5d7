/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-02 13:43:26
 * @Description:
 */
/*
 * @Description:
 * @Autor: shh
 * @Date: 2022-04-17 19:20:26
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-02 13:43:26
 */
import axios from "axios";
import { get, post, put, del, down } from "@/utils/request";
import data from "@/utils/targetdisplay";

// ==================产品树====================
export const productListApi = (data) => get(`/product-tree/all-tree`, data);
export const productAddApi = (data) => post(`/product-tree`, data);
export const productEditApi = (data) => put(`/product-tree`, data);
export const productDelApi = (id) => del(`/product-tree/${id}`);
export const productAllApi = (data) =>
  get(`/product-tree/all-tree`, {
    pageNumber: 1,
    pageSize: 10000,
  });
export const getFullProductTreeApi = (id) =>
  get(`/product-tree/getFullProductTree/${id}`);

export const productSerialApi = (data) =>
  get(`/product-tree/serial-tree`, data);
export const productThirdApi = (data) => get(`/product-tree/third-tree`, data);

export const modelListApi = (data) => get(`/product-tree/model-page`, data);
export const bomModelListApi = (data) =>
  get(`/product-tree/bom/model-page`, data);
export const SeriesListApi = (data) =>
  post(`/product-tree/brand-serial-list`, data);

// ==================设备====================
export const deviceListApi = (data) => post(`/product-device/page`, data);
export const deviceAddApi = (data) => post(`/product-device`, data);
export const deviceEditApi = (data) => put(`/product-device`, data);
export const deviceDelApi = (id) => del(`/product-device/${id}`);
export const deviceProductTreeApi = (id) =>
  get(`/product-device/product-tree/${id}`);
export const deviceProductPathApi = (id) =>
  get(`/product-device/product-tree-full/${id}`);

// ==================零件====================
export const partListApi = (data) => post(`/product-part/page`, data);
export const partAddApi = (data) => post(`/product-part`, data);
export const partEditApi = (data) => put(`/product-part`, data);
export const partDelApi = (id) => del(`/product-part/${id}`);
export const partProductTreeApi = (id) =>
  get(`/product-part/product-tree/${id}`);
export const partProductPathApi = (id) =>
  get(`/product-part/product-tree-full/${id}`);
export const partPMPageApi = (data) => post(`/product-part-bom/pm-page`, data);
export const partPMDeleteApi = (id) => del(`/product-part/delete-pm/${id}`);
export const getPartByModelApi = (data) => post(`/product-part/page`, data);
export const updatePartPMApi = (data) => put(`/product-part/update-pm`, data);

// ==================选配件====================
export const accessoryListApi = (data) => post(`/product-accessory/page`, data);
export const accessoryAddApi = (data) => post(`/product-accessory`, data);
export const accessoryEditApi = (data) => put(`/product-accessory`, data);
export const accessoryDelApi = (id) => del(`/product-accessory/${id}`);
export const accessoryProductTreeApi = (id) =>
  get(`/product-accessory/product-tree/${id}`);
export const accessoryProductPathApi = (id) =>
  get(`/product-accessory/product-tree-full/${id}`);

// ==================通用关联====================
export const getAssociationPageApi = (data) =>
  get(`/product-part-association/page`, data);
export const addAssociationApi = (data) =>
  post(`/product-part-association`, data);
export const updateAssociationApi = (data) =>
  put(`/product-part-association`, data);
export const deleteAssociationApi = (id) =>
  del(`/product-part-association/${id}`);

export const uploadApi = `/file/upload`; // 文件上传

export const ExportApi = (id) => down(`/report/download/${id}`); // 详情

export const getAssociationInfoApi = (id) =>
  get(`/product-part-association/detail/${id}`);
// ==================BOM====================
export const bomListApi = (data) => post(`/product-part-bom/page`, data);
export const bomAddApi = (data) => post(`/product-part-bom`, data);
export const bomEditApi = (data) => put(`/product-part-bom`, data);
export const bomDelApi = (id) => del(`/product-part-bom/${id}`);

export const pmInvListApi = (data) => post(`/pm-inventory/page`, data);

export const pmPartListApi = (data) =>
  get(`/pm-inventory/queryPartInventory`, data);

export const pmSkuListApi = (data) =>
  get(`/pm-inventory/querySkuByInvSkuId`, data);

// ==================  积分获取配置  ====================
// 获取积分属性配置
export const getIntegralConfigApi = (data) => get(`/points-config/info`, data);
// 修改
export const editIntegralConfigApi = (data) => post(`/points-config`, data);
