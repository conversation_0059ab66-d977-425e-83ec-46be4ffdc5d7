<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-30 16:04:30
 * @Description: 报损报溢 - 耗材
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      :row-key="(row) => row.id"
      :query-param="queryParam"
      show-pagination
      sticky
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>报损物品数量：{{ totalData.withoutCount || 0 }}</div>
          <div>报损数量：{{ totalData.withoutNum || 0 }}</div>
          <div>报损金额：{{ totalData.withoutAmount || 0 }}</div>
          <div>报溢物品数量：{{ totalData.overflowCount || 0 }}</div>
          <div>报溢数量：{{ totalData.overflowNum || 0 }}</div>
          <div>报溢金额：{{ totalData.overflowAmount || 0 }}</div>
          <div>损溢金额：{{ totalData.totalAmount || 0 }}</div>
        </div>
      </template>
      <template #actions="{ row }">
        <span class="fixed-width">
          <el-button icon="el-icon-view" @click="handleLosses(row, 'info')">
            查看
          </el-button>
          <el-button
            v-if="
              !(
                row.discrepancyStatus?.value === 'COMPLETE' ||
                row.discrepancyStatus?.value === 'NORMAL' ||
                row.discrepancyStatus?.value === 'REJECT'
              )
            "
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleLosses(row, 'audit')"
          >
            审核
          </el-button>
          <el-button
            v-else
            icon="el-icon-warning-outline"
            @click="handleLosses(row, 'info')"
          >
            损溢详情
          </el-button>
        </span>
      </template>
    </ProTable>
    <ProDrawer
      :value="checkInventory"
      :title="checkTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :no-footer="true"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="editForm"
        :form-param="editForm"
        :form-list="columns"
        :confirm-loading="editFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="editType"
      >
        <template #type>
          <div>{{ editForm.type?.label }}</div>
        </template>
        <template #createdBy>
          <div>{{ editForm.createdBy }}</div>
        </template>
        <template #inventoryDetailList>
          <ProTable
            ref="inventoryProTable"
            :columns="inventoryColumns"
            :data="inventoryTableData"
            :local-pagination="detailLocalPagination"
            :row-key="(row) => row.id"
            :query-param="inventoryQueryParam"
            :show-loading="false"
            :show-setting="false"
            :show-search="false"
            show-pagination
            :height="500"
            sticky
            @loadData="loadInventoryData"
          >
            <template #picture="{ row }">
              <el-image
                v-if="row.picture"
                style="width: 100px; height: 100px"
                :src="row.picture"
                :preview-src-list="[row.picture]"
              />
              <div v-else>暂无</div>
            </template>
            <template #batchInfo="{ row }">
              <el-popover trigger="hover" placement="top">
                <div class="content-popover">
                  <div v-for="item in row.batchInfo" :key="item.batchCode">
                    <p>批次号：{{ item.batcheCode }}</p>
                    <p>价格：{{ item.price }}</p>
                    <p>数量：{{ item.num }}</p>
                  </div>
                </div>
                <div slot="reference" class="name-wrapper">
                  <div>{{ batchCodeText(row.batchInfo) }}</div>
                </div>
              </el-popover>
            </template>
            <template #stockNum="{ row }">
              <el-input
                v-model="row.stockNum"
                type="number"
                min="0"
                style="width: 70%"
                :disabled="editType === 'info' || editType === 'audit'"
                @change="handleStockNumChange(row)"
              />
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <ProDrawer
      :value="checkLosses"
      :title="lossesTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :no-footer="editType === 'info'"
      :no-confirm-footer="true"
      @cancel="closeLossesDrawer"
    >
      <ProForm
        ref="editForm"
        :form-param="lossesForm"
        :form-list="columns"
        :confirm-loading="editFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="editType"
      >
        <template #type>
          <div>{{ lossesForm.type?.label }}</div>
        </template>
        <template #createdBy>
          <div>{{ lossesForm.createdBy }}</div>
        </template>
        <template #lossesDetailList>
          <p class="tit-box m-b-12">报损报溢</p>
          <ProTable
            ref="lossesProTable"
            :columns="lossesColumns"
            :data="lossesTableData"
            :local-pagination="lossesLocalPagination"
            :row-key="(row) => row.id"
            show-pagination
            :height="500"
            sticky
            @loadData="loadLossesData"
          >
            <template #btn>
              <div class="title-box-right">
                <div>报损物品数量：{{ lossesTotalData.withoutCount || 0 }}</div>
                <div>报损数量：{{ lossesTotalData.withoutNum || 0 }}</div>
                <div>报损金额：{{ lossesTotalData.withoutAmount || 0 }}</div>
                <div>
                  报溢物品数量：{{ lossesTotalData.overflowCount || 0 }}
                </div>
                <div>报溢数量：{{ lossesTotalData.overflowNum || 0 }}</div>
                <div>报溢金额：{{ lossesTotalData.overflowAmount || 0 }}</div>
                <div>损溢金额：{{ lossesTotalData.totalAmount || 0 }}</div>
              </div>
            </template>
            <template #picture="{ row }">
              <el-image
                v-if="
                  row.imageFiles &&
                  Array.isArray(row.imageFiles) &&
                  row.imageFiles.length > 0
                "
                style="width: 100px; height: 100px"
                :src="row.imageFiles[0].url"
                :preview-src-list="[row.imageFiles[0].url]"
              />
              <div v-else>暂无</div>
            </template>
            <template #batchInfo="{ row }">
              <el-popover trigger="hover" placement="top">
                <div class="content-popover">
                  <div v-for="item in row.batchInfo" :key="item.batchCode">
                    <p>批次号：{{ item.batcheCode }}</p>
                    <p>价格：{{ item.price }}</p>
                    <p>数量：{{ item.num }}</p>
                  </div>
                </div>
                <div slot="reference" class="name-wrapper">
                  <div>{{ batchCodeText(row.batchInfo) }}</div>
                </div>
              </el-popover>
            </template>
            <template #lossesStatus="{ row }">
              <el-link
                :underline="false"
                :type="
                  row.discrepancyStatus?.value === 'WITHOUT'
                    ? 'danger'
                    : 'success'
                "
                >{{ row.discrepancyStatus?.label }}</el-link
              >
            </template>
            <template #stockNum="{ row }">
              <el-input
                v-model="row.stockNum"
                type="number"
                min="0"
                style="width: 70%"
                :disabled="editType === 'info' || editType === 'audit'"
              />
            </template>
          </ProTable>
        </template>
      </ProForm>
      <template #footer>
        <div v-if="editType === 'audit'" class="footer">
          <el-button type="danger" @click="handleAudit('REJECT', 'error')">
            驳回
          </el-button>
          <el-button type="primary" @click="handleAudit('COMPLETE', 'success')">
            审核通过
          </el-button>
          <el-button @click="closeLossesDrawer"> 取消 </el-button>
        </div>
      </template>
    </ProDrawer>
  </div>
</template>

<script>
import {
  checkWarehouseInventoryApi,
  warehouseListApi,
  lossAndOverflowPageApi,
  lossAndOverflowApproveApi,
  lossAndOverflowListApi,
  lossAndOverflowTotalApi,
} from "@/api/store";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
export default {
  name: "ConsumableInventory",
  data() {
    return {
      editType: "info",
      queryParam: {
        isDiscrepanc: 1,
      },
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      tableData: [],
      totalData: {},
      columns: [
        {
          title: "盘点单号",
          dataIndex: "code",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        // {
        //   title: "盘点单号",
        //   dataIndex: "code",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 24,
        // },
        {
          title: "归属仓库",
          dataIndex: "warehouseName",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          isForm: true,
          disabled: false,
          formSpan: 6,
          width: 120,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择归属仓库",
              trigger: "change",
            },
          ],
        },
        {
          title: "仓库类型",
          dataIndex: "type",
          isTable: true,
          formatter: (row) => row.type?.label,
          isSearch: true,
          valueType: "select",
          clearable: true,
          isForm: true,
          formSlot: "type",
          disabled: false,
          formSpan: 6,
          option: [],
          optionMth: () => dictTreeByCodeApi(1300),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          title: "库存数量",
          dataIndex: "inventoryNum",
          isTable: true,
        },
        {
          title: "库存金额",
          dataIndex: "inventoryAmount",
          isTable: true,
        },
        {
          title: "盘点数量",
          dataIndex: "stockNum",
          isTable: true,
        },
        {
          title: "盘点金额",
          dataIndex: "stockAmount",
          isTable: true,
        },
        {
          title: "盘点人",
          dataIndex: "createdBy",
          isTable: true,
          isSearch: true,
          align: "center",
          valueType: "input",
          isForm: true,
          disabled: false,
          formSlot: "createdBy",
          formSpan: 6,
          formatter: (row) => row.createdBy?.name,
        },
        {
          title: "盘点日期",
          dataIndex: "createdAt",
          isForm: true,
          formSpan: 6,
          clearable: true,
          valueType: "date-picker",
          pickerType: "datetime",
          disabled: true,
        },
        {
          title: "审核人",
          dataIndex: "auditorByName",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          title: "审核时间",
          dataIndex: "auditAt",
          isForm: true,
          valueType: "date-picker",
          pickerType: "datetime",
          formSpan: 6,
        },
        {
          title: "盘点日期",
          dataIndex: "createdAt",
          isTable: true,
          isSearch: true,
          clearable: true,
          width: 150,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
        },
        {
          title: "耗材盘点",
          dataIndex: "inventoryDetail",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "inventoryDetailList",
        },
        {
          title: "报损报溢",
          dataIndex: "lossesDetailList",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "lossesDetailList",
        },
        {
          title: "审核人",
          dataIndex: "auditorByName",
          isTable: true,
          align: "center",
        },
        {
          title: "审核时间",
          dataIndex: "auditAt",
          isTable: true,
          width: 150,
        },
        {
          title: "状态",
          dataIndex: "discrepancyStatus",
          isTable: true,
          formatter: (row) => row.discrepancyStatus?.label,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [
            {
              label: "报损报溢",
              value: "ABNORMAL",
            },
            {
              label: "待审核",
              value: "WAIT_APPROVE",
            },
            {
              label: "驳回",
              value: "REJECT",
            },
            {
              label: "处理完成",
              value: "COMPLETE",
            },
          ],
        },
        {
          title: "操作",
          dataIndex: "action",
          isTable: true,
          tooltip: false,
          width: 220,
          tableSlot: "actions",
        },
      ],
      checkInventory: false,
      checkTitle: "查看 —— ",
      confirmLoading: false,
      editForm: {},
      editFormLoading: false,
      inventoryQueryParam: {},
      inventoryColumns: [
        {
          title: "储位",
          dataIndex: "location",
          isTable: true,
        },
        {
          title: "物品编号",
          dataIndex: "articleCode",
          isTable: true,
          width: 180,
        },
        {
          title: "图片",
          dataIndex: "picture",
          isTable: true,
          tableSlot: "picture",
        },
        {
          title: "物品名称",
          dataIndex: "articleName",
          isTable: true,
          width: 250,
        },
        {
          title: "OEM编号",
          dataIndex: "numberOem",
          isTable: true,
          width: 150,
        },
        {
          title: "批次号",
          dataIndex: "batchInfo",
          isTable: true,
          width: 250,
          tableSlot: "batchInfo",
        },
        {
          title: "库存数量",
          dataIndex: "inventoryNum",
          isTable: true,
          align: "center",
        },
        {
          title: "库存盘点",
          dataIndex: "stockNum",
          isTable: true,
          width: 130,
          tableSlot: "stockNum",
        },
      ],
      inventoryTableData: [],
      detailLocalPagination: {
        pageSize: 99,
        pageNumber: 1,
        total: 0,
      },
      cacheInventoryData: [],
      lossVisible: false,
      checkLosses: false,
      lossesTitle: "",
      lossesQueryParam: {},
      lossesForm: {},
      lossesLocalPagination: {
        pageSize: 99,
        pageNumber: 1,
        total: 0,
      },
      lossesColumns: [
        {
          title: "物品编号",
          dataIndex: "articleCode",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          title: "物品名称",
          dataIndex: "articleName",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          title: "OEM编号",
          dataIndex: "numberOem",
          isTable: true,
          width: 150,
          isSearch: true,
          valueType: "input",
        },
        {
          title: "批次号",
          dataIndex: "batchCode",
          isTable: true,
          // tableSlot: "batchInfo",
          width: 120,
        },
        {
          title: "图片",
          dataIndex: "picture",
          isTable: true,
          tableSlot: "picture",
        },
        {
          title: "单位",
          dataIndex: "unit",
          isTable: true,
        },
        {
          title: "单价",
          dataIndex: "price",
          isTable: true,
        },
        // {
        //   title: "金额",
        //   dataIndex: "inventoryAmount",
        //   isTable: true,
        // },
        {
          title: "库存数量",
          dataIndex: "inventoryNum",
          isTable: true,
        },
        {
          title: "库存盘点",
          dataIndex: "stockNum",
          isTable: true,
        },
        {
          title: "损益值",
          dataIndex: "discrepancyNum",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          title: "损益金额",
          dataIndex: "discrepancyAmount",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          title: "损溢状态",
          dataIndex: "discrepancyStatus",
          isTable: true,
          // formatter: (row) => row.discrepancyStatus?.label,
          tableSlot: "lossesStatus",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "报损",
              value: "WITHOUT",
            },
            {
              label: "报溢",
              value: "OVERFLOW",
            },
          ],
        },
      ],
      lossesTableData: [],
      lossesTotalData: {},
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startTime: null,
          endTime: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      lossAndOverflowListApi({ ...requestParameters, stockType: 0 })
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getLossesTotalData(requestParameters);
    },
    loadInventoryData(parameter) {
      this.inventoryQueryParam = filterParam(
        Object.assign({}, this.inventoryQueryParam, parameter)
      );
      const requestParameters = cloneDeep(this.inventoryQueryParam);
      const params = {
        ...requestParameters,
        stockType: 0,
        warehouseId: this.editForm.warehouseId
          ? this.editForm.warehouseId
          : this.editForm.warehouseName,
        takeStockId: this.editForm.id || null,
      };
      checkWarehouseInventoryApi(params)
        .then((res) => {
          this.inventoryTableData = res.data.rows;
          this.detailLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.inventoryProTable
            ? (this.$refs.inventoryProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    loadLossesData(parameter) {
      this.lossesQueryParam = filterParam(
        Object.assign({}, this.lossesQueryParam, parameter)
      );
      const searchRange = [
        {
          minNum: null,
          maxNum: null,
          data: parameter.discrepancyNum,
        },
        {
          minAmount: null,
          maxAmount: null,
          data: parameter.discrepancyAmount,
        },
      ];
      filterParamRange(this, this.lossesQueryParam, searchRange);
      const requestParameters = cloneDeep(this.lossesQueryParam);
      ["discrepancyNum", "discrepancyAmount"].forEach((item) => {
        delete requestParameters[item];
      });
      lossAndOverflowPageApi({
        ...requestParameters,
        takeStockCode: this.lossesForm.code,
      })
        .then((res) => {
          this.lossesTableData = res.data.rows;
          this.lossesLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.lossesProTable
            ? (this.$refs.lossesProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getLossesTotalData({
        ...requestParameters,
        takeStockCode: this.lossesForm.code,
      });
    },
    getLossesTotalData(params) {
      lossAndOverflowTotalApi(params).then((res) => {
        if (params.takeStockCode) {
          this.lossesTotalData = res.data;
        } else {
          this.totalData = res.data;
        }
      });
    },
    handleView(row) {
      this.reset();
      this.editType = "info";
      this.checkTitle = "查看 — " + row.code;
      this.columns.forEach((item, index) => {
        if (item.dataIndex === "inventoryDetail") {
          this.columns[index].isForm = true;
        }
        if (item.dataIndex === "lossesDetailList") {
          this.columns[index].isForm = false;
        }
      });
      this.editForm = cloneDeep(row);
      this.editForm.createdBy = row.createdBy?.name;
      this.checkInventory = true;
      this.$nextTick(() => {
        this.$refs.inventoryProTable.refresh();
      });
    },
    batchCodeText(info) {
      return (
        info &&
        info.reduce((acc, cur) => {
          return acc + (acc ? "、" : "") + cur.batcheCode;
        }, "")
      );
    },
    // 报损报溢
    handleLosses(row, type) {
      this.lossesForm = cloneDeep(row);
      this.editType = type;
      this.lossesTitle = this.getLossesTitle(type);
      this.lossesForm.createdBy = row.createdBy?.name;
      this.columns.forEach((item, index) => {
        if (item.dataIndex === "lossesDetailList") {
          this.columns[index].isForm = true;
        }
        if (item.dataIndex === "inventoryDetail") {
          this.columns[index].isForm = false;
        }
      });
      this.checkLosses = true;
      this.$nextTick(() => {
        this.$refs.lossesProTable.refresh();
      });
    },
    getLossesTitle(type) {
      switch (type) {
        case "info":
          return "盘点单号：" + this.lossesForm.code + " — 详情";
        case "audit":
          return "盘点单号：" + this.lossesForm.code + " — 审核";
        default:
      }
    },
    handleAudit(status, type) {
      const confirmText = status === "COMPLETE" ? "通过" : "驳回";
      this.$confirm(`此操作将${confirmText}对该内容的审核, 是否继续?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: type,
      }).then(() => {
        const params = {
          id: this.lossesForm.id,
          status,
        };
        lossAndOverflowApproveApi(params).then((res) => {
          this.closeLossesDrawer();
          this.$refs.ProTable.refresh();
        });
      });
    },
    closeDrawer() {
      this.reset();
      this.checkInventory = false;
    },
    closeLossesDrawer() {
      this.checkLosses = false;
    },
    reset() {
      this.editForm = {};
      this.cacheInventoryData = [];
      this.inventoryTableData = [];
    },
  },
};
</script>

<style scoped lang="scss">
.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
::v-deep.fixed-width {
  .el-button {
    i {
      margin-right: 2px;
    }
  }
}

.content-popover {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer {
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style>
