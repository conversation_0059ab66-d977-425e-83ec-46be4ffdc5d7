<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Android 应用版本管理</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Element UI CSS CDN -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6;
        }
        .el-card {
            border-radius: 0.75rem; /* rounded-xl */
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-md */
        }
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }
        .el-button-group .el-button {
            border-radius: 0.5rem; /* rounded-lg */
            margin-right: 0.5rem;
        }
        .el-button-group .el-button:last-child {
            margin-right: 0;
        }
        .el-dialog {
            border-radius: 0.75rem; /* rounded-xl */
        }
        .el-upload-dragger {
            border-radius: 0.5rem; /* rounded-lg */
        }
        .el-dropdown-link {
            cursor: pointer;
            color: #409EFF;
            font-size: 14px;
            margin-left: 10px;
        }
        .el-icon-arrow-down {
            font-size: 12px;
        }
    </style>
</head>
<body class="p-4 sm:p-6 md:p-8">

    <div id="app" class="max-w-7xl mx-auto">
        <el-card class="mb-6">
            <div slot="header" class="card-header">
                <span class="text-2xl font-semibold text-gray-800">📱 应用版本管理</span>
                <div>
                    <el-button type="primary" @click="showPublishDialog" class="rounded-lg px-4 py-2">
                        + 发布新版本
                    </el-button>
                    <el-dropdown @command="handleEmergencyCommand">
                        <el-button type="danger" class="rounded-lg px-4 py-2 ml-2">
                            紧急操作<i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="rollback">版本回退</el-dropdown-item>
                            <el-dropdown-item command="pause">暂停所有更新</el-dropdown-item>
                            <el-dropdown-item command="resume">恢复更新推送</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>

            <el-table :data="versionList" v-loading="loading" class="w-full rounded-lg overflow-hidden">
                <el-table-column prop="versionName" label="版本名称" width="180">
                    <template slot-scope="scope">
                        <span>{{ scope.row.versionName }}</span>
                        <el-tag v-if="currentActiveVersion && scope.row.id === currentActiveVersion.id"
                                type="success" size="mini" class="ml-2 rounded-md">
                            当前使用
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="versionCode" label="版本号" width="100"></el-table-column>
                <el-table-column prop="createdAt" label="发布时间" width="180">
                    <template slot-scope="scope">
                        {{ formatDate(scope.row.createdAt) }}
                    </template>
                </el-table-column>
                <el-table-column prop="updateLog" label="更新说明" min-width="200"></el-table-column> <!-- New column for updateLog -->
                <el-table-column label="更新类型" width="120">
                    <template slot-scope="scope">
                        <el-tag :type="getUpdateTypeColor(scope.row)" class="rounded-md">
                            {{ getUpdateTypeText(scope.row) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="downloadCount" label="下载次数" width="100"></el-table-column>
                <el-table-column label="状态" width="80">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.isActive ? 'success' : 'info'" class="rounded-md">
                            {{ scope.row.isActive ? '启用' : '禁用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="280">
                    <template slot-scope="scope">
                        <el-button-group>
                            <el-button size="mini" @click="editVersion(scope.row)" class="rounded-lg">
                                编辑
                            </el-button>
                            <el-button size="mini" type="warning"
                                       @click="toggleForce(scope.row)" class="rounded-lg">
                                {{ scope.row.adminForce ? '取消强制' : '设为强制' }}
                            </el-button>
                            <el-button size="mini" type="danger"
                                       @click="deleteVersion(scope.row)" class="rounded-lg">
                                删除
                            </el-button>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <!-- 发布新版本对话框 -->
        <el-dialog
            title="发布新版本"
            :visible.sync="publishDialogVisible"
            width="50%"
            :before-close="handleClosePublishDialog"
            class="rounded-xl"
        >
            <el-form :model="publishForm" ref="publishForm" label-width="120px">
                <el-form-item label="版本名称" prop="versionName" :rules="[{ required: true, message: '请输入版本名称', trigger: 'blur' }]">
                    <el-input v-model="publishForm.versionName" placeholder="例如: 1.3.0" class="rounded-md"></el-input>
                </el-form-item>
                <el-form-item label="版本号" prop="versionCode" :rules="[{ required: true, message: '请输入版本号', trigger: 'blur' }, { type: 'number', message: '版本号必须为数字', trigger: 'blur' }]">
                    <el-input v-model.number="publishForm.versionCode" placeholder="例如: 13" class="rounded-md"></el-input>
                </el-form-item>
                <el-form-item label="更新说明" prop="updateLog">
                    <el-input type="textarea" v-model="publishForm.updateLog" placeholder="请输入更新说明" rows="4" class="rounded-md"></el-input>
                </el-form-item>
                <el-form-item label="是否强制更新" prop="isForce">
                    <el-switch v-model="publishForm.isForce" active-text="是" inactive-text="否"></el-switch>
                </el-form-item>
                <el-form-item label="是否启用" prop="isActive">
                    <el-switch v-model="publishForm.isActive" active-text="是" inactive-text="否"></el-switch>
                </el-form-item>
                <el-form-item label="APK文件" prop="apkFile">
                    <el-upload
                        class="upload-demo"
                        drag
                        action="#"
                        :auto-upload="false"
                        :on-change="handleApkFileChange"
                        :file-list="fileList"
                        :limit="1"
                        accept=".apk"
                    >
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                        <div slot="tip" class="el-upload__tip text-gray-500 text-sm">只能上传apk文件</div>
                    </el-upload>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClosePublishDialog" class="rounded-lg">取 消</el-button>
                <el-button type="primary" @click="submitPublishForm" class="rounded-lg">发 布</el-button>
            </span>
        </el-dialog>

        <!-- 编辑版本对话框 -->
        <el-dialog
            title="编辑版本信息"
            :visible.sync="editDialogVisible"
            width="50%"
            :before-close="handleCloseEditDialog"
            class="rounded-xl"
        >
            <el-form :model="editForm" ref="editForm" label-width="120px">
                <el-form-item label="版本名称" prop="versionName" :rules="[{ required: true, message: '请输入版本名称', trigger: 'blur' }]">
                    <el-input v-model="editForm.versionName" placeholder="例如: 1.3.0" class="rounded-md" :disabled="true"></el-input>
                </el-form-item>
                <el-form-item label="版本号" prop="versionCode" :rules="[{ required: true, message: '请输入版本号', trigger: 'blur' }, { type: 'number', message: '版本号必须为数字', trigger: 'blur' }]">
                    <el-input v-model.number="editForm.versionCode" placeholder="例如: 13" class="rounded-md" :disabled="true"></el-input>
                </el-form-item>
                <el-form-item label="更新说明" prop="updateLog">
                    <el-input type="textarea" v-model="editForm.updateLog" placeholder="请输入更新说明" rows="4" class="rounded-md"></el-input>
                </el-form-item>
                <el-form-item label="是否强制更新" prop="isForce">
                    <el-switch v-model="editForm.isForce" active-text="是" inactive-text="否"></el-switch>
                </el-form-item>
                <el-form-item label="是否启用" prop="isActive">
                    <el-switch v-model="editForm.isActive" active-text="是" inactive-text="否"></el-switch>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleCloseEditDialog" class="rounded-lg">取 消</el-button>
                <el-button type="primary" @click="submitEditForm" class="rounded-lg">保 存</el-button>
            </span>
        </el-dialog>

        <!-- 版本回退对话框 -->
        <el-dialog
            title="版本回退"
            :visible.sync="rollbackDialogVisible"
            width="40%"
            :before-close="handleCloseRollbackDialog"
            class="rounded-xl"
        >
            <el-form :model="rollbackForm" ref="rollbackForm" label-width="120px">
                <el-form-item label="目标版本" prop="targetVersionName" :rules="[{ required: true, message: '请选择目标版本', trigger: 'change' }]">
                    <el-select v-model="rollbackForm.targetVersionName" placeholder="请选择要回退到的版本" class="w-full rounded-md">
                        <el-option
                            v-for="item in versionList"
                            :key="item.id"
                            :label="item.versionName"
                            :value="item.versionName"
                            :disabled="item.adminForce"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleCloseRollbackDialog" class="rounded-lg">取 消</el-button>
                <el-button type="danger" @click="submitRollback" class="rounded-lg">确认回退</el-button>
            </span>
        </el-dialog>
    </div>

    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <!-- Element UI JS CDN -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    versionList: [],
                    loading: false,
                    publishDialogVisible: false,
                    editDialogVisible: false,
                    rollbackDialogVisible: false,
                    publishForm: {
                        versionName: '',
                        versionCode: null,
                        updateLog: '',
                        isForce: false,
                        isActive: true,
                        apkFile: null
                    },
                    editForm: {
                        id: null,
                        versionName: '',
                        versionCode: null,
                        updateLog: '',
                        isForce: false,
                        isActive: true,
                        adminForce: false
                    },
                    rollbackForm: {
                        targetVersionName: ''
                    },
                    fileList: []
                };
            },
            computed: {
                // Computed property to determine the current active version
                currentActiveVersion() {
                    // First, check for any admin-forced active versions
                    const adminForcedVersion = this.versionList.find(v => v.adminForce && v.isActive);
                    if (adminForcedVersion) {
                        return adminForcedVersion;
                    }

                    // If no admin-forced, find the latest active version by versionCode
                    const activeVersions = this.versionList.filter(v => v.isActive);
                    if (activeVersions.length === 0) {
                        return null;
                    }
                    // Sort by versionCode descending to get the latest
                    const sortedActiveVersions = [...activeVersions].sort((a, b) => b.versionCode - a.versionCode);
                    return sortedActiveVersions[0];
                }
            },
            mounted() {
                this.loadVersionList();
            },
            methods: {
                // Simulates loading version list from an API
                async loadVersionList() {
                    this.loading = true;
                    try {
                        // Mock API call delay
                        await new Promise(resolve => setTimeout(resolve, 500));

                        // Mock data
                        this.versionList = [
                            {
                                id: 1,
                                versionName: '1.0.0',
                                versionCode: 10,
                                createdAt: '2023-01-15T10:00:00',
                                updateLog: '初始版本发布',
                                isForce: false,
                                adminForce: false,
                                isActive: true,
                                downloadCount: 1500
                            },
                            {
                                id: 2,
                                versionName: '1.1.0',
                                versionCode: 11,
                                createdAt: '2023-03-20T14:30:00',
                                updateLog: '1. 修复已知问题\\n2. 优化用户体验',
                                isForce: false,
                                adminForce: false,
                                isActive: true,
                                downloadCount: 2300
                            },
                            {
                                id: 3,
                                versionName: '1.2.0',
                                versionCode: 12,
                                createdAt: '2023-05-10T09:15:00',
                                updateLog: '1. 新增功能A\\n2. 性能提升',
                                isForce: true,
                                adminForce: false,
                                isActive: true,
                                downloadCount: 800
                            },
                            {
                                id: 4,
                                versionName: '1.2.1',
                                versionCode: 13,
                                createdAt: '2023-05-25T11:00:00',
                                updateLog: '紧急修复崩溃问题',
                                isForce: true,
                                adminForce: true, // Admin forced update
                                isActive: true,
                                downloadCount: 500
                            },
                            {
                                id: 5,
                                versionName: '1.3.0',
                                versionCode: 14,
                                createdAt: '2023-07-01T16:45:00',
                                updateLog: '1. 界面改版\\n2. 适配Android 14',
                                isForce: false,
                                adminForce: false,
                                isActive: false, // Disabled version
                                downloadCount: 100
                            }
                        ].sort((a, b) => b.versionCode - a.versionCode); // Sort by version code descending
                        this.$message.success('版本列表加载成功');
                    } catch (error) {
                        this.$message.error('加载版本列表失败');
                        console.error('Error loading version list:', error);
                    } finally {
                        this.loading = false;
                    }
                },

                // Shows the publish new version dialog
                showPublishDialog() {
                    this.publishDialogVisible = true;
                },

                // Handles closing the publish dialog, resets form
                handleClosePublishDialog() {
                    this.publishDialogVisible = false;
                    this.$refs.publishForm.resetFields(); // Reset form fields
                    this.fileList = []; // Clear file list
                    this.publishForm.apkFile = null; // Clear apkFile
                },

                // Handles successful publish (mock)
                handlePublishSuccess() {
                    this.$message.success('版本发布成功');
                    this.handleClosePublishDialog();
                    this.loadVersionList(); // Reload list to show new version
                },

                // Toggles force update status for a version (mock)
                async toggleForce(row) {
                    const action = row.adminForce ? '取消强制更新' : '设为强制更新';
                    this.$confirm(`确认要${action}吗？`, '确认', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(async () => {
                        try {
                            // Mock API call delay
                            await new Promise(resolve => setTimeout(resolve, 300));
                            row.adminForce = !row.adminForce; // Update locally
                            this.$message.success(`${action}成功`);
                        } catch (error) {
                            this.$message.error(`${action}失败`);
                            console.error('Error toggling force update:', error);
                        }
                    }).catch(() => {
                        this.$message.info('已取消操作');
                    });
                },

                // Deletes a version (mock)
                async deleteVersion(row) {
                    this.$confirm(`确认要删除版本 ${row.versionName} 吗？`, '确认', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(async () => {
                        try {
                            // Mock API call delay
                            await new Promise(resolve => setTimeout(resolve, 300));
                            this.versionList = this.versionList.filter(v => v.id !== row.id); // Remove from list
                            this.$message.success(`版本 ${row.versionName} 删除成功`);
                        } catch (error) {
                            this.$message.error(`删除版本 ${row.versionName} 失败`);
                            console.error('Error deleting version:', error);
                        }
                    }).catch(() => {
                        this.$message.info('已取消删除');
                    });
                },

                // Edits a version (populates form and shows dialog)
                editVersion(row) {
                    // Deep copy the row data to editForm to avoid direct mutation
                    this.editForm = JSON.parse(JSON.stringify(row));
                    this.editDialogVisible = true;
                },

                // Handles closing the edit dialog, resets form
                handleCloseEditDialog() {
                    this.editDialogVisible = false;
                    this.$refs.editForm.resetFields();
                },

                // Submits the edit form (mock)
                submitEditForm() {
                    this.$refs.editForm.validate(async (valid) => {
                        if (valid) {
                            this.loading = true;
                            try {
                                // Mock API call delay
                                await new Promise(resolve => setTimeout(resolve, 1000));

                                // Find and update the version in the list
                                const index = this.versionList.findIndex(v => v.id === this.editForm.id);
                                if (index !== -1) {
                                    // Update only the editable fields
                                    this.versionList[index].updateLog = this.editForm.updateLog;
                                    this.versionList[index].isForce = this.editForm.isForce;
                                    this.versionList[index].isActive = this.editForm.isActive;
                                    // Note: versionName and versionCode are disabled for editing in this prototype
                                }

                                this.$message.success('版本信息更新成功');
                                this.handleCloseEditDialog();
                                // No need to reload versionList completely, as changes are local
                            } catch (error) {
                                this.$message.error('版本信息更新失败');
                                console.error('Error updating version:', error);
                            } finally {
                                this.loading = false;
                            }
                        } else {
                            this.$message.error('请检查表单填写');
                            return false;
                        }
                    });
                },

                // Handles commands from the emergency operations dropdown
                handleEmergencyCommand(command) {
                    if (command === 'rollback') {
                        this.showRollbackDialog();
                    } else if (command === 'pause') {
                        this.handleEmergencyAction('pause');
                    } else if (command === 'resume') {
                        this.handleEmergencyAction('resume');
                    }
                },

                // Shows the rollback dialog
                showRollbackDialog() {
                    this.rollbackDialogVisible = true;
                    this.rollbackForm.targetVersionName = ''; // Reset selection
                },

                // Handles closing the rollback dialog, resets form
                handleCloseRollbackDialog() {
                    this.rollbackDialogVisible = false;
                    this.$refs.rollbackForm.resetFields();
                },

                // Submits the rollback action (mock)
                submitRollback() {
                    this.$refs.rollbackForm.validate(async (valid) => {
                        if (valid) {
                            const targetVersionName = this.rollbackForm.targetVersionName;
                            this.handleEmergencyAction('rollback', { targetVersion: targetVersionName });
                            this.handleCloseRollbackDialog();
                        } else {
                            this.$message.error('请选择目标版本');
                            return false;
                        }
                    });
                },

                // Handles emergency actions (rollback, pause, resume)
                async handleEmergencyAction(actionType, params = {}) {
                    let confirmMessage = '';
                    let successMessage = '';
                    let errorMessage = '';

                    if (actionType === 'rollback') {
                        const targetVersionName = params.targetVersion;
                        confirmMessage = `确认要回退到版本 ${targetVersionName} 吗？回退后，该版本将设为管理员强制更新，其他版本将取消管理员强制。`;
                        successMessage = `已成功回退到版本 ${targetVersionName}。`;
                        errorMessage = `回退到版本 ${targetVersionName} 失败。`;
                    } else if (actionType === 'pause') {
                        confirmMessage = '确认要暂停所有应用更新推送吗？暂停后，客户端将不再收到更新通知。';
                        successMessage = '所有应用更新已暂停。';
                        errorMessage = '暂停更新失败。';
                    } else if (actionType === 'resume') {
                        confirmMessage = '确认要恢复应用更新推送吗？';
                        successMessage = '应用更新已恢复。';
                        errorMessage = '恢复更新失败。';
                    } else {
                        this.$message.error('不支持的紧急操作类型。');
                        return;
                    }

                    this.$confirm(confirmMessage, '紧急操作确认', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(async () => {
                        this.loading = true;
                        try {
                            // Mock API call for emergency action
                            await new Promise(resolve => setTimeout(resolve, 800)); // Simulate API call

                            if (actionType === 'rollback') {
                                const targetVersionName = params.targetVersion;
                                this.versionList.forEach(version => {
                                    if (version.versionName === targetVersionName) {
                                        version.adminForce = true;
                                        version.isActive = true; // Ensure the target version is active
                                    } else {
                                        version.adminForce = false;
                                    }
                                });
                            } else if (actionType === 'pause') {
                                this.versionList.forEach(version => {
                                    version.isActive = false;
                                    version.adminForce = false; // Pause should also remove admin force
                                });
                            } else if (actionType === 'resume') {
                                this.versionList.forEach(version => {
                                    version.isActive = true;
                                    version.adminForce = false; // Resume should remove admin force
                                });
                            }
                            this.$message.success(successMessage);
                            // Re-sort to ensure admin forced version is visually prominent if needed, or just update state
                            this.versionList.sort((a, b) => b.versionCode - a.versionCode);
                        } catch (error) {
                            this.$message.error(errorMessage);
                            console.error('Emergency action failed:', error);
                        } finally {
                            this.loading = false;
                        }
                    }).catch(() => {
                        this.$message.info('已取消紧急操作。');
                    });
                },

                // Formats date for display
                formatDate(dateString) {
                    const options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' };
                    return new Date(dateString).toLocaleString('zh-CN', options);
                },

                // Determines Element UI tag color based on update type
                getUpdateTypeColor(row) {
                    if (row.adminForce) return 'danger';
                    if (row.isForce) return 'warning';
                    return 'primary';
                },

                // Determines update type text
                getUpdateTypeText(row) {
                    if (row.adminForce) return '管理员强制';
                    if (row.isForce) return '强制更新';
                    return '可选更新';
                },

                // Handles APK file change in upload component
                handleApkFileChange(file, fileList) {
                    this.fileList = fileList.slice(-1); // Keep only the last file
                    this.publishForm.apkFile = file.raw; // Store the raw file object
                },

                // Submits the publish form (mock)
                submitPublishForm() {
                    this.$refs.publishForm.validate(async (valid) => {
                        if (valid) {
                            if (!this.publishForm.apkFile) {
                                this.$message.error('请上传APK文件');
                                return;
                            }
                            this.loading = true;
                            try {
                                // Mock API call delay
                                await new Promise(resolve => setTimeout(resolve, 1000));

                                // Simulate adding a new version to the list
                                const newVersion = {
                                    id: Date.now(), // Unique ID
                                    versionName: this.publishForm.versionName,
                                    versionCode: this.publishForm.versionCode,
                                    createdAt: new Date().toISOString(),
                                    updateLog: this.publishForm.updateLog,
                                    isForce: this.publishForm.isForce,
                                    adminForce: false,
                                    isActive: this.publishForm.isActive,
                                    downloadCount: 0
                                };
                                this.versionList.unshift(newVersion); // Add to the beginning
                                this.versionList.sort((a, b) => b.versionCode - a.versionCode); // Re-sort

                                this.handlePublishSuccess();
                            } catch (error) {
                                this.$message.error('版本发布失败');
                                console.error('Error publishing version:', error);
                            } finally {
                                this.loading = false;
                            }
                        } else {
                            this.$message.error('请检查表单填写');
                            return false;
                        }
                    });
                }
            }
        });
    </script>
</body>
</html>
