/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-07-10 09:17:37
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 09:37:50
 * @Description: 元素尺寸发生变化执行回调
 */
const targetMap = new WeakMap();
const ob = new ResizeObserver((entries) => {
  entries.forEach((entry) => {
    const handler = targetMap.get(entry.target);
    handler && handler(entry);
  });
});
export default {
  bind(el, binding) {
    ob.observe(el);
    targetMap.set(el, binding.value);
  },
  unbind(el) {
    ob.unobserve(el);
    targetMap.delete(el);
  },
};
