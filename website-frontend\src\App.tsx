import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { HelmetProvider } from 'react-helmet-async';

// 页面组件
import AboutPage from './pages/website/about/AboutPage';
import ServicesPage from './pages/website/services/ServicesPage';
import CasesPage from './pages/website/cases/CasesPage';
import ContactPage from './pages/website/contact/ContactPage';

// 布局组件
import WebsiteLayout from './components/layout/WebsiteLayout';



// 创建 QueryClient 实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5分钟
      gcTime: 10 * 60 * 1000, // 10分钟
    },
  },
});

function App() {
  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider locale={zhCN}>
          <Router>
            <Routes>
              <Route path="/" element={<WebsiteLayout />}>
                <Route index element={<AboutPage />} />
                <Route path="about" element={<AboutPage />} />
                <Route path="services" element={<ServicesPage />} />
                <Route path="cases" element={<CasesPage />} />
                <Route path="contact" element={<ContactPage />} />
              </Route>

              {/* 404页面 */}
              <Route
                path="*"
                element={
                  <div className="min-h-screen flex items-center justify-center">
                    <div className="text-center">
                      <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                      <p className="text-gray-600">页面未找到</p>
                    </div>
                  </div>
                }
              />
            </Routes>
          </Router>
        </ConfigProvider>
      </QueryClientProvider>
    </HelmetProvider>
  );
}

export default App;
