<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:48
 * @Description: 机器盘点
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      :row-key="(row) => row.id"
      :query-param="queryParam"
      show-pagination
      sticky
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          icon="el-icon-plus"
          type="success"
          size="mini"
          @click="handleAdd"
        >
          新增机器盘点单
        </el-button>
        <!--<el-button-->
        <!--  type="success"-->
        <!--  :loading="exportLoading"-->
        <!--  icon="el-icon-download"-->
        <!--  size="mini"-->
        <!--  @click="handleExport"-->
        <!--&gt;-->
        <!--  导出数据-->
        <!--</el-button>-->
      </template>
      <template #actions="{ row }">
        <span class="fixed-width">
          <el-button icon="el-icon-view" @click="handleViewOrEdit(row, 'info')">
            查看
          </el-button>
          <el-button
            v-auth="['@ums:manage:inventory:download']"
            @click="handleExport(row)"
          >
            导出盘点数据
          </el-button>
          <el-button
            v-if="row.stockStatus?.value === 'WAIT_APPROVE'"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleViewOrEdit(row, 'audit')"
          >
            复核
          </el-button>
          <el-button
            v-if="
              row.stockStatus?.value === 'STASH' ||
              row.stockStatus?.value === 'REJECT'
            "
            icon="el-icon-circle-check"
            @click="handleViewOrEdit(row, 'edit')"
          >
            继续盘点
          </el-button>
          <el-button
            v-if="row.stockStatus?.value === 'PASS'"
            type="danger"
            icon="el-icon-error"
            @click="handleLoss(row)"
          >
            报损报溢
          </el-button>
          <el-button
            v-if="
              row.stockStatus?.value === 'STASH' ||
              row.stockStatus?.value === 'REJECT'
            "
            type="danger"
            icon="el-icon-circle-close"
            @click="handleClose(row.id)"
          >
            关闭
          </el-button>
        </span>
      </template>
    </ProTable>
    <ProDrawer
      :value="checkInventory"
      :title="checkTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :no-footer="editType === 'info'"
      :no-confirm-footer="true"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="editForm"
        :form-param="editForm"
        :form-list="columns"
        :confirm-loading="editFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="editType"
      >
        <template #type>
          <div>{{ editForm.type?.label }}</div>
        </template>
        <template #createdBy>
          <div>{{ editForm.createdBy }}</div>
        </template>
        <template #inventoryDetailList>
          <div class="inventory-table">
            <p class="tit-box m-b-12">机器盘点</p>
            <ProTable
              ref="inventoryProTable"
              :columns="inventoryColumns"
              :data="inventoryTableData"
              :local-pagination="detailLocalPagination"
              :row-key="(row) => row.articleCode"
              :query-param="inventoryQueryParam"
              :show-loading="false"
              show-pagination
              :height="500"
              sticky
              @loadData="tempLoadInventoryData"
            >
              <template #btn>
                <div class="title-box-right">
                  <div>总盘点数量: {{ totalData.totalNum || 0 }}</div>
                  <div>在库数量: {{ totalData.inStockNum || 0 }}</div>
                  <div v-if="editType !== 'info'">
                    已盘数量: {{ totalData.overTakeNum || 0 }}
                  </div>
                  <div v-if="editType !== 'info'">
                    未盘数量: {{ totalData.notTakeNum || 0 }}
                  </div>
                  <div>报损数量: {{ totalData.notStockNum || 0 }}</div>
                </div>
              </template>
              <!--<template #btn>-->
              <!--  <el-button-->
              <!--    v-if="editType === 'add'"-->
              <!--    type="success"-->
              <!--    size="mini"-->
              <!--    @click="handleCheck"-->
              <!--  >-->
              <!--    盘点-->
              <!--  </el-button>-->
              <!--</template>-->
              <!-- 原机器编号 -->
              <!--@blur="handleTableInputEdit(row, 'originCode')"-->
              <template #originCode="{ row }">
                <el-input
                  v-if="editType !== 'info'"
                  v-model="row.originCode"
                  placeholder="请输入原机器编号"
                  type="text"
                  size="small"
                  :disabled="editType === 'info' || editType === 'audit'"
                ></el-input>
                <div v-else>{{ row?.originCode }}</div>
              </template>
              <!--@change="(e) => handleTableEdit(e, row, 'hostType')"-->
              <template #hostType="{ row }">
                <el-select
                  v-if="editType !== 'info'"
                  v-model="row.hostType"
                  size="small"
                  :disabled="editType === 'info' || editType === 'audit'"
                  @change="handleHostTypeChange(row)"
                >
                  <el-option
                    v-for="item in hostTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <div v-else>{{ row.hostType?.label }}</div>
              </template>
              <template #productId="{ row }">
                <div v-if="editType !== 'info'">
                  <div
                    v-if="row.hostType !== '2008'"
                    style="
                      display: flex;
                      justify-content: space-between;
                      gap: 20px;
                    "
                  >
                    <el-input
                      v-model="row.articleName"
                      disabled
                      size="small"
                      placeholder="请选择选配件"
                    />
                    <el-button
                      v-if="editType === 'edit' || editType === 'add'"
                      type="primary"
                      size="mini"
                      @click="handleSelectSpare(row)"
                    >
                      选择
                    </el-button>
                  </div>
                  <!--@change="(e) => handleTableEdit(e, row, 'productId')"-->
                  <el-cascader
                    v-else
                    ref="ProductIds"
                    :value="row.fullIdPath"
                    filterable
                    clearable
                    :options="options"
                    style="width: 100%"
                    :show-all-levels="false"
                    size="small"
                    :disabled="editType === 'info' || editType === 'audit'"
                    :props="{
                      label: 'name',
                      value: 'fullIdPath',
                      children: 'children',
                      expandTrigger: 'click',
                    }"
                    @change="(e) => handleProductIdChange(e, row)"
                  ></el-cascader>
                </div>
                <div v-else>{{ row?.articleName }}</div>
              </template>
              <!-- @change="(e) => handleTableEdit(e, row, 'deviceOn')"-->
              <template #deviceOn="{ row }">
                <el-select
                  v-if="editType !== 'info'"
                  v-model="row.deviceOn"
                  size="small"
                  :disabled="editType === 'info' || editType === 'audit'"
                >
                  <el-option
                    v-for="item in deviceOnOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <div v-else>{{ row?.deviceOn?.label }}</div>
              </template>
              <!-- @change="(e) => handleTableEdit(e, row, 'deviceStatus')"-->
              <template #deviceStatus="{ row }">
                <el-select
                  v-if="editType !== 'info'"
                  v-model="row.deviceStatus"
                  size="small"
                  :disabled="editType === 'info' || editType === 'audit'"
                >
                  <el-option
                    v-for="item in deviceStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <div v-else>{{ row?.deviceStatus?.label }}</div>
              </template>
              <template #picture="{ row }">
                <el-image
                  v-if="
                    row.imageFiles &&
                    Array.isArray(row.imageFiles) &&
                    row.imageFiles.length > 0
                  "
                  style="width: 100px; height: 100px"
                  :src="row.imageFiles[0].url"
                  :preview-src-list="[row.imageFiles[0].url]"
                />
                <div v-else>暂无</div>
              </template>
              <template #location="{ row }">
                <el-input
                  v-if="editType !== 'info'"
                  v-model="row.location"
                  placeholder="请输入储位"
                  type="text"
                  size="small"
                  :disabled="editType === 'info' || editType === 'audit'"
                ></el-input>
                <div v-else>{{ row?.location }}</div>
              </template>
              <!--@change="(e) => handleTableEdit(e, row, 'stockNum')"-->
              <template #stockNum="{ row }">
                <el-select
                  v-if="editType !== 'info'"
                  v-model="row.stockNum"
                  size="small"
                  :disabled="editType === 'info' || editType === 'audit'"
                >
                  <el-option
                    v-for="item in stockOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <div v-else>{{ row.stockNum == 1 ? "是" : "否" }}</div>
              </template>
              <template #actions="{ row }">
                <div class="fixed-width">
                  <el-button
                    v-if="editType === 'edit' || editType === 'add'"
                    icon="el-icon-circle-check"
                    @click="handleRowSave(row)"
                  >
                    保存
                  </el-button>
                </div>
              </template>
            </ProTable>
          </div>
        </template>
      </ProForm>
      <template #footer>
        <div
          v-if="!(editType === 'info' || editType === 'audit')"
          class="footer"
        >
          <!--<el-button type="info" @click="handleSubmit('staging')">-->
          <!--  暂存-->
          <!--</el-button>-->
          <el-button type="primary" @click="confirmSubmit">确认提交</el-button>
          <el-button @click="closeDrawer">关闭</el-button>
        </div>
        <div v-if="editType === 'audit'" class="footer">
          <el-button type="danger" @click="handleAudit('REJECT', 'error')">
            驳回
          </el-button>
          <el-button type="primary" @click="handleAudit('PASS', 'success')">
            审核通过
          </el-button>
          <el-button @click="closeDrawer">取消</el-button>
        </div>
      </template>
    </ProDrawer>
    <ProDrawer
      :value="checkLosses"
      :title="'报损报溢'"
      size="80%"
      :confirm-loading="confirmLoading"
      :no-footer="true"
      @cancel="closeLossesDrawer"
    >
      <ProForm
        ref="editForm"
        :form-param="lossesForm"
        :form-list="columns"
        :confirm-loading="editFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="editType"
      >
        <template #createdBy>
          <div>{{ lossesForm.createdBy }}</div>
        </template>
        <template #type>
          <div>{{ lossesForm.type?.label }}</div>
        </template>
        <template #lossesDetailList>
          <ProTable
            ref="lossesProTable"
            :columns="lossesColumns"
            :data="lossesTableData"
            :local-pagination="lossesLocalPagination"
            :row-key="(row) => row.id"
            :show-loading="false"
            :show-setting="false"
            :show-search="false"
            show-pagination
            :height="500"
            sticky
            @loadData="loadLossesData"
          >
            <template #picture="{ row }">
              <el-image
                v-if="
                  row.imageFiles &&
                  Array.isArray(row.imageFiles) &&
                  row.imageFiles.length > 0
                "
                style="width: 100px; height: 100px"
                :src="row.imageFiles[0].url"
                :preview-src-list="[row.imageFiles[0].url]"
              />
              <div v-else>暂无</div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 选配件选择 -->
    <SparePart
      ref="sparePart"
      :dialog-visible.sync="sparePartDialog"
      :spare-type="hostType"
      @confirm="confirmSpare"
    />
  </div>
</template>

<script>
import {
  getWarehouseInventoryListApi,
  warehouseListApi,
  saveWarehouseInventoryApi,
  submitWarehouseInventoryApi,
  checkWarehouseInventoryDetailApi,
  lossAndOverflowPageApi,
  checkWarehouseInventoryApi,
  deleteWarehouseInventoryApi,
  getMachineInventoryListApi,
  editWarehouseInventoryApi,
  exportWarehouseInventoryApi,
  getMachineInventoryTotalApi,
  getMachineInventoryDetailListApi,
  getMachineInventoryDetailTotalApi,
} from "@/api/store";
import { filterParam, filterParamRange, transformFormParams } from "@/utils";
import { cloneDeep } from "lodash";
import { Message } from "element-ui";
import { dictTreeByCodeApi } from "@/api/user";
import SparePart from "@/views/procure/cpns/sparePart.vue";
import { productAllApi } from "@/api/dispose";
import { handleExcelExport } from "@/utils/exportExcel";
export default {
  name: "MachineInventory",
  components: { SparePart },
  data() {
    return {
      editType: "info", // info:查看  edit: 编辑  add:新增
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      tableData: [],
      columns: [
        {
          title: "盘点单号",
          dataIndex: "code",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        // {
        //   title: "盘点单号",
        //   dataIndex: "code",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 24,
        // },
        // {
        //   title: "归属仓库",
        //   dataIndex: "warehouseName",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "select",
        //   clearable: true,
        //   // isForm: true,
        //   disabled: false,
        //   formSpan: 6,
        //   width: 120,
        //   option: [],
        //   optionMth: () => warehouseListApi({ status: 1401 }),
        //   optionskey: {
        //     label: "name",
        //     value: "id",
        //   },
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择归属仓库",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          title: "库存数量",
          dataIndex: "inventoryNum",
          isTable: true,
        },
        {
          title: "库存金额",
          dataIndex: "inventoryAmount",
          isTable: true,
        },
        {
          title: "盘点数量",
          dataIndex: "stockNum",
          isTable: true,
        },
        {
          title: "盘点金额",
          dataIndex: "stockAmount",
          isTable: true,
        },
        {
          title: "盘点人",
          dataIndex: "createdBy",
          isTable: true,
          isSearch: true,
          isForm: true,
          disabled: false,
          align: "center",
          valueType: "input",
          formSlot: "createdBy",
          formSpan: 6,
          formatter: (row) => row.createdBy?.name,
        },
        // {
        //   title: "盘点日期",
        //   dataIndex: "createdAt",
        //   isForm: true,
        //   formSpan: 6,
        //   clearable: true,
        //   valueType: "date-picker",
        //   pickerType: "datetime",
        //   disabled: true,
        // },
        {
          title: "盘点日期",
          dataIndex: "createdAt",
          isTable: true,
          isSearch: true,
          clearable: true,
          width: 150,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
        },
        {
          title: "耗材盘点",
          dataIndex: "inventoryDetail",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "inventoryDetailList",
        },
        {
          title: "报损报溢",
          dataIndex: "lossesDetailList",
          isForm: false,
          formSpan: 24,
          formSlot: "lossesDetailList",
        },

        {
          title: "复核人",
          dataIndex: "recheckName",
          isTable: true,
          align: "center",
        },
        {
          title: "复核时间",
          dataIndex: "recheckAt",
          isTable: true,
          width: 150,
        },
        {
          title: "状态",
          dataIndex: "stockStatus",
          isTable: true,
          formatter: (row) => row.stockStatus?.label,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [
            {
              label: "待审核",
              value: "WAIT_APPROVE",
            },
            {
              label: "通过",
              value: "PASS",
            },
            {
              label: "驳回",
              value: "REJECT",
            },
            {
              label: "暂存",
              value: "STASH",
            },
            {
              label: "关闭",
              value: "CLOSE",
            },
          ],
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 350,
        },
      ],
      checkInventory: false,
      checkTitle: "查看 —— ",
      confirmLoading: false,
      editForm: {},
      editFormLoading: false,
      inventoryQueryParam: {},
      hostTypeOptions: [],
      deviceOnOptions: [],
      deviceStatusOptions: [],
      inventoryColumns: [
        // {
        //   title: "图片",
        //   dataIndex: "picture",
        //   isTable: true,
        //   tableSlot: "picture",
        // },
        {
          dataIndex: "productIds",
          title: "机器型号",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "articleName",
          title: "选配件型号",
          isSearch: true,
          valueType: "input",
        },
        {
          title: "机器编号",
          dataIndex: "articleCode",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          title: "原机器编号",
          dataIndex: "originCode",
          isTable: true,
          tableSlot: "originCode",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          tableSlot: "hostType",
          // formatter: (row) => row.hostType?.label,
        },
        {
          dataIndex: "hostTypes",
          title: "主机类型",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(2000),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   title: "机器型号",
        //   dataIndex: "articleName",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          title: "机器/选配件型号",
          dataIndex: "productId",
          isTable: true,
          tableSlot: "productId",
          minWidth: 150,
        },

        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          tableSlot: "deviceOn",
          // formatter: (row) => row.deviceOn?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1100),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 80,
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          // formatter: (row) => row.deviceStatus?.label,
          tableSlot: "deviceStatus",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6600),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 80,
        },
        {
          dataIndex: "inventoryAmount",
          title: "采购价",
          isTable: true,
          width: 100,
        },
        {
          title: "储位",
          dataIndex: "location",
          isTable: true,
          tableSlot: "location",
          isSearch: true,
          valueType: "input",
        },
        {
          title: "是否在库",
          dataIndex: "stockNum",
          isTable: true,
          tableSlot: "stockNum",
          width: 150,
        },
        {
          title: "是否在库",
          dataIndex: "isNormal",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "isTake",
          title: "是否盘点",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          title: "操作",
          dataIndex: "action",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 100,
        },
      ],
      inventoryTableData: [],
      detailLocalPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      cacheInventoryData: [],
      checkLosses: false,
      lossesQueryParam: {},
      lossesForm: {},
      lossesLocalPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      lossesColumns: [
        {
          title: "机器编号",
          dataIndex: "articleCode",
          isTable: true,
          minWidth: 100,
        },
        {
          title: "机器型号",
          dataIndex: "articleName",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          minWidth: 100,
        },
        // {
        //   title: "OEM编号",
        //   dataIndex: "numberOem",
        //   isTable: true,
        //   width: 150,
        // },
        // {
        //   title: "批次号",
        //   dataIndex: "batchInfo",
        //   isTable: true,
        //   tableSlot: "batchInfo",
        // },
        {
          title: "图片",
          dataIndex: "picture",
          isTable: true,
          tableSlot: "picture",
          minWidth: 120,
        },
        // {
        //   title: "单位",
        //   dataIndex: "unit",
        //   isTable: true,
        // },
        // {
        //   title: "单价",
        //   dataIndex: "price",
        //   isTable: true,
        //   minWidth: 100,
        // },
        {
          title: "金额",
          dataIndex: "inventoryAmount",
          isTable: true,
          minWidth: 100,
        },
        // {
        //   title: "库存数量",
        //   dataIndex: "inventoryNum",
        //   isTable: true,
        //   minWidth: 80,
        // },
        {
          title: "库存盘点",
          dataIndex: "stockNum",
          isTable: true,
          minWidth: 80,
          // tableSlot: "stockNum",
        },
        {
          title: "损益值",
          dataIndex: "discrepancyNum",
          isTable: true,
          minWidth: 80,
        },
        {
          title: "损益金额",
          dataIndex: "discrepancyAmount",
          isTable: true,
          minWidth: 100,
        },
      ],
      lossesTableData: [],
      stockOptions: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      sparePartDialog: false,
      selectedSparePart: null,
      hostType: "",
      options: [],
      takeStockId: "", // 盘点单ID
      exportLoading: false,
      requestParameters: {},
      totalData: {},
      tempLoadInventoryData: null,
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
    this.initData();
  },
  methods: {
    handleRowSave(row) {
      const args = {
        ...row,
        takeStockId: this.takeStockId,
      };
      delete args.fullIdPath;
      if (!row.hostType) {
        this.$message.warning("请选择主机类型");
        return;
      }
      if (!row.productId) {
        this.$message.warning("请选择机器/选配件型号");
        return;
      }
      editWarehouseInventoryApi(args).then((res) => {
        this.takeStockId = res.data;
        this.$refs.inventoryProTable.refresh();
        this.$message.success("保存成功");
      });
    },
    handleProductIdChange(e, row) {
      if (e.length) {
        row.productId = e[e.length - 1].split("/").pop();
      } else {
        row.productId = "";
      }
    },
    handleHostTypeChange(row) {
      row.productId = "";
      row.articleName = "";
    },
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startTime: null,
          endTime: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      getWarehouseInventoryListApi({ ...requestParameters, stockType: 1 })
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    loadInventoryData(parameter) {
      this.inventoryQueryParam = filterParam(
        Object.assign({}, this.inventoryQueryParam, parameter)
      );
      const requestParameters = cloneDeep(this.inventoryQueryParam);
      const params = {
        ...requestParameters,
        stockType: 1,
        warehouseId: 0,
        takeStockId: this.editForm.id || this.takeStockId || null,
      };
      this.requestParameters = cloneDeep(params);
      getMachineInventoryListApi(params)
        .then((res) => {
          this.inventoryTableData = res.data.rows;
          this.inventoryTableData.forEach((item) => {
            if (item.fullIdPath) {
              const inputString = item.fullIdPath;
              const trimmedString = inputString.replace(/^\/|\/$/g, "");
              const parts = trimmedString.split("/");
              item.fullIdPath =
                Array.isArray(parts) &&
                parts.reduce((acc, part, index) => {
                  if (index === 0) {
                    acc.push(`/${part}`);
                  } else {
                    acc.push(`${acc[index - 1]}/${part}`);
                  }
                  return acc;
                }, []);
            }
            return transformFormParams(item);
          });
          this.detailLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.inventoryProTable
            ? (this.$refs.inventoryProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getTotalData();
    },
    loadInventoryDetailData(parameter) {
      this.inventoryQueryParam = filterParam(
        Object.assign({}, this.inventoryQueryParam, parameter)
      );
      const requestParameters = cloneDeep(this.inventoryQueryParam);
      const params = {
        ...requestParameters,
        stockType: 1,
        warehouseId: 0,
        takeStockId: this.editForm.id || this.takeStockId || null,
      };
      this.requestParameters = cloneDeep(params);
      getMachineInventoryDetailListApi(params)
        .then((res) => {
          this.inventoryTableData = res.data.rows;
          // this.inventoryTableData.forEach((item) => {
          //   if (item.fullIdPath) {
          //     const inputString = item.fullIdPath;
          //     const trimmedString = inputString.replace(/^\/|\/$/g, "");
          //     const parts = trimmedString.split("/");
          //     item.fullIdPath =
          //       Array.isArray(parts) &&
          //       parts.reduce((acc, part, index) => {
          //         if (index === 0) {
          //           acc.push(`/${part}`);
          //         } else {
          //           acc.push(`${acc[index - 1]}/${part}`);
          //         }
          //         return acc;
          //       }, []);
          //   }
          //   return transformFormParams(item);
          // });
          this.detailLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.inventoryProTable
            ? (this.$refs.inventoryProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getDetailTotalData();
    },
    // 报损报溢数据
    loadLossesData(parameter) {
      this.lossesQueryParam = filterParam(
        Object.assign({}, this.lossesQueryParam, parameter)
      );
      const requestParameters = cloneDeep(this.lossesQueryParam);
      lossAndOverflowPageApi({
        ...requestParameters,
        takeStockCode: this.lossesForm.code,
      })
        .then((res) => {
          this.lossesTableData = res.data.rows;
          this.lossesLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.lossesProTable
            ? (this.$refs.lossesProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    getTotalData() {
      getMachineInventoryTotalApi(this.requestParameters).then((res) => {
        this.totalData = res.data;
      });
    },
    getDetailTotalData() {
      getMachineInventoryDetailTotalApi(this.requestParameters).then((res) => {
        this.totalData = res.data;
      });
    },
    initData() {
      dictTreeByCodeApi(2000).then((res) => {
        this.hostTypeOptions = res.data;
      });
      dictTreeByCodeApi(1100).then((res) => {
        this.deviceOnOptions = res.data;
      });
      dictTreeByCodeApi(6600).then((res) => {
        this.deviceStatusOptions = res.data;
      });
      productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    handleSelectSpare(row) {
      this.selectedSparePart = null;
      if (!row.hostType && row.hostType !== "2008") {
        Message.warning("请选择主机类型");
        return;
      }
      this.selectedSparePart = row;
      this.hostType = row.hostType;
      this.sparePartDialog = true;
    },
    confirmSpare(row) {
      this.$set(this.selectedSparePart, "articleName", row.modeType);
      this.selectedSparePart.productId = row.id;
      console.log(row, "row");
      console.log(this.selectedSparePart, "this.selectedSparePart");
      this.sparePartDialog = false;
    },
    handleMachine() {
      this.checkInventory = false;
      this.$router.push({
        name: "CompanyMachine",
        params: { tabName: "拆借零件" },
      });
    },
    // 新增盘点
    handleAdd() {
      this.reset();
      this.editType = "add";
      this.checkTitle = "新增盘点";
      this.takeStockId = "";
      this.columns.forEach((item, index) => {
        if (
          item.dataIndex === "createdBy" ||
          item.dataIndex === "lossesDetailList"
        ) {
          this.columns[index].isForm = false;
        }
        if (item.dataIndex === "inventoryDetail") {
          this.columns[index].isForm = true;
        }
        if (item.dataIndex === "warehouseName") {
          this.columns[index].disabled = false;
        }
      });
      this.inventoryColumns.forEach((item, index) => {
        if (item.dataIndex === "action") {
          item.isTable = true;
        }
      });
      this.checkInventory = true;
      this.$nextTick(() => {
        this.detailLocalPagination = {
          pageSize: 20,
          pageNumber: 1,
          total: 0,
        };
        this.tempLoadInventoryData = this.loadInventoryData;
        this.$refs.editForm.handleSubmit().then(() => {
          // 加载耗材数据
          this.$refs.inventoryProTable.refresh();
        });
      });
    },
    // 暂存 Or 提交
    handleSubmit(type) {
      const params = {
        stockType: 1,
        id: this.editForm.id || this.takeStockId,
        warehouseId: 0,
        takeDetails: this.cacheInventoryData,
      };
      const editApi =
        type === "submit"
          ? submitWarehouseInventoryApi
          : saveWarehouseInventoryApi;
      editApi(params).then((res) => {
        this.$message.success("保存成功");
        this.$refs.ProTable.refresh();
        this.closeDrawer();
      });
    },
    // 盘点仓库
    handleCheck() {
      this.detailLocalPagination = {
        pageSize: 20,
        pageNumber: 1,
        total: 0,
      };
      this.$refs.editForm.handleSubmit().then(() => {
        // 加载耗材数据
        this.$refs.inventoryProTable.refresh();
      });
    },
    // 审核
    handleAudit(status, type) {
      const confirmText = status === "PASS" ? "通过" : "驳回";
      this.$confirm(
        `此操作将${confirmText}对该条盘点内容的审核, 是否继续?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: type,
        }
      ).then(() => {
        const params = {
          id: this.editForm.id,
          stockStatus: status,
        };
        checkWarehouseInventoryDetailApi(params).then((res) => {
          this.$refs.ProTable.refresh();
          this.closeDrawer();
          this.$message.success(res.message);
        });
      });
    },
    /**
     * @description  查看 Or 编辑
     * @param {Object} row
     * @param {String} type info:查看  edit: 编辑
     */
    handleViewOrEdit(row, type) {
      this.detailLocalPagination = {
        pageSize: 20,
        pageNumber: 1,
        total: 0,
      };
      this.reset();
      switch (type) {
        case "info":
          this.checkTitle = `盘点单号：${row.code} — 查看`;
          break;
        case "edit":
          this.checkTitle = `盘点单号：${row.code} — 继续盘点`;
          break;
        case "audit":
          this.checkTitle = `盘点单号：${row.code} — 复核`;
          break;
        default:
          this.checkTitle = `盘点单号：${row.code} — 查看`;
      }
      this.editType = type;
      this.columns.forEach((item, index) => {
        if (item.dataIndex === "inventoryDetail") {
          this.columns[index].isForm = true;
        }
        if (item.dataIndex === "lossesDetailList") {
          this.columns[index].isForm = false;
        }
        if (type === "edit") {
          if (item.dataIndex === "warehouseName") {
            this.columns[index].disabled = true;
          }
        }
      });
      if (type === "info" || type === "audit") {
        this.inventoryColumns.forEach((item, index) => {
          if (item.dataIndex === "action") {
            item.isTable = false;
          }
        });
      } else {
        this.inventoryColumns.forEach((item, index) => {
          if (item.dataIndex === "action") {
            item.isTable = true;
          }
        });
      }
      this.editForm = cloneDeep(row);
      this.editForm.createdBy = row.createdBy?.name;
      this.takeStockId = row.id;
      this.checkInventory = true;
      this.tempLoadInventoryData =
        type === "info" || type === "audit"
          ? this.loadInventoryDetailData
          : this.loadInventoryData;
      this.$nextTick(() => {
        this.$refs.inventoryProTable.refresh();
      });
    },
    confirmSubmit() {
      this.$confirm("请确定每一行数据操作后都已点保存,否则数据将丢失", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.handleSubmit("submit");
        })
        .catch(() => {});
    },
    // 报损报溢
    handleLoss(row) {
      this.lossesForm = cloneDeep(row);
      this.lossesForm.createdBy = row.createdBy?.name;
      this.columns.forEach((item, index) => {
        if (item.dataIndex === "lossesDetailList") {
          this.columns[index].isForm = true;
        }
        if (item.dataIndex === "inventoryDetail") {
          this.columns[index].isForm = false;
        }
      });
      this.checkLosses = true;
      this.$nextTick(() => {
        this.$refs.lossesProTable.refresh();
      });
    },
    // 关闭当前盘点
    handleClose(id) {
      this.$confirm("此操作将关闭改盘点单, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteWarehouseInventoryApi(id).then((res) => {
          Message.success("操作成功");
          this.localPagination = {
            pageNumber: 1,
            pageSize: 10,
            total: 0,
          };
          this.$nextTick(() => {
            this.$refs.ProTable.refresh();
          });
        });
      });
    },
    closeLossesDrawer() {
      this.reset();
      this.checkLosses = false;
    },
    closeDrawer() {
      this.reset();
      this.checkInventory = false;
    },
    handleExport(row) {
      this.$confirm("此操作将导出机器盘点详情, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          exportWarehouseInventoryApi,
          { takeStockId: row.id },
          `机器盘点单${row.code}数据`
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    reset() {
      this.editForm = {};
      this.cacheInventoryData = [];
      this.inventoryTableData = [];
      this.columns.forEach((item, index) => {
        if (item.dataIndex === "createdBy" || item.dataIndex === "type") {
          this.columns[index].isForm = true;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
::v-deep.fixed-width {
  .el-button {
    i {
      margin-right: 2px;
    }
  }
}
.footer {
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style>
