<!--
 * @Author: wskg
 * @Date: 2024-08-31 16:22:37
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-11 16:35:45
 * @Description: 换件记录
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      row-key="label"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="520"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:machine:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div class="title-box-right">
          <div>总数量：{{ totalData?.num || 0 }}</div>
          <div>总金额：{{ totalData?.amount || 0 }}</div>
        </div>
      </template>
      <template #deviceTree>
        <el-cascader
          v-model="queryParam.deviceGroupName"
          filterable
          clearable
          style="width: 250px"
          :options="productTreeOption"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          @change="handleProductTree"
        >
        </el-cascader>
      </template>
    </ProTable>
  </div>
</template>
<script>
import {
  changeReplaceExportApi,
  changeReplaceListApi,
  changeReplaceSummaryListApi,
} from "@/api/statisics";
import { cloneDeep } from "lodash";
import { filterParam, filterParamRange } from "@/utils";
import { productListApi } from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";
import { handleExcelExport } from "@/utils/exportExcel";
export default {
  name: "PartCount",
  mixins: [],
  props: {},
  data() {
    return {
      productIdName: "",
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      productTreeOption: [],
      queryParam: {},
      columns: [
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          valueType: "product",
          isSearch: true,
          clearable: true,
          // width: 200,
          // searchSlot: "deviceTree",
        },

        {
          dataIndex: "customerSeq",
          title: "客户编号",
          width: 140,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          width: 140,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "brand",
          title: "品牌/机型",
          isTable: true,
          formatter: (row) => {
            const brand = row?.brand ?? "";
            const machine = row?.machine ?? "";
            if (brand && machine) {
              return `${brand}/${machine}`;
            }
            return brand || machine;
          },
          minWidth: 130,
        },
        // {
        //   dataIndex: "machine",
        //   title: "机型",
        //   isTable: true,
        //   width: 100,
        // },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          isSearch: true,
          valueType: "select",
          optionMth: () => dictTreeByCodeApi(700),
          option: [],
          optionskey: {
            label: "label",
            value: "value",
          },
          formatter: (row) => row.deviceGroup?.label,
          width: 100,
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          valueType: "select",
          isTable: true,
          formatter: (row) => row.serType?.label,
        },
        {
          dataIndex: "serTypes",
          title: "服务类型",
          valueType: "select",
          isSearch: true,
          multiple: true,
          option: [
            {
              label: "散修",
              value: "SCATTERED",
            },
            {
              label: "购机不保",
              value: "NO_WARRANTY",
            },
            {
              label: "购机质保",
              value: "WARRANTY",
            },
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "质保服务",
              value: "QA",
            },
            {
              label: "质保含部件",
              value: "QA_COMPONENT",
            },
            {
              label: "维保服务",
              value: "MAINTENANCE",
            },
            {
              label: "其它",
              value: "OTHER",
            },
          ],
        },
        {
          dataIndex: "regCliState",
          title: "安装客户端",
          valueType: "select",
          isSearch: true,
          isTable: true,
          clearable: true,
          option: [
            { value: "1", label: "是" },
            { value: "0", label: "否" },
          ],
          formatter: (row) => (row.regCliState === "1" ? "是" : "否"),
          width: 100,
        },
        {
          dataIndex: "articleName",
          title: "零件名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 170,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "商品单价",
          isTable: true,
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "金额",
          isTable: true,
        },
        {
          dataIndex: "blackWhite",
          title: "黑白计数器",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "color",
          title: "彩色计数器",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "dataSource",
          title: "维修类型",
          isTable: true,
          formatter: (row) => row.dataSource?.label,
          isSearch: true,
          valueType: "select",
          option: [
            { value: "WORK", label: "工单" },
            { value: "SELF", label: "自修" },
          ],
        },
        {
          dataIndex: "workCode",
          title: "维修单号",
          width: 160,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },

        {
          dataIndex: "createdAt",
          title: "更换日期",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
      ],
      totalData: {},
      exportLoading: false,
      requestParameters: {},
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
    this.getProductTree();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          beginDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      this.requestParameters = requestParameters;
      changeReplaceListApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
      this.getTotalData(this.requestParameters);
    },
    async getProductTree() {
      try {
        const result = await productListApi({ pageNumber: 1, pageSize: 9999 });
        if (result.code === 200 && result.data) {
          this.productTreeOption = result.data;
        }
      } catch (error) {
        console.log(error);
      }
    },
    getTotalData(params) {
      changeReplaceSummaryListApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    handleExport() {
      this.$confirm("此操作将导出换件记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          changeReplaceExportApi,
          this.requestParameters,
          "换件记录数据",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    handleProductTree(item) {
      console.log(item);
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.image-view {
  width: 100%;
  padding: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  .img-list {
    width: 135px;
    height: 135px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    /* 鼠标经过有遮罩效果 */
    &:hover {
      position: relative;
      .mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &::after {
          content: "+";
          font-size: 36px;
          color: #fff;
        }
      }
    }
  }
}
.preview-img {
  width: 100%;
  height: 100%;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
