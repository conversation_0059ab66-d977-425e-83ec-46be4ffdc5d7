<template>
  <div class="contact-page-editor">
    <!-- 页面标题 -->
    <div class="page-title-section">
      <EditableContent
        :value="content.title || '联系我们'"
        placeholder="点击编辑页面标题"
        display-class="editable-page-title"
        @input="updateTitle"
      />
    </div>

    <!-- 英雄区配置 -->
    <div class="editor-section">
      <div class="hero-preview">
        <div class="hero-content">
          <EditableContent
            :value="heroTitle"
            placeholder="点击编辑英雄区主标题"
            display-class="editable-hero-title"
            @input="updateHeroTitle"
          />
          <EditableContent
            :value="heroSubtitle"
            placeholder="点击编辑英雄区副标题"
            display-class="editable-hero-subtitle"
            @input="updateHeroSubtitle"
          />
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content-grid">
      <!-- 左侧：表单预览区 -->
      <div class="form-preview-section">
        <div class="editor-section">
          <h3 class="section-title">在线咨询表单预览</h3>
          <div class="form-preview">
            <div class="form-header">
              <EditableContent
                :value="formTitle"
                placeholder="点击编辑表单标题"
                display-class="form-title-preview"
                @input="updateFormTitle"
              />
            </div>

            <!-- 表单字段预览 -->
            <div class="form-fields-preview">
              <div class="field-preview">
                <label>店铺名称 *</label>
                <div class="input-preview">请输入您的店铺名称</div>
              </div>
              <div class="field-preview">
                <label>姓名 *</label>
                <div class="input-preview">请输入您的姓名</div>
              </div>
              <div class="field-preview">
                <label>联系电话 *</label>
                <div class="input-preview">请输入您的手机号码</div>
              </div>
              <div class="field-preview">
                <label>服务类型 *</label>
                <div class="select-preview" :class="{ 'select-open': serviceTypeDropdownOpen }">
                  <div class="select-trigger" @click="toggleServiceTypeDropdown">
                    <span>{{ businessTypes[0] || '请选择咨询类型' }}</span>
                    <i class="el-icon-arrow-down" :class="{ 'arrow-up': serviceTypeDropdownOpen }"></i>
                  </div>
                  <div v-if="serviceTypeDropdownOpen" class="select-options">
                    <div
                      v-for="(type, index) in businessTypes"
                      :key="index"
                      class="select-option"
                      :class="{ 'selected': index === 0 }"
                    >
                      {{ type }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="field-preview">
                <label>详细需求 *</label>
                <div class="textarea-preview">请详细描述您的设备问题或服务需求</div>
              </div>
              <div class="submit-preview">
                <button class="submit-button-preview">提交咨询</button>
              </div>
            </div>
            <p class="form-preview-note">
              * 此处为表单样式预览，功能由系统提供
            </p>

            <!-- 服务类型配置 -->
            <div class="service-types-config">
              <h3 class="config-title">咨询类型配置</h3>
              <div class="service-types-list">
                <div
                  v-for="(type, index) in businessTypes"
                  :key="`stype-${index}-${type}`"
                  class="service-type-item"
                >
                  <EditableContent
                    :value="type"
                    placeholder="`服务类型 #${index + 1}`"
                    display-class="service-type-editable"
                    @input="(value) => updateBusinessTypeItem(index, value)"
                  />
                  <i
                    class="el-icon-delete service-type-delete"
                    @click="deleteBusinessType(index)"
                  ></i>
                </div>
                <el-button
                  type="dashed"
                  size="small"
                  icon="el-icon-plus"
                  class="add-service-type-btn"
                  @click="addBusinessType"
                >
                  添加服务类型
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：联系信息和服务承诺 -->
      <div class="contact-info-section">
        <!-- 联系信息 -->
        <div class="editor-section">
          <h3 class="section-title">联系信息</h3>
          <div class="contact-info-card">
            <div class="contact-row">
              <label>公司地址：</label>
              <EditableContent
                :value="companyAddress"
                placeholder="点击编辑公司地址"
                display-class="contact-text"
                @input="updateCompanyAddress"
              />
            </div>
            <div class="contact-row">
              <label>服务热线：</label>
              <EditableContent
                :value="servicePhone"
                placeholder="点击编辑服务热线"
                display-class="contact-text"
                @input="updateServicePhone"
              />
            </div>
            <div class="contact-row">
              <label>服务时间：</label>
              <EditableContent
                :value="businessHours"
                placeholder="点击编辑服务时间"
                display-class="contact-text"
                @input="updateBusinessHours"
              />
            </div>
            <div class="contact-row">
              <label>邮箱地址：</label>
              <EditableContent
                :value="contactEmail"
                placeholder="点击编辑邮箱地址"
                display-class="contact-text"
                @input="updateContactEmail"
              />
            </div>
          </div>
        </div>

        <!-- 服务承诺 -->
        <div class="editor-section">
          <div class="commitments-section">
            <EditableContent
              :value="commitmentTitle"
              placeholder="点击编辑服务承诺标题"
              display-class="commitments-title-text"
              @input="updateCommitmentTitle"
            />
            <div class="commitments-list">
              <div
                v-for="(commitment, index) in commitments"
                :key="`commitment-${index}-${commitment.substring(0, 10)}`"
                class="commitment-item"
              >
                <span class="commitment-check">✓</span>
                <EditableContent
                  :value="commitment"
                  placeholder="`承诺 #${index + 1}`"
                  display-class="commitment-editable-text"
                  @input="(value) => updateCommitmentItem(index, value)"
                />
                <i
                  class="el-icon-delete commitment-delete"
                  @click="deleteCommitment(index)"
                ></i>
              </div>
            </div>
            <el-button
              type="primary"
              ghost
              size="small"
              icon="el-icon-plus"
              class="add-commitment-btn"
              @click="addCommitment"
            >
              添加承诺
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 位置导航 -->
    <div class="map-section">
      <div class="editor-section">
        <div class="map-container">
          <EditableContent
            :value="mapTitle"
            placeholder="点击编辑地图标题"
            display-class="map-title-text"
            @input="updateMapTitle"
          />
          <div class="map-preview-container">
            <div v-if="companyAddress && companyAddress !== '北京市朝阳区XXX路XXX号'" class="map-preview-with-address">
              <MapPreview
                :key="companyAddress"
                :address="companyAddress"
                height="100%"
                :showNavigateButton="true"
              />
            </div>
            <div v-else class="map-preview-empty">
              地图预览区域 (请先填写公司地址)
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 业务类型配置（隐藏区域，仅用于数据管理） -->
    <div class="business-types-config" style="display: none;">
      <EditableContent
        :value="businessTypeString"
        placeholder="业务类型配置"
        @input="updateBusinessTypes"
      />
    </div>




  </div>
</template>

<script>
import EditableContent from '@/websites/components/website/EditableContent.vue'
import MapPreview from '@/websites/components/website/MapPreview.vue'

export default {
  name: 'ContactPageEditor',
  components: {
    EditableContent,
    MapPreview
  },
  props: {
    content: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      serviceTypeDropdownOpen: true // 默认展开
    }
  },
  computed: {
    // 英雄区配置 - 使用扁平化结构
    heroTitle() {
      return this.content.config?.heroTitle || '联系我们'
    },
    heroSubtitle() {
      return this.content.config?.heroSubtitle || '24小时快速响应，专业维修服务'
    },

    // 表单配置
    formTitle() {
      return this.content.config?.formTitle || '在线咨询'
    },

    // 联系信息 - 使用扁平化结构，与React项目保持一致
    companyAddress() {
      return this.content.config?.companyAddress || '北京市朝阳区XXX路XXX号'
    },
    servicePhone() {
      return this.content.config?.servicePhone || '************'
    },
    businessHours() {
      return this.content.config?.businessHours || '周一至周五 9:00-18:00'
    },
    contactEmail() {
      return this.content.config?.contactEmail || '<EMAIL>'
    },

    // 服务承诺
    commitmentTitle() {
      return this.content.config?.commitmentTitle || '服务承诺'
    },
    commitments() {
      return this.content.config?.commitments || [
        '24小时内快速响应',
        '上门服务，现场检修',
        '质量保证，售后无忧',
        '合理收费，透明价格'
      ]
    },

    // 业务类型
    businessTypeString() {
      return this.content.config?.businessType || '复印机维修,打印机维修,设备保养,紧急维修,设备升级'
    },
    businessTypes() {
      return this.businessTypeString
        .split(',')
        .map(type => type.trim())
        .filter(type => type)
    },

    // 地图配置
    mapTitle() {
      return this.content.config?.mapTitle || '位置导航'
    }
  },
  methods: {
    // 基本信息更新
    updateTitle(value) {
      this.$emit('update', { title: value })
      this.$emit('change')
    },

    // 英雄区更新
    updateHeroTitle(value) {
      this.updateConfig({ heroTitle: value })
    },
    updateHeroSubtitle(value) {
      this.updateConfig({ heroSubtitle: value })
    },

    // 表单配置更新
    updateFormTitle(value) {
      this.updateConfig({ formTitle: value })
    },

    // 联系信息更新 - 使用扁平化结构，与React项目保持一致
    updateCompanyAddress(value) {
      this.updateConfig({ companyAddress: value })
    },
    updateServicePhone(value) {
      this.updateConfig({ servicePhone: value })
    },
    updateBusinessHours(value) {
      this.updateConfig({ businessHours: value })
    },
    updateContactEmail(value) {
      this.updateConfig({ contactEmail: value })
    },

    // 业务类型更新
    updateBusinessTypes(value) {
      this.updateConfig({ businessType: value })
    },
    updateBusinessTypeItem(index, value) {
      const newTypes = [...this.businessTypes]
      newTypes[index] = value
      this.updateConfig({ businessType: newTypes.join(',') })
    },
    addBusinessType() {
      const newTypes = [...this.businessTypes]
      newTypes.push('')
      this.updateConfig({ businessType: newTypes.join(',') })
    },
    deleteBusinessType(index) {
      const newTypes = [...this.businessTypes]
      newTypes.splice(index, 1)
      this.updateConfig({ businessType: newTypes.join(',') })
    },

    // 服务承诺管理 - 完全匹配React项目实现
    updateCommitmentTitle(value) {
      this.updateConfig({ commitmentTitle: value })
    },
    updateCommitmentItem(index, value) {
      const newCommitments = [...this.commitments]
      newCommitments[index] = value
      this.updateConfig({ commitments: newCommitments })
    },
    addCommitment() {
      const newCommitments = [...this.commitments]
      newCommitments.push('')
      this.updateConfig({ commitments: newCommitments })
    },
    deleteCommitment(index) {
      const newCommitments = [...this.commitments]
      newCommitments.splice(index, 1)
      this.updateConfig({ commitments: newCommitments })
    },

    // 地图设置更新
    updateMapTitle(value) {
      this.updateConfig({ mapTitle: value })
    },

    // 服务类型下拉控制
    toggleServiceTypeDropdown() {
      this.serviceTypeDropdownOpen = !this.serviceTypeDropdownOpen
    },

    // 统一的配置更新方法
    updateConfig(configUpdate) {
      const newConfig = { ...this.content.config, ...configUpdate }
      this.$emit('update', { config: newConfig })
      this.$emit('change')
    }
  }
}
</script>

<style lang="scss" scoped>
.contact-page-editor {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 16px 20px;

  // 页面标题 - 统一使用EditableContent组件的全局样式
  .page-title-section {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 2rem;
  }

  // 英雄区预览
  .hero-preview {
    background: #1f2937;
    color: white;
    padding: 64px 32px;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 16px;
    // 英雄区内容样式由EditableContent组件统一管理
  }

  // 主要内容网格布局
  .main-content-grid {
    display: grid;
    grid-template-columns: 3fr 2fr;
    gap: 32px;
    margin-bottom: 32px;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 24px;
    }
  }

  // 表单预览区域
  .form-preview-section {
    .form-preview {
      background: white;
      border-radius: 12px;
      padding: 32px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

      .form-header {
        margin-bottom: 24px;

        .form-title-preview {
          font-size: 1.5rem;
          font-weight: 600;
          color: #1f2937;
          display: block;
        }
      }

      .form-fields-preview {
        .field-preview {
          margin-bottom: 16px;

          label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
            font-size: 14px;
            text-align: left;
          }

          .input-preview, .textarea-preview {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #f9fafb;
            color: #9ca3af;
            font-size: 14px;
            min-height: 40px;
            display: flex;
            align-items: center;
          }

          .textarea-preview {
            min-height: 80px;
            align-items: flex-start;
            padding-top: 12px;
          }

          .select-preview {
            width: 100%;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #f9fafb;
            position: relative;

            &.select-open {
              border-bottom-left-radius: 0;
              border-bottom-right-radius: 0;
            }

            .select-trigger {
              padding: 8px 12px;
              color: #9ca3af;
              display: flex;
              align-items: center;
              justify-content: space-between;
              font-size: 14px;
              min-height: 40px;
              cursor: pointer;
              user-select: none;

              span {
                text-align: left;
                flex: 1;
              }

              &:hover {
                background: #f3f4f6;
              }

              i {
                color: #6b7280;
                transition: transform 0.2s ease;

                &.arrow-up {
                  transform: rotate(180deg);
                }
              }
            }

            .select-options {
              position: absolute;
              top: 100%;
              left: -1px;
              right: -1px;
              background: white;
              border: 1px solid #d1d5db;
              border-top: none;
              border-bottom-left-radius: 6px;
              border-bottom-right-radius: 6px;
              box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
              z-index: 10;
              max-height: 200px;
              overflow-y: auto;

              .select-option {
                padding: 8px 12px;
                font-size: 14px;
                color: #374151;
                cursor: pointer;
                border-bottom: 1px solid #f3f4f6;
                text-align: left;

                &:last-child {
                  border-bottom: none;
                }

                &:hover {
                  background: #f9fafb;
                }

                &.selected {
                  background: #eff6ff;
                  color: #2563eb;
                  font-weight: 500;
                }
              }
            }
          }
        }

        .submit-preview {
          margin-top: 24px;

          .submit-button-preview {
            width: 100%;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: not-allowed;
            opacity: 0.8;
          }
        }
      }

      .form-preview-note {
        text-align: center;
        color: #9ca3af;
        font-size: 14px;
        margin-top: 16px;
      }

      // 服务类型配置区域
      .service-types-config {
        margin-top: 32px;

        .config-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 16px;
        }

        .service-types-list {
          .service-type-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            position: relative;

            &:hover .service-type-delete {
              opacity: 1;
            }

            .service-type-editable {
              flex: 1;
              min-height: 24px;
              padding: 4px 8px;
              border: 2px dashed transparent;
              border-radius: 4px;

              &:hover {
                border-color: #d1d5db;
              }

              // 显示模式时垂直居中对齐
              &:not(.editing) {
                display: flex;
                align-items: center;
              }
            }

            .service-type-delete {
              color: #f87171;
              cursor: pointer;
              font-size: 16px;
              opacity: 0;
              transition: opacity 0.2s ease;
              flex-shrink: 0;
              margin-top: 8px; // 为编辑模式添加顶部边距，使其与输入框对齐
              margin-left: 12px; // 增加左边距，与编辑框保持适当间距

              &:hover {
                color: #ef4444;
              }
            }

            // 当包含编辑状态的EditableContent时，调整删除按钮位置
            &:has(.editable-content.editing) .service-type-delete {
              margin-top: 12px; // 微调编辑模式下的对齐
            }

            // 兼容性方案：使用相邻兄弟选择器
            .editable-content.editing + .service-type-delete {
              margin-top: 12px;
            }
          }

          .add-service-type-btn {
            border-color: #1890ff;
            color: #1890ff;
            background: transparent;

            &:hover {
              background: rgba(24, 144, 255, 0.1);
            }
          }
        }
      }
    }
  }

  // 联系信息区域
  .contact-info-section {
    .contact-info-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 24px;
    }

    .commitments-section {
      background: #6366f1; // 蓝色背景，匹配React项目
      color: white;
      border-radius: 12px;
      padding: 32px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 24px;

      .commitments-title-text {
        font-size: 1.5rem;
        font-weight: 600;
        color: white;
        display: block;
        min-height: 32px;
        padding: 8px;
        border: 2px dashed transparent;
        border-radius: 6px;
        margin-bottom: 24px;

        &:hover {
          border-color: rgba(255, 255, 255, 0.3);
        }
      }

      .commitments-list {
        margin-bottom: 16px;

        .commitment-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          position: relative;

          &:hover .commitment-delete {
            opacity: 1;
          }

          .commitment-check {
            color: #10b981; // 绿色勾选，匹配React项目
            margin-right: 8px;
            font-size: 16px;
            font-weight: bold;
            flex-shrink: 0;
          }

          .commitment-editable-text {
            flex: 1;
            color: white;
            font-size: 14px;
            min-height: 24px;
            padding: 4px 8px;
            border: 2px dashed transparent;
            border-radius: 4px;
            margin-right: 8px;

            &:hover {
              border-color: rgba(255, 255, 255, 0.3);
            }
          }

          .commitment-delete {
            color: #f87171; // 红色删除图标
            cursor: pointer;
            font-size: 16px;
            opacity: 0;
            transition: opacity 0.2s ease;
            flex-shrink: 0;

            &:hover {
              color: #ef4444;
            }
          }
        }
      }

      .add-commitment-btn {
        background: transparent;
        border: 1px dashed rgba(255, 255, 255, 0.5);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.8);
        }
      }
    }

  // 隐藏的业务类型配置
  .business-types-config {
    display: none;
  }
  }

  // 地图区域样式
  .map-section {
    margin-top: 48px;

    .map-container {
      background: white;
      border-radius: 12px;
      padding: 32px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

      .map-title-text {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
        display: block;
        text-align: center;
        margin-bottom: 24px;
        padding: 8px;
        border: 2px dashed transparent;
        border-radius: 6px;

        &:hover {
          border-color: #d1d5db;
        }
      }

      .map-preview-container {
        height: 400px; // 增大地图预览尺寸，提供更好的视觉体验
        border-radius: 12px;
        overflow: hidden;

        .map-preview-with-address {
          height: 100%;
          width: 100%;
        }

        .map-preview-empty {
          height: 100%;
          background: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #9ca3af;
          font-size: 14px;
        }
      }
    }
  }

  // 通用编辑器样式
  .editor-section {
    margin-bottom: 32px;

    .section-title {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      border-bottom: 2px solid #e5e7eb;
      padding-bottom: 8px;
    }
  }

  // 联系信息行样式
  .contact-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    label {
      width: 100px;
      font-weight: 500;
      color: #374151;
      margin-right: 16px;
      margin-top: 4px;
      font-size: 14px;
    }

    .contact-text {
      flex: 1;
      min-height: 24px;
      padding: 4px 8px;
      border: 2px dashed transparent;
      border-radius: 4px;

      &:hover {
        border-color: #d1d5db;
      }
    }
  }


}
</style>
