/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-11 16:39:50
 * @Description:
 */

import { get, post, put, del, down, postDown } from "@/utils/request";

// ================== 领料明细列表 ====================
export const receiveListApi = (data) =>
  post(`/statisics/receiveDetailPage`, data);
// 明细汇总
export const receiveDetailSummaryApi = (data) =>
  post(`/statisics/receiveDetailSummary`, data);

// ================== 用料明细列表 ====================
export const usedListApi = (data) => post(`/statisics/usedDetailPage`, data);
// 导出用料明细
export const usedListExportApi = (data) =>
  postDown(`/statisics/usedDetail/export`, data);

// ================== 抄表记录 ====================
export const meterListApi = (data) => post(`/iot-pointer/page`, data);
// 维修工单统计打印数量分页查询
// export const printListApi = (data) => get(`/iot-pointer/repair/page`, data);
// 修改抄表数据
export const meterUpdateApi = (data) => post(`/iot-pointer/add`, data);
// 手动生成对账单
export const createMeterOrderApi = (data) =>
  post(`/iot-receipt/selectGenerateReceipt`, data);
// 打印服务收款单分页查询
export const printReceiveListApi = (data) =>
  post(`/iot-pointer/receipt/page`, data);

// 收款单登记减免费用
export const receiptReduceApi = (data) =>
  post(`/iot-pointer/receipt/adjust`, data);
// 查看客户与设备抄表记录
export const showMeterDetailApi = (data) => get(`/iot-pointer/list`, data);
// 查看收款单明细
export const receiptDetailApi = (id) => get(`/iot-receipt/${id}`);
// 重新统计打印数量
export const rePrintListApi = (data) => post(`/iot-pointer/reCountIot`, data);
// 从新调整黑白计数器数值
export const resetPrintMeterListApi = (data) =>
  post(`/iot-pointer/adjustPointNum`, data);
// 导出抄表对账单
export const meterExportDetailApi = (data) =>
  postDown(`/iot-receipt/export`, data);

// 维修记录统计表
export const printListApi = (data) => post(`/statisics/repair/page`, data);
// 维修记录统计详情
export const repairDetailApi = (id) => get(`/statisics/repair/detail/${id}`);
// 每日打印量统计
export const printDailyListApi = (data) =>
  post(`/statisics/dayCount/page`, data);
// 每日统计统计明细
export const printDailyDetailListApi = (id) =>
  get(`/statisics/dayCount/detail/${id}`);
// 每月打印量统计
export const printMonthListApi = (data) =>
  post(`/statisics/monthCount/page`, data);
// 重新统计月度销售数据统计
export const resetMonthAmountApi = (cycle) =>
  post(`/statisics/reCalculateMonthlySales`, cycle);
// 零件换件数量统计
export const printChangeReplaceListApi = (data) =>
  post(`/statisics/replace/page`, data);
// 零件更换明细
export const printChangeDetailListApi = (id) =>
  get(`/statisics/replace/detail/${id}`);
// 客户自修登记详情
export const printSelfDetailListApi = (code) =>
  get(`/self/repair-report/detailByCode/${code}`);
// 工单详情
export const printWorkDetailListApi = (code) =>
  get(`/work-order-pc/detailByCode/${code}`);
// 客户月度销售数据
export const customMonthSaleListApi = (data) =>
  post(`/statisics/salesMonthly/page`, data);
// 月度销售数据汇总
export const customMonthSaleSummaryListApi = (data) =>
  post(`/statisics/salesMonthly/summary`, data);

// 换件记录
export const changeReplaceListApi = (data) =>
  post(`/statisics/replace/detailPage`, data);
// 导出换件记录
export const changeReplaceExportApi = (data) =>
  postDown(`/statisics/replace/export`, data);
// 换件数据汇总
export const changeReplaceSummaryListApi = (data) =>
  post(`/statisics/replace/detailSummary`, data);
// 月销售统计
export const monthSaleListApi = (data) =>
  post(`/statisics/salesMonthly/salesPage`, data);
// 获取销售情况
export const getSaleDetailApi = () => get(`/statisics/getSalesData`);
// 客户成本核算统计表
export const customCostListApi = (data) =>
  post(`/statisics/singleCost/page`, data);
// 客户和工程师耗材仓库列表
export const customConsumableListApi = (data) =>
  post(`/customerItemStore/pagePc`, data);
// 客户仓库金额统计
export const customConsumableDetailListApi = (data) =>
  post(`/customerItemStore/total`, data);
// 导出客户仓库数据
export const customConsumableExportApi = (data) =>
  postDown(`/customerItemStore/export`, data);
// 工程师操作日志
export const engineerLogListApi = (data) =>
  post(`/engineerItemStore/logPageList`, data);
// 工程师仓库数据导出
export const engineerLogExportApi = (data) =>
  postDown(`/engineerItemStore/export`, data);

// ================================  包月维修  ============================
// 列表查询
export const monthRepairListApi = (data) => post(`/month-repair`, data);
// 重新计算包月费用
export const reCalculateMonthRepairApi = (data) =>
  post(`/month-repair/reCountIot`, data);
//抄表明细
export const monthRepairDetailListApi = (id) => get(`/month-repair/pc/${id}`);
// 对账单
export const receiptMonthRepairListApi = (data) =>
  post(`month-repair/receiptPage`, data);
//
