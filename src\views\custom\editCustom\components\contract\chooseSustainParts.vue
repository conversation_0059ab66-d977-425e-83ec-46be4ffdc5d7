<template>
  <div class="app-container">
    <!-- 选择机器/选配件 -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="70%"
      :top="'2%'"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProTable
        ref="ProTable"
        :row-key="(row) => row.id"
        :query-param="queryParam"
        :local-pagination="localPagination"
        :columns="columns"
        :data="tableData"
        :show-selection="true"
        :height="400"
        @loadData="loadData"
        @handleSelectionChange="handleSelectionChange"
      >
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { getMachinePageApi } from "@/api/store";
import { dictTreeByCodeApi, dictTreeByCodeApi2 } from "@/api/user";
import { partListApi } from "@/api/dispose";

export default {
  name: "ChooseDispatchMachine",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    selectedData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogTitle: "选择质保零件",
      queryParam: {},
      defaultQueryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 10,
      },
      columns: [
        {
          dataIndex: "ch",
          title: "零件中/英名称",
          isSearch: true,
          clearable: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "oemNumber",
          title: "原厂零件编号",
          isTable: true,
          isSearch: true,
          clearable: true,
          placeholder: "原厂零件编号（OEM）",
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "ch",
          title: "零件中文名称",
          isTable: true,
        },
        {
          dataIndex: "en",
          title: "零件英文名称",
          isTable: true,
        },

        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
          formatter: (row) => row.type.label,
          isForm: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formSpan: 24,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择物品小类",
              trigger: "change",
            },
          ],
        },
      ],
      tableData: [],
      selectionData: [],
      flag: false,
    };
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.ProTable.refresh();
          this.flag = true;
        });
      }
    },
  },
  mounted() {},
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      partListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          if (this.flag) {
            this.$refs.ProTable.$refs.ProElTable.clearSelection();
            if (this.selectedData.length) {
              this.selectedData.forEach((row) => {
                this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
                  row,
                  true
                );
              });
            }
          }
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
          this.flag = false;
        });
    },
    handleDialogOk() {
      this.$emit("confirm", this.selectionData);
    },
    handleDialogCancel() {
      this.$emit("update:dialogVisible", false);
    },
    handleSelectionChange(row) {
      this.selectionData = row;
    },
  },
};
</script>

<style scoped lang="scss"></style>
