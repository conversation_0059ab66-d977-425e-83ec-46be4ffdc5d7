<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 配置模板预览对话框组件
-->
<template>
  <el-dialog
    title="配置模板详情"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    :modal="false"
  >
    <div v-if="template" class="template-preview">
      <!-- 基本信息 -->
      <div class="preview-section">
        <h3 class="section-title">基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模板名称">
            {{ template.templateName }}
          </el-descriptions-item>
          <el-descriptions-item label="显示名称">
            {{ template.displayName }}
          </el-descriptions-item>
          <el-descriptions-item label="日志级别">
            <el-tag :type="getLogLevelType(template.logLevel)">
              {{ template.logLevel }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="配置版本" :span="2">
            {{ template.description }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 位置日志配置 -->
      <div class="preview-section">
        <h3 class="section-title">位置日志配置</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="启用状态">
            <el-tag :type="template.enableLocationLog ? 'success' : 'info'">
              {{ template.enableLocationLog ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="采集间隔">
            {{ formatInterval(template.locationLogInterval) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 日志管理配置 -->
      <div class="preview-section">
        <h3 class="section-title">日志管理配置</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="上传间隔">
            {{ formatInterval(template.logUploadInterval) }}
          </el-descriptions-item>
          <el-descriptions-item label="最大文件数">
            {{ template.maxLogFiles }} 个
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 配置预览 -->
      <div class="preview-section">
        <h3 class="section-title">配置预览</h3>
        <div class="config-preview">
          <pre>{{ formatConfigJson(template) }}</pre>
        </div>
      </div>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleAssign">分配此配置</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'ConfigTemplatePreview',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    template: {
      type: Object,
      default: null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
    },
    
    handleAssign() {
      this.$emit('assign', this.template)
      this.handleClose()
    },
    
    getLogLevelType(level) {
      const types = {
        'DEBUG': 'info',
        'INFO': 'success',
        'WARN': 'warning',
        'ERROR': 'danger'
      }
      return types[level] || ''
    },
    
    formatInterval(interval) {
      if (!interval) return '未设置'

      // 如果interval已经是秒为单位，直接处理
      let seconds = interval
      if (interval > 1000) {
        // 如果是毫秒为单位，转换为秒
        seconds = Math.floor(interval / 1000)
      }

      if (seconds < 60) return `${seconds}秒`

      const minutes = Math.floor(seconds / 60)
      if (minutes < 60) return `${minutes}分钟`

      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60

      if (remainingMinutes > 0) {
        return `${hours}小时${remainingMinutes}分钟`
      }
      return `${hours}小时`
    },
    
    formatConfigJson(template) {
      const config = {
        templateName: template.templateName,
        displayName: template.displayName,
        logLevel: template.logLevel,
        enableLocationLog: template.enableLocationLog,
        locationLogInterval: template.locationLogInterval,
        logUploadInterval: template.logUploadInterval,
        maxLogFiles: template.maxLogFiles,
        description: template.description
      }
      return JSON.stringify(config, null, 2)
    }
  }
}
</script>

<style lang="scss" scoped>
.template-preview {
  .preview-section {
    margin-bottom: 24px;
    
    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 8px;
    }
  }
  
  .config-preview {
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 16px;
    
    pre {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      line-height: 1.5;
      color: #606266;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
