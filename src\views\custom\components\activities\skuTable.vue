<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-28 13:18:14
 * @Description: 
 -->
<!--
 * @Description: 特价商品
 * @version: 
 * @Author: Lenle
 * @Date: 2025-02-11 11:19:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-28 13:18:14
-->
<template>
  <div>
    <el-button
      type="success"
      class="add-btn"
      size="mini"
      style="margin-bottom: 10px"
      icon="el-icon-plus"
      @click="addSaleRow()"
    >
      新增
    </el-button>
    <el-table
      :data="modelValue"
      style="width: 100%; margin-bottom: 20px"
      :height="400"
    >
      <el-table-column type="index" label="序号"> </el-table-column>
      <el-table-column prop="articleCode" label="商品编码"> </el-table-column>
      <el-table-column prop="name" label="商品名称"> </el-table-column>
      <el-table-column prop="saleUnitPrice" label="单价"> </el-table-column>
      <el-table-column prop="promotionPrice" label="促销价">
        <template slot-scope="scope">
          <el-input v-model="scope.row.promotionPrice" type="number"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="quantity" label="总数量">
        <template slot-scope="scope">
          <el-input v-model.number="scope.row.quantity"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="maxBuy" label="单个客户最多数量">
        <template slot-scope="scope">
          <el-input v-model.number="scope.row.maxBuy"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="minBuy" label="单个客户最低数量">
        <template slot-scope="scope">
          <el-input v-model.number="scope.row.minBuy"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="skuId" label="操作" width="80px">
        <template slot-scope="scope">
          <div class="fixed-width">
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="deleteRow(scope.row, scope.$index)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      custom-class="center-dialog"
      width="80vw"
      :visible.sync="skuVisible"
      append-to-body
      title="关联特价商品"
    >
      <div>
        <ProTable
          ref="ProTable"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :columns="SKUcolumns"
          show-pagination
          :local-pagination="localPagination"
          :data="tableData"
          sticky
          row-key="saleSkuId"
          :height="400"
          :show-selection="true"
          :table-row-class-name="dialogTableClass"
          :query-param="queryParam"
          @loadData="loadData"
          @handleSelectionChange="selectStu"
        >
          <template #btn> 商品列表 </template>
          <template #type="slotProps">
            {{ slotProps.row.type.label }}
          </template>
          <template #picsUrl="{ row }">
            <el-image
              v-if="row.picsUrl && row.picsUrl.length > 0"
              style="max-width: 100px; max-height: 100px"
              :src="getPicsUrlImg(row)"
              :preview-src-list="[getPicsUrlImg(row)]"
            >
            </el-image>
          </template>
          <template #saleAttrVals="slotProps">
            <span
              v-for="(item, index) in slotProps.row.saleAttrVals"
              :key="index"
              style="border: 1px solid #ddd"
              >{{ item.name }}: {{ item.val }}
            </span>
          </template>

          <template #saleStatus="slotProps">
            {{ slotProps.row.saleStatus ? "已上架" : "未上架" }}
          </template>
        </ProTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="skuVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveStuData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { cloneDeep } from "lodash";
import { itemSummaryListApi } from "@/api/goods";
import { dictTreeByCodeApi, dictTreeByCodeApi2 } from "@/api/user";
export default {
  name: "SkuTable",
  components: {},
  model: {
    prop: "modelValue",
    event: "change",
  },
  props: {
    modelValue: {
      type: Array,
      default: () => [],
    },
    activityId: {
      default: 0,
      type: [String, Number],
    },
  },
  data() {
    return {
      skuVisible: false,
      tableData: [],
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      queryParam: {},
      SKUcolumns: [
        {
          dataIndex: "lastIds",
          title: "适用机型",
          isSearch: true,
          clearable: true,
          valueType: "product",
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isSearch: true,
          isTable: true,
          valueType: "input",
          clearable: true,
          minWidth: 150,
          width: 180,
        },

        {
          dataIndex: "itemCode",
          title: "商品编号",
          isSearch: true,
          valueType: "input",
          clearable: true,
          minWidth: 150,
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          width: 150,
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          width: 120,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          formSpan: 16,
          width: 180,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
        },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
          formatter: (row) => row.type?.label,
        },
        {
          dataIndex: "productPartTypeList",
          title: "物品小类",
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
      ],
      newStuTable: [],
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    //添加促销商品
    addSaleRow() {
      this.skuVisible = true;
      this.queryParam = {};
      this.$nextTick(() => {
        this.$refs.ProTable.refresh();
      });
    },
    //加载表格
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const requestParameters = cloneDeep(this.queryParam);
      itemSummaryListApi({ ...requestParameters, saleStatus: "ON_SALE" })
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },

    selectStu(selectedRows) {
      this.newStuTable = selectedRows;
    },
    //保存新增的促销商品
    saveStuData() {
      let stuData = cloneDeep(this.newStuTable);
      let oldData = [];
      if (this.modelValue && this.modelValue.length) {
        oldData = this.modelValue.map((o) => o.skuId);
      } else {
        this.$emit("change", []);
      }
      if (stuData && stuData.length) {
        //排除原有的
        stuData = stuData.filter((o) => {
          return !oldData.includes(o.saleSkuId);
        });
        const resultList = cloneDeep(this.modelValue);
        stuData.forEach((o) => {
          resultList.push({
            activityId: this.activityId || 0,
            articleCode: o.articleCode,
            name: o.itemName,
            maxBuy: 1,
            minBuy: 1,
            promotionPrice: 0,
            quantity: 0,
            saleUnitPrice: o.saleUnitPrice,
            skuId: o.saleSkuId,
          });
        });
        this.$emit("change", resultList);
      }
      this.newStuTable = [];
      this.skuVisible = false;
    },
    deleteRow(row, index) {
      const resultList = cloneDeep(this.modelValue);
      resultList.splice(index, 1);
      // resultList = resultList.filter((o) => o.skuId != row.skuId);
      this.$emit("change", resultList);
    },
    dialogTableClass({ row }) {
      let list = cloneDeep(this.modelValue);
      if (list.length) {
        list = list.map((o) => o.skuId);
      }
      if (list && list.length && list.includes(row.saleSkuId)) {
        return "hide-select";
      } else {
        return "";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
:deep(.center-dialog) {
  .el-dialog__body {
    padding-top: 10px;
    .el-table__body-wrapper {
      overflow: auto;
    }
  }
  &.el-dialog {
    top: 50%;
    transform: translate(0, -50%);
    margin-top: 0 !important;
  }
  .hide-select .el-checkbox {
    display: none;
  }
}
:deep(.dialog-proTable) {
  .table-page-search-submitButtons {
    display: inline-block;
    width: auto;
    margin-top: 0;
  }
  #search-form-wrap {
    width: calc(100% - 150px);
    display: inline-block;
    vertical-align: middle;
  }
}
</style>
