<!--
 * @Author: wskg
 * @Date: 2024-08-14 19:43:11
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 15:52:18
 * @Description: 供应商退款
 -->
<template>
  <ProDrawer
    :value="showDrawer"
    size="80%"
    :title="drawerTitle"
    :confirm-text="'确认退款'"
    :no-footer="methodType === 'info'"
    @ok="handleDrawerOk"
    @cancel="handleCloseDrawer"
  >
    <ProForm
      ref="ProForm"
      :form-param="form"
      :form-list="formColumns"
      :confirm-loading="formLoading"
      :layout="{ formWidth: '100%', labelWidth: '110px' }"
      :open-type="methodType"
    >
      <template #orderDetail>
        <div class="title-box" style="margin: 0">订单详情</div>
        <ProTable
          ref="ProTable"
          :columns="columns"
          :data="tableData"
          :show-loading="false"
          :show-search="false"
          :show-setting="false"
          :height="300"
        ></ProTable>
      </template>
      <template #licenseImg>
        <div class="license-img">
          <ProUpload
            :file-list="form.picUrls"
            :type="methodType"
            :limit="2"
            :drag="true"
            @uploadSuccess="handleLicenseImgUploadSuccess"
            @uploadRemove="handleLicenseImgUploadRemove"
          />
        </div>
      </template>
    </ProForm>
  </ProDrawer>
</template>

<script>
import ProUpload from "@/components/ProUpload/index.vue";
import { getReturnDetailApi, providerRefundApi } from "@/api/manufacturer";
import { Message } from "element-ui";

export default {
  name: "Drawback",
  components: { ProUpload },
  data() {
    return {
      methodType: "edit",
      showDrawer: false,
      columns: [
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "buyNumber",
          title: "购买数量",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "退货金额",
          isTable: true,
        },
        {
          dataIndex: "realAmount",
          title: "实付金额",
          isTable: true,
        },
      ],
      tableData: [],
      form: {},
      formColumns: [
        // {
        //   dataIndex: "code",
        //   title: "退货单号",
        //   isForm: true,
        //   formSpan: 6,
        //   valueType: "text",
        // },
        {
          dataIndex: "manufacturerOrderCode",
          title: "订单编号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "orderAmount",
          title: "订单金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "payAmount",
          title: "支付金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "createdAt",
          title: "申请时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "refundType",
          title: "退款类型",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "refundStatus",
          title: "退货状态",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },

        {
          dataIndex: "amount",
          title: "申请退款金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "remark",
          title: "申请原因",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorTime",
          title: "下单时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "receiveByName",
          title: "退货收货人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "receiveAt",
          title: "退货收货时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "orderDetail",
          title: "订单详情",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "orderDetail",
        },
        {
          dataIndex: "initiatorName",
          title: "下单人",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "initiatorPhone",
          title: "下单手机号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "account",
          title: "收款公司",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "bankAccount",
          title: "退货收款账号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "bank",
          title: "开户行",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "bankClient",
          title: "开户网点",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "serialNumber",
          title: "付款流水号",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入付款流水号",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "picUrls",
          title: "付款凭证",
          isForm: true,
          formSpan: 24,
          formSlot: "licenseImg",
          prop: [
            {
              required: true,
              message: "请上传付款凭证",
              trigger: "blur",
            },
          ],
        },
      ],
      formLoading: false,
      drawerTitle: "退款",
    };
  },
  methods: {
    show(row, type) {
      this.methodType = type;
      this.drawerTitle =
        row.refundStatus?.value === "SUCCESS"
          ? `退款详情 - ${row.code}`
          : `退款 - ${row.code}`;
      getReturnDetailApi(row.id).then((res) => {
        const data = res.data;
        this.form = {
          ...data,
          refundStatus: data.refundStatus?.label,
          refundType: data.refundType?.label,
        };
        this.tableData = this.form.manufacturerReturnGoods;
      });
      this.showDrawer = true;
    },
    // 确认退款
    handleDrawerOk() {
      this.$refs.ProForm.handleSubmit().then((res) => {
        this.$confirm("是否确认退款", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          const params = {
            id: this.form.id,
            picUrls: this.form.picUrls,
            serialNumber: this.form.serialNumber,
          };
          providerRefundApi(params).then((res) => {
            Message.success("退款成功");
            this.handleCloseDrawer();
            this.$emit("refresh");
          });
        });
      });
    },
    handleLicenseImgUploadSuccess(result) {
      if (!this.form.picUrls) {
        this.$set(this.form, "picUrls", []);
      }
      this.form.picUrls.push(result);
    },
    handleLicenseImgUploadRemove(file) {
      const index = this.form.picUrls.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      this.form.picUrls.splice(index, 1);
    },
    handleCloseDrawer() {
      this.form = {};
      this.showDrawer = false;
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .license-img {
  .el-upload {
    .el-upload-dragger {
      border: none;
    }
  }
}
</style>
