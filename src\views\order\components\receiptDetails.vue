<!--
 * @Author: wskg
 * @Date: 2025-02-18 16:27:13
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:48
 * @Description: 对账单明细
 -->
<template>
  <div class="container">
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="80%"
      :no-footer="true"
      @ok="handleDrawerOk"
      @cancel="handleDrawerCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParam"
        :form-list="formColumns"
        :open-type="editType"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
      >
        <template #status>
          {{ formParam.status?.label }}
        </template>
        <!--<template #refreshAction>-->
        <!--  <el-button-->
        <!--    type="success"-->
        <!--    icon="el-icon-refresh"-->
        <!--    size="mini"-->
        <!--    @click="handleRefreshAction"-->
        <!--  >-->
        <!--    重新计算-->
        <!--  </el-button>-->
        <!--</template>-->
        <template #totalPoint> {{ formParam?.totalPoint || 0 }} </template>
        <template #totalExclude> {{ formParam?.totalExclude || 0 }} </template>
        <template #deductionPoint>
          {{ formParam?.deductionPoint || 0 }}
        </template>
        <template #givePoint>
          {{ formParam?.givePoint || 0 }}
        </template>
        <!-- 抄表设备组信息 -->
        <template #deviceGroup>
          <ProTable
            ref="ProTable"
            :show-search="false"
            :show-loading="false"
            :show-setting="false"
            :show-pagination="false"
            :height="450"
            :columns="columns"
            :data="tableData"
          >
            <template #contract="{ row }">
              <el-button type="text" @click="handleContract(row)">
                查看合约
              </el-button>
            </template>
            <template #action="{ row }">
              <div class="fixed-width">
                <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
                  查看
                </el-button>
                <!--v-if="editType !== 'info' && !row.settmentStatus"-->
                <el-button
                  v-if="editType !== 'info' && row.settmentStatus !== 2"
                  icon="el-icon-edit"
                  @click="handleEdit(row, 'edit')"
                >
                  编辑
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 编辑单台设备组信息 -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="70%"
      top="2%"
      confirm-text="确认修改"
      :no-footer="methodType === 'info'"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="dialogProForm"
        :form-param="dialogForm"
        :form-list="dialogFormColumns"
        :open-type="methodType"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
      >
        <template #deviceGroup>
          {{ dialogForm.deviceGroup?.label || "" }}
        </template>
        <template #deductionPoint>
          {{ dialogForm?.deductionPoint || 0 }}
        </template>
        <template #giveCount>
          {{ dialogForm?.giveCount || 0 }}
        </template>
        <template #picUrls>
          <div class="title-box">计数器图片</div>
          <div class="counter-pic">
            <el-image
              v-for="item in dialogForm.picUrls"
              :key="item.key"
              fit="fit"
              style="width: 120px; height: 120px"
              :src="item.url"
              :preview-src-list="[item.url]"
            ></el-image>
          </div>
        </template>
      </ProForm>
    </ProDialog>
    <!-- 购机合约明细 -->
    <BuyMachineInfo ref="buyMachineInfo" />
    <!-- 租赁合约明细 -->
    <RentContractInfo ref="rentContractInfo" />
    <!-- 融资合约明细 -->
    <FinancingContractInfo ref="financingContractInfo" />
    <!-- 抄表合约明细 -->
    <FullHalfContractInfo ref="fullHalfContractInfo" />
  </div>
</template>

<script>
import { meterUpdateApi, receiptDetailApi } from "@/api/statisics";
import { cloneDeep } from "lodash";
import { getCustomerContractDetailApi } from "@/api/customer";

export default {
  name: "ReceiptDetails",
  components: {
    BuyMachineInfo: () =>
      import(
        "@/views/custom/editCustom/components/contract/buyMachineInfo.vue"
      ),
    RentContractInfo: () =>
      import(
        "@/views/custom/editCustom/components/contract/rentContractInfo.vue"
      ),
    FinancingContractInfo: () =>
      import(
        "@/views/custom/editCustom/components/contract/financingContractInfo.vue"
      ),
    FullHalfContractInfo: () =>
      import(
        "@/views/custom/editCustom/components/contract/fullHalfContractInfo.vue"
      ),
  },
  data() {
    return {
      rowId: "",
      drawerVisible: false,
      drawerTitle: "",
      editType: "info",
      // form
      formParam: {},
      formColumns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        // {
        //   dataIndex: "cycle",
        //   title: "抄表月份",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 6,
        // },
        {
          dataIndex: "totalPoint",
          title: "总印量",
          isForm: true,
          // valueType: "text",
          formSlot: "totalPoint",
          formSpan: 6,
        },
        {
          dataIndex: "totalExclude",
          title: "扣减废张",
          isForm: true,
          // valueType: "text",
          formSlot: "totalExclude",
          formSpan: 6,
        },
        {
          dataIndex: "deductionPoint",
          title: "抵扣印量",
          isForm: true,
          // valueType: "text",
          formSlot: "deductionPoint",
          formSpan: 6,
        },
        {
          dataIndex: "givePoint",
          title: "赠送印量",
          isForm: true,
          // valueType: "text",
          formSlot: "givePoint",
          formSpan: 6,
        },
        {
          dataIndex: "totalAmount",
          title: "总抄表费",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "amount",
          title: "应付金额",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "paidAmount",
          title: "实付金额",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "status",
          title: "付款状态",
          isForm: true,
          valueType: "text",
          formSlot: "status",
          formSpan: 6,
        },
        {
          dataIndex: "createdAt",
          title: "付款时间",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        // {
        //   dataIndex: "refreshAction",
        //   title: "重新计算",
        //   isForm: true,
        //   formOtherSlot: "refreshAction",
        //   formSpan: 6,
        // },
        {
          dataIndex: "deviceGroup",
          title: "设备组信息",
          isForm: true,
          formOtherSlot: "deviceGroup",
          formSpan: 24,
        },
      ],
      // 设备组信息
      columns: [
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          formatter: (row) => row.deviceGroup?.label,
          minWidth: 100,
        },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isTable: true,
          formatter: (row) => `${row.brand ?? ""}/${row.machine ?? ""}`,
          minWidth: 120,
        },
        {
          dataIndex: "cycle",
          title: "抄表日期",
          isTable: true,
        },
        {
          dataIndex: "blackWhitePoint",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorPoint",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "totalPoint",
          title: "总印量",
          isTable: true,
        },
        {
          dataIndex: "contract",
          title: "抄表合约",
          isTable: true,
          tableSlot: "contract",
        },
        {
          dataIndex: "totalAmount",
          title: "总抄表费",
          isTable: true,
        },
        {
          dataIndex: "derateAmount",
          title: "减免费用",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "应付金额",
          isTable: true,
        },
        {
          dataIndex: "endTime",
          title: "截止时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "settmentStatus",
          title: "付款状态",
          isTable: true,
          formatter: (row) => {
            switch (row.settmentStatus) {
              case 0:
                return "未结算";
              case 1:
                return "结算中";
              case 2:
                return "已结算";
              default:
                return "";
            }
          },
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 140,
        },
      ],
      tableData: [],
      methodType: "info", // Table类型
      // 设备组信息
      dialogVisible: false,
      dialogTitle: "",
      dialogForm: {},
      dialogFormColumns: [
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isForm: true,
          valueType: "text",
          formSlot: "deviceGroup",
          formSpan: 8,
        },
        {
          dataIndex: "beginTime",
          title: "期初时间",
          isForm: true,
          // valueType: "text",
          valueType: "date-picker",
          pickerType: "date",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
        },
        {
          dataIndex: "endTime",
          title: "期末时间",
          isForm: true,
          // valueType: "text",
          valueType: "date-picker",
          pickerType: "date",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
        },
        {
          dataIndex: "blackWhitePrice",
          title: "黑白单价",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "colorPrice",
          title: "彩色单价",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "fiveColourPrice",
          title: "五色单价",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "blackWhiteInception",
          title: "期初黑白计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "colorInception",
          title: "期初彩色计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "fiveColourIncption",
          title: "期初五色计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },

        {
          dataIndex: "blackWhiteCutoff",
          title: "期末黑白计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "colorCutoff",
          title: "期末彩色计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "fiveColourCutoff",
          title: "期末五色计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "blackWhiteExclude",
          title: "扣减黑白废张",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "colorExclude",
          title: "扣减彩色废张",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "fiveColourExclude",
          title: "扣减五色废张",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "blackWhitePoint",
          title: "黑白总印量",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "colorPoint",
          title: "彩色总印量",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "fiveColourPoint",
          title: "五色总印量",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "totalPoint",
          title: "总印量",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "deductionPoint",
          title: "抵扣印量",
          isForm: true,
          // valueType: "text",
          formSlot: "deductionPoint",
          formSpan: 8,
        },
        {
          dataIndex: "giveCount",
          title: "赠送印量",
          isForm: true,
          // valueType: "text",
          formSlot: "giveCount",
          formSpan: 8,
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "derateAmount",
          title: "减免金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "amount",
          title: "应付金额",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "picUrls",
          title: "计数器照片",
          isForm: true,
          formOtherSlot: "picUrls",
          formSpan: 24,
        },
      ],
    };
  },
  mounted() {},
  methods: {
    async show(row, type) {
      this.editType = type;
      this.drawerTitle = `${row.customerName} - 抄表对账`;
      this.rowId = row.id;
      // this.formParam = cloneDeep(row);
      await this.getFormParamDetail();
      this.drawerVisible = true;
    },
    handleEdit(row, type) {
      this.methodType = type;
      this.dialogForm = cloneDeep(row);
      this.dialogTitle = `${row.deviceGroup.label} - 抄表数据明细`;
      this.dialogVisible = true;
    },
    async handleDialogOk() {
      const result = await meterUpdateApi(this.dialogForm);
      if (result.code === 200) {
        this.formParam = {};
        this.tableData = [];
        this.$message.success("修改成功");
        await this.getFormParamDetail();
        this.dialogVisible = false;
      }
    },
    // 查看合约
    async handleContract(row) {
      if (!row.contractItemCode) {
        this.$message.error("该设备未关联合约");
        return;
      }
      try {
        const result = await getCustomerContractDetailApi(row.contractItemCode);
        if (result.code === 200) {
          const contractInfo = result.data;
          if (contractInfo.contractType.value === "1201") {
            this.$refs.buyMachineInfo.visible(contractInfo, "info");
          } else if (contractInfo.contractType.value === "1202") {
            this.$refs.fullHalfContractInfo.visible(contractInfo, "info");
          } else if (contractInfo.contractType.value === "1265") {
            this.$refs.rentContractInfo.visible(contractInfo, "info");
          } else if (contractInfo.contractType.value === "1230") {
            this.$refs.financingContractInfo.visible(contractInfo, "info");
          }
        }
      } catch (e) {
        console.error(e.message);
      }
    },
    handleDialogCancel() {
      this.dialogVisible = false;
      this.$nextTick(() => {
        this.dialogForm = {};
      });
    },
    handleDrawerOk() {
      this.drawerVisible = false;
    },
    handleDrawerCancel() {
      this.drawerVisible = false;
      this.$nextTick(() => {
        this.formParam = {};
        this.tableData = [];
      });
    },
    async getFormParamDetail() {
      try {
        const result = await receiptDetailApi(this.rowId);
        if (result.code === 200) {
          this.formParam = result.data;
          console.log("this.formParam", this.formParam);
          this.tableData = this.formParam.iotPrintCounts || [];
        }
      } catch (e) {
        this.formParam = {};
        this.tableData = [];
      }
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep.counter-pic {
  width: 100%;
  display: flex;
  gap: 20px;
  .el-image {
    width: 200px;
    height: 200px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
