# 📋 后端API接口规范

## 🎯 概述

本文档定义了日志控制模块前端所需的后端API接口规范，基于《后台控制页面实施指导方案.md》中的API设计。前端已完成实施，现需要后端提供以下接口。

## 🔗 API接口列表

### 📊 配置管理API

#### 1.1 获取配置模板
**接口地址：** `GET /logcontrol/config/templates`

**响应格式：**
```javascript
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "templateName": "DEBUG_TEMPLATE",
      "displayName": "调试模板",
      "description": "用于开发调试的日志配置",
      "logLevel": "DEBUG",
      "enableLocationLog": true,
      "logUploadInterval": 30000
    }
  ]
}
```

#### 1.2 获取配置分配情况
**接口地址：** `GET /logcontrol/config/assignments`

**请求参数：**
```javascript
{
  targetType: "USER",      // 目标类型：USER/DEVICE
  keyword: "关键词",        // 搜索关键词
  page: 1,                // 页码
  size: 20                // 每页数量
}
```

**响应格式：**
```javascript
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "targetType": "USER",
        "targetId": "USER001",
        "targetName": "用户001",
        "configName": "调试配置",
        "logLevel": "DEBUG",
        "assignTime": "2025-01-25 10:00:00"
      }
    ],
    "total": 100,
    "current": 1,
    "size": 20
  }
}
```

#### 1.3 批量分配配置
**接口地址：** `POST /logcontrol/config/assign-batch`

**请求体：**
```javascript
{
  "configId": "CONFIG001",
  "targets": [
    {
      "targetType": "USER",
      "targetId": "USER001"
    },
    {
      "targetType": "DEVICE",
      "targetId": "DEV001"
    }
  ]
}
```

**响应格式：**
```javascript
{
  "code": 200,
  "message": "success",
  "data": {
    "success": 8,    // 成功数量
    "failed": 2      // 失败数量
  }
}
```

### 📈 统计数据API

#### 2.1 获取仪表板统计数据
**接口地址：** `GET /logcontrol/stats/dashboard`

**响应格式：**
```javascript
{
  "code": 200,
  "message": "success",
  "data": {
    "activeDevices": 3,      // 活跃设备数
    "todayLogs": 45,         // 今日日志数
    "crashEvents": 7,        // 崩溃事件数
    "configAssignments": 25  // 配置分配数量
  }
}
```

#### 2.2 获取日志趋势数据
**接口地址：** `GET /logcontrol/stats/log-trend`

**请求参数：**
```javascript
{
  days: 7  // 天数，默认7天
}
```

**响应格式：**
```javascript
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "date": "2025-01-25",
      "logCount": 120,
      "crashCount": 2
    }
  ]
}
```

#### 2.3 获取设备状态分布
**接口地址：** `GET /logcontrol/stats/device-status`

**响应格式：**
```javascript
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "status": "online",
      "count": 2,
      "percentage": 66.7
    },
    {
      "status": "offline",
      "count": 1,
      "percentage": 33.3
    }
  ]
}
```

### 📋 日志分析API

#### 3.1 获取日志列表（分页）
**接口地址：** `GET /logcontrol/logs`

**请求参数：**
```javascript
{
  page: 1,              // 页码，从1开始
  size: 20,             // 每页数量
  dateRange: [          // 时间范围（可选）
    "2025-01-01 00:00:00",
    "2025-01-31 23:59:59"
  ],
  logLevel: "INFO",     // 日志级别（可选）：DEBUG/INFO/WARN/ERROR
  deviceId: "DEV001",   // 设备ID（可选）
  userId: "USER001",    // 用户ID（可选）
  keyword: "关键词"      // 搜索关键词（可选）
}
```

**响应格式：**
```javascript
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "logLevel": "INFO",
        "deviceId": "DEV001",
        "userId": "USER001",
        "message": "系统启动成功",
        "createTime": "2025-01-25 10:00:00",
        "details": null  // 详细信息（可选，JSON字符串）
      }
    ],
    "total": 625,        // 总记录数
    "current": 1,        // 当前页码
    "size": 20           // 每页数量
  }
}
```

#### 3.2 获取日志详情
**接口地址：** `GET /logcontrol/logs/{logId}`

**路径参数：**
- `logId`: 日志ID

**响应格式：**
```javascript
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "logLevel": "ERROR",
    "deviceId": "DEV001",
    "userId": "USER001",
    "message": "数据库连接异常",
    "createTime": "2025-01-25 10:00:00",
    "details": "{\"stackTrace\":\"Error at line 123\",\"errorCode\":\"E001\"}"
  }
}
```

#### 3.3 导出日志
**接口地址：** `GET /logcontrol/logs/export`

**请求参数：**
```javascript
{
  // 与日志列表相同的过滤参数
  dateRange: [...],
  logLevel: "INFO",
  deviceId: "DEV001",
  userId: "USER001",
  keyword: "关键词"
}
```

**响应格式：**
- Content-Type: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- 返回Excel文件的二进制数据

### 🖥️ 设备管理API

#### 4.1 获取设备列表
**接口地址：** `GET /logcontrol/devices`

**请求参数：**
```javascript
{
  page: 1,        // 页码（可选）
  size: 20,       // 每页数量（可选）
  keyword: "关键词" // 搜索关键词（可选）
}
```

**响应格式：**
```javascript
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "DEV001",
      "name": "设备001",
      "deviceId": "DEV001",
      "status": "online",
      "lastActiveTime": "2025-01-25 10:00:00"
    }
  ]
}
```

#### 4.2 获取设备详情
**接口地址：** `GET /logcontrol/devices/{deviceId}`

### 👥 用户管理API

#### 5.1 获取用户列表
**接口地址：** `GET /logcontrol/users`

**请求参数：**
```javascript
{
  page: 1,        // 页码（可选）
  size: 20,       // 每页数量（可选）
  keyword: "关键词" // 搜索关键词（可选）
}
```

**响应格式：**
```javascript
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "USER001",
      "name": "用户001",
      "username": "user001",
      "status": "active",
      "lastLoginTime": "2025-01-25 10:00:00"
    }
  ]
}
```

### 💥 崩溃分析API

#### 6.1 获取崩溃列表
**接口地址：** `GET /logcontrol/crashes`

**请求参数：**
```javascript
{
  page: 1,
  size: 20,
  deviceId: "DEV001",    // 设备ID（可选）
  userId: "USER001",     // 用户ID（可选）
  dateRange: [...]       // 时间范围（可选）
}
```

**响应格式：**
```javascript
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "deviceId": "DEV001",
        "userId": "USER001",
        "crashType": "EXCEPTION",
        "message": "NullPointerException",
        "stackTrace": "...",
        "createTime": "2025-01-25 10:00:00"
      }
    ],
    "total": 7,
    "current": 1,
    "size": 20
  }
}
```

## 📝 数据库表结构参考

根据实施指导方案，相关数据库表：

### b_log_entry（日志条目表）
```sql
CREATE TABLE b_log_entry (
  id BIGINT PRIMARY KEY,
  log_level VARCHAR(10),     -- DEBUG/INFO/WARN/ERROR
  device_id VARCHAR(50),     -- 设备ID
  user_id VARCHAR(50),       -- 用户ID
  message TEXT,              -- 日志内容
  details TEXT,              -- 详细信息（JSON格式）
  create_time DATETIME       -- 创建时间
);
```

### b_device_info（设备信息表）
```sql
CREATE TABLE b_device_info (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100),         -- 设备名称
  device_id VARCHAR(50)      -- 设备ID
);
```

### st_user_basic（用户基础表）
```sql
CREATE TABLE st_user_basic (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100),         -- 用户名称
  username VARCHAR(50)       -- 用户账号
);
```

## 🔧 实现建议

### 1. 分页查询优化
- 使用索引优化查询性能
- 支持按时间范围快速查询
- 考虑大数据量的分页性能

### 2. 搜索功能
- 关键词搜索支持模糊匹配
- 多条件组合查询
- 时间范围查询优化

### 3. 统计数据
- 可以使用缓存提高统计查询性能
- 今日日志数可以按日期索引快速查询
- 活跃设备数可以基于最近活动时间统计

### 4. 导出功能
- 支持Excel格式导出
- 考虑大数据量导出的性能和内存使用
- 可以异步处理大文件导出

### 5. 错误处理
- 统一的错误响应格式
- 适当的HTTP状态码
- 详细的错误信息

## 🚀 测试建议

### 1. 接口测试
- 使用Postman或类似工具测试各个接口
- 验证参数校验和错误处理
- 测试边界条件和异常情况

### 2. 性能测试
- 大数据量查询性能测试
- 并发访问测试
- 导出功能性能测试

### 3. 集成测试
- 前后端联调测试
- 完整业务流程测试
- 用户体验测试

## 📞 联调支持

前端已完成实施，API接口已切换到真实调用。如需联调支持：

1. **接口调试**：可以通过浏览器开发者工具查看请求参数和响应
2. **错误排查**：前端有完善的错误处理和日志输出
3. **数据格式**：如需调整数据格式，可以在前端进行适配

期待与后端团队的顺利对接！🤝
