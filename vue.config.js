/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 16:59:45
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-07 09:32:48
 * @Description:
 */

const { defineConfig } = require("@vue/cli-service");
const isDevelopment = process.env.NODE_ENV === "development";
const devServer = require("./src/config/devServer");
const path = require("path");
const webpack = require("webpack");
const setting = require("./src/config/setting");

const { title, viewportVw, viewportRem } = setting;
// const {
//   base,
//   publicDir,
//   outDir,
//   assetsDir,
//   sourcemap,
//   cssCodeSplit,
//   host,
//   port,
//   strictPort,
//   open,
//   cors,
//   brotliSize,
//   logLevel,
//   clearScreen,
//   drop_console,
//   drop_debugger,
//   chunkSizeWarningLimit,
// } = setting;

module.exports = defineConfig({
  transpileDependencies: true, //解决第一行代码爆红的现象
  lintOnSave: false, //设置是否在开发环境下每次保存代码时都启用 eslint验证。
  publicPath: "./",
  assetsDir: "assets",
  productionSourceMap: false, //生成sourcemap文件 map文件相当于是查看源码的一个东西
  // runtimeCompiler: true,
  // parallel: require('os').cpus().length > 1,
  pages: {
    index: {
      entry: "src/main.js", // page 的入口
      template: "public/index.html", // 模板来源
      filename: "index.html", // 在 dist中生成的html 的文件名
      title: title, // template 中的 <title><%= htmlWebpackPlugin.options.title %></title>
      chunks: ["chunk-vendors", "chunk-common", "index"], // 在这个页面中要包含的块
    },
    // barTemperature: {
    //   entry: 'src/pages/bar-temperature/main.js',
    //   template: 'public/subpage.html',
    //   filename: 'bar-temperature.html'
    // },
  },
  css: {
    loaderOptions: {
      postcss: {
        postcssOptions: {
          plugins: [
            [
              //页面自适应转换px=>rem的配置 同时配合main.js引入rem.js
              "postcss-pxtorem",
              viewportRem,
            ],

            // [//页面自适应转换px=>vw的配置
            //   'postcss-px-to-viewport', viewportVw
            // ]
          ],
        },
      },
    },
  },
  devServer: {
    ...devServer,
    client: {
      overlay: false,
    },
  },
  chainWebpack: (config) => {
    const oneOfsMap = config.module.rule("scss").oneOfs.store;
    oneOfsMap.forEach((item) => {
      item
        .use("sass-resources-loader")
        .loader("sass-resources-loader")
        .options({
          resources: ["./src/assets/styles/variables.scss"],
        })
        .end();
    }); // 2022.5.10 新增全局sass变量配置
    config.resolve.symlinks(false);
    config
      .plugin("ignore")
      .use(
        new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /zh-cn$/)
      );

    config.resolve.alias.set("@", path.resolve(__dirname, "src"));

    config.optimization.sideEffects(false);
  },
  // css: {
  //   loaderOptions: {
  //     scss: {
  //       prependData: '@import "~@/themes/handle.scss";'
  //     }
  //   }
  // },
  configureWebpack: {
    // 2025.7.4 新增配置打包文件名
    output: {
      filename: `js/[name]-[contenthash:8].js`,
      chunkFilename: `js/[name]-[contenthash:8].js`,
    },
    module: {
      unknownContextCritical: false,
      // rules: [{
      //   test: /\.js$/,
      //   use: {
      //     loader: '@open-wc/webpack-import-meta-loader'
      //   }
      // }]
    },
    plugins: [],
  },
});
