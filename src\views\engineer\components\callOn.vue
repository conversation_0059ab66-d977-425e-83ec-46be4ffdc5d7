<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 11:22:39
 * @Description: 
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <div v-if="type === 'detail'" class="title-box-right">
          <div>总条目数：{{ localPagination.total }}</div>
        </div>
        <div v-if="type === 'stat'" class="title-box-right">
          <div>总条目数：{{ localPagination.total }}</div>
          <div>技术回访：{{ totalData.returnCount || 0 }}</div>
          <div>客户答疑：{{ totalData.questionCount || 0 }}</div>
          <div>维修工单：{{ totalData.workCount || 0 }}</div>
          <div>活动通知：{{ totalData.activityCount || 0 }}</div>
          <div>销售拜访：{{ totalData.saleCount || 0 }}</div>
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <el-button icon="el-icon-edit" @click="handleEdit(row, 'edit')">
            编辑
          </el-button>
          <el-button
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="showDrawer"
      :title="drawerTitle"
      size="65%"
      :confirm-loading="confirmLoading"
      :no-footer="methodType === 'info'"
      @ok="handleOk"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
      >
        <template #callImgs>
          <ProUpload
            :file-list="form.callImgs"
            :type="methodType"
            :limit="5"
            :multiple="true"
            @uploadSuccess="handleCallImgsUploadSuccess"
            @uploadRemove="handleCallImgsUploadRemove"
          />
        </template>
      </ProForm>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { dictTreeByCodeApi, userListApi } from "@/api/user";
import ProUpload from "@/components/ProUpload/index.vue";
import {
  delVisitDetailApi,
  updateVisitDetailApi,
  visitDataApi,
  visitDataStatApi,
  visitDetailApi,
  visitStatApi,
} from "@/api/repair";
import { cloneDeep } from "lodash";
import { Message } from "element-ui";

export default {
  name: "CallOn",
  components: { ProUpload },
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "detail",
    },
  },
  data() {
    return {
      methodType: "info",
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      showDrawer: false,
      drawerTitle: "查看 - ",
      confirmLoading: false,
      form: {},
      formColumns: [
        // {
        //   dataIndex: "customerName",
        //   title: "客户名字",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 8,
        // },
        {
          dataIndex: "reachShopRole",
          title: "受访角色",
          isForm: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "reachShopName",
          title: "受访人名字",
          isTable: true,
          // tableSlot: "reachShopRole",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "reachShopTel",
          title: "受访人电话",
          isForm: true,
          valueType: "input",
          disabled: false,
          clearable: true,
          formSpan: 8,
        },

        {
          dataIndex: "callType",
          title: "拜访方式",
          isForm: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(3100),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "callGoal",
          title: "拜访目的",
          isForm: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(2400),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "reachShopTime",
          title: "到店时间",
          isForm: true,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
        },

        {
          dataIndex: "remark",
          title: "拜访内容",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          clearable: true,
          autosize: {
            minRows: 3,
            maxRows: 6,
          },
        },
        {
          dataIndex: "nextNoticeRemark",
          title: "下次注意事项",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          clearable: true,
          autosize: {
            minRows: 3,
            maxRows: 6,
          },
        },
        {
          dataIndex: "optUserName",
          title: "拜访人名称",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        // {
        //   dataIndex: "operatName",
        //   title: "员工名称",
        //   isTable: true,
        //   isForm: true,
        //   tableSlot: "operatName",
        //   valueType: "select",
        //   option: [],
        //   formSpan: 8,
        // },
        // {
        //   dataIndex: "operatRoleName",
        //   title: "员工角色",
        //   isTable: true,
        //   disabled: false,
        //   clearable: true,
        //   formatter: (row) =>
        //     row.operatRoleName ? JSON.parse(row.operatRoleName).join("、") : "",
        // },
        // {
        //   dataIndex: "optUserName",
        //   title: "操作员",
        //   isTable: true,
        //   valueType: "input",
        //   disabled: false,
        //   clearable: true,
        //   formSpan: 8,
        // },
        {
          dataIndex: "callState",
          title: "跟进状态",
          isTable: true,
          tableSlot: "callState",
          isForm: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(3700),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "callImgs",
          title: "拜访图片",
          isForm: true,
          isTable: true,
          valueType: "img",
          formSlot: "callImgs",
        },
      ],
      formLoading: false,
      userList: [],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
    this.operatList();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          reachShopTimeStart: null,
          reachShopTimeEnd: null,
          data: parameter.reachShopTime,
        },
        {
          startMonth: null,
          endMonth: null,
          data: parameter.yearMonth,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.reachShopTime;
      delete requestParameters.yearMonth;
      const editApi = this.type === "detail" ? visitDataApi : visitStatApi;
      editApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      if (this.type === "stat") {
        this.getTotalData(requestParameters);
      }
    },
    // 员工列表处理
    operatList() {
      userListApi({ pageNumber: 1, pageSize: 10000 }).then((res) => {
        res.data.rows.map((item) => {
          this.userList.push({
            value: item.name,
            label: item.name,
          });
        });
        this.formColumns[8].option = this.userList;
      });
    },
    handleEdit(row, type) {
      this.form = {};
      this.methodType = type;
      this.drawerTitle =
        type === "info"
          ? `查看 - ${row.customerName} 拜访记录`
          : `编辑 - ${row.customerName} 拜访记录`;
      visitDetailApi(row.id).then((res) => {
        const formParam = res.data;
        Object.keys(formParam).forEach((key) => {
          formParam[key] = formParam[key].label
            ? formParam[key].value
            : formParam[key];
        });
        this.form = formParam;
      });
      this.showDrawer = true;
    },
    handleOk() {
      updateVisitDetailApi(this.form).then((res) => {
        this.refresh();
        Message.success("修改成功");
        this.showDrawer = false;
      });
    },
    handleDelete(row) {
      this.$confirm("此操作将会删除该条访问记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delVisitDetailApi(row.id).then((res) => {
          Message.success("删除成功");
          this.refresh();
        });
      });
    },
    // 图片处理
    handleCallImgsUploadSuccess(result) {
      if (!this.form.callImgs) {
        this.$set(this.form, "callImgs", []);
      }
      this.form.callImgs.push(result);
    },
    handleCallImgsUploadRemove(file) {
      const index = this.form.callImgs.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      this.form.callImgs.splice(index, 1);
    },
    getTotalData(params) {
      visitDataStatApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    closeDrawer() {
      this.showDrawer = false;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  display: flex;
  flex: 1;
  justify-content: flex-end;
  margin-right: 20px;
  gap: 50px;
  font-size: 16px;
  color: #6488cf;
}
</style>
