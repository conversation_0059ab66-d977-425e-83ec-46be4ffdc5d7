<!--
 * @Author: wskg
 * @Date: 2024-08-16 10:01:02
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-03 17:02:40
 * @Description: 库管 - 呆滞统计
-->

<template>
  <div class="view app-container">
    <IdleItem :type="'consumables'" :columns="consumablesColumns" />
    <!--    <el-tabs v-model="activeName">-->
    <!--      <el-tab-pane label="耗材呆滞料" name="耗材呆滞料">-->
    <!--        <IdleItem :type="'consumables'" :columns="consumablesColumns" />-->
    <!--      </el-tab-pane>-->
    <!--      <el-tab-pane label="机器呆滞料" name="机器呆滞料">-->
    <!--        <IdleItem :type="'machine'" :columns="machineColumns" />-->
    <!--      </el-tab-pane>-->
    <!--    </el-tabs>-->
  </div>
</template>

<script>
import IdleItem from "@/views/store/components/idleItem.vue";
import { warehouseListApi } from "@/api/store";
import { dictTreeByCodeApi } from "@/api/user";
export default {
  name: "Idle",
  components: { IdleItem },
  data() {
    return {
      activeName: "耗材呆滞料",
      consumablesColumns: [
        {
          dataIndex: "warehouseName",
          title: "仓库名称",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "warehouseId",
          title: "仓库名称",
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          width: 120,
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "img",
          title: "物品图片",
          isTable: true,
          tableSlot: "img",
          width: 150,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "partBrand",
          title: "品牌",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 80,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.manufacturerChannel?.label,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          minWidth: 100,
        },
        // {
        //   dataIndex: "machine",
        //   title: "主机型号",
        //   isTable: true,
        //   isSearch: true,
        //   searchSlot: "fullIdPath",
        //   width: 120,
        // },
        // {
        //   dataIndex: "partType",
        //   title: "选配件型号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "select",
        //   width: 120,
        // },
        // {
        //   dataIndex: "machineType",
        //   title: "设备类型",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   width: 120,
        // },

        {
          dataIndex: "purchaseNum",
          title: "总采购量",
          isTable: true,
        },
        {
          dataIndex: "inventoryNum",
          title: "库存量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "salesNum",
          title: "总销售量",
          isTable: true,
        },
        {
          dataIndex: "avgSalesNum",
          title: "月均销量",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "均价",
          isTable: true,
        },
        {
          dataIndex: "inventoryAmount",
          title: "库存金额",
          isTable: true,
        },
        {
          dataIndex: "unsaleableNum",
          title: "滞销天数",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "finalSalesTime",
          title: "最近销售时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          width: 150,
        },
        {
          dataIndex: "isUnsalable",
          title: "呆滞状态",
          isTable: true,
          isSearch: true,
          tableSlot: "isUnsalable",
          valueType: "select",
          clearable: true,
          fixed: "right",
          width: 100,
          option: [
            {
              label: "正常",
              value: false,
            },
            {
              label: "呆滞",
              value: true,
            },
          ],
        },
        {
          dataIndex: "productIds",
          title: "机型",
          isSearch: true,
          clearable: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
      ],
      machineColumns: [],
    };
  },
};
</script>

<style scoped lang="scss"></style>
