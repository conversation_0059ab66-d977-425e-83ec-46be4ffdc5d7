<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 11:18:45
 * @Description: 
 -->
<template>
  <!-- 编辑弹框 -->
  <div v-if="showSettleDialog">
    <ProDialog
      :value="showSettleDialog"
      :title="openType === 'edit' ? '编辑' : '详情'"
      width="1200px"
      :confirm-loading="confirmLoading"
      top="50px"
      :no-footer="showFooter"
      @ok="handleSettleDialogConfirm"
      @cancel="handleSettleDialogCancel"
    >
      <ProForm
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="openType"
        @proSubmit="proSubmit"
      >
        <template #contractFiles>
          <span>/</span>
        </template>
        <template #validityTime>
          <el-date-picker
            v-model="validityTimeData"
            :disabled="openType === 'edit' ? false : true"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :format="'yyyy-MM-dd HH:mm:ss'"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="changeDate"
          >
          </el-date-picker>
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import _ from "lodash";
export default {
  data() {
    return {
      showSettleDialog: false,
      form: {},
      formcolumns: [
        {
          dataIndex: "settlementNumberOne",
          isForm: true,
          title: "结算量1",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "settlementPriceOne",
          isForm: true,
          title: "结算价格1",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          attrs: {
            precision: 2,
          },
          prop: [
            {
              required: true,
              message: "请输入",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "settlementNumberTwo",
          isForm: true,
          title: "结算量2",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementPriceTwo",
          isForm: true,
          title: "结算价格2",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          attrs: {
            precision: 2,
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementNumberThree",
          isForm: true,
          title: "结算量3",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementPriceThree",
          isForm: true,
          title: "结算价格3",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          attrs: {
            precision: 2,
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementNumberFour",
          isForm: true,
          title: "结算量4",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementPriceFour",
          isForm: true,
          title: "结算价格4",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          attrs: {
            precision: 2,
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementNumberFive",
          isForm: true,
          title: "结算量5",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementPriceFive",
          isForm: true,
          title: "结算价格5",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          attrs: {
            precision: 2,
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "deliveryTime",
          isForm: true,
          title: "发货时间标准",
          clearable: true,
          formSpan: 12,
          valueType: "input-number",
          prop: [
            {
              required: true,
              message: "请输入",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "arrivalTime",
          isForm: true,
          title: "到货时间标准",

          clearable: true,
          formSpan: 12,
          valueType: "input-number",

          prop: [
            {
              required: true,
              message: "请输入",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "validityTime",
          isForm: true,
          title: "有效期",
          clearable: true,
          formSlot: "validityTime",
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "updatedByName",
          isForm: true,
          title: "编辑人",
          disabled: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "updatedAt",
          isForm: true,
          title: "编辑时间",
          disabled: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "contractFiles",
          isForm: true,
          title: "合同附件",
          formSlot: "contractFiles",
          clearable: true,
          formSpan: 24,
        },
      ],
      validityTimeData: [],
      formListData: null,
      confirmLoading: false,
      showFooter: true,
      openType: "edit",
    };
  },
  methods: {
    handleSettleDialogConfirm() {
      this.$refs.proform.handleSubmit();
    },
    handleSettleDialogCancel() {
      this.showSettleDialog = false;
      // this.form = {};
    },
    changeDate(time) {
      this.form.validityTime = time;
      this.form = { ...this.form };
    },
    show(data, type) {
      this.openType = type;
      if (type === "edit") {
        this.showFooter = false;
      } else {
        this.showFooter = true;
      }
      this.form = _.cloneDeep(data);
      console.log(this.form);
      this.validityTimeData = [];
      if (data.validityStartTime && data.validityEndTime) {
        this.validityTimeData[0] = data.validityStartTime;
        this.validityTimeData[1] = data.validityEndTime;
        this.form.validityTime = this.validityTimeData;
      }
      this.showSettleDialog = true;
    },
    proSubmit(data) {
      console.log(data);
      this.showSettleDialog = false;
      this.formListData = data;
      this.formListData.validityStartTime = this.validityTimeData[0];
      this.formListData.validityEndTime = this.validityTimeData[1];
      delete this.formListData.manufacturer;
      delete this.formListData.storageArticle;
      this.$emit("sure", this.formListData);
    },
  },
};
</script>

<style lang="scss" scoped></style>
