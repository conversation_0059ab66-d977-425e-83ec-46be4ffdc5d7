<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="按机器分月份" name="按机器分月份" lazy>
        <AMachineMonth />
      </el-tab-pane>
      <el-tab-pane label="按机型分月" name="按机型分月" lazy>
        <AModelMonth />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import AMachineMonth from "@/views/machine/components/AMachineMonth.vue";
import AModelMonth from "@/views/machine/components/AModelMonth.vue";
export default {
  name: "PrintStat",
  components: { AMachineMonth, AModelMonth },
  data() {
    return {
      activeName: "按机器分月份",
    };
  },
};
</script>

<style scoped lang="scss"></style>
