<!--
 * @Author: AI Assistant
 * @Date: 2025-01-29
 * @Description: 分发情况查看对话框组件
-->
<template>
  <el-dialog
    title="分发情况"
    :visible.sync="dialogVisible"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    destroy-on-close
  >
    <div class="distribution-container">
      <!-- 版本信息 -->
      <el-alert
        v-if="versionInfo"
        :title="`版本 ${versionInfo.versionName} 分发情况`"
        type="info"
        :closable="false"
        show-icon
        class="mb-4"
      />

      <!-- 统计信息 -->
      <div class="stats-row mb-4">
        <el-card shadow="never" class="stat-card">
          <div class="stat-item">
            <span class="stat-label">总分发数</span>
            <span class="stat-value">{{ distributions.length }}</span>
          </div>
        </el-card>
        <el-card shadow="never" class="stat-card">
          <div class="stat-item">
            <span class="stat-label">用户数</span>
            <span class="stat-value">{{ getCountByType('USER') }}</span>
          </div>
        </el-card>
        <el-card shadow="never" class="stat-card">
          <div class="stat-item">
            <span class="stat-label">设备数</span>
            <span class="stat-value">{{ getCountByType('DEVICE') }}</span>
          </div>
        </el-card>
        <el-card shadow="never" class="stat-card">
          <div class="stat-item">
            <span class="stat-label">用户组数</span>
            <span class="stat-value">{{ getCountByType('GROUP') }}</span>
          </div>
        </el-card>
      </div>

      <!-- 筛选器 -->
      <div class="filter-row mb-4">
        <el-select
          v-model="filterType"
          placeholder="筛选类型"
          clearable
          style="width: 150px; margin-right: 10px;"
        >
          <el-option label="全部" value="" />
          <el-option label="用户" value="USER" />
          <el-option label="设备" value="DEVICE" />
          <el-option label="用户组" value="GROUP" />
        </el-select>
        <el-input
          v-model="filterKeyword"
          placeholder="搜索目标ID或名称"
          clearable
          style="width: 200px; margin-right: 10px;"
        />
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>

      <!-- 分发列表 -->
      <el-table
        :data="filteredDistributions"
        v-loading="loading"
        border
        stripe
        max-height="400"
      >
        <el-table-column label="类型" width="80">
          <template slot-scope="{ row }">
            <el-tag :type="getTypeColor(row.targetType)">
              {{ getTypeText(row.targetType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="目标ID" prop="targetId" width="180" />
        <el-table-column label="目标名称" prop="targetName" min-width="300" show-overflow-tooltip />
        <el-table-column label="分发时间" width="160">
          <template slot-scope="{ row }">
            {{ formatDateTime(row.assignTime) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template slot-scope="{ row }">
            <el-tag :type="row.isActive ? 'success' : 'info'">
              {{ row.isActive ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="{ row }">
            <el-button-group>
              <!-- 激活/取消激活按钮 -->
              <el-button
                v-if="row.isActive"
                size="mini"
                type="warning"
                @click="handleDeactivateDistribution(row)"
              >
                取消激活
              </el-button>
              <el-button
                v-else
                size="mini"
                type="success"
                @click="handleActivateDistribution(row)"
              >
                重新激活
              </el-button>

              <!-- 移除按钮 -->
              <el-button
                size="mini"
                type="danger"
                @click="handleRemoveDistribution(row)"
              >
                移除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="!loading && distributions.length === 0" class="empty-state">
        <i class="el-icon-info"></i>
        <p>该版本暂无分发关系</p>
      </div>
    </div>
    
    <div slot="footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="handleRefresh">刷新</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  getVersionDistributions,
  removeDistribution,
  deactivateDistribution,
  activateDistribution
} from '@/appupdate/api/appVersion';

export default {
  name: 'DistributionDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    versionInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      distributions: [],
      filterType: '',
      filterKeyword: ''
    };
  },
  computed: {
    filteredDistributions() {
      let result = this.distributions;
      
      if (this.filterType) {
        result = result.filter(item => item.targetType === this.filterType);
      }
      
      if (this.filterKeyword) {
        const keyword = this.filterKeyword.toLowerCase();
        result = result.filter(item => 
          item.targetId.toLowerCase().includes(keyword) ||
          (item.targetName && item.targetName.toLowerCase().includes(keyword))
        );
      }
      
      return result;
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val && this.versionInfo) {
        this.loadDistributions();
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  methods: {
    async loadDistributions() {
      this.loading = true;
      try {
        this.distributions = await getVersionDistributions(this.versionInfo.id);
      } catch (error) {
        this.$message.error('获取分发情况失败：' + error.message);
        this.distributions = [];
      } finally {
        this.loading = false;
      }
    },

    getCountByType(type) {
      return this.distributions.filter(item => item.targetType === type).length;
    },

    getTypeText(type) {
      const typeMap = {
        'USER': '用户',
        'DEVICE': '设备',
        'GROUP': '用户组'
      };
      return typeMap[type] || type;
    },

    getTypeColor(type) {
      const colorMap = {
        'USER': 'primary',
        'DEVICE': 'success',
        'GROUP': 'warning'
      };
      return colorMap[type] || 'info';
    },

    handleSearch() {
      // 搜索逻辑已在computed中实现
    },

    async handleRemoveDistribution(distribution) {
      try {
        await this.$confirm(
          `确定要永久移除 ${distribution.targetName || distribution.targetId} 的分发关系吗？\n移除后该用户/设备将无法获取定向版本。`,
          '确认移除',
          { type: 'warning' }
        );

        // 调用删除分发关系的API
        await removeDistribution(this.versionInfo.id, distribution.targetType, distribution.targetId);
        console.log('移除分发关系成功:', distribution);
        this.$message.success('移除成功');

        // 重新加载分发关系列表
        this.loadDistributions();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('移除分发关系失败:', error);
          this.$message.error('移除失败：' + (error.message || '未知错误'));
        }
      }
    },

    async handleDeactivateDistribution(distribution) {
      try {
        await this.$confirm(
          `确定要取消激活 ${distribution.targetName || distribution.targetId} 的分发关系吗？\n取消后该用户/设备暂时无法获取定向版本，但可以重新激活。`,
          '确认取消激活',
          { type: 'warning' }
        );

        // 调用取消激活分发关系的API
        await deactivateDistribution(this.versionInfo.id, distribution.targetType, distribution.targetId);
        console.log('取消激活分发关系成功:', distribution);
        this.$message.success('取消激活成功');

        // 重新加载分发关系列表
        this.loadDistributions();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消激活分发关系失败:', error);
          this.$message.error('取消激活失败：' + (error.message || '未知错误'));
        }
      }
    },

    async handleActivateDistribution(distribution) {
      try {
        await this.$confirm(
          `确定要重新激活 ${distribution.targetName || distribution.targetId} 的分发关系吗？\n激活后该用户/设备将重新可以获取定向版本。`,
          '确认重新激活',
          { type: 'info' }
        );

        // 调用重新激活分发关系的API
        await activateDistribution(this.versionInfo.id, distribution.targetType, distribution.targetId);
        console.log('重新激活分发关系成功:', distribution);
        this.$message.success('重新激活成功');

        // 重新加载分发关系列表
        this.loadDistributions();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重新激活分发关系失败:', error);
          this.$message.error('重新激活失败：' + (error.message || '未知错误'));
        }
      }
    },

    handleRefresh() {
      this.loadDistributions();
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '-';
      return new Date(dateTime).toLocaleString('zh-CN');
    }
  }
};
</script>

<style scoped>
.distribution-container {
  max-height: 600px;
  overflow-y: auto;
}

.stats-row {
  display: flex;
  gap: 16px;
}

.stat-card {
  flex: 1;
  border: 1px solid #e4e7ed;
}

.stat-item {
  text-align: center;
  padding: 10px;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.filter-row {
  display: flex;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
