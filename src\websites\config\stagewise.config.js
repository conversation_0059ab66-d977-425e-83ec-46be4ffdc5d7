/**
 * Stagewise 配置文件
 * 用于配置Stagewise工具栏的行为和外观
 */

export const stagewiseConfig = {
  // 基础配置
  enabled: process.env.NODE_ENV === 'development', // 仅在开发环境启用
  
  // 工具栏位置
  // 可选值: 'top-left', 'top-right', 'bottom-left', 'bottom-right'
  position: 'bottom-right',
  
  // 主题配置
  theme: {
    primary: '#1890ff',      // 主色调
    secondary: '#52c41a',    // 次要颜色
    background: '#ffffff',   // 背景色
    text: '#333333',         // 文字颜色
    border: '#d9d9d9',       // 边框颜色
    shadow: 'rgba(0, 0, 0, 0.1)' // 阴影颜色
  },
  
  // 项目信息
  project: {
    name: 'benyin-web',
    description: '本印科技Web管理系统',
    framework: 'Vue 2',
    version: '2.7.16',
    author: '本印科技开发团队'
  },
  
  // 功能开关
  features: {
    componentInspector: true,    // 组件检查器
    performanceMonitor: true,    // 性能监控
    debugConsole: true,          // 调试控制台
    codeGeneration: true,        // 代码生成
    hotReload: true,             // 热重载
    sourceMap: true              // 源码映射
  },
  
  // 快捷键配置
  shortcuts: {
    toggleToolbar: 'Ctrl+Shift+S',
    openInspector: 'Ctrl+Shift+I',
    openConsole: 'Ctrl+Shift+C'
  },
  
  // 排除的组件或路径
  exclude: {
    components: [
      'transition',
      'transition-group',
      'keep-alive',
      'router-view',
      'router-link'
    ],
    paths: [
      '/node_modules/',
      '/dist/',
      '/build/'
    ]
  },
  
  // 开发环境特定配置
  development: {
    autoConnect: true,           // 自动连接到开发服务器
    liveReload: true,           // 实时重载
    errorOverlay: true,         // 错误覆盖层
    warningOverlay: false,      // 警告覆盖层
    sourceMapSupport: true      // 源码映射支持
  },
  
  // 生产环境配置（通常禁用）
  production: {
    enabled: false,
    telemetry: false,           // 遥测数据
    errorReporting: false       // 错误报告
  },
  
  // 集成配置
  integrations: {
    vue: {
      devtools: true,           // Vue开发工具集成
      router: true,             // Vue Router集成
      vuex: true                // Vuex集成
    },
    elementUI: {
      themeSync: true           // Element UI主题同步
    }
  }
}

// 根据环境获取配置
export function getStagewiseConfig() {
  const env = process.env.NODE_ENV
  const baseConfig = { ...stagewiseConfig }
  
  if (env === 'production') {
    return {
      ...baseConfig,
      ...baseConfig.production,
      enabled: baseConfig.production.enabled
    }
  }
  
  return {
    ...baseConfig,
    ...baseConfig.development
  }
}

// 验证配置
export function validateConfig(config) {
  const errors = []
  
  if (!config.position || !['top-left', 'top-right', 'bottom-left', 'bottom-right'].includes(config.position)) {
    errors.push('Invalid position value')
  }
  
  if (!config.theme || typeof config.theme !== 'object') {
    errors.push('Theme configuration is required')
  }
  
  if (!config.project || !config.project.name) {
    errors.push('Project name is required')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}
