<template>
  <div class="state-box">
    <div v-if="type == 'pub'">
      <span v-if="state === 0" style="color: #4d83ec"> 待审核 </span>
      <span v-if="state === 1" style="color: #e3c828"> 审核中 </span>
      <span v-if="state === 2" style="color: #88d76a"> 同意 </span>
      <span v-if="state === 3" style="color: #f87978"> 回退 </span>
    </div>
    <div v-if="type == 'state'">
      <span v-if="state === 0" style="color: #4d83ec"> 待部门审核 </span>
      <span v-if="state === 1" style="color: #4d83ec"> 待专业审核 </span>
      <span v-if="state === 2" style="color: #88d76a"> 通过 </span>
      <span v-if="state === 3" style="color: #f87978"> 回退 </span>
    </div>
    <div v-if="type == 'check'">
      <span v-if="state === 0" style="color: #4d83ec"> 待验收 </span>
      <span v-if="state === 1" style="color: #88d76a"> 同意 </span>
      <span v-if="state === 2" style="color: #f87978"> 回退 </span>
    </div>
    <div v-if="type == 'complete'">
      <span v-if="state === 0" style="color: #4d83ec"> 未完成 </span>
      <span v-if="state === 1" style="color: #e3c828"> 暂停 </span>
      <span v-if="state === 2" style="color: #88d76a"> 已完成 </span>
    </div>
    <div v-if="type == 'evaluation'">
      <span v-if="state === 0" style="color: #4d83ec"> 待评估 </span>
      <span v-if="state === 1" style="color: #88d76a"> 同意 </span>
      <span v-if="state === 2" style="color: #f87978"> 回退 </span>
    </div>
  </div>
</template>
<script>
export default {
  name: "",
  components: {},
  props: {
    state: {
      type: Number,
      default: null,
    },
    type: {
      type: String,
      default: "pub",
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  destroyed() {},
  methods: {},
};
</script>
<style lang="scss" scoped>
.state-box {
  display: flex;
  justify-content: center;
}
</style>
