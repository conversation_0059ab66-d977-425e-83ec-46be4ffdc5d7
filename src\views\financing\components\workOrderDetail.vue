<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-10-10 15:33:11
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-01 13:54:52
 * @FilePath: src/views/financing/components/workOrderDetail.vue
 * @Description: 维修工单详情
 * 
-->

<template>
  <div class="container">
    <ProDrawer
      :value="dialogVisible"
      :title="title"
      size="85%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="closeDrawer"
    >
      <div class="dialog-content-box">
        <div>
          <div class="boxa box0">
            <div class="tit-box">维修进度跟踪</div>
            <div class="sp-content jbxx-box">
              <el-steps :active="stepActive">
                <el-step
                  index="CREATE"
                  title="发起报修"
                  :description="formData.createdAt"
                >
                </el-step>
                <el-step
                  index="ENGINEER_RECEIVE"
                  title="工程师接单"
                  :description="formData.orderReceiveTime"
                ></el-step>
                <el-step
                  index="ENGINEER_DEPARTURE"
                  title="工程师出发"
                  :description="formData.departureTime"
                ></el-step>

                <el-step
                  index="ENGINEER_ARRIVE"
                  title="到店维修"
                  :description="formData.actualArriveTime"
                >
                </el-step>
                <el-step
                  index="WAIT_CONFIRM"
                  :description="formData.sendReportTime"
                  title="提交维修报告"
                ></el-step>
                <el-step
                  index="DONE"
                  :description="formData.completedAt"
                  title="已完成"
                ></el-step>
              </el-steps>
            </div>
          </div>
          <div class="boxa box1">
            <div class="tit-box">工单信息</div>
            <div class="card-box">
              <el-descriptions :column="2">
                <el-descriptions-item label="工单编号">
                  {{ formData.code }}
                </el-descriptions-item>
                <el-descriptions-item label="客户编号">
                  {{ formData.customer?.seqId }}
                </el-descriptions-item>
                <el-descriptions-item label="工单状态">
                  {{ formData.status?.label }}
                </el-descriptions-item>
                <el-descriptions-item label="店铺名称">
                  {{ formData.customer?.shopRecruitment }}</el-descriptions-item
                >
                <el-descriptions-item label="店铺地址">
                  {{ formData.customer?.address }}
                </el-descriptions-item>
                <el-descriptions-item label="报修人">
                  {{ formData?.customer?.customerStaff }}
                </el-descriptions-item>
                <el-descriptions-item label="报修人电话">
                  {{ formData?.customer?.contactPhone }}
                </el-descriptions-item>
                <el-descriptions-item label="设备组图" :span="2">
                  <div>
                    <el-image
                      class="imgs1"
                      :src="
                        formData.deviceGroupImg && formData.deviceGroupImg.url
                          ? formData.deviceGroupImg.url
                          : require('../../../assets/images/top.png')
                      "
                      alt=""
                      :preview-src-list="[formData.deviceGroupImg?.url]"
                    >
                    </el-image>
                    <div
                      style="float: left; margin-left: 10px; font-weight: bold"
                    >
                      <div>{{ formData.deviceGroup?.label }}</div>
                      <div>{{ formData.productInfo }}</div>
                    </div>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="故障描述" :span="2">
                  {{ formData.excDesc }}
                </el-descriptions-item>
                <el-descriptions-item label="故障照片" :span="2">
                  <div style="display: flex; width: 100%; flex-wrap: wrap">
                    <el-image
                      v-for="(item, index) in formData.excPics"
                      :key="index"
                      class="imgs"
                      :src="item.url"
                      alt=""
                      :preview-src-list="[item.url]"
                    >
                    </el-image>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="card-box">
              <el-descriptions :column="2">
                <el-descriptions-item label="期望到店时间">
                  {{ formData.expectArriveTime }}
                </el-descriptions-item>
                <el-descriptions-item label="预计到店时间">
                  {{ formData.prospectArriveTime }}
                </el-descriptions-item>
                <el-descriptions-item label="接单工程师">
                  {{ formData.engineerId?.name }}
                </el-descriptions-item>
                <el-descriptions-item label="维修工程师">
                  {{ formData?.engineerId?.name }}
                </el-descriptions-item>
                <el-descriptions-item label="接单时长">
                  {{ formData.orderReceiveTime }}
                </el-descriptions-item>
                <!--<el-descriptions-item label="确认工单时长">-->
                <!--</el-descriptions-item>-->
                <!--<el-descriptions-item label="领料时长"></el-descriptions-item>-->
                <el-descriptions-item label="路途时长">{{
                  formData.travelTime
                }}</el-descriptions-item>
                <el-descriptions-item label="维修时长"
                  >{{ formData.fixTime }}
                </el-descriptions-item>
                <!--<el-descriptions-item-->
                <!--  label="确认报告时长"-->
                <!--&gt;</el-descriptions-item>-->
                <!--<el-descriptions-item label="付款时长"></el-descriptions-item>-->
                <el-descriptions-item
                  v-if="formData.repairReport"
                  label="维修报告"
                >
                  <el-button
                    type="primary"
                    size="small"
                    style="padding: 4px 8px"
                    @click="handleReport(formData.id)"
                  >
                    查看
                  </el-button>
                </el-descriptions-item>
                <el-descriptions-item v-if="formData.isAppeal" label="申诉次数">
                  {{ formData.appealCount }}次
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="card-box">
              <el-descriptions :column="2">
                <el-descriptions-item label="上门费">
                  {{ formData.visitPay }} 元
                </el-descriptions-item>
                <el-descriptions-item label="远程误工费">
                  {{ formData.longWayVisitPay || 0 }} 元
                </el-descriptions-item>
                <el-descriptions-item label="零件更换费">
                  {{ formData.actualReplacePay || 0 }} 元
                </el-descriptions-item>
                <el-descriptions-item label="维修诊断费">
                  {{ formData.repairPay || 0 }} 元
                </el-descriptions-item>
                <el-descriptions-item label="工程师减免费用">
                  -{{ formData.derateAmount || 0 }} 元
                </el-descriptions-item>
                <el-descriptions-item label="工程师加价">
                  {{ formData.engineerAdditionalPay || 0 }} 元
                </el-descriptions-item>

                <el-descriptions-item label="客户追加报酬">
                  {{ formData.additionalPay || 0 }} 元
                </el-descriptions-item>
                <el-descriptions-item label="会员减免" :span="2">
                  -{{ formData.discountAmount || 0 }} 元
                </el-descriptions-item>

                <el-descriptions-item label="技术咨询费用合计" :span="2">
                  <span style="color: #ff541e">
                    ¥{{ formData?.laborCost }}元
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="耗材费用合计" :span="2">
                  <span style="color: #ff541e"
                    >{{ formData?.itemPay }} 元
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="总费用" :span="2">
                  <span style="color: #ff541e">
                    {{ formData?.totalAmount }} 元
                  </span>
                </el-descriptions-item>

                <el-descriptions-item label="实付费用" :span="2">
                  <span style="color: #ff541e">
                    合计：¥{{ formData?.totalPay }}元
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="申诉状态">
                  {{ formData.isAppeal ? "申诉中" : "无" }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </div>
    </ProDrawer>
    <ProDialog
      :value="dialogVisible2"
      :title="'维修报告'"
      width="60%"
      :top="'0px'"
      :no-footer="true"
      @cancel="dialogVisible2 = false"
    >
      <div class="dialog-content-box">
        <div style="height: 700px; overflow: hidden; overflow-y: scroll">
          <div class="boxa box0">
            <div class="tit-box">故障描述</div>
            <div style="line-height: 20px; width: 80%">
              {{ formReport?.excDesc }}
            </div>
            <div style="display: flex; width: 100%; flex-wrap: wrap">
              <el-image
                v-for="(item, index) in formReport?.excDescPics"
                :key="index"
                class="imgs"
                :src="item.url"
                alt=""
                :preview-src-list="[item.url]"
              >
              </el-image>
            </div>
          </div>
          <div class="boxa box1">
            <div class="tit-box">解决措施</div>
            <div>
              <div style="line-height: 20px; width: 80%">
                {{ formReport?.resolveDesc }}
              </div>

              <div style="display: flex; width: 100%; flex-wrap: wrap">
                <el-image
                  v-for="(item, index) in formReport?.resolveDescPics"
                  :key="index"
                  class="imgs"
                  :src="item.url"
                  alt=""
                  :preview-src-list="[item.url]"
                >
                </el-image>
              </div>
            </div>
            <div class="tit-box">下次注意事项</div>
            <div>
              <div style="line-height: 20px; width: 80%">
                {{
                  formReport?.announcements ? formReport?.announcements : "无"
                }}
              </div>
            </div>
            <div class="tit-box">其他</div>
            <div style="line-height: 30px">
              现象分类：{{ formReport?.excType?.label }}
            </div>
            <div style="line-height: 30px">
              原因分类：{{ formReport?.reasonType?.label }}
            </div>
            <div style="line-height: 30px">
              处理类型：{{ formReport?.resolveType?.label }}
            </div>
            <div style="line-height: 30px">
              故障组件：{{ formReport?.excUnit?.label }}
            </div>
            <div style="line-height: 30px">
              黑白计数器：{{ formReport.blackWhiteCount }}
            </div>
            <div style="line-height: 30px">
              彩色计数器：{{ formReport.colorCount }}
            </div>
            <div style="line-height: 30px">
              上次维修后到目前的印量：{{ formReport.printCount }}
            </div>

            <div class="tit-box">更换耗材零件</div>
            <div>
              <DataTable
                ref="ProTable1"
                :columns="columns1"
                :show-setting="false"
                :show-pagination="false"
                :show-search="false"
                row-key="index"
                :data="tableData1"
                sticky
                :height="350"
                style="width: 100%; margin-top: 20px"
                :show-table-operator="false"
              >
                <template #picUrl="slotProps">
                  <el-image
                    :preview-src-list="[slotProps.row?.skuInfo?.picUrl[0]?.url]"
                    style="width: 100px; height: 100px"
                    :src="slotProps.row?.skuInfo?.picUrl[0]?.url"
                  ></el-image>
                </template>

                <template #saleAttrVals="slotProps">
                  <div
                    v-for="attr in slotProps.row?.skuInfo?.saleAttrVals"
                    :key="attr.val"
                  >
                    {{ attr.name }}:{{ attr.val }}
                  </div>
                </template>
              </DataTable>
            </div>
          </div>
        </div>
      </div>
    </ProDialog>
  </div>
</template>

<script>
import { WorkOrderDetailInfoApi, getReportApi } from "@/api/repair";
import { cloneDeep } from "lodash";
export default {
  name: "WorkOrderDetail",
  props: {
    title: {
      type: String,
      default: "工单详情",
    },
  },
  data() {
    return {
      stepActive: 0,
      stepList: [
        "CREATE",
        "ENGINEER_RECEIVE",
        "ENGINEER_DEPARTURE",
        "ENGINEER_ARRIVE",
        "WAIT_CONFIRM",
        "DONE",
      ],
      dialogVisible: false,
      confirmLoading: false,
      formData: {},
      formReport: {},
      dialogVisible2: false,
      tableData1: [],
      columns1: [
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 120,
          formatter: (row) => row.itemStore?.oemNumber,
        },
        {
          dataIndex: "picUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picUrl",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          formatter: (row) => row.itemStore?.articleCode,
          width: 120,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku规格",
          isTable: true,
          width: 110,
          tableSlot: "saleAttrVals",
        },

        {
          dataIndex: "saleUnitPrice",
          title: "单价",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "num",
          title: "更换数量",
          width: 80,
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "location",
          title: "更换位置",
          width: 80,
          isTable: true,
        },
        {
          dataIndex: "itemStore",
          title: "更换类型",
          isTable: true,
          width: 100,
          formatter: (row) => {
            switch (row.itemStore?.skuSource) {
              case "ENGINEER_APPLY":
                return "工程师外带";
              default:
                return "客户自配";
            }
          },
        },
      ],
    };
  },
  methods: {
    show(tradeOrderNumber) {
      this.formData = {};
      this.confirmLoading = true;
      WorkOrderDetailInfoApi(tradeOrderNumber)
        .then((res) => {
          this.formData = cloneDeep(res.data);
          this.stepActive = this.stepList.indexOf(this.formData.currentProcess);
        })
        .finally(() => {
          this.confirmLoading = false;
        });
      this.$nextTick(() => {
        this.dialogVisible = true;
      });
    },
    handleReport(id) {
      this.formReport = {};
      getReportApi(id).then((res) => {
        this.dialogVisible2 = true;
        this.formReport = cloneDeep(res.data.repairReport);
        this.tableData1 = res.data?.replaceOrder?.replaceDetailList;
      });
    },
    closeAppealDialog() {
      this.appealValue = false;
    },
    closeDrawer() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style scoped lang="scss">
.imgs {
  height: 120px;
  margin: 10px;
  max-width: calc((100% - 80px) / 4);
}
.imgs1 {
  height: 120px;
  width: 120px;
  float: left;
}
.card-box {
  border-bottom: 5px solid #f1eeee;
  margin: 10px 0;
  padding: 0 20px;
}
.dialog-content-box {
  position: relative;
  height: 100%;
  overflow: scroll;
  padding-bottom: 80px;
  .steps-box {
    position: absolute;
    width: 80%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    z-index: 2;
  }

  .content-fix {
    height: calc(100vh - 110px);
    overflow: auto;
  }

  .tit-box {
    width: 100%;
    padding: 5px 10px;
    color: #409eff;
    position: relative;
    margin: 20px auto;
    font-size: 16px;
    font-weight: 800;

    &::before {
      content: "";
      width: 5px;
      height: 20px;
      background: #409eff;
      display: inline-block;
      position: absolute;
      left: -1px;
      top: 4px;
    }
  }
}
</style>
