/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-25 16:21:19
 * @Description: 按钮权限
 */
import store from "@/store";
// 按钮权限校验
export default {
  inserted(el, binding) {
    const { value } = binding;
    const permits = store.getters && store.getters.permits;
    if (value && Array.isArray(value) && value.length > 0) {
      const permissionRoles = value;
      const hasPermission = permits.some((permission) =>
        permissionRoles.includes(permission.permit)
      );
      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      throw new Error(`need roles! Like v-auth="['admin','editor']"`);
    }
  },
};
