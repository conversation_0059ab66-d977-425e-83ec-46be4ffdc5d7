/*
 * @Author: s<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-02-04 11:50:51
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-03 13:59:16
 * @FilePath: src/api/iot.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { get, post, put, del, down } from "@/utils/request";

export const getExceptionLogListApi = (data) =>
  post("/iot-exception-log/page", data);
export const getNumericCodeListApi = (data) =>
  post("/iot-numeric-code/page", data);
export const exitNumericCodeListApi = (data) => put("/iot-numeric-code", data);
export const addNumericCodeListApi = (data) => post("/iot-numeric-code", data);

export const getConfigureListApi = (data) => post("/iot-configure/page", data);
export const getCounterListApi = (data) => post("/iot-counter/page", data);
// 修改计数器上报状态
export const editCounterStatusApi = (data) =>
  post("/iot-counter/updateStatus", data);

export const addConfigureApi = (data) => post("/iot-configure", data);
export const editConfigureApi = (data) => put("/iot-configure", data);

export const delConfigureApi = (id) => del(`/iot-configure/${id}`);

export const getInstallSoftwareApi = (data) =>
  post("/iot-install-software/page", data);
export const addInstallSoftwareApi = (data) =>
  post("/iot-install-software", data);
export const getOidConfigApi = (data) => post("/iot-oid-config/page", data);
export const addOidConfigApi = (data) => post("/iot-oid-config", data);
export const putOidConfigApi = (data) => put("/iot-oid-config", data);
export const getStateApi = (data) => post("/iot-state/page", data);
export const getPowderApi = (data) => post("/iot-powder/page", data);

export const deleteOidApi = (id) => del("/iot-oid-config/" + id);
export const deleteNumericCodeApi = (id) => del("/iot-numeric-code/" + id);
// 新oid配置
// 添加
export const addOidConfigNewApi = (data) => post("/iot-oid-setting", data);
// 修改
export const editOidConfigNewApi = (data) => put("/iot-oid-setting", data);
// 分页查询
export const getOidConfigNewApi = (data) => post("/iot-oid-setting/page", data);
// 删除
export const deleteOidNewApi = (id) => del(`/iot-oid-setting/${id}`);

// ========================================  绑定日志管理 ========================================
// 分页查询
export const getBindLogApi = (data) => post("/iot-bind-log/page", data);
