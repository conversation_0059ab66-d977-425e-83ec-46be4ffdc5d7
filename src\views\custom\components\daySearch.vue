<!--
 * @Author: wskg
 * @Date: 2024-08-29 14:38:11
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 未搜索到结果
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>客户数：{{ totalData?.custCount || 0 }}</div>
          <div>总次数：{{ totalData?.viewCount || 0 }}</div>
        </div>
      </template>
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { optionsGetRegionApi } from "@/api/operator";
import {
  getCustomerSearchDayByPageApi,
  getCustomerSearchDayStatApi,
} from "@/api/customer";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";

export default {
  name: "DaySearch",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "customerName",
          title: "店铺名",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        // {
        //   dataIndex: "eventType",
        //   title: "事件类型",
        //   isTable: true,
        //   formatter: (row) => {
        //     switch (row.eventType) {
        //       case "NO_LOGIN":
        //         return "未登录";
        //       case "Key_word":
        //         return "关键字搜索";
        //       case "Key_params":
        //         return "关键字+参数搜索";
        //       default:
        //         return "";
        //     }
        //   },
        // },
        {
          dataIndex: "eventSource",
          title: "来源页面",
          isTable: true,
          formatter: (row) => {
            switch (row.eventSource) {
              case "MALL_HOME":
                return "商城";
              case "KNOWLEDGE_HOME":
                return "知识库";
              default:
                return "";
            }
          },
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "商城",
              value: "MALL_HOME",
            },
            {
              label: "知识库",
              value: "KNOWLEDGE_HOME",
            },
          ],
        },
        {
          dataIndex: "searchCount",
          title: "搜索次数",
          isTable: true,
        },
        {
          dataIndex: "currDate",
          title: "搜索日期",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
        },
      ],
      tableData: [],
      optionsGetRegion: [],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
    // this.optionsGetRegionAFn();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );

      const result = [
        {
          createStartTime: null,
          createEndTime: null,
          data: parameter.currDate,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.currDate;
      getCustomerSearchDayByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData(requestParameters);
    },
    // optionsGetRegionAFn() {
    //   optionsGetRegionApi().then((res) => {
    //     this.optionsGetRegion = res.data;
    //   });
    // },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },

    // 日搜索统计
    getTotalData(params) {
      getCustomerSearchDayStatApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
