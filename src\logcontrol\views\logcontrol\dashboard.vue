<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 仪表板主页面
-->
<template>
  <div class="dashboard app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">仪表板</h1>
        <p class="page-description">日志控制系统总览</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <i class="el-icon-refresh"></i>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="8">
        <stat-card
          title="设备总数"
          :value="stats.totalDevices"
          unit="台"
          icon="el-icon-mobile-phone"
          color="#409EFF"
          :loading="statsLoading"
        />
      </el-col>
      <el-col :span="8">
        <stat-card
          title="日志总数"
          :value="stats.totalLogs"
          unit="条"
          icon="el-icon-document"
          color="#67C23A"
          :loading="statsLoading"
        />
      </el-col>
      <el-col :span="8">
        <stat-card
          title="崩溃事件"
          :value="stats.crashEvents"
          unit="个"
          icon="el-icon-warning"
          color="#E6A23C"
          :loading="statsLoading"
        />
      </el-col>
    </el-row>



    <!-- 基于真实接口数据的图表区域 -->
    <LogCharts ref="logCharts" />
  </div>
</template>

<script>
import StatCard from '@/logcontrol/components/Common/StatCard.vue'
import LogCharts from './components/LogCharts.vue'
import { analysisApi } from '@/logcontrol/api/analysisApi'

export default {
  name: 'Dashboard',
  components: {
    StatCard,
    LogCharts
  },
  data() {
    return {
      loading: false,
      statsLoading: false,
      chartsLoading: false,
      
      // 统计数据
      stats: {
        totalDevices: 0,
        totalLogs: 0,
        crashEvents: 0
      }
    }
  },
  mounted() {
    this.initData()
    // 定时刷新已取消 - 只在页面加载时初始化数据
  },

  methods: {
    // 初始化数据
    async initData() {
      this.loading = true
      try {
        await this.loadDashboardStats()
      } finally {
        this.loading = false
      }
    },
    
    // 加载仪表板统计数据
    async loadDashboardStats() {
      this.statsLoading = true
      try {
        // 获取综合统计数据
        const response = await analysisApi.getComprehensiveStats()
        const data = response.data || {}

        this.stats = {
          totalDevices: data.totalDevices || 0,
          totalLogs: data.totalLogs || 0,
          crashEvents: data.totalCrashes || 0
        }
      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        // 使用模拟数据
        this.stats = {
          totalDevices: 156,
          totalLogs: 8432,
          crashEvents: 23
        }
      } finally {
        this.statsLoading = false
      }
    },

    // 刷新数据（手动刷新，显示提示消息）
    async refreshData() {
      try {
        // 同时刷新统计卡片和图表数据
        await Promise.all([
          this.loadDashboardStats(),
          this.$refs.logCharts.loadAllStats()
        ])
        this.$message.success('数据刷新成功')
      } catch (error) {
        console.error('刷新数据失败:', error)
        this.$message.error('数据刷新失败')
      }
    }
  }
}

</script>

<style lang="scss" scoped>
.dashboard {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      .page-title {
        margin: 0 0 5px 0;
        font-size: 24px;
        font-weight: 500;
        color: #303133;
      }
      
      .page-description {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .stats-row {
    margin-bottom: 20px;
  }
  
  .charts-row {
    margin-bottom: 20px;

    .el-card {
      height: 380px;

      ::v-deep .el-card__body {
        height: calc(100% - 60px);
        padding: 10px 20px 20px 20px;
        overflow: hidden;
      }
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      padding: 0;
    }
  }

  .realtime-row {
    .el-card {
      height: 320px;

      ::v-deep .el-card__header {
        padding: 15px 20px;
        border-bottom: 1px solid #ebeef5;
        font-weight: 500;
        color: #303133;
      }

      ::v-deep .el-card__body {
        height: calc(100% - 56px);
        padding: 10px 20px 20px 20px;
        overflow: hidden;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 10px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .charts-row {
      .el-card {
        height: 350px;
        margin-bottom: 20px;
      }

      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        height: auto;
        padding: 10px 0;

        .el-radio-group {
          .el-radio-button {
            font-size: 12px;
          }
        }
      }
    }

    .realtime-row {
      .el-card {
        height: 280px;
        margin-bottom: 20px;
      }
    }
  }

  @media (max-width: 480px) {
    padding: 5px;

    .stats-row {
      .el-col {
        margin-bottom: 10px;
      }
    }

    .charts-row {
      .card-header {
        .el-radio-group {
          .el-radio-button {
            padding: 8px 10px;
            font-size: 11px;
          }
        }
      }
    }
  }
}
</style>
