<!--
 * @Author: wskg
 * @Date: 2025-01-09 14:06:04
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:48
 * @Description: 安装工单
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      sticky
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-if="type === 'EXCHANGE'"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleCreateOrder"
        >
          创建换机工单
        </el-button>
      </template>
      <!--<template #btn>-->
      <!--  <div class="title-box-right">-->
      <!--    <div>工单数：{{ totalData.orderNum || 0 }}</div>-->
      <!--    <div>维修人工费用：{{ totalData.laborAmount || 0 }}</div>-->
      <!--    <div>耗材费用：{{ totalData.itemPay || 0 }}</div>-->
      <!--    <div>维修费用：{{ totalData.totalPay || 0 }}</div>-->
      <!--  </div>-->
      <!--</template>-->
      <template #actions="{ row }">
        <span class="fixed-width">
          <el-button icon="el-icon-view" @click="handleInfo(row)">
            查看
          </el-button>
          <el-button
            v-if="
              row?.status?.value === 'wait_distribute' && type !== 'EXCHANGE'
            "
            icon="el-icon-edit-outline"
            @click="handleUpdate(row.id, row.productId)"
          >
            分配工单
          </el-button>

          <el-button
            v-if="
              type == 'EXCHANGE' &&
              !row?.isReceive &&
              row.status?.value !== 'close'
            "
            icon="el-icon-circle-check"
            @click="handleReceive(row)"
          >
            退/换机入库
          </el-button>
          <el-button
            v-if="
              type == 'EXCHANGE' &&
              row?.status?.value !== 'close' &&
              row?.status?.value !== 'completed'
            "
            type="danger"
            icon="el-icon-circle-close"
            @click="handleCheck(row)"
          >
            关闭工单
          </el-button>
          <!--<el-button-->
          <!--  v-if="-->
          <!--    slotProps.row?.status?.value !== 'close' &&-->
          <!--    slotProps.row?.status?.value !== 'completed'-->
          <!--  "-->
          <!--  size="mini"-->
          <!--  type="danger"-->
          <!--  icon="el-icon-circle-close"-->
          <!--  @click="handleDelete(slotProps.row.id)"-->
          <!--&gt;-->
          <!--  关闭工单-->
          <!--</el-button>-->
        </span>
      </template>
    </ProTable>

    <!--  安装工单明细 -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="70%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="closeDialog"
    >
      <div class="dialog-content-box">
        <div>
          <!--<div class="boxa box0">-->
          <!--  <div class="tit-box">维修进度跟踪</div>-->
          <!--  <div class="sp-content jbxx-box">-->
          <!--    <el-steps :active="stepActive">-->
          <!--      <el-step-->
          <!--        index="wait_distribute"-->
          <!--        title="下单日期"-->
          <!--        :description="form.createdAt"-->
          <!--      >-->
          <!--      </el-step>-->
          <!--      <el-step-->
          <!--        index="engineer_departure"-->
          <!--        title="工程师出发"-->
          <!--        :description="form.departureTime"-->
          <!--      ></el-step>-->
          <!--      <el-step-->
          <!--        index="WAIT_CONFIRM"-->
          <!--        :description="form.installedTime"-->
          <!--        title="提交维修报告"-->
          <!--      ></el-step>-->
          <!--      <el-step-->
          <!--        index="DONE"-->
          <!--        :description="form.completedAt"-->
          <!--        title="已完成"-->
          <!--      ></el-step>-->
          <!--    </el-steps>-->
          <!--  </div>-->
          <!--</div>-->
          <div class="boxa box1">
            <div class="tit-box">工单信息</div>
            <div class="card-box">
              <el-descriptions :column="2">
                <el-descriptions-item label="工单编号">
                  {{ form.code }}
                </el-descriptions-item>
                <el-descriptions-item label="客户编号">
                  {{ form.customerSeqId }}
                </el-descriptions-item>
                <el-descriptions-item label="工单状态">
                  {{ form.status?.label }}
                </el-descriptions-item>
                <el-descriptions-item label="店铺名称">
                  {{ form.customerName }}
                </el-descriptions-item>

                <el-descriptions-item label="联系电话">
                  {{ form?.consigneePhone }}
                </el-descriptions-item>
                <el-descriptions-item label="店铺地址" :span="2">
                  {{ form?.consigneeFullAddress }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="card-box">
              <el-descriptions :column="2">
                <el-descriptions-item label="预计安装时间">
                  {{ form.expectInstallTime }}
                </el-descriptions-item>
                <el-descriptions-item label="安装工程师">
                  {{ form.engineerName }}
                </el-descriptions-item>
                <el-descriptions-item label="是否包邮">
                  {{ form.freeShipping ? "是" : "否" }}
                </el-descriptions-item>
                <el-descriptions-item label="安装费用">
                  {{ form.installAmount }}
                </el-descriptions-item>
                <el-descriptions-item
                  v-if="form.status?.value === 'completed'"
                  label="安装报告"
                >
                  <el-button
                    type="primary"
                    size="mini"
                    style="padding: 4px 8px"
                    @click="handleInstallReport(form.id)"
                  >
                    查看
                  </el-button>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="tit-box" style="margin: 0">安装列表</div>
            <ProTable
              :show-pagination="false"
              :show-search="false"
              :show-loading="false"
              :show-setting="false"
              :height="350"
              :columns="installColumns"
              :data="installTableData"
            ></ProTable>
            <template v-if="type === 'EXCHANGE'">
              <div class="tit-box" style="margin: 0">退还机器列表</div>
              <ProTable
                :show-pagination="false"
                :show-search="false"
                :show-loading="false"
                :show-setting="false"
                :height="350"
                :columns="installColumns"
                :data="returnTableData"
              ></ProTable>
            </template>

            <template v-if="type != 'EXCHANGE'">
              <div class="tit-box" style="margin: 0">物品赠送列表</div>
              <ProTable
                :show-pagination="false"
                :show-search="false"
                :show-loading="false"
                :show-setting="false"
                :height="350"
                :columns="giftColumns"
                :data="giftTableData"
              >
                <template #saleAttrVals="slotProps">
                  <div v-if="slotProps.row.saleAttrVals">
                    <span
                      v-for="(item, index) in slotProps.row.saleAttrVals"
                      :key="index"
                      style="border: 1px solid #ddd"
                    >
                      {{ item.name }}: {{ item.val }}
                    </span>
                  </div>
                </template>
              </ProTable>
            </template>
          </div>
        </div>
        <div class="btn-box">
          <!--<div-->
          <!--  v-if="-->
          <!--    form.status?.value !== 'close' &&-->
          <!--    form.status?.value !== 'completed'-->
          <!--  "-->
          <!--  class="cancel-button"-->
          <!--  @click="handleDelete(form.id)"-->
          <!--&gt;-->
          <!--  关闭工单-->
          <!--</div>-->
          <div
            v-if="form.status?.value === 'pending_orders'"
            class="cancel-button m-l-40"
            @click="handleUpdate(form.id, form.productId)"
          >
            指派其他工程师
          </div>
        </div>
      </div>
    </ProDrawer>
    <!-- 分配工单 -->
    <ProDialog
      :value="dialogVisible1"
      :title="'指派安装工程师'"
      width="600px"
      :confirm-btn-loading="confirmLoading"
      :top="'10%'"
      @ok="handleDialogOk"
      @cancel="dialogVisible1 = false"
    >
      <ProForm
        v-if="dialogVisible1"
        ref="proForm"
        :form-param="form1"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      ></ProForm>
    </ProDialog>
    <!-- 关闭工单 -->
    <ProDialog
      :value="dialogVisible3"
      :title="'关闭工单'"
      width="600px"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      @ok="handleDialogOk3"
      @cancel="dialogVisible3 = false"
    >
      <div style="margin-bottom: 20px">关闭后，此工单作废。</div>
      <el-form :model="form3" label-width="180px" style="width: 100%">
        <el-form-item label="是否需要收取店铺维修费" style="width: 100%">
          <el-radio-group
            v-model="form3.needPay"
            size="medium"
            style="width: 100%"
            @change="getPrice(true)"
          >
            <el-radio border :label="false">否</el-radio>
            <el-radio border :label="true">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form3.needPay" label="上门费" style="width: 100%">
          <el-input
            v-model="form3.visitPay"
            style="width: 80%"
            type="number"
            :min="0"
            @input="getPrice()"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="form3.needPay" label="维修费" style="width: 100%">
          <el-input
            v-model="form3.repairPay"
            style="width: 80%"
            type="number"
            :min="0"
            @input="getPrice()"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="form3.needPay" label="应付金额" style="width: 100%">
          {{ allPay }}元
        </el-form-item>
      </el-form>
    </ProDialog>
    <!-- 安装报告 -->
    <ProDialog
      :value="dialogVisible2"
      :title="'安装报告'"
      width="60%"
      :top="'0px'"
      :no-footer="true"
      @cancel="dialogVisible2 = false"
    >
      <div class="dialog-content-box">
        <div style="height: 700px; overflow: hidden; overflow-y: scroll">
          <div class="boxa box0">
            <div class="tit-box">问题描述</div>
            <div style="display: flex; width: 100%; flex-wrap: wrap">
              {{ formReport.disassemblyDesc || "" }}
            </div>
          </div>
          <div class="boxa box0">
            <div class="tit-box">安装图片</div>
            <div style="display: flex; width: 100%; flex-wrap: wrap">
              <el-image
                v-for="(item, index) in formReport?.disassemblyPics"
                :key="index"
                class="imgs"
                :src="item.url"
                alt=""
                :preview-src-list="[item.url]"
              ></el-image>
            </div>
            <div class="tit-box">其他</div>
            <div style="line-height: 30px">
              安装费用：{{ formReport?.installAmount }}
            </div>
            <div style="line-height: 30px">
              计数方式：{{ formReport?.paperType }}
            </div>
            <div style="line-height: 30px">
              黑白计数器：{{ formReport?.blackWhiteCount }}
            </div>
            <div style="line-height: 30px">
              彩色计数器：{{ formReport?.colorCount }}
            </div>
            <div style="line-height: 30px">
              五色计数器：{{ formReport?.fiveColoursCounter }}
            </div>
            <div style="line-height: 30px">运费：{{ formReport?.freight }}</div>
            <div style="line-height: 30px">
              工程师追加费用：{{ formReport?.additionalPay }}
            </div>
            <div style="line-height: 30px">
              工程师减免费用：{{ formReport?.derateAmount }}
            </div>
            <div style="line-height: 30px">
              应付费用：{{ formReport?.totalPay }}
            </div>
          </div>
        </div>
      </div>
    </ProDialog>
    <!--  收货  -->
    <ProDrawer
      :value="receiveVisible"
      title="换机信息"
      size="70%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="closeDialog2"
    >
      <div class="dialog-content-box">
        <div>
          <div class="boxa box1">
            <div class="tit-box">工单信息</div>
            <div class="card-box">
              <el-descriptions :column="2">
                <el-descriptions-item label="工单编号">
                  {{ form.code }}
                </el-descriptions-item>
                <el-descriptions-item label="客户编号">
                  {{ form.customerSeqId }}
                </el-descriptions-item>
                <el-descriptions-item label="工单状态">
                  {{ form.status?.label }}
                </el-descriptions-item>
                <el-descriptions-item label="店铺名称">
                  {{ form.customerName }}
                </el-descriptions-item>

                <el-descriptions-item label="联系电话">
                  {{ form?.consigneePhone }}
                </el-descriptions-item>
                <el-descriptions-item label="店铺地址" :span="2">
                  {{ form?.consigneeFullAddress }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="card-box">
              <el-descriptions :column="2">
                <el-descriptions-item label="预计安装时间">
                  {{ form.expectInstallTime }}
                </el-descriptions-item>
                <el-descriptions-item label="安装工程师">
                  {{ form.engineerName }}
                </el-descriptions-item>
                <el-descriptions-item label="是否包邮">
                  {{ form.freeShipping ? "是" : "否" }}
                </el-descriptions-item>
                <el-descriptions-item label="安装费用">
                  {{ form.installAmount }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="tit-box" style="margin: 0">换机列表</div>
            <ProTable
              :show-pagination="false"
              :show-search="false"
              :show-loading="false"
              :show-setting="false"
              :height="300"
              :columns="installColumns"
              :data="installTableData"
            >
              <!-- 主机类型 -->
              <template #type="{ row }">
                <el-select
                  v-model="row.hostType"
                  :disabled="true"
                  placeholder="请选择主机类型"
                  style="width: 100%"
                  size="small"
                  @change="(e) => handleHostTypeChange(e, row)"
                >
                  <el-option
                    v-for="item in hostTypeListOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
              <template #productId="{ row }">
                <div
                  v-if="row.hostType.value !== '2008'"
                  style="
                    display: flex;
                    justify-content: space-between;
                    gap: 20px;
                  "
                >
                  <el-input
                    v-model="row.productName"
                    disabled
                    size="small"
                    placeholder="请选择选配件"
                  />
                </div>
                <el-cascader
                  v-else
                  ref="ProductIds"
                  v-model="row.productIdName"
                  filterable
                  clearable
                  :options="options"
                  style="width: 100%"
                  size="small"
                  placeholder="请选择机型/系列"
                  :disabled="true"
                  :show-all-levels="false"
                  :props="{
                    label: 'name',
                    value: 'id',
                    children: 'children',
                    expandTrigger: 'click',
                    multiple: false,
                  }"
                  leaf-only
                ></el-cascader>
              </template>
              <!-- 成色 -->
              <template #percentage="{ row }">
                <el-select
                  v-model="row.percentage"
                  size="small"
                  placeholder="请选择成色"
                >
                  <el-option
                    v-for="item in percentageOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
              <template #price="{ row }">
                <el-input-number
                  v-model="row.purchasePrice"
                  :controls="false"
                  :precision="2"
                  size="small"
                  :min="0"
                  style="width: 100%"
                ></el-input-number>
              </template>
              <template #deviceOn="{ row }">
                <el-select v-model="row.deviceOn" style="width: 100%" clearable>
                  <el-option
                    v-for="item in deviceOnOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
              <template #deviceStatus="{ row }">
                <el-select
                  v-model="row.deviceStatus"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="item in deviceStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
              <!-- 储位 -->
              <template #location="{ row }">
                <el-input
                  v-model="row.location"
                  size="small"
                  clearable
                  placeholder="请输入储位"
                />
              </template>
              <!-- 黑白计数器 -->
              <template #blackWhiteCounter="{ row }">
                <el-input-number
                  v-model="row.blackWhiteCounter"
                  style="width: 100%"
                  size="small"
                  :controls="false"
                  :min="0"
                  placeholder="请输入黑白计数器"
                  controls-position="right"
                ></el-input-number>
              </template>
              <!-- 彩色计数器 -->
              <template #colorCounter="{ row }">
                <el-input-number
                  v-model="row.colorCounter"
                  style="width: 100%"
                  size="small"
                  :controls="false"
                  :min="0"
                  placeholder="请输入彩色计数器"
                  controls-position="right"
                ></el-input-number>
              </template>
              <!-- 五色计数器 -->
              <template #fiveColourCounter="{ row }">
                <el-input-number
                  v-model="row.fiveColourCounter"
                  style="width: 100%"
                  :controls="false"
                  size="small"
                  :min="0"
                  placeholder="请输入五色计数器"
                  controls-position="right"
                ></el-input-number>
              </template>
            </ProTable>
          </div>
        </div>
        <div class="btn-box">
          <!--<div-->
          <!--  v-if="-->
          <!--    form.status?.value !== 'close' &&-->
          <!--    form.status?.value !== 'completed'-->
          <!--  "-->
          <!--  class="cancel-button"-->
          <!--  @click="handleDelete(form.id)"-->
          <!--&gt;-->
          <!--  关闭工单-->
          <!--</div>-->
          <el-button type="primary" @click="receiveProduct(form.code)">
            确认收货
          </el-button>
          <el-button @click="closeDialog2">取消</el-button>
        </div>
      </div>
    </ProDrawer>
    <!-- 创建安装工单 -->
    <CreateExchangeOrder ref="exchangeOrder" @refresh="refresh" />
  </div>
</template>
<script>
import CreateExchangeOrder from "@/views/order/components/exchangeOrder.vue";
import {
  closeOrderApi,
  calcPay,
  getInstallOrderByPageApi,
  assignEngineerApi,
  getInstallOrderDetailApi,
  passInstallOrderApi,
  getReceiveMachineApi,
  getInstallMachineApi,
  receiveMachineApi,
} from "@/api/repair";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi, roleMemberApi } from "@/api/user";
import { filterParam, filterParamRange } from "@/utils";
import { productAllApi } from "@/api/dispose";
export default {
  name: "InstallOrder",
  components: { CreateExchangeOrder },
  props: {
    type: { default: "INSTALL", type: String },
  },
  data() {
    return {
      stepActive: 0,
      stepList: [
        "wait_distribute",
        "engineer_departure",
        "wait_confirm",
        "completed",
      ],
      allPay: 0,
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      productIdName: "",
      queryParam: {},
      columns: [
        {
          dataIndex: "code",
          title: "工单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "contractCode",
          title: "合同编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          clearable: true,
          span: 4,
          minWidth: 140,
          valueType: "input",
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          clearable: true,
          minWidth: 140,
          valueType: "input",
        },
        {
          dataIndex: "consigneePhone",
          title: "联系电话",
          isTable: true,
          // isSearch: true,
          // clearable: true,
          // valueType: "input",
          minWidth: 110,
        },
        {
          dataIndex: "status",
          title: "工单状态",
          isTable: true,
          formatter: (row) =>
            row.cancelStatus && row.status?.value === "close"
              ? "客户取消"
              : row.status?.label,
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [
            { label: "待分配", value: "wait_distribute" },
            { label: "已分配", value: "distributed" },
            { label: "工程师出发", value: "engineer_departure" },
            { label: "待确认", value: "wait_confirmed" },
            { label: "已完成", value: "completed" },
            { label: "待结算", value: "to_be_settled" },
            { label: "待审核", value: "wait_audit" },
            { label: "关闭", value: "close" },
          ],
        },
        {
          dataIndex: "productName",
          title: "品牌/机型",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "createdAt",
          title: "下单日期",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "expectInstallTime",
          title: "预计安装时间",
          isTable: true,
          width: 150,
        },
        // {
        //   dataIndex: "payMode",
        //   title: "支付方式",
        //   isTable: true,
        //   formatter: (row) => row.payMode?.label,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [
        //     { label: "微信支付", value: "WECHART" },
        //     { label: "线下支付", value: "OFFLINE" },
        //   ],
        //   minWidth: 100,
        // },
        {
          dataIndex: "totalPay",
          title: "总费用",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "createTimeStartDate",
          title: "下单日期",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
        {
          dataIndex: "expectInstallTime",
          title: "预计安装时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "engineerName",
          title: "安装工程师",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "engineerIds",
          title: "安装工程师",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "completedAt",
          title: "安装完成日期",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "Actions",
          width: 280,
          title: "操作",
          align: "left",
          fixed: "right",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      // 工程师列表
      workerList: [],
      //新增
      methodType: "add",
      confirmLoading: false,
      form: {},
      dialogTitle: "",
      dialogVisible: false,
      form1: {},
      form3: {},
      dialogVisible1: false,
      dialogVisible2: false,
      formReport: {},
      dialogVisible3: false,
      receiveVisible: false,
      defaultFormParams: {},
      formColumns: [
        {
          dataIndex: "engineerId",
          title: "安装工程师",
          isForm: true,
          formSpan: 24,
          valueType: "select",
          option: [],
          prop: [
            {
              required: true,
              message: "请选择安装工程师",
              trigger: "change",
            },
          ],
        },
      ],
      // totalData: {},
      // 安装列表
      installColumns: [
        {
          dataIndex: "machineNum",
          title: "机器编码",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          minWidth: 100,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 100,
        },
      ],
      // 安装机器列表
      installTableData: [],
      // 退还机器列表
      returnTableData: [],
      // 赠送列表
      giftColumns: [
        {
          dataIndex: "giveType",
          title: "礼品类型",
          isTable: true,
          formatter: (row) => row.giveType?.label,
          minWidth: 120,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "articleCode",
          title: "物品编码",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          width: 150,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "quantity",
          title: "赠送数量",
          isTable: true,
          align: "center",
          minWidth: 120,
        },
      ],
      giftTableData: [],
      percentageOptions: [],
      deviceOnOptions: [],
      deviceStatusOptions: [],
      hostTypeListOptions: [],
      options: [],
    };
  },
  mounted() {
    this.init();
    this.searchList();
    productAllApi().then((res) => {
      this.options = res.data;
    });
    dictTreeByCodeApi(1100).then((res) => {
      this.deviceOnOptions = res.data;
    });
    dictTreeByCodeApi(6600).then((res) => {
      this.deviceStatusOptions = res.data;
    });
    this.getPercentage();
    this.getProductType();
    this.refresh();
  },
  methods: {
    // 加载表格数据
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter, {
          type: this.type,
        })
      );
      const searchRange = [
        {
          createTimeStart: null,
          createTimeEnd: null,
          data: parameter.createTimeStartDate,
        },
        {
          completeTimeStart: null,
          completeTimeEnd: null,
          data: parameter.completedAt,
        },
        {
          installTimeStart: null,
          installTimeEnd: null,
          data: parameter.expectInstallTime,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      ["createdAt", "completedAt", "expectInstallTime"].forEach(
        (key) => delete requestParameters[key]
      );
      getInstallOrderByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
      // this.getTotalData(requestParameters);
    },
    // 创建换机工单
    handleCreateOrder() {
      this.$refs.exchangeOrder.show();
    },
    // getTotalData(params) {
    //   getWorkOrderSummary(params).then((res) => {
    //     this.totalData = res.data;
    //   });
    // },
    getPrice(type) {
      const obj = {
        repairPay: this.form3.repairPay || 0,
        visitPay: this.form3.visitPay || 0,
        needPay: this.form3.needPay,
        id: this.form3.id,
      };
      if (!type) {
        setTimeout(() => {
          calcPay(obj).then((res) => {
            this.allPay = res.data;
          });
        }, 600);
      } else {
        calcPay(obj).then((res) => {
          this.allPay = res.data;
        });
      }
    },
    init() {
      dictTreeByCodeApi(2100).then((res) => {
        this.goodsTypeOptions = res.data;
      });
    },
    // 搜索列表处理
    searchList() {
      // 工程师列表
      this.workerList = [];
      roleMemberApi("1002", { pageNumber: 1, pageSize: 10000 }).then((res) => {
        res.data.rows.map((item) => {
          this.workerList.push({
            value: item.id,
            label: item.name,
          });
        });
      });
      this.formColumns[0].option = this.workerList;
      this.columns.forEach((item) => {
        if (item.dataIndex === "engineerIds") {
          item.option = this.workerList;
        }
      });
    },
    // 初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    // 触发表单提交
    handleDialogOk() {
      this.$refs["proForm"].handleSubmit();
    },
    // 响应表单提交
    async proSubmit(val) {
      try {
        this.confirmLoading = true;
        const result = await assignEngineerApi(val);
        if (result.code === 200) {
          this.$message.success("指派成功");
          this.dialogVisible1 = false;
          this.refresh();
        }
      } finally {
        this.confirmLoading = false;
      }
      // this.form = cloneDeep(val);
      // this.confirmLoading = true;
      // this.update();
    },
    closeDialog() {
      this.dialogVisible = false;
    },
    closeDialog2() {
      this.receiveVisible = false;
    },
    // 查看安装工单报告详情
    handleInstallReport(id) {
      getInstallOrderDetailApi(id).then((res) => {
        this.dialogVisible2 = true;
        this.formReport = res.data;
      });
    },
    // 触发编辑
    handleUpdate(id, productId) {
      this.form1 = {};
      this.form1.id = id;
      this.methodType = "edit";
      this.dialogVisible1 = true;
    },
    // 响应编辑
    update() {
      const obj = {
        engineerId: this.form1.engineerId,
        id: this.form1.id,
      };
      assignEngineerApi(obj)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible1 = false;
          this.dialogVisible = false;
          this.refresh();
        });
    },
    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.code;
      this.resetFrom();
      getInstallOrderDetailApi(row.id).then((res) => {
        this.form = cloneDeep(res.data);
        // this.stepActive = this.stepList.indexOf(this.form.status?.value);
        // if (this.form.exchangeInfos && this.form.exchangeInfos.length > 0) {
        //   this.installTableData =
        //     this.form.exchangeInfos.map((o) => {
        //       return {
        //         ...o,
        //         machineName: o.machineName || "",
        //         machineNum: o.machineCode || "",
        //       };
        //     }) || [];
        // }
        if (this.type === "EXCHANGE") {
          // getReceiveMachineApi
          getInstallMachineApi(row.code).then((res) => {
            this.installTableData = res.data;

            this.installTableData.forEach((item) => {
              if (item.hostType?.value === "2008" && item.fullProductPath) {
                const trimmedStr = item.fullProductPath.trim("/");
                item.productIdName = trimmedStr.split("/").filter(Boolean);
              }
            });
          });
          getReceiveMachineApi(row.code).then((res) => {
            this.returnTableData = res.data;

            this.returnTableData.forEach((item) => {
              if (item.hostType?.value === "2008" && item.fullProductPath) {
                const trimmedStr = item.fullProductPath.trim("/");
                item.productIdName = trimmedStr.split("/").filter(Boolean);
              }
            });
          });
          this.installColumns = [
            {
              dataIndex: "machineNum",
              title: "机器编号",
              isTable: true,
              minWidth: 120,
            },
            {
              dataIndex: "hostType",
              title: "主机类型",
              isTable: true,
              align: "center",
              formatter: (row) => row.hostType?.label,
              // tableSlot: "type",
              minWidth: 100,
            },
            {
              dataIndex: "productName",
              title: "机型/系列/选配件",
              isTable: true,
              align: "center",
              minWidth: 150,
            },
            {
              dataIndex: "percentage",
              title: "成色",
              isTable: true,
              align: "center",
              formatter: (row) => row.percentage?.label,
              // tableSlot: "percentage",
              minWidth: 120,
            },
            // {
            //   dataIndex: "puschaseDetailStatus",
            //   title: "状态",
            //   isTable: true,
            //   align: "center",
            //   formatter: (row) => {
            //     switch (row.puschaseDetailStatus) {
            //       case "WAIT_APPROVE":
            //         return "待确认";
            //       case "WAIT_RECEIVE":
            //         return "待收货";
            //       case "SUCCESS":
            //         return "已收货";
            //       case "CANCEL":
            //         return "已退货";
            //       default:
            //         return "未知";
            //     }
            //   },
            //   minWidth: 80,
            // },
            {
              dataIndex: "deviceOn",
              title: "设备新旧",
              isTable: true,
              align: "center",
              formatter: (row) => row.deviceOn?.label,
              minWidth: 100,
            },
            {
              dataIndex: "deviceStatus",
              title: "设备状态",
              isTable: true,
              align: "center",
              formatter: (row) => row.deviceStatus?.label,
              // tableSlot: "deviceStatus",
              minWidth: 100,
            },
            {
              dataIndex: "purchasePrice",
              title: "单价",
              isTable: true,
              align: "center",
              // tableSlot: "price",
              minWidth: 100,
            },
            // {
            //   dataIndex: "originCode",
            //   title: "原机器编号",
            //   isTable: true,
            //   align: "center",
            //   tableSlot: "originCode",
            //   minWidth: 120,
            // },
            // {
            //   dataIndex: "deviceSequence",
            //   title: "机器序列号",
            //   isTable: true,
            //   align: "center",
            //   tableSlot: "deviceSequence",
            //   minWidth: 120,
            // },
            {
              dataIndex: "location",
              title: "储位",
              isTable: true,
              align: "center",
              // tableSlot: "location",
              minWidth: 100,
            },
            {
              dataIndex: "blackWhiteCounter",
              title: "黑白计数器",
              isTable: true,
              align: "center",
              // tableSlot: "blackWhiteCounter",
              minWidth: 100,
            },
            {
              dataIndex: "colorCounter",
              title: "彩色计数器",
              isTable: true,
              align: "center",
              // tableSlot: "colorCounter",
              minWidth: 100,
            },
            {
              dataIndex: "fiveColourCounter",
              title: "五色计数器",
              isTable: true,
              align: "center",
              // tableSlot: "fiveColourCounter",
              minWidth: 100,
            },
          ];
        } else {
          this.installColumns = [
            {
              dataIndex: "machineNum",
              title: "机器编码",
              isTable: true,
              minWidth: 100,
            },
            {
              dataIndex: "hostType",
              title: "主机类型",
              isTable: true,
              formatter: (row) => row.hostType?.label,
              minWidth: 100,
            },
            {
              dataIndex: "productName",
              title: "机器型号",
              isTable: true,
              minWidth: 120,
            },
            {
              dataIndex: "percentage",
              title: "成色",
              isTable: true,
              formatter: (row) => row.percentage?.label,
              minWidth: 100,
            },
            {
              dataIndex: "deviceOn",
              title: "设备新旧",
              isTable: true,
              formatter: (row) => row.deviceOn?.label,
              minWidth: 100,
            },
          ];
          this.installTableData = this.form.machines || [];
        }
        this.giftTableData = this.form.customerContractGives || [];
        this.methodType = "info";
        this.dialogVisible = true;
      });
    },
    handleDialogOk3() {
      closeOrderApi(this.form3)
        .then(() => {
          this.$message.success("关闭成功");
          this.dialogVisible3 = false;
        })
        .finally(() => {
          this.refresh();
        });
    },
    // 响应删除
    handleDelete(id) {
      this.dialogVisible3 = true;
      this.form3 = { needPay: false, id: id, repairPay: 0, visitPay: 0 };
    },

    //审核
    handleCheck(row) {
      this.$confirm("此操作将会关闭该工单, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        passInstallOrderApi({ id: row.id, isPass: false }).then((res) => {
          this.refresh();
        });
      });
      // MessageBox.confirm("是否通过该工单？", "提示", {
      //   confirmButtonText: "通过",
      //   cancelButtonText: "不通过",
      //   type: "info",
      //   distinguishCancelAndClose: true,
      // })
      //   .then(() => {
      //     passInstallOrderApi({ id: row.id, isPass: true }).then((res) => {
      //       this.refresh();
      //     });
      //   })
      //   .catch((action) => {
      //     if (action == "cancel") {
      //       //不通过
      //       passInstallOrderApi({ id: row.id, isPass: false }).then((res) => {
      //         this.refresh();
      //       });
      //     }
      //   });
    },
    //确认收货
    receiveProduct(code) {
      this.$confirm("是否确认收货?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const args = {
          contractCode: this.form.contractCode,
          machines: this.installTableData,
        };
        receiveMachineApi(args).then((res) => {
          this.$message.success("操作成功");
          this.receiveVisible = false;
          this.refresh();
        });
      });

      // receiveInstallOrderApi(code).then((res) => {
      //   if (res.code == 200) {
      //     this.$message.success("确认收货成功");
      //     this.receiveVisible = false;
      //     this.refresh();
      //   }
      // });
    },
    //打开确认收货页面
    handleReceive(row) {
      this.resetFrom();
      getInstallOrderDetailApi(row.id).then((res) => {
        this.form = cloneDeep(res.data);
        // if (this.form.exchangeInfos && this.form.exchangeInfos.length > 0) {
        //   this.installTableData =
        //     this.form.exchangeInfos.map((o) => {
        //       return {
        //         ...o,
        //         machineName: o.machineName || "",
        //         machineNum: o.machineCode || "",
        //       };
        //     }) || [];
        // }
        this.installColumns = [
          {
            dataIndex: "machineNum",
            title: "机器编号",
            isTable: true,
            minWidth: 120,
          },
          {
            dataIndex: "type",
            title: "主机类型",
            isTable: true,
            align: "center",
            tableSlot: "type",
            minWidth: 100,
          },
          {
            dataIndex: "productId",
            title: "机型/系列/选配件",
            isTable: true,
            align: "center",
            tableSlot: "productId",
            minWidth: 150,
          },
          {
            dataIndex: "percentage",
            title: "成色",
            isTable: true,
            align: "center",
            tableSlot: "percentage",
            minWidth: 120,
          },
          // {
          //   dataIndex: "puschaseDetailStatus",
          //   title: "状态",
          //   isTable: true,
          //   align: "center",
          //   formatter: (row) => {
          //     switch (row.puschaseDetailStatus) {
          //       case "WAIT_APPROVE":
          //         return "待确认";
          //       case "WAIT_RECEIVE":
          //         return "待收货";
          //       case "SUCCESS":
          //         return "已收货";
          //       case "CANCEL":
          //         return "已退货";
          //       default:
          //         return "未知";
          //     }
          //   },
          //   minWidth: 80,
          // },
          {
            dataIndex: "deviceOn",
            title: "设备新旧",
            isTable: true,
            align: "center",
            tableSlot: "deviceOn",
            minWidth: 100,
          },
          {
            dataIndex: "deviceStatus",
            title: "设备状态",
            isTable: true,
            align: "center",
            tableSlot: "deviceStatus",
            minWidth: 100,
          },
          {
            dataIndex: "purchasePrice",
            title: "单价",
            isTable: true,
            align: "center",
            tableSlot: "price",
            minWidth: 100,
          },
          // {
          //   dataIndex: "originCode",
          //   title: "原机器编号",
          //   isTable: true,
          //   align: "center",
          //   tableSlot: "originCode",
          //   minWidth: 120,
          // },
          // {
          //   dataIndex: "deviceSequence",
          //   title: "机器序列号",
          //   isTable: true,
          //   align: "center",
          //   tableSlot: "deviceSequence",
          //   minWidth: 120,
          // },
          {
            dataIndex: "location",
            title: "储位",
            isTable: true,
            align: "center",
            tableSlot: "location",
            minWidth: 100,
          },
          {
            dataIndex: "blackWhiteCounter",
            title: "黑白计数器",
            isTable: true,
            align: "center",
            tableSlot: "blackWhiteCounter",
            minWidth: 100,
          },
          {
            dataIndex: "colorCounter",
            title: "彩色计数器",
            isTable: true,
            align: "center",
            tableSlot: "colorCounter",
            minWidth: 100,
          },
          {
            dataIndex: "fiveColourCounter",
            title: "五色计数器",
            isTable: true,
            align: "center",
            tableSlot: "fiveColourCounter",
            minWidth: 100,
          },
        ];
        this.receiveVisible = true;
      });
      getReceiveMachineApi(row.code).then((res) => {
        console.log(res.data);
        this.installTableData = res.data;

        this.installTableData.forEach((item) => {
          Object.keys(item).forEach((key) => {
            item[key] = item[key].value ? item[key].value : item[key];
          });
          if (item.hostType === "2008" && item.fullProductPath) {
            const trimmedStr = item.fullProductPath.trim("/");
            item.productIdName = trimmedStr.split("/").filter(Boolean);
          }
        });
      });
    },
    // 获取主机类型
    async getPercentage() {
      try {
        const result = await dictTreeByCodeApi(2500);
        if (result.code === 200) {
          this.percentageOptions = result.data;
        }
      } catch (error) {
        this.percentageOptions = [];
      }
    },
    async getProductType() {
      try {
        const result = await dictTreeByCodeApi(2000);
        if (result.code === 200) {
          this.hostTypeListOptions = result.data;
        }
      } catch (error) {
        this.hostTypeListOptions = [];
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>
<style>
.el-checkbox {
  line-height: 40px;
}

.el-collapse {
  border: none;
}

.el-collapse-item__header {
  border: none;
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-item__content {
  padding: 0;
}

.el-collapse-item:last-child {
  margin: auto;
}
</style>
<style lang="scss" scoped>
::v-deep .el-upload--picture-card,
::v-deep .el-upload-list__item {
  width: 120px;
  height: 120px;
}

.sp-content {
  padding: 10px;
  width: 90%;
  margin: auto;
}

.add-sp-box {
  z-index: 2;
  position: absolute;
  bottom: 0;
  text-align: center;
  width: 100%;
  background: #fff;
  padding: 10px 0;
  border-top: 1px solid #ebeaea;
}

.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-content-box {
  position: relative;
  height: 100%;
  overflow: scroll;
  padding-bottom: 80px;
  .steps-box {
    position: absolute;
    width: 80%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    z-index: 2;
  }

  .content-fix {
    height: calc(100vh - 110px);
    overflow: auto;
  }

  .tit-box {
    width: 100%;
    padding: 5px 10px;
    color: #409eff;
    position: relative;
    margin: 20px auto;
    font-size: 16px;
    font-weight: 800;

    &::before {
      content: "";
      width: 5px;
      height: 20px;
      background: #409eff;
      display: inline-block;
      position: absolute;
      left: -1px;
      top: 4px;
    }
  }
}
.boxa {
}
.card-box {
  border-bottom: 5px solid #f1eeee;
  margin: 10px 0;
  padding: 0 20px;
}
.imgs {
  height: 120px;
  margin: 10px;
  max-width: calc((100% - 80px) / 4);
}
.imgs1 {
  height: 120px;
  width: 120px;
  float: left;
}
.btn-box {
  position: fixed;
  bottom: 0;
  width: 70%;
  background: #fff;
  right: 0;
  padding: 20px;
}
</style>
