<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-10-11 14:20:13
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-10-11 14:25:36
 * @FilePath: \benyin-web\src\views\procure\partApply.vue
 * @Description: 采购 - 零件申请
 * 
-->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-params="queryParams"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #picUrls="{ row }">
        <el-image
          v-if="row.picUrls && row.picUrls.length > 0"
          style="max-width: 100px; max-height: 100px"
          :src="getPicsUrlImg(row)"
          :preview-src-list="[getPicsUrlImg(row)]"
        ></el-image>
      </template>
      <template #machine="slotProps">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in slotProps.row.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <el-button slot="reference" type="text" size="mini">
            适用机型
          </el-button>
        </el-popover>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-circle-check" @click="confirmProcure(row)">
            已采购
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="editDrawer"
      :title="editDrawerTitle"
      :confirm-loading="drawerConfirmLoading"
      :confirm-text="'确定'"
      :no-footer="editType === 'info'"
      :no-confirm-footer="editType === 'audit'"
      size="85%"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParams"
        :form-list="fromColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="editType"
        @proSubmit="formSubmit"
      >
        <template #applyList>
          <ProTable
            ref="purchaseApplyTable"
            :data="purchaseApplyData"
            :columns="purchaseApplyColumns"
            :height="400"
            :show-search="false"
            :show-loading="false"
            :show-setting="false"
          >
            <template v-if="editType === 'add'" #btn>
              <el-button
                type="success"
                class="add-btn"
                size="mini"
                icon="el-icon-plus"
                @click="handleChooseSupply()"
                >选择</el-button
              >
            </template>
            <template #number="{ row }">
              <el-input-number
                v-model="row.planNum"
                :disabled="editType === 'info'"
                style="width: 100px"
                size="mini"
                :min="0"
                :controls="false"
                @change="handleNumChange"
              ></el-input-number>
            </template>
            <template #approveNum="{ row }">
              <el-input-number
                v-model="row.approveNum"
                :disabled="editType === 'info' && !checkBtn"
                :min="0"
                size="mini"
                style="width: 100px"
                @change="handleApproveNumChange"
              ></el-input-number>
            </template>

            <template #machine="{ row }">
              <el-popover
                placement="bottom"
                title=""
                width="700"
                trigger="click"
              >
                <div style="margin: 20px; height: 400px; overflow-y: scroll">
                  <el-descriptions
                    class="margin-top"
                    title="适用机型"
                    :column="1"
                    border
                  >
                    <el-descriptions-item
                      v-for="item in row.machine"
                      :key="item.id"
                    >
                      <template slot="label"> 品牌/系列/机型 </template>
                      {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>

                <el-button slot="reference" size="mini">适用机型</el-button>
              </el-popover>
            </template>
            <template #action="{ row, index }">
              <div
                v-if="editType === 'add' || editType === 'audit'"
                class="fixed-width"
              >
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDelete(row, index)"
                  >移除</el-button
                >
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
      <template #footer>
        <div v-if="editType === 'audit'" class="footer-btn">
          <el-button type="danger" @click="handleAudit('REJECT')">
            驳回
          </el-button>
          <el-button type="primary" @click="handleAudit('PASS')">
            审核通过
          </el-button>
          <el-button @click="handleCancel">取消</el-button>
        </div>
      </template>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { warehouseListApi } from "@/api/store";
import { Message } from "element-ui";
import { getArticleApplyApi } from "@/api/procure";
export default {
  name: "PartApplyList",
  data() {
    return {
      queryParams: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "picUrls",
          title: "零件图片",
          isTable: true,
          tableSlot: "picUrls",
          width: 120,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "partName",
          title: "零件名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        // {
        //   dataIndex: "type",
        //   title: "物品小类",
        //   isTable: true,
        //   formatter: (row) => row.type.label,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi2(2100, 2101),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        //   minWidth: 120,
        // },
        {
          dataIndex: "machine",
          title: "适用机型",
          isTable: true,
          tableSlot: "machine",
          minWidth: 100,
        },
        {
          dataIndex: "number",
          title: "申请数量",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "requireDate",
          title: "需要时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "remark",
          title: "申请原因",
          isTable: true,
          minWidth: 200,
        },
        {
          dataIndex: "createdBy",
          title: "申请人",
          isTable: true,
          formatter: (row) => row.createdBy.name,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "createdAt",
          title: "申请时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => (row.status == "1" ? "已采购" : "未采购"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "未采购",
              value: 0,
            },
            {
              label: "已采购",
              value: 1,
            },
          ],
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 100,
        },
      ],
      tableData: [],
      editType: "add",
      editDrawer: false,
      editDrawerTitle: "申请详情",
      drawerConfirmLoading: false,
      // 表单
      formParams: {},
      fromColumns: [
        {
          dataIndex: "warehouseId",
          title: "选择仓库",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择仓库",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "purchaseCode",
          title: "采购单编号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "applyList",
          title: "采购清单",
          isForm: true,
          formSlot: "applyList",
          formSpan: 24,
        },
        {
          dataIndex: "deliveryTime",
          title: "期望发货时间",
          isForm: true,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请选择期望发货时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "arrivalTime",
          title: "期望到货时间",
          isForm: true,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请选择期望到货时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "price",
          title: "总价",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
      ],
      formLoading: false,
      // 申请清单
      purchaseApplyColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "articleName",
          isSearch: true,
          clearable: true,
          title: "物品名称",
          valueType: "input",
          isTable: true,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          width: 180,
          isTable: true,
        },
        {
          dataIndex: "machine",
          isTable: true,
          title: "适用机型",
          valueType: "input",
          width: "150",
          tableSlot: "machine",
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          width: 250,
          isTable: true,
          sortable: true,
        },
        {
          dataIndex: "warehouseNumber",
          title: "库存量",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          tableSlot: "price",
          minWidth: 150,
        },
        {
          dataIndex: "planNum",
          title: "计划采购数量",
          isTable: true,
          width: 150,
          tableSlot: "number",
        },
        {
          dataIndex: "approveNum",
          title: "确认采购数量",
          isTable: true,
          width: 180,
          tableSlot: "approveNum",
        },
        {
          dataIndex: "purchasePrice",
          title: "采购金额",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          fixed: "right",
          width: 180,
          align: "left",
          isTable: true,
          tableSlot: "action",
        },
      ],
      purchaseApplyData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParams = filterParam(
        Object.assign({}, parameter, this.queryParams)
      );
      const searchRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParams, searchRange);
      const requestParameters = cloneDeep(this.queryParams);
      delete requestParameters.createdAt;
      getArticleApplyApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    getPicsUrlImg(row) {
      return row?.picUrls?.[0]?.url;
    },
    // 确认已采购
    confirmProcure(row) {
      this.$confirm("是否已采购该零件?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$message({
          type: "success",
          message: "删除成功!",
        });
      });
    },
    handleEdit(row, type) {
      this.editDrawer = true;
      this.editType = type;
      this.editDrawerTitle = this.getDrawerTitleText(type);
    },
    getDrawerTitleText(type) {
      switch (type) {
        case "info":
          return "查看零件采购申请";
        case "add":
          return "新增零件采购申请";
        case "audit":
          return "审核零件采购申请";
        default:
          return "查看零件采购申请";
      }
    },
    handleOk() {
      this.$refs.ProForm.handleSubmit();
    },
    formSubmit(val) {
      console.log(val);
    },
    handleAudit(type) {
      const confirmTitle = type === "PASS" ? "通过" : "驳回";
      const promptType = type === "PASS" ? "success" : "error";
      this.$confirm(`此操作将会${confirmTitle}该零件申请，是否继续?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: promptType,
      }).then(() => {
        this.refresh();
        this.handleCancel();
        Message.success("操作成功");
      });
    },
    handleCancel() {
      this.editDrawer = false;
      this.editType = "add";
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
