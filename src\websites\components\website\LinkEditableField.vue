<template>
  <div class="link-editable-field">
    <!-- 显示模式 -->
    <div
      v-if="!isEditing"
      :class="['field-display', className, { 'field-changed': isChanged }]"
      @click="startEdit"
    >
      <span v-if="text && link" class="link-text">{{ text }}</span>
      <span v-else-if="text" class="text-only">{{ text }}</span>
      <span v-else class="field-placeholder">点击编辑链接</span>
      <i v-if="isChanged" class="el-icon-edit-outline field-changed-icon"></i>
    </div>

    <!-- 编辑模式 -->
    <el-dialog
      title="编辑链接信息"
      :visible.sync="isEditing"
      width="600px"
      :before-close="handleCancel"
      append-to-body
    >
      <div class="edit-form">
        <el-form :model="editForm" :rules="rules" ref="editForm" label-width="80px">
          <el-form-item label="显示文本" prop="text">
            <el-input
              v-model="editForm.text"
              placeholder="请输入显示文本"
              :maxlength="100"
              show-word-limit
            />
            <div class="form-tip">链接显示的文本内容</div>
          </el-form-item>
          
          <el-form-item label="链接地址" prop="link">
            <el-input
              v-model="editForm.link"
              placeholder="请输入链接地址，如：https://example.com"
              :maxlength="500"
              show-word-limit
            />
            <div class="form-tip">完整的URL地址，包含http://或https://</div>
          </el-form-item>
          
          <!-- 链接预览 -->
          <el-form-item v-if="editForm.text && editForm.link" label="预览效果">
            <div class="link-preview">
              <a 
                :href="editForm.link" 
                target="_blank" 
                rel="noopener noreferrer"
                class="preview-link"
              >
                {{ editForm.text }}
                <i class="el-icon-top-right"></i>
              </a>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleClear" type="warning">清空</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'LinkEditableField',
  props: {
    // 显示文本
    text: {
      type: String,
      default: ''
    },
    // 链接地址
    link: {
      type: String,
      default: ''
    },
    // 文本字段键名
    textKey: {
      type: String,
      required: true
    },
    // 链接字段键名
    linkKey: {
      type: String,
      required: true
    },
    // 自定义样式类
    className: {
      type: String,
      default: ''
    },
    // 是否已更改
    isChanged: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEditing: false,
      saving: false,
      editForm: {
        text: '',
        link: ''
      }
    }
  },
  computed: {
    rules() {
      return {
        text: [
          {
            max: 100,
            message: '显示文本不能超过100个字符',
            trigger: 'blur'
          }
        ],
        link: [
          {
            pattern: /^https?:\/\/.+/,
            message: '请输入有效的URL地址（以http://或https://开头）',
            trigger: 'blur'
          },
          {
            max: 500,
            message: '链接地址不能超过500个字符',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    startEdit() {
      this.editForm.text = this.text || ''
      this.editForm.link = this.link || ''
      this.isEditing = true
    },
    handleCancel() {
      this.isEditing = false
      this.editForm.text = this.text || ''
      this.editForm.link = this.link || ''
    },
    handleClear() {
      this.editForm.text = ''
      this.editForm.link = ''
    },
    async handleSave() {
      try {
        // 如果有链接但没有文本，使用链接作为文本
        if (this.editForm.link && !this.editForm.text) {
          this.editForm.text = this.editForm.link
        }
        
        // 验证表单
        if (this.editForm.link) {
          await this.$refs.editForm.validate()
        }
        
        this.saving = true
        
        // 触发值更改事件
        this.$emit('change', this.textKey, this.editForm.text)
        this.$emit('change', this.linkKey, this.editForm.link)
        
        // 模拟保存延迟
        await new Promise(resolve => setTimeout(resolve, 300))
        
        this.isEditing = false
        this.$message.success('保存成功')
      } catch (error) {
        console.error('验证失败:', error)
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.link-editable-field {
  .field-display {
    position: relative;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    min-height: 20px;
    display: inline-flex;
    align-items: center;

    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
      border: 1px dashed #409eff;
    }
    
    &.field-changed {
      background-color: rgba(245, 166, 35, 0.1);
      border: 1px solid #f5a623;
      
      .field-changed-icon {
        margin-left: 4px;
        color: #f5a623;
        font-size: 12px;
      }
    }
    
    .link-text {
      color: #409eff;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    .text-only {
      color: #606266;
    }
    
    .field-placeholder {
      color: #c0c4cc;
      font-style: italic;
    }
  }
  
  .edit-form {
    .form-tip {
      margin-top: 4px;
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
    }
    
    .link-preview {
      padding: 8px 12px;
      background-color: #f5f7fa;
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      
      .preview-link {
        color: #409eff;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        
        &:hover {
          text-decoration: underline;
        }
        
        .el-icon-top-right {
          font-size: 12px;
        }
      }
    }
  }
  
  .dialog-footer {
    text-align: right;
  }
}
</style>
