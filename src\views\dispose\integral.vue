<template>
  <div class="view app-container" style="padding: 20px">
    <el-skeleton :loading="confirmLoading" animated>
      <template #template>
        <el-skeleton-item variant="p" style="width: 100%; height: 600px" />
      </template>

      <template #default>
        <el-form
          ref="editForm"
          :model="editForm"
          :rules="rules"
          label-width="150px"
          label-position="left"
          class="integral-form"
        >
          <el-card class="setting-card">
            <div slot="header">积分获取设置</div>

            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="分享入驻获得：" prop="registerGet">
                  <el-switch
                    v-model="editForm.registerGet"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  ></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="入驻获得数量：" prop="registerGetNum">
                  <el-input-number
                    v-model="editForm.registerGetNum"
                    :min="0"
                    :step="10"
                  ></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="分享是否获得：" prop="shareGet">
                  <el-switch
                    v-model="editForm.shareGet"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  ></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="分享获得数量：" prop="shareGetNum">
                  <el-input-number
                    v-model="editForm.shareGetNum"
                    :min="0"
                    :step="10"
                  ></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="首次消费获得：" prop="consumeGet">
                  <el-switch
                    v-model="editForm.consumeGet"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  ></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="首次消费获得数量：" prop="consumeGetNum">
                  <el-input-number
                    v-model="editForm.consumeGetNum"
                    :min="0"
                    :step="10"
                  ></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>

          <el-card class="setting-card">
            <div slot="header">积分使用设置</div>

            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="订单使用：" prop="orderUse">
                  <el-switch
                    v-model="editForm.orderUse"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  ></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="订单使用上限：" prop="orderUpperLimit">
                  <el-input-number
                    v-model="editForm.orderUpperLimit"
                    :min="0"
                    :step="100"
                  ></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="工单使用：" prop="repairUse">
                  <el-switch
                    v-model="editForm.repairUse"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  ></el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="工单使用上限：" prop="repairUpperLimit">
                  <el-input-number
                    v-model="editForm.repairUpperLimit"
                    :min="0"
                    :step="100"
                  ></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="抵扣比例" prop="deductionScale">
              <el-col :span="8">
                <el-input-group>
                  <el-input
                    v-model="editForm.deductionScale"
                    :min="0"
                    :max="100"
                    :step="1"
                    type="number"
                    style="width: 100%"
                  >
                    <template #suffix>积分 = 1元</template>
                  </el-input>
                </el-input-group>
              </el-col>
            </el-form-item>
            <!--<el-form-item label="抵扣比例(元)： " required>-->
            <!--  <el-row justify="center">-->
            <!--    <el-col :span="6">-->
            <!--      <el-row>-->
            <!--        <el-col :span="11">-->
            <!--          <el-form-item>-->
            <!--            <el-input-number-->
            <!--              style="width: 100%"-->
            <!--              value="100"-->
            <!--              disabled-->
            <!--              :controls="false"-->
            <!--            />-->
            <!--          </el-form-item>-->
            <!--        </el-col>-->
            <!--        <el-col class="line" :span="2">-->
            <!--          <div style="text-align: center">:</div>-->
            <!--        </el-col>-->
            <!--        <el-col :span="11">-->
            <!--          <el-form-item prop="deductionScale">-->
            <!--            <el-input-number-->
            <!--              v-model="editForm.deductionScale"-->
            <!--              style="width: 100%"-->
            <!--              :controls="false"-->
            <!--            ></el-input-number>-->
            <!--          </el-form-item>-->
            <!--        </el-col>-->
            <!--      </el-row>-->
            <!--    </el-col>-->
            <!--  </el-row>-->
            <!--</el-form-item>-->
          </el-card>

          <el-form-item>
            <el-button
              type="primary"
              :loading="confirmLoading"
              @click="handleSubmit"
            >
              保存
            </el-button>
            <el-button @click="getIntegralForm">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
    </el-skeleton>
  </div>
</template>

<script>
import { editIntegralConfigApi, getIntegralConfigApi } from "@/api/dispose";

export default {
  name: "Integral",
  data() {
    const validatePositiveNumber = (rule, value, callback) => {
      if (value < 0) {
        callback(new Error("数值不能小于0"));
      } else {
        callback();
      }
    };

    return {
      editForm: {
        registerGet: false,
        registerGetNum: 100,
        shareGet: true,
        shareGetNum: 100,
        consumeGet: true,
        consumeGetNum: 100,
        orderUse: true,
        orderUpperLimit: 1000,
        repairUse: true,
        repairUpperLimit: 1000,
        deductionScale: 1,
      },
      rules: {
        registerGet: [
          {
            required: false,
            message: "请选择分享入驻获得积分",
            trigger: "change",
          },
        ],
        registerGetNum: [
          { required: false, message: "请输入入驻获得积分数量" },
          { validator: validatePositiveNumber, trigger: "change" },
        ],
        shareGet: [
          {
            required: false,
            message: "请选择分享是否获得积分",
            trigger: "change",
          },
        ],
        shareGetNum: [
          { required: false, message: "请输入分享获得积分数量" },
          { validator: validatePositiveNumber, trigger: "change" },
        ],
        consumeGet: [
          {
            required: false,
            message: "请选择首次消费是否获得积分",
            trigger: "change",
          },
        ],
        consumeGetNum: [
          { required: false, message: "请输入首次消费获得积分数量" },
          { validator: validatePositiveNumber, trigger: "change" },
        ],
        orderUse: [
          {
            required: false,
            message: "请选择订单是否可使用积分抵扣",
            trigger: "change",
          },
        ],
        orderUpperLimit: [
          { required: false, message: "请输入积分使用上限" },
          { validator: validatePositiveNumber, trigger: "change" },
        ],
        repairUse: [
          {
            required: false,
            message: "请选择工单是否可使用积分抵扣",
            trigger: "change",
          },
        ],
        repairUpperLimit: [
          { required: false, message: "请输入积分抵扣上限" },
          { validator: validatePositiveNumber, trigger: "change" },
        ],
        deductionScale: [
          { required: false, message: "请输入抵扣比例" },
          { validator: validatePositiveNumber, trigger: "change" },
        ],
      },
      confirmLoading: false,
    };
  },

  mounted() {
    this.getIntegralForm();
  },

  methods: {
    async getIntegralForm() {
      try {
        this.confirmLoading = true;
        const result = await getIntegralConfigApi();
        if (result.code === 200) {
          this.editForm = result.data || {};
        }
      } catch (e) {
        this.$message.error("获取积分配置失败");
        this.editForm = {
          registerGet: true,
          registerGetNum: 100,
          shareGet: true,
          shareGetNum: 100,
          consumeGet: true,
          consumeGetNum: 100,
          orderUse: true,
          orderUpperLimit: 300,
          repairUse: true,
          repairUpperLimit: 300,
          deductionScale: 1,
        };
      } finally {
        this.confirmLoading = false;
      }
    },

    async handleSubmit() {
      try {
        await this.$refs.editForm.validate();

        await this.$confirm("确认保存积分配置更改?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        this.confirmLoading = true;
        const result = await editIntegralConfigApi(this.editForm);

        if (result.code === 200) {
          this.$message.success("保存成功");
          await this.getIntegralForm();
        } else {
          this.$message.error(result.message || "保存失败");
        }
      } catch (err) {
        if (err === "cancel") return;
        this.$message.error("操作失败：" + (err.message || err));
      } finally {
        this.confirmLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.setting-card {
  margin-bottom: 20px;

  ::v-deep .el-card__header {
    padding: 10px 20px;
    font-weight: bold;
    background-color: #f5f7fa;
  }
}

.el-input-number {
  width: 100%;
}

.integral-form {
  max-width: 1200px;
  margin: 0 auto;
}

::v-deep .el-form-item__label {
  font-weight: normal;
}

.el-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
</style>
