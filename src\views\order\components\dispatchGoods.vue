<template>
  <div class="app-container">
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      confirm-text="确认发货"
      :confirm-button-disabled="confirmBtnLoading"
      size="95%"
      :top="'10%'"
      @ok="handleDrawerOk"
      @cancel="drawerVisible = false"
    >
      <!-- 买家下单 -->
      <div v-if="orderInfo" ref="dialogContent" class="order-fix">
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="订单信息"
            :column="4"
            border
          >
            <el-descriptions-item label="订单状态">
              {{ getOrderStatusChinese(orderDetailInfo?.orderStatus) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单编号">
              {{ orderDetailInfo.orderNum }}
            </el-descriptions-item>
            <el-descriptions-item label="配送方式">
              {{ orderDetailInfo?.logisticsProvider?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="关联客户">
              {{ orderInfo.companyName }}
            </el-descriptions-item>
            <el-descriptions-item label="下单用户">
              {{ orderInfo?.buyerName }}
            </el-descriptions-item>
            <el-descriptions-item label="客户等级">
              {{ orderDetailInfo?.customerLevel?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="下单手机号">
              {{ orderDetailInfo?.consigneePhone }}
            </el-descriptions-item>
            <!--<el-descriptions-item label="支付方式">-->
            <!--  {{ orderDetailInfo?.payMode?.label }}-->
            <!--</el-descriptions-item>-->
            <!--<el-descriptions-item label="支付金额（元）">-->
            <!--  {{ orderDetailInfo?.actualAmount }}-->
            <!--</el-descriptions-item>-->
            <el-descriptions-item label="收货地址" :span="3">
              {{ orderDetailInfo?.consigneeFullAddress }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 商品信息 -->
        <div class="m-t-8">
          <p class="tit-box m-b-12">机器信息</p>
          <ProTable
            ref="ProSPXXTable"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="columns"
            :show-pagination="false"
            :show-loading="false"
            :data="tradeOrderTableData"
            :show-setting="false"
            :show-search="false"
            :show-table-operator="false"
            sticky
            :height="400"
          >
            <template #machineNum="{ row }">
              <div
                style="display: flex; justify-content: space-between; gap: 20px"
              >
                <el-input v-model="row.machineNum" disabled size="small" />
                <el-button
                  v-if="row.orderStatus === 'WAIT_DELIVER'"
                  type="primary"
                  size="small"
                  @click="chooseGoods(row)"
                >
                  选择出库机器
                </el-button>
              </div>
            </template>
            <template #deviceSequence="{ row }">
              <el-input
                v-model="row.deviceSequence"
                size="small"
                :disabled="row.orderStatus !== 'WAIT_DELIVER'"
              />
            </template>
          </ProTable>
        </div>
      </div>
    </ProDrawer>
    <!-- 选择机器/选配件 -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      :no-footer="true"
      width="70%"
      :top="'2%'"
      @ok="handleDrawerOk"
      @cancel="dialogVisible = false"
    >
      <ProTable
        ref="ProTable"
        :query-param="queryParam"
        :local-pagination="localPagination"
        :columns="goodsColumns"
        :data="goodsTableData"
        :height="400"
        @loadData="loadData"
      >
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button
              icon="el-icon-circle-check"
              @click="selectionCurrRow(row)"
            >
              确认选择
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import {
  operatorTradeOrderDeliveryApi,
  operatorTradeOrderDetailApi,
} from "@/api/operator";
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { getMachinePageApi } from "@/api/store";

export default {
  name: "DispatchGoods",
  data() {
    return {
      drawerVisible: false,
      drawerTitle: "",
      active: 0,
      orderInfo: null,
      orderDetailInfo: {},
      tradeOrderTableData: [],
      columns: [
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          minWidth: 100,
        },
        {
          dataIndex: "itemName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 100,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          tableSlot: "machineNum",
          minWidth: 150,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          tableSlot: "deviceSequence",
          isTable: true,
          minWidth: 100,
        },
      ],
      // 选择机器/选配件
      dialogVisible: false,
      dialogTitle: "选择出库机器/选配件",
      queryParam: {},
      defaultQueryParam: {
        status: ["ON_SALE"],
        isSale: true,
      },
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 10,
      },
      goodsColumns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          // isSearch: true,
          // valueType: "select",
          // option: [],
          // multiple: true,
          // optionMth: () => dictTreeByCodeApi(2000),
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
          minWidth: 100,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "tagName",
          title: "标签型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "placeOrigin",
          title: "产地版本",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "electric",
          title: "供电电压",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          minWidth: 80,
        },
        {
          dataIndex: "isSale",
          title: "是否上架",
          isTable: true,
          formatter: (row) => (row.isSale ? "已上架" : "未上架"),
          minWidth: 80,
        },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "colorCounter",
          title: "彩色计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "fiveColourCounter",
          title: "五色计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "source",
          title: "来源",
          isTable: true,
          formatter: (row) => row.source?.label,
          minWidth: 100,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          minWidth: 80,
        },
        {
          dataIndex: "createdAt",
          title: "入库时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 140,
          tableSlot: "action",
          fixed: "right",
        },
      ],
      goodsTableData: [],
      // 当前操作行
      currentRow: null,
      orderId: null,
      confirmBtnLoading: false,
    };
  },
  mounted() {},
  methods: {
    show(row) {
      this.orderId = row.id;
      this.orderInfo = null;
      this.orderDetailInfo = null;
      this.drawerTitle = "查看 - " + row.orderNum;
      this.drawerVisible = true;
      operatorTradeOrderDetailApi(row.orderNum).then((res) => {
        this.orderInfo = res.data;
        this.orderDetailInfo = res.data.tradeOrder;
        this.tradeOrderTableData =
          this.orderDetailInfo?.tradeOrderDetailList || [];
      });
      this.drawerVisible = true;
    },
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      requestParameters.status = ["ON_SALE"];
      requestParameters.isSale = true;
      requestParameters.hostType = [this.currentRow?.hostType?.value];
      requestParameters.productId = this.currentRow?.productId;
      getMachinePageApi(requestParameters)
        .then((res) => {
          this.goodsTableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    // 确认发货
    handleDrawerOk() {
      this.$confirm("是否确认发货?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          if (!this.tradeOrderTableData.length) {
            return this.$message.error("暂无可出库机器");
          }
          if (!this.tradeOrderTableData.some((item) => item.machineNum)) {
            return this.$message.error("请选择出库机器编号");
          }
          this.confirmBtnLoading = true;
          const deliveryList = this.tradeOrderTableData
            .filter(
              (item) => item.machineNum && item.orderStatus === "WAIT_DELIVER"
            )
            .map((item) => ({
              id: item.id,
              machineNum: item.machineNum,
              deviceSequence: item.deviceSequence,
            }));
          const args = {
            id: this.orderId,
            machineDeliveryDetailVoList: deliveryList,
          };
          const result = await operatorTradeOrderDeliveryApi(args);
          if (result.code === 200) {
            this.drawerVisible = false;
            this.$emit("refresh");
            this.$message.success("操作成功");
          }
        } finally {
          this.confirmBtnLoading = false;
        }
      });
    },
    // 选择出库机器或选配件
    chooseGoods(row) {
      this.currentRow = null;
      this.currentRow = row;
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.ProTable.refresh();
      });
    },
    selectionCurrRow(row) {
      this.$set(this.currentRow, "machineNum", row.machineNum);
      // this.currentRow.deviceSequence = row.deviceSequence;
      this.$set(this.currentRow, "deviceSequence", row.deviceSequence);
      this.dialogVisible = false;
    },
    getOrderStatusChinese(orderStatus) {
      let value = "";
      switch (orderStatus) {
        case "CLOSED":
          value = "订单关闭";
          this.active = null;
          break;
        case "PAID":
          value = "已支付";
          this.active = 2;
          break;
        case "SUCCESS":
          value = "交易成功";
          this.active = 4;
          break;
        case "WAIT_DELIVER":
          value = "待发货";
          this.active = 2;
          break;
        case "WAIT_PAY":
          value = "待支付";
          this.active = 1;
          break;
        case "WAIT_RECEIVE":
          value = "待收货";
          this.active = 3;
          break;
      }
      return value;
    },
  },
};
</script>

<style scoped lang="scss">
.steps-box {
  position: relative;
  width: 80%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  z-index: 2;
}
.order-fix {
  margin-left: 20px;
  font-size: 14px;

  .red {
    color: #d14b50;
  }

  .text-content {
    display: flex;
    justify-content: space-between;
    .text-p {
      //&.right {
      //  position: relative;
      //  left: 85%;
      //  top: 0;
      //}

      color: #606266;

      .p-label {
        color: #606266;
        font-weight: 700;
      }

      //margin-top: 15px;
      margin: 30px 0;
    }
  }

  .content-fixed {
    display: flex;
    justify-content: space-between;

    .text-p {
      flex: 1;
      display: flex;
    }
  }

  .btn-p {
    margin-top: 15px;

    .el-button {
      padding: 8px 29px;
    }
  }

  .order-border-box {
    border: dashed 1px #ccc;
    padding: 10px;
  }

  .title-p {
    background: #d9d9d9;
    color: #232323;
    padding: 5px;
  }
}

.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
.box-box {
  .tradedetail {
    width: 100%;
    height: 100%;
    clear: both;
    margin-top: 20px;
  }

  .trade {
    width: 140px;
    padding: 10px 0;
    text-align: center;
    border: 1px solid #555555;
    border-radius: 20px;
    cursor: pointer;
  }

  .info {
    display: inline-block;

    text {
      display: block;
      text-align: left;
    }
  }

  .iconbaoguo {
    font-size: 40px;
  }

  .trade.active {
    border: 1px solid #ee822f;
    color: #ee822f;
  }
}

.tit-boxs {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px 7px;
  font-size: 16px;
  font-weight: 800;

  // &::before {
  //   content: "";
  //   width: 5px;
  //   height: 20px;
  //   background: #409eff;
  //   display: inline-block;
  //   position: absolute;
  //   left: -1px;
  //   top: 4px;
  // }
}
</style>
