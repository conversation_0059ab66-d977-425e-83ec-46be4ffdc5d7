<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-index
      show-search
      show-loading
      show-pagination
      @loadData="loadData"
    >
      <template #productName>
        <el-cascader
          ref="ProductIds"
          v-model="machine"
          filterable
          clearable
          :options="options"
          style="width: 500px"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleEdit(row)"
          >
            查看
          </el-button>
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>

    <!--  查看详情  -->
    <ProDialog
      :value="showDialog"
      title="详情"
      width="80%"
      :confirm-loading="dialogLoading"
      top="50px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <div id="dialog-content" ref="dialogContent">
        <ProForm
          ref="calculateForm"
          :form-param="editData"
          :form-list="editFormColumns"
          :confirm-loading="dialogLoading"
          :layout="{ formWidth: '100%', labelWidth: '140px' }"
          :open-type="'info'"
        ></ProForm>
        <ProTable
          ref="ChooseGoodsTable"
          row-key="id"
          :data="editData.offerPriceDetails"
          :columns="editTableColumns"
          :show-pagination="false"
          :show-search="false"
          :show-loading="false"
          :show-setting="false"
          :height="dialogTableHeight"
        ></ProTable>
      </div>
      <el-button type="primary" class="image-btn" @click="toImage"
        >生成图片</el-button
      >
    </ProDialog>
  </div>
</template>
<script>
import { Message, MessageBox, Loading } from "element-ui";
import {
  getOfferPriceDetailsApi,
  getOfferPriceListApi,
  removeOfferPriceApi,
} from "@/api/goods";
import { getFullProductTreeApi, productAllApi } from "@/api/dispose";
import html2canvas from "html2canvas";
import { filterParamRange } from "@/utils";

export default {
  name: "OfferSheet",
  data() {
    return {
      tableData: [],
      columns: [
        {
          dataIndex: "productId",
          title: "适用机型",
          isSearch: true,
          multiple: false,
          valueType: "product",
          // searchSlot: "productName",
        },
        {
          dataIndex: "name",
          title: "报价单名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "code",
          title: "核算单号",
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          formatter: (row) => row.productTreeDto.brand,
        },
        {
          dataIndex: "tree",
          title: "产品树",
          isTable: true,
          formatter: (row) => row.productTreeDto.tree,
        },
        {
          dataIndex: "serial",
          title: "系列",
          isTable: true,
          formatter: (row) => row.productTreeDto.serial,
        },
        {
          dataIndex: "machine",
          title: "机型",
          isTable: true,
          formatter: (row) => row.productTreeDto.machine,
        },
        // {
        //   dataIndex: "machine",
        //   title: "总单张成本呢",
        //   isTable: true,
        //   // formatter: (row) => row.productTreeDto.machine,
        // },
        {
          dataIndex: "createBy",
          title: "创建人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          formatter: (row) => row.createBy.name,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
        },
      ],
      queryParam: {},
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      machine: [],
      options: [],
      editData: {},
      showDialog: false,
      dialogLoading: false,
      editFormColumns: [
        {
          dataIndex: "name",
          title: "报价单名称",
          isForm: true,
          valueType: "input",
          formSpan: 10,
        },
        {
          dataIndex: "code",
          title: "报价单编号",
          isForm: true,
          valueType: "input",
          formSpan: 10,
        },
        {
          dataIndex: "productName",
          title: "适用机型",
          isForm: true,
          valueType: "input",
          formSpan: 10,
        },
        {
          dataIndex: "totalCostSingle",
          title: "总单张成本",
          isForm: true,
          valueType: "input",
          formSpan: 10,
        },
      ],
      editTableColumns: [
        {
          title: "OEM编号",
          dataIndex: "numberOem",
          isTable: true,
          formatter: (row) => row.storageArticle.numberOem,
        },
        {
          title: "物品名称",
          dataIndex: "name",
          isTable: true,
          formatter: (row) => row.storageArticle.name,
        },
        {
          title: "物品编号",
          dataIndex: "code",
          isTable: true,
          formatter: (row) => row.storageArticle.code,
        },
        {
          title: "单机数量",
          dataIndex: "pmNum",
          isTable: true,
        },
        {
          title: "所属单元",
          dataIndex: "unit",
          isTable: true,
          formatter: (row) => row.bom.unit.label,
        },
        {
          title: "运营修正生命周期",
          dataIndex: "correctedLifespan",
          isTable: true,
        },
        {
          title: "售价",
          dataIndex: "saleUnitPrice",
          isTable: true,
          formatter: (row) => row.saleSku.saleUnitPrice,
        },
        {
          title: "单张成本（分/张）",
          dataIndex: "costSingle",
          isTable: true,
        },
      ],
      dialogTableHeight: 400,
    };
  },
  mounted() {
    this.init();
    this.refresh();
  },
  methods: {
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
        this.$refs.ProTable.listLoading = false;
        // this.$refs.ProTable.refresh();
      });
    },
    async loadData(params) {
      try {
        this.queryParam = {
          ...this.queryParam,
          ...params,
        };
        const res = [
          {
            createBy: null,
            createEnd: null,
            data: params.createdAt,
          },
        ];
        filterParamRange(this, this.queryParam, res);
        delete this.queryParam.createdAt;
        const result = await getOfferPriceListApi(this.queryParam);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    handleDelete(row) {
      MessageBox.confirm("确定删除该报价单？", "删除报价单", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        removeOfferPriceApi(row.id).then((res) => {
          if (res.code === 200) {
            this.$message.success("删除成功");
            this.refresh();
          }
        });
      });
    },
    handleSelect(item) {
      this.machine = item;
      this.queryParam.productId = item[item.length - 1];
      // this.lastIds = [item[item.length - 1]]
    },
    async handleEdit(row) {
      try {
        this.$refs.ProTable.listLoading = true;
        const { id } = row;
        const result = await getOfferPriceDetailsApi(id);
        if (result.code === 200) {
          const { data } = result;
          const product = await getFullProductTreeApi(data.productId);
          const {
            data: { brand, tree, serial, machine },
          } = product;
          data.productName = brand + "/" + tree + "/" + serial + "/" + machine;
          this.editData = data;
          console.log(this.editData);
          this.showDialog = true;
        }
      } catch (err) {
        Message.error(err.message);
      } finally {
        this.$refs.ProTable.listLoading = false;
      }
    },
    toImage() {
      const loading = Loading.service({
        fullscreen: true,
        text: "正在生成图片...",
      });
      this.dialogTableHeight = 60 + 34 * this.editData.offerPriceDetails.length;
      this.$nextTick(() => {
        html2canvas(this.$refs.dialogContent).then((canvas) => {
          this.getWaterMark("本印猫", canvas);
          const dataURL = canvas.toDataURL("image/png");
          const a = document.createElement("a");
          a.href = dataURL;
          document.body.append(a);
          console.log(a);
          a.download = `报价单-${this.editData.name}.png`;
          a.click();
          document.body.removeChild(a);
          this.dialogTableHeight = 400;
          loading.close();
        });
      });
    },
    getWaterMark(str, canvas) {
      const ctx = canvas.getContext("2d");
      ctx.font = "24px Microsoft YaHei";
      ctx.fillStyle = "rgba(0, 0, 0, 0.2)";
      ctx.textAlign = "left";
      ctx.textBaseline = "top";
      ctx.translate(0, -(canvas.width / 2));
      ctx.rotate((20 * Math.PI) / 180);
      // ctx.fillText('str', 200, 400)
      for (let i = -1000; i < canvas.width; i++) {
        for (let j = -1000; j < canvas.height; j++) {
          ctx.fillText(str, i * 150, j * 150);
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.offer-sheet {
}
#dialog-content {
  padding: 30px 20px;
}
.image-btn {
  position: absolute;
  right: 40px;
  top: 114px;
}
::v-deep .el-dialog__body {
  padding: 0;
  position: relative;
}
</style>
