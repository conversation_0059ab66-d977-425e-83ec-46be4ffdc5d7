<template>
  <div class="image-selector">
    <!-- 图片显示区域 -->
    <div class="image-display" @click="openSelector">
      <div v-if="selectedImage" class="selected-image">
        <img :src="selectedImage.url" :alt="selectedImage.title" />
        <div class="image-overlay">
          <div class="image-actions">
            <el-button size="mini" type="primary" icon="el-icon-edit" @click.stop="openSelector">
              更换
            </el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" @click.stop="removeImage">
              移除
            </el-button>
          </div>
        </div>
      </div>
      <div v-else class="image-placeholder">
        <i class="el-icon-plus"></i>
        <p>点击选择图片</p>
      </div>
    </div>
    
    <!-- 图片选择对话框 -->
    <el-dialog
      title="选择图片"
      :visible.sync="dialogVisible"
      width="80%"
      :close-on-click-modal="false"
      class="image-selector-dialog"
    >
      <div class="dialog-content">
        <!-- 操作栏 -->
        <div class="action-bar">
          <div class="left-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索图片..."
              prefix-icon="el-icon-search"
              @input="handleSearch"
              style="width: 300px;"
            />
            <el-select v-model="selectedCategory" placeholder="选择分类" @change="loadImages">
              <el-option label="全部" value=""></el-option>
              <el-option label="轮播图" value="banner"></el-option>
              <el-option label="产品图" value="product"></el-option>
              <el-option label="案例图" value="case"></el-option>
              <el-option label="其他" value="other"></el-option>
            </el-select>
          </div>
          <div class="right-actions">
            <el-upload
              :action="uploadAction"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleUploadSuccess"
              :before-upload="beforeUpload"
              accept="image/*"
            >
              <el-button type="primary" icon="el-icon-upload">
                上传图片
              </el-button>
            </el-upload>
          </div>
        </div>
        
        <!-- 图片网格 -->
        <div class="image-grid" v-loading="loading">
          <div
            v-for="image in filteredImages"
            :key="image.id"
            class="image-item"
            :class="{ 'selected': selectedImageId === image.id }"
            @click="selectImage(image)"
          >
            <div class="image-wrapper">
              <img :src="image.url" :alt="image.title" />
              <div class="image-info">
                <p class="image-title">{{ image.title }}</p>
                <p class="image-size">{{ formatFileSize(image.size) }}</p>
              </div>
              <div class="image-actions">
                <el-button size="mini" type="primary" icon="el-icon-view" @click.stop="previewImage(image)">
                  预览
                </el-button>
                <el-button size="mini" type="danger" icon="el-icon-delete" @click.stop="deleteImage(image)">
                  删除
                </el-button>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="!loading && filteredImages.length === 0" class="empty-state">
            <i class="el-icon-picture"></i>
            <p>暂无图片</p>
          </div>
        </div>
        
        <!-- 分页 -->
        <div class="pagination-wrapper" v-if="pagination.total > 0">
          <el-pagination
            @current-change="handlePageChange"
            :current-page="pagination.current"
            :page-size="pagination.size"
            layout="prev, pager, next"
            :total="pagination.total"
          />
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelection" :disabled="!selectedImageId">
          确定选择
        </el-button>
      </div>
    </el-dialog>
    
    <!-- 图片预览对话框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="previewVisible"
      width="60%"
      center
    >
      <div class="preview-content" v-if="previewImage">
        <img :src="previewImage.url" :alt="previewImage.title" class="preview-image" />
        <div class="preview-info">
          <p><strong>标题：</strong>{{ previewImage.title }}</p>
          <p><strong>大小：</strong>{{ formatFileSize(previewImage.size) }}</p>
          <p><strong>上传时间：</strong>{{ formatDate(previewImage.createdAt) }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { websiteImageApi } from '@/websites/api/website'
import { getToken } from '@/utils/auth'

export default {
  name: 'ImageSelector',
  props: {
    // 当前选中的图片（可以是对象或字符串URL）
    value: {
      type: [Object, String],
      default: null
    },
    // 图片分类
    category: {
      type: String,
      default: ''
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      dialogVisible: false,
      previewVisible: false,
      loading: false,
      
      // 图片列表
      images: [],
      filteredImages: [],
      
      // 搜索和筛选
      searchKeyword: '',
      selectedCategory: '',
      
      // 分页
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },
      
      // 选择状态
      selectedImageId: null,
      previewImageData: null,
      
      // 搜索防抖定时器
      searchTimer: null
    }
  },
  
  computed: {
    selectedImage() {
      if (!this.value) return null

      // 如果 value 是字符串（URL），转换为对象格式
      if (typeof this.value === 'string') {
        return {
          url: this.value,
          title: '图片',
          id: null
        }
      }

      // 如果 value 是对象，直接返回
      return this.value
    },
    
    uploadAction() {
      return window.config?.api?.uploadURL + '/website-image/upload' || '/api/website-image/upload'
    },
    
    uploadHeaders() {
      return {
        'X-AUTH-TOKEN': getToken()
      }
    }
  },
  
  watch: {
    value: {
      handler(newVal) {
        this.selectedImageId = newVal ? newVal.id : null
      },
      immediate: true
    }
  },
  
  methods: {
    // 打开选择器
    openSelector() {
      this.dialogVisible = true
      this.selectedCategory = this.category
      this.loadImages()
    },
    
    // 加载图片列表
    async loadImages() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.size,
          category: this.selectedCategory,
          keyword: this.searchKeyword
        }
        
        const response = await websiteImageApi.getImagePage(params)
        this.images = response.data.records
        this.pagination.total = response.data.total
        this.filterImages()
        
      } catch (error) {
        console.error('加载图片失败:', error)
        this.$message.error('加载图片失败')
      } finally {
        this.loading = false
      }
    },
    
    // 筛选图片
    filterImages() {
      this.filteredImages = this.images
    },
    
    // 搜索处理
    handleSearch() {
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.pagination.current = 1
        this.loadImages()
      }, 500)
    },
    
    // 分页处理
    handlePageChange(page) {
      this.pagination.current = page
      this.loadImages()
    },
    
    // 选择图片
    selectImage(image) {
      this.selectedImageId = image.id
    },
    
    // 确认选择
    confirmSelection() {
      const selectedImage = this.images.find(img => img.id === this.selectedImageId)
      if (selectedImage) {
        // 根据原始 value 的类型决定输出格式
        const outputValue = typeof this.value === 'string' ? selectedImage.url : selectedImage
        this.$emit('input', outputValue)
        this.$emit('change', outputValue)
      }
      this.dialogVisible = false
    },
    
    // 移除图片
    removeImage() {
      this.$emit('input', null)
      this.$emit('change', null)
    },
    
    // 预览图片
    previewImage(image) {
      this.previewImageData = image
      this.previewVisible = true
    },
    
    // 删除图片
    deleteImage(image) {
      this.$confirm(`确定要删除图片"${image.title}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await websiteImageApi.deleteImage(image.id)
          this.$message.success('删除成功')
          this.loadImages()
        } catch (error) {
          console.error('删除图片失败:', error)
          this.$message.error('删除失败')
        }
      })
    },
    
    // 上传前检查
    beforeUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt5M = file.size / 1024 / 1024 < 5
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过 5MB!')
        return false
      }
      return true
    },
    
    // 上传成功
    handleUploadSuccess(response) {
      if (response.code === 200) {
        this.$message.success('上传成功')
        this.loadImages()
      } else {
        this.$message.error('上传失败')
      }
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.image-selector {
  .image-display {
    cursor: pointer;

    .selected-image {
      position: relative;
      border-radius: 8px;
      overflow: hidden;

      img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        display: block;
      }

      .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        .image-actions {
          display: flex;
          gap: 8px;
        }
      }

      &:hover .image-overlay {
        opacity: 1;
      }
    }

    .image-placeholder {
      height: 200px;
      border: 2px dashed #dcdfe6;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #909399;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        color: #409eff;
      }

      i {
        font-size: 48px;
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

.image-selector-dialog {
  .dialog-content {
    .action-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .left-actions {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }

    .image-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;
      min-height: 300px;

      .image-item {
        border: 2px solid #e4e7ed;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.selected {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        .image-wrapper {
          position: relative;

          img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            display: block;
          }

          .image-info {
            padding: 12px;

            .image-title {
              margin: 0 0 4px 0;
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .image-size {
              margin: 0;
              font-size: 12px;
              color: #909399;
            }
          }

          .image-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          &:hover .image-actions {
            opacity: 1;
          }
        }
      }

      .empty-state {
        grid-column: 1 / -1;
        text-align: center;
        padding: 60px 20px;
        color: #909399;

        i {
          font-size: 64px;
          margin-bottom: 16px;
          display: block;
        }

        p {
          margin: 0;
          font-size: 16px;
        }
      }
    }

    .pagination-wrapper {
      margin-top: 20px;
      text-align: center;
    }
  }
}

.preview-content {
  text-align: center;

  .preview-image {
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .preview-info {
    text-align: left;
    background: #f5f7fa;
    padding: 16px;
    border-radius: 8px;

    p {
      margin: 0 0 8px 0;

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        color: #303133;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .image-selector-dialog {
    .dialog-content {
      .action-bar {
        flex-direction: column;
        gap: 12px;

        .left-actions {
          width: 100%;
          flex-direction: column;

          .el-input {
            width: 100% !important;
          }
        }
      }

      .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 12px;
      }
    }
  }
}
</style>
