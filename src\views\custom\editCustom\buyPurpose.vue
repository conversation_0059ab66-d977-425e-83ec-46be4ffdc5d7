<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :height="550"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-if="type !== 'info'"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleEvent(undefined, 'add')"
          >新增</el-button
        >
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEvent(row, 'info')"
            >查看</el-button
          >
          <el-button
            v-if="type !== 'info'"
            icon="el-icon-edit"
            @click="handleEvent(row, 'edit')"
            >编辑</el-button
          >
          <el-button
            v-if="type !== 'info'"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            >删除</el-button
          >
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="showDrawer"
      :title="title"
      size="65%"
      :confirm-loading="confirmLoading"
      :method-type="methodType"
      :no-footer="methodType === 'info'"
      @ok="handleSubmit"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
      ></ProForm>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi, userListApi } from "@/api/user";
import {
  addCustomerIntentionApi,
  deleteCustomerIntentionApi,
  getCustomerIntentionApi,
  getCustomerIntentionByPageApi,
  updateCustomerIntentionApi,
  getCustomerUserListApi,
} from "@/api/customer";
import { Message } from "element-ui";

export default {
  name: "BuyPurpose",
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    shopName: {
      type: [String, null],
      default: null,
    },
    seqId: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "buyType",
          title: "购买类型",
          isTable: true,
          width: 100,
          formatter: (row) => row.buyType?.label,
        },
        {
          dataIndex: "content",
          title: "具体内容",
          isTable: true,
        },
        {
          dataIndex: "intentionTime",
          title: "期望购买时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "createdBy",
          title: "记录人",
          isTable: true,
          width: 100,
          formatter: (row) => row.createdBy?.name,
        },
        {
          dataIndex: "createdAt",
          title: "记录时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "callState",
          title: "跟进状态",
          isTable: true,
          width: 100,
          formatter: (row) => row.callState?.label,
        },
        {
          dataIndex: "result",
          title: "处理结果",
          isTable: true,
          width: 100,
          formatter: (row) => row.result?.label,
        },
        // {
        //   dataIndex: "record",
        //   title: "拜访记录",
        //   isTable: true,
        //   width: 100,
        // },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 200,
        },
      ],
      tableData: [],
      title: "",
      showDrawer: false,
      methodType: "add",
      confirmLoading: false,
      form: {
        customerSeqId: this.$props.seqId,
        customerName: this.$props.shopName,
      },
      formLoading: false,
      formColumns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "intentionTime",
          title: "期望购买时间",
          isForm: true,
          formSpan: 8,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          prop: [
            {
              required: true,
              message: "请选择期望购买时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "buyType",
          title: "购买类型",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(4700),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择购买类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "operatId",
          title: "登记人",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          option: [],
          prop: [
            {
              required: true,
              message: "请选择登记人",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "reachShopTime",
          title: "登记时间",
          isForm: true,
          formSpan: 8,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          prop: [
            {
              required: true,
              message: "请选择登记时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "content",
          title: "期望购买内容",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
          autosize: { minRows: 4, maxRows: 6 },
          prop: [
            {
              required: true,
              message: "请输入期望购买内容",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "reachShopRole",
          title: "拜访角色",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "reachShopTel",
          title: "拜访人电话",
          isForm: true,
          formSpan: 8,
          valueType: "input",
        },
        {
          dataIndex: "remark",
          title: "回访内容",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
          autosize: { minRows: 4, maxRows: 6 },
        },
        {
          dataIndex: "nextNoticeRemark",
          title: "下次注意事项",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
          autosize: { minRows: 4, maxRows: 6 },
        },
        {
          dataIndex: "result",
          title: "处理结果",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          option: [
            {
              label: "签单成功",
              value: "SUCCESS",
            },
            {
              label: "飞单",
              value: "FAIL",
            },
          ],
        },
        {
          dataIndex: "followUpName",
          title: "跟进人",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "callState",
          title: "跟进状态",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(3700),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
      ],
      userList: [],
    };
  },
  mounted() {
    this.refresh();
    this.operatList();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      getCustomerIntentionByPageApi({
        ...requestParameters,
        customerId: this.$props.id,
      })
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    // 员工列表处理
    operatList() {
      // userListApi({ pageNumber: 1, pageSize: 10000 }).then((res) => {
      //   this.userTureList = res.data.rows;
      //   res.data.rows.map((item) => {
      //     this.userList.push({
      //       value: item.id,
      //       label: item.name,
      //     });
      //   });
      //   this.formColumns[4].option = this.userList;
      //   this.formColumns[12].option = this.userList;
      // });
      getCustomerUserListApi().then((res) => {
        this.userTureList = res.data;
        res.data.map((item) => {
          this.userList.push({
            value: item.id,
            label: item.name,
          });
        });
        this.formColumns[4].option = this.userList;
        this.formColumns[12].option = this.userList;
      });
    },
    handleEvent(row, type) {
      this.methodType = type;
      this.title = this.titleMap(type);
      if (row) {
        // console.log(row);
        getCustomerIntentionApi(row.id).then((res) => {
          const formParam = JSON.parse(JSON.stringify(res.data));
          Object.keys(formParam).forEach((key) => {
            formParam[key] = formParam[key].label
              ? formParam[key].value
              : formParam[key];
          });
          this.form = formParam;
        });
      }
      this.showDrawer = true;
    },
    // handleCreate() {
    //   addCustomerIntentionApi(this.form).then((res) => {
    //     this.refresh();
    //     this.closeDrawer();
    //     Message.success("购买意向新增成功");
    //   });
    // },
    // handleEdit() {
    //   updateCustomerIntentionApi(this.form).then((res) => {
    //     this.refresh();
    //     this.closeDrawer();
    //     Message.success("购买意向新增成功");
    //   });
    // },
    handleDelete(row) {
      this.$confirm("此操作将会删除该条购买意向记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteCustomerIntentionApi(row.id).then((res) => {
          this.refresh();
          this.closeDrawer();
          Message.success("购买意向删除成功");
        });
      });
    },
    titleMap(type) {
      const data = {
        add: "新增购买意向",
        edit: "编辑购买意向",
        info: "查看购买意向",
      };
      return data[type];
    },
    handleSubmit() {
      this.$refs.ProForm.handleSubmit().then((res) => {
        const params = {
          ...this.form,
          customerId: this.$props.id,
        };
        const editApi =
          this.methodType === "add"
            ? addCustomerIntentionApi
            : updateCustomerIntentionApi;
        editApi(params).then((res) => {
          this.refresh();
          this.closeDrawer();
          Message.success("操作成功");
        });
      });
    },
    closeDrawer() {
      this.form = {
        customerSeqId: this.$props.seqId,
        customerName: this.$props.shopName,
      };
      this.showDrawer = false;
    },
    refresh() {
      // this.$refs.ProTable.listLoading = false;
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
