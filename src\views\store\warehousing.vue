<!--
 * @Author: yangzhong
 * @Date: 2023-11-15 17:57:00
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-30 19:31:31
 * @Description: 入库管理
-->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-index
      show-search
      show-loading
      show-pagination
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleEdit(null, 'add')"
        >
          新增入库单
        </el-button>
        <!-- <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleUpload"
          >导入</el-button
        > -->
      </template>
      <!-- <template #imageFiles="slotProps">
        <img
          style="max-width: 100px; max-height: 100px"
          :src="getPicsUrlImg(slotProps.row)"
          @click="searchBigImage(slotProps.row)"
        />
      </template> -->
      <!-- <template #mainWaybills="slotProps">
        <el-input
          v-model="mainWaybills"
          style="width: 60%"
          placeholder="库位"
        ></el-input>
      </template> -->
      <template #inType="{ row }">
        {{ row.inType.label }}
      </template>
      <template #inStatus="{ row }">
        {{ row.inStatus.label }}
      </template>
      <template #remarks="{ row }">
        <!-- <el-tooltip effect="dark" :content="row.remarks" placement="top"> -->
        <div style="display: flex; align-items: center">
          <span class="exceed">{{ row.remarks }}</span>
          <!-- </el-tooltip> -->
          <span
            style="margin-left: 10px; cursor: pointer"
            @click="handleUpdateRemark(row, 'add')"
            ><i class="el-icon-edit"></i
          ></span>
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
          <!-- <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'info')"
            >确定库位</el-button
          > -->
          <!--v-auth="['@ums:manage:warehousing:audit']"-->
          <el-button
            v-if="row.inStatus.value !== 'yrk'"
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'check')"
          >
            审核
          </el-button>
          <!-- <el-button
            v-if="row.inStatus !== '已入库' && row.inType != '退货入库'"
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleEdit(row, 'edit')"
            >编辑</el-button
          >
          <el-button
            v-if="row.inStatus !== '已入库'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            >删除</el-button
          > -->
        </div>
      </template>
    </ProTable>

    <!-- 新增编辑弹窗 -->
    <ProDrawer
      :value="showDrawer"
      :title="drawerTitle"
      size="85%"
      :destroy-on-close="true"
      :no-footer="editType === 'info'"
      :confirm-button-disabled="editFormLoading"
      confirm-text="确认新增"
      @ok="handleDrawerOk"
      @cancel="handleCloseDrawer"
    >
      <ProForm
        ref="editForm"
        :form-param="editForm"
        :form-list="columns"
        :confirm-loading="editFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="editType"
        @proSubmit="editFormSubmit"
      >
        <template #mainWaybill>
          <el-input
            v-model="editForm.mainWaybill"
            :disabled="
              !(
                editType !== 'info' ||
                (showCheckBtn && editForm.inType === '2503')
              )
            "
            placeholder="请输入关联物流主单号"
          />
        </template>
        <template #secondlyWaybill>
          <el-input
            v-model="editForm.secondlyWaybill"
            :disabled="
              !(
                editType !== 'info' ||
                (showCheckBtn && editForm.inType === '2503')
              )
            "
            placeholder="请输入关联物流子单号"
          />
        </template>
        <template #goodsDataList>
          <div class="title-box" style="margin: 0">入库货品信息</div>
          <ProTable
            ref="ProTable1"
            row-key="id"
            :data="goodTableData"
            :columns="goodColumns"
            show-index
            :show-search="false"
            :show-setting="false"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :height="400"
            :show-loading="false"
          >
            <template #btn>
              <el-button
                v-if="editType == 'add'"
                type="success"
                class="add-btn"
                size="mini"
                icon="el-icon-plus"
                @click="handleOpenGoodDialog"
              >
                选择物品
              </el-button>
            </template>
            <template #articleImages="slotProps">
              <el-image
                v-if="
                  slotProps.row.articleImages &&
                  slotProps.row.articleImages.length !== 0
                "
                style="width: 100px; height: 100px"
                :src="slotProps.row?.articleImages?.[0].url"
              ></el-image>
              <div v-else>暂无</div>
            </template>
            <template #code="{ row }">
              <span
                v-if="editType === 'info'"
                class="code_id"
                @click="lookDetailsFn(row)"
                >{{ row.code }}</span
              >
              <span v-if="editType !== 'info'">{{ row.code }}</span>
            </template>
            <template #inStatus="{ row }">
              {{ row }}
            </template>

            <template #location="{ row }">
              <div v-if="editType !== 'add'" class="locationIn">
                <el-input
                  v-if="row.location || !row.check"
                  v-model="row.location"
                  class="el-inputs"
                  :disabled="row.check"
                  size="small"
                  placeholder="请输入储位"
                />
                <span v-if="!row.location && row.check">/</span>
                <el-link
                  v-if="row.check"
                  style="margin-left: 10px"
                  icon="el-icon-edit"
                  :underline="false"
                  @click="row.check = false"
                ></el-link>
                <el-link
                  v-if="!row.check"
                  style="margin-left: 10px"
                  icon="el-icon-edit"
                  :underline="false"
                  @click="saveFn(row)"
                  >保存</el-link
                >
              </div>
              <span v-else>{{ row.location || "/" }}</span>
            </template>
            <template #price="{ row }">
              <el-input
                v-model="row.price"
                :disabled="editType !== 'add'"
                style="width: 100%"
                size="small"
                min="0"
                type="number"
                placeholder="单价"
              />
            </template>
            <template #batchCode="{ row }">
              <el-input
                v-if="editType === 'add'"
                v-model="row.batchCode"
                :disabled="
                  !(
                    editType !== 'info' ||
                    (showCheckBtn && editForm.inType === '2503')
                  )
                "
                size="small"
                placeholder="请输入批次号"
              />
              <span v-else>{{ row.batchCode || "/" }}</span>
            </template>
            <template #inWarehouseNumber="{ row }">
              <el-input
                v-model="row.inWarehouseNumber"
                :disabled="editType === 'info'"
                step-strictly
                :min="0"
                :controls="false"
                size="small"
                type="number"
                oninput="if(value){value=value.replace(/[^\d]/g,'')}
              if(value<=0){value=''}"
                placeholder="应入库数量"
              />
            </template>
            <template #action="{ row }">
              <div class="fixed-width">
                <el-button
                  v-if="
                    showCheckBtn &&
                    editType === 'info' &&
                    row.auditInWarehouseNumber !== row.inWarehouseNumber
                  "
                  type="btn3"
                  icon="none"
                  :disabled="
                    row.auditInWarehouseNumber == row.inWarehouseNumber
                  "
                  @click="handleRkCheck(row)"
                >
                  入库审核
                </el-button>

                <el-button
                  v-if="row.auditInWarehouseNumber > 0"
                  type="primary"
                  icon="none"
                  @click="handleRkLook(row)"
                >
                  入库明细
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
      <!-- <div v-if="editType == 'add'" class="dialog-footer1">
        <div class="btn-box">
          <el-button type="primary" @click="handleCheckPass('2602')"
            >确认入库</el-button
          >
          <el-button @click="handleCloseDrawer">取消</el-button>
        </div> 
       </div>-->
    </ProDrawer>
    <!-- 详情 -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType == 'info'"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '180px' }"
        :open-type="editType"
      >
        <template #numberOem>
          <el-input
            v-model="form.numberOem"
            style="width: 100%"
            disabled
            placeholder="请输入内容"
          ></el-input>
        </template>
        <template #type>
          <el-cascader
            ref="ProductIds"
            v-model="form.type"
            filterable
            :disabled="methodType == 'info'"
            :options="options1"
            style="width: 100%"
            :props="{
              label: 'label',
              value: 'value',
              children: 'children',
              expandTrigger: 'click',
            }"
            clearable
            leaf-only
            @change="handleChange1"
          ></el-cascader>
        </template>
        <template #imageFiles>
          <ProUpload
            v-if="methodType !== 'info'"
            :file-list="form.imageFiles"
            :type="methodType"
            :limit="1"
            :multiple="false"
            @uploadSuccess="handleUploadSuccess"
            @uploadRemove="handleUploadRemove"
          />
          <div class="demo-image__preview">
            <el-image
              v-if="
                methodType == 'info' &&
                form.imageFiles &&
                form.imageFiles.length !== 0
              "
              style="width: 100px; height: 100px"
              :src="form.imageFiles[0].url"
              :preview-src-list="[form.imageFiles[0].url]"
            >
            </el-image>
          </div>
          <!-- <el-image
            v-if="methodType == 'info' && form.imageFiles && form.imageFiles.length !== 0"
            style="width: 100px; height: 100px"
            :src="form.imageFiles[0].url"
            :fit="fit"
          ></el-image> -->
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 选择物品弹窗 -->
    <ProDialog
      :value="showDialog"
      title="选择物品"
      width="60%"
      :confirm-loading="dialogLoading"
      confirm-text="确认选择"
      top="50px"
      @ok="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <ProTable
        ref="AddGoodsTable"
        :data="addGoodsTableData"
        :columns="addGoodsColumns"
        :height="400"
        :query-param="addGoodsQueryParam"
        :local-pagination="addGoodsLocalPagination"
        show-selection
        @loadData="loadGoodsData"
        @handleSelectionChange="handleSelectionChange"
      >
        <!-- <template #action="{ row }">
          <div class="fixed-width">
            <el-button
              type="primary"
              size="mini"
              @click="handleChooseGoods(row)"
              >选择</el-button
            >
          </div>
        </template> -->
      </ProTable>
    </ProDialog>

    <!-- 导入 -->
    <ProDialog
      :value="dialogUpload"
      :title="'导入文件'"
      :confirm-text="'导入'"
      width="400px"
      :top="'10%'"
      @ok="UploadOk"
      @cancel="dialogUpload = false"
    >
      <el-select
        v-model="warehouse"
        style="margin-bottom: 20px"
        placeholder="请选择归属仓库"
      >
        <el-option
          v-for="item in warehouseList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-upload
        ref="upload"
        class="upload-demo"
        :action="actionUrl"
        name="file"
        :headers="{ 'X-AUTH-TOKEN': token }"
        accept=".xls,.xlsx,.XLSX,.XLS"
        :multiple="false"
        :limit="1"
        :file-list="fileList"
        :on-success="handleSuccess"
        :on-change="handleChange"
        :auto-upload="false"
        :data="{ ckid: warehouse }"
      >
        <el-button size="small" type="primary">选择文件</el-button>
        <div slot="tip" class="el-upload__tip">
          提示：仅支持上传“xls”或“xlsx”格式文件。
        </div>
      </el-upload>
      <el-button size="small" type="text" @click="handleDownloadTemplate"
        >下载模板</el-button
      >
    </ProDialog>

    <!-- 备注修改 -->
    <ProDialog
      :value="remarkDialog"
      :title="'编辑备注'"
      :confirm-text="'保存'"
      width="600px"
      :top="'10%'"
      @ok="updateRemark"
      @cancel="handleCloseDialog"
    >
      <ProForm
        ref="editForm"
        :form-param="remarkForm"
        :form-list="remarkColumns"
        :confirm-loading="remarkFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '70px' }"
        :open-type="editType"
        @proSubmit="updateRemark"
      >
      </ProForm>
    </ProDialog>
    <!-- 入库审核 -->
    <ProDialog
      :value="rkdialogVisible"
      :title="'入库审核'"
      width="400px"
      :top="'10%'"
      confirm-text="确认入库"
      @ok="rkOk"
      @cancel="rkdialogVisible = false"
    >
      <ProForm
        v-if="rkdialogVisible"
        ref="rkproform"
        :form-param="rkform"
        :form-list="rkformcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        open-type="edit"
        @proSubmit="rkOkSub"
      ></ProForm>
    </ProDialog>
    <!-- 入库明细 -->
    <ProDialog
      :value="rkdialogLook"
      :title="'入库明细'"
      width="400px"
      :top="'10%'"
      :no-footer="true"
      @cancel="rkdialogLook = false"
    >
      <div style="height: 400px; overflow-y: scroll">
        <el-timeline>
          <el-timeline-item v-for="(item, index) in rkLook" :key="index">
            <el-card>
              <div>入库数量：{{ item.number }}</div>
              <div>批次号：{{ item.batchCode }}</div>
              <div>入库时间：{{ item.time }}</div>
              <div>操作人：{{ item.operatorName }}</div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </ProDialog>

    <!-- 领料详情框  -->
    <ProDrawer
      :value="returnDialogVisible"
      :title="'入库单详情'"
      size="60%"
      :top="'10%'"
      :no-footer="true"
      @cancel="returnDialogVisible = false"
    >
      <ProForm
        v-if="returnDialogVisible"
        ref="proform"
        :form-param="returnForm"
        :form-list="returnFormColumns"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="'info'"
      >
        <template #status>
          {{
            returnForm.status == "CREATE"
              ? "提交申请"
              : returnForm.status == "WAIT_EX_WAREHOUSE"
              ? "待出库"
              : returnForm.status == "REFUSE_EX_WAREHOUSE"
              ? "拒绝出库"
              : returnForm.status == "PARTIAL_DONE"
              ? "部分完成"
              : returnForm.status == "DONE"
              ? "完成"
              : returnForm.status == "CANCEL"
              ? "已取消"
              : "/"
          }}</template
        >
        <template #location>
          <DataTable
            :columns="returnColumns"
            :show-setting="false"
            :show-pagination="false"
            :show-search="false"
            row-key="id"
            :data="returnForm.applyReturnDetailList"
            sticky
            :height="350"
          >
            <template #saleAttrVals="slotProps">
              <div
                v-for="attr in slotProps.row?.itemStore?.skuInfo?.saleAttrVals"
                :key="attr.val"
              >
                {{ attr.name }}:{{ attr.val }}
              </div>
            </template>
          </DataTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 入库单关联采购单明细 -->
    <ProcureWaybill ref="procureWaybill" />
    <!-- 耗材退货入库 -->
    <ReturnWaybill ref="returnWaybill" />
    <!-- 翻新组件入库 -->
    <RepairWaybill ref="repairWaybill" />
    <!-- 拆机件入库 -->
    <SplitWaybill ref="splitWaybill" />
  </div>
</template>

<script>
import ProcureWaybill from "@/views/store/components/procureWaybill.vue";
import ReturnWaybill from "@/views/store/components/returnWaybill.vue";
import RepairWaybill from "@/views/store/components/repairWaybill.vue";
import SplitWaybill from "@/views/store/components/splitWaybill.vue";
import { getInboundOrderDetailApi } from "@/api/operator";
const { uploadURL } = window.config.api;
import { cloneDeep } from "lodash";
import {
  getInboundByPageApi,
  importInboundApi,
  warehouseListApi,
  downloadInboundTemplateApi,
  deleteInboundApi,
  getInboundDetailApi,
  addInboundApi,
  manufacturerListApi,
  updateInboundApi,
  articlePageApi,
  examineInboundApi,
  articleGetDetailApi,
  updateRemarksApi,
  inOutTypeListApi,
  inStatusListApi,
  fixLocationApi,
} from "@/api/store";
import { dictTreeByCodeApi } from "@/api/user";
import { manufacturerInfoApi, goodsAudit, goodsLook } from "@/api/manufacturer";

export default {
  name: "Warehousing",
  components: { ProcureWaybill, ReturnWaybill, RepairWaybill, SplitWaybill },

  data() {
    const that = this;
    return {
      options1: [],
      dialogVisible: false,
      dialogTitle: "",
      confirmLoading: false,
      methodType: "info",
      form: { parentId: "" },
      defaultFormParams: { parentId: "" },
      tableData: [],
      columns: [
        {
          dataIndex: "inWarehouseId",
          title: "入库单编号",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "shopWaybill",
          title: "关联单号",
          isForm: false,
          clearable: true,
          valueType: "text",
          formSpan: 24,
          atext: true,
          fun: {
            click() {
              if (that.editForm.inType === "工程师退料入库") {
                that.getEngineerInfo(that.editForm.shopWaybill);
              } else if (
                that.editForm.inType === "商城退货入库" ||
                that.editForm.inType === "退回仓库入库"
              ) {
                that.$refs.returnWaybill.show(that.editForm.shopWaybill);
              } else if (that.editForm.inType === "采购入库") {
                that.$refs.procureWaybill.show(that.editForm.shopWaybill);
              } else if (that.editForm.inType === "维修入库") {
                that.$refs.repairWaybill.show(that.editForm.shopWaybill);
              } else if (that.editForm.inType === "拆机入库") {
                that.$refs.splitWaybill.show(that.editForm.shopWaybill);
              } else {
                this.$message.warning("暂不支持该类型入库单关联信息查看");
              }
            },
          },
        },
        {
          dataIndex: "shopWaybill",
          title: "关联单号",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
          width: 180,
        },
        // {
        //   dataIndex: "imageFiles",
        //   title: "物品图片",
        //   isTable: true,
        //   tableSlot: "imageFiles",
        //   clearable: true,
        // },
        // {
        //   dataIndex: "manufacturerChannel",
        //   title: "制造商渠道",
        //   isTable: true,
        // formatter: (row) => row.manufacturerChannel.label,
        //   isSearch: true,
        //   valueType: "select",
        //   clearable: true,
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(2200),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        {
          dataIndex: "warehouseName",
          title: "归属仓库",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "warehouseId",
          title: "归属仓库",
          isForm: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          formSpan: 12,
          optionskey: {
            label: "name",
            value: "id",
          },
          fun: {
            change(val) {
              that.goodTableData = [];
            },
          },
        },
        // {
        //   dataIndex: "inWarehouseNumber",
        //   title: "入库数量",
        //   isTable: true,
        // },
        {
          dataIndex: "inWarehouseNumber",
          title: "应入库量",
          isTable: true,
        },
        {
          dataIndex: "auditInWarehouseNumber",
          title: "已入库量",
          isTable: true,
        },
        {
          dataIndex: "inType",
          tableSlot: "inType",
          title: "入库类型",
          isTable: true,
          isSearch: true,
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 12,
          option: [],
          disabled: true,
          optionMth: () => inOutTypeListApi(),
          optionskey: {
            label: "label",
            value: "value",
          },
          filterOption: {
            key: "type",
            value: 1,
          },
          prop: [
            {
              required: true,
              message: "请选择入库类型",
              trigger: "change",
            },
          ],
          width: 120,
        },
        // {
        //   dataIndex: "inWarehouseId",
        //   title: "入库人员",
        //   isTable: true,
        //   isSearch: true,
        //   clearable: true,
        //   valueType: "select",
        //   clearable: true,
        //   formSpan: 12,
        //   option: [],
        //   disabled:true,
        //   optionMth: () => usersListApi(),
        //   optionskey: {
        //     label: "name",
        //     value: "id",
        //   },
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择入库人员",
        //       trigger: "change",
        //     },
        //   ],

        // },
        {
          dataIndex: "inWarehouseTime",
          title: "入库时间",
          isTable: true,
          isSearch: true,
          isExport: false,
          width: 180,
          valueType: "date-picker",
          // pickerType: "daterange",
          // pickerFormat: "yyyy-MM-dd",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          clearable: true,
        },
        {
          dataIndex: "inStatus",
          tableSlot: "inStatus",
          title: "入库状态",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [],
          optionMth: () => inStatusListApi(),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "mainWaybills",
        //   title: "储位",
        //   isTable: true,
        //   clearable: true,
        //   tableSlot: "mainWaybills",
        // },
        {
          dataIndex: "mainWaybill",
          title: "关联物流主单号",
          isForm: true,
          clearable: true,
          valueType: "input",
          formSlot: "mainWaybill",
          formSpan: 12,
        },

        {
          dataIndex: "secondlyWaybill",
          title: "关联物流子单号",
          isForm: true,
          clearable: true,
          valueType: "input",
          formSlot: "secondlyWaybill",
          formSpan: 12,
        },
        {
          dataIndex: "reverseOrderId",
          title: "关联逆向单号",
          isForm: true,
          valueType: "text",
          formSpan: 12,
        },
        {
          dataIndex: "goodsDataList",
          title: "入库货品信息",
          isForm: true,
          formOtherSlot: "goodsDataList",
        },
        {
          dataIndex: "remarks",
          title: "备注",
          isTable: true,
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          tableSlot: "remarks",
          width: 350,
          attrs: {
            rows: 6,
          },
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 160,
          tableSlot: "action",
        },
      ],
      queryParam: {},
      localPagination: {
        pageSize: 10,
        page: 1,
        total: 0,
      },
      dialogUpload: false,
      warehouse: "",
      warehouseList: [],
      fileList: [],
      actionUrl: uploadURL + importInboundApi,
      token: localStorage.getItem("token"),

      // 修改编辑
      editType: "add",
      showDrawer: false,
      drawerTitle: "",
      editForm: {},
      editFormLoading: false,
      showCheckBtn: false, // 审批显示按钮
      goodTableData: [],
      goodColumns: [
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          minWidth: 140,
        },
        {
          dataIndex: "articleImages",
          title: "物品图片",
          isTable: true,
          width: 110,
          tableSlot: "articleImages",
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          minWidth: 140,
          tableSlot: "code",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "inWarehouseNumber",
          title: "应入库量",
          isTable: true,
          width: 140,
          tableSlot: "inWarehouseNumber",
        },
        {
          dataIndex: "auditInWarehouseNumber",
          title: "已入库量",
          isTable: true,
          width: 90,
        },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          width: 180,
          tableSlot: "location",
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          width: 140,
          tableSlot: "price",
        },
        {
          dataIndex: "sumWarehouseNumber",
          title: "库存数量",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "batchCode",
          title: "批次号",
          width: 150,
          isTable: true,
          tableSlot: "batchCode",
        },
        // {
        //   dataIndex: "createdAt",
        //   title: "入库时间",
        //   width: 180,
        //   isTable: true,
        // },
        // {
        //   dataIndex: "inWarehouseNumber",
        //   title: "入库量",
        //   isTable: true,
        //   tableSlot: "inWarehouseNumber",
        // },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 160,
          fixed: "right",
          tableSlot: "action",
        },
      ],
      addGoodsTableData: [],
      addGoodsColumns: [
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          searchSpan: 8,
          minWidth: 110,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          width: 180,
          valueType: "input",
          searchSpan: 8,
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          width: 180,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel.label,
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "partBrand",
          title: "品牌",
          isTable: true,
        },
        // {
        //   dataIndex: "action",
        //   width: 180,
        //   fixed: "right",
        //   title: "操作",
        //   align: "left",
        //   isTable: true,
        //   tableSlot: "action",
        // },
      ],
      addGoodsQueryParam: {},
      addGoodsLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      showDialog: false,
      dialogLoading: false,
      choosedata: [],
      formcolumns: [
        {
          dataIndex: "name",
          isForm: true,
          title: "物品名称",
          valueType: "input",
          clearable: true,
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入物品名称",
              trigger: "change",
            },
          ],
        },
        {
          clearboth: true,
          dataIndex: "type",
          title: "物品大小类",
          isForm: true,
          formSpan: 6,
          formSlot: "type",

          prop: [
            {
              required: true,
              message: "请选择物品大小类",
              trigger: "change",
            },
          ],
        },

        // {
        //   clearboth: true,
        //   dataIndex: "type",
        //   title: "物品类型",
        //   isForm: true,
        //   valueType: "select",
        //   formSpan: 6,
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(2100),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择物品类型",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "numberOem",
          title: "原厂零件编号（OEM）",
          isForm: true,
          valueType: "select",
          formSpan: 6,
          formSlot: "numberOem",
          // option: [],
          // optionMth: () => partListApi({ pageNumber: 1, pageSize: 10000 }),
          // fun: {
          //   change: ((data) => { this.setOem(data) }),
          // },
          // datakey: 'rows',
          // optionskey: {
          //   label: 'oemNumber',
          //   value: 'oemNumber',
          //   ruturnAll: true,
          // },
          prop: [
            {
              required: true,
              message: "请选择原厂零件编号（OEM）",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "partName",
          isForm: true,
          title: "零件名称",
          valueType: "input",
          disabled: true,
          clearable: true,
          formSpan: 6,
        },

        // {
        //   dataIndex: "supplierCode",
        //   title: "供应商编号",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请输入供应商编号",
        //       trigger: "change",
        //     },
        //   ],
        // },
        // {
        //   dataIndex: "supplierName",
        //   title: "供应商名称",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请输入供应商名称",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 6,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择制造商渠道",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "manufacturerGoodsCode",
          title: "制造商物品编号",
          isForm: true,
          valueType: "input",
          clearable: true,
          disabled: false,
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请输入制造商物品编号",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          isForm: true,
          valueType: "input",
          clearable: true,
          disabled: false,
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请输入制造商物品名称",
              trigger: "change",
            },
          ],
        },

        {
          dataIndex: "manufacturerId",
          title: "制造商名称",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 6,
          option: [],
          optionMth: () => manufacturerListApi(2200),
          fun: {
            change(val) {
              that.$set(that.form, "manufacturerCode", val);
            },
          },
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请输入制造商名称",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "manufacturerCode",
          title: "制造商编号",
          isForm: true,
          valueType: "input",
          disabled: true,
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "partBrand",
          title: "零件品牌",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        // {
        //   dataIndex: "packNumber",
        //   title: "装箱数量（个）",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        // },
        // {
        //   dataIndex: "minOrder",
        //   title: "最小起订量",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        // },
        {
          dataIndex: "minUnit",
          title: "单位（最小单位）",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "colorBox",
          title: "颜色/纸盒",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "expectLife",
          title: "预计寿命",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "suttle",
          title: "净重",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "packSize",
          title: "包装尺寸",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "inCarrier",
          title: "含载体",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "inChip",
          title: "含芯片",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "packWeight",
          title: "包装-重量",
          isForm: true,
          valueType: "input",
          step: 1,
          isnumtop: true,
          clearable: true,
          formSpan: 6,
          unit: "g",
          prop: [
            {
              required: true,
              message: "请输入包装-重量",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "packLong",
          title: "包装-长",
          isForm: true,
          valueType: "input",
          step: 1,
          isnumtop: true,
          clearable: true,
          formSpan: 6,
          unit: "mm",
          prop: [
            {
              required: true,
              message: "请输入包装-长",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "packWide",
          title: "包装-宽",
          isForm: true,
          valueType: "input",
          step: 1,
          isnumtop: true,
          clearable: true,
          formSpan: 6,
          unit: "mm",
          prop: [
            {
              required: true,
              message: "请输入包装-宽",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "packHigh",
          title: "包装-高",
          isForm: true,
          valueType: "input",
          step: 1,
          isnumtop: true,
          clearable: true,
          formSpan: 6,
          unit: "mm",
          prop: [
            {
              required: true,
              message: "请输入包装-高",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "saveWeek",
          title: "保存周期",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "saveTemp",
          title: "保存温度",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "saveHumidity",
          title: "保存湿度",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "isSqueeze",
          title: "能否挤压",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "paperWeight",
          title: "纸张克重",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "paperLong",
          title: "纸张-长",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "paperWide",
          title: "纸张-宽",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "paperSize",
          title: "纸张尺寸规格",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "paperSeries",
          title: "纸张系列",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "totalNumber",
          title: "统计数量",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "avgMoney",
          title: "平均单价",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "sumLife",
          title: "修正寿命",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "avgLife",
          title: "平均寿命",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "imageFiles",
          title: "物品图片",
          isForm: true,
          formSlot: "imageFiles",
        },
      ],
      remarkDialog: false,
      remarkForm: {},
      remarkFormLoading: false,
      remarkColumns: [
        {
          dataIndex: "remarks",
          title: "备注",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          attrs: {
            rows: 6,
          },
        },
      ],
      //-======
      rkform: {},
      rkdialogVisible: false,
      rkformcolumns: [
        {
          dataIndex: "batchCode",
          title: "批次号",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 24,
          disabled: true,
          prop: [
            {
              required: true,
              message: "请输入批次号",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "price",
          title: "入库单价",
          isForm: true,
          valueType: "input-number",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入入库单价",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "auditInWarehouseNumber1",
          title: "入库数量",
          isForm: true,
          valueType: "input-number",
          isnumtop: true,
          min: 1,
          step: 1,
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入入库数量",
              trigger: "change",
            },
          ],
        },
      ],
      rkdialogLook: false,
      rkLook: null,
      returnDialogVisible: false,
      returnForm: {},
      returnFormColumns: [
        {
          isForm: true,
          dataIndex: "code",
          title: "退料单编号",
          valueType: "text",
          formSpan: 8,
        },

        {
          dataIndex: "engineerName",
          title: "工程师姓名",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "engineerMobile",
          title: "工程师手机号",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "createdAt",
          title: "发起时间",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "status",
          title: "退料状态",
          isForm: true,
          valueType: "text",
          formSlot: "status",
          formSpan: 8,
        },

        {
          dataIndex: "location",
          title: "退料明细",
          formSlot: "location",
          isForm: true,
          formSpan: 24,
        },
      ],
      returnColumns: [
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          formatter: (row) => row.itemStore.itemName,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku规格",
          isTable: true,
          tableSlot: "saleAttrVals",
          // formatter: (row) => row.itemStore.itemName,
        },
        {
          dataIndex: "num",
          title: "领料数量",
          isTable: true,
          formatter: (row) => row.itemStore.num,
        },
      ],
    };
  },
  watch: {
    "form.manufacturerId": {
      handler: function (val) {
        if (val) {
          manufacturerInfoApi(val).then((res) => {
            this.$set(this.form, "manufacturerCode", res.data.code);
          });
        }
      },
      immediate: true,
      deep: true,
    },
  },

  mounted() {
    this.refresh();
    this.getWarehouseList();
    dictTreeByCodeApi(2100).then((res) => {
      this.options1 = res.data;
    });
  },
  methods: {
    async loadData(params) {
      if (params.inWarehouseTime) {
        params.minInTime = params.inWarehouseTime[0];
        params.maxInTime = params.inWarehouseTime[1];
        delete params["inWarehouseTime"];
      }
      try {
        const result = await getInboundByPageApi(params);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        this.$message.error(error.message);
      } finally {
        this.$refs.ProTable.listLoading = false;
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    // 编辑修改
    async handleEdit(row, type) {
      this.drawerTitle =
        type === "add"
          ? "新增入库"
          : type === "info"
          ? "查看入库单"
          : "编辑入库";
      if (type === "check" || type === "info") {
        this.editType = "info";
        this.drawerTitle = "审核入库单";
        if (type === "check") {
          this.showCheckBtn = true;
        } else {
          this.showCheckBtn = false;
        }

        this.columns.find(
          (item) => item.dataIndex === "shopWaybill"
        ).isForm = true;
        this.columns.find(
          (item) => item.dataIndex === "reverseOrderId"
        ).isForm = true;

        // this.columns.find(
        //   (item) => item.dataIndex === "secondlyWaybill"
        // ).isForm = false;
      } else {
        this.editType = "add";
        this.showCheckBtn = false;
        this.columns.find(
          (item) => item.dataIndex === "shopWaybill"
        ).isForm = false;
        this.columns.find(
          (item) => item.dataIndex === "reverseOrderId"
        ).isForm = false;
        // this.columns.find(
        //   (item) => item.dataIndex === "mainWaybill"
        // ).isForm = true;
        // this.columns.find(
        //   (item) => item.dataIndex === "secondlyWaybill"
        // ).isForm = true;
      }
      if (type === "add") {
        this.editForm = {};
        this.editForm.inType = "originally";
        this.goodTableData = [];
        this.goodColumns.forEach((item) => {
          if (
            item.dataIndex === "batchCode" ||
            item.dataIndex === "action" ||
            item.dataIndex === "sumWarehouseNumber"
          ) {
            item.isTable = false;
          }
        });
      } else {
        this.goodColumns.forEach((item) => {
          if (
            item.dataIndex === "batchCode" ||
            item.dataIndex === "action" ||
            item.dataIndex === "sumWarehouseNumber"
          ) {
            item.isTable = true;
          }
        });
        this.editForm = await this.getDetails(row.id);
        console.log(this.editForm);
        this.editForm.inType = row.inType.label;
        this.editForm.inTypeValue = row.inType.value;

        this.goodTableData = this.editForm.inWarehouseGoodsList;
      }
      this.showDrawer = true;
    },
    /**
     * 获取订单详情
     * @param code
     */
    getEngineerInfo(code) {
      if (!code) return;
      this.returnForm = {};
      getInboundOrderDetailApi({
        keyword: code,
        pageNumber: 1,
        pageSize: 999,
      }).then((res) => {
        this.returnForm = res.data.rows[0];
        this.returnDialogVisible = true;
      });
    },

    async getDetails(id) {
      try {
        const result = await getInboundDetailApi(id);
        if (result.code === 200 && result.data) {
          result.data.inWarehouseGoodsList.forEach((item) => {
            item.check = true;
          });
          return result.data;
        }
      } catch (error) {
        this.$message.error(error.message);
      }
    },
    handleDelete(row) {
      this.$confirm("删除出库单。确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const result = await deleteInboundApi(row.id);
          if (result.code === 200) {
            this.$message.success("删除成功");
            this.$nextTick(() => {
              this.refresh();
            });
          }
        })
        .catch(() => {
          console.log("取消删除");
        });
    },
    handleDrawerOk() {
      this.$refs.editForm.handleSubmit();
    },
    handleCloseDrawer() {
      this.showDrawer = false;
    },
    handleChange1(item) {
      this.$set(this.form, "type", item[item.length - 1]);
    },
    async handleCheckPass(inStatus) {
      try {
        const { id, remarks } = this.editForm;
        this.goodTableData.forEach((item) => {
          if (!item.inWarehouseNumber) {
            throw new Error("请输入应入库量");
          }
        });
        const args = {
          ...this.editForm,
          inWarehouseGoodsList: this.goodTableData,
          inStatus,
        };
        if (args.inWarehouseGoodsList) {
          delete args["inWarehouseGoodsList"];
        }
        const result = await examineInboundApi(args);
        if (result.code === 200) {
          this.showDrawer = false;
          this.$message.success("操作成功");
          this.$nextTick(() => {
            this.refresh();
          });
        }
      } catch (error) {
        this.$message.error(error.message);
      }
    },
    async editFormSubmit(val) {
      this.$confirm("是否确认新增入库?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          this.editFormLoading = true;
          const editApi =
            this.editType === "add" ? addInboundApi : updateInboundApi;
          let inWarehouseNumber = 0;
          let noWarehouseNumber = false;
          const nobatchCode = false;
          this.goodTableData.map((item) => {
            if (
              !item.inWarehouseNumber ||
              item.inWarehouseNumber === "" ||
              item.inWarehouseNumber === 0
            ) {
              noWarehouseNumber = true;
            }
            // if (
            //   !item.batchCode ||
            //   item.batchCode === "" ||
            //   item.batchCode === 0
            // ) {
            //   nobatchCode = true;
            // }
          });
          if (noWarehouseNumber) {
            this.$message.error("请输入应入库量");
            return;
          }
          // if (nobatchCode) {
          //   this.$message.error("请输入批次号");
          //   return;
          // }
          const inWarehouseGoodsList = this.goodTableData.map((item) => {
            delete item.id;
            inWarehouseNumber += item.inWarehouseNumber;
            return item;
          });
          val["inWarehouseGoodsList"] = inWarehouseGoodsList;
          val["inWarehouseNumber"] = inWarehouseNumber;
          const result = await editApi(val);
          if (result.code === 200) {
            this.showDrawer = false;
            this.$message.success("保存成功");
            this.$nextTick(() => {
              this.refresh();
            });
          }
        } finally {
          this.editFormLoading = false;
        }
      });
    },
    handleDeleteGoods(row) {
      this.goodTableData.splice(
        this.goodTableData.findIndex((item) => item.id === row.id),
        1
      );
    },
    //初始化表单
    // resetFrom() {
    //   this.form = cloneDeep(this.defaultFormParams);
    // },
    // 查看详情
    lookDetailsFn(row) {
      this.methodType = "info";
      this.form = {};
      this.dialogTitle = "查看 - " + row.name;
      this.dialogVisible = true;
      // this.resetFrom();
      articleGetDetailApi(row.storageArticleId).then((res) => {
        console.log(res);

        this.form = { ...this.form, ...res.data };
        this.form.type = this.form.type.value;
        this.form.manufacturerChannel = this.form.manufacturerChannel.value;
      });
    },
    closedialog() {
      this.dialogVisible = false;
    },
    async handleOpenGoodDialog() {
      if (!this.editForm.warehouseId) {
        this.$message.error("请先选择归属仓库");
        return;
      }
      this.showDialog = true;
      this.addGoodsQueryParam = {};
      this.addGoodsLocalPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.$nextTick(() => {
        this.refreshAddGoodsTable();
        this.$refs.AddGoodsTable.$refs.ProElTable.clearSelection();
        this.choosedata = cloneDeep(this.goodTableData);
        this.choosedata.map((row) => {
          this.$refs.AddGoodsTable.$refs.ProElTable.toggleRowSelection(row);
        });
      });
    },
    handleDialogConfirm() {
      this.goodTableData = [];
      this.choosedata.map((ele) => {
        this.goodTableData.push({
          ...ele,
          batchCode: "",
          inWarehouseNumber: null,
        });
      });
      this.handleDialogCancel();
      this.dialogLoading = false;
    },
    handleDialogCancel() {
      this.showDialog = false;
    },
    async loadGoodsData(params) {
      try {
        const result = await articlePageApi({
          ...params,
          warehouseId: this.editForm.warehouseId,
        });
        if (result.code === 200 && result.data) {
          this.addGoodsTableData = result.data.rows;
          this.addGoodsLocalPagination = {
            pageNumber: params.pageNumber,
            pageSize: params.pageSize,
            total: +result.data.total,
          };
        }
      } catch (error) {
        this.$message.error(error.message);
      } finally {
        this.$refs.AddGoodsTable &&
          (this.$refs.AddGoodsTable.listLoading = false);
      }
    },
    refreshAddGoodsTable() {
      this.$refs.AddGoodsTable.refresh();
    },
    async handleSelectionChange(row) {
      this.choosedata = row;
    },
    async handleChooseGoods(row) {
      try {
        this.dialogLoading = true;
        const { id } = row;
        if (!id) throw new Error({ message: "请选择商品" });
        const target = this.goodTableData.find((good) => good.id === row.id);
        if (target) throw new Error("该商品已存在");
        this.goodTableData.push({
          ...row,
          batchCode: "",
          inWarehouseNumber: null,
        });
        this.handleDialogCancel();
      } catch (error) {
        this.$message.error(error.message);
      } finally {
        this.dialogLoading = false;
      }
    },

    // 导入上传部分
    handleUpload() {
      this.warehouse = "";
      this.fileList = [];
      this.dialogUpload = true;
    },
    UploadOk() {
      this.$refs.upload.submit();
    },
    handleSuccess(res) {
      if (res.code === 200) {
        this.$message.success("导入成功");
        this.dialogUpload = false;
        this.$refs.ProTable.refresh();
      } else {
        this.$message.error(res.message);
      }
    },
    handleChange(files, fileList) {
      if (files.status === "ready") {
        if (!/\.(xlsx|xls|XLSX|XLS)$/.test(files.raw.name)) {
          this.$message.error("仅支持上传“xls”或“xlsx”格式文件。");
          fileList.splice(-1, 1);
          return;
        }
        if (files.raw.size / 1024 / 1024 > 2) {
          fileList.splice(-1, 1);
          return;
        }
      }
      this.fileList = fileList.slice(-1);
      // const newFileList = fileList.slice(-1);
      // this.fileList = newFileList;
    },
    async handleDownloadTemplate() {
      window.open(downloadInboundTemplateApi);
    },
    async getWarehouseList() {
      try {
        const result = await warehouseListApi({ status: 1401 });
        if (result.code === 200 && result.data) {
          this.warehouseList = result.data;
        }
      } catch (error) {
        this.$message.error(error.message);
      }
    },
    handleCloseDialog() {
      this.remarkForm = {};
      this.remarkDialog = false;
    },
    async updateRemark() {
      try {
        this.remarkFormLoading = true;
        const result = await updateRemarksApi(this.remarkForm);
        if (result.code === 200) {
          this.$message.success("修改成功");
          this.remarkDialog = false;
          this.$refs.ProTable.refresh();
        }
      } catch (error) {
        this.$message.error(error.message);
      } finally {
        this.remarkFormLoading = false;
      }
    },
    handleUpdateRemark(row, type) {
      const { id, remarks } = row;
      this.remarkForm = { id, remarks };
      this.remarkDialog = true;
      this.editType = type;
    },
    getPicsUrlImg(row) {
      return row?.article.imageFiles?.[0]?.url;
    },
    searchBigImage(row) {},
    saveFn(row) {
      fixLocationApi({ id: row.id, location: row.location }).then((res) => {
        this.$message.success(res.message);
        row.check = true;
      });
    },
    // ======
    handleRkCheck(row) {
      this.rkform = cloneDeep(row);
      if (this.editForm.inTypeValue === "overflow") {
        this.rkformcolumns.forEach((item) => {
          if (item.dataIndex === "price") {
            item.isForm = true;
          }
        });
      } else {
        this.rkformcolumns.forEach((item) => {
          if (item.dataIndex === "price") {
            item.isForm = false;
          }
        });
      }
      this.rkdialogVisible = true;
    },
    handleRkLook(row) {
      goodsLook(row.id).then((res) => {
        this.rkdialogLook = true;
        this.rkLook = res.data;
      });
    },
    //触发表单提交
    rkOk() {
      this.$refs["rkproform"].handleSubmit();
    },
    rkOkSub() {
      console.log(this.rkform);
      const obj = cloneDeep(this.rkform);
      obj.auditInWarehouseNumber = parseInt(obj.auditInWarehouseNumber1);
      goodsAudit(obj).then((res) => {
        this.$message.success("审核成功");

        this.handleEdit(this.editForm, "check");
        setTimeout(() => {
          this.rkdialogVisible = false;
        }, 1000);
        this.$nextTick(() => {
          this.refresh();
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.warehousing {
}

.code_id {
  color: #457dec;
  cursor: pointer;
}

.exceed {
  display: inline-block;
  width: 92%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

//::v-deep .fixed-width {
//  display: flex;
//  align-items: center;
//  flex-wrap: wrap;
//
//  .el-button {
//    margin-right: 12px;
//    margin-left: 0 !important;
//    margin-top: 10px;
//  }
//}

.el-inputs {
  width: 100px;
}

.locationIn {
  display: flex;
  align-items: center;
}
</style>
