# 📋 日志控制系统接口使用详细说明

## 🎯 概述

本文档详细描述日志控制系统中每个页面使用的API接口，包括页面初始化、刷新按钮和其他功能触发的接口。

---

## 1. 仪表板页面 (dashboard.vue)

### 📡 使用的API文件
- `@/api/analysisApi.js`

### 🔄 页面初始化接口
```javascript
// mounted() -> initData() -> loadDashboardStats()
GET /logcontrol/analysis/comprehensive-stats
```

### 🔄 刷新按钮触发的接口
```javascript
// refreshData() 方法触发
Promise.all([
  GET /logcontrol/analysis/comprehensive-stats,  // 统计卡片数据
  this.$refs.logCharts.loadAllStats()           // 图表组件数据
])
```

### 📊 图表组件接口 (LogCharts.vue)
```javascript
// 日志趋势图表
GET /logcontrol/analysis/log-stats

// 设备统计图表  
GET /logcontrol/analysis/device-stats

// 崩溃统计图表
GET /logcontrol/analysis/crash-stats
```

### ⏰ 定时刷新
- 每60秒自动调用 `silentRefreshData()` 静默刷新所有数据

---

## 2. 配置管理页面 (configManagement.vue)

### 📡 使用的API文件
- `@/api/configApi.js`

### 🔄 页面初始化接口
```javascript
// mounted() -> initData()
Promise.all([
  GET /logcontrol/config/list,        // 配置列表
  GET /logcontrol/config/templates,   // 配置模板
  GET /logcontrol/config/assignments  // 分配情况
])
```

### 🔄 刷新按钮触发的接口
```javascript
// refreshData() -> initData() 
// 重新调用所有初始化接口
Promise.all([
  GET /logcontrol/config/list,
  GET /logcontrol/config/templates, 
  GET /logcontrol/config/assignments
])
```

### 🛠️ 其他功能接口
```javascript
// 批量分配配置
POST /logcontrol/config/batch-assign

// 预览配置
GET /logcontrol/config/preview

// 移除分配
DELETE /logcontrol/config/assignment/{targetType}/{targetId}

// 创建配置
POST /logcontrol/config/create

// 更新配置
PUT /logcontrol/config/update

// 激活配置
PUT /logcontrol/config/activate/{id}

// 删除配置
DELETE /logcontrol/config/{id}

// 获取配置详情
GET /logcontrol/config/detail/{id}

// 获取模板详情
GET /logcontrol/config/template/{templateName}

// 从模板创建配置
POST /logcontrol/config/create-from-template
```

---

## 3. 设备管理页面 (deviceManagement.vue)

### 📡 使用的API文件
- `@/api/deviceApi.js`

### 🔄 页面初始化接口
```javascript
// mounted() -> initData() -> loadDevices()
GET /logcontrol/device/list
```

### 🔄 刷新按钮触发的接口
```javascript
// refreshData() -> initData() -> loadDevices()
GET /logcontrol/device/list
```

### 🛠️ 其他功能接口
```javascript
// 删除设备
DELETE /logcontrol/device/{id}

// 获取设备详情
GET /logcontrol/device/get/{id}

// 获取设备信息（按deviceId）
GET /logcontrol/device/get?deviceId={deviceId}

// 获取设备总数
GET /logcontrol/device/count

// 统计接口
GET /logcontrol/device/stats/brand      // 品牌统计
GET /logcontrol/device/stats/model      // 型号统计
GET /logcontrol/device/stats/os-version // 系统版本统计
GET /logcontrol/device/stats/rooted     // Root状态统计
```

---

## 4. 日志查看页面 (logAnalysis.vue)

### 📡 使用的API文件
- `@/api/logApi.js`
- `@/api/analysisApi.js`

### 🔄 页面初始化接口
```javascript
// mounted() -> initData()
Promise.all([
  GET /logcontrol/log/page,                    // 分页日志列表
  GET /logcontrol/analysis/comprehensive-stats, // 统计数据
  // 基于日志数据生成筛选选项（无额外接口）
])
```

### 🔄 刷新按钮触发的接口
```javascript
// refreshData() -> initData()
// 重新调用所有初始化接口
Promise.all([
  GET /logcontrol/log/page,
  GET /logcontrol/analysis/comprehensive-stats
])
```

### 🛠️ 其他功能接口
```javascript
// 获取日志详情
GET /logcontrol/log/detail/{id}

// 导出日志
GET /logcontrol/log/export?params

// 分页查询日志（带筛选）
GET /logcontrol/log/page?pageNum=1&pageSize=20&deviceId=xxx&logLevel=xxx

// 获取所有日志（不分页）
GET /logcontrol/log/all

// 删除日志
DELETE /logcontrol/log/{id}
```

### 📊 筛选参数支持
```javascript
{
  pageNum: 1,           // 页码
  pageSize: 20,         // 每页数量
  deviceId: "DEV001",   // 设备ID筛选
  logType: "SYSTEM",    // 日志类型筛选
  logLevel: "ERROR",    // 日志级别筛选
  userId: "USER001",    // 用户ID筛选
  startTime: "2025-01-01 00:00:00", // 开始时间
  endTime: "2025-01-31 23:59:59"    // 结束时间
}
```

---

## 5. 崩溃分析页面 (crashAnalysis.vue)

### 📡 使用的API文件
- `@/api/analysisApi.js`

### 🔄 页面初始化接口
```javascript
// mounted() -> initData()
Promise.all([
  GET /logcontrol/analysis/crash-list,  // 崩溃列表
  GET /logcontrol/analysis/crash-stats  // 崩溃统计
])
```

### 🔄 刷新按钮触发的接口
```javascript
// refreshData() -> initData()
// 重新调用所有初始化接口
Promise.all([
  GET /logcontrol/analysis/crash-list,
  GET /logcontrol/analysis/crash-stats
])
```

### 🛠️ 其他功能接口
```javascript
// 获取崩溃详情
GET /logcontrol/analysis/crash-detail/{id}

// 分页查询崩溃（带筛选）
GET /logcontrol/analysis/crash-list?pageNumber=1&pageSize=20&deviceId=xxx
```

### 📊 筛选参数支持
```javascript
{
  pageNumber: 1,        // 页码
  pageSize: 20,         // 每页数量
  deviceId: "DEV001",   // 设备ID筛选
  exceptionType: "NullPointerException", // 异常类型筛选
  appVersion: "1.0.0",  // 应用版本筛选
  dateRange: ["2025-01-01", "2025-01-31"] // 时间范围
}
```

---

## 🔧 API降级处理

所有接口都实现了降级处理机制：

### ✅ 真实接口可用时
- 调用后端真实接口
- 返回真实数据

### ⚠️ 真实接口不可用时  
- 自动切换到模拟数据
- 在控制台输出警告信息
- 保证页面正常显示和功能可用

### 📝 降级日志示例
```javascript
console.warn('使用模拟综合统计数据:', error.message)
console.warn('使用模拟分页日志数据:', error.message)
console.warn('使用模拟配置列表数据:', error.message)
```

---

## 🎯 总结

### 刷新按钮覆盖的接口数量：
- **仪表板**: 4个接口（1个统计 + 3个图表）
- **配置管理**: 3个接口（配置列表 + 模板 + 分配情况）
- **设备管理**: 1个接口（设备列表）
- **日志查看**: 2个接口（日志列表 + 统计数据）
- **崩溃分析**: 2个接口（崩溃列表 + 统计数据）

### 接口特点：
- **RESTful设计** - 遵循REST API规范
- **统一前缀** - 所有接口都以 `/logcontrol/` 开头
- **分页支持** - 列表接口都支持分页查询
- **筛选支持** - 支持多维度数据筛选
- **降级处理** - 确保系统稳定性和可用性

---

## 📊 接口响应格式

### 标准响应格式
```javascript
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据内容
  }
}
```

### 分页响应格式
```javascript
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [...],      // 数据列表
    "total": "100",     // 总记录数（字符串）
    "pageNum": "1",     // 当前页码（字符串）
    "pageSize": "20",   // 每页大小（字符串）
    "pages": "5"        // 总页数（字符串）
  }
}
```

### 统计数据响应格式
```javascript
{
  "code": 200,
  "message": "success",
  "data": {
    "totalDevices": "2",        // 设备总数（字符串）
    "totalLogs": "1040",        // 日志总数（字符串）
    "totalCrashes": "160",      // 崩溃总数（字符串）
    "unuploadedLogs": "1040",   // 未上传日志数（字符串）
    "brandDistribution": [...], // 品牌分布数据
    "modelDistribution": [...]  // 型号分布数据
  }
}
```

---

## 🔄 刷新按钮执行流程

### 通用刷新流程
```javascript
async refreshData() {
  try {
    // 1. 显示加载状态
    this.loading = true

    // 2. 调用初始化方法重新获取数据
    await this.initData()

    // 3. 显示成功提示
    this.$message.success('数据刷新成功')
  } catch (error) {
    // 4. 错误处理和提示
    console.error('刷新数据失败:', error)
    this.$message.error('数据刷新失败')
  } finally {
    // 5. 隐藏加载状态
    this.loading = false
  }
}
```

### 各页面具体执行的接口

#### 仪表板刷新执行：
1. `analysisApi.getComprehensiveStats()` - 更新统计卡片
2. `logCharts.loadAllStats()` - 更新所有图表数据
   - 日志趋势图表数据
   - 设备统计图表数据
   - 崩溃统计图表数据

#### 配置管理刷新执行：
1. `configApi.getConfigList()` - 更新配置列表
2. `configApi.getTemplates()` - 更新配置模板
3. `configApi.getAssignments()` - 更新分配情况

#### 设备管理刷新执行：
1. `deviceApi.getDeviceList()` - 更新设备列表和统计

#### 日志查看刷新执行：
1. `logApi.getLogListWithPagination()` - 更新日志列表
2. `analysisApi.getComprehensiveStats()` - 更新统计数据
3. 基于新数据重新生成筛选选项

#### 崩溃分析刷新执行：
1. `analysisApi.getCrashList()` - 更新崩溃列表
2. `analysisApi.getCrashStats()` - 更新崩溃统计数据

---

## 🎯 接口调用优化

### 并发调用
使用 `Promise.all()` 并发调用多个接口，提高加载效率：
```javascript
await Promise.all([
  this.loadConfigs(),
  this.loadTemplates(),
  this.loadAssignments()
])
```

### 错误隔离
每个接口调用都有独立的错误处理，避免单个接口失败影响整体功能。

### 数据缓存
部分页面会基于已有数据生成衍生信息，减少不必要的API调用。
