<template>
  <div class="visual-content-editor">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <div class="nav-content">
        <div class="nav-left">
          <h1 class="nav-title">内容编辑</h1>
        </div>
        <div class="nav-right">
          <el-button
            type="default"
            icon="el-icon-refresh"
            @click="handleRefresh"
          >
            刷新页面
          </el-button>
        </div>
      </div>

      <!-- 页面类型选项卡 - 使用API数据 -->
      <el-tabs v-model="activeTab" type="card" @tab-click="handleTabChange" class="page-tabs" v-loading="menuLoading">
        <el-tab-pane
          v-for="menuItem in menuItems"
          :key="menuItem.key"
          :name="menuItem.key"
        >
          <span slot="label">
            <i :class="menuItem.icon"></i>
            {{ menuItem.label }}
          </span>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" v-loading="loading">
      <!-- 页面头部信息 -->
      <div class="content-header">
        <div class="header-left">
          <h1 class="page-title">
            {{ getPageTitle() }} - 可视化编辑
          </h1>
          <p class="page-description">直接在页面上点击内容进行编辑，支持增删改所有内容，所见即所得</p>
        </div>
        <div class="header-right">
          <el-button type="default" icon="el-icon-view" @click="handlePreview">
            预览页面
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-check"
            @click="saveContent"
            :loading="saving"
            :disabled="!hasChanges"
          >
            {{ hasChanges ? '保存修改' : '无修改' }}
          </el-button>
        </div>
      </div>

      <!-- 修改提示 -->
      <div v-if="hasChanges" class="change-alert">
        <el-alert
          title="您有未保存的修改"
          description="请记得点击保存修改按钮保存您的更改"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 页面编辑器内容 -->
      <div class="editor-content" v-if="currentContent">
        <!-- 关于我们页面编辑器 -->
        <AboutPageEditor
          v-if="activeTab === 'ABOUT_PAGE'"
          :content="localContent"
          @update="updateContent"
          @change="markAsChanged"
        />

        <!-- 服务介绍页面编辑器 -->
        <ServicesPageEditor
          v-else-if="activeTab === 'SERVICE_PAGE'"
          :content="localContent"
          @update="updateContent"
          @change="markAsChanged"
        />

        <!-- 案例展示页面编辑器 -->
        <CasesPageEditor
          v-else-if="activeTab === 'CASE_PAGE'"
          :content="localContent"
          @update="updateContent"
          @change="markAsChanged"
        />

        <!-- 联系我们页面编辑器 -->
        <ContactPageEditor
          v-else-if="activeTab === 'CONTACT_PAGE'"
          :content="localContent"
          @update="updateContent"
          @change="markAsChanged"
        />
      </div>
    </div>

    
    <!-- 预览对话框 -->
    <el-dialog
      title="页面预览"
      :visible.sync="previewVisible"
      width="90%"
      :close-on-click-modal="false"
      class="preview-dialog"
    >
      <div class="preview-content">
        <iframe 
          v-if="previewUrl"
          :src="previewUrl"
          frameborder="0"
          style="width: 100%; height: 70vh;"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { websiteContentApi, websitePublicApi, websiteImageApi, websiteConfigApi } from '@/websites/api/website'
import AboutPageEditor from './editors/AboutPageEditor.vue'
import ServicesPageEditor from './editors/ServicesPageEditor.vue'
import CasesPageEditor from './editors/CasesPageEditor.vue'
import ContactPageEditor from './editors/ContactPageEditor.vue'

export default {
  name: 'VisualContentEditor',
  components: {
    AboutPageEditor,
    ServicesPageEditor,
    CasesPageEditor,
    ContactPageEditor
  },
  
  data() {
    return {
      loading: false,
      saving: false,
      hasChanges: false,
      previewVisible: false,
      previewUrl: '',
      isInitializing: false, // 添加初始化标志

      // 当前页面类型
      activeTab: 'ABOUT_PAGE',

      // 页面内容数据
      currentContent: null,
      localContent: {
        title: '',
        description: '',
        seoTitle: '',
        seoDescription: '',
        seoKeywords: '',
        config: {}
      },

      // 菜单数据
      menuItems: [],
      menuLoading: false,

      // 网站配置数据
      websiteConfig: {},

      // 默认页面标题映射（作为备用）
      defaultPageTitles: {
        'ABOUT_PAGE': '关于我们',
        'SERVICE_PAGE': '服务介绍',
        'CASE_PAGE': '案例展示',
        'CONTACT_PAGE': '联系我们'
      }
    }
  },
  
  computed: {
    currentPageTitle() {
      // 优先使用API菜单数据，如果没有则使用默认标题
      const menuItem = this.menuItems.find(item => item.key === this.activeTab)
      return menuItem ? menuItem.label : (this.defaultPageTitles[this.activeTab] || '页面')
    },

    // 获取页面标题映射（用于兼容现有代码）
    pageTitles() {
      const titles = {}
      this.menuItems.forEach(item => {
        titles[item.key] = item.label
      })
      // 合并默认标题作为备用
      return { ...this.defaultPageTitles, ...titles }
    }
  },
  
  mounted() {
    this.loadMenus()
    this.loadContent()
    this.loadWebsiteConfig()
  },
  
  methods: {
    // 加载菜单数据
    async loadMenus() {
      this.menuLoading = true
      try {
        const response = await websitePublicApi.getMenus()

        // 处理API响应数据结构
        let menuData = []
        if (response && response.data) {
          menuData = response.data
        } else if (Array.isArray(response)) {
          menuData = response
        }

        // 转换菜单数据格式，确保包含必要的字段
        this.menuItems = menuData.map(item => ({
          key: item.key || item.id,
          label: item.label || item.name,
          path: item.path || '',
          icon: this.getMenuIcon(item.key || item.id)
        }))
      } catch (error) {
        // 如果API失败，使用默认菜单数据
        this.menuItems = [
          { key: 'ABOUT_PAGE', label: '关于我们', path: '/', icon: 'el-icon-user' },
          { key: 'SERVICE_PAGE', label: '服务介绍', path: '/services', icon: 'el-icon-service' },
          { key: 'CASE_PAGE', label: '案例展示', path: '/cases', icon: 'el-icon-picture' },
          { key: 'CONTACT_PAGE', label: '联系我们', path: '/contact', icon: 'el-icon-phone' }
        ]
      } finally {
        this.menuLoading = false
      }
    },

    // 获取菜单图标
    getMenuIcon(key) {
      const iconMap = {
        'ABOUT_PAGE': 'el-icon-user',
        'SERVICE_PAGE': 'el-icon-service',
        'CASE_PAGE': 'el-icon-picture',
        'CONTACT_PAGE': 'el-icon-phone'
      }
      return iconMap[key] || 'el-icon-document'
    },

    // 加载内容
    async loadContent() {
      this.loading = true
      this.isInitializing = true // 设置初始化标志
      try {
        const response = await websiteContentApi.getContentByType(this.activeTab)

        // 处理 API 响应数据结构，兼容不同的响应格式
        let contentData = null
        if (response && response.data) {
          // 如果响应有 data 字段，使用 data 字段的内容
          contentData = response.data
        } else if (response) {
          // 如果响应本身就是数据，直接使用
          contentData = response
        }

        this.currentContent = contentData

        if (this.currentContent) {
          // 解析 content 字段中的 JSON 数据（如果存在）
          let parsedConfig = {}
          let parsedCases = []
          if (this.currentContent.content) {
            try {
              const parsed = JSON.parse(this.currentContent.content)
              parsedConfig = parsed.config || {}
              parsedCases = parsed.cases || []
            } catch (error) {
              // 解析失败时静默处理
            }
          }

          this.localContent = {
            title: this.currentContent.title || '',
            description: this.currentContent.description || this.currentContent.summary || '',
            seoTitle: this.currentContent.seoTitle || (this.currentContent.seoMeta && this.currentContent.seoMeta.title) || '',
            seoDescription: this.currentContent.seoDescription || (this.currentContent.seoMeta && this.currentContent.seoMeta.description) || '',
            seoKeywords: this.currentContent.seoKeywords || (this.currentContent.seoMeta && this.currentContent.seoMeta.keywords) || '',
            config: parsedConfig,
            cases: parsedCases  // 添加cases数据
          }


        } else {
          // 创建默认内容
          await this.createDefaultContent()
        }

        this.hasChanges = false

      } catch (error) {
        this.$message.error('加载内容失败，请稍后重试')
      } finally {
        this.loading = false
        this.isInitializing = false // 重置初始化标志
      }
    },

    // 创建默认内容
    async createDefaultContent() {
      try {
        const defaultLocalContent = {
          title: this.pageTitles[this.activeTab],
          description: `${this.pageTitles[this.activeTab]}页面描述`,
          seoTitle: this.pageTitles[this.activeTab],
          seoDescription: `${this.pageTitles[this.activeTab]}页面SEO描述`,
          seoKeywords: '',
          config: {}
        }

        const defaultContent = {
          type: this.activeTab,
          title: defaultLocalContent.title,
          summary: defaultLocalContent.description,
          content: JSON.stringify({
            title: defaultLocalContent.title,
            summary: defaultLocalContent.description,
            config: defaultLocalContent.config
          }),
          status: 'PUBLISHED',
          seoTitle: defaultLocalContent.seoTitle,
          seoDescription: defaultLocalContent.seoDescription,
          seoKeywords: defaultLocalContent.seoKeywords
        }

        const response = await websiteContentApi.createContent(defaultContent)
        this.currentContent = response.data
        this.localContent = { ...defaultLocalContent }

      } catch (error) {
        this.$message.error('创建默认内容失败')
      }
    },

    // 切换标签页
    async handleTabChange(tab) {
      if (this.hasChanges) {
        try {
          await this.$confirm('当前页面有未保存的修改，是否保存？', '确认', {
            confirmButtonText: '保存',
            cancelButtonText: '不保存',
            type: 'warning'
          })
          await this.saveContent()
        } catch (error) {
          if (error === 'cancel') {
            // 用户选择不保存，重置更改状态
            this.hasChanges = false
          } else {
            // 保存失败，阻止切换
            return
          }
        }
      }

      this.activeTab = tab.name
      await this.loadContent()
    },

    // 更新内容
    updateContent(newContent) {
      this.localContent = { ...this.localContent, ...newContent }
      // 只有在非初始化状态下才标记为已更改
      if (!this.isInitializing) {
        this.markAsChanged()
      }
    },

    // 标记为已更改
    markAsChanged() {
      this.hasChanges = true
    },

    // 保存内容
    async saveContent() {
      this.saving = true
      try {
        // 准备保存数据，将配置序列化为 JSON 字符串
        const saveData = {
          title: this.localContent.title || '',
          summary: this.localContent.description || '',
          content: JSON.stringify({
            title: this.localContent.title || '',
            summary: this.localContent.description || '',
            config: this.localContent.config || {},
            cases: this.localContent.cases || []  // 添加cases数据到保存内容中
          }),
          type: this.activeTab,
          status: 'PUBLISHED',
          seoTitle: this.localContent.seoTitle || '',
          seoDescription: this.localContent.seoDescription || '',
          seoKeywords: this.localContent.seoKeywords || ''
        }

        let savedContent
        if (this.currentContent && this.currentContent.id) {
          // 更新现有内容
          saveData.id = this.currentContent.id
          const response = await websiteContentApi.updateContent(saveData)
          savedContent = response.data || { ...this.currentContent, ...saveData }
        } else {
          // 创建新内容
          const response = await websiteContentApi.createContent(saveData)
          savedContent = response.data
        }

        // 保存成功后，记录内容中使用的图片到数据库
        try {
          await this.recordContentImages()
        } catch (error) {
          // 不影响保存成功的提示
        }

        // 更新currentContent为最新保存的内容，作为下次比较的基准
        this.currentContent = savedContent

        this.hasChanges = false
        this.$message.success('保存成功')

      } catch (error) {
        this.$message.error('保存失败，请稍后重试')
      } finally {
        this.saving = false
      }
    },

    // 预览页面
    previewPage() {
      // 获取配置的域名
      const websiteDomain = this.websiteConfig?.websiteDomain?.trim()

      if (websiteDomain) {
        // 使用配置的域名
        const baseUrl = websiteDomain.endsWith('/') ? websiteDomain.slice(0, -1) : websiteDomain
        const pagePath = this.activeTab.toLowerCase().replace('_page', '')
        this.previewUrl = `${baseUrl}/${pagePath}`
      } else {
        // 如果没有配置域名，使用默认方式
        const baseUrl = window.config?.api?.baseURL?.replace('/api', '') || ''
        this.previewUrl = `${baseUrl}/website/${this.activeTab.toLowerCase().replace('_page', '')}`
      }

      this.previewVisible = true
    },

    // 记录内容中使用的图片到数据库
    async recordContentImages() {
      try {
        // 提取当前内容中的所有图片URL
        const currentImageUrls = this.extractImageUrls(this.localContent)

        // 提取原始内容中的图片URL（用于对比）
        // 需要从原始内容的相同结构中提取
        let originalImageUrls = []
        if (this.currentContent) {
          // 构造与localContent相同结构的原始内容用于比较
          const originalLocalContent = this.buildOriginalLocalContent()
          originalImageUrls = this.extractImageUrls(originalLocalContent)
        }

        // 找出新增的图片URL（在当前内容中但不在原始内容中）
        const newImageUrls = currentImageUrls.filter(url => !originalImageUrls.includes(url))

        if (newImageUrls.length === 0) {
          return
        }

        // 只为新增的图片URL记录到数据库
        for (const url of newImageUrls) {
          try {
            await this.recordImageByUrl(url)
          } catch (error) {
            // 继续处理其他图片
          }
        }
      } catch (error) {
        throw error
      }
    },

    // 构造与localContent相同结构的原始内容用于比较
    buildOriginalLocalContent() {
      if (!this.currentContent) return {}

      // 解析原始内容的JSON数据
      let parsedConfig = {}
      let parsedCases = []
      if (this.currentContent.content) {
        try {
          const parsed = JSON.parse(this.currentContent.content)
          parsedConfig = parsed.config || {}
          parsedCases = parsed.cases || []
        } catch (error) {
          // 解析失败时静默处理
        }
      }

      return {
        title: this.currentContent.title || '',
        description: this.currentContent.description || this.currentContent.summary || '',
        seoTitle: this.currentContent.seoTitle || '',
        seoDescription: this.currentContent.seoDescription || '',
        seoKeywords: this.currentContent.seoKeywords || '',
        config: parsedConfig,
        cases: parsedCases
      }
    },

    // 提取内容中的所有图片URL
    extractImageUrls(content) {
      const urls = new Set() // 使用Set避免重复

      // 递归提取对象中的所有图片URL
      const extractFromObject = (obj) => {
        if (!obj || typeof obj !== 'object') return

        for (const key in obj) {
          const value = obj[key]

          // 检查是否是图片URL
          if (typeof value === 'string' && this.isImageUrl(value)) {
            urls.add(value)
          }
          // 递归处理嵌套对象和数组
          else if (typeof value === 'object') {
            extractFromObject(value)
          }
        }
      }

      extractFromObject(content)
      return Array.from(urls)
    },

    // 判断是否是图片URL
    isImageUrl(url) {
      if (!url || typeof url !== 'string') return false

      // 检查是否是HTTP/HTTPS URL
      if (!url.startsWith('http://') && !url.startsWith('https://')) return false

      // 检查是否包含图片文件扩展名
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg']
      const lowerUrl = url.toLowerCase()

      // 检查文件扩展名、包含image/img关键词，或者是COS临时文件URL
      return imageExtensions.some(ext => lowerUrl.includes(ext)) ||
             lowerUrl.includes('image') ||
             lowerUrl.includes('img') ||
             lowerUrl.includes('cos.ap-') ||  // 腾讯云COS URL
             lowerUrl.includes('/temp/') ||   // 临时文件目录
             lowerUrl.includes('/team/') ||   // 团队头像目录
             lowerUrl.includes('/company/') || // 公司图片目录
             lowerUrl.includes('/milestone/')  // 里程碑图片目录
    },

    // 根据URL记录图片到数据库
    async recordImageByUrl(url) {
      try {
        // 从URL中提取文件信息
        const urlParts = url.split('/')
        const fileName = urlParts[urlParts.length - 1] || 'unknown'
        const cosKey = url.replace(/^https?:\/\/[^\/]+\//, '') // 移除域名部分

        // 确定图片分类
        const category = this.getCategoryByPageType(this.activeTab)

        const imageData = {
          originalName: fileName,
          cosKey: cosKey,
          cosUrl: url,
          fileSize: 0, // 无法获取文件大小，设为0
          mimeType: this.getMimeTypeFromUrl(url),
          category: category,
          description: `${this.activeTab}页面使用的图片`,
          isPublic: true
        }

        await websiteImageApi.recordImageUpload(imageData)
      } catch (error) {
        throw error
      }
    },

    // 根据页面类型获取图片分类
    getCategoryByPageType(pageType) {
      const categoryMap = {
        'ABOUT_PAGE': 'HOMEPAGE',
        'SERVICE_PAGE': 'SERVICE',
        'CASE_PAGE': 'CASE',
        'CONTACT_PAGE': 'GALLERY'
      }
      return categoryMap[pageType] || 'GALLERY'
    },

    // 从URL获取MIME类型
    getMimeTypeFromUrl(url) {
      const lowerUrl = url.toLowerCase()
      if (lowerUrl.includes('.jpg') || lowerUrl.includes('.jpeg')) return 'image/jpeg'
      if (lowerUrl.includes('.png')) return 'image/png'
      if (lowerUrl.includes('.gif')) return 'image/gif'
      if (lowerUrl.includes('.webp')) return 'image/webp'
      if (lowerUrl.includes('.svg')) return 'image/svg+xml'
      return 'image/jpeg' // 默认
    },

    // 刷新页面
    refreshPage() {
      this.loadContent()
    },

    // 处理刷新按钮点击
    handleRefresh() {
      this.loadContent()
    },

    // 获取页面标题（参考React版本的实现）
    getPageTitle() {
      // 优先使用用户编辑的标题，然后是API返回的标题，最后是默认标题
      const editedTitle = (this.localContent.title || '').trim()
      const apiTitle = (this.currentContent?.title || '').trim()
      const defaultTitle = this.pageTitles[this.activeTab] || '页面'

      return editedTitle || apiTitle || defaultTitle
    },

    // 加载网站配置
    async loadWebsiteConfig() {
      try {
        const response = await websiteConfigApi.getPublicConfig()
        let configData = response.data
        if (response.data && typeof response.data === 'object' && 'code' in response.data && 'data' in response.data) {
          configData = response.data.data
        }
        this.websiteConfig = configData || {}

        // 调试信息：检查获取到的配置
        console.log('内容编辑页面获取到的网站配置:', this.websiteConfig)
        console.log('域名配置:', this.websiteConfig.websiteDomain)
      } catch (error) {
        console.error('加载网站配置失败:', error)
        // 静默处理错误，不影响主要功能
        this.websiteConfig = {}
      }
    },

    // 处理预览（参考React版本的实现）
    handlePreview() {
      const pageUrls = {
        'ABOUT_PAGE': '/about',
        'SERVICE_PAGE': '/services',
        'CASE_PAGE': '/cases',
        'CONTACT_PAGE': '/contact'
      }

      const url = pageUrls[this.activeTab]
      if (!url) {
        this.$message.info('预览功能开发中...')
        return
      }

      // 获取配置的域名
      const websiteDomain = this.websiteConfig?.websiteDomain?.trim()
      let fullUrl

      if (websiteDomain) {
        // 使用配置的域名
        const baseUrl = websiteDomain.endsWith('/') ? websiteDomain.slice(0, -1) : websiteDomain
        fullUrl = `${baseUrl}${url}`
      } else {
        // 如果没有配置域名，提示用户先配置
        this.$message({
          message: '请先在公共配置中配置网站域名以使用预览功能',
          type: 'warning',
          duration: 4000
        })
        return
      }

      window.open(fullUrl, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.visual-content-editor {
  min-height: 100vh;
  background-color: #f5f5f5;

  .top-nav {
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .nav-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      border-bottom: 1px solid #e8e8e8;

      .nav-left {
        .nav-title {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: #1f2937;
        }
      }

      .nav-right {
        display: flex;
        gap: 12px;
      }
    }

    .page-tabs {
      ::v-deep .el-tabs__header {
        margin: 0;
        background-color: #fafafa;
        border-bottom: 1px solid #e8e8e8;
      }

      ::v-deep .el-tabs__nav-wrap {
        padding: 0 24px;
      }

      ::v-deep .el-tabs__item {
        border: none;
        background-color: transparent;

        &.is-active {
          background-color: white;
          border-bottom: 2px solid #1890ff;
        }
      }

      // 隐藏空的tab面板内容，消除空白
      ::v-deep .el-tabs__content {
        display: none;
      }

      ::v-deep .el-tab-pane {
        display: none;
      }
    }
  }

  .main-content {
    padding: 24px;

    .content-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;

      .header-left {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #1f2937;
        }

        .page-description {
          margin: 0;
          color: #6b7280;
          font-size: 14px;
        }
      }

      .header-right {
        display: flex;
        gap: 12px;
      }
    }

    .change-alert {
      margin-bottom: 24px;
    }

    .editor-content {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

  }

  .preview-dialog {
    ::v-deep .el-dialog__body {
      padding: 0;
    }

    .preview-content {
      iframe {
        border: none;
        width: 100%;
        height: 70vh;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .visual-content-editor {
    .main-content {
      padding: 16px;

      .content-header {
        flex-direction: column;
        align-items: stretch;

        .header-right {
          margin-top: 16px;
          justify-content: flex-start;
        }
      }

      .editor-content {
        margin: 0 -8px;
        border-radius: 0;
      }
    }
  }
}
</style>
