<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:53
 * @Description: 
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      row-key="label"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="550"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #deviceTree>
        <el-cascader
          v-model="productIdName"
          filterable
          clearable
          style="width: 250px"
          :options="productTreeOption"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          @change="handleProductTree"
        >
        </el-cascader>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-edit-outline"
            @click="showDetail(slotProps.row)"
          >
            查看
          </el-button>
        </span>
      </template>
    </ProTable>
    <ProDrawer
      class="margin-top"
      :value="unfoldDrawer"
      size="40%"
      :title="drawerTitle"
      :top="'10%'"
      :no-footer="true"
      @cancel="closeDrawer"
    >
      <div class="order-border-box">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="客户编号">{{
            dayPrintCount.customerSeq
          }}</el-descriptions-item>
          <el-descriptions-item label="店铺名称">{{
            dayPrintCount.customerName
          }}</el-descriptions-item>
          <el-descriptions-item label="品牌">{{
            dayPrintCount.brand
          }}</el-descriptions-item>
          <el-descriptions-item label="机型">{{
            dayPrintCount.machine
          }}</el-descriptions-item>
          <el-descriptions-item label="设备组名称" span="2">{{
            dayPrintCount.deviceGroup?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="合约类型">{{
            dayPrintCount.treatyType?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="是否安装客户端">{{
            dayPrintCount.regCliState == 0 ? "否" : "是"
          }}</el-descriptions-item>
          <el-descriptions-item label="统计类型">{{
            dayPrintCount.dataSource?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="日期">{{
            dayPrintCount.currDate
          }}</el-descriptions-item>
          <el-descriptions-item
            v-if="dayPrintCount.dataSource?.value === 'REPAIR'"
            label="统计周期"
            >{{ dayPrintCount.invtervalDays }}天</el-descriptions-item
          >
          <el-descriptions-item label="起始黑白打印数">{{
            dayPrintCount.blackWhiteInception
          }}</el-descriptions-item>
          <el-descriptions-item label="起始彩色打印数">{{
            dayPrintCount.colorInception
          }}</el-descriptions-item>
          <el-descriptions-item label="截止黑白打印数">{{
            dayPrintCount.blackWhiteCutoff
          }}</el-descriptions-item>
          <el-descriptions-item label="截止彩色打印数">{{
            dayPrintCount.colorCutoff
          }}</el-descriptions-item>
          <el-descriptions-item label="黑白打印量">{{
            dayPrintCount.blackWhiteCount
          }}</el-descriptions-item>
          <el-descriptions-item label="彩色印量">{{
            dayPrintCount.colorCount
          }}</el-descriptions-item>
          <el-descriptions-item label="总打印量" span="2">{{
            dayPrintCount.totalCount
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </ProDrawer>
  </div>
</template>
<script>
import { printDailyListApi, printDailyDetailListApi } from "@/api/statisics";
import { cloneDeep } from "lodash";
import { filterParam, filterParamRange } from "@/utils";
import { productListApi } from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "DayCount",

  mixins: [],
  props: {},
  data() {
    return {
      productIdName: "",
      // 列表
      spareiTypeList: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      productTreeOption: [],
      queryParam: {},
      columns: [
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          valueType: "product",
          isSearch: true,
          clearable: true,
          // width: 200,
          // searchSlot: "deviceTree",
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          width: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          width: 200,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          valueType: "select",
          isTable: true,
          width: 70,
        },
        {
          dataIndex: "machine",
          title: "机型",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          valueType: "input",
          isTable: true,
          isSearch: true,
          placeholder: "1号机： 1",
          formatter: (row) => row.deviceGroup.label,
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => row.serType?.label,
        },
        {
          dataIndex: "serTypes",
          title: "服务类型",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "散修",
              value: "SCATTERED",
            },
            {
              label: "购机不保",
              value: "NO_WARRANTY",
            },
            {
              label: "购机质保",
              value: "WARRANTY",
            },
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "质保服务",
              value: "QA",
            },
            {
              label: "质保含部件",
              value: "QA_COMPONENT",
            },
            {
              label: "维保服务",
              value: "MAINTENANCE",
            },

            {
              label: "其它",
              value: "OTHER",
            },
          ],
        },
        {
          dataIndex: "regCliState",
          title: "安装客户端",
          valueType: "select",
          isSearch: true,
          isTable: true,
          clearable: true,
          option: [
            { value: 1, label: "是" },
            { value: 0, label: "否" },
          ],
          formatter: (row) => (row.regCliState == 1 ? "是" : "否"),
        },
        {
          dataIndex: "dataSource",
          title: "数据来源",
          formatter: (row) => row.dataSource?.label,
          isTable: true,
        },
        {
          dataIndex: "blackRange",
          title: "黑白印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "colorRange",
          title: "彩色印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "totalRange",
          title: "总印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "currDate",
          title: "统计日期",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
          width: 100,
        },

        // {
        //   dataIndex: "blackWhiteInception",
        //   title: "初始黑白打印数",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "blackWhiteCutoff",
        //   title: "截止黑白打印数",
        //   isTable: true,
        // },
        {
          dataIndex: "blackWhiteCount",
          title: "黑白印量",
          isTable: true,
        },
        // {
        //   dataIndex: "colorInception",
        //   title: "起始彩色打印数",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "colorCutoff",
        //   title: "截止彩色打印数",
        //   isTable: true,
        // },
        {
          dataIndex: "colorCount",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "totalCount",
          title: "总印量",
          width: 150,
          isTable: true,
        },
        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      unfoldDrawer: false,
      drawerTitle: "",
      dayPrintCount: {},
    };
  },

  computed: {},

  watch: {},
  created() {},

  mounted() {
    this.$refs.ProTable.refresh();
    this.getProductTree();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          beginYearMonth: null,
          endYearMonth: null,
          data: parameter.currDate,
        },
        {
          beginBlackWhite: null,
          endBlackWhite: null,
          data: parameter.blackRange,
        },
        {
          beginColor: null,
          endColor: null,
          data: parameter.colorRange,
        },
        {
          beginCount: null,
          endCount: null,
          data: parameter.totalRange,
        },
      ];
      filterParamRange(this, this.queryParam, result);

      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.currDate;
      delete requestParameters.blackRange;
      delete requestParameters.colorRange;
      delete requestParameters.totalRange;
      printDailyListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    // 计算日印量
    showDetail(row) {
      this.drawerTitle = "日印量统计 - " + row.customerName;
      printDailyDetailListApi(row.id)
        .then((res) => {
          console.log(res, "日统计明细");
          this.dayPrintCount = res.data;
        })
        .catch((err) => {
          console.log(err);
        });
      this.unfoldDrawer = true;
    },
    closeDrawer() {
      this.unfoldDrawer = false;
    },
    async getProductTree() {
      try {
        const result = await productListApi({ pageNumber: 1, pageSize: 9999 });
        if (result.code === 200 && result.data) {
          this.productTreeOption = result.data;
        }
      } catch (error) {
        console.log(error);
      }
    },
    formSubmit(val) {
      console.log(val);
    },

    handleProductTree(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.gap {
  margin: 20px 0;
}
.order-border-box {
  border: dashed 1px #ccc;
  padding: 10px;
}
</style>
