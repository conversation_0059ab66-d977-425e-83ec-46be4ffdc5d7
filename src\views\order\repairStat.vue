<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-09-14 17:18:31
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-09-18 09:47:32
 * @FilePath: \benyin-web\src\views\order\repairStat.vue
 * @Description: 订单 - 维修统计
 * 
-->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="按机型分类" name="first" lazy>
        <RepairsStat type="model" :columns="modelColumns" />
      </el-tab-pane>
      <el-tab-pane label="按地区分类" name="second" lazy>
        <RepairsStat type="area" :columns="areaColumns" />
      </el-tab-pane>
      <el-tab-pane label="按服务分类" name="third" lazy>
        <RepairsStat type="service" :columns="serviceColumns" />
      </el-tab-pane>
      <!--<el-tab-pane label="按工作分类" name="fourth" lazy>-->
      <!--  工作类型暂未区分-->
      <!--  &lt;!&ndash;<RepairsStat type="work" :columns="workColumns" />&ndash;&gt;-->
      <!--</el-tab-pane>-->
      <el-tab-pane label="按问题分类" name="fifth" lazy>
        <RepairsStat type="problem" :columns="problemColumns" />
      </el-tab-pane>
      <el-tab-pane label="按频次分类" name="sixth" lazy>
        <RepairsStat type="frequency" :columns="frequencyColumns" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import RepairsStat from "@/views/order/components/repairsStat.vue";
import { dictTreeByCodeApi } from "@/api/user";
export default {
  name: "RepairStat",
  components: { RepairsStat },
  data() {
    return {
      activeName: "first",
      modelColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "productIds",
          title: "系列",
          isSearch: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "series",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "totalPay",
          title: "维修金额",
          isTable: true,
        },
        {
          dataIndex: "mechineCount",
          title: "机器数量",
          isTable: true,
        },
        {
          dataIndex: "printCount",
          title: "总印量",
          isTable: true,
        },
        {
          dataIndex: "repairCount",
          title: "维修台次",
          isTable: true,
        },
        {
          dataIndex: "avgAmount",
          title: "次均金额",
          isTable: true,
        },
        // {
        //   dataIndex: "avgAmount1",
        //   title: "次均频率",
        //   isTable: true,
        //   formatter: (row) => "/",
        // },
        {
          dataIndex: "avgPrintCount",
          title: "台均印量",
          isTable: true,
        },
      ],
      areaColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "totalPay",
          title: "维修金额",
          isTable: true,
        },
        {
          dataIndex: "printCount",
          title: "总印量",
          isTable: true,
        },
        {
          dataIndex: "repairCount",
          title: "维修台次",
          isTable: true,
        },
        {
          dataIndex: "avgAmount",
          title: "次均金额",
          isTable: true,
        },
        {
          dataIndex: "avgPrintCount",
          title: "台均印张",
          isTable: true,
        },
      ],
      serviceColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => row.treatyType?.label,
        },
        {
          dataIndex: "serTypes",
          title: "服务类型",
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [
            {
              label: "散修",
              value: "SCATTERED",
            },
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
          ],
          // optionMth: () => dictTreeByCodeApi(1200),
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
        },
        {
          dataIndex: "totalPay",
          title: "维修金额",
          isTable: true,
        },
        {
          dataIndex: "mechineCount",
          title: "机器数量",
          isTable: true,
        },
        {
          dataIndex: "printCount",
          title: "总印量",
          isTable: true,
        },
        {
          dataIndex: "repairCount",
          title: "维修台次",
          isTable: true,
        },
        {
          dataIndex: "avgAmount",
          title: "次均金额",
          isTable: true,
        },
        {
          dataIndex: "avgMechinePrintCount",
          title: "台均印量",
          isTable: true,
        },
        {
          dataIndex: "avgPrintCount",
          title: "次均印张",
          isTable: true,
        },
      ],
      workColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },

        {
          dataIndex: "workOrderType",
          title: "工作类型",
          isTable: true,
          formatter: (row) => row.workOrderTypes?.label,
          isSearch: true,
          valueType: "select",
          option: [
            { label: "维修", value: 1 },
            { label: "保养", value: 2 },
            { label: "装机", value: 3 },
            { label: "移机", value: 4 },
          ],
        },
        {
          dataIndex: "totalPay",
          title: "维修金额",
          isTable: true,
        },
        {
          dataIndex: "workCount",
          title: "工作次数",
          isTable: true,
        },
        {
          dataIndex: "workTime",
          title: "工作时长",
          isTable: true,
        },
        {
          dataIndex: "percentageOfDuration",
          title: "时长占比",
          isTable: true,
        },
        {
          dataIndex: "amountPercent",
          title: "金额占比",
          isTable: true,
        },
      ],
      problemColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "series",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "repairCount",
          title: "总次数",
          isTable: true,
        },
        {
          dataIndex: "excType",
          title: "现象分类",
          isTable: true,
          formatter: (row) => row.excType?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6000),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "excTypeScale",
          title: "占比(%)",
          isTable: true,
          formatter: (row) => (row.excTypeScale ? row.excTypeScale + "%" : "/"),
        },
        {
          dataIndex: "reasonType",
          title: "原因分类",
          isTable: true,
          formatter: (row) => row.reasonType?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(7000),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "reasonTypeScale",
          title: "占比(%)",
          isTable: true,
          formatter: (row) =>
            row.reasonTypeScale ? row.reasonTypeScale + "%" : "/",
        },
        {
          dataIndex: "resolveType",
          title: "处理类型",
          isTable: true,
          formatter: (row) => row.resolveType?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(8000),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "resolveTypeScale",
          title: "占比(%)",
          isTable: true,
          formatter: (row) =>
            row.resolveTypeScale ? row.resolveTypeScale + "%" : "/",
        },
        {
          dataIndex: "excUnit",
          title: "故障组件",
          isTable: true,
          formatter: (row) => row.excUnit?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(9000),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "excUnitScale",
          title: "占比(%)",
          isTable: true,
          formatter: (row) => (row.excUnitScale ? row.excUnitScale + "%" : "/"),
        },
      ],
      frequencyColumns: [
        {
          dataIndex: "productIds",
          title: "系列",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "series",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "mechineCount",
          title: "机器数量",
          isTable: true,
        },
        {
          dataIndex: "printCount",
          title: "总印量",
          isTable: true,
        },
        {
          dataIndex: "repairCount",
          title: "维修台次",
          isTable: true,
        },
        {
          dataIndex: "avgPrintCount",
          title: "台均印量",
          isTable: true,
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss"></style>
