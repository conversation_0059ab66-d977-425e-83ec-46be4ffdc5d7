<template>
  <div class="website-config">
    <!-- 顶部操作栏 -->
    <div class="config-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">网站配置管理</h1>
          <p class="page-description">点击任意配置项即可编辑，实时预览网站效果</p>

          <!-- 安全状态显示 -->
          <div v-if="encryptedKeys.length > 0" class="security-status">
            <div class="flex items-center gap-2 whitespace-nowrap">
              <i class="el-icon-lock text-green-600"></i>
              <span class="text-gray-500" style="font-size: 12px;">
                已启用加密存储：
              </span>
              <el-tag
                v-for="key in encryptedKeys.filter(k => CONFIG_FIELD_INFO[k])"
                :key="key"
                type="success"
                size="mini"
                style="font-size: 11px; padding: 2px 6px; margin-right: 4px;"
              >
                {{ CONFIG_FIELD_INFO[key].label }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="header-actions">
          <!-- 状态指示 -->
          <div v-if="hasUnsavedChanges" class="status-indicator">
            <el-badge
              :value="changedFields.size"
              :show-zero="false"
              class="bg-orange-500"
            >
              <span class="text-orange-600 bg-orange-50 px-3 py-1 rounded whitespace-nowrap">
                待保存更改
              </span>
            </el-badge>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button
              type="default"
              icon="el-icon-refresh"
              @click="refreshConfig"
              :loading="loading"
              size="middle"
            >
              <span class="hidden sm:inline">刷新数据</span>
            </el-button>

            <el-button
              type="default"
              icon="el-icon-view"
              @click="handlePreview"
              size="middle"
            >
              <span class="hidden sm:inline">预览网站</span>
            </el-button>

            <el-button
              v-if="hasUnsavedChanges"
              type="default"
              icon="el-icon-refresh-left"
              @click="handleResetChanges"
              size="middle"
            >
              <span class="hidden sm:inline">撤销更改</span>
            </el-button>

            <el-button
              type="primary"
              icon="el-icon-check"
              @click="handleSaveAll"
              :loading="isSaving"
              :disabled="!hasUnsavedChanges"
              size="middle"
            >
              {{ hasUnsavedChanges ? '保存配置' : '已保存' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 可视化编辑区域 -->
    <div class="config-content" v-loading="loading">
      <VisualConfigPreview
        :config="localConfig"
        :hasUnsavedChanges="hasUnsavedChanges"
        :changedFields="changedFields"
        @configChange="handleConfigChange"
      />
    </div>

    <!-- 底部提示栏 -->
    <div class="config-footer">
      <div class="footer-content">
        <span class="text-gray-500 text-sm">
          <kbd>Enter</kbd> 保存编辑 • <kbd>Esc</kbd> 取消编辑
          <template v-if="hasUnsavedChanges">
            <el-divider direction="vertical" />
            <span class="text-orange-600">
              {{ changedFields.size }} 项配置待保存
            </span>
          </template>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { websiteConfigApi } from '@/websites/api/website'
import { CONFIG_FIELD_INFO } from '@/websites/config/websiteConfig'
import { decryptConfigValue } from '@/websites/untils/encryption'
import VisualConfigPreview from '@/websites/components/website/VisualConfigPreview.vue'

export default {
  name: 'WebsiteConfig',
  components: {
    VisualConfigPreview
  },
  data() {
    return {
      loading: false,
      isSaving: false,
      originalConfig: {},
      localConfig: {},
      changedFields: new Set(),
      changedFieldsCount: 0, // 添加响应式计数器
      encryptedKeys: [],
      CONFIG_FIELD_INFO,
      decryptConfigValue
    }
  },

  computed: {
    hasUnsavedChanges() {
      return this.changedFieldsCount > 0
    }
  },
  
  mounted() {
    this.loadConfig()
    this.fetchEncryptionStatus()
  },

  methods: {
    // 加载配置
    async loadConfig() {
      this.loading = true
      try {
        // 获取公共配置
        const response = await websiteConfigApi.getPublicConfig()

        // 处理响应数据格式，与React项目保持一致
        let configData = response.data
        if (response.data && typeof response.data === 'object' && 'code' in response.data && 'data' in response.data) {
          configData = response.data.data
        }

        // 确保配置数据的空值安全
        const safeConfig = this.ensureConfigSafety(configData || {})

        // 获取敏感配置（与React项目完全一致）
        try {
          const encryptedTencentMapKey = await websiteConfigApi.getSensitiveConfigEncrypted('tencentMapKey')
          const tencentMapKey = encryptedTencentMapKey ? this.decryptConfigValue(encryptedTencentMapKey) : ''
          const sensitiveConfig = { tencentMapKey }

          // 合并公共配置和敏感配置
          const fullConfig = { ...safeConfig, ...sensitiveConfig }



          this.originalConfig = { ...fullConfig }
          this.localConfig = { ...fullConfig }
        } catch (sensitiveError) {
          // 敏感配置获取失败，只使用公共配置
          this.originalConfig = { ...safeConfig }
          this.localConfig = { ...safeConfig }
        }

        this.changedFields.clear()
        this.changedFieldsCount = 0
      } catch (error) {
        this.$message.error('加载配置失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 确保配置数据的空值安全
    ensureConfigSafety(config) {
      const safeConfig = {}

      // 遍历所有配置字段，确保每个字段都有默认值
      Object.keys(CONFIG_FIELD_INFO).forEach(fieldKey => {
        const fieldInfo = CONFIG_FIELD_INFO[fieldKey]
        const value = config[fieldKey]

        if (value !== undefined && value !== null) {
          safeConfig[fieldKey] = value
        } else {
          // 根据字段类型设置默认值
          switch (fieldInfo.type) {
            case 'switch':
              safeConfig[fieldKey] = false
              break
            case 'number':
              safeConfig[fieldKey] = 0
              break
            default:
              safeConfig[fieldKey] = ''
          }
        }
      })

      return safeConfig
    },

    // 刷新配置
    refreshConfig() {
      this.loadConfig()
    },

    // 处理配置更改
    handleConfigChange(key, value) {
      // 更新本地配置
      this.$set(this.localConfig, key, value)

      // 检查是否与原始值不同
      if (this.originalConfig[key] !== value) {
        this.changedFields.add(key)
      } else {
        this.changedFields.delete(key)
      }

      // 更新响应式计数器
      this.changedFieldsCount = this.changedFields.size
    },

    // 保存所有配置
    async handleSaveAll() {
      this.isSaving = true
      try {
        // 验证配置数据
        const validationErrors = this.validateAllConfigs()
        if (validationErrors.length > 0) {
          this.$message.error(`配置验证失败：${validationErrors[0]}`)
          return
        }

        // 准备配置数据，处理敏感信息
        const configToSave = { ...this.localConfig }

        // 定义敏感字段列表
        const sensitiveFields = ['tencentMapKey']

        // 检查敏感字段是否有变更
        const hasSensitiveChanges = sensitiveFields.some(field =>
          this.localConfig[field] !== this.originalConfig[field]
        )

        // 如果敏感字段已经加密且没有变更，从请求中移除以避免明文传输
        if (!hasSensitiveChanges) {
          sensitiveFields.forEach(field => {
            if (this.encryptedKeys.includes(field)) {
              delete configToSave[field]
            }
          })
        }

        // 调用API更新配置，与React项目保持一致
        const response = await websiteConfigApi.updateConfig(configToSave)

        // 处理响应，与React项目的错误处理逻辑一致
        if (typeof response.code === 'number' && response.code !== 200) {
          throw new Error(response.message || '配置更新失败')
        }

        // 如果有敏感字段变更，自动执行加密迁移
        if (hasSensitiveChanges) {
          try {
            await websiteConfigApi.migrateToEncryption()
          } catch (migrateError) {
            // 加密迁移失败不影响主要保存流程
          }
        }

        // 更新原始配置，重置变更状态
        this.originalConfig = { ...this.localConfig }
        this.changedFields.clear()
        this.changedFieldsCount = 0

        this.$message.success('配置保存成功')

        // 刷新配置以确保数据同步
        setTimeout(async () => {
          await this.loadConfig()
          await this.fetchEncryptionStatus() // 同时刷新加密状态
        }, 500)

      } catch (error) {
        const errorMessage = this.extractErrorMessage(error, '配置保存失败')
        this.$message.error(errorMessage)
      } finally {
        this.isSaving = false
      }
    },

    // 验证所有配置
    validateAllConfigs() {
      const errors = []

      Object.keys(this.localConfig).forEach(fieldKey => {
        const fieldInfo = CONFIG_FIELD_INFO[fieldKey]
        if (!fieldInfo) return

        const value = this.localConfig[fieldKey]
        const validation = this.validateFieldValue(fieldKey, value)

        if (!validation.valid) {
          errors.push(validation.message)
        }
      })

      return errors
    },

    // 验证字段值
    validateFieldValue(fieldKey, value) {
      const fieldInfo = CONFIG_FIELD_INFO[fieldKey]
      if (!fieldInfo) {
        return { valid: false, message: '未知字段' }
      }

      // 必填验证
      if (fieldInfo.required && (!value || value.toString().trim() === '')) {
        return { valid: false, message: `${fieldInfo.label}不能为空` }
      }

      // 长度验证
      if (fieldInfo.maxLength && value && value.toString().length > fieldInfo.maxLength) {
        return { valid: false, message: `${fieldInfo.label}不能超过${fieldInfo.maxLength}个字符` }
      }

      // 格式验证
      if (fieldInfo.pattern && value && !fieldInfo.pattern.test(value.toString())) {
        return { valid: false, message: fieldInfo.patternMessage || `${fieldInfo.label}格式不正确` }
      }

      return { valid: true }
    },

    // 提取错误信息
    extractErrorMessage(error, defaultMessage) {
      if (error?.response?.data?.message) {
        return error.response.data.message
      }
      if (error?.message) {
        return error.message
      }
      return defaultMessage
    },

    // 预览前端页面
    handlePreview() {
      // 如果有未保存的更改，提示用户
      if (this.hasUnsavedChanges) {
        this.$message({
          message: '当前有未保存的配置更改，预览将显示已保存的版本',
          type: 'info',
          duration: 4000
        })
      }

      // 获取配置的域名
      const websiteDomain = this.originalConfig?.websiteDomain?.trim()
      let previewUrl = '/'

      if (websiteDomain) {
        // 使用配置的域名
        previewUrl = websiteDomain.endsWith('/') ? websiteDomain.slice(0, -1) : websiteDomain
      } else {
        // 如果没有配置域名，提示用户先配置
        this.$message({
          message: '请先配置网站域名以使用预览功能',
          type: 'warning',
          duration: 4000
        })
        return
      }

      // 打开预览窗口
      const previewWindow = window.open(previewUrl, '_blank', 'noopener,noreferrer')

      if (!previewWindow) {
        this.$message.error('无法打开预览窗口，请检查浏览器弹窗设置')
        return
      }

      // 设置窗口标题以便识别
      setTimeout(() => {
        try {
          previewWindow.document.title = `预览模式 - ${this.originalConfig?.siteTitle || '网站'}`
        } catch (e) {
          // 跨域限制，忽略错误
        }
      }, 1000)
    },

    // 撤销所有变更
    handleResetChanges() {
      this.localConfig = { ...this.originalConfig }
      this.changedFields.clear()
      this.changedFieldsCount = 0
      this.$message.info('已撤销所有未保存的更改')
    },

    // 获取加密配置状态
    async fetchEncryptionStatus() {
      try {
        const keys = await websiteConfigApi.getEncryptionStatus()
        this.encryptedKeys = keys || []
      } catch (error) {
        // 静默处理错误，不显示警告
        // 如果API调用失败，使用默认值
        this.encryptedKeys = []
      }
    },



  },

  // 页面离开前提醒保存
  beforeRouteLeave(_to, _from, next) {
    if (this.hasUnsavedChanges) {
      this.$confirm('您有未保存的更改，确定要离开吗？', '确认离开', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        next()
      }).catch(() => {
        next(false)
      })
    } else {
      next()
    }
  }
}
</script>

<style lang="scss" scoped>
.website-config {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f9fafb; // bg-gray-50

  // 顶部操作栏
  .config-header {
    background: white; // bg-white
    border-bottom: 1px solid #e5e7eb; // border-b
    padding: 16px 24px; // px-4 sm:px-6 py-4
    flex-shrink: 0;

    .header-content {
      display: flex;
      flex-direction: column;
      gap: 16px; // gap-4

      @media (min-width: 1024px) {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
      }

      .header-left {
        flex: 1;
        min-width: 0;

        .page-title {
          margin: 0 0 4px 0; // mb-1
          font-size: 24px; // text-2xl
          font-weight: 500; // font-medium
          color: #111827; // text-gray-900
          line-height: 1.25;
        }

        .page-description {
          margin: 0;
          color: #6b7280; // text-gray-500
          font-size: 14px; // text-sm
          display: block;
        }

        .security-status {
          margin-top: 8px; // mt-2
        }
      }

      .header-actions {
        display: flex;
        flex-direction: column;
        gap: 12px;
        flex-shrink: 0;

        @media (min-width: 640px) {
          flex-direction: row;
          align-items: center;
        }

        .status-indicator {
          display: flex;
          justify-content: center;

          @media (min-width: 640px) {
            justify-content: flex-start;
          }
        }

        .action-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          justify-content: center;

          @media (min-width: 640px) {
            justify-content: flex-end;
          }
        }

        .el-button {
          border-radius: 6px;
          font-weight: 500;
          padding: 8px 16px;
          font-size: 14px;
          transition: all 0.3s ease;

          &.el-button--primary {
            background: #3b82f6; // bg-blue-500
            border-color: #3b82f6;

            &:hover {
              background: #2563eb; // hover:bg-blue-600
              border-color: #2563eb;
            }

            &:disabled {
              background: #9ca3af; // bg-gray-400
              border-color: #9ca3af;
            }
          }

          &.el-button--default {
            border-color: #d1d5db; // border-gray-300
            color: #374151; // text-gray-700
            background: white;

            &:hover {
              border-color: #9ca3af; // hover:border-gray-400
              color: #111827; // hover:text-gray-900
            }
          }
        }
      }
    }
  }

  // 可视化编辑区域
  .config-content {
    flex: 1;
    padding: 24px; // p-6
    overflow: auto;
  }

  // 底部提示栏
  .config-footer {
    background: white; // bg-white
    border-top: 1px solid #e5e7eb; // border-t
    padding: 12px 24px; // px-6 py-3
    flex-shrink: 0;

    .footer-content {
      text-align: center;

      kbd {
        background: #f3f4f6; // bg-gray-100
        border: 1px solid #d1d5db; // border-gray-300
        border-radius: 3px;
        padding: 2px 6px;
        font-size: 12px;
        font-family: monospace;
        margin: 0 2px;
      }
    }
  }


}

// 响应式设计
@media (max-width: 1200px) {
  .website-config {
    .config-header .header-content {
      flex-direction: column;
      align-items: stretch;
      gap: 20px;

      .header-actions {
        justify-content: flex-end;
      }
    }

    .config-content {
      padding: 20px;
    }
  }
}

@media (max-width: 768px) {
  .website-config {
    .config-header .header-content {
      padding: 20px;

      .header-left {
        .page-title {
          font-size: 24px;
        }

        .page-description {
          font-size: 14px;
        }
      }

      .header-actions {
        flex-direction: column;
        gap: 8px;

        .el-button {
          width: 100%;
          justify-content: center;
        }
      }
    }

    .config-content {
      padding: 16px;
    }
  }
}

@media (max-width: 480px) {
  .website-config {
    .config-header .header-content {
      padding: 16px;

      .header-left {
        .page-title {
          font-size: 20px;
        }

        .page-description {
          font-size: 13px;
        }

        .unsaved-alert {
          margin-top: 12px;
        }
      }

      .header-actions {
        .el-button {
          padding: 10px 20px;
          font-size: 14px;
        }
      }
    }

    .config-content {
      padding: 12px;
    }
  }
}

// 页面加载动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.website-config {
  animation: fadeInUp 0.6s ease-out;

  .config-header {
    animation: fadeInUp 0.6s ease-out 0.1s both;
  }

  .config-content {
    animation: fadeInUp 0.6s ease-out 0.2s both;
  }
}

// 深色模式支持（预留）
@media (prefers-color-scheme: dark) {
  .website-config {
    background-color: #111827;

    .config-header {
      background: #1f2937;
      border-color: #374151;

      .header-content {
        .header-left {
          .page-title {
            color: #f9fafb;
          }

          .page-description {
            color: #d1d5db;
          }
        }
      }
    }

    .config-content {
      background-color: #111827;
    }
  }
}
</style>
