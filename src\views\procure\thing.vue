<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-25 10:59:08
 * @Description: 库品管理
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增物品
        </el-button>
        <!--<el-button-->
        <!--  type="success"-->
        <!--  icon="el-icon-upload2"-->
        <!--  size="mini"-->
        <!--  @click="$refs.uploadExcel.show()"-->
        <!--&gt;-->
        <!--  导入数据-->
        <!--</el-button>-->
      </template>
      <template #type="slotProps">
        {{ slotProps.row.type.label }}
      </template>
      <template #img="slotProps">
        <el-image
          v-if="
            slotProps.row.imageFiles && slotProps.row.imageFiles.length !== 0
          "
          style="width: 100px; height: 100px"
          :src="slotProps.row?.imageFiles?.[0].url"
          :preview-src-list="[slotProps.row?.imageFiles?.[0].url]"
        ></el-image>
        <div v-else>暂无</div>
      </template>
      <template #searchType>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.type"
          filterable
          :options="options1"
          style="width: 100%"
          :props="{
            label: 'label',
            value: 'value',
            children: 'children',
            expandTrigger: 'click',
          }"
          clearable
          leaf-only
          @change="handleChangeType"
        ></el-cascader>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-circle-check"
            @click="handleInfo(slotProps.row)"
          >
            详情
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType == 'info'"
      :confirm-text="methodType === 'add' ? '确认新增' : '保存'"
      @ok="handleDialogOk"
      @cancel="closedDialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{
          formWidth: '100%',
          labelWidth: '180px',
          formPaddingBottom: '20px',
        }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
        <template #articleType>
          <el-radio-group
            v-model="form.articleType"
            :disabled="methodType === 'info'"
          >
            <el-radio :label="0">单品</el-radio>
            <el-radio :label="1">组合</el-radio>
          </el-radio-group>
        </template>
        <template #numberOem>
          <div style="display: flex; justify-content: space-between; gap: 10px">
            <el-input
              v-model="form.numberOem"
              disabled
              placeholder="请选择"
            ></el-input>
            <el-button
              v-if="methodType !== 'info'"
              type="text"
              @click="dialogVisibleOem = true"
            >
              选择OEM
            </el-button>
          </div>
        </template>
        <template #articleConfigs>
          <el-button
            v-if="methodType !== 'info'"
            type="primary"
            size="small"
            plain
            icon="el-icon-plus"
            @click="chooseArticle"
          >
            选择物品
          </el-button>
          <ProTable
            ref="ArticleProTable"
            :columns="selectedArticleColumns"
            show-pagination
            :show-search="false"
            :show-loading="false"
            :show-setting="false"
            row-key="id"
            :height="400"
            :local-pagination="selectedArticleLocalPagination"
            :data="selectedArticleData"
            sticky
          >
            <template #num="{ row }">
              <el-input-number
                v-model="row.num"
                style="width: 100%"
                size="small"
                controls-position="right"
                :disabled="methodType === 'info'"
                :min="1"
                :step="1"
              ></el-input-number>
            </template>
            <template #ArticlePrice="{ row }">
              <el-input-number
                v-model="row.price"
                style="width: 100%"
                size="small"
                controls-position="right"
                :disabled="methodType === 'info'"
                :min="0"
                :step="0.1"
              ></el-input-number>
            </template>
            <template #ArticleProcurePrice="{ row }">
              <el-input-number
                v-model="row.purchasePrice"
                style="width: 100%"
                size="small"
                controls-position="right"
                :disabled="methodType === 'info'"
                :min="0"
                :step="0.1"
              ></el-input-number>
            </template>
            <template #actions="{ row }">
              <div class="fixed-width">
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  :disabled="methodType === 'info'"
                  @click="handleDeleteArticle(row)"
                >
                  移除
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>
        <template #partName>
          <el-input
            v-model="form.partName"
            style="width: 100%"
            disabled
            placeholder="请输入内容"
          ></el-input>
        </template>
        <template #type>
          <el-cascader
            ref="ProductIds"
            v-model="form.type"
            filterable
            :disabled="methodType == 'info'"
            :options="options1"
            style="width: 100%"
            :props="{
              label: 'label',
              value: 'value',
              children: 'children',
              expandTrigger: 'click',
            }"
            clearable
            leaf-only
            @change="handleChange1"
          ></el-cascader>
        </template>
        <template #machine>
          <el-cascader
            v-model="productIdName"
            filterable
            clearable
            collapse-tags
            style="width: 544px"
            :options="productTreeOption"
            :props="{
              label: 'name',
              value: 'fullIdPath',
              children: 'children',
              expandTrigger: 'click',
            }"
            @change="handleProductTree"
          ></el-cascader>
        </template>
        <template #imageFiles>
          <ProUpload
            :file-list="form.imageFiles"
            :type="methodType"
            :limit="1"
            :multiple="false"
            @uploadSuccess="handleUploadSuccess"
            @uploadRemove="handleUploadRemove"
          />
          <!--          <div class="demo-image__preview">-->
          <!--            <el-image-->
          <!--              v-if="-->
          <!--                methodType == 'info' &&-->
          <!--                form.imageFiles &&-->
          <!--                form.imageFiles.length !== 0-->
          <!--              "-->
          <!--              style="width: 100px; height: 100px"-->
          <!--              :src="form.imageFiles[0].url"-->
          <!--              :preview-src-list="[form.imageFiles[0].url]"-->
          <!--              :fit="fit"-->
          <!--            >-->
          <!--            </el-image>-->
          <!--          </div>-->
          <!-- <el-image
            v-if="methodType == 'info' && form.imageFiles && form.imageFiles.length !== 0"
            style="width: 100px; height: 100px"
            :src="form.imageFiles[0].url"
            :fit="fit"
          ></el-image> -->
        </template>
      </ProForm>
      <!-- -->
      <!-- <ProTable v-show="deviceProductTree.length > 0" style="margin-top:-40px" ref="ProTable1" :show-search="false"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" :columns="columns1" :show-pagination="false"
        row-key="id" :data="deviceProductTree" sticky :height="400" default-expand-all :show-index="false"
        :show-setting="false" @loadData="load1">

      </ProTable> -->
    </ProDrawer>
    <ProDrawer
      :value="dialogVisibleOem"
      title="选择OEM"
      size="80%"
      :confirm-loading="false"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisibleOem = false"
    >
      <OEM @chooseOem="setOem"></OEM>
    </ProDrawer>

    <!-- 物品选择弹窗 -->
    <ProDialog
      :value="articleDialog"
      title="选择物品"
      width="90%"
      :confirm-loading="articleDialogLoading"
      confirm-text="确认选择"
      top="10px"
      @ok="handleArticleDialogOk"
      @cancel="closeArticleDialog"
    >
      <ProTable
        ref="chooseArticleTable"
        row-key="id"
        :layout="{ labelWidth: '120px' }"
        :data="chooseArticleData"
        :columns="articleColumns"
        :height="400"
        :local-pagination="articleLocalPagination"
        show-index
        show-search
        show-loading
        show-selection
        :reserve-selection="true"
        @loadData="loadArticleData"
        @handleSelectionChange="handleSelectionArticle"
      >
        <template #unit>
          <el-input
            v-model="articleQueryUnit"
            style="width: 100%"
            placeholder="请输入内容"
          ></el-input>
        </template>
      </ProTable>
    </ProDialog>
    <!-- 导入 -->
    <UploadExcel
      ref="uploadExcel"
      title="导入物品数据"
      :action-url="actionUrl"
      :download-template-fun="handleDownloadTemplate"
      @uploadSuccess="handleUploadExcelSuccess"
    />
  </div>
</template>
<script>
import {
  articlePageApi,
  articleAddApi,
  articleDelApi,
  articleEditApi,
  warehouseListApi,
  manufacturerListApi,
  productAssocTreeApi,
  articleGetDetailApi,
  importThingApi,
  downloadThingTemplateApi,
} from "@/api/store";
import { manufacturerInfoApi } from "@/api/manufacturer";
import OEM from "@/views/dispose/components/oem.vue";
import { partListApi, partProductTreeApi, productListApi } from "@/api/dispose";
import { dictTreeByCodeApi, dictTreeByCodeApi2 } from "@/api/user";
import ProUpload from "@/components/ProUpload/index.vue";
import { cloneDeep } from "lodash";
import { articleInquiryOrderPageApi } from "@/api/procure";
import { Message } from "element-ui";
import { filterParam } from "@/utils";
const { uploadURL } = window.config.api;
import { handleExcelExport } from "@/utils/exportExcel";
import UploadExcel from "@/components/ProUpload/excel.vue";
export default {
  name: "Thing",
  components: {
    OEM,
    ProUpload,
    UploadExcel,
  },
  mixins: [],
  props: {},
  data() {
    const that = this;
    return {
      actionUrl: uploadURL + importThingApi,
      productIdName: [],
      productTreeOption: [],
      deviceProductTree: [],
      dialogVisibleOem: false,
      active: 9,
      options1: [],
      // 列表
      spareiTypeList: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        aduitState: null,
        name: null,
      },
      columns: [
        {
          dataIndex: "img",
          title: "物品图片",
          isTable: true,
          tableSlot: "img",
          width: 120,
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          minWidth: 150,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          minWidth: 150,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.manufacturerChannel.label,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },
        {
          dataIndex: "partName",
          title: "零件中文名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerName",
          title: "制造商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "partBrand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "type",
          title: "物品大小类",
          isSearch: true,
          valueType: "select",
          searchSlot: "searchType",
        },
        // {
        //   dataIndex: "name",
        //   title: "关键字搜索",
        //   isTable: false,
        //   isSearch: true,
        //   span: 16,
        //   valueType: "input",
        //   placeholder: "物品名称/零件名称/制造商物品名称",
        // },
        // {
        //   dataIndex: "brand",
        //   title: "品牌",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "series",
        //   title: "系列",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "model",
        //   title: "机型",
        //   isTable: true,
        // },
        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 200,
        },
      ],
      columns1: [
        {
          dataIndex: "name",
          title: "品牌/产品树信息",
          isTable: true,
        },
      ],
      //新增
      methodType: "add",
      confirmLoading: false,
      form: { parentId: "" },
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: { parentId: "" },
      formcolumns: [
        {
          dataIndex: "name",
          isForm: true,
          title: "物品名称",
          valueType: "input",
          clearable: true,
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入物品名称",
              trigger: "change",
            },
          ],
        },

        {
          clearboth: true,
          dataIndex: "type",
          title: "物品大小类",
          isForm: true,
          formSpan: 6,
          formSlot: "type",
          prop: [
            {
              required: true,
              message: "请选择物品大小类",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "articleType",
          title: "物品类型",
          isForm: true,
          valueType: "radio",
          formSpan: 6,
          formSlot: "articleType",
          prop: [
            {
              required: true,
              message: "请选择物品类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "productId",
          title: "选择机型",
          isForm: true,
          formSpan: 12,
          formSlot: "machine",
          prop: [
            {
              required: true,
              message: "请选择机型",
              trigger: "change",
            },
          ],
        },

        // {
        //   clearboth: true,
        //   dataIndex: "type",
        //   title: "物品类型",
        //   isForm: true,
        //   valueType: "select",
        //   formSpan: 6,
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(2100),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择物品类型",
        //       trigger: "change",
        //     },
        //   ],
        // },

        {
          dataIndex: "numberOem",
          title: "原厂零件编号（OEM）",
          isForm: true,
          valueType: "select",
          formSpan: 6,
          formSlot: "numberOem",
          // option: [],
          // optionMth: () => partListApi({ pageNumber: 1, pageSize: 10000 }),
          // fun: {
          //   change: ((data) => { this.setOem(data) }),
          // },
          // datakey: 'rows',
          // optionskey: {
          //   label: 'oemNumber',
          //   value: 'oemNumber',
          //   ruturnAll: true,
          // },
          prop: [
            {
              required: true,
              message: "请选择原厂零件编号（OEM）",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "partName",
          isForm: true,
          title: "零件名称",
          valueType: "input",
          clearable: true,
          formSlot: "partName",
          formSpan: 6,
        },

        // {
        //   dataIndex: "supplierCode",
        //   title: "供应商编号",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请输入供应商编号",
        //       trigger: "change",
        //     },
        //   ],
        // },
        // {
        //   dataIndex: "supplierName",
        //   title: "供应商名称",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请输入供应商名称",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 6,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择制造商渠道",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "manufacturerGoodsCode",
          title: "制造商物品编号",
          isForm: true,
          valueType: "input",
          clearable: true,
          disabled: false,
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请输入制造商物品编号",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          isForm: true,
          valueType: "input",
          clearable: true,
          disabled: false,
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请输入制造商物品名称",
              trigger: "change",
            },
          ],
        },

        {
          dataIndex: "manufacturerId",
          title: "制造商名称",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 6,
          option: [],
          optionMth: () => manufacturerListApi(),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请输入制造商名称",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "manufacturerCode",
          title: "制造商编号",
          isForm: true,
          valueType: "input",
          disabled: true,
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "partBrand",
          title: "零件品牌",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        // {
        //   dataIndex: "packNumber",
        //   title: "装箱数量（个）",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        // },
        // {
        //   dataIndex: "minOrder",
        //   title: "最小起订量",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        // },
        {
          dataIndex: "minUnit",
          title: "单位（最小单位）",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "colorBox",
          title: "颜色/纸盒",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "expectLife",
          title: "预计寿命",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "suttle",
          title: "净重",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "packSize",
          title: "包装尺寸",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "inCarrier",
          title: "含载体",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "inChip",
          title: "含芯片",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "packWeight",
          title: "包装-重量",
          isForm: true,
          valueType: "input-number",
          step: 1,
          isnumtop: true,
          clearable: true,
          formSpan: 6,
          unit: "g",
          prop: [
            {
              required: false,
              message: "请输入包装-重量",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "packLong",
          title: "包装-长",
          isForm: true,
          valueType: "input-number",
          step: 1,
          isnumtop: true,
          clearable: true,
          formSpan: 6,
          unit: "mm",
          prop: [
            {
              required: false,
              message: "请输入包装-长",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "packWide",
          title: "包装-宽",
          isForm: true,
          valueType: "input-number",
          step: 1,
          isnumtop: true,
          clearable: true,
          formSpan: 6,
          unit: "mm",
          prop: [
            {
              required: false,
              message: "请输入包装-宽",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "packHigh",
          title: "包装-高",
          isForm: true,
          valueType: "input-number",
          step: 1,
          isnumtop: true,
          clearable: true,
          formSpan: 6,
          unit: "mm",
          prop: [
            {
              required: false,
              message: "请输入包装-高",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "saveWeek",
          title: "保存周期",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "saveTemp",
          title: "保存温度",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "saveHumidity",
          title: "保存湿度",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "isSqueeze",
          title: "能否挤压",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        // {
        //   dataIndex: "paperWeight",
        //   title: "纸张克重",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        // },
        // {
        //   dataIndex: "paperLong",
        //   title: "纸张-长",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        // },
        // {
        //   dataIndex: "paperWide",
        //   title: "纸张-宽",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        // },
        // {
        //   dataIndex: "paperSize",
        //   title: "纸张尺寸规格",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        // },
        // {
        //   dataIndex: "paperSeries",
        //   title: "纸张系列",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 6,
        // },
        {
          dataIndex: "totalNumber",
          title: "统计数量",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "avgMoney",
          title: "平均单价",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "sumLife",
          title: "修正寿命",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "avgLife",
          title: "平均寿命",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "packSpec",
          title: "包装规格",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "imageFiles",
          title: "物品图片",
          isForm: true,
          width: 150,
          formSlot: "imageFiles",
        },

        {
          dataIndex: "articleConfigs",
          title: "下级物品",
          isForm: false,
          formSlot: "articleConfigs",
          formSpan: 24,
        },
      ],
      //字典项
      roleId: null,
      dialogTitleR: "",
      dialogVisibleR: false,
      dialogVisibleU: false,
      includeList: [], // 耗材/零件包含的value
      articleLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      selectedArticleLocalPagination: { pageNumber: 1, pageSize: 10, total: 0 },
      articleDialog: false,
      articleDialogLoading: false,
      articleSelection: [],
      chooseArticleData: [],
      // 物品表单相关
      articleColumns: [
        {
          dataIndex: "code",
          title: "物品编号",
          minWidth: 120,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        // {
        //   dataIndex: "name",
        //   title: "物品名称",
        //   minWidth: 120,
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          minWidth: 120,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "OEM中文名称",
          minWidth: 150,
          isTable: true,
        },
        {
          dataIndex: "manufacturerGoodsCode",
          title: "制造商物品编号",
          minWidth: 120,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          minWidth: 150,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerName",
          title: "制造商名称",
          minWidth: 150,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "id",
          title: "制造渠道",
          minWidth: 80,
          isTable: true,
          formatter: (row) => row.manufacturerChannel.label,
        },
        {
          dataIndex: "type",
          title: "物品小类",
          minWidth: 80,
          isTable: true,
          formatter: (row) => row.type.label,
        },
        {
          dataIndex: "unit",
          title: "单位",
          minWidth: 80,
          isTable: true,
          isSearch: true,
          searchSlot: "unit",
        },
        // {
        //   dataIndex: "colorBox",
        //   title: "颜色/纸盒",
        //   width: 120,
        //   isTable: true,
        // },
        // {
        //   dataIndex: "inCarrier",
        //   title: "含载体",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "inChip",
        //   title: "含芯片",
        //   width: 120,
        //   isTable: true,
        // },
        // {
        //   dataIndex: "suttle",
        //   title: "净重",
        //   width: 120,
        //   isTable: true,
        // },

        // {
        //   dataIndex: "unit",
        //   title: "单位",
        //   isSearch: true,
        //   width: 120,
        //   searchSlot: "unit",
        // },
      ],
      selectedArticleColumns: [
        {
          dataIndex: "code",
          title: "物品编号",
          width: 180,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          width: 230,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "OEM中文名称",
          width: 180,
          isTable: true,
        },
        {
          dataIndex: "num",
          title: "价格",
          width: 140,
          isTable: true,
          tableSlot: "num",
        },
        {
          dataIndex: "price",
          title: "价格",
          width: 140,
          isTable: true,
          tableSlot: "ArticlePrice",
        },
        {
          dataIndex: "purchasePrice",
          title: "采购价格",
          width: 140,
          isTable: true,
          tableSlot: "ArticleProcurePrice",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          // fixed: "right",
          width: 120,
          tableSlot: "actions",
        },
      ],
      selectedArticleData: [],
      articleQueryUnit: "",
    };
  },

  computed: {},
  watch: {
    $route: {
      handler(val) {
        if (val.query.type === "add") {
          this.handleAdd();
          this.$set(this.form, "name", val.query.name);
          this.$set(this.form, "manufacturerChannel", "2206");
        }
      },
      immediate: true,
    },
    "form.manufacturerChannel": {
      handler: function (val) {
        if (val == 2202) {
          this.$set(this.form, "manufacturerGoodsCode", this.form.numberOem);
          this.$set(this.form, "manufacturerGoodsName", this.form.partName);
          // this.formcolumns[7].disabled = true;
          // this.formcolumns[8].disabled = true;
        } else {
          this.$set(
            this.form,
            "manufacturerGoodsCode",
            this.form.manufacturerGoodsCode || ""
          );
          this.$set(
            this.form,
            "manufacturerGoodsName",
            this.form.manufacturerGoodsName || ""
          );
          // this.formcolumns[7].disabled = false;
          // this.formcolumns[8].disabled = false;
        }
      },
      immediate: true,
      deep: true,
    },
    "form.manufacturerId": {
      handler: function (val) {
        if (val) {
          manufacturerInfoApi(val).then((res) => {
            this.$set(this.form, "manufacturerCode", res.data.code);
          });
        }
      },
      immediate: true,
      deep: true,
    },
    "form.articleType": {
      handler(value) {
        if (value === 1) {
          this.formcolumns.forEach((item, index) => {
            if (item.dataIndex === "articleConfigs") {
              this.formcolumns[index].isForm = true;
            }
            if (item.dataIndex === "numberOem") {
              this.formcolumns[index].prop = [];
            }
            if (item.dataIndex === "manufacturerGoodsCode") {
              this.formcolumns[index].prop = [];
            }
            if (item.dataIndex === "manufacturerGoodsName") {
              this.formcolumns[index].prop = [];
            }
          });
        } else {
          this.formcolumns.forEach((item, index) => {
            if (item.dataIndex === "articleConfigs") {
              this.formcolumns[index].isForm = false;
            }
            if (item.dataIndex === "numberOem") {
              this.formcolumns[index].prop = [
                {
                  required: true,
                  message: "请输入原厂零件编号（OEM）",
                  trigger: "change",
                },
              ];
            }
            if (item.dataIndex === "manufacturerGoodsCode") {
              this.formcolumns[index].prop = [
                {
                  required: true,
                  message: "请输入制造商物品编号",
                  trigger: "change",
                },
              ];
            }
            if (item.dataIndex === "manufacturerGoodsName") {
              this.formcolumns[index].prop = [
                {
                  required: true,
                  message: "请输入制造商物品名称",
                  trigger: "change",
                },
              ];
            }
          });
        }
      },
    },
  },

  created() {},
  mounted() {
    this.$refs.ProTable.refresh();
    dictTreeByCodeApi(2100).then((res) => {
      res.data.forEach((item) => {
        if (item.value === "2101") {
          this.includeList.push(item.value);
          item.children.forEach((i) => {
            this.includeList.push(i.value);
          });
        }
      });
      this.options1 = res.data;
    });
    this.getProductTree();
  },
  methods: {
    handleUploadExcelSuccess() {
      this.$refs.ProTable.refresh();
    },
    handleDownloadTemplate() {
      handleExcelExport(downloadThingTemplateApi, {}, "物品数据导入模板");
    },
    setOem(data) {
      this.$set(this.form, "numberOem", data.oemNumber);
      this.$set(this.form, "partId", data.id);
      this.$set(this.form, "partName", data.ch);
      this.dialogVisibleOem = false;
    },
    load1() {
      productAssocTreeApi(this.form.partId).then((res) => {
        this.deviceProductTree = res.data;
        this.$refs.ProTable1.listLoading = false;
      });
    },
    handleChange1(item) {
      this.form.numberOem = "";
      this.form.partName = "";
      const updateFormColumns = (dataIndices, isForm) => {
        this.formcolumns.forEach((column, index) => {
          if (dataIndices.includes(column.dataIndex)) {
            this.formcolumns[index].isForm = isForm;
          }
        });
      };
      if (!this.includeList.includes(item[0])) {
        updateFormColumns(
          ["numberOem", "partName", "articleType", "articleConfigs"],
          false
        );
      } else {
        updateFormColumns(["numberOem", "partName", "articleType"], true);
        // 物品类型为组合时，显示选择物品操作
        if (this.form.articleType === 1) {
          updateFormColumns(["articleConfigs"], true);
        }
      }
      // 数码印刷
      if (item[0] !== "2123") {
        updateFormColumns(["productId"], false);
      } else {
        updateFormColumns(["productId"], true);
      }
      if (item[0] === "2121") {
        updateFormColumns(
          ["manufacturerGoodsName", "manufacturerGoodsCode"],
          false
        );
      } else {
        updateFormColumns(
          ["manufacturerGoodsName", "manufacturerGoodsCode"],
          true
        );
      }
      this.$set(this.form, "type", item[item.length - 1]);
    },
    handleChangeType(val) {
      this.$set(this.queryParam, "type", val[val.length - 1]);
    },
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      articlePageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },

    // 加载已选物品表格数据
    async loadArticleData(params) {
      try {
        const result = await articleInquiryOrderPageApi({
          ...params,
          unit: this.articleQueryUnit,
        });
        if (result.code === 200) {
          this.chooseArticleData = result.data.rows;
          this.articleLocalPagination = {
            pageNumber: params.pageNumber,
            pageSize: params.pageSize,
            total: +result.data.total,
          };
          // 回显物品表格已选中数据
          // this.chooseArticleData.forEach((row) => {
          //   if (
          //     this.selectedArticleData.some(
          //       (selectedRow) => selectedRow.id === row.id
          //     )
          //   ) {
          //     this.$refs.chooseArticleTable.$refs.ProElTable.toggleRowSelection(
          //       row,
          //       true
          //     );
          //   }
          // });
        }
      } catch (err) {
        Message.error(err.message);
      } finally {
        this.$refs.chooseArticleTable &&
          (this.$refs.chooseArticleTable.listLoading = false);
      }
    },
    // 耗材选择物品
    chooseArticle() {
      this.articleDialog = true;
      // 加载物品数据
      this.$nextTick(() => {
        this.$refs.chooseArticleTable.refresh();
      });
    },
    //多选物品赋值
    handleSelectionArticle(val) {
      // 检查选中的数据是否在form数据中
      if (val.length > 0) {
        val.forEach((item, index) => {
          if (item.id === this.form.id) {
            this.$message.error("不可选中物品本身");
            this.$refs.chooseArticleTable.$refs.ProElTable.clearSelection();
          } else {
            this.articleSelection = val;
          }
        });
      }
    },
    //物品选择框确认
    handleArticleDialogOk() {
      this.selectedArticleData = cloneDeep(this.articleSelection); //物品表格数据
      this.closeArticleDialog();
    },
    // 移除选中物品
    handleDeleteArticle(row) {
      this.selectedArticleData.filter((item) => {
        if (item.id === row.id) {
          this.selectedArticleData.splice(
            this.selectedArticleData.indexOf(item),
            1
          );
        }
      });
    },
    closeArticleDialog() {
      this.articleDialog = false;
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
      this.deviceProductTree = [];
    },
    // 获取产品树数据
    async getProductTree() {
      try {
        const result = await productListApi({ pageNumber: 1, pageSize: 9999 });
        if (result.code === 200 && result.data) {
          this.productTreeOption = result.data;
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleProductTree(item) {
      const result = cloneDeep(item);
      const id = result.length > 0 && result.pop().split("/").pop();
      this.$set(this.form, "productId", id);
    },

    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      if (this.form.articleType !== 1) {
        this.form.articleConfigs = [];
      } else {
        this.form.articleConfigs = [];
        this.selectedArticleData.forEach((item) => {
          this.form.articleConfigs.push({
            articleId: item.id,
            articleCode: item.code,
            articleName: item.name,
            price: item.price,
            num: item.num,
            purchasePrice: item.purchasePrice,
            oemNumber: item.numberOem,
          });
        });
      }
      try {
        // 校验this.form.articleConfigs.price
        if (this.form.articleConfigs.length > 0) {
          this.form.articleConfigs.forEach((item) => {
            if (item.price === undefined || item.price === "") {
              throw new Error("请输入物品价格");
            }
            if (!item.num) {
              throw new Error("请输入物品数量");
            }
          });
        }
        this.confirmLoading = true;
        this.methodType === "add" ? this.create() : this.update();
      } catch (err) {
        this.$message.error(err.message);
      }
    },
    closedDialog() {
      this.dialogVisible = false;
      this.selectedArticleData = [];
    },
    // 图片处理
    handleUploadSuccess(result) {
      this.form.imageFiles.push(result);
    },
    handleUploadRemove(file) {
      const index = this.form.imageFiles.findIndex(
        (val) => val.key === file.key
      );
      this.form.imageFiles.splice(index, 1);
    },
    //触发新增
    handleAdd(data) {
      this.dialogTitle = "新增物品";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;
      this.deviceProductTree = [];
      this.form.imageFiles = [];
      // this.form.articleType = 0; // 默认单品
      this.$set(this.form, "articleType", 0);
      this.selectedArticleData = [];
      this.formcolumns.forEach((item, index) => {
        if (item.dataIndex === "articleConfigs") {
          this.formcolumns[index].isForm = false;
        }
      });

      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应新增
    create() {
      articleAddApi(this.form)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },

    //触发编辑
    handleUpdate(row) {
      const type = [row.type.value];
      this.handleChange1(type);
      this.productIdName = [];
      this.resetFrom();
      // 物品详情
      articleGetDetailApi(row.id).then((res) => {
        this.form = res.data;
        this.methodType = "edit";
        this.productIdName = this.form.fullIdPath;
        this.dialogTitle = "编辑 - " + this.form.name;
        this.form.type = this.form.type?.value;
        this.form.manufacturerChannel = this.form.manufacturerChannel.value;
        this.deviceProductTree = [];
        this.form.imageFiles = this.form.imageFiles || [];
        if (this.form.articleType === 1) {
          this.formcolumns.forEach((item, index) => {
            if (item.dataIndex === "articleConfigs") {
              this.formcolumns[index].isForm = true;
            }
          });
        } else {
          this.formcolumns.forEach((item, index) => {
            if (item.dataIndex === "articleConfigs") {
              this.formcolumns[index].isForm = false;
            }
          });
        }
        this.selectedArticleData =
          this.form.articleConfigs && Array.isArray(this.form.articleConfigs)
            ? this.form.articleConfigs.map((item) => {
                return {
                  code: item.articleCode,
                  name: item.articleName,
                  id: item.articleId,
                  price: item.price,
                  purchasePrice: item.purchasePrice,
                  numberOem: item.oemNumber,
                  num: item.num,
                };
              })
            : [];
        this.$nextTick((e) => {
          // this.$refs.ProTable1.refresh();
          this.$refs["proform"].resetFormParam();
        });
        this.dialogVisible = true;
      });
      // this.form = cloneDeep(row);
    },
    //响应编辑
    update() {
      if (!this.form.packWeight) {
        this.form.packWeight = null;
      }
      if (!this.form.packLong) {
        this.form.packLong = null;
      }
      if (!this.form.packWide) {
        this.form.packWide = null;
      }
      if (!this.form.packHigh) {
        this.form.packHigh = null;
      }
      articleEditApi(this.form)
        .then(() => {
          this.$message.success("修改成功");
          this.$refs.ProTable.refresh();
          this.dialogVisible = false;
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.name;
      this.resetFrom();
      const type = [row.type.value];
      this.handleChange1(type);
      // this.form = cloneDeep(row);
      articleGetDetailApi(row.id).then((res) => {
        this.form = res.data;
        this.methodType = "info";
        this.dialogVisible = true;
        this.form.type = this.form.type?.value;
        this.form.manufacturerChannel = this.form.manufacturerChannel.value;
        this.form.imageFiles = this.form.imageFiles || [];

        if (this.form.articleType === 1) {
          this.formcolumns.forEach((item, index) => {
            if (item.dataIndex === "articleConfigs") {
              this.formcolumns[index].isForm = true;
            }
          });
        } else {
          this.formcolumns.forEach((item, index) => {
            if (item.dataIndex === "articleConfigs") {
              this.formcolumns[index].isForm = false;
            }
          });
        }
        this.selectedArticleData =
          this.form.articleConfigs && Array.isArray(this.form.articleConfigs)
            ? this.form.articleConfigs.map((item) => {
                return {
                  code: item.articleCode,
                  name: item.articleName,
                  id: item.articleId,
                  price: item.price,
                  purchasePrice: item.purchasePrice,
                  numberOem: item.oemNumber,
                  num: item.num,
                };
              })
            : [];
        this.$nextTick((e) => {
          // this.$refs.ProTable1.refresh();
          this.$refs["proform"].resetFormParam();
        });
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        articleDelApi(data.id).then(() => {
          this.$message.success("删除成功");
          this.localPagination = {
            pageNumber: 1,
            pageSize: 10,
            total: 0,
          };
          this.$nextTick(() => {
            this.$refs.ProTable.refresh();
          });
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
