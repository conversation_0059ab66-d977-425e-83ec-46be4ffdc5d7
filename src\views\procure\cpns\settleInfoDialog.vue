<template>
  <!-- 结算信息弹框 -->
  <div v-if="showSettleDialog">
    <ProDialog
      :value="showSettleDialog"
      title="结算信息"
      width="1200px"
      :confirm-loading="confirmLoading"
      top="50px"
      :no-footer="showFooter"
      @ok="handleSettleDialogConfirm"
      @cancel="handleSettleCancel">
      <ProForm
        ref="proform"
        :form-param="form"
        :form-list="formColumns"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        @proSubmit="proSubmit">
        <template #contractFiles>
          <span>/</span>
        </template>
        <template #validityTime>
          <el-date-picker
            v-model="form.validityTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :format="'yyyy-MM-dd HH:mm:ss'"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="changeDate"></el-date-picker>
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import _ from "lodash";
import dayjs from "dayjs";
export default {
  data() {
    return {
      showSettleDialog: false,
      form: {},
      formColumns: [
        {
          dataIndex: "settlementNumberOne",
          isForm: true,
          title: "结算量1",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "settlementPriceOne",
          isForm: true,
          title: "结算价格1",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          attrs: {
            precision: 2,
          },
          prop: [
            {
              required: true,
              message: "请输入",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "settlementNumberTwo",
          isForm: true,
          title: "结算量2",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementPriceTwo",
          isForm: true,
          title: "结算价格2",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          attrs: {
            precision: 2,
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementNumberThree",
          isForm: true,
          title: "结算量3",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementPriceThree",
          isForm: true,
          title: "结算价格3",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          attrs: {
            precision: 2,
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementNumberFour",
          isForm: true,
          title: "结算量4",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementPriceFour",
          isForm: true,
          title: "结算价格4",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          attrs: {
            precision: 2,
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementNumberFive",
          isForm: true,
          title: "结算量5",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "settlementPriceFive",
          isForm: true,
          title: "结算价格5",
          valueType: "input-number",
          clearable: true,
          formSpan: 12,
          attrs: {
            precision: 2,
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "deliveryTime",
          isForm: true,
          title: "发货时间标准",
          clearable: true,
          formSpan: 12,
          valueType: "input-number",
          prop: [
            {
              required: true,
              message: "请输入",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "arrivalTime",
          isForm: true,
          title: "到货时间标准",

          clearable: true,
          formSpan: 12,
          valueType: "input-number",

          prop: [
            {
              required: true,
              message: "请输入",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "validityTime",
          isForm: true,
          title: "有效期",
          clearable: true,
          formSlot: "validityTime",
          formSpan: 12,
          // valueType: "date-picker",
          // pickerType: "datetimerange",
          // pickerFormat: "yyyy-MM-dd HH:mm:ss",
          // valueFormat: "yyyy-MM-dd HH:mm:ss",
          // allowClear: true,
          // attrs: {
          //   "range-separator": "至",
          //   "start-placeholder": "开始日期",
          //   "end-placeholder": "结束日期",
          // },
          prop: [
            {
              required: true,
              message: "请输入",
              trigger: "change",
            },
          ],
        },
        //   {
        //     dataIndex: "validityEndTime",
        //     isForm: true,
        //     title: "有效期截至",
        //     clearable: true,
        //     formSpan: 12,
        //     valueType: "date-picker",
        //     pickerType: "daterange",
        //     pickerFormat: "yyyy-MM-dd",
        //     prop: [
        //       {
        //         required: true,
        //         message: "请输入",
        //         trigger: "change",
        //       },
        //     ],
        //   },
        {
          dataIndex: "contractFiles",
          isForm: true,
          title: "合同附件",
          formSlot: "contractFiles",
          clearable: true,
          formSpan: 24,
        },
      ],
      validityTimeData: [],
      formListData: null,
      confirmLoading: false,
      showFooter: true,
    };
  },
  methods: {
    show(data, type) {
      let args = {};
      if (type === "edit") {
        this.showFooter = false;
        args = {
          settlementNumberOne: 1,
          settlementPriceOne: data.price,
          deliveryTime: 2,
          arrivalTime: 3,
          validityTime: [
            dayjs().format("YYYY-MM-DD HH:mm:ss"),
            dayjs().add(3, "month").format("YYYY-MM-DD HH:mm:ss"),
          ],
        };
      } else {
        this.showFooter = true;
      }
      // this.form = _.cloneDeep({ ...data, ...args });
      this.form = { ...data, ...args };
      if (data.validityStartTime && data.validityEndTime) {
        this.form.validityTime[0] = data.validityStartTime;
        this.form.validityTime[1] = data.validityEndTime;
      }
      console.log(this.form);
      this.showSettleDialog = true;
    },
    handleSettleDialogConfirm() {
      this.$refs.proform.handleSubmit();
    },
    handleSettleCancel() {
      this.form = {};
      // this.$refs.proform.$refs.ProForm.clearValidate();
      this.showSettleDialog = false;
    },
    changeDate(time) {
      console.log(time);
      // this.form.validityTime = time;
      // console.log(this.form.validityTime);
    },

    proSubmit(data) {
      this.showSettleDialog = false;
      this.formListData = data;
      this.formListData.validityStartTime = this.form.validityTime[0];
      this.formListData.validityEndTime = this.form.validityTime[1];
      delete this.formListData.manufacturer;
      delete this.formListData.storageArticle;
      this.$emit("sure", this.formListData);
    },
  },
};
</script>

<style lang="scss" scoped></style>
