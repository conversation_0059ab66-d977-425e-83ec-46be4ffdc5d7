/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-17 09:25:09
 * @Description: 框架相关的一些配置
 */

const setting = {
  title: "管理系统", //浏览器标题
  proName: "四川至简智印业务管理系统", //项目名称
  viewportRem: {
    //页面自适应转换px=>rem的配置 同时配合main.js引入rem.js
    viewportWidth: 1920, //设计稿的视窗宽度
    rootValue: 16, // 换算的基数
    selectorBlackList: ["wu"], // 忽略转换正则匹配项
    propList: ["*"], //能转化为rem的属性列表  ['*', '!font-size']这样就是不转换文字大小
  },
  viewportVw: {
    //页面自适应转换px=>vw的配置 该项目未启用
    viewportWidth: 1920, //设计稿的视窗宽度
    minPixelValue: 5, //设置最小的转换数值，如果为 1 的话，只有大于 1 的值会被转换
    propList: ["*"], //能转化为 vw 的属性列表  ['*', '!font-size']这样就是不转换文字大小
    selectorBlackList: [], //需要忽略的 CSS 选择器，不会转为视窗单位，使用原有的 px 等单位
  },
  cors: true, //为开发服务器配置 CORS。默认启用并允许任何源，传递一个 选项对象 来调整行为或设为 false 表示禁用
  contentType: "application/json;charset=UTF-8", //根据后端定义配置
  otherContentType: "application/x-www-form-urlencoded;charset=UTF-8",
  messageDuration: 3000, //消息框消失时间
  requestTimeout: 10000, //最长请求时间
  successCode: [200], //操作正常code，支持String、Array、int多种类型
  invalidCode: 401, //登录失效code
  noPermissionCode: 4001, //无权限code
  errorCode: 500, //错误数据
  //项目部署的基础路径
  base: "/gdjg/",
  // 静态资源服务的文件夹 类型 string | false
  publicDir: "public",
  // 存储缓存文件的目录
  cacheDir: "node_modules/.vite",
  // 输出路径
  outDir: "dist",
  // 生成静态资源的存放路径
  assetsDir: "static/",
  // 构建后是否生成 source map 文件
  sourcemap: false,
  // chunk 大小警告的限制
  chunkSizeWarningLimit: 2000,
  // 启用/禁用 CSS 代码拆分
  // 压缩大型输出文件可能会很慢，因此禁用该功能可能会提高大型项目的构建性能。
  cssCodeSplit: true,
  // 启用/禁用 brotli 压缩大小报告
  brotliSize: false,
  // 指定服务器应该监听哪个 IP 地址
  host: "0.0.0.0",
  // 指定开发服务器端口
  port: "8089",
  // 设为 true 时若端口已被占用则会直接退出，而不是尝试下一个可用端口
  strictPort: false,
  // 服务器启动时自动在浏览器中打开应用程序 此值为字符串时，会被用作 URL 的路径名
  open: true,
  //是否显示顶部进度条
  progressBar: true,
  // 菜单栏默认打开路由
  defaultOpeneds: ["/comp", "/errorPage", "/chart"],
  // vertical布局时是否只保持一个子菜单的展开
  uniqueOpened: false,
  //token名称
  tokenName: "X-AUTH-TOKEN",
  //是否开启登录拦截
  loginInterception: true,
  //token在localStorage、sessionStorage存储的key的名称
  tokenStorage: "token",
  // lang storage
  langKey: "i18nLang",
  // theme storage
  themeKey: "theme",
  // default language
  lang: "zh-cn",
  //token存储位置localStorage sessionStorage
  storage: "localStorage",
  // 版权信息
  copyright: " ",
  // 是否显示页面底部自定义版权信息
  footerCopyright: true,
  // 缓存路由的最大数量
  keepAliveMaxNum: 99,
  // intelligence 前端控制路由 all 后端控制
  authentication: "intelligence",
  //token失效回退到登录页时是否记录本次的路由
  recordRoute: true,
  // 路由白名单不经过token校验的路由
  routesWhiteList: ["/login", "/register", "/404", "/401", "/index"],
  // 需要加loading层的请求，防止重复提交
  debounce: [],
  // 导入时想要省略的扩展名列表
  extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json"],
  // 调整控制台输出的级别 'info' | 'warn' | 'error' | 'silent'
  logLevel: "info",
  // 设为 false 可以避免 Vite 清屏而错过在终端中打印某些关键信息
  clearScreen: false,
  // 是否删除生产环境console
  drop_console: true,
  // 是否删除生产环境debugger
  drop_debugger: true,
};

module.exports = setting;
