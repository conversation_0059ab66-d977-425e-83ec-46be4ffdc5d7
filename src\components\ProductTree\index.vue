<template>
  <div class="app-container">
    <el-cascader
      ref="ProductIds"
      v-model="productIdName"
      style="width: 100%"
      filterable
      :clearable="clearable"
      :collapse-tags="collapseTags"
      :options="productOptions"
      :props="options"
      :placeholder="placeholder"
      leaf-only
      @change="handleChange"
    ></el-cascader>
  </div>
</template>

<script>
import { productAllApi } from "@/api/dispose";

export default {
  name: "ProductTree",
  props: {
    value: {
      type: [String, Array],
      default: "",
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    collapseTags: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      productIdName: null,
      options: {
        label: "name",
        value: "id",
        children: "children",
        expandTrigger: "click",
        multiple: this.multiple,
      },
      productOptions: [],
    };
  },
  watch: {
    value(val) {
      if (!val) {
        this.productIdName = null;
      }
    },
  },
  mounted() {
    this.getProductOptions();
  },
  methods: {
    getProductOptions() {
      productAllApi().then((res) => {
        this.productOptions = res.data;
      });
    },
    handleChange(val) {
      let result = null;
      if (this.multiple) {
        result = val.map((item) => item[item.length - 1]);
      } else {
        result = val[val.length - 1];
      }
      this.$emit("change", result);
    },
  },
};
</script>

<style scoped lang="scss"></style>
