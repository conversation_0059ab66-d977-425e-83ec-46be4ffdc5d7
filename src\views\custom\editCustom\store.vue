<template>
  <div class="store">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      :show-search="false"
      :show-settings="false"
      @loadData="loadData"
    >
      <template #btn>
        <div class="box">
          <div>抄表开始时间：{{ totalData?.meterDate }}</div>
          <div>总库存金额：{{ totalData?.amount }}</div>
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { customConsumableListApi } from "@/api/statisics";
import { getWarehouseInventoryDetailApi } from "@/api/customer";

export default {
  name: "Store",
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
  },
  data() {
    return {
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "金额",
          isTable: true,
        },
      ],
      tableData: [],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
    getWarehouseInventoryDetailApi(this.id).then((res) => {
      this.totalData = res.data;
    });
  },
  methods: {
    loadData(params) {
      customConsumableListApi({ ...params, customerId: this.id })
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
          console.log(res);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  display: flex;
  gap: 50px;
  font-size: 16px;
  color: #6488cf;
}
</style>
