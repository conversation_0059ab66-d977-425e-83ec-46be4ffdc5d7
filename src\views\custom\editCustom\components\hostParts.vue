<!--
 * @Author: wskg
 * @Date: 2025-01-21 14:25:23
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:49
 * @Description: 主机关联的选配件
 -->
<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :local-pagination="localPagination"
      :show-search="false"
      :columns="columns"
      :data="tableData"
      :show-loading="false"
      :show-setting="false"
      :height="250"
    >
      <template #btn>
        <el-button
          v-if="editType !== 'info'"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleAddMeter(false)"
        >
          添加选配件
        </el-button>
        <el-button
          v-if="editType !== 'info'"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleAddMeter(true)"
        >
          从机器仓库选择
        </el-button>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            v-if="editType !== 'info'"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row.id)"
          >
            移除
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDialog
      v-model="dialogVisible"
      title="关联选配件"
      width="75%"
      top="1%"
      @ok="handleOk"
      @cancel="dialogVisible = false"
    >
      <ProTable
        ref="PartProTable"
        :local-pagination="partLocalPagination"
        :row-key="(row) => row.id"
        :query-param="queryParam"
        :columns="partColumns"
        :data="partTableData"
        :show-selection="true"
        :height="400"
        @loadData="loadPartsData"
        @handleSelectionChange="handlePartSelectionChange"
      ></ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { getMachinePageApi } from "@/api/store";
import { cloneDeep } from "lodash";
import { accessoryListApi } from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "HostParts",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    editType: {
      type: String,
      default: "add",
    },
    machineNum: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "accessoryCode",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "hostType",
          title: "选配件类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
        },
        {
          dataIndex: "modeType",
          title: "配件型号",
          isTable: true,
        },
        {
          dataIndex: "actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 100,
        },
      ],
      // tableData: [],
      // 选择关联选配件
      dialogVisible: false,
      partLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      queryParam: {},
      partColumns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "hostType",
          title: "选配件类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,

          minWidth: 100,
        },
        {
          dataIndex: "productName",
          title: "配件型号",
          isTable: true,
          minWidth: 120,
        },
      ],
      partTableData: [],
      selectedPartData: [],
      isStore: false,
    };
  },
  computed: {
    tableData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    // loadData(parameter) {},
    handleAddMeter(isStore) {
      this.isStore = isStore;
      if (isStore) {
        this.partColumns = [
          {
            dataIndex: "machineNum",
            title: "机器编号",
            isTable: true,
            isSearch: true,
            valueType: "input",
            minWidth: 140,
          },
          {
            dataIndex: "hostType",
            title: "选配件类型",
            isTable: true,
            formatter: (row) => row.hostType?.label,

            minWidth: 100,
          },
          {
            dataIndex: "productName",
            title: "配件型号",
            isTable: true,
            minWidth: 120,
          },
        ];
      } else {
        this.partColumns = [
          {
            dataIndex: "type",
            title: "选配件类型",
            isTable: true,
            formatter: (row) => row.type?.label,
            isSearch: true,
            clearable: true,
            valueType: "select",
            formSpan: 8,
            optionMth: () => dictTreeByCodeApi(2000),
            option: [],
            filterOption: ["2008"],
            optionskey: {
              label: "label",
              value: "value",
            },
          },
          {
            dataIndex: "modeType",
            title: "配件型号",
            isTable: true,
            isSearch: true,
            valueType: "input",
          },
          {
            dataIndex: "machineNumber",
            title: "机器编号",
            isTable: true,
          },
        ];
      }
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.PartProTable.refresh();
      });
    },
    handleOk() {
      const val = cloneDeep(this.selectedPartData);
      const result = val.filter((item) => {
        if (this.tableData.some((j) => item.accessoryId === j.accessoryId)) {
          return false;
        } else {
          return true;
        }
      });
      this.tableData = [...this.tableData, ...result];
      this.dialogVisible = false;
    },
    handlePartSelectionChange(val) {
      if (this.isStore) {
        this.selectedPartData = val.map((item) => {
          return {
            ...item,
            accessoryCode: item.accessoryCode
              ? item.accessoryCode
              : item.machineNum,
            modeType: item.modeType ? item.modeType : item.productName,
            accessoryId: item.accessoryId ? item.accessoryId : item.productId,
          };
        });
      } else {
        this.selectedPartData = val.map((item) => {
          return {
            ...item,
            accessoryId: item.accessoryId ? item.accessoryId : item.id,
            hostType: item.hostType ? item.hostType : item.type,
            accessoryCode: item.accessoryCode ? item.accessoryCode : null,
          };
        });
      }
    },
    loadPartsData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const requestParameters = cloneDeep(this.queryParam);
      requestParameters.isBind = false; // 为绑定
      requestParameters.existType = "2008"; // 排除主机类型
      const editAit = this.isStore ? getMachinePageApi : accessoryListApi;
      editAit(requestParameters)
        .then((res) => {
          this.partTableData = res.data.rows;
          this.partLocalPagination.total = +res.data.total;
          if (this.selectedPartData.length) {
            this.$refs.PartProTable.$refs.ProElTable.clearSelection();
            this.tableData.forEach((row) => {
              this.$refs.PartProTable.$refs.ProElTable.toggleRowSelection(
                row,
                true
              );
            });
          }
        })
        .finally(() => {
          this.$refs.PartProTable &&
            (this.$refs.PartProTable.listLoading = false);
        });
    },
    handleDelete(id) {
      this.$confirm("是否确认移除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.tableData = this.tableData.filter((item) => item.id !== id);
        // this.selectedPartData = this.selectedPartData.filter(
        //   (item) => item.id !== id
        // );
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
