<!--
 * @Author: AI Assistant
 * @Date: 2025-01-29
 * @Description: 紧急操作对话框组件
-->
<template>
  <el-dialog
    :title="actionConfig.title"
    :visible.sync="dialogVisible"
    width="500px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    destroy-on-close
  >
    <!-- 操作说明 -->
    <el-alert
      :title="actionConfig.description"
      :type="actionConfig.alertType"
      :closable="false"
      show-icon
      class="mb-4"
    />
    
    <!-- 版本回退表单 -->
    <el-form 
      v-if="actionType === 'rollback'"
      ref="rollbackForm" 
      :model="formData" 
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="目标版本" prop="targetVersionName">
        <el-select
          v-model="formData.targetVersionName"
          placeholder="请选择要回退到的版本"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="version in availableVersions"
            :key="version.id"
            :label="`${version.versionName} (版本号: ${version.versionCode})`"
            :value="version.versionName"
            :disabled="version.adminForce"
          >
            <div class="version-option">
              <span class="version-name">{{ version.versionName }}</span>
              <span class="version-code">({{ version.versionCode }})</span>
              <el-tag 
                v-if="version.adminForce" 
                type="danger" 
                size="mini"
                class="ml-2"
              >
                当前强制
              </el-tag>
              <el-tag 
                v-else-if="!version.isActive" 
                type="info" 
                size="mini"
                class="ml-2"
              >
                已禁用
              </el-tag>
            </div>
          </el-option>
        </el-select>
        <div class="form-tip">
          选择版本后，该版本将被设为管理员强制更新，其他版本的管理员强制标志将被取消
        </div>
      </el-form-item>
      
      <el-form-item label="回退原因" prop="reason">
        <el-input
          type="textarea"
          v-model="formData.reason"
          :rows="3"
          placeholder="请输入回退原因（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <!-- 暂停/恢复操作确认 -->
    <div v-else class="operation-confirm">
      <div class="confirm-content">
        <i :class="actionConfig.icon" class="confirm-icon"></i>
        <div class="confirm-text">
          <h4>{{ actionConfig.confirmTitle }}</h4>
          <p>{{ actionConfig.confirmMessage }}</p>
        </div>
      </div>
      
      <!-- 影响范围显示 -->
      <div class="impact-info">
        <h5>影响范围：</h5>
        <ul>
          <li v-for="impact in actionConfig.impacts" :key="impact">
            {{ impact }}
          </li>
        </ul>
      </div>
    </div>
    
    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        :type="actionConfig.buttonType"
        :loading="executing"
        @click="handleSubmit"
      >
        {{ executing ? '执行中...' : actionConfig.confirmText }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { emergencyAction } from '@/appupdate/api/appVersion';

export default {
  name: 'EmergencyDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    actionType: {
      type: String,
      default: '',
    },
    versionList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      executing: false,
      
      // 表单数据
      formData: {
        targetVersionName: '',
        reason: '',
      },
      
      // 表单验证规则
      formRules: {
        targetVersionName: [
          { required: true, message: '请选择目标版本', trigger: 'change' },
        ],
      },
      
      // 操作配置
      actionConfigs: {
        rollback: {
          title: '版本回退',
          description: '将指定版本设为管理员强制更新，用户将被强制更新到该版本',
          alertType: 'warning',
          confirmText: '确认回退',
          buttonType: 'danger',
          icon: 'el-icon-refresh-left',
          confirmTitle: '确认版本回退',
          confirmMessage: '此操作将立即生效，请谨慎操作',
          impacts: [
            '目标版本将被设为管理员强制更新',
            '其他版本的管理员强制标志将被取消',
            '所有用户将被强制更新到目标版本',
          ],
        },
        pause: {
          title: '暂停所有更新',
          description: '暂停所有版本的更新推送，用户将不会收到任何更新通知',
          alertType: 'warning',
          confirmText: '确认暂停',
          buttonType: 'warning',
          icon: 'el-icon-video-pause',
          confirmTitle: '确认暂停更新',
          confirmMessage: '暂停后，所有用户都无法获取到应用更新',
          impacts: [
            '所有版本将被禁用',
            '用户无法获取到任何更新',
            '管理员强制更新标志将被清除',
          ],
        },
        resume: {
          title: '恢复更新推送',
          description: '恢复正常的版本更新推送，用户可以正常获取应用更新',
          alertType: 'success',
          confirmText: '确认恢复',
          buttonType: 'success',
          icon: 'el-icon-video-play',
          confirmTitle: '确认恢复更新',
          confirmMessage: '恢复后，用户可以正常获取应用更新',
          impacts: [
            '所有版本将被启用',
            '用户可以正常获取更新',
            '管理员强制更新标志将被清除',
          ],
        },
      },
    };
  },
  computed: {
    actionConfig() {
      return this.actionConfigs[this.actionType] || {};
    },
    
    availableVersions() {
      // 对于回退操作，显示所有版本，但标记当前强制的版本
      return this.versionList
        .filter(v => v.versionName && v.versionCode)
        .sort((a, b) => b.versionCode - a.versionCode);
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.resetForm();
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    },
  },
  methods: {
    /**
     * 提交操作
     */
    async handleSubmit() {
      try {
        // 版本回退需要表单验证
        if (this.actionType === 'rollback') {
          const valid = await this.$refs.rollbackForm.validate();
          if (!valid) return;
        }
        
        // 最终确认
        const confirmMessage = this.getConfirmMessage();
        await this.$confirm(confirmMessage, '最终确认', {
          confirmButtonText: '确定执行',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
        });
        
        this.executing = true;
        
        // 构建操作参数
        const params = this.buildActionParams();
        
        // 执行紧急操作
        await emergencyAction(this.actionType, params);
        
        // 成功消息
        const successMessage = this.getSuccessMessage();
        this.$emit('success', successMessage);
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('紧急操作执行失败:', error);
          this.$message.error('操作执行失败，请重试');
        }
      } finally {
        this.executing = false;
      }
    },

    /**
     * 构建操作参数
     */
    buildActionParams() {
      switch (this.actionType) {
        case 'rollback':
          return {
            targetVersion: this.formData.targetVersionName,
            reason: this.formData.reason,
          };
        case 'pause':
        case 'resume':
        default:
          return {};
      }
    },

    /**
     * 获取确认消息
     */
    getConfirmMessage() {
      switch (this.actionType) {
        case 'rollback':
          return `确认要回退到版本 <strong>${this.formData.targetVersionName}</strong> 吗？<br/>
                  此操作将立即生效，所有用户将被强制更新到该版本。`;
        case 'pause':
          return '确认要暂停所有应用更新吗？<br/>暂停后，用户将无法获取到任何更新。';
        case 'resume':
          return '确认要恢复应用更新推送吗？<br/>恢复后，用户可以正常获取应用更新。';
        default:
          return '确认要执行此操作吗？';
      }
    },

    /**
     * 获取成功消息
     */
    getSuccessMessage() {
      switch (this.actionType) {
        case 'rollback':
          return `已成功回退到版本 ${this.formData.targetVersionName}`;
        case 'pause':
          return '所有应用更新已暂停';
        case 'resume':
          return '应用更新推送已恢复';
        default:
          return '操作执行成功';
      }
    },

    /**
     * 关闭对话框
     */
    handleClose() {
      if (this.executing) {
        this.$message.warning('正在执行操作，请稍候...');
        return;
      }
      
      this.dialogVisible = false;
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.formData = {
        targetVersionName: '',
        reason: '',
      };
      
      this.$nextTick(() => {
        this.$refs.rollbackForm?.resetFields();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.version-option {
  display: flex;
  align-items: center;
  
  .version-name {
    font-weight: 500;
  }
  
  .version-code {
    color: #909399;
    margin-left: 4px;
  }
}

.operation-confirm {
  .confirm-content {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .confirm-icon {
      font-size: 32px;
      color: #e6a23c;
      margin-right: 16px;
      margin-top: 4px;
    }
    
    .confirm-text {
      flex: 1;
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 16px;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #606266;
        line-height: 1.5;
      }
    }
  }
  
  .impact-info {
    background-color: #f5f7fa;
    padding: 16px;
    border-radius: 4px;
    border-left: 4px solid #e6a23c;
    
    h5 {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: #303133;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        color: #606266;
        line-height: 1.6;
        margin-bottom: 4px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 不同操作类型的图标颜色
.operation-confirm {
  &.rollback .confirm-icon {
    color: #f56c6c;
  }
  
  &.pause .confirm-icon {
    color: #e6a23c;
  }
  
  &.resume .confirm-icon {
    color: #67c23a;
  }
}
</style>
