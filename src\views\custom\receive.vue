<!--
 * @Author: wskg
 * @Date: 2024-08-28 14:10:40
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:21
 * @Description: 领料查询
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      sticky
      :columns="columns"
      :data="tableData"
      :height="550"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>总数量：{{ totalData?.itemNum || 0 }}</div>
          <div>总金额：{{ totalData?.payAmount || 0 }}</div>
        </div>
      </template>
    </ProTable>
  </div>
</template>
<script>
import { receiveDetailSummaryApi, receiveListApi } from "@/api/statisics";
import { cloneDeep } from "lodash";
import { filterParam } from "@/utils";

export default {
  name: "ReceiveMange",
  data() {
    return {
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      queryParam: {},
      columns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编码",
          width: 140,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          valueType: "input",
          isSearch: true,
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "orderNum",
          title: "领料单号",
          minWidth: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "buyerName",
          title: "领料人",
          valueType: "input",
          isSearch: false,
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "itemCode",
          title: "商品编码",
          minWidth: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "code",
          title: "物品编码",
          minWidth: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "name",
          title: "物品名称",
          minWidth: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编码",
          valueType: "input",
          isSearch: true,
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel.label,
        },
        {
          dataIndex: "itemNum",
          title: "数量",
          valueType: "input",
          isTable: true,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "商品单价",
          isTable: true,
        },
        // {
        //   dataIndex: "actuUnitPrice",
        //   title: "成交价（元）",
        //   isTable: true,
        // },
        {
          dataIndex: "payAmount",
          title: "金额",
          isTable: true,
        },
        {
          dataIndex: "createdAt",
          title: "领料时间",
          isTable: true,
          span: 4,
          width: 150,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
        },
      ],
      totalData: {},
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      this.queryParam.startDate = this.queryParam.createdAt
        ? this.queryParam.createdAt[0]
        : null;
      this.queryParam.endDate = this.queryParam.createdAt
        ? this.queryParam.createdAt[1]
        : null;
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      receiveListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
      this.getTotalData(requestParameters);
    },
    getTotalData(params) {
      receiveDetailSummaryApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
