<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-10 10:12:18
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-25 16:09:17
 * @Description: 耗材预付款明细
 -->
<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:finance:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>预付总金额：{{ totalData?.amount || 0 }}</div>
          <div>不含税金额：{{ totalData?.noTaxAmount || 0 }}</div>
          <div>税额：{{ totalData?.taxAmount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { handleExcelExport } from "@/utils/exportExcel";
import {
  prepayConsumableSummaryListApi,
  payableBaseSummaryStatisticsApi,
  prepayConsumableSummaryExportApi,
} from "@/api/finance";

export default {
  name: "PayConsumables",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "付款单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        // {
        //   dataIndex: "type",
        //   title: "类型",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [
        //     {
        //       label: "采购入库",
        //       value: 1,
        //     },
        //     {
        //       label: "采购退库",
        //       value: 0,
        //     },
        //   ],
        //   minWidth: 100,
        // },
        {
          dataIndex: "manufacturerCode",
          title: "供应商编号",
          isSearch: true,
          isTable: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 200,
        },

        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "articleCode",
          title: "物品编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
          minWidth: 100,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "amount",
          title: "采购金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "noTaxAmount",
          title: "不含税金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "taxAmount",
          title: "税额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "tax",
          title: "税率",
          isTable: true,
          formatter: (row) => (row.tax ? row.tax + "%" : row.tax),
          minWidth: 100,
        },
      ],
      tableData: [],
      totalData: {},
      requestParameters: {},
      statLoading: false,
      exportLoading: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      this.requestParameters = requestParameters;
      prepayConsumableSummaryListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      payableBaseSummaryStatisticsApi({
        ...this.requestParameters,
        paymentType: 4,
      })
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    handleExport() {
      this.$confirm("此操作将导出耗材预付款明细数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          prepayConsumableSummaryExportApi,
          this.requestParameters,
          "耗材预付款明细数据",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
