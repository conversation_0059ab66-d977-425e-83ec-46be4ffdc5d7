<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-29 12:02:17
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 14:03:54
 * @Description: 临时采购
 -->

<template>
  <ProDrawer
    :value="visible"
    title="新增临时采购"
    size="85%"
    :confirm-text="'确认采购'"
    :confirm-button-disabled="confirmLoading"
    @ok="handleSubmit"
    @cancel="closeDrawer"
  >
    <ProForm
      ref="ProForm"
      :form-param="addForm"
      :form-list="columns"
      :confirm-loading="formLoading"
      :layout="{ formWidth: '100%', labelWidth: '120px' }"
      :open-type="editType"
      @proSubmit="formSubmit"
    >
      <template #manufacturerId>
        <div v-if="!addForm.manufacturerId">
          <el-button type="text" @click="handleChooseSupplier">
            选择供应商
          </el-button>
        </div>
        <div v-else>
          <el-button type="text" @click="handleChooseSupplier">
            {{ addForm?.manufacturerName }}
          </el-button>
        </div>
      </template>
    </ProForm>
    <ProTable
      :columns="tableColumn"
      :data="tableData"
      :show-search="false"
      :show-setting="false"
      :show-loading="false"
      height="64vh"
      :show-pagination="false"
    >
      <template #btn>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-plus"
          @click="handleChooseSupply()"
        >
          选择物品
        </el-button>
      </template>
      <template #price="{ row }">
        <el-input
          v-model="row.price"
          type="number"
          placeholder="请输入价格"
        ></el-input>
      </template>
      <template #machine="{ row }">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in row.adapterDevice"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-button slot="reference" type="text" size="mini">
            适用机型
          </el-button>
        </el-popover>
      </template>
      <template #num="{ row }">
        <el-input
          v-model.number="row.planNum"
          placeholder="请输入采购数量"
        ></el-input>
      </template>
      <template #amount="{ row }">
        {{ row | amount }}
      </template>
      <template #action="{ row, index }">
        <div class="fixed-width">
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="deleteRow(row, index)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <div>
      <el-form
        ref="bottomFormRef"
        inline
        :model="addForm"
        :rules="rules"
        label-width="130px"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="期望发货时间：" prop="deliveryTime">
              <el-date-picker
                v-model="addForm.deliveryTime"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择期望发货时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="期望到货时间：" prop="arrivalTime">
              <el-date-picker
                v-model="addForm.arrivalTime"
                type="date"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择期望到货时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物品采购总金额：">
              {{ tableData | totalAmount }} 元
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 选择供应商弹窗 -->
    <ProDialog
      :value="showSupplierDialog"
      title="选择供应商"
      width="90%"
      :confirm-loading="false"
      top="10px"
      :confirm-text="'确认选择'"
      no-footer
      @cancel="showSupplierDialog = false"
    >
      <ProTable
        ref="supplierTable"
        :query-param="supplierParam"
        :local-pagination="supplierLocal"
        :columns="supplierColumns"
        :data="supplierTableData"
        :show-selection="true"
        :height="400"
        @loadData="loadSupplierData"
        @handleSelected="selectSupplier"
      >
        <template #keyword1>
          <el-cascader
            style="width: 100%"
            :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
            leaf-only
            clearable
            filterable
            @change="handleReginChange"
          ></el-cascader>
        </template>
      </ProTable>
    </ProDialog>
    <!-- 选择物品弹窗 -->
    <ProDialog
      :value="showChooseDialog"
      title="选择采购物品"
      width="90%"
      :confirm-loading="false"
      top="10px"
      :no-footer="false"
      @ok="handleDialogConfirm"
      @cancel="showChooseDialog = false"
    >
      <ProTable
        ref="ProTable"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="prodColumns"
        show-pagination
        row-key="id"
        :local-pagination="localPagination"
        :data="prodTableData"
        :show-selection="true"
        :reserve-selection="true"
        sticky
        :query-param="queryParam"
        @handleSelectionChange="tableSelect"
        @loadData="loadData"
      >
        <template #type="slotProps">
          {{ slotProps.row.type.label }}
        </template>
        <template #img="slotProps">
          <el-image
            v-if="
              slotProps.row.imageFiles && slotProps.row.imageFiles.length !== 0
            "
            style="width: 100px; height: 100px"
            :src="slotProps.row?.imageFiles?.[0].url"
            :preview-src-list="[slotProps.row?.imageFiles?.[0].url]"
          ></el-image>
          <div v-else>暂无</div>
        </template>
        <template #searchType>
          <el-cascader
            ref="ProductIds"
            v-model="queryParam.type"
            filterable
            :options="options1"
            style="width: 100%"
            :props="{
              label: 'label',
              value: 'value',
              children: 'children',
              expandTrigger: 'click',
            }"
            clearable
            leaf-only
            @change="handleChangeType"
          ></el-cascader>
        </template>
      </ProTable>
    </ProDialog>
  </ProDrawer>
</template>
<script>
import { articlePageApi, warehouseListApi } from "@/api/store";
import { addPurchaseApi } from "@/api/procure";
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
import { manufacturerListApi } from "@/api/manufacturer";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";
export default {
  name: "TemporaryOrder",
  components: {},
  filters: {
    amount(row) {
      if (row.price && row.planNum) {
        return (row.price * row.planNum).toFixed(2);
      } else {
        return 0;
      }
    },
    totalAmount(data) {
      let total = 0;
      if (data.length > 0) {
        data.forEach((item) => {
          total += Number(item.price * item.planNum);
        });
        return total.toFixed(2);
      } else {
        return 0;
      }
    },
  },
  props: {},
  data() {
    return {
      visible: false,
      addForm: {
        deliveryTime: "",
        arrivalTime: "",
        price: 0,
        warehouseId: "",
        purchaseCode: "",
        manufacturerId: "",
        manufacturerName: "",
      },
      tableData: [],
      editType: "add",
      formLoading: false,
      rules: {
        deliveryTime: [
          { required: true, message: "请选择期望发货时间", trigger: "blur" },
        ],
        arrivalTime: [
          { required: true, message: "请选择期望到货时间", trigger: "blur" },
        ],
      },
      columns: [
        {
          dataIndex: "warehouseId",
          title: "选择仓库",
          isForm: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择仓库",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "manufacturerId",
          title: "供应商名称",
          formSpan: 8,
          isForm: true,
          formSlot: "manufacturerId",
        },
      ],
      tableColumn: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "articleName",
          isSearch: true,
          clearable: true,
          title: "物品名称",
          valueType: "input",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          minWidth: 150,
          isTable: true,
        },
        {
          dataIndex: "machine",
          isTable: true,
          title: "适用机型",
          valueType: "input",
          width: 120,
          tableSlot: "machine",
        },
        {
          dataIndex: "warehouseNumber",
          title: "库存量",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          tableSlot: "price",
          minWidth: 150,
        },
        {
          dataIndex: "planNum",
          title: "计划采购数量",
          isTable: true,
          width: 150,
          tableSlot: "num",
          // formatter: (row) => row.number,
        },
        {
          dataIndex: "purchasePrice",
          title: "采购金额",
          isTable: true,
          tableSlot: "amount",
          // formatter: (row) => mulAmount(row.price, row.number),
        },
        {
          dataIndex: "action",
          title: "操作",
          fixed: "right",
          width: 180,
          align: "left",
          isTable: true,
          tableSlot: "action",
        },
      ],
      showChooseDialog: false,
      prodColumns: [
        {
          dataIndex: "img",
          title: "物品图片",
          isTable: true,
          tableSlot: "img",
          width: 150,
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          minWidth: 150,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          minWidth: 150,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.manufacturerChannel.label,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },
        {
          dataIndex: "partName",
          title: "零件中文名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerName",
          title: "制造商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "partBrand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "type",
          title: "物品大小类",
          isSearch: true,
          valueType: "select",
          searchSlot: "searchType",
        },
      ],
      prodTableData: [],
      queryParam: {
        aduitState: null,
        name: null,
      },
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      options1: [],
      selectionData: [],
      showSupplierDialog: false,
      supplierParam: {},
      supplierLocal: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      supplierColumns: [
        {
          dataIndex: "code",
          title: "制造商编号",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "name",
          title: "制造商简称",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
          width: 240,
        },
        {
          dataIndex: "groupName",
          title: "所属集团",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "keyword1",
          title: "所属省市区",
          isSearch: true,
          searchSlot: "keyword1",
          span: 16,
        },
        {
          dataIndex: "province",
          title: "省份",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市区",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区县",
          isTable: true,
        },
        {
          dataIndex: "address",
          title: "发货地址",
          isTable: true,
          width: 350,
        },
        {
          dataIndex: "legalPerson",
          title: "联系人",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "legalPersonTel",
          title: "电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "settleMethod",
          title: "结算方式",
          isTable: true,
          formatter: (row) => row.settleMethod?.label,
          width: 120,
        },
      ],
      supplierTableData: [],
      confirmLoading: false,
    };
  },
  computed: {},
  created() {},
  mounted() {
    dictTreeByCodeApi(2100).then((res) => {
      this.options1 = res.data;
    });
  },
  methods: {
    show() {
      this.addForm = {
        deliveryTime: "",
        arrivalTime: "",
        price: 0,
        warehouseId: "",
        purchaseCode: "",
        manufacturerId: "",
      };
      this.tableData = [];
      this.visible = true;
    },
    // 供应商选择确认
    handleChooseSupplier() {
      this.supplierParam = {};
      this.showSupplierDialog = true;
      this.$nextTick(() => {
        this.$refs.supplierTable.refresh();
      });
    },
    selectSupplier(selectedRows, row) {
      this.addForm.manufacturerId = row.id;
      this.$set(this.addForm, "manufacturerName", row.name);
      this.$refs.supplierTable.setSelection([row.id]);
      this.showSupplierDialog = false;
    },
    loadSupplierData(parameter) {
      this.supplierParam = filterParam(
        Object.assign({}, this.supplierParam, parameter)
      );
      const requestParameters = cloneDeep(this.supplierParam);
      manufacturerListApi(requestParameters)
        .then((res) => {
          this.supplierTableData = res.data.rows;
          this.supplierLocal.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.supplierTable
            ? (this.$refs.supplierTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    handleSubmit() {
      this.$refs.ProForm.handleSubmit();
    },
    closeDrawer() {
      this.visible = false;
    },
    handleChangeType(val) {
      this.$set(this.queryParam, "type", val[val.length - 1]);
    },
    totalPrice(data) {
      let total = 0;
      if (data.length > 0) {
        data.forEach((item) => {
          total += Number(item.price * item.planNum);
        });
        return total.toFixed(2);
      } else {
        return 0;
      }
    },
    formSubmit() {
      this.$refs.bottomFormRef.validate((valid) => {
        if (!valid) return;
        this.formSubmitData();
      });
    },
    async formSubmitData() {
      if (!this.tableData.length) {
        this.$message.error("请选择采购物品");
        return;
      }
      if (!this.addForm.manufacturerId) {
        this.$message.error("请选择供应商");
        return;
      }
      const userInfo = JSON.parse(localStorage.getItem("userInfo"));
      this.tableData.forEach((item) => {
        if (!item.planNum) {
          this.$message.error("请输入采购数量");
          throw new Error("请输入采购数量");
        }
        if (!item.price) {
          this.$message.error("请输入单价");
          throw new Error("请输入单价");
        }
      });
      this.$confirm("是否确认提交采购单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          this.confirmLoading = true;
          const args = {
            ...this.addForm,
            purchaseGoods: this.tableData,
            initiatorId: userInfo.id,
            initiatorName: userInfo.name,
            purchaseType: "TEMPORARY",
            price: this.totalPrice(this.tableData),
          };
          const result = await addPurchaseApi(args);
          if (result.code === 200) {
            this.$message.success("添加成功");
            this.closeDrawer();
            this.$emit("refresh");
          }
        } finally {
          this.confirmLoading = false;
        }
      });
    },
    deleteRow(row, index) {
      console.log(index);
      this.tableData.splice(index, 1);
    },
    supplyRefresh() {
      this.$refs.ProTable.refresh();
    },
    handleChooseSupply() {
      this.showChooseDialog = true;
      this.localPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.queryParam = {};
      this.$nextTick(() => {
        this.supplyRefresh();
      });
    },
    async handleDialogConfirm() {
      this.showChooseDialog = false;
      if (this.addForm.price === undefined) {
        this.addForm.price = 0;
      }
      const addData = this.addTableData();
      this.tableData = [...this.tableData, ...addData];
    },
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      articlePageApi(requestParameters)
        .then((res) => {
          this.prodTableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    tableSelect(data) {
      this.selectionData = data;
    },
    addTableData() {
      this.tableData.forEach((row) => {
        const selectIndex = this.selectionData.findIndex(
          (item) => item.code === row.articleCode
        );
        if (selectIndex > -1) {
          this.selectionData.splice(selectIndex, 1);
        }
      });
      return this.selectionData.map((o) => {
        return {
          articleId: o.id,
          manufacturerId: o.manufacturerId,
          manufacturerName: o.manufacturerName,
          articleCode: o.code,
          articleName: o.name,
          oemNumber: o.numberOem,
          adapterDevice: o.adapterDevice, //适用机型
          warehouseNumber: o.inventoryNum, //库存量
          price: "",
          planNum: "",
        };
      });
    },
    /**
     * @description 获取省市区区域数据
     * @param node
     * @param {Function} resolve
     * @returns {Promise<void>}
     */
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 处理省市区数据
     * @param list
     * @returns {*}
     */
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleReginChange(val) {
      this.supplierParam["regionPath"] = val[val.length - 1];
    },
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-input) {
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type="number"] {
    -moz-appearance: textfield;
  }
}
</style>
