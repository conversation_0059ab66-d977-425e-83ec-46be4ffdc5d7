<template>
  <div class="website-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div>
          <h1 class="page-title">仪表板</h1>
          <p class="page-description">欢迎回来，管理员！以下是系统概览信息。</p>
        </div>
        <div class="header-actions">
          <el-button 
            type="default" 
            icon="el-icon-refresh" 
            @click="refreshDashboard"
            :loading="loading"
          >
            刷新全部
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <!-- 错误状态显示 -->
      <el-col :span="24" v-if="statisticsError">
        <ErrorFallback
          :error="statisticsError"
          title="统计数据加载失败"
          description="无法加载系统统计数据，请重试"
          :show-reload="true"
          :reset-error="refetchStatistics"
          class="mb-4"
        />
      </el-col>

      <!-- 统计卡片 -->
      <el-col
        :xs="12" :sm="6" :md="6" :lg="6" :xl="6"
        v-for="stat in statistics"
        :key="stat.key"
        v-loading="loading"
      >
        <el-card class="stat-card" :class="{ 'stat-card-loading': loading }">
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color + '20', color: stat.color }">
              <i :class="stat.icon"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">
                <span v-if="!loading">{{ formatNumber(stat.value) }}</span>
                <span v-else class="stat-skeleton">--</span>
                <span class="stat-unit">{{ stat.unit }}</span>
              </div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-trend" v-if="stat.trend">
                <i :class="stat.trend > 0 ? 'el-icon-top' : 'el-icon-bottom'"
                   :style="{ color: stat.trend > 0 ? '#52c41a' : '#ff4d4f' }"></i>
                <span :style="{ color: stat.trend > 0 ? '#52c41a' : '#ff4d4f' }">
                  {{ Math.abs(stat.trend) }}%
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="16" class="content-row">
      <!-- 快捷操作 -->
      <el-col :xs="24" :lg="8">
        <el-card title="快捷操作" class="quick-actions-card">
          <div class="quick-actions">
            <div
              v-for="action in quickActions"
              :key="action.key"
              class="quick-action-item"
              @click="navigateToTab(action.tab)"
            >
              <div
                class="action-icon"
                :style="{ backgroundColor: action.color + '20', color: action.color }"
              >
                <i :class="action.icon"></i>
              </div>
              <div class="action-info">
                <h3 class="action-title">{{ action.title }}</h3>
                <p class="action-description">{{ action.description }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 咨询状态分布图表 -->
      <el-col :xs="24" :lg="8">
        <el-card title="咨询状态分布" class="chart-card">
          <div class="chart-container">
            <ServiceDistributionChart
              :data="inquiryStatusData"
              :loading="loading"
              :error="statisticsError ? '数据加载失败' : ''"
              title="咨询状态分布"
            />
          </div>
        </el-card>
      </el-col>

      <!-- 咨询类型分布图表 -->
      <el-col :xs="24" :lg="8">
        <el-card title="咨询类型分布" class="chart-card">
          <div class="chart-container">
            <ServiceDistributionChart
              :data="serviceDistribution"
              :loading="loading"
              :error="serviceDistributionError ? '数据加载失败' : ''"
              title=""
            />
          </div>
        </el-card>
      </el-col>
    </el-row>



    <!-- 待处理咨询表格 -->
    <el-row :gutter="16">
      <el-col :span="24">
        <el-card class="inquiry-table-card" body-style="padding: 0;">
          <div slot="header" class="card-header">
            <span class="card-title">待处理咨询详情</span>
            <el-button type="text" size="small" @click="navigateToTab('inquiries')">
              查看全部
            </el-button>
          </div>
          
          <!-- 错误状态 -->
          <ErrorFallback
            v-if="inquiriesError"
            :error="inquiriesError"
            title="咨询数据加载失败"
            description="无法加载咨询数据，请重试"
            :show-reload="true"
            :reset-error="refetchInquiries"
          />

          <!-- 表格内容 -->
          <div v-else class="table-container">
            <el-table
              :data="paginatedInquiries"
              v-loading="loading"
              stripe
              size="small"
              style="width: 100%"
            >
              <el-table-column type="index" label="序号" width="50" :index="getRowIndex" />
              <el-table-column prop="company" label="店铺名称" width="160">
                <template #default="{ row }">
                  {{ row.company || '未填写' }}
                </template>
              </el-table-column>
              <el-table-column prop="name" label="客户姓名" width="90" />
              <el-table-column prop="phone" label="联系电话" width="120" />
              <el-table-column prop="subject" label="咨询类型" width="160">
                <template #default="{ row }">
                  <el-tag type="info" size="small">{{ row.subject || '其它' }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="content" label="咨询内容" min-width="200" show-overflow-tooltip>
                <template #default="{ row }">
                  <div class="content-cell">
                    {{ row.content }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)" size="small">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="提交时间" width="140">
                <template #default="{ row }">
                  {{ formatDate(row.createdAt) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="160" fixed="right">
                <template #default="{ row }">
                  <el-button size="mini" @click="viewInquiry(row)">查看</el-button>
                  <el-button size="mini" type="primary" @click="replyInquiry(row)">回复</el-button>
                </template>
              </el-table-column>

              <!-- 空状态 -->
              <template #empty>
                <EmptyState
                  title="暂无待处理咨询"
                  description="目前没有需要处理的客户咨询"
                  icon="el-icon-message"
                  class="py-4"
                />
              </template>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container" v-if="pendingInquiriesTotal > 0">
              <el-pagination
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="pageSize"
                :total="pendingInquiriesTotal"
                layout="total, prev, pager, next"
                small
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查看咨询详情对话框 -->
    <el-dialog
      title="咨询详情"
      :visible.sync="detailDialogVisible"
      width="700px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal-append-to-body="true"
      custom-class="inquiry-detail-dialog"
    >
      <div v-if="selectedInquiry" class="inquiry-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">{{ selectedInquiry.name }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ selectedInquiry.phone }}</el-descriptions-item>
          <el-descriptions-item label="服务类型">{{ selectedInquiry.subject || '无主题' }}</el-descriptions-item>
          <el-descriptions-item label="状态" :span="2">
            <el-tag :type="getStatusType(selectedInquiry.status)">
              {{ getStatusText(selectedInquiry.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="咨询内容" :span="2">
            <div class="content-text">{{ selectedInquiry.content || '-' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="咨询时间">{{ formatDate(selectedInquiry.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间" :span="2">
            {{ selectedInquiry.updatedAt ? formatDate(selectedInquiry.updatedAt) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="处理内容" :span="2">
            <div class="content-text reply-content-bg">
              {{ selectedInquiry.replyContent || '-' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="操作人" :span="2">
            {{ selectedInquiry.repliedBy && selectedInquiry.repliedBy.name ? `${selectedInquiry.repliedBy.code || ''} / ${selectedInquiry.repliedBy.name}` : '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          type="primary"
          @click="replyInquiry(selectedInquiry)"
          v-if="selectedInquiry && selectedInquiry.status !== 'COMPLETED' && selectedInquiry.status !== 'CLOSED'"
        >
          处理
        </el-button>
      </template>
    </el-dialog>

    <!-- 回复咨询对话框 -->
    <el-dialog
      title="处理咨询"
      :visible.sync="replyDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :append-to-body="true"
      :modal-append-to-body="true"
      custom-class="inquiry-reply-dialog"
    >
      <el-form ref="replyForm" :model="replyForm" :rules="replyRules" label-width="100px">
        <el-form-item label="处理状态" prop="status">
          <el-select v-model="replyForm.status" placeholder="请选择处理状态">
            <el-option label="已处理" value="COMPLETED"></el-option>
            <el-option label="已关闭" value="CLOSED"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="处理内容" prop="replyContent">
          <el-input
            type="textarea"
            v-model="replyForm.replyContent"
            :rows="4"
            placeholder="请输入处理内容..."
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="replyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitReply" :loading="submitting">确认处理</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { websiteDashboardApi, websiteInquiryApi, getInquiryPage } from '@/websites/api/website'
import { ServiceDistributionChart } from '@/websites/components/charts'
import { ErrorFallback, EmptyState } from '@/websites/components/ui'

export default {
  name: 'WebsiteDashboard',
  components: {
    ServiceDistributionChart,
    ErrorFallback,
    EmptyState
  },
  data() {
    return {
      loading: false,
      submitting: false,
      detailDialogVisible: false,
      replyDialogVisible: false,
      selectedInquiry: null,
      refreshTimer: null,
      autoRefreshTimer: null,
      resizeTimer: null,

      // 错误状态
      statisticsError: null,
      inquiriesError: null,
      serviceDistributionError: null,

      // 分页相关
      currentPage: 1,
      pageSize: 10, // 每页显示10条，确保最多显示10条
      
      // 统计数据
      dashboardStats: {
        contentCount: 0,
        imageCount: 0,
        inquiryCount: 0,
        pendingInquiryCount: 0,
        processingInquiryCount: 0,
        completedInquiryCount: 0
      },
      
      // 待处理咨询
      pendingInquiriesData: [],
      pendingInquiriesTotal: 0,
      
      // 服务分布数据
      serviceDistribution: [],
      
      // 回复表单
      replyForm: {
        status: '',
        replyContent: ''
      },
      
      // 表单验证规则
      replyRules: {
        status: [
          { required: true, message: '请选择处理状态', trigger: 'change' }
        ],
        replyContent: [
          { required: true, message: '请输入处理内容', trigger: 'blur' }
        ]
      }
    }
  },
  
  computed: {
    // 统计卡片数据
    statistics() {
      return [
        {
          key: 'content',
          label: '内容数量',
          value: this.dashboardStats.contentCount || 0,
          unit: '篇',
          icon: 'el-icon-document',
          color: '#1890ff',
          trend: this.dashboardStats.contentTrend || 0
        },
        {
          key: 'image',
          label: '图片数量',
          value: this.dashboardStats.imageCount || 0,
          unit: '张',
          icon: 'el-icon-picture',
          color: '#722ed1',
          trend: this.dashboardStats.imageTrend || 0
        },
        {
          key: 'inquiry',
          label: '咨询总数',
          value: this.dashboardStats.inquiryCount || 0,
          unit: '条',
          icon: 'el-icon-message',
          color: '#eb2f96',
          trend: this.dashboardStats.inquiryTrend || 0
        },
        {
          key: 'pending',
          label: '待处理咨询',
          value: this.dashboardStats.pendingInquiryCount || 0,
          unit: '条',
          icon: 'el-icon-warning',
          color: '#fa8c16',
          trend: this.dashboardStats.pendingTrend || 0
        }
      ]
    },
    
    // 快捷操作
    quickActions() {
      return [
        {
          key: 'content',
          title: '内容编辑',
          description: '可视化编辑网站页面内容',
          icon: 'el-icon-edit',
          color: '#1890ff',
          tab: 'content'
        },
        {
          key: 'config',
          title: '公共配置',
          description: '配置网站基本设置',
          icon: 'el-icon-setting',
          color: '#52c41a',
          tab: 'config'
        },
        {
          key: 'images',
          title: '图片管理',
          description: '上传和管理图片资源',
          icon: 'el-icon-picture',
          color: '#722ed1',
          tab: 'images'
        },
        {
          key: 'inquiries',
          title: '咨询管理',
          description: '处理客户咨询和反馈',
          icon: 'el-icon-message',
          color: '#eb2f96',
          tab: 'inquiries'
        }
      ]
    },

    // 咨询状态分布数据（与React版本保持一致，只显示待处理和已完成）
    inquiryStatusData() {
      const pendingCount = Number(this.dashboardStats.pendingInquiryCount) || 0
      const completedCount = Number(this.dashboardStats.completedInquiryCount) || 0
      const totalCount = pendingCount + completedCount

      const data = [
        {
          serviceType: '待处理',
          count: pendingCount,
          percentage: totalCount > 0 ? Number(((pendingCount / totalCount) * 100).toFixed(1)) : 0
        },
        {
          serviceType: '已完成',
          count: completedCount,
          percentage: totalCount > 0 ? Number(((completedCount / totalCount) * 100).toFixed(1)) : 0
        }
      ]

      return data.filter(item => item.count > 0)
    },

    // 待处理咨询列表
    pendingInquiries() {
      return this.pendingInquiriesData
    },

    // 分页后的咨询列表（服务端分页，直接返回当前页数据）
    paginatedInquiries() {
      return this.pendingInquiries
    }
  },
  
  mounted() {
    this.loadDashboardData()
    // 设置自动刷新
    this.setupAutoRefresh()
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleWindowResize)
  },

  activated() {
    // 页面被激活时（从其他页面返回），重新渲染图表
    this.$nextTick(() => {
      this.resizeAllCharts()
    })
  },

  beforeDestroy() {
    // 清理定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
    }
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer)
    }
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer)
    }
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleWindowResize)
  },
  
  methods: {
    // 格式化数字
    formatNumber(num) {
      if (num === undefined || num === null) return 0
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    },

    // 设置自动刷新
    setupAutoRefresh() {
      // 每5分钟自动刷新一次数据
      this.autoRefreshTimer = setInterval(() => {
        this.loadDashboardData(true) // 静默刷新
      }, 5 * 60 * 1000)
    },

    // 加载仪表板数据
    async loadDashboardData(silent = false) {
      if (!silent) this.loading = true

      // 清除之前的错误状态
      this.statisticsError = null
      this.inquiriesError = null
      this.serviceDistributionError = null

      try {
        // 并行加载所有数据
        const [statsRes, serviceRes, pendingRes] = await Promise.allSettled([
          websiteDashboardApi.getStatistics(),
          websiteDashboardApi.getServiceDistribution(),
          getInquiryPage({ status: 'PENDING', current: 1, size: 20 }) // 专门获取待处理咨询
        ])

        // 处理统计数据
        if (statsRes.status === 'fulfilled') {
          this.dashboardStats = statsRes.value.data
        } else {
          this.statisticsError = statsRes.reason
        }

        // 处理服务分布数据
        if (serviceRes.status === 'fulfilled') {
          this.serviceDistribution = serviceRes.value.data
        } else {
          this.serviceDistributionError = serviceRes.reason
        }

        // 处理待处理咨询数据
        if (pendingRes.status === 'fulfilled') {
          this.pendingInquiriesData = pendingRes.value.data.rows || []
          this.pendingInquiriesTotal = parseInt(pendingRes.value.data.total || '0', 10)
        } else {
          // 加载待处理咨询数据失败时静默处理
        }

        // 数据加载完成，图表组件会自动渲染

      } catch (error) {
        if (!silent) {
          this.$message.error('加载数据失败，请稍后重试')
        }
      } finally {
        if (!silent) this.loading = false
      }
    },
    
    // 刷新仪表板
    refreshDashboard() {
      this.debouncedRefresh()
    },

    // 防抖刷新
    debouncedRefresh() {
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer)
      }
      this.refreshTimer = setTimeout(() => {
        this.loadDashboardData()
      }, 300)
    },

    // 重新获取统计数据
    async refetchStatistics() {
      try {
        const response = await websiteDashboardApi.getStatistics()
        this.dashboardStats = response.data
        this.statisticsError = null
      } catch (error) {
        this.statisticsError = error
        throw error
      }
    },

    // 重新获取咨询数据
    async refetchInquiries() {
      try {
        const response = await getInquiryPage({ status: 'PENDING', current: 1, size: 20 })
        this.pendingInquiriesData = response.data.rows || []
        this.pendingInquiriesTotal = parseInt(response.data.total || '0', 10)
        this.inquiriesError = null
      } catch (error) {
        this.inquiriesError = error
        throw error
      }
    },

    // 重新获取服务分布数据
    async refetchServiceDistribution() {
      try {
        const response = await websiteDashboardApi.getServiceDistribution()
        this.serviceDistribution = response.data
        this.serviceDistributionError = null
      } catch (error) {
        this.serviceDistributionError = error
        throw error
      }
    },


    // 分页切换
    async handleCurrentChange(page) {
      this.currentPage = page
      // 重新获取当前页的待处理咨询数据
      try {
        const response = await getInquiryPage({
          status: 'PENDING',
          current: page,
          size: this.pageSize
        })
        this.pendingInquiriesData = response.data.rows || []
        this.pendingInquiriesTotal = parseInt(response.data.total || '0', 10)
      } catch (error) {
        this.$message.error('获取数据失败，请稍后重试')
      }
    },

    // 获取行序号（考虑分页）
    getRowIndex(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1
    },

    // 导航到页面
    navigateToPage(path) {
      // 处理带查询参数的路径
      if (path.includes('?')) {
        const [pathname, queryString] = path.split('?')
        const query = {}
        queryString.split('&').forEach(param => {
          const [key, value] = param.split('=')
          query[key] = value
        })
        this.$router.push({ path: pathname, query })
      } else {
        this.$router.push(path)
      }
    },

    // 导航到指定tab
    navigateToTab(tab) {
      // 通过$emit向父组件传递tab切换事件
      this.$emit('tab-change', tab)
    },
    
    // 查看咨询详情
    viewInquiry(inquiry) {
      this.selectedInquiry = inquiry
      this.detailDialogVisible = true
    },
    
    // 回复咨询
    replyInquiry(inquiry) {
      this.selectedInquiry = inquiry
      this.replyForm = {
        status: inquiry.status === 'PENDING' ? 'COMPLETED' : inquiry.status,
        replyContent: inquiry.replyContent || ''
      }
      this.detailDialogVisible = false // 关闭详情弹窗
      this.replyDialogVisible = true
    },
    
    // 提交回复
    async submitReply() {
      try {
        await this.$refs.replyForm.validate()
        
        this.submitting = true
        
        await websiteInquiryApi.updateInquiry(this.selectedInquiry.id, {
          id: this.selectedInquiry.id,
          status: this.replyForm.status,
          reply: this.replyForm.replyContent
        })
        
        this.$message.success('处理提交成功')
        this.replyDialogVisible = false
        this.loadDashboardData() // 刷新数据
        
      } catch (error) {
        this.$message.error('提交失败，请稍后重试')
      } finally {
        this.submitting = false
      }
    },
    
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'PENDING': 'warning',
        'PROCESSING': 'primary',
        'COMPLETED': 'success',
        'CLOSED': 'info'
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'PENDING': '待处理',
        'PROCESSING': '处理中',
        'COMPLETED': '已处理',
        'CLOSED': '已关闭'
      }
      return statusMap[status] || '未知'
    },
    
    // 格式化日期
    formatDate(dateString) {
      return new Date(dateString).toLocaleString()
    },

    // 处理窗口大小变化
    handleWindowResize() {
      // 防抖处理，避免频繁触发
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer)
      }
      this.resizeTimer = setTimeout(() => {
        this.resizeAllCharts()
      }, 300)
    },

    // 重新渲染所有图表
    resizeAllCharts() {
      // 通过递归查找所有图表组件
      this.$nextTick(() => {
        this.findAndResizeCharts(this)
      })
    },

    // 递归查找并重新渲染图表组件
    findAndResizeCharts(component) {
      if (component.$options.name === 'ServiceDistributionChart') {
        // 找到图表组件，触发重新渲染
        if (component.handleResize) {
          component.handleResize()
        }
        if (component.renderChart) {
          setTimeout(() => {
            component.renderChart()
          }, 50)
        }
      }

      // 递归查找子组件
      if (component.$children && component.$children.length > 0) {
        component.$children.forEach(child => {
          this.findAndResizeCharts(child)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.website-dashboard {
  padding: 24px;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 700;
        color: #111827;
      }

      .page-description {
        margin: 0;
        color: #6b7280;
        font-size: 14px;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .stats-row {
    margin-bottom: 24px;

    .stat-card {
      transition: all 0.3s ease;
      border-radius: 8px;
      margin: 0 4px; /* 增加左右间距 */

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.stat-card-loading {
        opacity: 0.7;
      }

      .stat-content {
        display: flex;
        align-items: center;
        height: 100%;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          i {
            font-size: 24px;
          }
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: 28px;
            font-weight: 600;
            color: #111827;
            line-height: 1;
            margin-bottom: 4px;

            .stat-skeleton {
              color: #d1d5db;
            }

            .stat-unit {
              font-size: 14px;
              color: #6b7280;
              margin-left: 4px;
            }
          }

          .stat-label {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 4px;
          }

          .stat-trend {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;

            i {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .content-row {
    margin-bottom: 24px;

    .quick-actions-card,
    .chart-card {
      height: 384px;

      .el-card__body {
        height: calc(100% - 60px);
        padding: 20px;
      }
    }

    .quick-actions {
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .quick-action-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #3b82f6;
          background-color: #f9fafb;
          transform: translateX(2px);
        }

        .action-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;

          i {
            font-size: 20px;
          }
        }

        .action-info {
          flex: 1;

          .action-title {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #111827;
          }

          .action-description {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
          }
        }
      }
    }

    .chart-container {
      height: 100%;

      .chart {
        width: 100%;
        height: 100%;
      }
    }
  }

  .inquiry-table-card {
    /* 移除固定高度，改为自适应 */

    .el-card__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .table-container {
      padding: 20px 20px 20px 32px; /* 增加左内边距 */
      display: flex;
      flex-direction: column;

      /* 确保表格完全自适应，不产生滚动条 */
      .el-table {
        height: auto !important;
        max-height: none !important;

        .el-table__body-wrapper {
          height: auto !important;
          max-height: none !important;
          overflow: visible !important;
        }

        .el-table__header-wrapper {
          overflow: visible !important;
        }
      }
    }

    .content-cell {
      width: 100%; /* 占满列宽 */
      max-width: 300px; /* 与列宽保持一致 */
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 1.4;
      word-break: break-all; /* 防止长单词撑开容器 */
      display: block; /* 确保宽度设置生效 */
    }

    /* 操作列按钮样式 */
    .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell:last-child {
      .el-button {
        margin-right: 4px;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .pagination-container {
      margin-top: 16px;
      display: flex;
      justify-content: center;
      padding: 12px 0;
      border-top: 1px solid #ebeef5;
      align-items: center;
      flex-shrink: 0; /* 防止分页栏被压缩 */
    }
  }

  .inquiry-detail {
    .content-text {
      white-space: pre-wrap;
      word-break: break-words;
      word-wrap: break-word;
      line-height: 1.6;
      min-height: auto;
      max-width: 100%;
      overflow-wrap: break-word;
      hyphens: auto;
    }

    .reply-content-bg {
      background-color: #eff6ff; /* 对应 bg-blue-50 */
      padding: 12px; /* 对应 p-3 */
      border-radius: 6px; /* 对应 rounded */
    }

    // 确保descriptions组件的内容区域能够自适应高度
    ::v-deep .el-descriptions__body .el-descriptions__table .el-descriptions__cell {
      vertical-align: top;
    }

    ::v-deep .el-descriptions__content {
      word-break: break-words;
      white-space: normal;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .website-dashboard {
    padding: 12px;

    .page-header .header-content {
      flex-direction: column;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .stats-row .stat-card {
      height: 100px;

      .stat-content {
        .stat-icon {
          width: 40px;
          height: 40px;
          margin-right: 12px;

          i {
            font-size: 20px;
          }
        }

        .stat-info .stat-value {
          font-size: 24px;
        }
      }
    }

    .content-row {
      .quick-actions-card,
      .chart-card {
        height: auto;
        margin-bottom: 16px;
      }

      .quick-actions .quick-action-item {
        padding: 12px;

        .action-icon {
          width: 36px;
          height: 36px;
          margin-right: 12px;

          i {
            font-size: 18px;
          }
        }

        .action-info {
          .action-title {
            font-size: 14px;
          }

          .action-description {
            font-size: 12px;
          }
        }
      }

      .chart-container {
        height: 300px;
      }
    }
  }
}

/* 修复对话框蒙版问题 - 使用具体的类名选择器 */
::v-deep .inquiry-detail-dialog,
::v-deep .inquiry-reply-dialog {
  z-index: 9999 !important;
}

::v-deep .inquiry-detail-dialog .el-dialog__wrapper,
::v-deep .inquiry-reply-dialog .el-dialog__wrapper {
  z-index: 9999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

::v-deep .inquiry-detail-dialog .el-overlay,
::v-deep .inquiry-reply-dialog .el-overlay {
  z-index: 9998 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* 确保对话框内容可以正常交互 */
::v-deep .inquiry-detail-dialog .el-dialog__body,
::v-deep .inquiry-reply-dialog .el-dialog__body,
::v-deep .inquiry-detail-dialog .el-dialog__footer,
::v-deep .inquiry-reply-dialog .el-dialog__footer {
  position: relative;
  z-index: 10001 !important;
  pointer-events: auto !important;
}

/* 确保按钮可以正常点击 */
::v-deep .inquiry-detail-dialog .el-button,
::v-deep .inquiry-reply-dialog .el-button {
  pointer-events: auto !important;
  z-index: 10002 !important;
  position: relative;
}
</style>
