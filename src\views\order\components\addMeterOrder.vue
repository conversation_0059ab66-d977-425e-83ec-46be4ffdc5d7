<!--
 * @Author: wskg
 * @Date: 2025-01-03 11:40:39
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:48
 * @Description: 新增抄表对账
 -->
<template>
  <div class="app-container">
    <ProDialog
      :value="drawerVisible"
      title="新增抄表对账"
      width="70%"
      top="2%"
      :confirm-btn-loading="confirmButLoading"
      :confirm-text="'确认新增'"
      @ok="handleDrawerOk"
      @cancel="handleDrawerCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="editForm"
        :form-list="formColumns"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :open-type="editType"
        :confirm-loading="confirmButLoading"
        @proSubmit="proSubmit"
      >
        <template #customerName>
          <div class="tit-boxs">
            <el-button type="success" @click="showDialogFn">
              选择客户
            </el-button>
          </div>
          <el-form
            ref="proFormChild"
            :model="editForm"
            :rules="editFormRules"
            :disabled="editType === 'info'"
            label-width="120px"
            class="demo-ruleForm"
          >
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="客户编码 : " prop="seqId">
                  <el-input
                    v-model="editForm.seqId"
                    placeholder="请选择客户信息"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="店铺名称 : " prop="customerName">
                  <el-input
                    v-model="editForm.customerName"
                    placeholder="请选择客户信息"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="设备组名称 : " prop="deviceGroupId">
                  <el-select
                    v-model="editForm.deviceGroupId"
                    style="width: 100%"
                    placeholder="请选择设备组"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="item in deviceGroupOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="合约类型 : ">
                  {{ editForm.serType?.label || "" }}
                  <!--<el-select-->
                  <!--  v-model="editForm.serType"-->
                  <!--  style="width: 100%"-->
                  <!--  placeholder="请选择订单类型"-->
                  <!--  clearable-->
                  <!--  filterable-->
                  <!--  disabled-->
                  <!--&gt;-->
                  <!--  <el-option-->
                  <!--    v-for="item in serTypeOptions"-->
                  <!--    :key="item.value"-->
                  <!--    :label="item.label"-->
                  <!--    :value="item.value"-->
                  <!--  >-->
                  <!--  </el-option>-->
                  <!--</el-select>-->
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
        <template #picUrls>
          <div class="title-box">计数器图片</div>
          <ProUpload
            :file-list="editForm.picUrls"
            :type="editType"
            :limit="5"
            @uploadSuccess="handleLicenseImgUploadSuccess"
            @uploadRemove="handleLicenseImgUploadRemove"
          />
        </template>
      </ProForm>
    </ProDialog>
    <ProDialog
      :value="showDialog"
      title="客户信息"
      width="80%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <ProTable
        ref="ProTables"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="creatColumns"
        row-key="id"
        :local-pagination="localPagination"
        :data="tableData"
        sticky
        :query-param="queryParams"
        :height="430"
        :show-setting="false"
        @loadData="loadData"
      >
        <template #actions="{ row }">
          <span class="fixed-width">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-circle-check"
              @click="sureSelectCustom(row)"
            >
              确定
            </el-button>
          </span>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { getCustomerByPageApi, getCustomerDeviceListApi } from "@/api/customer";
import { mergeCycleOrderPayApi } from "@/api/pay";
import ProUpload from "@/components/ProUpload/index.vue";
import { dictTreeByCodeApi } from "@/api/user";
import { meterUpdateApi } from "@/api/statisics";

export default {
  name: "AddMeterOrder",
  components: { ProUpload },
  data() {
    return {
      drawerVisible: false,
      editForm: {},
      formColumns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isForm: true,
          formOtherSlot: "customerName",
          formSpan: 24,
        },
        {
          dataIndex: "blackWhiteInception",
          title: "初始黑白计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "colorInception",
          title: "初始彩色计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "fiveColourIncption",
          title: "初始五色计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "blackWhiteCutoff",
          title: "截止黑白计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "colorCutoff",
          title: "截止彩色计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "fiveColourCutoff",
          title: "截止五色计数器",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "blackWhiteExclude",
          title: "黑白废张数",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "colorExclude",
          title: "彩色废张数",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        {
          dataIndex: "fiveColourExclude",
          title: "五色废张数",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 8,
        },
        // {
        //   dataIndex: "cycle",
        //   title: "抄表日期",
        //   isForm: true,
        //   valueType: "date-picker",
        //   pickerType: "month",
        //   pickerFormat: "yyyy-MM",
        //   valueFormat: "yyyy-MM",
        //   formSpan: 8,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择抄表日期",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "beginTime",
          title: "起始抄表时间",
          isForm: true,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请选择起始抄表时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "endTime",
          title: "期末抄表时间",
          isForm: true,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请选择期末抄表时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "cycle",
          title: "抄表月度",
          isForm: true,
          valueType: "date-picker",
          pickerType: "month",
          pickerFormat: "yyyy-MM",
          valueFormat: "yyyy-MM",
          // effect: "light",
          // tooltipContent: '',
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请选择抄表日期",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "picUrls",
          title: "计数器图片",
          isForm: true,
          formOtherSlot: "picUrls",
          formSpan: 24,
        },
      ],
      editFormRules: {
        // seqId: [
        //   {
        //     required: true,
        //     message: "请点击选择客户",
        //     trigger: "blur",
        //   },
        // ],
        // customerName: [
        //   {
        //     required: true,
        //     message: "请点击选择客户",
        //     trigger: "blur",
        //   },
        // ],
        deviceGroupId: [
          {
            required: true,
            message: "请选择设备组名称",
            trigger: "change",
          },
        ],
        serType: [
          {
            required: true,
            message: "请选择合约类型",
            trigger: "change",
          },
        ],
      },
      editType: "add",
      // 客户信息
      showDialog: false,
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      queryParams: {},
      creatColumns: [
        // {
        //   dataIndex: "shopRecruitment",
        //   title: "店铺名称",
        //   isTable: true,
        // },
        {
          dataIndex: "name",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "seqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },

        {
          dataIndex: "legalPersonTel",
          title: "法人电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      confirmButLoading: false,
      serTypeOptions: [
        {
          label: "购机全保",
          value: "BUY_FULL",
        },
        {
          label: "购机半保",
          value: "BUY_HALF",
        },
        {
          label: "租赁全保",
          value: "RENT_FULL",
        },
        {
          label: "租赁半保",
          value: "RENT_HALF",
        },
        {
          label: "普通全保",
          value: "ALL",
        },
        {
          label: "普通半保",
          value: "HALF",
        },
        {
          label: "包量全保",
          value: "PACKAGE_ALL",
        },
        {
          label: "包量半保",
          value: "PACKAGE_HALF",
        },
        {
          label: "融资全保",
          value: "FINANCING_FULL",
        },
        {
          label: "融资半保",
          value: "FINANCING_HALF",
        },
      ], // 合约类型
      deviceGroupOptions: [], // 设备组
      deviceGroupList: [],
    };
  },
  watch: {
    "editForm.deviceGroupId": {
      handler(val) {
        if (val) {
          const findDevice = this.deviceGroupList.find(
            (item) => item.id === val
          );
          console.log(findDevice, "findDevice");
          if (findDevice && findDevice.serType) {
            this.$set(this.editForm, "serType", findDevice.serType);
          }
          // if (findDevice?.color) {
          //   this.$set(this.editForm, "color", findDevice.color);
          // }
          // this.handleDeviceChanges(findDevice?.color);
          // 1701 彩机  1702 黑白机  1703 五彩急
        } else {
          this.$set(this.editForm, "serType", null);
          // delete this.editForm.color;
        }
      },
    },
    // "editForm.serType": {
    //   handler(val) {
    //     this.changeContractType(val);
    //   },
    //   deep: true,
    // },
    // "editForm.priceType": {
    //   handler(val) {
    //     this.changePriceType(val);
    //   },
    //   deep: true,
    // },
    "editForm.customerId": {
      handler(val) {
        this.deviceGroupList = [];
        this.deviceGroupOptions = [];
        this.$set(this.editForm, "deviceGroupId", null);
        this.$set(this.editForm, "serType", null);
        this.getCustomerProductList(val);
      },
    },
  },
  methods: {
    show() {
      this.editForm = {};
      this.deviceGroupOptions = [];
      this.deviceGroupList = [];
      this.drawerVisible = true;
      // this.getContractType();
    },
    handleDeviceChanges(color) {
      const { formColumns } = this;
      // Reset all relevant counters
      this.updateIsForm(
        formColumns,
        [
          "signBlackWhiteCounter",
          "signColoursCounter",
          "signFiveColoursCounter",
        ],
        false
      );
      if (color?.value) {
        const colorValue = color.value;
        const countersToShow = this.getCountersByColor(colorValue);
        this.updateIsForm(formColumns, countersToShow, true);
      }
      // Handle contract type and price type changes
      this.changeContractType(this.form?.serType);
      this.changePriceType(this.form?.priceType);
    },
    getCountersByColor(colorValue) {
      switch (colorValue) {
        case "1703": // 五彩机
          return [
            "signBlackWhiteCounter",
            "signColoursCounter",
            "signFiveColoursCounter",
          ];
        case "1701": // 彩色机
          return ["signBlackWhiteCounter", "signColoursCounter"];
        case "1702": // 黑白机
          return ["signBlackWhiteCounter"];
        default:
          return [];
      }
    },
    changeContractType(val) {
      const { formColumns, form } = this;
      // Hide all related fields initially
      this.updateIsForm(
        formColumns,
        ["prepayment", "blackGuarantee", "colorGuarantee"],
        false
      );
      if (val === "1265") {
        const colorValue = form.color?.value;
        const fieldsToShow = this.getContractFieldsByColor(colorValue);
        this.updateIsForm(formColumns, fieldsToShow, true);
      }
    },
    getContractFieldsByColor(colorValue) {
      switch (colorValue) {
        case "1703": // 五彩机
          return ["prepayment"];
        case "1701": // 彩色机
          return ["prepayment", "blackGuarantee", "colorGuarantee"];
        case "1702": // 黑白机
          return ["prepayment", "blackGuarantee"];
        default:
          return [];
      }
    },
    changePriceType(val) {
      const { formColumns, form } = this;
      // Hide all related fields initially
      this.updateIsForm(
        formColumns,
        ["blackWhitePrice", "colorPrice", "fiveColourPrice"],
        false
      );
      this.updateIsForm(formColumns, ["accountMode", "ladder"], false);
      if (val === "IMMOBILIZATION") {
        const colorValue = form.color?.value;
        const fieldsToShow = this.getPriceFieldsByColor(colorValue);
        this.updateIsForm(formColumns, fieldsToShow, true);
      } else if (val === "LADDER") {
        this.updateIsForm(formColumns, ["accountMode", "ladder"], true);
      }
    },
    getPriceFieldsByColor(colorValue) {
      switch (colorValue) {
        case "1703": // 五彩机
          return ["blackWhitePrice", "colorPrice", "fiveColourPrice"];
        case "1701": // 彩色机
          return ["blackWhitePrice", "colorPrice"];
        case "1702": // 黑白机
          return ["blackWhitePrice"];
        default:
          return [];
      }
    },
    updateIsForm(columns, keys, isForm) {
      columns.forEach((item) => {
        if (keys.includes(item.dataIndex)) {
          item.isForm = isForm;
        }
      });
    },
    handleDrawerOk() {
      this.$refs.ProForm.handleSubmit();
    },
    async proSubmit(val) {
      try {
        if (!val.seqId) {
          this.$message.error("请先选择抄表客户");
          return;
        }
        if (!val.deviceGroupId) {
          this.$message.error("请先选择抄表设备组");
          return;
        }
        this.confirmButLoading = true;
        const args = {
          ...val,
        };
        const result = await meterUpdateApi(args);
        if (result.code === 200) {
          this.$message.success("操作成功");
          this.$emit("refresh");
          this.drawerVisible = false;
        }
      } finally {
        this.confirmButLoading = false;
      }
    },
    extractIds(data) {
      const ids = [];
      function recurse(items) {
        items.forEach((item) => {
          ids.push(item.code);
          if (item.children && Array.isArray(item.children)) {
            recurse(item.children);
          }
        });
      }
      recurse(data);
      return ids;
    },
    handleDrawerCancel() {
      this.drawerVisible = false;
    },
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParams, parameter);
      getCustomerByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTables
            ? (this.$refs.ProTables.listLoading = false)
            : null;
        });
    },
    showDialogFn() {
      this.showDialog = true;
      this.$nextTick((e) => {
        this.queryParams = {};
        this.$refs.ProTables.refresh();
      });
    },
    // 获取合约类型
    async getContractType() {
      try {
        const result = await dictTreeByCodeApi(1200);
        if (result.code === 200) {
          console.log("合约类型", result);
          this.serTypeOptions = result.data;
        }
      } catch (e) {
        this.serTypeOptions = [];
      }
    },
    // 获取客户设备组列表
    getCustomerProductList(customerId) {
      if (!customerId) {
        return;
      }
      getCustomerDeviceListApi(customerId).then((res) => {
        this.deviceGroupList = res.data;
        this.deviceGroupOptions = res.data.map((item) => {
          return {
            label: item.deviceGroup.label + " / " + item.productInfo,
            value: item.id,
          };
        });
      });
    },
    // 确认用户
    async sureSelectCustom(row) {
      this.editForm.seqId = row.seqId;
      this.editForm.customerName = row.name;
      // this.editForm.customerId = row.id;
      this.$set(this.editForm, "customerId", row.id);
      this.showDialog = false;
    },
    handleLicenseImgUploadSuccess(result) {
      if (!this.editForm.picUrls) {
        this.$set(this.editForm, "picUrls", []);
      }
      this.editForm.picUrls.push(result);
    },
    handleLicenseImgUploadRemove(file) {
      const index = this.editForm.picUrls.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.editForm.picUrls.splice(index, 1);
    },
  },
};
</script>

<style scoped lang="scss">
.tit-boxs {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px 7px;
  font-size: 16px;
  font-weight: 800;
}
</style>
