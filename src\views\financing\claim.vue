<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-19 15:09:49
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 17:52:39
 * @Description: 
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:finance:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>含税总金额：{{ totalData?.totalAmount || 0 }}</div>
          <div>不含税总金额：{{ totalData?.totalNoTaxAmount || 0 }}</div>
          <div>客户领料总额：{{ totalData?.applyAmount || 0 }}</div>
          <div>维修工单总额：{{ totalData?.workAmount || 0 }}</div>
          <div>毛机维修总额：{{ totalData?.repairAmount || 0 }}</div>
          <div>赠品订单总额：{{ totalData?.giftAmount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import {
  claimOrderOutboundDetailExportApi,
  claimOrderOutboundDetailListApi,
  claimOrderOutboundDetailStatisticsApi,
} from "@/api/finance";
import { handleExcelExport } from "@/utils/exportExcel";
import { filterParam, filterParamRange } from "@/utils";

export default {
  name: "ClaimOrder",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      columns: [
        {
          dataIndex: "createDate",
          title: "领料日期",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 100,
        },
        {
          dataIndex: "code",
          title: "工单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "type",
          title: "类型",
          isTable: true,
          formatter: (row) =>
            row.type == 1
              ? "客户领料"
              : row.type == 2
              ? "维修工单"
              : row.type == 3
              ? "毛机维修"
              : row.type == 4
              ? "赠品订单"
              : "",
          isSearch: true,
          valueType: "select",
          // multiple: true,
          option: [
            {
              label: "客户领料",
              value: 1,
            },
            {
              label: "维修工单",
              value: 2,
            },
            {
              label: "毛机维修",
              value: 3,
            },
            {
              label: "赠品订单",
              value: 4,
            },
          ],
          width: 80,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "license",
          title: "营业执照名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "customerCode",
          title: "客户/机器编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "applyType",
          title: "领料类别",
          isTable: true,
          formatter: (row) =>
            row.applyType == 1
              ? "运维抄表"
              : row.applyType == 2
              ? "售后领料"
              : row.applyType == 3
              ? "自用生产领料"
              : row.applyType == 4
              ? "装机领料"
              : "",
          // isSearch: true,
          // valueType: "select",
          // option: [
          //   {
          //     label: "运维抄表",
          //     value: 1,
          //   },
          //   {
          //     label: "售后领料",
          //     value: 2,
          //   },
          //   {
          //     label: "自用生产领料",
          //     value: 3,
          //   },
          //   {
          //     label: "装机领料",
          //     value: 4,
          //   },
          // ],
          minWidth: 110,
        },
        {
          dataIndex: "warehouseName",
          title: "仓库",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "articleCode",
          title: "物品编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
          minWidth: 100,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          minWidth: 80,
        },

        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "price",
          title: "成本单价",
          isTable: true,
          minWidth: 100,
        },

        {
          dataIndex: "taxAmount",
          title: "税额",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "tax",
          title: "税率（%）",
          isTable: true,
          formatter: (row) => (row.tax ? row.tax + "%" : ""),
          minWidth: 100,
        },
        {
          dataIndex: "amount",
          title: "成本含税金额",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "noTaxAmount",
          title: "成本不含税金额",
          isTable: true,
          minWidth: 120,
        },
        // {
        //   dataIndex: "tax",
        //   title: "税率(%)",
        //   isTable: true,
        //   formatter: (row) => (row.tax ? row.tax + "%" : ""),
        //   minWidth: 80,
        // },
        // {
        //   dataIndex: "invoiceStatus",
        //   title: "开票状态",
        //   isTable: true,
        //   formatter: (row) => (row.invoiceStatus === 1 ? "已开票" : "未开票"),
        //   minWidth: 80,
        // },
      ],
      requestParameters: {},
      exportLoading: false,
      totalData: {},
      statLoading: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const paramsRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createDate,
        },
      ];
      filterParamRange(this, this.queryParam, paramsRange);
      this.requestParameters = cloneDeep(this.queryParam);
      delete this.requestParameters.createDate;
      claimOrderOutboundDetailListApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      claimOrderOutboundDetailStatisticsApi(this.requestParameters)
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    handleExport() {
      this.$confirm("此操作将导出领料出库明细, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          claimOrderOutboundDetailExportApi,
          this.requestParameters,
          "领料出库明细",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
