<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 14:28:12
 * @Description: 
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      row-key="label"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="550"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right" style="font-size: 14px; gap: 16px">
          <div>订单数：{{ totalData?.orderNum || 0 }}</div>
          <div>总销售额：{{ totalData?.orderAmount || 0 }}</div>
          <div>领料单数：{{ totalData?.applyNum || 0 }}</div>
          <div>领料单金额：{{ totalData?.applyAmount || 0 }}</div>
          <div>全/半保工单数：{{ totalData?.pactWorkNum || 0 }}</div>
          <div>全/半保耗材费：{{ totalData?.pactPartAmount || 0 }}</div>
          <div>全/半人工费：{{ totalData?.pactLaborAmount || 0 }}</div>
          <div>工单数：{{ totalData?.workNum || 0 }}</div>
          <div>普通工单耗材费：{{ totalData?.workPartAmount || 0 }}</div>
          <div>普通工单人工费：{{ totalData?.workLaborAmount || 0 }}</div>
          <div>订单总金额：{{ totalData?.salesAmount || 0 }}</div>
          <div>抄表费用：{{ totalData?.receiptAmount || 0 }}</div>
          <div>维修总金额：{{ totalData?.repairAmount || 0 }}</div>
          <div>实付金额：{{ totalData?.actureAmount || 0 }}</div>
        </div>
      </template>
    </ProTable>
  </div>
</template>
<script>
import { divideAmount, filterParam, filterParamRange } from "@/utils";
import {
  customMonthSaleListApi,
  customMonthSaleSummaryListApi,
} from "@/api/statisics";
import { cloneDeep } from "lodash";

export default {
  name: "CustomMonthSaleAccount",
  data() {
    return {
      tableData: [],
      productIdName: "",
      productTreeOption: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        startDate: null,
        endDate: null,
        costDate: [],
      },
      columns: [
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          width: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          width: 200,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
          attrs: { "value-format": "yyyy-MM" },
          width: 100,
        },
        {
          dataIndex: "orderNum",
          title: "销售订单数",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "inputRange",
          startPlaceholder: "销售订单数",
          endPlaceholder: "销售订单数",
        },
        {
          dataIndex: "orderAmount",
          title: "销售金额",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "inputRange",
          startPlaceholder: "销售金额",
          endPlaceholder: "销售金额",
          formatter: (row) => divideAmount(row.orderAmount, 100),
          sortMethod: (a, b) => {
            return (
              divideAmount(a.orderAmount, 100) -
              divideAmount(b.orderAmount, 100)
            );
          },
        },
        {
          dataIndex: "applyNum",
          title: "领料单数",
          isTable: true,
          sortable: true,
          // isSearch: true,
          // valueType: "inputRange",
        },
        {
          dataIndex: "applyAmount",
          title: "领料单金额",
          isTable: true,
          sortable: true,
          formatter: (row) => divideAmount(row.applyAmount, 100),
          sortMethod: (a, b) => {
            return (
              divideAmount(a.applyAmount, 100) -
              divideAmount(b.applyAmount, 100)
            );
          },
        },

        {
          dataIndex: "pactWorkNum",
          title: "全/半保工单数",
          isTable: true,
          sortable: true,
        },
        {
          dataIndex: "pactPartAmount",
          title: "全/半保工单耗材费用",
          valueType: "select",
          isSearch: false,
          isTable: true,
          clearable: true,
          sortable: true,
          formatter: (row) => divideAmount(row.pactPartAmount, 100),
          sortMethod: (a, b) => {
            return (
              divideAmount(a.pactPartAmount, 100) -
              divideAmount(b.pactPartAmount, 100)
            );
          },
        },
        {
          dataIndex: "pactLaborAmount",
          title: "全/半保人工费用",
          valueType: "select",
          isSearch: false,
          isTable: true,
          multiple: true,
          clearable: true,
          sortable: true,
          formatter: (row) => divideAmount(row.pactLaborAmount, 100),
          sortMethod: (a, b) => {
            return (
              divideAmount(a.pactLaborAmount, 100) -
              divideAmount(b.pactLaborAmount, 100)
            );
          },
        },

        {
          dataIndex: "workNum",
          title: "工单数",
          isTable: true,
          sortable: true,
        },
        {
          dataIndex: "workPartAmount",
          title: "普通工单耗材费",
          isTable: true,
          sortable: true,
          formatter: (row) => divideAmount(row.workPartAmount, 100),
          sortMethod: (a, b) => {
            return (
              divideAmount(a.workPartAmount, 100) -
              divideAmount(b.workPartAmount, 100)
            );
          },
        },
        {
          dataIndex: "workLaborAmount",
          title: "普通工单人工费",
          isTable: true,
          sortable: true,
          formatter: (row) => divideAmount(row.workLaborAmount, 100),
          sortMethod: (a, b) => {
            return (
              divideAmount(a.workLaborAmount, 100) -
              divideAmount(b.workLaborAmount, 100)
            );
          },
        },

        {
          dataIndex: "salesAmount",
          title: "订单总金额",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          startPlaceholder: "订单总金额",
          endPlaceholder: "订单总金额",
          sortable: true,
          formatter: (row) => divideAmount(row.salesAmount, 100),
          sortMethod: (a, b) => {
            return (
              divideAmount(a.salesAmount, 100) -
              divideAmount(b.salesAmount, 100)
            );
          },
        },
        {
          dataIndex: "receiptAmount",
          title: "抄表费用",
          isTable: true,
          formatter: (row) => divideAmount(row.receiptAmount, 100),
        },

        {
          dataIndex: "repairAmount",
          title: "维修总金额",
          isTable: true,
          sortable: true,
          isSearch: true,
          valueType: "inputRange",
          startPlaceholder: "维修总金额",
          endPlaceholder: "维修总金额",
          formatter: (row) => divideAmount(row.repairAmount, 100),
          sortMethod: (a, b) => {
            return (
              divideAmount(a.repairAmount, 100) -
              divideAmount(b.repairAmount, 100)
            );
          },
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isTable: true,
          sortable: true,
          formatter: (row) => divideAmount(row.totalAmount, 100),
          sortMethod: (a, b) => {
            return (
              divideAmount(a.totalAmount, 100) -
              divideAmount(b.totalAmount, 100)
            );
          },
        },
        {
          dataIndex: "actureAmount",
          title: "实付金额",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "inputRange",
          startPlaceholder: "实付金额",
          endPlaceholder: "实付金额",
          formatter: (row) => divideAmount(row.actureAmount, 100),
          sortMethod: (a, b) => {
            return (
              divideAmount(a.actureAmount, 100) -
              divideAmount(b.actureAmount, 100)
            );
          },
        },
        {
          dataIndex: "refundAmount",
          title: "退款金额",
          isTable: true,
          isSearch: false,
          sortable: true,
          formatter: (row) => divideAmount(row.refundAmount, 100),
        },
        // {
        //   dataIndex: "Actions",
        //   fixed: "right",
        //   title: "操作",
        //   align: "left",
        //   isTable: true,
        //   tableSlot: "actions",
        // },
      ],
      totalData: {},
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );

      const result = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.currMonth,
        },
        {
          beginOrderNum: null,
          endOrderNum: null,
          data: parameter.orderNum, // 客户月度消费——销售订单数
        },
        {
          beginOrderAmount: null,
          endOrderAmount: null,
          data: parameter.orderAmount, // 客户月度消费——销售金额
        },
        {
          beginActureAmount: null,
          endActureAmount: null,
          data: parameter.actureAmount, // 客户月度消费——实付金额
        },
        {
          beginSalesAmount: null,
          endSalesAmount: null,
          data: parameter.salesAmount, // 客户月度消费——订单总金额
        },
        {
          beginRepairAmount: null,
          endRepairAmount: null,
          data: parameter.repairAmount, // 客户月度消费——维修总金额
        },
      ];
      filterParamRange(this, this.queryParam, result);

      const requestParameters = cloneDeep(this.queryParam);
      [
        "currMonth",
        "orderNum",
        "orderAmount",
        "actureAmount",
        "salesAmount",
        "repairAmount",
      ].forEach((key) => delete requestParameters[key]);

      customMonthSaleListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getTotalData(requestParameters);
    },
    getTotalData(params) {
      customMonthSaleSummaryListApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
