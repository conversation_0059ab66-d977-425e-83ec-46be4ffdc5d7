/**
 * 简化的配置管理
 * 自动读取Vue项目config.js，支持环境变量覆盖
 */

// 全局配置变量
let apiBaseUrl: string = '';

/**
 * 初始化配置
 */
export async function initConfig(): Promise<void> {
  try {
    // 1. 优先使用环境变量
    if (import.meta.env.VITE_API_BASE_URL) {
      apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
      return;
    }

    // 2. 生产环境尝试读取config.js文件
    if (import.meta.env.PROD) {
      try {
        const response = await fetch('/config.js');

        if (response.ok) {
          const configText = await response.text();

          // 检查是否是HTML页面（说明文件不存在）
          if (!configText.includes('<!DOCTYPE html>')) {
            // 简单的正则提取baseURL（匹配未注释的行）
            const lines = configText.split('\n');
            for (const line of lines) {
              // 跳过注释行
              if (line.trim().startsWith('//')) {
                continue;
              }

              const match = line.match(/baseURL:\s*["']([^"']+)["']/);
              if (match && match[1]) {
                apiBaseUrl = match[1];
                return;
              }
            }
          }
        }
      } catch (error) {
        // 静默处理错误
      }
    }

    // 3. 使用默认配置
    apiBaseUrl = import.meta.env.PROD ? '/api' : 'http://localhost:8080/api';

  } catch (error) {
    console.error('配置初始化失败:', error);
    apiBaseUrl = '/api'; // 最终兜底
  }
}

/**
 * 获取API基础URL
 */
export function getApiBaseUrl(): string {
  return apiBaseUrl;
}

/**
 * 手动设置API地址（用于开发调试）
 */
export function setApiBaseUrl(url: string): void {
  apiBaseUrl = url;
  console.log('手动设置API地址:', url);
}
