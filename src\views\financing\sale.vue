<!--
 * @Author: wskg
 * @Date: 2025-03-19 14:06:27
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 09:09:22
 * @Description: 采购单明细
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:finance:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>销售含税总额：{{ totalData?.totalAmount || 0 }}</div>
          <div>销售不含税总额：{{ totalData?.totalNoTaxAmount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import {
  saleOrderDetailExportApi,
  saleOrderDetailListApi,
  saleOrderDetailStatisticsApi,
} from "@/api/finance";
import { handleExcelExport } from "@/utils/exportExcel";
import { filterParam, filterParamRange } from "@/utils";

export default {
  name: "SaleDetails",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      columns: [
        {
          dataIndex: "createDate",
          title: "销售日期",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 100,
        },
        {
          dataIndex: "code",
          title: "订单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "type",
          title: "类型",
          isTable: true,
          formatter: (row) =>
            row.type == 1
              ? "销售出货"
              : row.type == 0
              ? "销售退货"
              : row.type == 2
              ? "维修工单"
              : "",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "销售出货",
              value: 1,
            },
            {
              label: "销售退货",
              value: 0,
            },
            {
              label: "维修工单",
              value: 2,
            },
          ],
          width: 80,
        },
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "customerCode",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "license",
          title: "营业执照名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },

        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "articleCode",
          title: "物品编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
          minWidth: 100,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          minWidth: 80,
        },

        {
          dataIndex: "num",
          title: "销售数量",
          isTable: true,
          minWidth: 80,
        },

        {
          dataIndex: "price",
          title: "销售单价",
          isTable: true,
          minWidth: 100,
        },

        {
          dataIndex: "amount",
          title: "销售金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "noTaxAmount",
          title: "不含税金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "taxAmount",
          title: "税额",
          isTable: true,
          // formatter: (row) => (row.taxAmount ? row.taxAmount + "%" : ""),
          minWidth: 80,
        },
        {
          dataIndex: "tax",
          title: "税率(%)",
          isTable: true,
          formatter: (row) => (row.tax ? row.tax + "%" : ""),
          minWidth: 80,
        },
        {
          dataIndex: "purchasePrice",
          title: "采购含税单价",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "purchaseTaxAmount",
          title: "采购税额",
          isTable: true,
          // formatter: (row) =>
          //   row.purchaseTaxAmount ? row.purchaseTaxAmount + "%" : "",
          minWidth: 100,
        },
        {
          dataIndex: "purchaseAmount",
          title: "成本含税金额",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "purchaseNoTaxAmount",
          title: "成本不含税金额",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "status",
          title: "订单状态",
          isTable: true,
          formatter: (row) => {
            switch (row.status) {
              case "WAIT_PAY":
                return "待支付";
              case "WAIT_DELIVER":
                return "待发货";
              case "WAIT_RECEIVE":
                return "待收货";
              case "SUCCESS":
                return "已完成";
              case "CLOSED":
                return "已取消";
              default:
                return "";
            }
          },
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [
            { label: "待支付", value: "WAIT_PAY" },
            { label: "待发货", value: "WAIT_DELIVER" },
            { label: "待收货", value: "WAIT_RECEIVE" },
            // { label: "待审核", value: "WAIT_AUDIT" },
            { label: "已完成", value: "SUCCESS" },
            { label: "已取消", value: "CLOSED" },
          ],
          minWidth: 100,
        },
        // {
        //   dataIndex: "invoiceStatus",
        //   title: "开票状态",
        //   isTable: true,
        //   formatter: (row) => (row.invoiceStatus === 1 ? "已开票" : "未开票"),
        //   minWidth: 80,
        // },
      ],
      requestParameters: {},
      exportLoading: false,
      totalData: {},
      statLoading: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const paramsRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createDate,
        },
      ];
      filterParamRange(this, this.queryParam, paramsRange);
      this.requestParameters = cloneDeep(this.queryParam);
      delete this.requestParameters.createDate;
      saleOrderDetailListApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      saleOrderDetailStatisticsApi(this.requestParameters).then((res) => {
        this.totalData = res.data;
        this.statLoading = true;
      });
    },
    handleExport() {
      this.$confirm("此操作将导出耗材销售明细, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          saleOrderDetailExportApi,
          this.requestParameters,
          "耗材销售明细",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
