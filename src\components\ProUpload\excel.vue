<template>
  <div class="upload" :class="objClass">
    <ProDialog
      id="uploadDialog"
      :value="dialogUpload"
      :title="title"
      :dialog-loading="dialogLoading"
      :confirm-text="confirmText"
      :confirm-btn-loading="confirmLoading"
      width="500px"
      @ok="submitUpload"
      @cancel="handleClose"
    >
      <el-upload
        ref="upload"
        class="upload-demo"
        name="file"
        :action="actionUrl"
        :headers="{ 'X-AUTH-TOKEN': token }"
        accept=".xls,.xlsx,.XLSX,.XLS"
        :multiple="false"
        drag
        :limit="limit"
        :file-list="fileList"
        :on-success="handleSuccess"
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-error="handleError"
        :auto-upload="false"
        :disabled="flag"
      >
        <div slot="trigger" class="el-upload-area">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </div>
        <div
          slot="tip"
          class="el-upload__tip"
          style="max-height: 400px; overflow: auto"
        >
          {{ errorMsg || "提示：仅支持上传“xls”或“xlsx”格式文件。" }}
        </div>
        <!--<el-progress-->
        <!--  v-if="progress > 0"-->
        <!--  :percentage="progress"-->
        <!--  style="margin-top: 10px"-->
        <!--&gt;</el-progress>-->
      </el-upload>
      <el-button
        v-if="isDownTemplate"
        size="small"
        type="text"
        @click="handleDownloadTemplate"
      >
        下载模板
      </el-button>
    </ProDialog>
  </div>
</template>

<script>
import { Message } from "element-ui";
import axios from "axios";

export default {
  name: "ExcelUpload",
  props: {
    title: {
      type: String,
      default: "导入文件",
    },
    confirmText: {
      type: String,
      default: "导入",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    limit: {
      type: Number,
      default: 1,
    },
    actionUrl: {
      type: String,
      default: "",
    },
    isDownTemplate: {
      type: Boolean,
      default: true,
    },
    downloadTemplateFun: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      fileList: [],
      dialogUpload: false,
      dialogLoading: false,
      confirmLoading: false,
      token: localStorage.getItem("token"),
      objClass: {
        upLoadShow: true,
        upLoadHide: false,
      },
      errorMsg: "", // 错误提示文字
      flag: false, // 文件导入中不允许关闭
      progress: 0,
    };
  },
  watch: {
    fileList: {
      handler(val) {
        if (val.length > 0) {
          this.errorMsg = "";
        }
      },
    },
  },
  methods: {
    show() {
      this.progress = 0;
      this.dialogUpload = true;
    },
    handleSubmit(options) {
      const { file, onProgress, onSuccess, onError } = options;
      const formData = new FormData();
      formData.append("file", file);

      axios
        .post(this.actionUrl, formData, {
          headers: {
            "X-AUTH-TOKEN": this.token,
            "Content-Type": "multipart/form-data",
          },
          onUploadProgress: (progressEvent) => {
            console.log(progressEvent);
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            onProgress({ percent: percentCompleted });
          },
        })
        .then((response) => {
          onSuccess(response.data);
        })
        .catch((error) => {
          onError(error);
        });
    },
    submitUpload() {
      if (!this.fileList.length) {
        return Message.error("请选择上传文件");
      }
      this.confirmLoading = true;
      this.$refs.upload.submit();
      this.flag = true;
    },
    handleSuccess(res) {
      if (res.code === 200) {
        this.$emit("uploadSuccess", res);
        this.flag = false;
        this.handleClose();
        Message.success("文件上传成功");
      } else {
        this.errorMsg = res.message;
        this.progress = 0;
        Message.error(res.message || "文件上传失败，请稍后重试。");
        this.confirmLoading = false;
        this.flag = false;
        this.clear();
      }
    },
    handleChange(files, fileList) {
      if (files.status === "ready") {
        if (!/\.(xlsx|xls|XLSX|XLS)$/.test(files.raw.name)) {
          Message.error("仅支持上传“xls”或“xlsx”格式文件。");
          fileList.splice(-1, 1);
          return;
        }
        if (files.raw.size / 1024 / 1024 > 20) {
          Message.error("文件大小不能超过20MB。");
          fileList.splice(-1, 1);
          return;
        }
      }
      this.fileList = fileList.slice(-1);
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    handleError() {
      this.flag = false;
      this.clear();
      Message.error("文件上传失败，请稍后重试。");
      this.confirmLoading = false;
    },
    handleDownloadTemplate() {
      this.downloadTemplateFun();
    },
    clear() {
      this.$refs.upload.clearFiles();
    },
    abort() {
      this.$refs.upload.abort();
    },
    handleProgress(event, file, fileList) {
      // 处理上传进度
      this.progress = Math.round(event.percent);
    },
    handleClose() {
      if (this.flag) {
        return Message.error("文件上传中不允许关闭");
      }
      // this.abort();
      this.flag = false;
      this.clear();
      this.errorMsg = "";
      this.confirmLoading = false;
      this.dialogUpload = false;
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep.upload-demo {
  .el-upload {
    display: flex;
    justify-content: center;
  }
}
</style>
