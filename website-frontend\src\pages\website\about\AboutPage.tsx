import { useState, useEffect, useMemo } from 'react';
import { Helmet } from 'react-helmet-async';
import { Modal, Spin } from 'antd';
import { useAbout } from '@/hooks/useWebsiteApi';



export default function AboutPage() {
  const { data: aboutData, isLoading, error } = useAbout();

  // 图片预览状态
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState('');
  const [previewImageTitle, setPreviewImageTitle] = useState('');
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  // 检测移动端设备
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 修复：aboutData 直接就是内容，不需要解包 data 字段
  const content: any = aboutData;
  const seoMeta = content?.seoMeta;



  /**
   * 解析后端返回的 JSON 字符串，仅解析一次
   */
  const parsedConfig = useMemo(() => {
    if (!content?.content) return null;
    try {
      const parsed = JSON.parse(content.content);
      // 根据API响应结构，config在parsed.config中
      return parsed?.config ?? {};
    } catch (err) {
      return null;
    }
  }, [content]);

  // 键盘导航支持 - 移到顶部避免Hooks顺序问题
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!imagePreviewVisible || !content) return;

      // 获取公司图片数据
      let companyImages = [];
      try {
        const parsed = JSON.parse(content.content || '{}');
        companyImages = parsed?.config?.companyImages || [];
      } catch (e) {
        companyImages = [];
      }

      if (companyImages.length <= 1) return;

      switch (event.key) {
        case 'ArrowLeft':
          setCurrentImageIndex(prev => prev > 0 ? prev - 1 : companyImages.length - 1);
          break;
        case 'ArrowRight':
          setCurrentImageIndex(prev => prev < companyImages.length - 1 ? prev + 1 : 0);
          break;
        case 'Escape':
          setImagePreviewVisible(false);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [imagePreviewVisible, currentImageIndex, content]);

  // 处理加载和错误状态
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" spinning={true}>
          <div className="p-8">公司信息加载中...</div>
        </Spin>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
          <p className="text-gray-600">请刷新页面重试</p>
        </div>
      </div>
    );
  }

  /**
   * 根据 key 从配置中获取值，若不存在返回默认值
   */
  const getAboutValue = (key: string, defaultValue: any): any => {
    if (!parsedConfig || !(key in parsedConfig)) return defaultValue;
    const value = (parsedConfig as any)[key];
    return value !== undefined && value !== null ? value : defaultValue;
  };

  // 从配置中获取公司信息
  const companyInfo = {
    name: getAboutValue('companyName', '复印机维修服务'),
    profile: getAboutValue('companyProfile', '我们是一家专业从事复印机、打印机等办公设备维修服务的企业...'),
    philosophy: getAboutValue('servicePhilosophy', '专业、快速、诚信'),
    advantages: getAboutValue('coreAdvantages', '专业技术团队、24小时响应、质量保证、上门服务')
  };

  const heroTitle = getAboutValue('aboutHeroTitle', '关于我们');
  const heroSubtitle = getAboutValue('aboutHeroSubtitle', `${companyInfo.philosophy}，客户至上`);
  const teamDescription = getAboutValue('aboutTeamDescription', '经验丰富的技师团队，为您提供专业服务');
  
  // CTA配置
  const ctaTitle = getAboutValue('aboutCtaTitle', '需要维修服务？');
  const ctaSubtitle = getAboutValue('aboutCtaSubtitle', '立即联系我们，获得专业的设备维修解决方案');
  const ctaButtonText = getAboutValue('aboutCtaButtonText', '立即联系我们');

  // 移除条件return，确保Hooks调用顺序一致



  // 团队成员数据 - 优先使用动态数据
  const teamMembers = getAboutValue('teamMembers', [
    {
      id: 1,
      name: '张工程师',
      position: '技术总监',
      experience: '10年维修经验',
      specialty: '复印机、打印机故障诊断',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: 2,
      name: '李技师',
      position: '高级技师',
      experience: '8年维修经验',
      specialty: '激光打印机维修',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: 3,
      name: '王师傅',
      position: '资深技师',
      experience: '12年维修经验',
      specialty: '喷墨打印机维修',
      avatar: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: 4,
      name: '陈工程师',
      position: '维修工程师',
      experience: '6年维修经验',
      specialty: '设备保养维护',
      avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face'
    }
  ]);

  // 公司发展历程 - 使用动态数据
  const milestones = getAboutValue('milestones', [
    { year: '2010', title: '2010年', description: '公司成立，专注于复印机维修服务' },
    { year: '2013', title: '2013年', description: '业务扩展至打印机维修和设备保养' },
    { year: '2016', title: '2016年', description: '技师团队扩展到20人，服务能力大幅提升' },
    { year: '2019', title: '2019年', description: '引入先进检测设备，提高维修效率和质量' },
    { year: '2022', title: '2022年', description: '推出24小时快速响应服务，客户满意度显著提升' }
  ]);

  // 公司图片 - 使用动态数据
  const companyImages = getAboutValue('companyImages', [
    { id: 1, url: "https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop", title: "公司前台", description: "现代化的办公环境" },
    { id: 2, url: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop", title: "维修车间", description: "专业的设备维修环境" },
    { id: 3, url: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=600&h=400&fit=crop", title: "团队合影", description: "专业的维修团队" }
  ]);

  // 前端最多展示 4 张图片（1 张主图 + 最多 3 张缩略图）
  const displayedSecondaryImages = companyImages.slice(1, 4);

  // 企业文化数据 - 使用动态数据
  const cultureItems = getAboutValue('cultureItems', [
    { id: 1, icon: "🎯", title: "使命", content: "为客户提供专业、快速、可靠的办公设备维修服务" },
    { id: 2, icon: "👁️", title: "愿景", content: "成为行业领先的办公设备维修服务提供商" },
    { id: 3, icon: "💎", title: "价值观", content: "诚信为本，专业至上，客户第一" }
  ]);



  // 使用动态SEO信息或默认值
  const pageSeoMeta = seoMeta || {
    title: `关于我们 - ${companyInfo.name}`,
    description: `了解${companyInfo.name}的公司历史、团队成员和服务理念`
  };

  // 打开图片预览
  const openImagePreview = (imageUrl: string, imageTitle: string, imageIndex: number) => {
    setPreviewImageUrl(imageUrl);
    setPreviewImageTitle(imageTitle);
    setCurrentImageIndex(imageIndex);
    setImagePreviewVisible(true);
  };

  // 切换到上一张图片
  const goToPrevImage = () => {
    if (companyImages.length <= 1) return;
    const prevIndex = currentImageIndex > 0 ? currentImageIndex - 1 : companyImages.length - 1;
    const prevImage = companyImages[prevIndex];
    if (prevImage) {
      setPreviewImageUrl(prevImage.url);
      setPreviewImageTitle(prevImage.title);
      setCurrentImageIndex(prevIndex);
    }
  };

  // 切换到下一张图片
  const goToNextImage = () => {
    if (companyImages.length <= 1) return;
    const nextIndex = currentImageIndex < companyImages.length - 1 ? currentImageIndex + 1 : 0;
    const nextImage = companyImages[nextIndex];
    if (nextImage) {
      setPreviewImageUrl(nextImage.url);
      setPreviewImageTitle(nextImage.title);
      setCurrentImageIndex(nextIndex);
    }
  };



  // 处理加载和错误状态
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" tip="公司信息加载中..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
          <p className="text-gray-600">请刷新页面重试</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{pageSeoMeta.title}</title>
        <meta name="description" content={pageSeoMeta.description} />
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* 页面头部 */}
        <div className="bg-gray-800 text-white py-16 text-center min-h-[276px]">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">{heroTitle}</h1>
            <p className="text-xl max-w-3xl mx-auto">{heroSubtitle}</p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-6 md:py-12">
          {/* 公司简介 */}
          <section className="mb-16">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
              <div className="font-sans">
                <h2 className="text-[28px] md:text-[32px] font-semibold text-gray-900 tracking-tight mb-8">公司简介</h2>
                <div className="space-y-3 text-gray-700 text-[16px] leading-snug">
                  {companyInfo.profile.split('\n\n').map((paragraph: string, index: number) => (
                    <p key={index}>{paragraph}</p>
                  ))}
                </div>
              </div>
              <div>
                {/* 多图展示 */}
                {companyImages.length === 1 ? (
                  // 单图展示
                  <div className="rounded-lg overflow-hidden shadow-lg cursor-pointer group relative flex flex-col h-72"
                       onClick={() => {
                         openImagePreview(companyImages[0].url, companyImages[0].title, 0);
                       }}>
                    <img 
                      src={companyImages[0].url} 
                      alt={companyImages[0].title}
                      className="w-full h-48 object-cover transition-transform group-hover:scale-105"
                      onError={(e) => {
                        e.currentTarget.src = 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop';
                      }}
                    />
                    {/* 放大图标提示 */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity bg-white bg-opacity-90 rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
                        <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                        </svg>
                      </div>
                    </div>
                    <div className="px-2 py-0.5 bg-white flex-1 flex flex-col justify-center">
                      <h4 className="font-semibold text-sm leading-[1.0] truncate">{companyImages[0].title}</h4>
                      {companyImages[0].description && (
                        <p className="text-gray-600 text-xs leading-[1.0] mt-0.5 line-clamp-2">{companyImages[0].description}</p>
                      )}
                    </div>
                  </div>
                ) : companyImages.length === 2 ? (
                  // 双图展示
                  <div className="space-y-4">
                    {companyImages.map((image: any, index: number) => (
                      <div key={image.url || `image-${index}`} className="rounded-lg overflow-hidden shadow-lg cursor-pointer group relative flex flex-col h-56"
                           onClick={() => {
                             openImagePreview(image.url, image.title, index);
                           }}>
                        <img 
                          src={image.url} 
                          alt={image.title}
                          className="w-full h-36 object-cover transition-transform group-hover:scale-105"
                          onError={(e) => {
                            e.currentTarget.src = 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop';
                          }}
                        />
                        {/* 放大图标提示 */}
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity bg-white bg-opacity-90 rounded-full w-8 h-8 flex items-center justify-center shadow-lg">
                            <svg className="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                            </svg>
                          </div>
                        </div>
                        <div className="px-1.5 py-0.5 bg-white flex-1 flex flex-col justify-center">
                          <h4 className="font-semibold text-xs leading-[1.0] truncate">{image.title}</h4>
                          {image.description && (
                            <p className="text-gray-600 text-[10px] leading-[1.0] mt-0 line-clamp-2">{image.description}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : companyImages.length >= 4 ? (
                  // 四图及以上：统一 2×2 布局，超出数量折叠到最后一张
                  <div className="grid grid-cols-2 gap-4">
                    {companyImages.slice(0, 4).map((image: any, idx: number) => (
                      <div key={image.url || `image-${idx}`} className="rounded-lg overflow-hidden shadow-lg cursor-pointer group relative flex flex-col h-56"
                           onClick={() => {
                             openImagePreview(image.url, image.title, idx);
                           }}>
                        <img
                          src={image.url}
                          alt={image.title}
                          className="w-full h-36 object-cover transition-transform group-hover:scale-105"
                          onError={(e) => {
                            e.currentTarget.src = 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop';
                          }}
                        />
                        {/* 放大图标提示 */}
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity bg-white bg-opacity-90 rounded-full w-8 h-8 flex items-center justify-center shadow-lg">
                            <svg className="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                            </svg>
                          </div>
                        </div>
                        {/* 如果图片超过 4 张，在最后一个格子叠加折叠层 */}
                        {companyImages.length > 4 && idx === 3 && (
                          <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                            <span className="text-white text-lg font-medium">+{companyImages.length - 4}</span>
                          </div>
                        )}
                        <div className="px-1.5 py-0.5 bg-white flex-1 flex flex-col justify-center">
                          <h4 className="font-semibold text-xs leading-[1.0] truncate" title={image.title}>{image.title}</h4>
                          {image.description && (
                            <p className="text-gray-600 text-[10px] leading-[1.0] mt-0 line-clamp-2">{image.description}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  // 三图展示 (保留主大图 + 两张小图)
                  <div className="grid grid-cols-2 gap-4">
                    {/* 主图 */}
                    <div className="col-span-2 rounded-lg overflow-hidden shadow-lg cursor-pointer group relative flex flex-col h-72"
                         onClick={() => {
                           openImagePreview(companyImages[0].url, companyImages[0].title, 0);
                         }}>
                      <img 
                        src={companyImages[0].url} 
                        alt={companyImages[0].title}
                        className="w-full h-48 object-cover transition-transform group-hover:scale-105"
                        onError={(e) => {
                          e.currentTarget.src = 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop';
                        }}
                      />
                      {/* 放大图标提示 */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity bg-white bg-opacity-90 rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
                          <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                          </svg>
                        </div>
                      </div>
                      <div className="px-2 py-0.5 bg-white flex-1 flex flex-col justify-center">
                        <h4 className="font-semibold text-sm leading-[1.0] truncate">{companyImages[0].title}</h4>
                        {companyImages[0].description && (
                          <p className="text-gray-600 text-xs leading-[1.0] mt-0.5 line-clamp-2">{companyImages[0].description}</p>
                        )}
                      </div>
                    </div>
                    {/* 副图（最多 3 张）*/}
                    {displayedSecondaryImages.map((image: any, idx: number) => (
                      <div key={image.url || `secondary-${idx}`} className="rounded-lg overflow-hidden shadow-lg cursor-pointer group relative flex flex-col h-56"
                           onClick={() => {
                             openImagePreview(image.url, image.title, idx + 1);
                           }}>
                        <img 
                          src={image.url} 
                          alt={image.title}
                          className="w-full h-36 object-cover transition-transform group-hover:scale-105"
                          onError={(e) => {
                            e.currentTarget.src = 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop';
                          }}
                        />
                        {/* 放大图标提示 */}
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity bg-white bg-opacity-90 rounded-full w-8 h-8 flex items-center justify-center shadow-lg">
                            <svg className="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                            </svg>
                          </div>
                        </div>
                        {/* 当总数 >4 但走不到这里（3 图分支）无折叠 */}
                        <div className="px-1.5 py-0.5 bg-white flex-1 flex flex-col justify-center">
                          <h4 className="font-semibold text-xs leading-[1.0] truncate">{image.title}</h4>
                          {image.description && (
                            <p className="text-gray-600 text-[10px] leading-[1.0] mt-0 line-clamp-2">{image.description}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </section>

          {/* 企业文化 */}
          <section className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">企业文化</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {cultureItems.map((culture: any, index: number) => (
                <div key={culture.id || `culture-${index}`} className="text-center bg-white rounded-lg shadow-lg p-8">
                  <div className="text-4xl mb-4">{culture.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{culture.title}</h3>
                <p className="text-gray-600">
                    {culture.content}
                </p>
              </div>
              ))}
            </div>
          </section>

          {/* 发展历程 */}
          <section className="mb-16">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">发展历程</h2>
              <p className="text-gray-500 text-lg">见证我们的成长与创新之路</p>
            </div>
              
            {/* 苹果风格卡片布局 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 items-start">
                {milestones.map((milestone: any, index: number) => (
                <div key={milestone.year} className="group h-full">
                  {/* 苹果风格卡片 - 修复圆角变直角问题 */}
                  <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 perspective-1000 transform-gpu h-full flex flex-col" style={{ transformStyle: 'preserve-3d' }}>
                    {/* 图片区域 */}
                    {milestone.image && (
                      <div className="relative overflow-hidden rounded-t-2xl flex-shrink-0 cursor-pointer"
                           onClick={(e) => {
                             e.preventDefault();
                             e.stopPropagation();
                             setPreviewImageUrl(milestone.image);
                             setPreviewImageTitle(`${milestone.year}年 - ${milestone.description}`);
                             setImagePreviewVisible(true);
                           }}>
                        <img 
                          src={milestone.image} 
                          alt={`${milestone.year}年里程碑`}
                          className="w-full h-44 object-cover transition-transform duration-500 group-hover:scale-110 pointer-events-none"
                          onError={(e) => {
                            e.currentTarget.src = 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=300&fit=crop';
                          }}
                        />
                        {/* 苹果风格的渐变叠加 */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />

                        {/* 放大图标 - 苹果风格 */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
                          <div className="bg-white/90 backdrop-blur-sm rounded-full w-12 h-12 flex items-center justify-center shadow-lg transform scale-90 group-hover:scale-100 transition-transform duration-200">
                            <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* 内容区域 - 优化布局和换行 */}
                    <div className="p-5 flex-grow flex flex-col">
                      {/* 年份标签减少留白 */}
                      <div className="mb-3">
                        <div className="flex justify-center">
                          <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg px-3 py-1.5 shadow-md" style={{ background: 'linear-gradient(to bottom right, #3b82f6, #9333ea)' }}>
                            <span className="text-white font-semibold text-lg tracking-wide">{milestone.year}</span>
                          </div>
                        </div>
                      </div>
                      
                      {/* 文字内容区域 */}
                      <div className="flex-grow">
                        <div className="text-center">
                          <p className="text-gray-700 leading-relaxed text-base font-medium break-words hyphens-auto mb-4">
                            {milestone.description}
                          </p>
                          
                          {/* 苹果风格的装饰线 */}
                          <div className="flex justify-center">
                            <div className="w-12 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  </div>
                ))}
            </div>
          </section>

          {/* 团队介绍 */}
          <section className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">专业团队</h2>
              <p className="text-gray-600">{teamDescription}</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {teamMembers.map((member: any, index: number) => (
                <div key={member.id || `member-${index}`} className="text-center bg-white rounded-lg shadow-lg p-6">
                  <div className="w-24 h-24 rounded-full mx-auto mb-4 overflow-hidden">
                    <img
                      src={member.avatar}
                      alt={member.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face';
                      }}
                    />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">{member.name}</h3>
                  <p className="text-blue-600 font-medium mb-2">{member.position}</p>
                  <p className="text-sm text-gray-600 mb-1">{member.experience}</p>
                  <p className="text-sm text-gray-500">{member.specialty}</p>
                </div>
              ))}
            </div>
          </section>

          {/* CTA Section */}
          <section className="text-center bg-blue-600 text-white rounded-lg p-12">
            <h2 className="text-3xl font-bold mb-4">{ctaTitle}</h2>
            <p className="text-xl mb-8">{ctaSubtitle}</p>
            <a href="/contact" className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              {ctaButtonText}
            </a>
          </section>
        </div>
      </div>

      {/* 图片放大预览模态框 - 移动端优化 */}
      <Modal
        title={
          <div className="flex justify-between items-center pr-8">
            <span className="truncate mr-4">{previewImageTitle || "图片预览"}</span>
            {companyImages.length > 1 && companyImages.some((img: any) => img.url === previewImageUrl) && (
              <span className="text-sm font-normal text-gray-500 whitespace-nowrap">
                {currentImageIndex + 1} / {companyImages.length}
              </span>
            )}
          </div>
        }
        open={imagePreviewVisible}
        onCancel={() => setImagePreviewVisible(false)}
        footer={null}
        width="95vw"
        style={{ 
          maxWidth: '1200px'
        }}
        centered={true} // 移动端和桌面端都居中
        className="image-preview-modal"
        destroyOnHidden
      >
        <div className="text-center p-2 md:p-4 relative">
          {/* 导航按钮 - 左 - 只在查看公司图片时显示 */}
          {companyImages.length > 1 && companyImages.some((img: any) => img.url === previewImageUrl) && (
            <button
              onClick={goToPrevImage}
              className="absolute left-2 md:left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-90 text-gray-700 rounded-full p-2 md:p-3 z-10 transition-all shadow-lg border-0 outline-none focus:outline-none"
              aria-label="上一张图片"
            >
              <svg className="w-4 h-4 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}
          
          {/* 导航按钮 - 右 - 只在查看公司图片时显示 */}
          {companyImages.length > 1 && companyImages.some((img: any) => img.url === previewImageUrl) && (
            <button
              onClick={goToNextImage}
              className="absolute right-2 md:right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-90 text-gray-700 rounded-full p-2 md:p-3 z-10 transition-all shadow-lg border-0 outline-none focus:outline-none"
              aria-label="下一张图片"
            >
              <svg className="w-4 h-4 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}

          <img 
            src={previewImageUrl}
            alt={previewImageTitle}
            className="max-w-full max-h-[80vh] md:max-h-[70vh] object-contain rounded shadow-lg cursor-zoom-out mx-auto"
            onError={(e) => {
              e.currentTarget.src = 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop';
            }}
            onClick={() => setImagePreviewVisible(false)}
          />
          
          <div className="mt-3 md:mt-4">
            <h3 className="text-lg md:text-xl font-semibold text-gray-800 mb-1">
              {previewImageTitle}
            </h3>
            <p className="text-gray-500 text-sm md:text-base">
              点击图片或外区域关闭预览
            </p>
            {companyImages.length > 1 && companyImages.some((img: any) => img.url === previewImageUrl) && (
              <p className="text-gray-400 text-xs md:text-sm mt-1">
                ← → 键切换图片 | ESC 键关闭
              </p>
            )}
            <p className="text-gray-400 text-xs md:text-sm mt-1">
              💡 移动端：双指缩放可查看更多细节
            </p>
          </div>
        </div>
      </Modal>
    </>
  );
} 