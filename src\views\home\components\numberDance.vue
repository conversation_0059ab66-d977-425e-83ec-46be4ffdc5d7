<template>
  <div class="number-dance">
    <span v-for="(digit, index) in digits" :key="index" class="digit-container">
      <div
        class="digit-column"
        :class="{ 'initial-animation': isInitialLoad }"
        :style="{ transform: `translateY(-${digit * 10}%)` }"
      >
        <span v-for="n in 10" :key="n">{{ n - 1 }}</span>
      </div>
    </span>
  </div>
</template>

<script>
export default {
  name: "NumberDance",
  props: {
    value: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      isInitialLoad: true,
    };
  },
  computed: {
    digits() {
      return String(Math.floor(this.value || 0))
        .padStart(1, "0")
        .split("")
        .map(Number);
    },
  },
  mounted() {
    setTimeout(() => {
      this.isInitialLoad = false;
    }, 1000);
  },
};
</script>

<style scoped>
.number-dance {
  display: inline-flex;
}

.digit-container {
  height: 1em;
  overflow: hidden;
}

.digit-column {
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease-out;
}

/* 初始加载动画 */
.digit-column.initial-animation {
  transform: translateY(-1000%);
  transition: transform 1s cubic-bezier(0.23, 1, 0.32, 1);
}

.digit-column span {
  height: 1em;
  line-height: 1em;
}
</style>
