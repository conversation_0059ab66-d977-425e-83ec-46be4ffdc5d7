/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-18 16:44:05
 * @Description:
 */

import { get, post, put, del } from "@/utils/request";
//   前缀用来识别权限ip

//=========登陆流程=========
export const captcha = (data) => post("/magina/anno/captcha", data); //验证码生成
export const key = (data) => post("/magina/anno/key", data); //生成加密令牌及公钥
export const login = (data) => post("/magina/system/login", data); //登陆
export const verifyLogin = (data) => post("/clientInfoApi/login", data); // 快速登录
export const resources = () => get("/magina/system/resources"); //登录用户资源树
export const getInfo = () => get("/magina/department-user/list"); //获取用户所在部门
export const logout = () => put("/magina/system/logout"); //登出
// 设备管理人员
export const deviceManager = () =>
  get(
    "/magina/manage/user-role/member-page/1605103185486278658?pageNumber=1&pageSize=2000"
  ); //登录用户资源树
export const getTerminals = () => get("/magina/manage/resource/terminals"); //获取终端类型

//=========菜单管理=========
export const menuListApi = (type, data) =>
  get(`/magina/manage/resource/${type}`, data); // 分页查询
export const menuAddApi = (data) => post("/magina/manage/resource", data); // 新增
export const menuEditApi = (data) => put("/magina/manage/resource", data); // 修改
export const menuDelApi = (id) => del(`/magina/manage/resource/${[id]}`); // 删除
//=========角色管理=========
export const roleListApi = (data) => get("/magina/manage/role", data); // 分页查询
export const roleAddApi = (data) => post("/magina/manage/role", data); // 新增
export const roleEditApi = (data) => put("/magina/manage/role", data); // 修改
export const roleDelApi = (id) => del(`/magina/manage/role/${[id]}`); // 删除
export const roleMemberApi = (id, data) =>
  get(`/magina/manage/user-role/member-page/${id}`, data); // 角色人员列表\
export const dictMemberApi = (id, data) =>
  get(`dict-extend/member-page/${id}`, data); // 无权限角色人员列表
// 根据编码查询工程师信息
export const getRoleByCode = (code, data) =>
  get(`/customer/${code}/page`, data);
export const roleMemberDelApi = (id, data) =>
  del(`/magina/manage/user-role/${id}/${[data]}`); // 移除角色人员
export const roleMemberPageApi = (id, data) =>
  get(`/magina/manage/user-role/add-member-page/${id}`, data); // 可选择角色人员
export const roleMemberAddApi = (id, data) =>
  post(`/magina/manage/user-role/${id}`, data); // 新增角色人员
export const roleLimitGetApi = (id) =>
  get(`/magina/manage/role-resource/${id}`); // 获取权限
export const roleLimitApi = (id, data) =>
  post(`/magina/manage/role-resource/${id}`, data); // 配置权限

//=========部门管理=========
export const departListApi = (data) => get("/magina/manage/department", data); // 分页查询
export const departAddApi = (data) => post("/magina/manage/department", data); // 新增
export const departEditApi = (data) => put("/magina/manage/department", data); // 修改
export const departDelApi = (id) => del(`/magina/manage/department/${[id]}`); // 删除
export const departMemberApi = (id, data) =>
  get(`/magina/manage/department-user/member-page/${id}`, data); // 角色人员列表
export const departMemberDelApi = (id, data) =>
  del(`/magina/manage/department-user/${id}/${[data]}`); // 移除角色人员
export const departMemberPageApi = (id, data) =>
  get(`/magina/manage/department-user/add-member-page/${id}`, data); // 可选择角色人员
export const departMemberAddApi = (id, data) =>
  post(`/magina/manage/department-user/${id}`, data); // 新增角色人员
//=========系统用户=========
export const userListApi = (data) => get("/magina/manage/user", data); // 分页查询
export const userAddApi = (data) => post("/magina/manage/user", data); // 新增
export const userEditApi = (id, data) => put(`/magina/manage/user/${id}`, data); // 修改
export const userDelApi = (id) => del(`/magina/manage/user/${[id]}`); // 删除
export const userPwdApi = (data) =>
  post("/magina/manage/user/reset-password", data); // 重置密码
export const activeUserApi = (id) => put(`/magina/manage/user/activate/${id}`); // 激活账户
// 编辑密码
export const editPwdApi = (data) => post("/user-extends/update-password", data);
// 修改密码
export const editPwdApi2 = (data) => put("/magina/user/change-password", data);

//=========日志管理=========
export const logListApi = (data) => get("/magina/behavior/operation-log", data); // 分页查询

//=========字典管理=========
export const dictListApi = (data) => get("/magina/code/dict", data); // 分页查询
export const dictAddApi = (data) => post("/magina/code/dict", data); // 新增
export const dictEditApi = (data) => put("/magina/code/dict", data); // 修改
export const dictDelApi = (id) => del(`/magina/code/dict/${[id]}`); // 删除

// {
//   description
//   :
//   "1111",
//     dictId
//   :
//   "1684850374592221185",
//     label
//   :
//   "11",
//     sort
//   :
//   "1",
//     value
//   :
//   "11"
// }
export const dictoptListApi = (id, data) =>
  get(`/magina/code/dict-item/${id}`, data); // 字典项分页
export const dictoptAddApi = (data) => post("/magina/code/dict-item", data); // 新增
export const dictoptEditApi = (data) => put("/magina/code/dict-item", data); // 修改
export const dictoptDelApi = (id) => del(`/magina/code/dict-item/${[id]}`); // 删除

export const dictListByCodeApi = (code) =>
  get(`/magina/api/code/dict-item/${code}/list`);
export const dictTreeByCodeApi = (code) =>
  get(`/magina/api/code/dict-item/${code}/tree`);

export const dictTreeByCodeApi2 = (code, item) =>
  get(`/dict-extend/itemList/${code}/${item}`);

// export const deviceTreeApi = (data) => get('/worker/device/device-type-dict/tree', data)// 设备分类树列表
// export const devicePageApi = (data) => get('/worker/device/device-info-dict/page', data)// 设备信息分页查询
// export const deviceUseListApi = () => get('/worker/device/device-info-dict/list?status=2')// 可用设备信息查询

// export const deviceInfoApi = (data) => get(`/worker/device/device-info-dict/detail`, data) // 设备信息详情查询
// export const deviceSaveApi = (data) => post('/worker/device/device-info-dict', data)//设备保存
// export const deldeviceApi = (data) => post(`/worker/device/device-info-dict/batch`, data)//删除设备信息
//查字典
export const sexApi = (data) => get("/magina/manage/user/enum/sex", data); // 性别
export const menuTypeApi = (data) =>
  get("/magina/manage/resource/enum/type", data); // 路由类型
export const userTypeApi = (data) => get("/magina/manage/user/enum/type", data); // 用户类型
export const usersListApi = (data) => get("magina/api/user/list", data); // 用户类型

export const userDropApi = (id) =>
  get(`/customer-device-group/group-name-drop-down/${id}`); // 用户类型

// ==============================  平台属性  ==============================
// 分页查询
export const platformListApi = (data) => get("/magina/code/property", data);
// 添加
export const platformAddApi = (data) => post("/magina/code/property", data);
// 修改
export const platformEditApi = (data) => put("/magina/code/property", data);
// 删除
export const platformDelApi = (id) => del(`/magina/code/property/${id}`);

// ============================== 意向客户 ==============================
export const intentionListApi = (data) => post("/clientInfoApi/page", data);
// 编辑意向客户内容
export const intentionEditApi = (data) => put("/clientInfoApi", data);
