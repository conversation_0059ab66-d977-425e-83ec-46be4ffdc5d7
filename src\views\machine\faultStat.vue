<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #productId>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          collapse-tags
          @change="handleSelectForm"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { productAllApi } from "@/api/dispose";

export default {
  name: "FaultStat",
  data() {
    return {
      productIdName: "",
      options: [],
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "desc",
          title: "故障描述",
          isTable: true,
          isSearch: true,
          valueType: "input",
          inputType: "area",
          width: 300,
        },
        {
          dataIndex: "code",
          title: "代码",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isSearch: true,
          searchSlot: "productId",
        },
        {
          dataIndex: "series",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "次数",
          isTable: true,
        },
      ],
      tableData: [],
    };
  },
  mounted() {
    this.refresh();
    this.init();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
    },

    handleSelectForm(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
    refresh() {
      this.$refs.ProTable.listLoading = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
