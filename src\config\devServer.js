/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-08 12:03:24
 * @Description: http请求代理配置
 */

module.exports = {
  port: 8888,
  historyApiFallback: true,
  // progress: false,
  proxy: {
    "/api": {
      //功能ip
      target: "https://plat.sczjzy.com.cn/api",
      // target: "http://*************:8080/api",
      changeOrigin: true,
      ws: false,
      pathRewrite: {
        "^/api": "",
      },
    },
  },
};
