<template>
  <div class="website-inquiries">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div>
          <h1 class="page-title">
            <i class="el-icon-message" style="margin-right: 8px;"></i>
            咨询管理
          </h1>
          <p class="page-description">管理客户咨询和反馈信息</p>
        </div>
        <div class="header-actions">
          <el-button type="default" icon="el-icon-refresh" @click="refreshData" :loading="loading">
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :xs="24" :sm="12" :lg="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value" style="color: #1890ff;">{{ statistics?.totalCount || 0 }}</div>
            <div class="stat-label">总咨询数</div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :lg="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value" style="color: #fa8c16;">{{ statistics?.pendingCount || 0 }}</div>
            <div class="stat-label">待处理</div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :lg="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-value" style="color: #52c41a;">{{ statistics?.todayCount || 0 }}</div>
            <div class="stat-label">今日新增</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 咨询列表 -->
    <el-card class="inquiries-table-card">
      <!-- 筛选条件 -->
      <div class="filter-container">
        <el-form :model="filters" :inline="true" class="filter-form">
          <el-form-item label="状态">
            <el-select v-model="filters.status" placeholder="全部状态" style="width: 120px" clearable @change="handleFilterChange">
              <el-option label="待处理" value="PENDING"></el-option>
              <el-option label="已处理" value="COMPLETED"></el-option>
              <el-option label="已关闭" value="CLOSED"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <el-table
        :data="inquiries"
        v-loading="loading"
        stripe
        style="width: 100%"
        table-layout="fixed"
      >
        <el-table-column type="index" label="序号" width="60" :index="getRowIndex" />

        <el-table-column prop="company" label="店铺名称" width="180">
          <template #default="{ row }">
            <span class="font-medium">{{ row.company || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="姓名" width="100">
          <template #default="{ row }">
            <span class="font-medium">{{ row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="phone" label="联系电话" width="140">
          <template #default="{ row }">
            <div style="display: flex; align-items: center;">
              <i class="el-icon-phone" style="margin-right: 4px;"></i>
              {{ row.phone }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="subject" label="服务类型" width="190">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.subject || '其它' }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="咨询时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column prop="content" label="咨询内容" width="250" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="content-cell">
              {{ row.content || '无内容' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="updatedAt" label="处理时间" width="160">
          <template #default="{ row }">
            {{ row.updatedAt ? formatDate(row.updatedAt) : '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="replyContent" label="处理内容" width="250" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="content-cell">
              {{ row.replyContent || '-' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="repliedBy" label="操作人" width="100">
          <template #default="{ row }">
            {{ row.repliedBy && row.repliedBy.name ? row.repliedBy.name : '-' }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180">
          <template #default="{ row }">
            <div style="display: flex; gap: 8px;">
              <el-button
                type="text"
                icon="el-icon-view"
                size="small"
                @click="viewInquiry(row)"
              >
                查看
              </el-button>
              <el-button
                v-if="row.status !== 'COMPLETED' && row.status !== 'CLOSED'"
                type="text"
                icon="el-icon-message"
                size="small"
                @click="replyInquiry(row)"
              >
                处理
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="咨询详情"
      :visible.sync="detailDialogVisible"
      width="700px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal-append-to-body="true"
      custom-class="inquiry-detail-dialog"
    >
      <div v-if="selectedInquiry" class="inquiry-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">{{ selectedInquiry.name }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ selectedInquiry.phone }}</el-descriptions-item>
          <el-descriptions-item label="服务类型">{{ selectedInquiry.subject || '无主题' }}</el-descriptions-item>
          <el-descriptions-item label="状态" :span="2">
            <el-tag :type="getStatusType(selectedInquiry.status)">
              {{ getStatusText(selectedInquiry.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="咨询内容" :span="2">
            <div class="content-text">{{ selectedInquiry.content || '-' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="咨询时间">{{ formatDate(selectedInquiry.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间" :span="2">
            {{ selectedInquiry.updatedAt ? formatDate(selectedInquiry.updatedAt) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="处理内容" :span="2">
            <div class="content-text reply-content-bg">
              {{ selectedInquiry.replyContent || '-' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="操作人" :span="2">
            {{ selectedInquiry.repliedBy && selectedInquiry.repliedBy.name ? `${selectedInquiry.repliedBy.code || ''} / ${selectedInquiry.repliedBy.name}` : '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          type="primary"
          @click="replyInquiry(selectedInquiry)"
          v-if="selectedInquiry && selectedInquiry.status !== 'COMPLETED' && selectedInquiry.status !== 'CLOSED'"
        >
          处理
        </el-button>
      </template>
    </el-dialog>

    <!-- 回复咨询对话框 -->
    <el-dialog
      title="处理咨询"
      :visible.sync="replyDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :append-to-body="true"
      :modal-append-to-body="true"
      custom-class="inquiry-reply-dialog"
    >
      <el-form ref="replyForm" :model="replyForm" :rules="replyRules" label-width="100px">
        <el-form-item label="处理状态" prop="status">
          <el-select v-model="replyForm.status" placeholder="请选择处理状态">
            <el-option label="已处理" value="COMPLETED"></el-option>
            <el-option label="已关闭" value="CLOSED"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="处理内容" prop="replyContent">
          <el-input
            type="textarea"
            v-model="replyForm.replyContent"
            :rows="4"
            placeholder="请输入处理内容..."
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="replyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitReply" :loading="submitting">确认处理</el-button>
      </template>
    </el-dialog>


  </div>
</template>

<script>
import { websiteInquiryApi } from '@/websites/api/website'

export default {
  name: 'WebsiteInquiries',
  data() {
    return {
      loading: false,
      submitting: false,
      detailDialogVisible: false,
      replyDialogVisible: false,

      selectedInquiry: null,

      // 咨询列表和统计数据
      inquiries: [],
      statistics: null,

      // 分页信息
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },

      // 筛选条件
      filters: {
        status: ''
      },

      // 回复表单
      replyForm: {
        status: '',
        replyContent: ''
      },

      // 表单验证规则
      replyRules: {
        status: [
          { required: true, message: '请选择处理状态', trigger: 'change' }
        ],
        replyContent: [
          { required: true, message: '请输入处理内容', trigger: 'blur' }
        ]
      }
    }
  },
  
  mounted() {
    this.loadInquiries()
    this.loadStatistics()
  },
  
  methods: {
    // 加载咨询列表
    async loadInquiries() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.size,
          ...this.getFilterParams()
        }

        const response = await websiteInquiryApi.getInquiryPage(params)
        // 处理API返回的数据结构
        if (response.data && response.data.rows) {
          this.inquiries = response.data.rows || []
          this.pagination.total = parseInt(response.data.total) || 0
        } else if (response.data && response.data.records) {
          this.inquiries = response.data.records || []
          this.pagination.total = response.data.total || 0
        } else {
          this.inquiries = []
          this.pagination.total = 0
        }

      } catch (error) {
        console.error('加载咨询列表失败:', error)
        this.$message.error('加载数据失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        const response = await websiteInquiryApi.getInquiryStatistics()
        const data = response.data

        // 计算总咨询数（与React项目保持一致）
        const totalCount = Object.values(data.statusCount || {}).reduce((sum, val) => {
          return sum + parseInt(val || '0', 10)
        }, 0)

        this.statistics = {
          totalCount,
          pendingCount: parseInt(data.pendingCount || '0', 10),
          todayCount: parseInt(data.todayCount || '0', 10)
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
        // 设置默认值
        this.statistics = {
          totalCount: 0,
          pendingCount: 0,
          todayCount: 0
        }
      }
    },

    // 获取筛选参数
    getFilterParams() {
      const params = {}
      if (this.filters.status) params.status = this.filters.status
      return params
    },

    // 刷新数据
    refreshData() {
      this.loadInquiries()
      this.loadStatistics()
    },

    // 筛选条件变化
    handleFilterChange() {
      this.pagination.current = 1
      this.loadInquiries()
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadInquiries()
    },

    // 当前页变化
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadInquiries()
    },

    // 查看咨询详情
    viewInquiry(inquiry) {
      this.selectedInquiry = inquiry
      this.detailDialogVisible = true
    },

    // 回复咨询
    replyInquiry(inquiry) {
      this.selectedInquiry = inquiry
      this.replyForm = {
        status: 'COMPLETED',
        replyContent: ''
      }
      this.replyDialogVisible = true
      this.detailDialogVisible = false
    },

    // 提交回复
    async submitReply() {
      try {
        await this.$refs.replyForm.validate()

        this.submitting = true

        await websiteInquiryApi.updateInquiry(this.selectedInquiry.id, {
          id: this.selectedInquiry.id,
          status: this.replyForm.status,
          reply: this.replyForm.replyContent
        })

        this.$message.success('处理提交成功')
        this.replyDialogVisible = false
        this.loadInquiries() // 刷新列表
        this.loadStatistics() // 刷新统计

      } catch (error) {
        console.error('提交处理失败:', error)
        this.$message.error('提交失败，请稍后重试')
      } finally {
        this.submitting = false
      }
    },



    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'PENDING': 'warning',
        'PROCESSING': 'primary',
        'COMPLETED': 'success',
        'CLOSED': 'info'
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'PENDING': '待处理',
        'PROCESSING': '处理中',
        'COMPLETED': '已处理',
        'CLOSED': '已关闭'
      }
      return statusMap[status] || '未知'
    },



    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString()
    },

    // 截断文本
    truncateText(text, maxLength) {
      if (!text) return '-'
      const str = text.toString()
      return str.length > maxLength ? str.slice(0, maxLength) + '…' : str
    },

    // 获取行序号
    getRowIndex(index) {
      return (this.pagination.current - 1) * this.pagination.size + index + 1
    }
  }
}
</script>

<style lang="scss" scoped>
.website-inquiries {
  padding: 24px;

  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      .page-description {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .stats-row {
    margin-bottom: 24px;
  }

  .stat-card {
    border-radius: 8px;
  }

  .stat-content {
    padding: 16px;
    text-align: left;
  }

  .stat-value {
    font-size: 24px;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 14px;
    color: #8c8c8c;
  }

  .filter-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  .filter-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }



  .content-cell {
    width: 100%;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;
    word-break: break-all;
    display: block;
  }

  .inquiries-table-card {
    border-radius: 8px;

    .pagination-wrapper {
      margin-top: 16px;
      text-align: right;
    }
  }

  .font-medium {
    font-weight: 500;
  }

  .inquiry-detail {
    .content-text {
      white-space: pre-wrap;
      word-break: break-words;
      word-wrap: break-word;
      line-height: 1.6;
      min-height: auto;
      max-width: 100%;
      overflow-wrap: break-word;
      hyphens: auto;
    }

    .reply-content-bg {
      background-color: #eff6ff; /* 对应 bg-blue-50 */
      padding: 12px; /* 对应 p-3 */
      border-radius: 6px; /* 对应 rounded */
    }

    // 确保descriptions组件的内容区域能够自适应高度
    ::v-deep .el-descriptions__body .el-descriptions__table .el-descriptions__cell {
      vertical-align: top;
    }

    ::v-deep .el-descriptions__content {
      word-break: break-words;
      white-space: normal;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .website-inquiries {
    padding: 16px;

    .page-header .header-content {
      flex-direction: column;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .stats-row {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .filter-card .filter-form {
      .el-form-item {
        display: block;
        margin-bottom: 16px;

        .el-form-item__content {
          margin-left: 0 !important;
        }
      }
    }
  }
}

/* 修复对话框蒙版问题 - 使用具体的类名选择器 */
::v-deep .inquiry-detail-dialog,
::v-deep .inquiry-reply-dialog {
  z-index: 9999 !important;
}

::v-deep .inquiry-detail-dialog .el-dialog__wrapper,
::v-deep .inquiry-reply-dialog .el-dialog__wrapper {
  z-index: 9999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

::v-deep .inquiry-detail-dialog .el-overlay,
::v-deep .inquiry-reply-dialog .el-overlay {
  z-index: 9998 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* 确保对话框内容可以正常交互 */
::v-deep .inquiry-detail-dialog .el-dialog__body,
::v-deep .inquiry-reply-dialog .el-dialog__body,
::v-deep .inquiry-detail-dialog .el-dialog__footer,
::v-deep .inquiry-reply-dialog .el-dialog__footer {
  position: relative;
  z-index: 10001 !important;
  pointer-events: auto !important;
}

/* 确保按钮可以正常点击 */
::v-deep .inquiry-detail-dialog .el-button,
::v-deep .inquiry-reply-dialog .el-button {
  pointer-events: auto !important;
  z-index: 10002 !important;
  position: relative;
}
</style>
