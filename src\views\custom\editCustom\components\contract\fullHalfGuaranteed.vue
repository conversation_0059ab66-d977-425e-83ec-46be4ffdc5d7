<!--
 * @Author: wskg
 * @Date: 2025-01-15 15:55:03
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-08 14:09:15
 * @Description: 全/半保合约
 -->
<template>
  <div class="full-half-guaranteed">
    <div class="title-box" style="margin-top: 0">服务选项</div>
    <el-form
      ref="formRef"
      :model="infoData"
      :rules="formRules"
      label-width="140px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="合约开始时间：" prop="startTime">
            <el-date-picker
              v-model="infoData.startTime"
              style="width: 100%"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择合约开始时间"
              :disabled="editType === 'info'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="合约截止时间：" prop="endTime">
            <el-date-picker
              v-model="infoData.endTime"
              style="width: 100%"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择合约截止时间"
              :disabled="editType === 'info'"
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="
            serviceType === 'RENT_HALF' ||
            serviceType === 'RENT_FULL' ||
            serviceType === 'FINANCING_HALF' ||
            serviceType === 'FINANCING_FULL'
          "
          :span="6"
        >
          <el-form-item label="预付款：" prop="prepayment">
            <el-input
              v-model="infoData.prepayment"
              type="number"
              :min="0"
              placeholder="请输入预付款"
              :disabled="editType === 'info'"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" style="padding-left: 0; padding-right: 0">
          <el-col :span="6">
            <el-form-item label="期初黑白计数器：" prop="signBlackWhiteCounter">
              <!--当editType === 'info'不可编辑。当editType不等于info时，contractType等于1202才可以编辑-->
              <el-input
                v-model="infoData.signBlackWhiteCounter"
                type="number"
                :min="0"
                placeholder="请输入期初黑白计数器"
                :disabled="
                  editType === 'info' ||
                  (editType !== 'info' && contractType !== '1202')
                "
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="期初彩色计数器：" prop="signColoursCounter">
              <el-input
                v-model="infoData.signColoursCounter"
                type="number"
                :min="0"
                placeholder="请输入期初彩色计数器"
                :disabled="
                  editType === 'info' ||
                  (editType !== 'info' && contractType !== '1202')
                "
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="期初五色计数器："
              prop="signFiveColoursCounter"
            >
              <el-input
                v-model="infoData.signFiveColoursCounter"
                type="number"
                :min="0"
                placeholder="请输入期初五色计数器"
                :disabled="
                  editType === 'info' ||
                  (editType !== 'info' && contractType !== '1202')
                "
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计数方式：" prop="paperType">
              <el-select
                v-model="infoData.paperType"
                :disabled="editType === 'info'"
              >
                <el-option
                  v-for="item in [
                    {
                      label: 'A4',
                      value: 'A4',
                    },
                    {
                      label: 'A3',
                      value: 'A3',
                    },
                  ]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-col>
        <!-- 包量半包\全包显示 -->
        <!--<div>-->
        <ResponsibleService
          v-if="serviceType === 'PACKAGE_HALF' || serviceType === 'PACKAGE_ALL'"
          v-model="infoData"
          :edit-type="editType"
        />
        <!--</div>-->
        <!-- 废张计算方式 -->
        <!--v-if="getVisibleFields()" 包量也计算废张-->
        <div>
          <el-col :span="24">
            <el-form-item label="废张计算方式：" prop="wasteType">
              <el-radio-group
                v-model="infoData.wasteType"
                :disabled="editType === 'info'"
              >
                <el-radio label="ACTUAL">工单计张</el-radio>
                <el-radio label="FIXED">固定数量</el-radio>
                <el-radio label="SCALE">比例核算</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            v-if="infoData.wasteType === 'FIXED'"
            :span="6"
            prop="bwWasteNumber"
          >
            <el-form-item label="黑色废张数：" prop="bwWasteNumber">
              <el-input
                v-model="infoData.bwWasteNumber"
                type="number"
                :min="0"
                placeholder="黑色废张数"
                :disabled="editType === 'info'"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.wasteType === 'FIXED'" :span="6">
            <el-form-item label="彩色废张数：" prop="colorWasteNumber">
              <el-input
                v-model="infoData.colorWasteNumber"
                type="number"
                :min="0"
                placeholder="彩色废张数"
                :disabled="editType === 'info'"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.wasteType === 'FIXED'" :span="6">
            <el-form-item label="第五色废张数：" prop="fiveWasteNumber">
              <el-input
                v-model="infoData.fiveWasteNumber"
                type="number"
                :min="0"
                placeholder="第五色废张数"
                :disabled="editType === 'info'"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.wasteType === 'SCALE'" :span="6">
            <el-form-item label="黑色废张比例：" prop="bwWasteScale">
              <el-input
                v-model="infoData.bwWasteScale"
                type="number"
                :min="0"
                placeholder="黑色废张比例"
                :disabled="editType === 'info'"
              >
                <template #suffix>千分比</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.wasteType === 'SCALE'" :span="6">
            <el-form-item label="彩色废张比例：" prop="colorWasteScale">
              <el-input
                v-model="infoData.colorWasteScale"
                type="number"
                :min="0"
                placeholder="彩色废张比例"
                :disabled="editType === 'info'"
              >
                <template #suffix>千分比</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.wasteType === 'SCALE'" :span="6">
            <el-form-item label="第五色废张比例：" prop="fiveWasteScale">
              <el-input
                v-model="infoData.fiveWasteScale"
                type="number"
                :min="0"
                placeholder="第五色废张比例"
                :disabled="editType === 'info'"
              >
                <template #suffix>千分比</template>
              </el-input>
            </el-form-item>
          </el-col>
        </div>
        <!-- 付款周期 -->
        <div v-if="getVisibleFields()">
          <el-col :span="24">
            <el-form-item label="付款周期：" prop="cycleType">
              <el-radio-group
                v-model="infoData.cycleType"
                :disabled="editType === 'info'"
              >
                <el-radio label="MONTH">月度付</el-radio>
                <el-radio label="QUARTER">季度付</el-radio>
                <el-radio label="HALF_YEAR">半年付</el-radio>
                <el-radio label="YEAR">年度付</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.cycleType === 'MONTH'" :span="6">
            <el-form-item
              label="每月生成账单日期："
              label-width="150px"
              prop="statementDate"
            >
              <el-input
                v-model="infoData.statementDate"
                type="number"
                :min="1"
                :max="31"
                placeholder="每月生成账单日期"
                :disabled="editType === 'info'"
              >
                <template #suffix>号生成</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.cycleType === 'MONTH'" :span="6">
            <el-form-item
              label="允许逾期天数："
              label-width="150px"
              prop="payExpireDate"
            >
              <el-input
                v-model="infoData.payExpireDate"
                type="number"
                :min="1"
                :max="31"
                placeholder="允许逾期天数"
                :disabled="editType === 'info'"
              >
                <template #suffix>天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </div>
        <!-- 是否自动续约、强制停保 -->
        <el-col :span="24" style="padding-left: 0; padding-right: 0">
          <el-col :span="6">
            <el-form-item label="是否自动续约：" prop="autoRenewal">
              <el-radio-group
                v-model="infoData.autoRenewal"
                :disabled="editType === 'info'"
              >
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否强制停保：" prop="forceStopServer">
              <el-radio-group
                v-model="infoData.forceStopServer"
                :disabled="editType === 'info'"
              >
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-col>

        <!-- 保底类型 -->
        <div v-if="getVisibleFields()">
          <el-col :span="24">
            <el-form-item label="保底类型：" prop="guaranteeType">
              <el-radio-group
                v-model="infoData.guaranteeType"
                :disabled="editType === 'info'"
              >
                <el-radio label="NONE">无</el-radio>
                <el-radio label="AMOUNT">金额</el-radio>
                <el-radio label="QUANTITY">印量</el-radio>
                <el-radio label="BOTH">印量+金额</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.guaranteeType !== 'NONE'" :span="6">
            <el-form-item label="总保底印量：" prop="guaranteeCount">
              <el-input
                v-model="infoData.guaranteeCount"
                type="number"
                :min="0"
                placeholder="总保底印量"
                :disabled="editType === 'info'"
              >
                <template #suffix>张</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.guaranteeType !== 'NONE'" :span="6">
            <el-form-item label="保底金额：" prop="guaranteeAmount">
              <el-input
                v-model="infoData.guaranteeAmount"
                type="number"
                :min="0"
                placeholder="保底金额"
                :disabled="editType === 'info'"
              >
                <template #suffix>元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.guaranteeType !== 'NONE'" :span="6">
            <el-form-item label="黑色保底印量：" prop="blackGuaranteeCount">
              <el-input
                v-model="infoData.blackGuaranteeCount"
                type="number"
                :min="0"
                placeholder="黑色保底印量"
                :disabled="editType === 'info'"
              >
                <template #suffix>张</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.guaranteeType !== 'NONE'" :span="6">
            <el-form-item label="彩色保底印量：" prop="colorGuaranteeCount">
              <el-input
                v-model="infoData.colorGuaranteeCount"
                type="number"
                :min="0"
                placeholder="彩色保底印量"
                :disabled="editType === 'info'"
              >
                <template #suffix>张</template>
              </el-input>
            </el-form-item>
          </el-col>
        </div>
        <!-- 有无赠送 -->
        <!--<div v-if="getVisibleFields()">-->
        <!--  <el-col :span="24">-->
        <!--    <el-form-item label="服务有无赠送：" prop="serveHasGive">-->
        <!--      <el-radio-group-->
        <!--        v-model="infoData.serveHasGive"-->
        <!--        :disabled="editType === 'info'"-->
        <!--      >-->
        <!--        <el-radio :label="false"> 无赠送 </el-radio>-->
        <!--        <el-radio :label="true"> 有赠送 </el-radio>-->
        <!--      </el-radio-group>-->
        <!--    </el-form-item>-->
        <!--  </el-col>-->
        <!--  &lt;!&ndash; 赠送服务 &ndash;&gt;-->
        <!--  <GiftService-->
        <!--    v-if="infoData.serveHasGive"-->
        <!--    ref="giftServiceRef"-->
        <!--    v-model="infoData.contractServeGive"-->
        <!--    :edit-type="editType"-->
        <!--  />-->
        <!--</div>-->

        <!-- 价格类型 -->
        <!--v-if="getVisibleFields()"-->
        <div>
          <el-col :span="24">
            <el-form-item label="价格类型：" prop="priceType">
              <el-radio-group
                v-model="infoData.priceType"
                :disabled="editType === 'info'"
              >
                <el-radio label="FIXED">固定单价</el-radio>
                <el-radio label="LADDER">阶梯价</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.priceType === 'FIXED'" :span="6">
            <el-form-item label="黑白单价：" prop="blackWhitePrice">
              <el-input
                v-model="infoData.blackWhitePrice"
                type="number"
                :min="0"
                placeholder="黑白单价"
                :disabled="editType === 'info'"
              >
                <template #suffix>元/张</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.priceType === 'FIXED'" :span="6">
            <el-form-item label="彩色单价：" prop="colorPrice">
              <el-input
                v-model="infoData.colorPrice"
                type="number"
                :min="0"
                placeholder="彩色单价"
                :disabled="editType === 'info'"
              >
                <template #suffix>元/张</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.priceType === 'FIXED'" :span="6">
            <el-form-item label="第五色单价：" prop="fiveColourPrice">
              <el-input
                v-model="infoData.fiveColourPrice"
                type="number"
                :min="0"
                placeholder="第五色单价"
                :disabled="editType === 'info'"
              >
                <template #suffix>元/张</template>
              </el-input>
            </el-form-item>
          </el-col>
          <!-- 阶梯价 -->
          <LadderPrices
            v-if="infoData.priceType === 'LADDER'"
            v-model="infoData"
            :edit-type="editType"
          />
          <!-- 备注 -->
          <el-col :span="24">
            <el-form-item label="备注：" prop="serveRemark">
              <el-input
                v-model="infoData.serveRemark"
                type="textarea"
                maxlength="255"
                show-word-limit
                placeholder="备注"
                :disabled="editType === 'info'"
              ></el-input>
            </el-form-item>
          </el-col>
        </div>
        <!-- 是否合并机器印量核算 -->
        <!--<div v-if="getVisibleFields()">-->
        <!--  <el-col :span="24">-->
        <!--    <el-form-item label="合并机器印量核算：">-->
        <!--      <el-radio-group-->
        <!--        v-model="infoData.mergeType"-->
        <!--        :disabled="editType === 'info'"-->
        <!--      >-->
        <!--        <el-radio label="NONE"> 否 </el-radio>-->
        <!--        <el-radio label="BW"> 合并黑色印量 </el-radio>-->
        <!--        <el-radio label="COLOUR"> 合并彩色印量 </el-radio>-->
        <!--        <el-radio label="TOTAL"> 合并总印量 </el-radio>-->
        <!--      </el-radio-group>-->
        <!--    </el-form-item>-->
        <!--  </el-col>-->
        <!--  <el-col v-if="infoData.mergeType !== 'NONE'" :span="6">-->
        <!--    <span>功能开发中！！！</span>-->
        <!--  </el-col>-->
        <!--</div>-->
        <!--v-if="getVisibleFields()"-->
      </el-row>
    </el-form>
  </div>
</template>

<script>
// import GiftService from "@/views/custom/editCustom/components/contract/giftService.vue";
import LadderPrices from "@/views/custom/editCustom/components/contract/ladderPrices.vue";
import ResponsibleService from "@/views/custom/editCustom/components/contract/responsibleService.vue";
import { validatePositiveNumber } from "@/utils/validate";
export default {
  name: "FullHalfGuaranteed",
  components: { LadderPrices, ResponsibleService },
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    serviceType: {
      type: String,
      default: "",
    },
    editType: {
      type: String,
      default: "add",
    },
    contractType: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      formRules: {
        prepayment: [
          {
            required: true,
            message: "请输入预付金额",
            trigger: "change",
          },
          {
            validator: validatePositiveNumber,
            message: "预付金额不能小于0",
            trigger: "change",
          },
        ],
        signBlackWhiteCounter: [
          {
            validator: validatePositiveNumber,
            message: "计数器必须大于等于0",
            trigger: "change",
          },
        ],
        signColoursCounter: [
          {
            validator: validatePositiveNumber,
            message: "计数器必须大于等于0",
            trigger: "change",
          },
        ],
        signFiveColoursCounter: [
          {
            validator: validatePositiveNumber,
            message: "计数器必须大于等于0",
            trigger: "change",
          },
        ],
        bwWasteNumber: [
          {
            validator: validatePositiveNumber,
            message: "废张数必须大于等于0",
            trigger: "change",
          },
        ],
        colorWasteNumber: [
          {
            validator: validatePositiveNumber,
            message: "废张数必须大于等于0",
            trigger: "change",
          },
        ],
        fiveWasteNumber: [
          {
            validator: validatePositiveNumber,
            message: "废张数必须大于等于0",
            trigger: "change",
          },
        ],
        bwWasteScale: [
          {
            validator: validatePositiveNumber,
            message: "废张比例必须大于等于0",
            trigger: "change",
          },
        ],
        colorWasteScale: [
          {
            validator: validatePositiveNumber,
            message: "废张比例必须大于等于0",
            trigger: "change",
          },
        ],
        fiveWasteScale: [
          {
            validator: validatePositiveNumber,
            message: "废张比例必须大于等于0",
            trigger: "change",
          },
        ],
        wasteType: [
          {
            required: true,
            message: "请选择废张计算方式",
            trigger: "change",
          },
        ],
        cycleType: [
          {
            required: true,
            message: "请选择付款周期",
            trigger: "change",
          },
        ],
        statementDate: [
          {
            required: true,
            message: "请输入每月生成账单日期",
            trigger: "change",
          },
          {
            validator: (rule, value, callback) => {
              if (value < 1 || value > 31) {
                this.infoData.statementDate = 1;
                callback(new Error("生成日期必须在1-31之间"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        payExpireDate: [
          {
            required: true,
            message: "请输入允许逾期天数",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (value < 1) {
                callback(new Error("逾期天数必须大于等于1"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        autoRenewal: [
          {
            required: true,
            message: "请选择是否自动续约",
            trigger: "change",
          },
        ],
        forceStopServer: [
          {
            required: true,
            message: "请选择是否强制停保",
            trigger: "change",
          },
        ],
        guaranteeType: [
          {
            required: true,
            message: "请选择保底类型",
            trigger: "change",
          },
        ],
        guaranteeCount: [
          {
            validator: validatePositiveNumber,
            message: "总保底印量必须大于等于0",
            trigger: "change",
          },
        ],
        guaranteeAmount: [
          {
            validator: validatePositiveNumber,
            message: "总保底金额必须大于等于0",
            trigger: "change",
          },
        ],
        blackGuaranteeCount: [
          {
            validator: validatePositiveNumber,
            message: "黑色保底印量必须大于等于0",
            trigger: "change",
          },
        ],
        colorGuaranteeCount: [
          {
            validator: validatePositiveNumber,
            message: "彩色保底印量必须大于等于0",
            trigger: "change",
          },
        ],
        serveHasGive: [
          {
            required: true,
            message: "请选择有无赠送",
            trigger: "change",
          },
        ],
        priceType: [
          {
            required: true,
            message: "请选择价格类型",
            trigger: "change",
          },
        ],
        blackWhitePrice: [
          {
            validator: validatePositiveNumber,
            message: "单价必须大于等于0",
            trigger: "change",
          },
        ],
        colorPrice: [
          {
            validator: validatePositiveNumber,
            message: "单价必须大于等于0",
            trigger: "change",
          },
        ],
        fiveColourPrice: [
          {
            validator: validatePositiveNumber,
            message: "单价必须大于等于0",
            trigger: "change",
          },
        ],
        packageNumber: [
          {
            required: true,
            message: "请输入包量数量",
            trigger: "change",
          },
        ],
        packageUse: [
          {
            required: true,
            message: "请输入已使用印量",
            trigger: "change",
          },
        ],
        packageExpireDate: [
          {
            required: true,
            message: "请输入包量截止日期",
            trigger: "change",
          },
        ],
        packageAmount: [
          {
            required: true,
            message: "请输入预付金额",
            trigger: "change",
          },
          {
            validator: validatePositiveNumber,
            message: "预付金额必须大于等于0",
          },
          // {
          //   type: "number",
          //   min: 0,
          //   message: "预付金额必须大于0",
          //   trigger: "change",
          // },
        ],
        paperType: [
          {
            required: true,
            message: "请选择计数方式",
            trigger: "change",
          },
        ],
        startTime: [
          {
            required: true,
            message: "请选择合约开始时间",
            trigger: "change",
          },
        ],
        endTime: [
          {
            required: true,
            message: "请选择合约截止开始时间",
            trigger: "change",
          },
          {
            validator: (rule, value, callback) => {
              if (value < this.infoData.startTime) {
                callback(new Error("合约截止时间不能小于开始时间"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
      },
    };
  },
  computed: {
    infoData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    serviceType: {
      handler(val) {
        // 服务类型改变清除包量信息
        this.$set(this.infoData, "packageType", null);
        this.$set(this.infoData, "packageBwNumber", null);
        this.$set(this.infoData, "packageColorNumber", null);
        this.$set(this.infoData, "packageNumber", null);
        this.$set(this.infoData, "packageBwPrice", null);
        this.$set(this.infoData, "packageColorPrice", null);
        this.$set(this.infoData, "packageExpireDate", null);
        this.$set(this.infoData, "serveSettleStatus", null);
        this.$set(this.infoData, "serveSettleMethod", null);
        this.$set(this.infoData, "packageAmount", null);
        this.$set(this.infoData, "packagePreAmount", null);
        this.$set(this.infoData, "prepayment", null);
      },
    },
  },
  mounted() {},
  created() {
    const defaultValues = {
      signBlackWhiteCounter: null,
      signColoursCounter: null,
      signFiveColoursCounter: null,
      wasteType: "ACTUAL", // 废张计算方式
      bwWasteNumber: null,
      colorWasteNumber: null,
      bwWasteScale: null,
      colorWasteScale: null,
      blackWhitePrice: null,
      colorPrice: null,
      fiveWasteNumber: null,
      fiveWasteScale: null,
      fiveColourPrice: null,
      cycleType: "MONTH", // 付款周期
      statementDate: 26, // 每月账单生成时间
      payExpireDate: 7, // 每月付款截止时间
      forceStopServer: false, // 是否强制停保
      autoRenewal: false, // 是否自动续约
      guaranteeType: "NONE", // 保底类型
      serveHasGive: false, // 是否赠送
      priceType: "FIXED", // 价格类型
      mergeType: "NONE",
      paperType: "A4",
      packageAmount: null,
      packageUse: null,
      depositPackageAmount: null,
      serveArrersAmount: null,
      prepayment: null, // 租赁预付款
      serveRemark: null,
    };
    this.$emit("input", { ...defaultValues, ...this.value });
  },
  methods: {
    getVisibleFields() {
      const serviceType = this.serviceType;
      return [
        "BUY_HALF",
        "BUY_FULL",
        "HALF",
        "ALL",
        "RENT_HALF",
        "RENT_FULL",
        "FINANCING_HALF",
        "FINANCING_FULL",
      ].includes(serviceType);
    },
  },
};
</script>

<style scoped lang="scss">
.full-half-guaranteed {
  width: 100%;
}

.ladder-section {
  width: 100%;
  margin: 10px 0;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}
</style>
