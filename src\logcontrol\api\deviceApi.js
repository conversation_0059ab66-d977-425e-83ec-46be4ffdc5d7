/**
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 设备管理API接口 - 基于后端完善版实现
 */

import { get, post, put, del } from '@/utils/request'

export const deviceApi = {
  // 获取设备列表
  getDeviceList() {
    return get('/logcontrol/device/list')
  },

  // 根据设备ID获取设备信息
  getDeviceInfo(deviceId) {
    return get('/logcontrol/device/get', { deviceId })
  },

  // 根据ID获取设备信息
  getDeviceInfoById(id) {
    return get(`/logcontrol/device/get/${id}`)
  },

  // 获取设备总数
  getDeviceCount() {
    return get('/logcontrol/device/count')
  },

  // 删除设备信息
  deleteDevice(id) {
    return del(`/logcontrol/device/${id}`)
  },

  // 统计接口
  getBrandStatistics() {
    return get('/logcontrol/device/stats/brand')
  },

  getModelStatistics() {
    return get('/logcontrol/device/stats/model')
  },

  getOsVersionStatistics() {
    return get('/logcontrol/device/stats/os-version')
  },

  getRootedStatistics() {
    return get('/logcontrol/device/stats/rooted')
  },

  // 新增统计接口（基于完善的字段）
  getManufacturerStatistics() {
    return get('/logcontrol/device/stats/manufacturer')
  },

  getSdkVersionStatistics() {
    return get('/logcontrol/device/stats/sdk-version')
  },

  getScreenResolutionStatistics() {
    return get('/logcontrol/device/stats/screen-resolution')
  },

  getCpuAbiStatistics() {
    return get('/logcontrol/device/stats/cpu-abi')
  },

  getEmulatorStatistics() {
    return get('/logcontrol/device/stats/emulator')
  },

  getNetworkTypeStatistics() {
    return get('/logcontrol/device/stats/network-type')
  },

  getLanguageStatistics() {
    return get('/logcontrol/device/stats/language')
  },

  // 高级搜索
  advancedSearch(params) {
    return get('/logcontrol/device/advanced-search', params)
  }
}
