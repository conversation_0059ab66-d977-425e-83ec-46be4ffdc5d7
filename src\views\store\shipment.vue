<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-07 14:04:23
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:48
 * @Description: 机器发货
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            v-if="row.deliveryStatus === 0 && row.status?.value !== 'CANCEL'"
            icon="el-icon-present"
            @click="handleMachineDispatch(row, 'edit')"
          >
            发货
          </el-button>
          <el-button
            v-if="row.deliveryStatus === 1 && row.status?.value !== 'CANCEL'"
            icon="el-icon-warning-outline"
            @click="handleMachineDispatch(row, 'info')"
          >
            发货详情
          </el-button>
          <el-button
            v-if="row.status?.value === 'RETURN' && row.isReturn"
            icon="el-icon-circle-check"
            @click="handleReturnMachineReceive(row, 'edit')"
          >
            退机收货
          </el-button>
          <el-button
            v-if="row.status?.value === 'UNEFFECT' && row.isReturn"
            icon="el-icon-warning-outline"
            @click="handleReturnMachineReceive(row, 'info')"
          >
            退机收货详情
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 机器发货 -->
    <DispatchMachine ref="dispatchMachine" @refresh="refresh" />
    <!-- 退机 -->
    <ReturnMachineReceive ref="returnMachineReceive" @refresh="refresh" />
  </div>
</template>

<script>
import DispatchMachine from "@/views/custom/components/dispatchMachine.vue";
import ReturnMachineReceive from "@/views/custom/components/returnMachineReceive.vue";
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { getCustomerSendListApi } from "@/api/customer";

export default {
  name: "Shipment",
  components: { DispatchMachine, ReturnMachineReceive },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "合同编号",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "contractType",
          title: "合约类型",
          isTable: true,
          formatter: (row) => row.contractType?.label,
          width: 100,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "code",
          title: "合同编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "signName",
          title: "签约人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 100,
        },
        {
          dataIndex: "expectInstallTime",
          title: "安装时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "deliveryStatus",
          title: "是否发货",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "待发货",
              value: 0,
            },
            {
              label: "已发货",
              value: 1,
            },
          ],
        },
        {
          dataIndex: "isReturn",
          title: "是否退货",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: 1,
            },
            {
              label: "否",
              value: 0,
            },
          ],
        },
        {
          dataIndex: "agentName",
          title: "经办人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 100,
        },
        {
          dataIndex: "status",
          title: "合同状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          width: 100,
        },

        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 260,
        },
      ],
      tableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameter = cloneDeep(this.queryParam);
      getCustomerSendListApi(requestParameter)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    /**
     * 机器发货
     * @param row
     * @param type
     */
    handleMachineDispatch(row, type) {
      this.$refs.dispatchMachine.visible(row, type);
    },
    /**
     * 退机收货
     * @param row
     * @param type
     */
    handleReturnMachineReceive(row, type) {
      this.$refs.returnMachineReceive.visible(row, type);
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        this.$message.error(error.message);
      }
    },
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
