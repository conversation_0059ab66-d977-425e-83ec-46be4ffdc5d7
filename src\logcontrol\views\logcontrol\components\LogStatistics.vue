<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 日志统计卡片组件
-->
<template>
  <div class="statistics-row">
    <div class="stat-card-wrapper" v-for="stat in statisticsData" :key="stat.key">
      <el-card class="stat-card" :class="{ 'loading': loading }">
        <div class="stat-content">
          <div class="stat-icon" :style="{ backgroundColor: stat.color + '20', color: stat.color }">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">
              <span v-if="!loading">{{ formatNumber(stat.value) }}</span>
              <span v-else>--</span>
              <span class="stat-unit">{{ stat.unit }}</span>
            </div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LogStatistics',
  props: {
    statistics: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 计算日志总数
    totalLogs() {
      const crashLogs = this.statistics.crashLogs || 0
      const locationLogs = this.statistics.locationLogs || 0
      const businessLogs = this.statistics.businessLogs || 0
      const performanceLogs = this.statistics.performanceLogs || 0
      return crashLogs + locationLogs + businessLogs + performanceLogs
    },

    statisticsData() {
      return [
        {
          key: 'totalLogs',
          label: '日志总数',
          value: this.totalLogs,
          unit: '条',
          icon: 'el-icon-document',
          color: '#409EFF'
        },
        {
          key: 'crashLogs',
          label: '崩溃日志',
          value: this.statistics.crashLogs || 0,
          unit: '条',
          icon: 'el-icon-warning-outline',
          color: '#ff4d4f'
        },
        {
          key: 'locationLogs',
          label: '位置日志',
          value: this.statistics.locationLogs || 0,
          unit: '条',
          icon: 'el-icon-location-outline',
          color: '#52c41a'
        },
        {
          key: 'businessLogs',
          label: '业务日志',
          value: this.statistics.businessLogs || 0,
          unit: '条',
          icon: 'el-icon-s-operation',
          color: '#1890ff'
        },
        {
          key: 'performanceLogs',
          label: '性能日志',
          value: this.statistics.performanceLogs || 0,
          unit: '条',
          icon: 'el-icon-cpu',
          color: '#722ed1'
        }
      ]
    }
  },
  methods: {
    formatNumber(num) {
      // 直接返回真实数量，不进行缩写
      return num.toString()
    }
  }
}
</script>

<style lang="scss" scoped>
.statistics-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;
  justify-content: space-between;
}

.stat-card-wrapper {
  flex: 1 1 calc(20% - 10px);
  min-width: 200px;

  .stat-card {
    height: 100px;
    
    &.loading {
      opacity: 0.6;
    }
    
    .stat-content {
      display: flex;
      align-items: center;
      height: 100%;
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        i {
          font-size: 24px;
        }
      }
      
      .stat-info {
        flex: 1;
        
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          line-height: 1;
          margin-bottom: 4px;
          
          .stat-unit {
            font-size: 14px;
            font-weight: normal;
            color: #909399;
            margin-left: 4px;
          }
        }
        
        .stat-label {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
}
</style>
