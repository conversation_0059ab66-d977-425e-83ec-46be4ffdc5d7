/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-01-25 17:01:20
 * @Description: 网站管理相关API
 */

import { get, post, put, del } from "@/utils/request";

// ================== 网站仪表板 ==================

/**
 * 获取仪表板统计数据
 */
export const getDashboardStatistics = () => get('/website/dashboard/statistics');

/**
 * 获取最近咨询
 * @param {number} limit 限制数量
 */
export const getRecentInquiries = (limit = 5) => get('/website/dashboard/recent-inquiries', { limit });

/**
 * 获取服务类型分布
 */
export const getServiceDistribution = () => get('/website/dashboard/service-distribution');

// ================== 网站内容管理 ==================

/**
 * 分页查询网站内容
 * @param {Object} params 查询参数
 */
export const getContentPage = (params) => get('/website-content/page', params);

/**
 * 根据类型获取内容（管理后台编辑用）
 * @param {string} type 内容类型
 */
export const getContentByType = (type) => get(`/website-content/first-by-type?type=${type}`);

/**
 * 根据类型获取内容（前端展示用）
 * @param {string} type 内容类型
 */
export const getContentByTypeForDisplay = (type) => get(`/website-public/content/${type}`);

/**
 * 创建网站内容
 * @param {Object} data 内容数据
 */
export const createContent = (data) => post('/website-content', data);

/**
 * 更新网站内容
 * @param {Object} data 内容数据（包含 id 字段）
 */
export const updateContent = (data) => put(`/website-content/${data.id}`, data);

/**
 * 删除网站内容
 * @param {number} id 内容ID
 */
export const deleteContent = (id) => del(`/website-content/${id}`);

/**
 * 发布内容
 * @param {number} id 内容ID
 */
export const publishContent = (id) => put(`/website-content/${id}/publish`);

/**
 * 取消发布内容
 * @param {number} id 内容ID
 */
export const unpublishContent = (id) => put(`/website-content/${id}/unpublish`);

// ================== 网站图片管理 ==================

/**
 * 分页查询图片
 * @param {Object} params 查询参数
 */
export const getImagePage = (params) => get('/website-image/page', params);

/**
 * 获取所有图片
 */
export const getAllImages = () => get('/website-image/all');

/**
 * 获取图片统计信息
 */
export const getImageStatistics = () => get('/website-image/statistics');

/**
 * 获取COS上传凭证
 */
export const getCosCredentials = () => get('/website-image/cos-credentials');

/**
 * 记录图片上传
 * @param {Object} data 图片数据
 */
export const recordImageUpload = (data) => post('/website-image/upload', data);

/**
 * 更新图片信息
 * @param {number} id 图片ID
 * @param {Object} data 更新数据
 */
export const updateImage = (id, data) => put(`/website-image/${id}`, data);

/**
 * 删除图片
 * @param {number} id 图片ID
 */
export const deleteImage = (id) => del(`/website-image/${id}`);

/**
 * 批量删除未使用的图片
 * @param {Array} ids 图片ID数组
 */
export const batchDeleteUnusedImages = (ids) => del('/website-image/batch-delete-unused', { ids });

/**
 * 刷新图片使用状态
 */
export const refreshImageUsage = () => post('/website-image/refresh-usage');

// ================== 网站咨询管理 ==================

/**
 * 分页查询咨询
 * @param {Object} params 查询参数
 */
export const getInquiryPage = (params) => get('/website-inquiry/page', params);

/**
 * 获取咨询详情
 * @param {number} id 咨询ID
 */
export const getInquiryById = (id) => get(`/website-inquiry/${id}`);

/**
 * 更新咨询
 * @param {number} id 咨询ID
 * @param {Object} data 更新数据
 */
export const updateInquiry = (id, data) => put(`/website-inquiry/update`, { id, ...data });

/**
 * 删除咨询
 * @param {number} id 咨询ID
 */
export const deleteInquiry = (id) => del(`/website-inquiry/${id}`);

/**
 * 批量删除咨询
 * @param {Array} ids 咨询ID数组
 */
export const batchDeleteInquiries = (ids) => del('/website-inquiry/batch-delete', { ids });

/**
 * 获取咨询统计信息
 */
export const getInquiryStatistics = () => get('/website-inquiry/statistics');

/**
 * 导出咨询数据
 * @param {Object} params 查询参数
 */
export const exportInquiries = (params) => get('/website-inquiry/export', params);

/**
 * 获取咨询历史记录
 * @param {number} id 咨询ID
 */
export const getInquiryHistory = (id) => get(`/website-inquiry/history/${id}`);

// ================== 网站配置管理 ==================

/**
 * 获取所有配置
 */
export const getAllConfig = () => get('/website-config');

/**
 * 获取公开配置
 */
export const getPublicConfig = () => get('/website-config/public');

/**
 * 更新配置
 * @param {Object} data 配置数据
 */
export const updateConfig = (data) => put('/website-config', data);

/**
 * 获取敏感配置的密文值
 * @param {string} configKey 配置键
 */
export const getSensitiveConfig = (configKey) => get(`/website-config/sensitive/${configKey}`);

/**
 * 获取敏感配置的密文值（前端安全传输用）
 * 与React项目API保持完全一致
 * @param {string} configKey 配置键
 */
export const getSensitiveConfigEncrypted = async (configKey) => {
  try {
    const response = await get(`/website-config/sensitive/${configKey}`);
    // 处理RestResponse格式
    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return response.data.data || null;
    }
    return response.data || null;
  } catch (error) {
    // 获取敏感配置失败，静默处理
    return null;
  }
};

/**
 * 获取配置加密状态
 */
export const getEncryptionStatus = async () => {
  try {
    const response = await get('/website-config/encryption-status');
    // 处理RestResponse格式
    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return response.data.data || [];
    }
    return response.data || [];
  } catch (error) {
    console.warn('获取加密状态失败:', error);
    return [];
  }
};

/**
 * 迁移配置到加密存储
 */
export const migrateToEncryption = async () => {
  try {
    const response = await post('/website-config/migrate-encryption');
    // 处理RestResponse格式
    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return response.data.data || '迁移完成';
    }
    return response.data || '迁移完成';
  } catch (error) {
    console.error('加密迁移失败:', error);
    throw error;
  }
};

// ================== 网站前端展示API ==================

/**
 * 获取首页数据
 */
export const getHomepage = () => get('/website-public/homepage');

/**
 * 根据类型获取公开内容
 * @param {string} type 内容类型
 */
export const getPublicContentByType = (type) => get(`/website-public/content/${type}`);

/**
 * 获取公开图片
 * @param {string} category 图片分类
 */
export const getPublicImages = (category) => get('/website-public/images', { category });

/**
 * 提交咨询
 * @param {Object} data 咨询数据
 */
export const submitInquiry = (data) => post('/website-inquiry/submit', data);

/**
 * 获取导航菜单
 */
export const getMenus = () => get('/website-public/menus');

// ================== 导出API对象 ==================

export const websiteDashboardApi = {
  getStatistics: getDashboardStatistics,
  getRecentInquiries,
  getServiceDistribution
};

export const websiteContentApi = {
  getContentPage,
  getContentByType,
  getContentByTypeForDisplay,
  createContent,
  updateContent,
  deleteContent,
  publishContent,
  unpublishContent
};

export const websiteImageApi = {
  getImagePage,
  getAllImages,
  getImageStatistics,
  getCosCredentials,
  recordImageUpload,
  updateImage,
  deleteImage,
  batchDeleteUnusedImages,
  refreshImageUsage
};

export const websiteInquiryApi = {
  getInquiryPage,
  getInquiryById,
  updateInquiry,
  deleteInquiry,
  batchDeleteInquiries,
  getInquiryStatistics,
  exportInquiries,
  getInquiryHistory
};

export const websiteConfigApi = {
  getAllConfig,
  getPublicConfig,
  updateConfig,
  getSensitiveConfig,
  getSensitiveConfigEncrypted,
  getEncryptionStatus,
  migrateToEncryption
};

export const websitePublicApi = {
  getHomepage,
  getPublicContentByType,
  getContentByTypeForDisplay,
  getPublicImages,
  submitInquiry,
  getMenus
};
