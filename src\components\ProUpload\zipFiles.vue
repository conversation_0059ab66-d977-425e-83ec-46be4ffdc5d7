<!--
 * @Author: yangz<PERSON>
 * @Date: 2023-11-02 11:32:05
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-10-09 12:00:38
 * @Description: 图片上传组件
-->

<template>
  <el-upload
    ref="upload"
    class="upload-demo"
    action=""
    :http-request="uploadImg"
    :disabled="disabled"
    :file-list="fileList"
    :limit="limit"
    :accept="accept"
    :multiple="multiple"
    :on-exceed="handleExceed"
    :before-remove="handleRemove"
    :before-upload="checkFileType"
  >
    <el-button size="small" type="primary" :disabled="disabled"
      >点击上传</el-button
    >
    <div slot="tip" class="el-upload__tip">只能上传.zip文件，且不超过100M</div>
  </el-upload>
</template>

<script>
import { uploadFile } from "@/api/upload";
import { Message } from "element-ui";
export default {
  name: "ProUpload",
  props: {
    fileList: {
      type: Array,
      default: () => [],
    },
    limit: {
      type: Number,
      default: 1,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    accept: {
      type: String,
      default: ".zip",
    },
    fileType: {
      type: Array,
      default: () => ["application/x-zip-compressed", "application/zip"],
    },
    fileSize: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      imgList: [],
    };
  },
  methods: {
    async uploadImg(val) {
      try {
        const { file } = val;
        const result = await uploadFile(file);
        this.imgList.push(file);
        this.$emit("uploadSuccess", result);
        Message.success("上传成功");
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleExceed() {
      Message.warning("最多只能上传" + this.limit + "个.zip压缩文件");
    },
    handleRemove(file) {
      this.imgList = this.imgList.filter((item) => item.uid !== file.uid);
      this.$emit("uploadRemove", file);
    },
    checkFileType(file) {
      const { type, size } = file;
      let errorMessage = "";
      if (!this.fileType.includes(type)) {
        errorMessage = "仅支持上传" + this.fileType.join("、") + "格式压缩文件";
      }
      if (size > this.fileSize * 1024 * 1024) {
        if (!errorMessage) {
          errorMessage = "压缩文件大小不能超过" + this.fileSize + "M";
        } else errorMessage += "，且压缩文件大小不能超过" + this.fileSize + "M";
      }
      if (errorMessage) {
        Message.error(errorMessage);
        return false;
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
<style>
/* .el-upload--picture-card {
    border: none !important;
    line-height: 0 !important;
    height: 0 !important;
    width: 0 !important;
} */
</style>
