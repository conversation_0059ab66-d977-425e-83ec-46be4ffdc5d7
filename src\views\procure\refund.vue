<!--
 * @Author: wskg
 * @Date: 2024-08-14 11:42:49
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 14:42:51
 * @Description: 采购退货单
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <el-button
            v-if="
              row.refundStatus?.value !== 'WAIT_CONFIRM' &&
              row.refundStatus?.value !== 'SUCCESS' &&
              row.refundStatus?.value !== 'WAIT_RECEIVE' &&
              row.refundStatus?.value !== 'CLOSED'
            "
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'edit')"
          >
            退货
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="showDrawer"
      :title="'退货'"
      size="80%"
      :destroy-on-close="true"
      :confirm-text="'确认退货'"
      :no-footer="methodType === 'info'"
      @ok="handleDrawerOk"
      @cancel="handleCloseDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="methodType"
      >
        <template #refundType>
          {{ form.refundType?.label }}
        </template>
        <template #refundStatus>
          {{ form.refundStatus?.label }}
        </template>
        <template #returnParams>
          <div class="title-box" style="margin: 0">退货清单</div>
          <ProTable
            ref="returnProTable"
            :columns="returnParamsColumns"
            :data="returnParamsData"
            :height="400"
            :show-search="false"
            :show-setting="false"
            :show-loading="false"
            sticky
          >
            <template #trackingNumber="{ row }">
              <el-input
                v-model="row.trackingNumber"
                placeholder="请输入快递单号"
                :disabled="methodType === 'info'"
              ></el-input>
            </template>
          </ProTable>
        </template>
        <template #courierList>
          <div class="title-box" style="margin: 0">快递清单</div>
          <ProTable
            ref="courierProTable"
            :columns="courierListColumns"
            :data="courierListData"
            :height="400"
            :show-search="false"
            :show-setting="false"
            :show-loading="false"
            sticky
          >
            <template #btn>
              <el-button
                v-if="methodType !== 'info'"
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="handleAddCourier"
              >
                新增快递单号
              </el-button>
            </template>
            <template #trackingNumber="{ row }">
              <el-input
                v-model="row.trackingNumber"
                placeholder="请输入快递单号"
                :disabled="methodType === 'info'"
              ></el-input>
            </template>
            <template #shippingFee="{ row }">
              <el-input
                v-model="row.shippingFee"
                placeholder="请输入运费"
                :disabled="methodType === 'info'"
              ></el-input>
            </template>
            <template #trackingPic="{ row }">
              <div class="tracking-pic">
                <ProUpload
                  :file-list="row.picUrls"
                  :type="methodType"
                  :limit="2"
                  :drag="true"
                  :disabled="methodType === 'info'"
                  @uploadSuccess="(e) => handleLicenseImgUploadSuccess(e, row)"
                  @uploadRemove="(e) => handleLicenseImgUploadRemove(e, row)"
                />
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import ProUpload from "@/components/ProUpload/index.vue";
import {
  getReturnDetailApi,
  getReturnDetailInfoApi,
  purchaseReturnListApi,
  registerReturnApi,
} from "@/api/manufacturer";
import { cloneDeep } from "lodash";
import { Message } from "element-ui";

export default {
  name: "Refund",
  components: { ProUpload },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "退货单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 160,
        },
        {
          dataIndex: "purchaseCode",
          title: "订单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 249,
        },
        {
          dataIndex: "trackingNumber",
          title: "快递单号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "refundType",
          title: "退款类型",
          isTable: true,
          formatter: (row) => row.refundType?.label,
          // isSearch: true,
          // valueType: "select",
          // option: [
          //   {
          //     label: "现金退款",
          //     value: "CASH",
          //   },
          //   {
          //     label: "冲抵货款",
          //     value: "GOODS",
          //   },
          // ],
        },
        {
          dataIndex: "refundStatus",
          title: "退货状态",
          isTable: true,
          formatter: (row) => row.refundStatus?.label,
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [
            {
              label: "待确认",
              value: "WAIT_CONFIRM",
            },
            {
              label: "退货中",
              value: "WAIT_RETURN",
            },
            {
              label: "待收货",
              value: "WAIT_RECEIVE",
            },
            {
              label: "退款中",
              value: "WAIT_REFUND",
            },
            {
              label: "部分退款",
              value: "PART_REFUND",
            },
            // {
            //   label: "已退款",
            //   value: "REFUND",
            // },
            {
              label: "完成退款",
              value: "SUCCESS",
            },

            {
              label: "关闭",
              value: "CLOSED",
            },
          ],
        },
        // {
        //   dataIndex: "invoiceMethod",
        //   title: "开票状态",
        //   isTable: true,
        // },
        {
          dataIndex: "invoiceStatus",
          title: "开票状态",
          isTable: true,
          formatter: (row) => row.invoiceStatus?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "已开票",
              value: 1,
            },
            {
              label: "未开票",
              value: 0,
            },
          ],
        },
        {
          dataIndex: "createdBy",
          title: "退货人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          formatter: (row) => row.createdBy?.name,
        },
        {
          title: "退货日期",
          dataIndex: "createdAt",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          title: "退货数量",
          dataIndex: "number",
          isTable: true,
        },
        {
          title: "退货原因",
          dataIndex: "reason",
          isTable: true,
          width: 249,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 150,
        },
      ],
      tableData: [],
      showDrawer: false,
      methodType: "info",
      form: {},
      // 退货清单
      formColumns: [
        {
          dataIndex: "code",
          title: "退货单号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "paymentCode",
          title: "付款单号",
          isForm: true,
          formSpan: 18,
          valueType: "text",
        },
        {
          dataIndex: "manufacturerCode",
          title: "供应商编号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商简称",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorTime",
          title: "采购时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "consignee",
          title: "收货人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入收货人姓名",
          //     trigger: "blur",
          //   },
          // ],
        },
        {
          dataIndex: "consigneePhone",
          title: "收货人电话",
          isForm: true,
          formSpan: 6,
          valueType: "text",
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入联系电话",
          //     trigger: "blur",
          //   },
          // ],
        },
        {
          dataIndex: "consigneeAddress",
          title: "收货地址",
          isForm: true,
          formSpan: 12,
          valueType: "text",
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入收货地址",
          //     trigger: "blur",
          //   },
          // ],
        },
        // {
        //   dataIndex: "shippingFee",
        //   title: "运费",
        //   isForm: true,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        {
          dataIndex: "refundType",
          title: "退款类型",
          isForm: true,
          formSpan: 6,
          formSlot: "refundType",
        },
        {
          dataIndex: "refundStatus",
          title: "退货状态",
          isForm: true,
          formSpan: 6,
          formSlot: "refundStatus",
        },
        {
          dataIndex: "amount",
          title: "退货总金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "returnParams",
          title: "退货清单",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "returnParams",
        },
        {
          dataIndex: "courierList",
          title: "快递清单",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "courierList",
        },
      ],
      formLoading: false,
      returnParamsColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        // {
        //   dataIndex: "number",
        //   title: "购买数量",
        //   isTable: true,
        // },
        {
          dataIndex: "number",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "退货金额",
          isTable: true,
        },
        {
          dataIndex: "returnBackNum",
          title: "需寄回数量",
          isTable: true,
        },
        {
          dataIndex: "trackingNumber",
          title: "快递单号",
          isTable: true,
          width: 200,
          tableSlot: "trackingNumber",
        },
      ],
      returnParamsData: [],
      // 快递清单
      courierListColumns: [
        {
          dataIndex: "trackingNumber",
          title: "快递单号",
          isTable: true,
          width: 220,
          tableSlot: "trackingNumber",
        },
        {
          dataIndex: "shippingFee",
          title: "运费",
          isTable: true,
          width: 120,
          tableSlot: "shippingFee",
        },
        {
          dataIndex: "trackingPic",
          title: "快递单照片",
          isTable: true,
          tableSlot: "trackingPic",
        },
      ],
      courierListData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      purchaseReturnListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.methodType = type;
      getReturnDetailApi(row.id).then((res) => {
        this.form = res.data;
        this.returnParamsData = this.form.manufacturerReturnGoods;
        this.courierListData = this.form.backRecordInfos;
      });
      this.showDrawer = true;
    },
    handleDrawerOk() {
      try {
        this.courierListData.forEach((item) => {
          if (!item.trackingNumber) {
            throw new Error("请输入快递单号");
          }
        });
        this.returnParamsData.forEach((item) => {
          if (!item.trackingNumber) {
            throw new Error("至少填写一个快递单号");
          }
        });
        this.$confirm("正在办理退货, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          const params = {
            id: this.form.id,
            deliveryDetails: this.courierListData,
            manufacturerReturnGoods: this.returnParamsData,
          };
          registerReturnApi(params).then((res) => {
            Message.success("操作成功");
            this.refresh();
            this.handleCloseDrawer();
          });
        });
      } catch (error) {
        console.log(error);
        Message.error(error.message);
      }
    },
    // 新增快递单号
    handleAddCourier() {
      this.courierListData.push({
        trackingNumber: "",
        shippingFee: 0,
        picUrls: [],
      });
    },
    handleLicenseImgUploadSuccess(result, row) {
      row.picUrls.push(result);
    },
    handleLicenseImgUploadRemove(file, row) {
      const index = row.picUrls.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      row.picUrls.splice(index, 1);
    },
    handleCloseDrawer() {
      this.form = {};
      this.showDrawer = false;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep.tracking-pic {
  .el-upload {
    .el-upload-dragger {
      border: none;
    }
  }
}
</style>
