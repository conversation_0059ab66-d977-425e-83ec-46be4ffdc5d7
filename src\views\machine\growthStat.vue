<!--
 * @Author: wskg
 * @Date: 2024-09-07 13:52:04
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 机器 - 增长统计
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
      <template #productId>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          collapse-tags
          @change="handleSelectForm"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { productAllApi } from "@/api/dispose";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";

export default {
  name: "GrowthStat",
  data() {
    return {
      productIdName: "",
      options: [],
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "productInfo",
          title: "品牌",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "machine",
          title: "机型",
          isTable: true,
          width: 100,
        },

        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isSearch: true,
          searchSlot: "productId",
        },
        {
          dataIndex: "number",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "numberRatio",
          title: "比例",
          isTable: true,
        },
        {
          dataIndex: "print",
          title: "印量",
          isTable: true,
        },
        {
          dataIndex: "printRatio",
          title: "印量",
          isTable: true,
        },
      ],
      tableData: [],
    };
  },
  mounted() {
    this.refresh();
    this.init();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    /**
     * @description 获取省市区区域数据
     * @param node
     * @param {Function} resolve
     * @returns {Promise<void>}
     */
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 处理省市区数据
     * @param list
     * @returns {*}
     */
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleSelectForm(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
    refresh() {
      this.$refs.ProTable.listLoading = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
