<template>
  <div class="edit-tag">
    <!--        <div class="headTop">-->
    <!--            <div class="workBox">-->
    <!--                <div>订单金额: {{ orderData.sumTradeOrderNum }}</div>-->
    <!--                <div>订单次数: {{ orderData.countTradeOrder }}</div>-->
    <!--                <div>维修耗材: {{ orderData.consumableNum }}</div>-->
    <!--                <div>耗材总金额: {{ orderData.consumableAllNum }}</div>-->
    <!--                <div>维修人工: {{ orderData.manualWorkNum }}</div>-->
    <!--                <div>维修次数: {{ orderData.countManualWorkNum }}</div>-->
    <!--            </div>-->
    <!--        </div>-->
    <ProForm
      ref="ProForm"
      :form-param="formParam"
      :form-list="columns"
      :confirm-loading="formLoading"
      :layout="{ formWidth: '100%', labelWidth: '140px' }"
      :open-type="type"
      @proSubmit="formSubmit"
    >
      <template #purchaseChannel>
        <el-select
          v-model="tempPurchaseChannel"
          :disabled="type === 'info'"
          multiple
          placeholder="请选择"
        >
          <el-option
            v-for="item in purchaseChannelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </template>
      <template #serviceType>
        <el-select
          v-model="tempServiceType"
          :disabled="type === 'info'"
          multiple
          placeholder="请选择"
        >
          <el-option
            v-for="item in serviceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </template>
      <template #shopRecruitmentImg>
        <ProUpload
          :file-list="formParam.shopRecruitmentImg"
          :type="type"
          :limit="2"
          :multiple="true"
          @uploadSuccess="
            (e) => handlePhotoUploadSuccess(e, 'shopRecruitmentImg')
          "
          @uploadRemove="
            (e) => handlePhotoUploadRemove(e, 'shopRecruitmentImg')
          "
        />
      </template>
      <template #shopOutsideImg>
        <ProUpload
          :file-list="formParam.shopOutsideImg"
          :type="type"
          :limit="4"
          :multiple="true"
          @uploadSuccess="(e) => handlePhotoUploadSuccess(e, 'shopOutsideImg')"
          @uploadRemove="(e) => handlePhotoUploadRemove(e, 'shopOutsideImg')"
        />
      </template>
      <template #shopInsideImg>
        <ProUpload
          :file-list="formParam.shopInsideImg"
          :type="type"
          :limit="5"
          :multiple="true"
          @uploadSuccess="(e) => handlePhotoUploadSuccess(e, 'shopInsideImg')"
          @uploadRemove="(e) => handlePhotoUploadRemove(e, 'shopInsideImg')"
        />
      </template>
    </ProForm>
    <div v-if="type !== 'info'" class="dialog-footer1">
      <div class="btn-box">
        <el-button type="primary" @click="handleOk">保存</el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import ProForm from "@/components/ProForm/index.vue";
import {
  getCustomerTagStatApi,
  getCustomerTagApi,
  addCustomerTagApi,
  updateCustomerTagApi,
} from "@/api/customer";
import { dictTreeByCodeApi } from "@/api/user";
import { Message } from "element-ui";
import ProUpload from "@/components/ProUpload/index.vue";
import { cloneDeep } from "lodash";

export default {
  name: "Span",
  components: { ProUpload, ProForm },
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    // shopName: {
    //     type: [String, null],
    //     default: null,
    // },
    // seqId: {
    //     type: [String, null],
    //     default: null,
    // },
    type: {
      type: String,
      default: "edit",
    },
  },
  data() {
    return {
      formParam: {},
      formLoading: false,
      orderData: {},
      columns: [
        {
          dataIndex: "nativePlace",
          title: "老板籍贯",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5900),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "siteArea",
          title: "店面大小",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(4900),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "personnelNum",
          title: "店面人数",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5100),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "customerCost",
          proWangEditorContent: null,
          title: "客户形态",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(3800),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "monthBlackWhiteNum",
          title: "月/黑白印量",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(4800),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "monthColoursNum",
          title: "月/彩色印量",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5800),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "turnover",
          title: "营业额",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "pictureRatio",
          title: "图文占比",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5300),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "peerProcessNum",
          title: "同行加工",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "realMachineNum",
          title: "实际机器数",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6300),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "hasNew",
          title: "是否有新机",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi("isTrue"),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "deviceNum",
          title: "后道设备数",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6400),
          optionskey: {
            label: "label",
            value: "value",
          },
        },

        {
          dataIndex: "purchaseChannel",
          title: "主要购买渠道",
          isForm: true,
          formSpan: 6,
          formSlot: "purchaseChannel",
          // valueType: "select",
          // option: [],
          // multiple: true,
          // optionMth: () => dictTreeByCodeApi(4600),
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
        },
        {
          dataIndex: "consumableAdvantage",
          title: "耗材价格优势",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          // multiple: true,
          optionMth: () => dictTreeByCodeApi(5400),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "consumableCompetitorMoney",
          title: "耗材价格反馈",
          isForm: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          dataIndex: "selfStudy",
          title: "自修能力",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "serviceType",
          title: "当前服务方式",
          isForm: true,
          formSpan: 6,
          formSlot: "serviceType",
          // valueType: "select",
          // option: [],
          // multiple: true,
          // optionMth: () => dictTreeByCodeApi(5600),
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
        },
        {
          dataIndex: "normalMaster",
          title: "是否以直客为主",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi("isTrue"),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "demandDegree",
          title: "印品要求程度",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5700),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "largScaleCustomer",
          title: "是否有大型客户",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi("isTrue"),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "consumableCompetitor",
          title: "耗材竞争对手",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
          placeholder: "请输入耗材竞争对手，如有多个请用“/”隔开",
        },
        {
          dataIndex: "serCompetitor",
          title: "服务竞争对手",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
          placeholder: "请输入服务竞争对手，如有多个请用”/“隔开",
        },
        {
          dataIndex: "shopRecruitmentImg",
          title: "店招照片",
          isForm: true,
          formSpan: 24,
          formSlot: "shopRecruitmentImg",
        },
        {
          dataIndex: "shopOutsideImg",
          title: "店铺周围照片",
          isForm: true,
          formSpan: 24,
          formSlot: "shopOutsideImg",
        },
        {
          dataIndex: "shopInsideImg",
          title: "店内照片",
          isForm: true,
          formSpan: 24,
          formSlot: "shopInsideImg",
        },
        {
          dataIndex: "custCreatedAt",
          title: "入驻平台时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "updatedBy",
          title: "更新人员",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "updateTime",
          title: "最后一次更新时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
      ],
      purchaseChannelOptions: [],
      tempPurchaseChannel: [],
      serviceOptions: [],
      tempServiceType: [],
    };
  },
  mounted() {
    getCustomerTagStatApi(this.id).then((res) => {
      this.orderData = res.data;
    });
    dictTreeByCodeApi(4600).then((res) => {
      this.purchaseChannelOptions = res.data;
    });
    dictTreeByCodeApi(5600).then((res) => {
      this.serviceOptions = res.data;
    });
    getCustomerTagApi(this.id).then((res) => {
      if (res.data) {
        Object.keys(res.data).forEach((key) => {
          res.data[key] = res.data[key].label
            ? res.data[key].value
            : res.data[key];
          if (JSON.stringify(res.data[key]) === "{}") {
            res.data[key] = "";
          }
        });
        res.data.updatedBy = res.data.updatedBy ? res.data.updatedBy?.name : "";

        this.tempPurchaseChannel = res.data.purchaseChannel;
        this.tempServiceType = res.data.serviceType;
        this.formParam = cloneDeep(res.data);
      }
    });
  },
  methods: {
    async formSubmit(val) {
      try {
        const editApi =
          this.type === "add" ? addCustomerTagApi : addCustomerTagApi;
        this.formLoading = true;
        const args = {
          ...val,
          customerId: this.id,
          purchaseChannel: this.tempPurchaseChannel,
          serviceType: this.tempServiceType,
        };
        // console.log(args)
        delete args.location;
        delete args.regionAddress;
        delete args.updatedBy;
        delete args.updateTime;

        const result = await editApi(this.removeEmptyAttr(args));
        if (result.code === 200) {
          // this.loadData()
          Message.success("保存成功");
          this.$emit("refresh");
          this.handleClose();
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.formLoading = false;
      }
    },
    handleOk() {
      if (this.formParam.updateTime) {
        this.formParam.updateTime = this.$moment(
          this.formParam.updateTime
        ).format("YYYY-MM-DD HH:MM:SS");
      }
      // console.log(this.formParam)
      this.$refs.ProForm.handleSubmit();
    },
    handleClose() {
      this.$emit("closeDrawer");
    },
    removeEmptyAttr(obj) {
      for (const key in obj) {
        if (obj[key] === "" || obj[key] === null || obj[key] === undefined) {
          delete obj[key];
        }

        // if (obj[key] instanceof Array && obj[key].length === 0) {
        //   delete obj[key];
        // }

        if (
          Object.prototype.toString.call(obj[key]) === "[object Object]" &&
          Object.keys(obj[key]).length === 0
        ) {
          delete obj[key];
        }
      }
      return obj;
    },
    handlePhotoUploadSuccess(result, type) {
      if (!this.formParam[type]) {
        this.$set(this.formParam, type, []);
      }
      this.formParam[type].push(result);
    },
    handlePhotoUploadRemove(file, type) {
      const index = this.formParam[type].findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParam[type].splice(index, 1);
    },
  },
};
</script>

<style lang="scss" scoped>
.headTop {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;

  .workBox {
    display: flex;
    // flex-wrap: wrap;
    justify-content: space-between;
    flex: 1;

    div {
      text-wrap: nowrap;
      flex: 1;
      // width: 0%;
      font-size: 16px;
      color: #6488cf;
    }
  }
}

.edit-tag {
  height: 100%;
  overflow: auto;
  padding-bottom: 50px;

  .location {
    display: flex;

    & > span {
      display: block;
      margin: 0 10px;
    }
  }
}
</style>
