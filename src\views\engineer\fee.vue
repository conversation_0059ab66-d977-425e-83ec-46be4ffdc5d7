<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 11:13:19
 * @Description: 维修费管理
 -->

<template>
  <div class="view app-container">
    <el-tabs v-model="activeName" @tab-click="change">
      <el-tab-pane label="机型维修报价" name="model" lazy>
        <modelFee ref="model"></modelFee>
      </el-tab-pane>
      <el-tab-pane label="远程路费加价" name="distance" lazy>
        <distanceFee></distanceFee>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import modelFee from "./components/modelFee.vue";
import distanceFee from "./components/distanceFee.vue";

export default {
  name: "Fee",
  components: {
    modelFee,
    distanceFee,
  },
  mixins: [],
  props: {},
  data() {
    return {
      activeName: "model",
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {},
  methods: {
    change(data) {
      // if (data.index == "0") {
      //   this.$refs.model.init();
      // } else if (data.index == "1") {
      //   this.$refs.distance.init();
      // }
    },
  },
};
</script>
<style lang="scss" scoped></style>
