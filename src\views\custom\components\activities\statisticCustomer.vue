<!--
 * @Author: wskg
 * @Date: 2025-02-17 09:58:47
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:23
 * @Description: 活动注册客户信息
 -->
<template>
  <div class="container">
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="70%"
      top="1%"
      :no-footer="true"
      @cancel="handleDialogCancel"
    >
      <ProTable
        ref="ProTable"
        :columns="columns"
        :data="tableData"
        :height="450"
        :query-param="queryParam"
        :local-pagination="localPagination"
        @loadData="loadData"
      >
        <template #keyword1>
          <el-cascader
            style="width: 100%"
            :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
            leaf-only
            clearable
            filterable
            @change="handleReginChange"
          ></el-cascader>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import {
  getActivityShareApi,
  getActivityShareClickCustomerApi,
} from "@/api/customer";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";
import { filterParamRange } from "@/utils";

export default {
  name: "StatisticCustomer",
  data() {
    return {
      dialogVisible: false,
      columns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "province",
          title: "省份",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市区",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区县",
          isTable: true,
        },
        {
          dataIndex: "keyword1",
          title: "所属省市区",
          isSearch: true,
          searchSlot: "keyword1",
        },
        {
          dataIndex: "machineNum",
          title: "登记机器数",
          isTable: true,
        },
        {
          dataIndex: "orderNum",
          title: "消费次数",
          isTable: true,
        },
        {
          dataIndex: "orderAmount",
          title: "消费金额(元)",
          isTable: true,
        },

        {
          dataIndex: "workOrderNum",
          title: "维修次数",
          isTable: true,
        },
        {
          dataIndex: "workAmount",
          title: "维修金额(元)",
          isTable: true,
        },
        {
          dataIndex: "totalAmount",
          title: "总消费金额(元)",
          isTable: true,
        },
        {
          dataIndex: "entryTime",
          title: "入驻时间",
          isTable: true,
          isSearch: true,
          isExport: false,
          width: 150,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
        },
      ],
      tableData: [],
      queryParam: {
        activityId: "", // 活动Id
        customerId: "", // 分享客户Id
      },
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      type: "add",
      dialogTitle: "注册客户明细",
    };
  },
  mounted() {},
  methods: {
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const rangeParams = [
        {
          startDate: null,
          endDate: null,
          data: parameter.entryTime,
        },
      ];
      filterParamRange(this, this.queryParam, rangeParams);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.entryTime;
      const editApi =
        this.type === "add"
          ? getActivityShareApi
          : getActivityShareClickCustomerApi;
      editApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    show(row, type) {
      this.resetData();
      this.type = type;
      this.dialogTitle = type === "add" ? "注册客户明细" : "触达客户明细";
      this.queryParam = {};
      if (type === "add") {
        this.queryParam.activityId = row.activityId;
        this.queryParam.customerId = row.customerId;
      } else {
        this.queryParam.situationId = row.id;
      }
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.refresh();
      });
    },
    /**
     * @description 获取省市区区域数据
     * @param node
     * @param {Function} resolve
     * @returns {Promise<void>}
     */
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 处理省市区数据
     * @param list
     * @returns {*}
     */
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    handleDialogCancel() {
      this.dialogVisible = false;
    },
    resetData() {
      this.queryParam = {
        activityId: "",
        customerId: "",
      };
      this.localPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.tableData = [];
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
