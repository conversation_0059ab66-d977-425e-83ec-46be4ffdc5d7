<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 17:57:54
 * @Description: 
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #customerStaffName="{ row }">
        {{ row.customerStaff?.name }}
      </template>
      <template #customerMobile="{ row }">
        {{ row.customerStaff?.tel }}
      </template>
      <template #deviceGroup="{ row }">
        {{ row.customerDeviceGroup.deviceGroup?.label }}
      </template>
      <template #productInfo="{ row }">
        {{ row.customerDeviceGroup.productInfo }}
      </template>

      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            详情
          </el-button>
        </span>
      </template>
    </ProTable>
    <!--  查看详情  -->
    <ProDialog
      :value="showDialog"
      :title="dialogTitle"
      width="80%"
      :confirm-loading="dialogLoading"
      top="50px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <div id="dialog-content" ref="dialogContent">
        <ProForm
          ref="calculateForm"
          :form-param="editData"
          :form-list="editFormColumns"
          :confirm-loading="dialogLoading"
          :layout="{ formWidth: '100%', labelWidth: '140px' }"
          :open-type="'info'"
        >
          <template #deviceGroup>
            <el-input
              v-model="editData.deviceGroup.label"
              type="text"
              :disabled="true"
              placeholder="请输入设备"
            ></el-input>
          </template>
        </ProForm>
        <ProTable
          ref="ChooseGoodsTable"
          row-key="id"
          :data="editFrom"
          :columns="editTableColumns"
          :show-pagination="false"
          :show-search="false"
          :show-loading="false"
          :show-setting="false"
          :height="dialogTableHeight"
        >
          <template #oemNumber="{ row }">
            {{ row.productPart?.oemNumber }}
          </template>
          <template #saleAttrVals="{ row }">
            <span v-for="(v, i) in row?.skuInfo?.saleAttrVals" :key="i"
              >{{ v.val
              }}{{
                i === row?.skuInfo?.saleAttrVals.length - 1 ? "" : "/"
              }}</span
            >
          </template>
          <template #numberOem="{ row }">
            {{ row?.productPartBom?.unit.label }}
          </template>
        </ProTable>
      </div>
    </ProDialog>
  </div>
</template>
<script>
import { selfRepairApi, selfRepairDetailApi } from "@/api/repair";

import { cloneDeep } from "lodash";

export default {
  name: "MineRepair",
  data() {
    const self = this;
    return {
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      showDialog: false,
      dialogLoading: false,
      queryParam: {},
      editData: {},
      editFrom: [],
      editFormColumns: [
        {
          dataIndex: "id",
          title: "设备编号",
          isForm: true,
          valueType: "input",
          formSpan: 10,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备序号",
          isForm: true,
          formSlot: "deviceGroup",
          valueType: "input",
          formSpan: 10,
        },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isForm: true,
          valueType: "input",
          formSpan: 10,
        },
      ],
      editTableColumns: [
        {
          title: "物品编号",
          dataIndex: "articleCode",
          isTable: true,
          minWidth: 120,
        },

        {
          title: "商品名称",
          dataIndex: "itemName",
          isTable: true,
        },
        {
          title: "商品编号",
          dataIndex: "itemCode",
          isTable: true,
          minWidth: 120,
        },
        {
          title: "商品来源",
          dataIndex: "skuSource",
          isTable: true,
          formatter: (row) => {
            switch (row.skuSource) {
              case "ENGINEER_APPLY":
                return "工程师领料";
              case "MALL":
                return "商城购入";
              case "APPLY":
                return "领料单";
              case "CUSTOMER_REGISTER":
                return "客户自购";
              default:
                return "未知";
            }
          },
        },
        {
          title: "OEM编号",
          dataIndex: "oemNumber",
          isTable: true,
          tableSlot: "oemNumber",
        },
        {
          title: "规格属性",
          dataIndex: "saleAttrVals",
          isTable: true,
          tableSlot: "saleAttrVals",
        },
        {
          title: "位置",
          dataIndex: "location",
          isTable: true,
          formatter: (row) =>
            Array.isArray(row.location) ? row.location.join("、") : "",
          minWidth: 120,
        },
        {
          title: "单位",
          dataIndex: "numberOem",
          isTable: true,
          tableSlot: "numberOem",
        },
        {
          title: "数量",
          dataIndex: "num",
          isTable: true,
        },
        {
          title: "单价",
          dataIndex: "saleUnitPrice",
          isTable: true,
        },
      ],
      columns: [
        {
          dataIndex: "code",
          title: "报修单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerStaffName",
          title: "上报人员",
          isTable: true,
          tableSlot: "customerStaffName",
          // isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerMobile",
          title: "维修人手机",
          isTable: true,
          tableSlot: "customerMobile",
          // isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          placeholder: "1号机： 1",
          tableSlot: "deviceGroup",
        },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isTable: true,
          tableSlot: "productInfo",
        },
        {
          dataIndex: "blackWhiteCount",
          title: "黑白计数器",
          isTable: true,
          width: 100,
        },

        {
          dataIndex: "colorCount",
          title: "彩色计数器",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "excDesc",
          title: "故障描述",
          isTable: true,
        },
        {
          dataIndex: "updatedAt",
          title: "维修时间",
          isTable: true,
          isSearch: true,
          isExport: false,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          attrs: {
            change(val) {
              if (!val) {
                self.queryParam.createdAtStartTime = null;
                self.queryParam.createdAtEndTime = null;
                return;
              }
              self.queryParam.createdAtStartTime = val[0];
              self.queryParam.createdAtEndTime = val[1];
            },
          },
          clearable: true,
        },
        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      confirmLoading: false,
      form: {},
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      dialogTableHeight: 400,
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      selfRepairApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },

    // 触发详情
    handleInfo(row) {
      try {
        this.$refs.ProTable.listLoading = true;
        this.dialogTitle = "自修详情";
        const { id } = row;
        selfRepairDetailApi(id).then((res) => {
          if (res.code === 200) {
            this.editData = res.data.customerDeviceGroup;
            this.editFrom = res.data.listReplaceDetails;
            this.showDialog = true;
          }
        });
      } finally {
        this.$refs.ProTable.listLoading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
