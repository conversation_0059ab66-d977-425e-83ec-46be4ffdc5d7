<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-29 16:19:47
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-02 17:52:32
 * @Description: 入库单关联采购入库单号
 -->
<template>
  <ProDrawer
    :value="providerDrawer"
    title="采购详情"
    size="80%"
    :method-type="methodType"
    :no-confirm-footer="true"
    @cancel="closeDrawer"
  >
    <ProForm
      ref="ProForm"
      :form-param="form"
      :form-list="formColumns"
      :confirm-loading="formLoading"
      :layout="{ formWidth: '100%', labelWidth: '140px' }"
      :open-type="methodType"
    >
      <template #purchaseItems>
        <div class="title-box">采购物品</div>
        <ProTable
          ref="itemsProTable"
          :row-key="(row) => row.id"
          :columns="columns"
          :data="tableData"
          :height="400"
          :show-setting="false"
          :show-search="false"
          :show-loading="false"
          sticky
        >
          <template #applicableModels="{ row }">
            <el-popover placement="bottom" title="" width="700" trigger="click">
              <div style="margin: 20px; height: 400px; overflow-y: scroll">
                <el-descriptions
                  class="margin-top"
                  title="适用机型"
                  :column="1"
                  border
                >
                  <el-descriptions-item
                    v-for="item in row.productTreeDtoList"
                    :key="item.id"
                  >
                    <template slot="label"> 品牌/系列/机型 </template>
                    {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              <el-button slot="reference" type="text" size="mini">
                适用机型
              </el-button>
            </el-popover>
          </template>
        </ProTable>
      </template>
    </ProForm>
  </ProDrawer>
</template>

<script>
import {
  getPurchaseDetailApi,
  getPurchaseDetailGoodsApi,
} from "@/api/manufacturer";

export default {
  name: "ProcureWaybill",
  data() {
    return {
      providerDrawer: false,
      methodType: "info",
      form: {},
      formColumns: [
        {
          dataIndex: "code",
          title: "订单编号",
          isForm: true,
          formSpan: 24,
          valueType: "text",
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商",
          isForm: true,
          formSpan: 24,
          valueType: "text",
        },
        {
          dataIndex: "receiveCompany",
          title: "下单公司",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "createdAt",
          title: "提交时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "deliveryTime",
          title: "期待发货时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "status",
          title: "审核状态",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "refundStatus",
          title: "退货状态",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "invoiceStatus",
          title: "开票状态",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "amount",
          title: "总金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "purchaseItems",
          title: "采购物品",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "purchaseItems",
        },
      ],
      formLoading: false,
      columns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "applicableModels",
          title: "适用机型",
          isTable: true,
          width: 110,
          tableSlot: "applicableModels",
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
        {
          dataIndex: "approveNum",
          title: "计划采购数量",
          isTable: true,
        },
        {
          dataIndex: "refundNum",
          title: "已退数量",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "可采购量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "订单金额",
          isTable: true,
        },
      ],
      tableData: [],
    };
  },
  methods: {
    /**
     *
     * @param purchaseCode 采购单号
     * @param type
     */
    show(purchaseCode, type = "code") {
      if (type === "code") {
        getPurchaseDetailGoodsApi(purchaseCode).then((res) => {
          const data = res.data;
          this.form = {
            ...data,
            status: data.status.label,
            refundStatus: this.getRefundStatusLabel(data.refundStatus),
            invoiceStatus: data.invoiceStatus === 0 ? "未开票" : "已开票",
          };
          this.tableData = res.data.manufacturerOrderGoodsList;
        });
      } else if (type === "id") {
        getPurchaseDetailApi(purchaseCode).then((res) => {
          const data = res.data;
          this.form = {
            ...data,
            status: data.status.label,
            refundStatus: this.getRefundStatusLabel(data.refundStatus),
            invoiceStatus: data.invoiceStatus === 0 ? "未开票" : "已开票",
          };
          this.tableData = res.data.manufacturerOrderGoodsList;
        });
      }

      this.providerDrawer = true;
    },
    getRefundStatusLabel(status) {
      switch (status) {
        case 0:
          return "无";
        case 1:
          return "已退货";
        case 2:
          return "部分退货";
        default:
          return "无";
      }
    },
    closeDrawer() {
      this.providerDrawer = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
