/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-02 16:29:28
 * @Description:
 */
import { parseTime } from "@/utils";

export default {
  // filters

  parseTime(value) {
    return parseTime(value, "{y}-{m}-{d} {h}:{i}:{s}");
  },
  filterName(value, arr, type1, type2) {
    if (value === 0 || value) {
      const num = String(value);
      if (num != "") {
        const res = arr.filter((items) => items[type1] === num);
        if (res) {
          if (res.length > 0) {
            return res[0][type2];
          }
        }
      }
    } else {
      return null;
    }
  },
};
