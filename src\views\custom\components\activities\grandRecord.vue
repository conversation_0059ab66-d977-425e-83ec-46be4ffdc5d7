<!--
 * @Author: wskg
 * @Date: 2025-02-17 19:23:46
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:50
 * @Description: 奖品发放记录
 -->
<template>
  <div class="container">
    <ProDrawer
      :value="drawerVisible"
      title="奖品发放记录"
      size="70%"
      :no-footer="true"
      @cancel="handleDrawerCancel"
    >
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="发放清单" name="first" lazy>
          <ProTable
            ref="ProTable"
            :query-param="queryParam"
            :columns="columns"
            :data="tableData"
            :height="450"
            :local-pagination="localPagination"
            @loadData="loadData"
          >
            <template #btn>
              <div class="title-box-right">
                <div>客户数量：{{ statData.customerNum || 0 }}</div>
                <div>耗材数量：{{ statData.awardPartNum || 0 }}</div>
                <div>耗材金额：{{ statData.awardPartAmount || 0 }}</div>
                <div>维修券数量：{{ statData.awardRepairNum || 0 }}</div>
                <div>代金券数量：{{ statData.awardTicketNum || 0 }}</div>
                <div>代金券金额：{{ statData.awardTicketAmount || 0 }}</div>
                <div>积分数量：{{ statData.awardPointsNum || 0 }}</div>
                <div>其他数量：{{ statData.awardOtherNum || 0 }}</div>
                <div>其他金额：{{ statData.awardOtherAmount || 0 }}</div>
              </div>
            </template>
            <template #limit="{ row, index }">
              <div
                v-if="
                  row.awardType.value === 'REPAIR' ||
                  row.awardType.value === 'MACHINE' ||
                  row.awardType.value === 'TICKET'
                "
              >
                <el-button type="text" @click="showLimitDialog(row, index)">
                  限制条件
                </el-button>
              </div>
              <div v-else></div>
            </template>
          </ProTable>
        </el-tab-pane>
        <el-tab-pane label="奖品统计" name="second" lazy>
          <ProTable
            ref="ProTable2"
            :query-param="queryParam"
            :columns="statColumns"
            :data="tableData"
            :height="450"
            :local-pagination="localPagination"
            @loadData="loadData"
          >
            <template #btn>
              <div class="title-box-right" style="font-size: 14px">
                <div>客户数量：{{ grandStatData.customerNum || 0 }}</div>
                <div>耗材数量：{{ grandStatData.awardPartNum || 0 }}</div>
                <div>耗材金额：{{ grandStatData.awardPartAmount || 0 }}</div>
                <div>维修券数量：{{ grandStatData.awardRepairNum || 0 }}</div>
                <div>代金券数量：{{ grandStatData.awardTicketNum || 0 }}</div>
                <div>
                  代金券金额：{{ grandStatData.awardTicketAmount || 0 }}
                </div>
                <div>积分数量：{{ grandStatData.awardPointsNum || 0 }}</div>
                <div>其他数量：{{ grandStatData.awardOtherNum || 0 }}</div>
                <div>其他金额：{{ grandStatData.awardOtherAmount || 0 }}</div>
                <div>总金额：{{ grandStatData.awardAmount || 0 }}</div>
              </div>
            </template>

            <template #action="{ row }">
              <div class="fixed-width">
                <el-button icon="el-icon-view" @click="handleEdit(row)">
                  查看明细
                </el-button>
              </div>
            </template>
          </ProTable>
        </el-tab-pane>
      </el-tabs>
    </ProDrawer>
    <ProDialog
      :value="dialogVisible"
      title="奖品发放明细"
      width="70%"
      top="1%"
      :no-footer="true"
      @cancel="handleDialogCancel"
    >
      <ProTable
        ref="CustomerProTable"
        :query-param="queryParam1"
        :local-pagination="localPagination1"
        :columns="columns1"
        :data="tableData1"
        :height="450"
        @loadData="loadCustomerData"
      ></ProTable>
    </ProDialog>
    <el-dialog
      title="限制条件"
      :visible.sync="limitVisible"
      width="500px"
      append-to-body
    >
      <div
        v-if="
          selectRow.awardType === 'REPAIR' || selectRow.awardType === 'MACHINE'
        "
      >
        <el-radio-group v-model="selectRow.limitType" :disabled="true">
          <el-radio label="GENERAL">通用</el-radio>
          <el-radio label="MODEL">机型限制</el-radio>
        </el-radio-group>
        <div
          v-if="selectRow.limitType === 'MODEL'"
          style="
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            gap: 10px;
            align-items: center;
          "
        >
          <span>机器型号：</span>
          <el-cascader
            v-model="selectRow.fullIdPath"
            :options="modelOptions"
            placeholder="请选择可使用该券机型"
            filterable
            clearable
            :disabled="true"
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
              expandTrigger: 'click',
              multiple: true,
            }"
            leaf-only
            style="flex: 1"
          ></el-cascader>
        </div>
      </div>
      <div v-if="selectRow.awardType === 'TICKET'">
        <el-radio-group v-model="selectRow.limitType" :disabled="true">
          <el-radio label="GENERAL">通用</el-radio>
          <el-radio label="AMOUNT">最低消费限制</el-radio>
        </el-radio-group>
        <div
          v-if="selectRow.limitType === 'AMOUNT'"
          style="
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            gap: 10px;
            align-items: center;
          "
        >
          <span>最低消费金额：</span>
          <el-input
            v-model="selectRow.minAmount"
            style="flex: 1"
            type="number"
            placeholder="请输入最低消费金额"
            :disabled="true"
          >
            <template slot="append">元</template>
          </el-input>
        </div>
      </div>
      <div
        style="
          margin-top: 20px;
          display: flex;
          justify-content: space-between;
          gap: 10px;
          align-items: center;
        "
      >
        <span>过期时间：</span>
        <el-date-picker
          v-if="
            selectRow.awardType === 'REPAIR' ||
            selectRow.awardType === 'MACHINE' ||
            selectRow.awardType === 'TICKET'
          "
          v-model="selectRow.expireDate"
          style="flex: 1"
          type="date"
          :disabled="true"
          placeholder="选择过期时间"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
        <div v-else></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import {
  getActivityShareGrantApi,
  getActivityShareGrantStatApi,
  getActivityShareGrantTypeApi,
  getActivityStatApi,
  getGrandRecordApi,
} from "@/api/customer";
import { productAllApi } from "@/api/dispose";

export default {
  name: "GrandRecord",
  data() {
    return {
      activeName: "first",
      drawerVisible: false,
      queryParam: {},
      activityId: "", // 活动ID
      columns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "awardType",
          title: "奖品类型",
          isTable: true,
          formatter: (row) => row.awardType?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "零件耗材",
              value: "PART",
            },
            {
              label: "维修券",
              value: "REPAIR",
            },
            {
              label: "积分券",
              value: "POINTS",
            },
            {
              label: "代金券",
              value: "TICKET",
            },
            {
              label: "其他",
              value: "OTHER",
            },
          ],
          minWidth: 120,
        },
        {
          dataIndex: "limit",
          title: "限制条件",
          isTable: true,
          tableSlot: "limit",
          minWidth: 120,
        },
        {
          dataIndex: "status",
          title: "发放状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "待审核",
              value: "WAIT_APPROVE",
            },
            {
              label: "驳回",
              value: "REJECT",
            },
            {
              label: "已发放",
              value: "COMPLETE",
            },
          ],
          minWidth: 120,
        },
        {
          dataIndex: "createdAt",
          title: "发放时间",
          isTable: true,
          // option: [],
          minWidth: 120,
        },
        {
          dataIndex: "quantity",
          title: "发送数量",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "createdBy",
          title: "发送人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
          minWidth: 120,
        },
      ],
      statColumns: [
        {
          dataIndex: "awardType",
          title: "奖品类型",
          isTable: true,
          formatter: (row) => row.awardType?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "零件耗材",
              value: "PART",
            },
            {
              label: "维修券",
              value: "REPAIR",
            },
            {
              label: "积分券",
              value: "POINTS",
            },
            {
              label: "代金券",
              value: "TICKET",
            },
            {
              label: "其他",
              value: "OTHER",
            },
          ],
          minWidth: 120,
        },
        {
          dataIndex: "awardName",
          title: "奖品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "articleCode",
          title: "奖品编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "price",
          title: "单价/券面额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "totalQuantity",
          title: "总数量",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 100,
        },
      ],
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      statData: {},
      grandStatData: {},
      requestParameters: {},
      dialogVisible: false,
      // queryParam1
      queryParam1: {},
      localPagination1: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns1: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "awardType",
          title: "奖品类型",
          isTable: true,
          formatter: (row) => row.awardType?.label,
          minWidth: 120,
        },
        {
          dataIndex: "awardName",
          title: "奖品名称",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "status",
          title: "发放状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "待审核",
              value: "WAIT_APPROVE",
            },
            {
              label: "驳回",
              value: "REJECT",
            },
            {
              label: "已发放",
              value: "COMPLETE",
            },
          ],
          minWidth: 120,
        },
        {
          dataIndex: "createdAt",
          title: "发放时间",
          isTable: true,
          // option: [],
          minWidth: 120,
        },
        {
          dataIndex: "quantity",
          title: "发送数量",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "createdBy",
          title: "发放人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
          minWidth: 120,
        },
      ],
      tableData1: [],
      awardType: "",
      articleCode: "",
      selectRow: {},
      modelOptions: [],
      limitVisible: false,
    };
  },
  mounted() {},
  methods: {
    /**
     * 加载列表数据
     * @param parameter
     */
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      this.requestParameters = cloneDeep({
        ...this.queryParam,
        activityId: this.activityId,
      });
      const editApi =
        this.activeName === "first"
          ? getActivityShareGrantApi
          : getActivityShareGrantTypeApi;
      editApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.activeName === "first" &&
            this.$refs.ProTable &&
            (this.$refs.ProTable.listLoading = false);
          this.activeName === "second" &&
            this.$refs.ProTable2 &&
            (this.$refs.ProTable2.listLoading = false);
        });
      this.activeName === "first" && this.getStatData();
      this.activeName === "second" && this.getGrandStatData();
    },
    /**
     * 加载发放奖品客户详情数据
     * @param parameter
     */
    loadCustomerData(parameter) {
      this.queryParam1 = Object.assign({}, this.queryParam1, parameter);
      const requestParameters = cloneDeep({
        ...this.queryParam1,
        activityId: this.activityId,
        awardType: this.awardType,
        articleCode: this.articleCode,
      });
      getGrandRecordApi(requestParameters)
        .then((res) => {
          this.tableData1 = res.data.rows;
          this.localPagination1.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.CustomerProTable &&
            (this.$refs.CustomerProTable.listLoading = false);
        });
    },
    // 券使用限制条件
    showLimitDialog(row, index) {
      // this.actRowIndex = index;
      this.selectRow = cloneDeep(row);
      this.selectRow.limitType = this.selectRow.limitType?.value;
      this.selectRow.awardType = this.selectRow.awardType?.value;
      if (
        Array.isArray(this.selectRow.fullIdPath) &&
        this.selectRow.fullIdPath.length > 0
      ) {
        this.selectRow.fullIdPath = this.selectRow.fullIdPath.map((path) => {
          const trimmedString = path.replace(/^\/|\/$/g, "");
          const parts = trimmedString.split("/");
          return parts.map((part) => part); // 直接返回每个部分
        });
      }
      this.limitVisible = true;
    },
    show(row) {
      this.activityId = row.id;
      this.activeName = "first";
      this.resetParams();
      this.drawerVisible = true;
      this.$nextTick(() => {
        this.refresh();
        this.getProductThird();
      });
    },
    handleEdit(row) {
      this.awardType = null;
      this.articleCode = null;
      this.awardType = row.awardType?.value;
      this.articleCode = row.articleCode || "";
      this.queryParam1 = {};
      this.localPagination1 = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.CustomerProTable.refresh();
      });
    },
    handleClick(tab, event) {
      this.resetParams();
      this.$refs.ProTable.refresh();
      // console.log(tab.name);
    },
    getStatData() {
      getActivityShareGrantStatApi(this.requestParameters).then((res) => {
        this.statData = res.data;
      });
    },
    getGrandStatData() {
      getActivityStatApi(this.requestParameters).then((res) => {
        this.grandStatData = res.data;
      });
    },
    handleDialogCancel() {
      this.dialogVisible = false;
    },
    handleDrawerCancel() {
      this.drawerVisible = false;
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.modelOptions = res.data;
      });
    },
    resetParams() {
      this.tableData = [];
      this.queryParam = {};
      this.localPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.requestParameters = {};
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
