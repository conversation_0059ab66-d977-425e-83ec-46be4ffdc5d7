<template>
  <el-dialog
    title="编辑分发关系"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :modal="false"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <!-- 基本信息（只读） -->
      <el-form-item label="分发ID">
        <el-input :value="form.distributionId" disabled />
      </el-form-item>

      <el-form-item label="目标类型">
        <el-tag :type="form.targetType === 'USER' ? 'primary' : 'warning'">
          {{ form.targetType === 'USER' ? '用户' : '设备' }}
        </el-tag>
      </el-form-item>

      <el-form-item label="目标ID">
        <el-input :value="form.targetId" disabled />
      </el-form-item>

      <el-form-item label="目标名称">
        <el-input :value="form.targetName" disabled />
      </el-form-item>

      <!-- 可编辑字段 -->
      <el-form-item label="激活状态" prop="isActive">
        <el-switch
          v-model="form.isActive"
          active-text="启用"
          inactive-text="停用"
          :active-value="true"
          :inactive-value="false"
        />
        <div class="field-tip">
          停用后，目标用户/设备会降级到默认配置
        </div>
      </el-form-item>

      <el-form-item label="分发配置" prop="configId">
        <el-select
          v-model="form.configId"
          placeholder="选择配置"
          style="width: 100%"
          :loading="configsLoading"
          @focus="loadConfigs"
        >
          <el-option
            v-for="config in configs"
            :key="config.id"
            :label="`${config.configName} (${config.logLevel})`"
            :value="config.id"
          >
            <span style="float: left">{{ config.configName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ config.logLevel }}
            </span>
          </el-option>
        </el-select>
        <div class="field-tip">
          更换配置会立即生效，请谨慎操作
        </div>
      </el-form-item>

      <!-- 当前状态信息 -->
      <el-form-item label="分发状态">
        <el-tag :type="getDistributionStatusType(form.distributionStatus)">
          {{ getDistributionStatusText(form.distributionStatus) }}
        </el-tag>
      </el-form-item>

      <el-form-item label="分配时间">
        <span>{{ form.assignTime || '未知' }}</span>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        保存更改
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { configApi, DISTRIBUTION_STATUS_TEXT, DISTRIBUTION_STATUS_TYPE } from '@/logcontrol/api/configApi'

export default {
  name: 'DistributionEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    distribution: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      saving: false,
      configsLoading: false,
      configs: [],
      form: {
        distributionId: '',
        targetType: '',
        targetId: '',
        targetName: '',
        isActive: true,
        configId: '',
        distributionStatus: '',
        assignTime: ''
      },
      originalForm: {},
      rules: {
        configId: [
          { required: true, message: '请选择配置', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    async visible(val) {
      this.dialogVisible = val
      if (val && this.distribution) {
        await this.initForm()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    async initForm() {
      this.form = {
        distributionId: this.distribution.distributionId || '',
        targetType: this.distribution.targetType || '',
        targetId: this.distribution.targetId || '',
        targetName: this.distribution.targetName || '',
        isActive: this.distribution.isActive !== false, // 默认为true
        configId: this.distribution.configId || '',
        distributionStatus: this.distribution.distributionStatus || '',
        assignTime: this.distribution.assignTime || ''
      }
      // 保存原始数据用于比较
      this.originalForm = { ...this.form }

      // 立即加载配置列表，确保选择器能正确显示配置名称
      await this.loadConfigs()
    },

    async loadConfigs() {
      if (this.configs.length > 0) return
      
      this.configsLoading = true
      try {
        const response = await configApi.getConfigList()
        this.configs = response.data || []
      } catch (error) {
        console.error('加载配置列表失败:', error)
        this.$message.error('加载配置列表失败')
      } finally {
        this.configsLoading = false
      }
    },

    async handleSave() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return

        this.saving = true
        try {
          const changes = this.getChanges()
          if (changes.length === 0) {
            this.$message.info('没有检测到更改')
            this.handleClose()
            return
          }

          // 执行更改
          for (const change of changes) {
            await this.executeChange(change)
          }

          this.$message.success('更新成功')
          this.$emit('success')
          this.handleClose()
        } catch (error) {
          console.error('保存失败:', error)
          if (error.response && error.response.data && error.response.data.message) {
            this.$message.error(`保存失败：${error.response.data.message}`)
          } else {
            this.$message.error('保存失败，请检查网络连接')
          }
        } finally {
          this.saving = false
        }
      })
    },

    getChanges() {
      const changes = []
      
      if (this.form.isActive !== this.originalForm.isActive) {
        changes.push({
          type: 'status',
          value: this.form.isActive
        })
      }
      
      if (this.form.configId !== this.originalForm.configId) {
        changes.push({
          type: 'config',
          value: this.form.configId
        })
      }
      
      return changes
    },

    async executeChange(change) {
      const distributionId = this.form.distributionId
      
      switch (change.type) {
        case 'status':
          await configApi.updateDistributionStatus(distributionId, change.value)
          break
        case 'config':
          await configApi.updateDistributionConfig(distributionId, change.value)
          break
      }
    },

    getDistributionStatusType(status) {
      return DISTRIBUTION_STATUS_TYPE[status] || 'info'
    },

    getDistributionStatusText(status) {
      return DISTRIBUTION_STATUS_TEXT[status] || '未知'
    },

    handleClose() {
      this.dialogVisible = false
      this.$refs.form?.resetFields()
      this.configs = []
    }
  }
}
</script>

<style lang="scss" scoped>
.field-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-select-dropdown__item {
  height: auto;
  line-height: 1.5;
  padding: 8px 20px;
}
</style>
