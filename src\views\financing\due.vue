<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-21 09:30:05
 * @Description: 应付账款
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <!--<el-tab-pane label="应付款汇总" name="first" lazy>-->
      <!--  <PaySummary />-->
      <!--</el-tab-pane>-->
      <el-tab-pane label="耗材应付款" name="second" lazy>
        <PayConsumables />
      </el-tab-pane>
      <el-tab-pane label="机器应付款" name="third" lazy>
        <PayEquipment />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// import PaySummary from "@/views/financing/paySummary.vue";
import PayConsumables from "@/views/financing/components/statistics/PayConsumables.vue";
import PayEquipment from "@/views/financing/components/statistics/PayEquipment.vue";
export default {
  name: "Due",
  components: { PayConsumables, PayEquipment },
  data() {
    return {
      activeName: "second",
    };
  },
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss"></style>
