<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-04 15:35:13
 * @Description: 
 -->
<template>
  <div class="container">
    <ProDialog
      :value="showDialog"
      top="5%"
      width="60%"
      @ok="handleConfirm"
      @cancel="handleCloseDialog"
    >
      <ProForm
        ref="ProForm"
        :form-list="formColumns"
        :form-param="formParams"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
      >
        <template #longitude>
          <el-input
            v-model="formParams.location.longitude"
            placeholder="请输入经度"
          />
        </template>
        <template #latitude>
          <el-input
            v-model="formParams.location.latitude"
            placeholder="请输入纬度"
          />
        </template>
      </ProForm>
      <div class="map-box">
        <div
          id="mapContainer"
          ref="map"
          class="map"
          style="height: 500px"
        ></div>
        <div class="map-search">
          <el-autocomplete
            v-model="searchValue"
            :fetch-suggestions="querySearchAsync"
            style="width: 30%; margin-right: 10px"
            placeholder="请输入位置信息"
            @select="handleSelect"
          ></el-autocomplete>
          <el-button type="primary" @click="handleSearchInput(searchValue)">
            搜索
          </el-button>
        </div>
      </div>
    </ProDialog>
  </div>
</template>

<script>
import { Message } from "element-ui";
import { debounce } from "lodash";

export default {
  name: "TXMap",
  data() {
    return {
      showDialog: false,
      formParams: {
        location: {
          longitude: "",
          latitude: "",
        },
        address: "",
      },
      formLoading: false,
      addressData: {},
      formColumns: [
        {
          dataIndex: "longitude",
          title: "经度",
          isForm: true,
          formSpan: 12,
          valueType: "input",
          formSlot: "longitude",
        },
        {
          dataIndex: "latitude",
          title: "纬度",
          isForm: true,
          formSpan: 12,
          valueType: "input",
          formSlot: "latitude",
        },
        {
          dataIndex: "address",
          title: "详细地址",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入详细地址",
              trigger: "blur",
            },
          ],
        },
      ],
      searchValue: "",
      restaurants: [], // 搜索结果
      debouncedSearchAddress: null,
    };
  },
  created() {
    this.debouncedSearchAddress = debounce(this.searchAddress, 300);
    if (this.script) return;
    this.script = document.createElement("script");
    this.script.type = "text/javascript";
    this.script.src = `https://map.qq.com/api/gljs?v=1.exp&libraries=service&key=NKWBZ-JSNL3-6DC3G-OSDDG-5LX2T-RDBFW`;
    document.head.appendChild(this.script);
  },
  mounted() {},
  methods: {
    show(location = {}, address = "") {
      this.showDialog = true;
      this.formParams.location = location;
      this.formParams.address = address;
      this.$nextTick(() => {
        this.initMapData();
      });
    },
    // SXEBZ-CKGCU-5HFVN-GHNV7-VTJ5J-ADBQB
    initMapData() {
      if (this.$jsonp) {
        const url = "https://apis.map.qq.com/ws/location/v1/ip";
        this.$jsonp(url, {
          key: "NKWBZ-JSNL3-6DC3G-OSDDG-5LX2T-RDBFW",
          output: "jsonp",
        })
          .then((res) => {
            // 初始化地图、地图标点
            setTimeout(() => {
              this.initMap();
            }, 300);
          })
          .catch(() => {
            this.$message.info("地图正在努力加载中...");
            setTimeout(() => {
              this.initMapData();
            }, 1500);
          });
      } else {
        this.initMapData();
      }
    },
    initMap() {
      // 搜索类
      this.searchEr = new window.TMap.service.Search({ pageSize: 10 });
      // 地图主类
      this.map = new window.TMap.Map(this.$refs.map, {
        backgroundColor: "#f7f7f7",
        mapStyleId: "style1",
        zoom: 12,
        mapTypeControlOptions: {
          mapTypeIds: [],
        },
        draggableCursor: "crosshair",
        center: new window.TMap.LatLng(30.689186, 104.190682),
      });
      // 图层类
      this.markerLayer = new window.TMap.MultiMarker({
        map: this.map,
        geometries: [],
      });
      // 地址逆解析
      this.geocoder = new window.TMap.service.Geocoder();
      const setMarker = () => {
        const latlng = new window.TMap.LatLng(
          this.formParams.location.latitude,
          this.formParams.location.longitude
        );
        this.markerLayer.setGeometries([]);
        const geometries = this.markerLayer.getGeometries();
        geometries.push({
          id: "-1",
          position: latlng,
        });
        this.markerLayer.updateGeometries(geometries);
      };
      this.map.on("click", (e) => {
        this.formParams.location.longitude = e.latLng.getLng(); // 经度
        this.formParams.location.latitude = e.latLng.getLat(); // 纬度
        setMarker();
        this.getAddressFormat();
      });
      if (this.formParams.location.longitude) {
        this.map.setCenter(
          new window.TMap.LatLng(
            this.formParams.location.latitude,
            this.formParams.location.longitude
          )
        );
        setMarker();
      }
    },
    async querySearchAsync(queryString, cb) {
      if (!queryString) {
        cb([]);
        return;
      }
      await this.searchAddress(queryString);
      const results = this.restaurants.map((item) => {
        return {
          value: item.title,
          address: item.address,
          id: item.id,
          location: item.location,
        };
      });
      cb(results);
    },
    // 地图搜索
    searchAddress(keyword = "") {
      return new Promise((resolve, reject) => {
        if (!keyword) {
          resolve();
          return;
        }
        this.markerLayer.setGeometries([]);
        this.searchEr
          .searchRectangle({
            keyword,
            bounds: this.map.getBounds(),
          })
          .then((result) => {
            this.restaurants = result.data || [];
            const { location = {} } = result.data[0] || {};
            const { lat = "", lng = "" } = location;
            this.map.setCenter(new window.TMap.LatLng(lat, lng));
            result.data.forEach((item, index) => {
              const geometries = this.markerLayer.getGeometries();
              // 点标注数据数组
              geometries.push({
                id: String(index),
                position: item.location,
              });
              // 绘制地点标注
              this.markerLayer.updateGeometries(geometries);
            });
            resolve();
          })
          .catch((error) => {
            Message.error(error.message);
            reject(error);
          });
      });
    },
    handleSearchInput(keyword) {
      this.debouncedSearchAddress(keyword);
    },
    handleSelect(item) {
      this.searchAddress(item.value);
      this.formParams.location.latitude = item.location?.lat;
      this.formParams.location.longitude = item.location?.lng;
      this.formParams.address = item.address;
    },
    getAddressFormat() {
      const { longitude, latitude } = this.formParams.location;
      this.geocoder
        .getAddress({
          location: new window.TMap.LatLng(latitude, longitude),
        })
        .then((res) => {
          const {
            formatted_addresses: { standard_address = "" },
            ad_info: { adcode = "" },
          } = res.result || {};
          this.formParams.address = standard_address;
          this.formParams.regionCode = Number(adcode);
        })
        .catch((error) => {
          Message.error(error.message);
        });
    },

    handleConfirm() {
      this.$refs.ProForm.handleSubmit().then((res) => {
        console.log(this.formParams);
        this.$emit("confirm", this.formParams);
        this.showDialog = false;
      });
    },
    handleCloseDialog() {
      // 销毁地图元素
      this.map.destroy();
      this.showDialog = false;
    },
  },
};
</script>

<style scoped lang="scss">
.map-box {
  position: relative;
  user-select: none;
  .map-search {
    width: 100%;
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 9999;
  }
}
</style>
