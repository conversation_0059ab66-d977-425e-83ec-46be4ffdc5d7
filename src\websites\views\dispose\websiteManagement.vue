<template>
  <div class="view app-container">
    <el-tabs v-model="active" style="padding: 10px">
      <el-tab-pane label="仪表盘" name="dashboard" lazy>
        <Dashboard @tab-change="handleTabChange" />
      </el-tab-pane>
      <el-tab-pane label="内容编辑" name="content" lazy>
        <Content />
      </el-tab-pane>
      <el-tab-pane label="公共配置" name="config" lazy>
        <Config />
      </el-tab-pane>
      <el-tab-pane label="图片管理" name="images" lazy>
        <Images />
      </el-tab-pane>
      <el-tab-pane label="咨询管理" name="inquiries" lazy>
        <Inquiries />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Dashboard from "@/websites/views/website/dashboard.vue";
import Content from "@/websites/views/website/content.vue";
import Images from "@/websites/views/website/images.vue";
import Inquiries from "@/websites/views/website/inquiries.vue";
import Config from "@/websites/views/website/config.vue";

export default {
  name: "WebsiteManagement",
  components: { 
    Dashboard, 
    Content, 
    Images, 
    Inquiries, 
    Config 
  },
  data() {
    return {
      active: "dashboard",
    };
  },
  mounted() {
    // 根据URL参数设置默认tab
    const tab = this.$route.query.tab;
    if (tab && ['dashboard', 'content', 'images', 'inquiries', 'config'].includes(tab)) {
      this.active = tab;
    }
  },
  methods: {
    // 处理Dashboard组件的tab切换事件
    handleTabChange(tab) {
      this.active = tab;
    }
  },
  watch: {
    // 监听tab变化，更新URL参数
    active(newTab) {
      if (this.$route.query.tab !== newTab) {
        this.$router.replace({
          path: this.$route.path,
          query: { ...this.$route.query, tab: newTab }
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.view {
  .el-tabs {
    ::v-deep .el-tab-pane {
      padding: 0;
    }
    
    ::v-deep .el-tabs__header {
      margin-bottom: 20px;
    }
    
    ::v-deep .el-tabs__item {
      font-size: 14px;
      font-weight: 500;
      
      &.is-active {
        color: #409eff;
      }
    }
    
    ::v-deep .el-tabs__active-bar {
      background-color: #409eff;
    }
  }
}

// 确保tab内容区域有足够的高度
::v-deep .el-tab-pane {
  min-height: calc(100vh - 200px);
}
</style>
