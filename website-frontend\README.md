# 复印机维修服务企业官网前端项目

这是一个独立的企业官网前端项目，从react-modules项目中提取前端展示功能，专门用于部署到指定域名作为企业官网展示。

## 🚀 项目特性

- ⚡️ **现代化技术栈**：React 18 + TypeScript + Vite
- 📱 **完美移动适配**：Ant Design + Ant Design Mobile 响应式设计
- 🎨 **精美UI设计**：Tailwind CSS + 自定义主题
- 🔄 **状态管理**：React Query 数据管理
- 📦 **模块化架构**：清晰的项目结构和组件设计
- 🌐 **SEO优化**：完整的元数据和结构化数据
- 🚀 **性能优化**：代码分割、懒加载、缓存策略

## 📦 安装依赖

```bash
# 进入项目目录
cd website-frontend

# 安装依赖
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

## 🛠 开发命令

```bash
# 启动开发服务器
npm run dev

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 代码格式化
npm run format

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 📁 项目结构

```
src/
├── components/          # 组件库
│   └── layout/         # 布局组件
├── pages/              # 页面组件
│   └── website/        # 企业官网页面
│       ├── about/      # 关于我们
│       ├── services/   # 服务介绍
│       ├── cases/      # 案例展示
│       └── contact/    # 联系我们
├── services/           # API服务层
├── hooks/              # 自定义Hooks
├── utils/              # 工具函数
├── styles/             # 样式文件
└── types/              # TypeScript类型定义
```

## 🔗 后端API集成

项目集成现有的Spring Boot后端API：

- **前端展示API**：`/api/website-public/*` (匿名访问)
- **咨询提交API**：`/api/website-inquiry/submit` (匿名访问)
- **配置获取API**：`/api/website-public/config` (匿名访问)

## 📱 响应式设计

项目采用移动端优先的响应式设计：

- **手机端**：< 768px (Ant Design Mobile)
- **平板端**：768px - 1024px (响应式 Ant Design)
- **桌面端**：> 1024px (标准 Ant Design)

## 🌐 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 📝 开发规范

- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 代码规范
- 组件采用函数式组件 + Hooks
- 使用 Tailwind CSS 进行样式开发
- 遵循语义化 HTML 和无障碍设计

## 🚀 部署指南

### 1. 构建项目

```bash
npm run build
```

### 2. 部署后配置API地址

构建完成后，直接修改 `website/config.js` 文件来调整API地址：

```javascript
// website/config.js
window.APP_CONFIG = {
  // 修改为实际的后端API地址
  API_BASE_URL: 'https://your-api-domain.com/api',

  // 修改应用标题
  APP_TITLE: '您的企业名称'
};
```

### 3. 配置说明

- ✅ **统一配置**：只需修改一个 `config.js` 文件
- ✅ **无需重新构建**：部署后可直接修改配置文件
- ✅ **立即生效**：修改配置后刷新页面即可生效
- ✅ **多环境支持**：适合开发、测试、生产环境

| 配置项 | 说明 | 示例 |
|--------|------|------|
| `API_BASE_URL` | 后端API地址 | `https://api.example.com/api` |
| `APP_TITLE` | 应用标题 | `复印机维修服务` |

## 🔍 功能特性

### 页面功能

- **关于我们**：公司介绍、企业文化、核心优势
- **服务介绍**：服务项目、服务流程、选择优势
- **案例展示**：成功案例、服务数据统计
- **联系我们**：在线咨询表单、联系方式、地图导航

### 技术特性

- **响应式设计**：完美适配PC和移动端
- **SEO优化**：每个页面都有独立的SEO配置
- **性能优化**：代码分割、图片懒加载、缓存策略
- **错误处理**：完善的错误边界和用户友好的错误提示
- **无障碍支持**：遵循WCAG 2.1标准

## 📄 License

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📞 支持

如果在使用过程中遇到问题，请联系技术支持团队。
