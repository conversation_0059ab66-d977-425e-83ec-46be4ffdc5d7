<!--
 * @Author: wskg
 * @Date: 2025-03-14 17:07:32
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:22
 * @Description: 租赁、融资退机收货
 -->
<template>
  <div class="app-container">
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="75%"
      :confirm-button-disabled="confirmLoading"
      :method-type="methodType"
      :no-footer="methodType === 'info'"
      confirm-text="确认收货"
      @ok="handleSubmit"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="methodType"
        @proSubmit="formSubmit"
      >
        <!-- 合约机器信息 -->
        <template #signMachineInfo>
          <div class="title-box">退机列表</div>
          <ProTable
            ref="ProTable"
            :show-search="false"
            :show-pagination="false"
            :show-setting="false"
            :show-loading="false"
            height="65vh"
            :columns="columns"
            :data="tableData"
          >
            <!-- 主机类型 -->
            <template #type="{ row }">
              <el-select
                v-model="row.hostType"
                :disabled="true"
                placeholder="请选择主机类型"
                style="width: 100%"
                size="small"
              >
                <el-option
                  v-for="item in hostTypeListOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
            <template #productId="{ row }">
              <div
                v-if="row.hostType.value !== '2008'"
                style="display: flex; justify-content: space-between; gap: 20px"
              >
                <el-input
                  v-model="row.productName"
                  disabled
                  size="small"
                  placeholder="请选择选配件"
                />
              </div>
              <el-cascader
                v-else
                ref="ProductIds"
                v-model="row.productIdName"
                filterable
                clearable
                :options="options"
                style="width: 100%"
                size="small"
                placeholder="请选择机型/系列"
                :disabled="true"
                :show-all-levels="false"
                :props="{
                  label: 'name',
                  value: 'id',
                  children: 'children',
                  expandTrigger: 'click',
                  multiple: false,
                }"
                leaf-only
              ></el-cascader>
            </template>
            <!-- 成色 -->
            <template #percentage="{ row }">
              <el-select
                v-model="row.percentage"
                size="small"
                placeholder="请选择成色"
                :disabled="methodType === 'info'"
              >
                <el-option
                  v-for="item in percentageOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
            <template #price="{ row }">
              <el-input-number
                v-model="row.purchasePrice"
                :controls="false"
                :precision="2"
                size="small"
                :min="0"
                :disabled="methodType === 'info'"
                style="width: 100%"
              >
              </el-input-number>
            </template>
            <template #deviceOn="{ row }">
              <el-select
                v-model="row.deviceOn"
                style="width: 100%"
                clearable
                :disabled="methodType === 'info'"
              >
                <el-option
                  v-for="item in deviceOnOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
            <template #deviceStatus="{ row }">
              <el-select
                v-model="row.deviceStatus"
                style="width: 100%"
                clearable
                :disabled="methodType === 'info'"
              >
                <el-option
                  v-for="item in deviceStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
            <!-- 储位 -->
            <template #location="{ row }">
              <el-input
                v-model="row.location"
                size="small"
                clearable
                placeholder="请输入储位"
                :disabled="methodType === 'info'"
              />
            </template>
            <!-- 黑白计数器 -->
            <template #blackWhiteCounter="{ row }">
              <el-input-number
                v-model="row.blackWhiteCounter"
                style="width: 100%"
                size="small"
                :controls="false"
                :min="0"
                placeholder="请输入黑白计数器"
                controls-position="right"
                :disabled="methodType === 'info'"
              ></el-input-number>
            </template>
            <!-- 彩色计数器 -->
            <template #colorCounter="{ row }">
              <el-input-number
                v-model="row.colorCounter"
                style="width: 100%"
                size="small"
                :controls="false"
                :min="0"
                placeholder="请输入彩色计数器"
                controls-position="right"
                :disabled="methodType === 'info'"
              ></el-input-number>
            </template>
            <!-- 五色计数器 -->
            <template #fiveColourCounter="{ row }">
              <el-input-number
                v-model="row.fiveColourCounter"
                style="width: 100%"
                :controls="false"
                size="small"
                :min="0"
                placeholder="请输入五色计数器"
                controls-position="right"
                :disabled="methodType === 'info'"
              ></el-input-number>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import {
  customerMachineContractReturnApi,
  getCustomerContractReturnMachineApi,
} from "@/api/customer";
import { dictTreeByCodeApi } from "@/api/user";
import { productAllApi } from "@/api/dispose";

export default {
  name: "DispatchMachine",
  components: {},
  props: {
    contractType: {
      type: String,
      default: "1201",
    },
  },
  data() {
    return {
      drawerVisible: false,
      drawerTitle: "",
      confirmLoading: false,
      methodType: "add",
      form: {},
      formColumns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "code",
          title: "合同编号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "returnReason",
          title: "退机原因",
          isForm: true,
          formSpan: 24,
          valueType: "text",
        },
        {
          dataIndex: "signMachineInfo",
          title: "签约机器信息",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "signMachineInfo",
        },
      ],
      columns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "type",
          title: "主机类型",
          isTable: true,
          align: "center",
          tableSlot: "type",
          minWidth: 100,
        },
        {
          dataIndex: "productId",
          title: "机型/系列/选配件",
          isTable: true,
          align: "center",
          tableSlot: "productId",
          minWidth: 150,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          align: "center",
          tableSlot: "percentage",
          minWidth: 120,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          align: "center",
          tableSlot: "deviceOn",
          minWidth: 100,
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          align: "center",
          tableSlot: "deviceStatus",
          minWidth: 100,
        },
        {
          dataIndex: "purchasePrice",
          title: "单价",
          isTable: true,
          align: "center",
          tableSlot: "price",
          minWidth: 100,
        },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          align: "center",
          tableSlot: "location",
          minWidth: 100,
        },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          isTable: true,
          align: "center",
          tableSlot: "blackWhiteCounter",
          minWidth: 100,
        },
        {
          dataIndex: "colorCounter",
          title: "彩色计数器",
          isTable: true,
          align: "center",
          tableSlot: "colorCounter",
          minWidth: 100,
        },
        {
          dataIndex: "fiveColourCounter",
          title: "五色计数器",
          isTable: true,
          align: "center",
          tableSlot: "fiveColourCounter",
          minWidth: 100,
        },
      ],
      tableData: [],
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 10,
      },
      percentageOptions: [],
      deviceOnOptions: [],
      deviceStatusOptions: [],
      hostTypeListOptions: [],
      options: [],
    };
  },
  mounted() {},
  methods: {
    visible(val, type) {
      this.form = cloneDeep(val);
      this.tableData = [];
      this.methodType = type;
      this.drawerTitle = "机器收货";
      getCustomerContractReturnMachineApi(val.id).then((res) => {
        this.tableData = res.data || [];
        this.tableData.forEach((item) => {
          Object.keys(item).forEach((key) => {
            item[key] = item[key].value ? item[key].value : item[key];
          });
          if (item.hostType === "2008" && item.fullProductPath) {
            const trimmedStr = item.fullProductPath.trim("/");
            item.productIdName = trimmedStr.split("/").filter(Boolean);
          }
        });
      });
      this.drawerVisible = true;
      this.$nextTick(() => {
        productAllApi().then((res) => {
          this.options = res.data;
        });
        dictTreeByCodeApi(1100).then((res) => {
          this.deviceOnOptions = res.data;
        });
        dictTreeByCodeApi(6600).then((res) => {
          this.deviceStatusOptions = res.data;
        });
        this.getPercentage();
        this.getProductType();
      });
    },
    handleSubmit() {
      this.$refs.ProForm.handleSubmit();
    },
    async formSubmit(val) {
      try {
        this.confirmLoading = true;
        this.$confirm("是否确认收货货?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          const args = {
            contractCode: this.form.code,
            machines: this.tableData,
          };
          const result = await customerMachineContractReturnApi(args);
          if (result.code === 200) {
            this.$message.success("操作成功");
            this.$emit("refresh");
            this.drawerVisible = false;
          }
        });
      } finally {
        this.confirmLoading = false;
      }
    },
    async getPercentage() {
      try {
        const result = await dictTreeByCodeApi(2500);
        if (result.code === 200) {
          this.percentageOptions = result.data;
        }
      } catch (error) {
        this.percentageOptions = [];
      }
    },
    async getProductType() {
      try {
        const result = await dictTreeByCodeApi(2000);
        if (result.code === 200) {
          this.hostTypeListOptions = result.data;
        }
      } catch (error) {
        this.hostTypeListOptions = [];
      }
    },
    closeDrawer() {
      this.drawerVisible = false;
      this.$nextTick(() => {
        this.form = {};
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
