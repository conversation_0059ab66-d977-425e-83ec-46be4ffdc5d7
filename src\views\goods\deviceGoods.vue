<!--
 * @Description: 商品管理
 * @Autor: shh
 * @Date: 2022-11-16 16:42:14
 * @LastEditors: shan<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-29 10:53:08
-->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </template>
      <template #type="slotProps">
        {{ slotProps.row.type.label }}
      </template>
      <template #picsUrl="slotProps">
        <img
          style="max-width: 100px; max-height: 100px"
          :src="getPicsUrlImg(slotProps.row)"
        />
      </template>
      <template #saleStatus="slotProps">
        <el-switch
          v-model="slotProps.row.saleStatus"
          @change="handleSaleStatusChange(slotProps.row)"
        ></el-switch>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-zoom-out"
            @click="handleCheck(slotProps.row)"
          >
            查看
          </el-button>
          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="95%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <div class="dialog-content-box">
        <el-steps
          v-if="methodType != 'info'"
          align-center
          :active="active"
          finish-status="success"
          class="steps-box"
        >
          <el-step title="商品基本信息"></el-step>
          <el-step title="商品规格库存"></el-step>
          <el-step title="商品详细信息"></el-step>
          <el-step title="商品其他信息"></el-step>
        </el-steps>
        <div
          class="content-fix"
          :style="{ 'padding-top': methodType != 'info' ? '80px' : 0 }"
        >
          <!-- 商品基本信息 -->
          <div v-if="active == 0 || methodType == 'info'" class="tit-box">
            商品基本信息
          </div>
          <div
            v-show="active == 0 || methodType == 'info'"
            class="sp-content jbxx-box"
          >
            <el-form
              ref="proform_child1"
              :model="form"
              :disabled="methodType == 'info'"
              label-width="140px"
              class="demo-ruleForm"
            >
              <el-row>
                <el-col :span="24">
                  <el-form-item
                    label="商品名称:"
                    :rules="[
                      {
                        required: true,
                        trigger: 'blur',
                        message: '请输入商品名称',
                      },
                    ]"
                    prop="name"
                  >
                    <el-input
                      v-model="form.name"
                      style="width: 300px"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="24">
                  <el-form-item label="商品品牌:" prop="brandId">
                    <el-select v-model="form.brandId" :multiple="false">
                      <el-option
                        v-for="(item, index) in formcolumns.brandList"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col> -->
                <el-col :span="24">
                  <el-form-item
                    label="商品分类:"
                    :rules="[
                      {
                        required: true,
                        trigger: 'change',
                        message: '请选择商品分类',
                      },
                    ]"
                    prop="categoryId"
                  >
                    <el-select
                      v-model="form.categoryId"
                      style="width: 300px"
                      @change="handleCategoryChange"
                    >
                      <el-option
                        v-for="item in formcolumns.categoryList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col v-show="form.categoryId" :span="24">
                  <el-form-item label="分类属性:" prop="saleAttrVals">
                    <div
                      v-for="(item, index) in formcolumns.saleAttrValsList"
                      :key="index"
                    >
                      {{ item.name }}:
                      <el-radio-group
                        v-model="item.selectVal"
                        size="small"
                        style="margin-left: 20px"
                      >
                        <el-radio
                          v-for="(it, index) in item.children"
                          :key="index"
                          style="width: 80px; margin-right: 10px"
                          :label="it.value"
                          border
                          >{{ it.label }}</el-radio
                        >
                      </el-radio-group>
                    </div>

                    <!-- <el-tree class="filter-tree" :data="formcolumns.saleAttrValsList" :props="{
                      children: 'children',
                      label: 'label',
                    }" default-expand-all ref="tree">
                      <span slot-scope="{ node, data }">
                       
                        <el-radio-group v-if="!data.isParent" v-model="node.parent.data.selectVal">
                          <el-radio :label="data.value" @click.native.prevent="
                            radioClick(data.value, node.parent.data.id)
                            ">
                            {{ data.label }}
                          </el-radio>
                        </el-radio-group>
                        <span v-else>{{ data.label }}</span>
                      </span>
                    </el-tree> -->
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="商品图:"
                    :rules="[
                      {
                        required: true,
                        trigger: 'change',
                        message: '请选择商品图',
                      },
                    ]"
                    prop="picsUrl"
                  >
                    <ProUpload
                      :file-list="form.picsUrl"
                      :type="methodType"
                      :limit="5"
                      :multiple="true"
                      @uploadSuccess="handleUploadSuccess"
                      @uploadRemove="handleUploadRemove"
                    />
                    建议尺寸：800*800，默认首张图为主图，最多上传5张。
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="24">
                  <el-form-item label="商品状态:" :rules="[
                    {
                      required: true,
                      trigger: 'change',
                      message: '请选择商品状态',
                    },
                  ]" prop="saleStatus">
                    <el-radio-group v-model="form.saleStatus">
                      <el-radio v-for="(item, index) in formcolumns.saleStatusList" :label="item.value">{{ item.label
                      }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col> -->
              </el-row>
            </el-form>
          </div>
          <!-- 商品规格库存 -->
          <div v-if="active == 1 || methodType == 'info'" class="tit-box">
            商品规格库存
          </div>
          <div
            v-show="active == 1 || methodType == 'info'"
            class="sp-content jbxx-box"
          >
            <productSpecifications
              ref="ProductSpecificationsRef"
              :item-id="itemId"
              :method-type="methodType"
              @successProduct="handleSuccessProduct"
            ></productSpecifications>
          </div>
          <!-- 商品详细信息 -->
          <div v-if="active == 2 || methodType == 'info'" class="tit-box">
            商品详细信息
          </div>
          <div
            v-show="active == 2 || methodType == 'info'"
            class="sp-content editor-box"
          >
            <div
              v-show="methodType == 'info'"
              v-html="proWangeEditorContent"
            ></div>
            <ProWangeEditor
              v-show="methodType != 'info'"
              ref="ProWangeEditorRef"
            ></ProWangeEditor>
          </div>
          <!-- 商品其他信息 -->
          <div v-if="active == 3 || methodType == 'info'" class="tit-box">
            商品其他信息
          </div>
          <div v-show="active == 3 || methodType == 'info'" class="sp-content">
            <el-form
              ref="proform_child"
              :model="formOther"
              label-width="140px"
              class="demo-ruleForm"
              :disabled="methodType == 'info'"
            >
              <el-form-item
                label="物流方式"
                :rules="[
                  {
                    required: true,
                    trigger: 'change',
                  },
                ]"
                prop="wlfs"
              >
                <span>{{ formOther.wlfs }}</span>
              </el-form-item>
              <el-form-item label="运费设置" prop="shippingFee">
                <el-input
                  v-model="formOther.shippingFee"
                  style="width: 300px"
                  placeholder="请输入运费设置"
                  type="number"
                  :min="0"
                >
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
          <div v-if="methodType != 'info'" class="add-sp-box m-t-2">
            <el-button v-if="active > 0" @click="back()">上一步</el-button>
            <el-button v-if="active < 3" @click="next()">下一步</el-button>
            <el-button v-if="active == 0 || active == 3" @click="save()"
              >保存</el-button
            >
          </div>
        </div>
      </div>
    </ProDrawer>
  </div>
</template>
<script>
import {
  itemListApi,
  itemAddApi,
  itemUpdateDetailApi,
  itemUpdateSaleStatusApi,
  itemUpdateOtherInfoApi,
  itemDelApi,
  classifyListApi,
  classifyInfoApi,
  itemDetailByIdApi,
} from "@/api/goods";
import { brandListApi } from "@/api/brand";
import { isEmpty, cloneDeep } from "lodash";
import ProUpload from "@/components/ProUpload/index.vue";
import ProWangeEditor from "@/components/ProWangeEditor/index.vue";
import productSpecifications from "./productSpecifications.vue";
export default {
  name: "DeviceGoods",
  components: { ProWangeEditor, productSpecifications, ProUpload },
  mixins: [],
  props: {},
  data() {
    return {
      itemId: "",
      active: 0,
      // 列表
      spareiTypeList: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        aduitState: null,
        name: null,
      },
      columns: [
        {
          dataIndex: "categoryId",
          title: "商品分类",
          isSearch: true,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          clearable: true,
          isSearch: true,
          formSpan: 8,
          valueType: "select",
          option: [
            { label: "上架", value: "ON_SALE" },
            { label: "下架", value: "NO_SALE" },
          ],
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          clearable: true,
          isSearch: true,
          formSpan: 16,
          valueType: "input",
        },

        {
          dataIndex: "code",
          title: "商品编号",
          isTable: true,
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
        },
        {
          dataIndex: "name",
          title: "商品名称",
          isTable: true,
        },
        // {
        //   dataIndex: "商品类型",
        //   title: "商品类型",
        //   isTable: true,
        // },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
        },
        {
          dataIndex: "soldOutNum",
          title: "已售卖数量",
          isTable: true,
        },
        {
          dataIndex: "Actions",
          width: 240,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      methodType: "add", //新增
      confirmLoading: false,
      dialogTitle: "",
      dialogVisible: false,
      cloneDeepPicsUrl: [],
      form: {
        name: null,
        brandId: null,
        picsUrl: [],
        categoryId: null,
        saleAttrVals: [],
        saleStatus: "NO_SALE",
      },
      formcolumns: {
        brandList: [],
        categoryList: [],
        saleAttrValsList: [],
        saleStatusList: [
          { label: "上架", value: "ON_SALE" },
          { label: "下架", value: "NO_SALE" },
        ],
      },
      formOther: {
        wlfs: "快递配送",
        shippingFee: null,
      },
      proWangeEditorContent: null,
      rowInfo: {},
    };
  },
  computed: {},
  watch: {
    active(val) {
      if (this.methodType == "edit") {
        if (val == 1) {
          this.$refs.ProductSpecificationsRef?.echo(this.rowInfo);
        } else if (val == 2) {
          this.$refs.ProWangeEditorRef?.echo(this.rowInfo.detailHtml);
        } else if (val == 3) {
          this.proWangeEditorContent = this.rowInfo.detailHtml;
        }
      }
    },
  },
  created() {},
  mounted() {
    this.$refs.ProTable.refresh();
    this.init();
  },
  methods: {
    init() {
      brandListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.formcolumns.brandList = (res.data.rows || []).map((item) => ({
          label: item.brandName,
          value: item.id,
        }));
      });
      classifyListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.formcolumns.categoryList = this.columns[0].option = (
          res.data.rows || []
        ).map((item) => ({
          label: item.name,
          value: item.id,
        }));
      });
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    handleSaleStatusChange(row) {
      itemUpdateSaleStatusApi({
        itemId: row.id,
        saleStatusEnum: row.saleStatus ? "ON_SALE" : "NO_SALE",
      }).finally(() => {
        this.$refs.ProTable.refresh();
      });
    },
    /**
     * 商品分类切换
     * @param {String} val 选中的商品分类
     * @param {Object} echoData 回显数据
     */
    handleCategoryChange(val, echoData) {
      if (val) {
        classifyInfoApi({
          id: val,
        }).then((res) => {
          this.formcolumns.saleAttrValsList = (res.data?.tagList || []).map(
            (item) => ({
              ...item,
              isParent: true,
              label: item.name,
              value: item.id,
              selectVal: echoData?.[item.id] || null,
              children: item.value.map((itemC) => ({
                isParent: false,
                label: itemC,
                value: itemC,
              })),
            })
          );
        });
      } else {
        this.formcolumns.saleAttrValsList = [];
      }
    },
    next() {
      if (this.active == 0) {
        this.firstStep();
      } else if (this.active == 1) {
        this.$refs.ProductSpecificationsRef.handleSubmit();
      } else if (this.active == 2) {
        localStorage.setItem(
          "detailHtml",
          this.$refs.ProWangeEditorRef?.getContent()
        );
        itemUpdateDetailApi({
          id: this.itemId,
          detailHtml: this.$refs.ProWangeEditorRef?.getContent() || "",
        }).finally((_) => {
          if (this.active++ > 3) this.active = 0;
        });
      }
    },
    back() {
      if (this.active-- < 1) this.active = 0;
    },
    /**
     * 第一步
     * @params {Boolean} isExit true为退出新增商品
     */
    firstStep(isExit) {
      this.$refs["proform_child1"].validate((valid) => {
        if (valid) {
          const saleAttrValsTemp = {};
          this.formcolumns.saleAttrValsList.map((item) => {
            if (item.selectVal) {
              saleAttrValsTemp[item.id] = item.selectVal;
            }
          });
          const args = {
            ...this.form,
            id: this.itemId || null,
            saleAttrVals: saleAttrValsTemp,
          };
          if (this.methodType !== "add") {
            args["id"] = this.itemId;
          }
          itemAddApi(args)
            .then((res) => {
              if (!this.itemId) {
                this.itemId = res.data;
              }
            })
            .finally((_) => {
              if (this.active++ > 3) this.active = 0;
              if (isExit) {
                this.dialogVisible = false;
              }
            });
        } else {
          return false;
        }
      });
    },
    /**
     * 商品规格成功
     */
    handleSuccessProduct() {
      if (this.active++ > 3) this.active = 0;
    },
    save() {
      if (this.active == 0) {
        this.firstStep(true);
      } else if (this.active == 3) {
        itemUpdateOtherInfoApi({
          ...this.formOther,
          id: this.itemId,
        }).finally((_) => {
          this.$refs.ProTable.refresh();
          this.dialogVisible = false;
          this.active = 0;
          this.itemId = null;
          this.$refs.ProductSpecificationsRef.clearStorage();
          localStorage.removeItem("detailHtml");
        });
      }
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign({}, parameter);
      itemListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows.map((item) => ({
            ...item,
            saleStatus: item.saleStatus === "ON_SALE",
          }));
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.form);
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
    },
    //触发新增
    handleAdd() {
      this.dialogTitle = "新增商品";
      this.methodType = "add";
      this.active = 0;
      this.resetFrom();
      this.dialogVisible = true;
      this.form = this.$options.data().form;
      this.formOther = this.$options.data().formOther;
      this.cloneDeepPicsUrl = [];
      this.$refs.ProductSpecificationsRef.clearStorage();
      localStorage.removeItem("detailHtml");
      this.$nextTick(() => {
        this.formcolumns.saleAttrValsList =
          this.formcolumns.saleAttrValsList.map((item) => ({
            ...item,
            selectVal: false,
          }));
        this.$refs["proform_child1"].clearValidate();
        console.log(this.form);
      });
    },
    //触发详情
    handleCheck(row) {
      this.dialogTitle = `查看 - ${row.name}`;
      this.methodType = "info";
      this.dialogVisible = true;
      this.form.picsUrl = [];
      this.getQueryDetail(row);
      this.$refs.ProductSpecificationsRef.clearStorage();
      localStorage.removeItem("detailHtml");
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = `编辑 - ${row.name}`;
      this.itemId = row.id;
      this.methodType = "edit";
      this.dialogVisible = true;
      this.active = 0;
      this.cloneDeepPicsUrl = [];
      this.form.picsUrl = [];
      this.getQueryDetail(row);
      this.$refs.ProductSpecificationsRef.clearStorage();
      localStorage.removeItem("detailHtml");
    },
    getQueryDetail(row) {
      itemDetailByIdApi(row.id).then((res) => {
        const data = res.data || {};
        this.rowInfo = data;
        this.cloneDeepPicsUrl = cloneDeep(data.picsUrl || []);
        this.form = {
          ...this.form,
          name: data.name,
          brandId: data.brandId,
          picsUrl: cloneDeep(data.picsUrl || []),
          categoryId: data.categoryId,
          saleAttrVals: data.saleAttrVals || [],
          saleStatus: data.saleStatus,
        };
        this.formOther = {
          ...this.formOther,
          shippingFee: data.shippingFee,
        };
        this.handleCategoryChange(data.categoryId, data.saleAttrVals);
        this.$nextTick((_) => {
          if (this.methodType == "info") {
            this.$refs.ProductSpecificationsRef?.echo(data);
            this.$refs.ProWangeEditorRef?.echo(data.detailHtml);
            this.proWangeEditorContent = data.detailHtml;
          }
        });
      });
    },
    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          itemDelApi(data.id)
            .then(() => {
              this.$message.success("删除成功");
            })
            .finally(() => {
              this.$refs.ProTable.refresh();
            });
        })
        .catch((_) => {});
    },
    handleRemove(file) {
      console.log(file);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleDownload(file) {
      console.log(file);
    },
    // 图片处理
    handleUploadSuccess(result) {
      this.form.picsUrl.push(result);
    },
    handleUploadRemove(file) {
      const index = this.form.picsUrl.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      this.form.picsUrl.splice(index, 1);
    },
    /**
     * 可以去单选框的选中值
     */
    radioClick(ownVal, parentId) {
      const temp = this.formcolumns.saleAttrValsList.find(
        (item) => item.id == parentId
      );
      temp.selectVal == ownVal
        ? (temp.selectVal = "")
        : (temp.selectVal = ownVal);
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-upload--picture-card,
::v-deep .el-upload-list__item {
  width: 120px;
  height: 120px;
}

.sp-content {
  padding: 10px;
  width: 90%;
  margin: auto;
}

.add-sp-box {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translate(-50%, 0);
}

.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-content-box {
  position: relative;

  .steps-box {
    position: absolute;
    width: 80%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    z-index: 2;
  }

  .content-fix {
    height: calc(100vh - 110px);
    overflow: auto;
  }

  .tit-box {
    width: 90%;
    padding: 5px 10px;
    color: #409eff;
    position: relative;
    margin: 20px auto;
    font-size: 16px;
    font-weight: 800;
    border-bottom: 1px solid #dcdfe6;

    &::before {
      content: "";
      width: 5px;
      height: 20px;
      background: #409eff;
      display: inline-block;
      position: absolute;
      left: -1px;
      top: 4px;
    }
  }
}
</style>
