<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-10-12 10:24:32
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-10-12 10:59:28
 * @FilePath: \benyin-web\src\views\engineer\components\registerInstallStat.vue
 * @Description: 登记安装统计组件
 * 
-->

<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    ></ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";

export default {
  name: "RegisterInstallStat",
  props: {
    type: {
      type: String,
      default: "full",
    },
    columns: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    /**
     * @description: 加载表格数据
     * @param {*} parameter
     * @return {*}
     */
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const editApi = this.getMethodsApi(this.type);
      if (!editApi) {
        return (this.$refs.ProTable.listLoading = false);
      }
      const searchRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.month,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      ["month"].forEach((key) => delete requestParameters[key]);
      editApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    getMethodsApi(type) {
      switch (type) {
        case "full":
          return "";
        case "detail":
          return "";
        case "stat":
          return "";
        default:
          return "";
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
