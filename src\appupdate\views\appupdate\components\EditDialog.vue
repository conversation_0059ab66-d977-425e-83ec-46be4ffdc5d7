<!--
 * @Author: AI Assistant
 * @Date: 2025-01-29
 * @Description: 编辑版本对话框组件
-->
<template>
  <el-dialog
    title="编辑版本信息"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    destroy-on-close
  >
    <el-form 
      ref="editForm" 
      :model="formData" 
      :rules="formRules"
      label-width="120px"
    >
      <!-- 基本信息（只读） -->
      <el-form-item label="版本名称">
        <el-input 
          v-model="formData.versionName" 
          disabled
        />
        <div class="form-tip">版本名称不可修改</div>
      </el-form-item>
      
      <el-form-item label="版本号">
        <el-input-number 
          v-model="formData.versionCode"
          disabled
          style="width: 100%"
        />
        <div class="form-tip">版本号不可修改</div>
      </el-form-item>
      
      <!-- 文件信息（只读） -->
      <el-form-item label="APK文件">
        <div class="file-info-readonly">
          <div class="file-item">
            <i class="el-icon-document"></i>
            <span class="file-name">{{ formData.apkFileName }}</span>
            <span class="file-size">({{ formatFileSize(formData.fileSize) }})</span>
          </div>
          <div class="file-actions">
            <el-button 
              size="mini" 
              type="text" 
              @click="downloadFile"
            >
              下载
            </el-button>
            <el-button 
              size="mini" 
              type="text" 
              @click="copyDownloadLink"
            >
              复制链接
            </el-button>
          </div>
        </div>
      </el-form-item>
      
      <!-- 可编辑字段 -->
      <el-form-item label="更新说明" prop="updateLog">
        <ProWangeEditor
          ref="ProWangeEditorRef"
          :content="formData.updateLog"
          :height="400"
          @onchange="onUpdateLogChange"
        />
      </el-form-item>
      
      <el-form-item label="版本设置">
        <div class="version-settings">
          <div class="setting-item">
            <el-checkbox v-model="formData.isForce">
              强制更新
              <el-tooltip content="启用后，用户必须更新到此版本才能继续使用应用" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </el-checkbox>
          </div>
          
          <div class="setting-item">
            <el-checkbox v-model="formData.isActive">
              启用版本
              <el-tooltip content="禁用后，此版本将不会推送给用户" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </el-checkbox>
          </div>
          
          <div class="setting-item admin-force">
            <el-alert
              v-if="formData.adminForce"
              title="管理员强制更新已启用"
              type="warning"
              :closable="false"
              show-icon
            >
              <div slot="description">
                此版本当前被设置为管理员强制更新，优先级最高。
                可通过版本列表中的"取消强制"按钮来取消。
              </div>
            </el-alert>
          </div>
        </div>
      </el-form-item>
      
      <!-- 统计信息 -->
      <el-form-item label="统计信息">
        <div class="stats-info">
          <!-- 发布时间 -->
          <div class="stat-row">
            <div class="stat-label">发布时间</div>
            <div class="stat-value">{{ formatDateTime(formData.createdAt) }}</div>
          </div>

          <!-- 文件MD5 -->
          <div class="stat-row">
            <div class="stat-label">文件MD5</div>
            <div class="stat-value md5-value" :title="formData.fileMd5">
              {{ formData.fileMd5 || '暂无' }}
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        :loading="saving"
        @click="handleSubmit"
      >
        {{ saving ? '保存中...' : '保存修改' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateVersion, formatFileSize } from '@/appupdate/api/appVersion';
import ProWangeEditor from '@/components/ProWangeEditor/index.vue';

export default {
  name: 'EditDialog',
  components: {
    ProWangeEditor
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    versionData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      saving: false,
      
      // 表单数据
      formData: {
        id: null,
        versionName: '',
        versionCode: null,
        apkFileName: '',
        fileSize: 0,
        fileMd5: '',
        cosUrl: '',
        updateLog: '',
        isForce: false,
        isActive: true,
        adminForce: false,
        downloadCount: 0,
        createdAt: '',
      },
      
      // 原始数据（用于比较是否有修改）
      originalData: {},
      
      // 表单验证规则
      formRules: {
        updateLog: [
          // 更新说明为可选字段，不进行必填验证
        ],
      },
    };
  },
  computed: {
    hasChanges() {
      return this.formData.updateLog !== this.originalData.updateLog ||
             this.formData.isForce !== this.originalData.isForce ||
             this.formData.isActive !== this.originalData.isActive;
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val && this.versionData) {
        this.initFormData();
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    },
    versionData: {
      handler(val) {
        if (val && this.visible) {
          this.initFormData();
        }
      },
      deep: true,
    },
  },
  methods: {
    /**
     * 初始化表单数据
     */
    initFormData() {
      this.formData = {
        id: this.versionData.id,
        versionName: this.versionData.versionName || '',
        versionCode: this.versionData.versionCode || 0,
        apkFileName: this.versionData.apkFileName || '',
        fileSize: parseInt(this.versionData.fileSize) || 0,
        fileMd5: this.versionData.fileMd5 || '',
        cosUrl: this.versionData.cosUrl || '',
        updateLog: this.versionData.updateLog || '',
        isForce: this.versionData.isForce || false,
        isActive: this.versionData.isActive !== false, // 默认为true
        adminForce: this.versionData.adminForce || false,
        downloadCount: this.versionData.downloadCount || 0,
        createdAt: this.versionData.createdAt || '',
      };
      
      // 保存原始数据
      this.originalData = {
        updateLog: this.formData.updateLog,
        isForce: this.formData.isForce,
        isActive: this.formData.isActive,
      };

      // 等待下一个tick后设置富文本编辑器内容
      this.$nextTick(() => {
        if (this.$refs.ProWangeEditorRef) {
          this.$refs.ProWangeEditorRef.echo(this.formData.updateLog || '');
        }
      });
    },

    /**
     * 下载文件
     */
    downloadFile() {
      if (this.formData.cosUrl) {
        window.open(this.formData.cosUrl, '_blank');
      } else {
        this.$message.warning('下载链接不可用');
      }
    },

    /**
     * 复制下载链接
     */
    async copyDownloadLink() {
      try {
        await navigator.clipboard.writeText(this.formData.cosUrl);
        this.$message.success('下载链接已复制到剪贴板');
      } catch (error) {
        this.$message.error('复制失败，请手动复制');
      }
    },

    /**
     * 富文本内容变化处理
     */
    onUpdateLogChange(content) {
      this.formData.updateLog = content;
    },

    /**
     * 提交表单
     */
    async handleSubmit() {
      try {
        // 表单验证
        const valid = await this.$refs.editForm.validate();
        if (!valid) return;
        
        // 检查是否有修改
        if (!this.hasChanges) {
          this.$message.info('没有修改内容');
          return;
        }
        
        this.saving = true;
        
        // 构建更新数据（包含后端要求的必要字段）
        const updateData = {
          versionName: this.formData.versionName,
          versionCode: this.formData.versionCode,
          updateLog: this.formData.updateLog,
          isForce: this.formData.isForce,
          isActive: this.formData.isActive,
        };
        
        // 调用更新API
        await updateVersion(this.formData.id, updateData);
        
        this.$emit('success');
        
      } catch (error) {
        this.$message.error('版本信息更新失败，请重试');
      } finally {
        this.saving = false;
      }
    },

    /**
     * 关闭对话框
     */
    handleClose() {
      if (this.saving) {
        this.$message.warning('正在保存中，请稍候...');
        return;
      }
      
      // 检查是否有未保存的修改
      if (this.hasChanges) {
        this.$confirm('有未保存的修改，确认关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.dialogVisible = false;
        }).catch(() => {
          // 用户取消关闭
        });
      } else {
        this.dialogVisible = false;
      }
    },

    /**
     * 格式化文件大小
     */
    formatFileSize,

    /**
     * 格式化日期时间
     */
    formatDateTime(dateTime) {
      if (!dateTime) return '';
      return this.$moment(dateTime).format('YYYY-MM-DD HH:mm:ss');
    },
  },
};
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.file-info-readonly {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  
  .file-item {
    display: flex;
    align-items: center;
    flex: 1;
    
    i {
      color: #409eff;
      margin-right: 8px;
    }
    
    .file-name {
      font-weight: 500;
      margin-right: 8px;
    }
    
    .file-size {
      color: #909399;
      font-size: 12px;
    }
  }
  
  .file-actions {
    display: flex;
    gap: 8px;
  }
}

.version-settings {
  .setting-item {
    margin-bottom: 12px;
    
    &.admin-force {
      margin-top: 16px;
    }
  }
}

.stats-info {
  .stat-row {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .stat-label {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
    width: 80px;
    flex-shrink: 0;
  }

  .stat-value {
    font-size: 14px;
    color: #303133;
    flex: 1;

    &.md5-value {
      font-family: monospace;
      cursor: pointer;
      word-break: break-all;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .file-info-readonly {
    flex-direction: column;
    align-items: flex-start;
    
    .file-actions {
      margin-top: 8px;
      align-self: flex-end;
    }
  }
}
</style>
