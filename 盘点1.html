<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存盘点原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }
        /* 自定义滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        .modal {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .modal-content {
            transition: transform 0.3s ease;
        }
        .status-tag {
            padding: 4px 10px;
            border-radius: 9999px;
            font-size: 0.8rem;
            font-weight: 500;
            white-space: nowrap;
        }
        .status-ok {
            background-color: #e6f7f2;
            color: #00875a;
        }
        .status-pending {
            background-color: #fff4e5;
            color: #ff9900;
        }
        .status-error {
            background-color: #ffebe5;
            color: #de350b;
        }
        .feedback-message {
            transition: opacity 0.5s ease;
        }
    </style>
</head>
<body class="bg-gray-50">

    <div class="container mx-auto p-4 md:p-6 lg:p-8">
        <!-- 头部信息 -->
        <header class="bg-white p-6 rounded-xl shadow-sm mb-6 border border-gray-200">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">库存盘点任务</h1>
                    <p class="text-sm text-gray-500 mt-1">盘点单ID: <span id="takeStockId" class="font-semibold text-gray-600"></span></p>
                </div>
                <div class="mt-4 md:mt-0 text-right">
                    <p class="text-sm text-gray-500">总计 <span id="totalItems" class="font-bold text-blue-600">0</span> 项</p>
                    <p class="text-sm text-gray-500">总金额 <span id="totalAmount" class="font-bold text-green-600">¥0.00</span></p>
                </div>
            </div>
        </header>

        <!-- 物品列表 -->
        <main class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-600">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-100 border-b border-gray-200">
                        <tr>
                            <th scope="col" class="px-6 py-4">物品信息</th>
                            <th scope="col" class="px-6 py-4">库位</th>
                            <th scope="col" class="px-6 py-4 text-right">库存数量</th>
                            <th scope="col" class="px-6 py-4 text-right">总金额</th>
                            <th scope="col" class="px-6 py-4 text-center">盘点操作</th>
                        </tr>
                    </thead>
                    <tbody id="inventory-list">
                        <!-- 列表项将通过JS动态插入 -->
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <!-- 盘点模态框 -->
    <div id="check-modal" class="modal fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 opacity-0 pointer-events-none">
        <div class="modal-content bg-white rounded-2xl shadow-2xl w-11/12 md:w-2/3 lg:w-1/2 max-w-4xl transform scale-95">
            <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-bold text-gray-800">盘点详情</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 transition">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                </button>
            </div>
            
            <div class="p-6 max-h-[60vh] overflow-y-auto">
                <!-- 物品主信息 -->
                <div class="flex items-start space-x-4 mb-6 pb-6 border-b border-gray-200">
                    <img id="modal-img" src="" class="w-24 h-24 rounded-lg object-cover border border-gray-200" onerror="this.onerror=null;this.src='https://placehold.co/96x96/e2e8f0/adb5bd?text=无图';">
                    <div>
                        <h4 id="modal-articleName" class="text-xl font-semibold text-gray-800"></h4>
                        <p class="text-sm text-gray-500">物品编码: <span id="modal-articleCode" class="font-mono"></span></p>
                        <p class="text-sm text-gray-500">OEM号: <span id="modal-numberOem" class="font-mono"></span></p>
                        <p class="text-sm text-gray-500">库位: <span id="modal-location" class="font-semibold bg-gray-100 px-2 py-1 rounded"></span></p>
                    </div>
                </div>

                <!-- 盘点总数输入 -->
                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
                    <label for="modal-total-check-num" class="block text-sm font-medium text-gray-700">盘点总数</label>
                    <input type="number" id="modal-total-check-num" placeholder="手动输入实盘总数" class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition">
                    <p id="feedback-message" class="text-sm font-semibold text-red-600 h-4 mt-2 opacity-0 feedback-message"></p>
                </div>

                <!-- 批次详情 -->
                <h5 class="font-semibold text-gray-700 mb-4">批次明细</h5>
                <div id="modal-batch-list" class="space-y-4">
                    <!-- 批次信息将通过JS动态插入 -->
                </div>
            </div>

            <div class="p-6 bg-gray-50 rounded-b-2xl flex justify-end space-x-3">
                <button onclick="closeModal()" class="px-5 py-2.5 text-sm font-medium text-gray-700 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-blue-300 transition">取消</button>
                <button onclick="confirmCheck()" class="px-5 py-2.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 transition">确认盘点</button>
            </div>
        </div>
    </div>

    <script>
        // 原始JSON数据
        const jsonData = {
            "rows": [
                { "id": "1953285353297170433", "takeStockId": "1953285106890199042", "articleCode": "WPID241221000399", "articleName": "C6503原装分装粉 黑K（盒装716g）", "numberOem": "842180", "manufacturerChannel": { "value": "2201", "label": "原装分装" }, "location": "I2-3", "inventoryAmount": "219.00", "inventoryNum": 1, "price": "219.00", "batchInfo": [{ "price": 219, "num": 1, "batcheCode": "250727013" }], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/4e7cabd0-e537-11ef-9821-a7c5cb668e6b" }], "batchCode": "250727013", "num": 1 },
                { "id": "1953285419156131842", "takeStockId": "1953285106890199042", "articleCode": "WPID241221000400", "articleName": "C6503原装分装粉 红M（盒装716g）", "numberOem": "842182", "manufacturerChannel": { "value": "2201", "label": "原装分装" }, "location": "I2-3", "inventoryAmount": "239.00", "inventoryNum": 1, "price": "239.00", "batchInfo": [{ "price": 239, "num": 1, "batcheCode": "250727014" }], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/5924a330-e537-11ef-9821-a7c5cb668e6b" }], "batchCode": "250727014", "num": 1 },
                { "id": "1953285483891019777", "takeStockId": "1953285106890199042", "articleCode": "WPID250123000025", "articleName": "C6503原装分装粉 黄Y（盒装716g）", "numberOem": "842181", "manufacturerChannel": { "value": "2201", "label": "原装分装" }, "location": "I2-3", "inventoryAmount": "239.00", "inventoryNum": 1, "price": "239.00", "batchInfo": [{ "price": 239, "num": 1, "batcheCode": "250727012" }], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/382b0480-e537-11ef-9821-a7c5cb668e6b" }], "batchCode": "250727012", "num": 1 },
                { "id": "1953285532884684802", "takeStockId": "1953285106890199042", "articleCode": "WPID250123000026", "articleName": "C6503原装分装粉 青C（盒装716g）", "numberOem": "842183", "manufacturerChannel": { "value": "2201", "label": "原装分装" }, "location": "I2-3", "inventoryAmount": "239.00", "inventoryNum": 1, "price": "239.00", "batchInfo": [{ "price": 239, "num": 1, "batcheCode": "250727011" }], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/4283f180-e537-11ef-9821-a7c5cb668e6b" }], "batchCode": "250727011", "num": 1 },
                { "id": "1953285724505657345", "takeStockId": "1953285106890199042", "articleCode": "WPID250527000002", "articleName": "一转清洁震动板驱动", "numberOem": "M0EBU306", "manufacturerChannel": { "value": "2202", "label": "原装" }, "location": "I2-3", "inventoryAmount": "120.00", "inventoryNum": 1, "price": "120.00", "batchInfo": [{ "price": 120, "num": 1, "batcheCode": "250801005" }], "imageFiles": [], "batchCode": "250801005", "num": 1 },
                { "id": "1953286313079754753", "takeStockId": "1953285106890199042", "articleCode": "WPID250217000017", "articleName": "TN622C青色碳粉", "numberOem": "A5E7450", "manufacturerChannel": { "value": "2202", "label": "原装" }, "location": "H1-1", "inventoryAmount": "2080.00", "inventoryNum": 4, "price": "520.00", "batchInfo": [], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/9f47efe0-ef62-11ef-b1d3-c1a6795a5170" }], "batchCode": "250220011", "num": 4 },
                { "id": "1953286313079754754", "takeStockId": "1953285106890199042", "articleCode": "WPID250217000017", "articleName": "TN622C青色碳粉", "numberOem": "A5E7450", "manufacturerChannel": { "value": "2202", "label": "原装" }, "location": "H1-1", "inventoryAmount": "2080.00", "inventoryNum": 4, "price": "520.00", "batchInfo": [], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/9f47efe0-ef62-11ef-b1d3-c1a6795a5170" }], "batchCode": "250623003", "num": 4 },
                { "id": "1953286313079754755", "takeStockId": "1953285106890199042", "articleCode": "WPID250217000017", "articleName": "TN622C青色碳粉", "numberOem": "A5E7450", "manufacturerChannel": { "value": "2202", "label": "原装" }, "location": "H1-1", "inventoryAmount": "1040.00", "inventoryNum": 2, "price": "520.00", "batchInfo": [], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/9f47efe0-ef62-11ef-b1d3-c1a6795a5170" }], "batchCode": "250623016", "num": 2 },
                { "id": "1953286682719571970", "takeStockId": "1953285106890199042", "articleCode": "WPID250217000018", "articleName": "TN622K黑色碳粉", "numberOem": "A5E7150", "manufacturerChannel": { "value": "2202", "label": "原装" }, "location": "H1-1", "inventoryAmount": "1740.00", "inventoryNum": 6, "price": "290.00", "batchInfo": [], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/b3064220-ef62-11ef-b1d3-c1a6795a5170" }], "batchCode": "250220015", "num": 6 },
                { "id": "1953286682719571971", "takeStockId": "1953285106890199042", "articleCode": "WPID250217000018", "articleName": "TN622K黑色碳粉", "numberOem": "A5E7150", "manufacturerChannel": { "value": "2202", "label": "原装" }, "location": "H1-1", "inventoryAmount": "1160.00", "inventoryNum": 4, "price": "290.00", "batchInfo": [], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/b3064220-ef62-11ef-b1d3-c1a6795a5170" }], "batchCode": "250623001", "num": 4 },
                { "id": "1953287071439278081", "takeStockId": "1953285106890199042", "articleCode": "WPID250217000016", "articleName": "TN622M红色碳粉", "numberOem": "A5E7350", "manufacturerChannel": { "value": "2202", "label": "原装" }, "location": "H1-1", "inventoryAmount": "510.00", "inventoryNum": 1, "price": "510.00", "batchInfo": [], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/a8a5b400-ef62-11ef-b1d3-c1a6795a5170" }], "batchCode": "250220012", "num": 1 },
                { "id": "1953287071439278082", "takeStockId": "1953285106890199042", "articleCode": "WPID250217000016", "articleName": "TN622M红色碳粉", "numberOem": "A5E7350", "manufacturerChannel": { "value": "2202", "label": "原装" }, "location": "H1-1", "inventoryAmount": "2040.00", "inventoryNum": 4, "price": "510.00", "batchInfo": [], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/a8a5b400-ef62-11ef-b1d3-c1a6795a5170" }], "batchCode": "250623002", "num": 4 },
                { "id": "1953287071439278083", "takeStockId": "1953285106890199042", "articleCode": "WPID250217000016", "articleName": "TN622M红色碳粉", "numberOem": "A5E7350", "manufacturerChannel": { "value": "2202", "label": "原装" }, "location": "H1-1", "inventoryAmount": "2550.00", "inventoryNum": 5, "price": "510.00", "batchInfo": [], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/a8a5b400-ef62-11ef-b1d3-c1a6795a5170" }], "batchCode": "250623015", "num": 5 },
                { "id": "1953287328470421506", "takeStockId": "1953285106890199042", "articleCode": "WPID250217000019", "articleName": "TN622Y黄色碳粉", "numberOem": "A5E7250", "manufacturerChannel": { "value": "2202", "label": "原装" }, "location": "H1-1", "inventoryAmount": "2320.00", "inventoryNum": 4, "price": "580.00", "batchInfo": [], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/9605f120-ef62-11ef-b1d3-c1a6795a5170" }], "batchCode": "250623004", "num": 4 },
                { "id": "1953287328470421507", "takeStockId": "1953285106890199042", "articleCode": "WPID250217000019", "articleName": "TN622Y黄色碳粉", "numberOem": "A5E7250", "manufacturerChannel": { "value": "2202", "label": "原装" }, "location": "H1-1", "inventoryAmount": "3480.00", "inventoryNum": 6, "price": "580.00", "batchInfo": [], "imageFiles": [{ "url": "https://sczjzy-1332842668.cos.ap-chengdu.myqcloud.com/prod/9605f120-ef62-11ef-b1d3-c1a6795a5170" }], "batchCode": "250623017", "num": 6 }
            ]
        };

        // 用于存储处理后的数据和盘点状态
        let processedData = {};
        let checkStatus = {};

        document.addEventListener('DOMContentLoaded', function() {
            // 1. 数据预处理：按 articleCode 分组
            const groupedData = jsonData.rows.reduce((acc, item) => {
                const code = item.articleCode;
                if (!acc[code]) {
                    acc[code] = {
                        ...item, // 使用第一个遇到的项目作为基础信息
                        batches: [],
                        totalInventoryNum: 0,
                        totalInventoryAmount: 0,
                    };
                }
                // 将当前项目作为一个批次添加
                acc[code].batches.push({
                    id: item.id,
                    batchCode: item.batchCode,
                    num: item.num,
                    price: parseFloat(item.price)
                });
                acc[code].totalInventoryNum += item.num;
                acc[code].totalInventoryAmount += parseFloat(item.inventoryAmount);
                return acc;
            }, {});
            
            processedData = groupedData;

            // 2. 渲染列表
            renderList();

            // 3. 更新头部统计
            updateHeader();
        });

        function renderList() {
            const listElement = document.getElementById('inventory-list');
            listElement.innerHTML = ''; // 清空列表

            Object.values(processedData).forEach(item => {
                const status = checkStatus[item.articleCode] || { state: 'pending', text: '待盘点' };
                
                let actionCellHtml = '';
                if (status.state === 'pending') {
                    actionCellHtml = `
                        <div class="flex items-center justify-center space-x-2">
                            <input type="number" id="quick-check-${item.articleCode}" 
                                   placeholder="输入总数" 
                                   class="w-24 p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                                   onblur="quickConfirm('${item.articleCode}')"
                                   onkeydown="handleQuickConfirmKey(event, '${item.articleCode}')">
                            <button onclick="openModal('${item.articleCode}')" class="px-3 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition">详情</button>
                        </div>
                    `;
                } else {
                    const statusClass = status.state === 'ok' ? 'status-ok' : 'status-error';
                    actionCellHtml = `
                        <div class="flex items-center justify-center space-x-2">
                            <span class="status-tag ${statusClass}">${status.text} (${status.totalCheckedNum})</span>
                            <button onclick="openModal('${item.articleCode}')" class="text-sm font-medium text-blue-600 hover:text-blue-800 transition">编辑</button>
                        </div>
                    `;
                }

                const row = `
                    <tr id="row-${item.articleCode}" class="border-b border-gray-200 hover:bg-gray-50 transition">
                        <td class="px-6 py-4">
                            <div class="flex items-center space-x-4">
                                <img src="${item.imageFiles[0]?.url || ''}" class="w-12 h-12 rounded-md object-cover" onerror="this.onerror=null;this.src='https://placehold.co/48x48/e2e8f0/adb5bd?text=无图';">
                                <div>
                                    <div class="font-semibold text-gray-800">${item.articleName}</div>
                                    <div class="text-xs text-gray-500 font-mono">${item.articleCode}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 font-semibold">${item.location}</td>
                        <td class="px-6 py-4 text-right font-semibold text-lg">${item.totalInventoryNum}</td>
                        <td class="px-6 py-4 text-right font-semibold text-green-600">¥${item.totalInventoryAmount.toFixed(2)}</td>
                        <td class="px-6 py-4 text-center">
                            ${actionCellHtml}
                        </td>
                    </tr>
                `;
                listElement.innerHTML += row;
            });
        }
        
        function handleQuickConfirmKey(event, articleCode) {
            if (event.key === 'Enter') {
                // 当按下回车时，让输入框失去焦点，从而触发 onblur 事件
                event.target.blur();
            }
        }

        function updateHeader() {
            const totalItems = Object.keys(processedData).length;
            const totalAmount = Object.values(processedData).reduce((sum, item) => sum + item.totalInventoryAmount, 0);
            
            document.getElementById('takeStockId').textContent = jsonData.rows[0]?.takeStockId || 'N/A';
            document.getElementById('totalItems').textContent = totalItems;
            document.getElementById('totalAmount').textContent = `¥${totalAmount.toFixed(2)}`;
        }

        function quickConfirm(articleCode) {
            const inputElement = document.getElementById(`quick-check-${articleCode}`);
            if (!inputElement) return;

            const quickCheckValueStr = inputElement.value;

            // 如果输入框为空，则不执行任何操作并直接返回
            if (quickCheckValueStr === '') {
                return;
            }

            if (isNaN(quickCheckValueStr)) {
                alert('请输入有效的盘点总数！');
                inputElement.focus();
                return;
            }
            
            const quickCheckValue = parseInt(quickCheckValueStr);
            const item = processedData[articleCode];

            if (quickCheckValue === item.totalInventoryNum) {
                // 数量相符，自动确认
                const newStatus = {
                    state: 'ok',
                    text: '已盘点',
                    totalCheckedNum: quickCheckValue,
                    batches: {}
                };

                item.batches.forEach(batch => {
                    newStatus.batches[batch.batchCode] = {
                        checkedNum: batch.num, // 假设批次数量正确
                        remark: '快速确认'
                    };
                });

                checkStatus[articleCode] = newStatus;
                renderList(); // 重新渲染列表以更新UI
            } else {
                // 数量不符，打开模态框并预填充总数
                openModal(articleCode, quickCheckValue);
            }
        }


        function openModal(articleCode, prefilledTotal = null) {
            const item = processedData[articleCode];
            if (!item) return;

            // 填充模态框主信息
            document.getElementById('modal-img').src = item.imageFiles[0]?.url || '';
            document.getElementById('modal-articleName').textContent = item.articleName;
            document.getElementById('modal-articleCode').textContent = item.articleCode;
            document.getElementById('modal-numberOem').textContent = item.numberOem;
            document.getElementById('modal-location').textContent = item.location;
            
            const totalCheckNumInput = document.getElementById('modal-total-check-num');
            const currentStatus = checkStatus[articleCode];
            
            // 如果从快速确认通道来，则使用其值；否则使用已保存的值
            if (prefilledTotal !== null) {
                totalCheckNumInput.value = prefilledTotal;
            } else {
                totalCheckNumInput.value = currentStatus?.totalCheckedNum ?? item.totalInventoryNum;
            }

            // 清空反馈消息
            showFeedback('');

            // 填充批次信息
            const batchListElement = document.getElementById('modal-batch-list');
            batchListElement.innerHTML = '';

            item.batches.forEach(batch => {
                const checkedNum = currentStatus?.batches?.[batch.batchCode]?.checkedNum ?? batch.num;
                const remark = currentStatus?.batches?.[batch.batchCode]?.remark ?? '';
                
                const batchHtml = `
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                            <div>
                                <label class="text-xs text-gray-500">批次号</label>
                                <p class="font-semibold font-mono text-sm">${batch.batchCode}</p>
                            </div>
                            <div>
                                <label class="text-xs text-gray-500">库存数量</label>
                                <p class="font-semibold text-sm">${batch.num}</p>
                            </div>
                            <div class="md:col-span-2">
                                <label class="text-xs text-gray-500">盘点数量</label>
                                <input type="number" data-batch-code="${batch.batchCode}" data-stock-num="${batch.num}" value="${checkedNum}"
                                       class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition" 
                                       placeholder="输入实盘数" oninput="updateDifference(this)">
                            </div>
                        </div>
                        <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
                             <div>
                                <label class="text-xs text-gray-500">差异</label>
                                <p class="font-semibold text-sm" id="diff-${batch.batchCode}"></p>
                            </div>
                            <div>
                                <label class="text-xs text-gray-500">备注</label>
                                <input type="text" data-remark-batch-code="${batch.batchCode}" value="${remark}"
                                    class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition" 
                                    placeholder="选填">
                            </div>
                        </div>
                    </div>
                `;
                batchListElement.innerHTML += batchHtml;
            });
            
            batchListElement.querySelectorAll('input[type="number"]').forEach(updateDifference);

            const modal = document.getElementById('check-modal');
            modal.classList.remove('opacity-0', 'pointer-events-none');
            modal.querySelector('.modal-content').classList.remove('scale-95');
        }

        function closeModal() {
            const modal = document.getElementById('check-modal');
            modal.classList.add('opacity-0', 'pointer-events-none');
            modal.querySelector('.modal-content').classList.add('scale-95');
        }
        
        function updateDifference(inputElement) {
            const batchCode = inputElement.dataset.batchCode;
            const stockNum = parseInt(inputElement.dataset.stockNum);
            const checkedNum = inputElement.value;
            const diffElement = document.getElementById(`diff-${batchCode}`);

            if (checkedNum === '' || isNaN(checkedNum)) {
                diffElement.innerHTML = '';
                return;
            }

            const difference = parseInt(checkedNum) - stockNum;
            if (difference === 0) {
                diffElement.innerHTML = `<span class="text-green-600 font-semibold">无差异</span>`;
            } else if (difference > 0) {
                diffElement.innerHTML = `<span class="text-blue-600 font-semibold">盘盈 ${difference}</span>`;
            } else {
                diffElement.innerHTML = `<span class="text-red-600 font-semibold">盘亏 ${Math.abs(difference)}</span>`;
            }
        }

        function showFeedback(message) {
            const feedbackElement = document.getElementById('feedback-message');
            feedbackElement.textContent = message;
            feedbackElement.classList.remove('opacity-0');
            if(message){
                setTimeout(() => {
                    feedbackElement.classList.add('opacity-0');
                }, 3000);
            }
        }

        function confirmCheck() {
            const articleCode = document.getElementById('modal-articleCode').textContent;
            const item = processedData[articleCode];
            const batchInputs = document.querySelectorAll('#modal-batch-list input[type="number"]');
            const totalCheckNumInput = document.getElementById('modal-total-check-num');
            const totalCheckNumStr = totalCheckNumInput.value;

            if (totalCheckNumStr === '' || isNaN(totalCheckNumStr)) {
                showFeedback('请输入盘点总数！');
                return;
            }
            const totalCheckNum = parseInt(totalCheckNumStr);
            
            let batchTotal = 0;
            let totalDifference = 0;
            let allBatchInputsValid = true;
            
            const newStatus = {
                state: 'ok',
                text: '已盘点',
                totalCheckedNum: totalCheckNum,
                batches: {}
            };

            batchInputs.forEach(input => {
                const batchCode = input.dataset.batchCode;
                const stockNum = parseInt(input.dataset.stockNum);
                const checkedNumStr = input.value;
                const remarkInput = document.querySelector(`input[data-remark-batch-code="${batchCode}"]`);
                const remark = remarkInput ? remarkInput.value : '';

                if (checkedNumStr === '' || isNaN(checkedNumStr)) {
                    allBatchInputsValid = false;
                } else {
                    const checkedNum = parseInt(checkedNumStr);
                    batchTotal += checkedNum;
                    totalDifference += checkedNum - stockNum;
                    newStatus.batches[batchCode] = {
                        checkedNum: checkedNum,
                        remark: remark
                    };
                }
            });

            if (!allBatchInputsValid) {
                showFeedback('请填写所有批次的盘点数量！');
                return;
            }

            if (batchTotal !== totalCheckNum) {
                showFeedback(`批次数量总和(${batchTotal})与盘点总数(${totalCheckNum})不符！`);
                return;
            }
            
            if (totalDifference !== 0) {
                newStatus.state = 'error';
                newStatus.text = '有差异';
            }

            checkStatus[articleCode] = newStatus;
            
            renderList();

            closeModal();
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('check-modal');
            if (event.target == modal) {
                closeModal();
            }
        }
    </script>

</body>
</html>
