/*
 * @Description:
 * @Autor: shh
 * @Date: 2022-11-15 10:44:16
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-12-18 09:33:22
 */
/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ["admin", "editor", "sa"];
  return valid_map.indexOf(str.trim()) >= 0;
}

// 密码
export function validatePassword(rule, value, callback) {
  const regPwd =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>,.\/,\\])[A-Za-z\d~!@#$%^&*()_+`\-={}:";'<>,.\/,\\]{8,20}/;

  // 正则修改密码
  // const regPwd = /^.{6,}$/;

  if (!value) {
    callback(new Error("请输入密码"));
  }
  if (value?.length < 8) {
    callback(new Error("密码长度不能少于8位"));
  } else if (value?.length > 20) {
    callback(new Error("密码长度不能大于20位"));
  } else if (!regPwd.test(value)) {
    callback(new Error("必须包含大、小写字母、数字、特殊字符"));
  } else {
    callback();
  }
}
// 身份证号
export function checkIdCard(rule, value, callback) {
  const regIdCard =
    /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/;
  if (!value) {
    callback(new Error("请输入身份证号"));
  } else if (!regIdCard.test(value)) {
    callback(new Error("请输入正确的身份证号"));
  } else {
    callback();
  }
}
// 手机号
export function checkMobile(rule, value, callback) {
  const regMobile =
    /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[0-9])\d{8}$/;

  if (!value) {
    callback(new Error("请输入手机号"));
  } else if (!regMobile.test(value)) {
    callback(new Error("请输入正确的手机号"));
  } else {
    callback();
  }
}
export function validatePositiveNumber(rule, value, callback) {
  if (value < 0) {
    callback(new Error(rule.message));
  } else {
    callback();
  }
}
