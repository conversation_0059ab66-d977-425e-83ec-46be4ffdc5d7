/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-09 18:23:39
 * @Description: axios请求封装
 */

import axios from "axios";
import store from "@/store/index.js";
import router from "@/router";
import { getToken, removeToken } from "@/utils/auth";
import { Message } from "element-ui";
import qs from "qs";
import setting from "@/config/setting.js";
const { baseURL } = window.config.api;
const {
  contentType,
  otherContentType,
  invalidCode,
  noPermissionCode,
  successCode,
  tokenStorage,
  tokenName,
  requestTimeout,
} = setting;
let tokenLose = true;

/**
 * @description Creates a configured Axios instance
 * @param {Object} config
 * @returns {AxiosInstance}
 */
const createAxiosInstance = (config = {}) => {
  const instance = axios.create({
    baseURL,
    // timeout: requestTimeout,  // 请求超时时间
    headers: {
      "Content-Type": contentType,
    },
    ...config,
  });

  instance.interceptors.request.use(
    (config) => {
      const token = getToken();
      if (token) {
        config.headers[tokenName] = token;
      }
      if (config.data && config.headers["Content-Type"] === otherContentType) {
        config.data = qs.stringify(config.data);
      }
      return config;
    },
    (error) => Promise.reject(error)
  );
  if (config.responseType === "blob") {
    instance.interceptors.response.use(
      (response) => {
        if (response.status === 200) {
          return response.data;
        } else {
          Message.error(`后端接口异常，状态码: ${response.status}`);
          return Promise.reject(
            new Error(`后端接口异常，状态码: ${response.status}`)
          );
        }
      },
      (error) => {
        if (error.response) {
          // 服务器返回了错误响应
          const { status, data } = error.response;
          switch (status) {
            case 400:
              Message.error("请求无效");
              break;
            case 401:
              Message.error("未授权，请登录");
              break;
            case 403:
              Message.error("禁止访问");
              break;
            case 404:
              Message.error("资源未找到");
              break;
            case 500:
              Message.error("服务器内部错误");
              break;
            default:
              Message.error(`后端接口异常，状态码: ${status}`);
          }
          return Promise.reject(new Error(`后端接口异常，状态码: ${status}`));
        } else if (error.request) {
          // 请求已发出，但没有收到响应
          Message.error("请求超时或网络错误");
          return Promise.reject(new Error("请求超时或网络错误"));
        } else {
          // 发送请求时发生了一些问题
          Message.error("请求发送失败");
          return Promise.reject(new Error("请求发送失败"));
        }
      }
    );
    return instance;
  }
  // if (config.responseType !== "blob") {
  instance.interceptors.response.use(
    (response) => {
      const res = response.data;
      const { code, message } = res;
      // 操作成功
      if (successCode.indexOf(code) !== -1) {
        return res;
      } else {
        // debugger;
        handleCode(code, message);
        return Promise.reject({ code, message });
      }
    },
    (error) => {
      const { response, message } = error;
      if (error.response && error.response.data) {
        const { status, data } = response;
        handleCode(status, data.msg || message);
        return Promise.reject(error);
      } else {
        let { message } = error;
        if (message === "Network Error") {
          message = "后端接口连接异常";
        }
        if (message.includes("timeout")) {
          message = "后端接口请求超时";
        }
        if (message.includes("Request failed with status code")) {
          const code = message.substr(message.length - 3);
          message = "后端接口" + code + "异常";
        }
        Message.error(message || `后端接口未知异常`);
        return Promise.reject(error);
      }
    }
  );
  // }
  return instance;
};
/**
 *
 * @description 处理code异常
 * @param {*} code
 * @param {*} msg
 */
const handleCode = (code, msg) => {
  switch (code) {
    case invalidCode:
      tokenLose = false;
      removeToken();
      localStorage.clear();
      Message.error(msg || "请重新登录");
      router.replace({ path: `/login` });
      break;
    case noPermissionCode:
      router.push({ path: "/401" }).catch(() => {});
      break;
    default:
      Message.error(msg || `后端接口${code}异常`);
      break;
  }
};
// Main Axios instance for regular requests
const instance = createAxiosInstance();

// Instance for downloading files
const instanceBlob = createAxiosInstance({ responseType: "blob" });

// API methods
export const get = (url, params) => instance({ url, method: "GET", params });
export const put = (url, data) => instance({ url, method: "PUT", data });
export const post = (url, data) => instance({ url, method: "POST", data });
export const del = (url, data) => instance({ url, method: "DELETE", data });
export const down = (url, data) => instanceBlob({ url, method: "GET", data });
export const postDown = (url, data) =>
  instanceBlob({ url, method: "POST", data });
