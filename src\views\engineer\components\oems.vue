<!--
 * @Description: 零件列表 
 * @Autor: shh
 * @Date: 2022-11-16 16:42:14
 * @LastEditors: shanhaihong <EMAIL>
 * @LastEditTime: 2023-12-06 20:19:56
-->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      show-selection
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="450"
      :query-param="queryParam"
      :show-setting="false"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.productIdName"
          filterable
          clearable
          :options="options"
          collapse-tags
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>

      <template #brand="slotProps">
        <el-popover
          placement="bottom"
          title="当前设备关联产品树"
          width="400"
          trigger="click"
        >
          <div style="margin: 20px">
            <el-tree
              :data="deviceProductTree"
              :props="{
                children: 'children',
                label: 'name',
              }"
              default-expand-all
            >
            </el-tree>
          </div>

          <el-button slot="reference" @click="getBrand(slotProps.row.id)"
            >查看品牌</el-button
          >
        </el-popover>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            选择
          </el-button>
        </span>
      </template>
    </ProTable>
  </div>
</template>
<script>
import { partListApi, productAllApi, partProductTreeApi } from "@/api/dispose";

import { isEmpty, cloneDeep } from "lodash";

export default {
  name: "Oems",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      // 列表
      deviceProductTree: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        lastIds: [],
      },
      columns: [
        // {
        //   dataIndex: 'brand',
        //   title: '产品树',
        //   isTable: true,
        //   tableSlot: 'brand',
        // },

        {
          dataIndex: "oemNumber",
          title: "原厂零件编号（OEM）",
          isTable: true,
          isSearch: true,
          clearable: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "ch",
          title: "零件中/英文名称",
          isSearch: true,
          clearable: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "ch",
          title: "零件中文名称",
          isTable: true,
        },
        {
          dataIndex: "en",
          title: "零件英文名称",
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单元",
          isTable: true,
        },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
          formatter: (row) => row.type.label,
        },
      ],
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    productAllApi().then((res) => {
      this.options = res.data.rows;
      this.$refs.ProTable.refresh();
    });
  },
  methods: {
    getBrand(id) {
      partProductTreeApi(id).then((res) => {
        console.log(res);
        this.deviceProductTree = res.data;
      });
    },
    handleSelect(item) {
      this.queryParam.lastIds = [];
      item.map((el) => {
        this.queryParam.lastIds.push(el[el.length - 1]);
      });
    },
    handleChange(item) {
      this.$set(this.form, "productIds", []);
      // console.log(this.$refs.ProductIds.getCheckedNodes(true))
      item.map((el) => {
        this.form.productIds.push(el[el.length - 1]);
      });
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      partListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },

    handleSelectionChange(row) {
      console.log(row);
      this.$emit("chooseOem", row);
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
