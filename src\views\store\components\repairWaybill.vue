<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-08 13:55:00
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-08 16:10:56
 * @Description: 翻新组件维修入库
 -->
<template>
  <ProDrawer
    :value="drawerVisible"
    :no-footer="true"
    :title="drawerTitle"
    size="70%"
    @cancel="drawerCancel"
  >
    <ProForm
      ref="ProForm"
      :form-param="formParam"
      :form-list="formColumns"
      :open-type="editType"
      :layout="{ formWidth: '100%', labelWidth: '100px' }"
    >
      <template #picUrls>
        <div v-if="formParam.picUrls && formParam.picUrls?.length > 0">
          <el-image
            v-for="item in formParam.picUrls"
            :key="item.url"
            style="width: 100px; height: 100px"
            :src="item.url"
            :preview-src-list="[item.url]"
          >
          </el-image>
        </div>
      </template>
      <template #componentRepairReplaces>
        <div class="title-box">更换耗材零件清单</div>
        <ProTable
          :show-loading="false"
          :show-search="false"
          :show-pagination="false"
          :show-setting="false"
          :columns="replaceColumns"
          :data="formParam.componentRepairReplaces"
        >
          <template #picUrl="slotProps">
            <el-image
              :preview-src-list="[slotProps.row?.skuInfo?.picUrl[0]?.url]"
              style="width: 100px; height: 100px"
              :src="slotProps.row?.skuInfo?.picUrl[0]?.url"
            ></el-image>
          </template>

          <template #saleAttrVals="slotProps">
            <div
              v-for="attr in slotProps.row?.skuInfo?.saleAttrVals"
              :key="attr.val"
            >
              {{ attr.name }}:{{ attr.val }}
            </div>
          </template>
        </ProTable>
      </template>
    </ProForm>
  </ProDrawer>
</template>

<script>
import { componentsRepairDetailByCodeApi } from "@/api/repair";

export default {
  name: "RepairWaybill",
  data() {
    return {
      drawerVisible: false,
      drawerTitle: "",
      formParam: {},
      formColumns: [
        {
          dataIndex: "code",
          title: "维修单号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        // {
        //   dataIndex: "productName",
        //   title: "机器型号",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 6,
        // },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "isStore",
          title: "是否入库",
          isForm: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请选择是否入库",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "amount",
          title: "价格",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请输入价格",
              trigger: "change",
            },
            {
              validator(rule, value, callback) {
                if (value < 0) {
                  callback("价格不能小于0");
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "description",
          title: "问题描述",
          isForm: true,
          valueType: "text",
          isWrap: true,
          formSpan: 24,
        },
        {
          dataIndex: "picUrls",
          title: "维修图片",
          isForm: true,
          formSlot: "picUrls",
          formSpan: 24,
        },
        {
          dataIndex: "componentRepairReplaces",
          title: "更换零件清单",
          isForm: true,
          formOtherSlot: "componentRepairReplaces",
          formSpan: 24,
        },
      ],
      replaceColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          minWidth: 140,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
          minWidth: 100,
        },

        {
          dataIndex: "picUrl",
          title: "物品图片",
          isTable: true,
          tableSlot: "picUrl",
          minWidth: 200,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "单价",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "costPrice",
          title: "成本价",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "num",
          title: "更换数量",
          minWidth: 80,
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "location",
          title: "更换位置",
          minWidth: 140,
          isTable: true,
          formatter: (row) =>
            Array.isArray(row.location) ? row.location.join("、") : "",
        },
      ],
      editType: "info",
    };
  },
  mounted() {},
  methods: {
    show(shopWaybill) {
      this.drawerVisible = true;
      this.drawerTitle = `${shopWaybill} - 维修报告`;
      componentsRepairDetailByCodeApi(shopWaybill).then((res) => {
        this.formParam = res.data || {};
      });
    },
    drawerCancel() {
      this.drawerVisible = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
