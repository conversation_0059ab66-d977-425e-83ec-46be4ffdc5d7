<template>
  <div>
    <div v-if="ifshow" id="mapdiv"></div>
  </div>
</template>

<script>
import Map from "ol/Map.js";
import View from "ol/View.js";
import XYZ from "ol/source/XYZ.js";
import { Tile as TileLayer, Vector as VectorLayer } from "ol/layer.js";
import Feature from "ol/Feature.js";
import Point from "ol/geom/Point.js";
import { Fill, Stroke, Style, Icon, Circle, Text } from "ol/style.js";
import VectorSource from "ol/source/Vector.js";

let templayer, floorlayer, map;
export default {
  props: {
    floorPois: {
      type: Object,
      default: () => { }
    }
  },
  // name: "map",
  data() {
    return {
      floorsData: {},
      ifshow: true
    };
  },
  watch: {
    floorPois: {
      handler(newV) {
        this.floorsData = newV;
        // this.dataAttrs = JSON.parse(JSON.stringify(newV)); //将监听到修改的值赋予到dataAttrs中
      },
      deep: true,
      immediate: true
    },
    ifshow: {
      handler(newV) {
        // map.setTarget("mapdiv");
        // this.floorsData = newV;
        // this.dataAttrs = JSON.parse(JSON.stringify(newV)); //将监听到修改的值赋予到dataAttrs中
      }
    }
  },
  mounted() {
    this.initMap();
  },
  destroyed() {
    this.clearAll();
  },
  methods: {
    initMap() {
      const _this = this;
      const floor = this.floorsData.floor;
      let imgurl = "./tile/ninef/{z}/{x}/{y}.png";
      if (floor == 10) {
        imgurl = "./tile/tenf/{z}/{x}/{y}.png";
      } else if (floor == 4) {
        imgurl = "./tile/fourf/{z}/{x}/{y}.png";
      }
      map = new Map({
        target: "mapdiv",
        layers: [
          new TileLayer({
            source: new XYZ({
              url: imgurl
            })
          })
        ],
        view: new View({
          projection: "EPSG:3857",

          center: [-21300, 12000],
          zoom: 14,
          minZoom: 8,
          maxZoom: 18,
          extent: [
            -37050.560900943907, 8380.14977258378, -6046.131155774747, 16375.26662540045
          ]
        })
      });
      console.log(map);

      templayer = this.addVectorLayer("temp");
      floorlayer = this.addVectorLayer("floor");

      this.addFloorPois();

      map.on("click", (e) => {
        console.log("点击", e);
        const coods = e.coordinate;
        // let xx = 102.732564 - 102.72468865160418;
        // let yy = 25.04306 - 25.039714885797355;
        // // 102.732564,25.043060
        // // 102.72468865160418, 25.039714885797355
        // coods[0] += xx;
        // coods[1] += yy;
        console.log(coods);
        this.$emit("pickedCoods", coods);
        // _this.addPoint(coods);
        _this.addTempPoi(coods);
        // _this.addFloorPoi(coods);
      });
    },
    addVectorLayer(name) {
      const layer = new VectorLayer({
        source: new VectorSource()
      });
      layer.name = name;
      map.addLayer(layer);
      return layer;
    },
    addTempPoi(coods) {
      this.clearTemp();
      const feature = this.creatIconFeature(coods, 1);
      templayer.getSource().addFeature(feature);
    },
    addFloorPois() {
      console.log(this.floorsData);
      // this.floorsData = JSON.stringify(coods);
      this.clearFloorPois();
      console.log(this.floorsData.list);
      this.floorsData.list.forEach((element) => {
        let feature;
        if (element.geometry) {
          if (element.id && this.floorsData.id !== element.id) {
            feature = this.creatIconFeature([
              Number(element.geometry[0]),
              Number(element.geometry[1])
            ]);
            feature && floorlayer.getSource().addFeature(feature);
          } else {
            this.addTempPoi([
              Number(element.geometry[0]),
              Number(element.geometry[1])
            ]);
          }
        }
      });
    },
    addFloorPoi(coods) {
      this.clearTemp();
      const feature = this.creatIconFeature(coods);
      floorlayer.getSource().addFeature(feature);
    },
    creatIconFeature(coods, type) {
      const feature = new Feature({
        geometry: new Point(coods)
      });
      let iconStyle = {
        anchor: [0.5, 1],
        src: "./images/map/loc.png",
        scale: 0.4
      };
      if (type == "1") {
        iconStyle = {
          anchor: [0.5, 1],
          src: "./images/map/loc.png",
          scale: 1
        };
      }
      const style = new Style({
        image: new Icon(iconStyle)
      });
      feature.setStyle(style);
      return feature;
    },
    clearTemp() {
      templayer.getSource().clear();
    },
    clearFloorPois() {
      floorlayer.getSource().clear();
    },

    clearAll() {
      templayer = null;
      floorlayer = null;
      map = null;
    }
  }
};
</script>

<style>
@import "ol/ol.css";

#mapdiv {
  /* top: 300px; */
  /* position: absolute; */
  width: 870px;
  height: 600px;
  border: 1px black solid;
  /* background-color: red; */
  z-index: 2;
}
</style>
