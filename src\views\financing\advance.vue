<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-21 09:30:05
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="耗材预付款" name="first" lazy>
        <PrepayConsumables />
      </el-tab-pane>
      <el-tab-pane label="机器预付款" name="second" lazy>
        <PrepayEquipment />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import PrepayConsumables from "@/views/financing/components/statistics/PrepayConsumables.vue";
import PrepayEquipment from "@/views/financing/components/statistics/PrepayEquipment.vue";

export default {
  name: "Advance",
  components: { PrepayConsumables, PrepayEquipment },
  data() {
    return {
      activeName: "first",
    };
  },
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss"></style>
