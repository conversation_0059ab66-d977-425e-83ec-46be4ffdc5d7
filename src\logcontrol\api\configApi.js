/**
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 配置管理API接口 - 基于后端已实现接口
 */

import { get, post, put, del } from '@/utils/request'

// 分发状态枚举
export const DISTRIBUTION_STATUS = {
  PENDING: 'PENDING',     // 待应用
  ASSIGNED: 'ASSIGNED',   // 已分配
  APPLIED: 'APPLIED'      // 已应用
}

// 分发状态文本映射
export const DISTRIBUTION_STATUS_TEXT = {
  [DISTRIBUTION_STATUS.PENDING]: '待应用',
  [DISTRIBUTION_STATUS.ASSIGNED]: '已分配',
  [DISTRIBUTION_STATUS.APPLIED]: '已应用'
}

// 分发状态标签类型映射
export const DISTRIBUTION_STATUS_TYPE = {
  [DISTRIBUTION_STATUS.PENDING]: 'info',
  [DISTRIBUTION_STATUS.ASSIGNED]: 'warning',
  [DISTRIBUTION_STATUS.APPLIED]: 'success'
}

export const configApi = {
  // 获取配置模板列表
  async getTemplates() {
    return await get('/logcontrol/config/templates')
  },

  // 获取配置列表
  async getConfigList() {
    return await get('/logcontrol/config/list')
  },

  // 更新配置接口 (推荐) - 用于创建和更新配置
  async updateConfig(configData) {
    return await post('/logcontrol/config/update', configData)
  },

  // 从模板创建配置
  async createFromTemplate(templateName, configName, customizations = {}) {
    return await post('/logcontrol/config/create-from-template', {
      templateName,
      configName,
      customizations
    })
  },

  // 激活配置
  async activateConfig(configId) {
    return await post(`/logcontrol/config/activate/${configId}`)
  },

  // 删除配置
  async deleteConfig(configId) {
    return await del(`/logcontrol/config/${configId}`)
  },

  // 获取单个配置详情
  async getConfigDetail(configId) {
    return await get(`/logcontrol/config/${configId}`)
  },

  // 获取单个配置模板详情
  async getTemplateDetail(templateName) {
    return await get(`/logcontrol/config/template/${templateName}`)
  },

  // 更新配置模板
  async updateTemplate(templateName, templateData) {
    return await put(`/logcontrol/config/template/${templateName}`, templateData)
  },

  // 获取配置分配情况
  async getAssignments(params) {
    const response = await get('/logcontrol/config/assignments', params)
    // 处理新的数据结构，包含 distributionId 等字段
    return this.transformAssignmentData(response)
  },

  // 转换分配数据结构
  transformAssignmentData(response) {
    if (response && response.data) {
      const assignments = Array.isArray(response.data) ? response.data :
                         (response.data.records || response.data.list || [])

      // 确保每个分配记录都有必要的字段
      const transformedAssignments = assignments.map(assignment => ({
        ...assignment,
        distributionId: assignment.distributionId || assignment.id,
        distributionStatus: assignment.distributionStatus || 'PENDING',
        assignTime: assignment.assignTime || assignment.createTime,
        targetType: assignment.targetType || 'DEVICE',
        targetId: assignment.targetId || '',
        targetName: assignment.targetName || '',
        configName: assignment.configName || '',
        logLevel: assignment.logLevel || 'INFO',
        isActive: assignment.isActive !== false // 默认为true，除非明确设置为false
      }))

      return {
        ...response,
        data: Array.isArray(response.data) ? transformedAssignments : {
          ...response.data,
          records: transformedAssignments,
          list: transformedAssignments
        }
      }
    }
    return response
  },

  // 获取激活的日志配置（客户端使用）
  async getActiveConfig(headers = {}) {
    return await get('/logcontrol/config/get', {}, { headers })
  },

  // 获取用户当前有效配置（用于验证配置优先级）
  async getUserEffectiveConfig(userId, deviceId = null) {
    const headers = {
      'X-User-Id': userId
    }
    if (deviceId) {
      headers['X-Device-Id'] = deviceId
    }
    return await get('/logcontrol/config/get', {}, { headers })
  },

  // 根据配置名称获取配置
  async getConfigByName(configName) {
    return await get('/logcontrol/config/get-by-name', { configName })
  },

  // 根据配置版本获取配置
  async getConfigByVersion(configVersion) {
    return await get('/logcontrol/config/get-by-version', { configVersion })
  },

  // 为用户分配配置
  async assignToUser(userId, configId) {
    return await post(`/logcontrol/config/assign-to-user?userId=${userId}&configId=${configId}`)
  },

  // 为设备分配配置
  async assignToDevice(deviceId, configId) {
    return await post(`/logcontrol/config/assign-to-device?deviceId=${deviceId}&configId=${configId}`)
  },

  // 批量分配配置
  async batchAssign(assignData) {
    return await post('/logcontrol/config/assign-batch', assignData)
  },

  // 移除配置分配
  async removeAssignment(targetType, targetId) {
    return await del(`/logcontrol/config/assignment/${targetType}/${targetId}`)
  },

  // 重新分发配置
  async retryDistribution(distributionId) {
    return await post(`/logcontrol/config/retry-distribution/${distributionId}`)
  },

  // 移除分发关系（通过分发ID）
  async removeDistribution(distributionId) {
    return await del(`/logcontrol/config/remove-distribution/${distributionId}`)
  },

  // 批量操作分发关系
  async batchOperateDistributions(distributionIds, operation) {
    return await post('/logcontrol/config/batch-operate-distributions', {
      distributionIds,
      operation
    })
  },

  // 预览配置（增强版）
  async previewConfig(params) {
    return await get('/logcontrol/config/preview', params)
  },

  // 更新分发关系激活状态
  async updateDistributionStatus(distributionId, isActive) {
    return await put(`/logcontrol/config/distribution/${distributionId}/status`, {
      isActive
    })
  },

  // 更新分发关系的配置
  async updateDistributionConfig(distributionId, configId) {
    return await put(`/logcontrol/config/distribution/${distributionId}/config`, {
      configId
    })
  },

  // 根据分发关系ID删除
  async deleteDistributionById(distributionId) {
    return await del(`/logcontrol/config/distribution/${distributionId}`)
  },

  // 测试接口
  async test() {
    return await get('/logcontrol/config/test')
  },


}

export default configApi
