<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 14:59:27
 * @Description: 供应商管理
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'twoLevelList', hasChildren: 'hasChildren' }"
      :columns="columns"
      row-key="id"
      show-pagination
      :data="tableData"
      sticky
      default-expand-all
      :local-pagination="localPagination"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增供应商
        </el-button>
      </template>

      <template #keyword1>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-circle-check"
            @click="handleInfo(slotProps.row)"
          >
            详情
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>

          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>

    <!-- 新增、编辑、详情框  -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="70%"
      :confirm-loading="confirmLoading"
      :top="'2%'"
      :no-footer="methodType === 'info'"
      :confirm-text="methodType === 'add' ? '确认新增' : '保存'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
        <template #city>
          <el-cascader
            v-model="addressCode"
            :disabled="methodType === 'info'"
            style="width: 100%"
            filterable
            :options="options1"
            :props="{
              label: 'name',
              value: 'code',
              children: 'children',
              expandTrigger: 'click',
            }"
            @change="handleReginSelect"
          ></el-cascader>
        </template>
        <template #licenseImg>
          <ProUpload
            :file-list="form.licenseImg"
            :type="methodType"
            :limit="3"
            @uploadSuccess="handleLicenseImgUploadSuccess"
            @uploadRemove="handleLicenseImgUploadRemove"
          />
          <span v-if="methodType !== 'info'">
            仅支持上传png、jpg格式且10M大小内的图片。
          </span>
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>
<script>
import {
  manufacturerListApi,
  manufacturerAddApi,
  manufacturerEditApi,
  manufacturerDelApi,
} from "@/api/manufacturer";

import { cloneDeep } from "lodash";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";
import { dictTreeByCodeApi } from "@/api/user";
import { regionTreeApi } from "@/api/store";
import ProUpload from "@/components/ProUpload/index.vue";

export default {
  name: "Management",
  components: { ProUpload },
  mixins: [],
  props: {},
  data() {
    return {
      addressCode: [],
      options1: [],
      // 列表
      tableData: [],
      queryParam: {
        aduitState: null,
        name: null,
      },
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      columns: [
        {
          dataIndex: "code",
          title: "制造商编号",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "name",
          title: "制造商简称",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "groupName",
          title: "所属集团",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "keyword1",
          title: "所属省市区",
          isSearch: true,
          searchSlot: "keyword1",
        },
        {
          dataIndex: "province",
          title: "省份",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "city",
          title: "市区",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "area",
          title: "区县",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "address",
          title: "发货地址",
          isTable: true,
          minWidth: 280,
        },
        {
          dataIndex: "legalPerson",
          title: "联系人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "legalPersonTel",
          title: "电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "license",
          title: "营业执照名称",
          isTable: true,
          minWidth: 220,
        },
        {
          dataIndex: "creditCode",
          title: "统一信用代码",
          isTable: true,
          minWidth: 220,
        },
        {
          dataIndex: "bank",
          title: "开户行",
          isTable: true,
          minWidth: 150,
        },
        // {
        //   dataIndex: "bankClient",
        //   title: "开户网点",
        //   isTable: true,
        //   width: 150,
        // },
        {
          dataIndex: "bankAccount",
          title: "账号",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "ticketType",
          title: "开票类型",
          isTable: true,
          formatter: (row) => row.ticketType?.label,
          minWidth: 120,
        },
        {
          dataIndex: "tax",
          title: "税率(%)",
          isTable: true,
          minWidth: 100,
          formatter: (row) => (row.tax ? row.tax + "%" : ""),
        },
        {
          dataIndex: "settleMethod",
          title: "结算方式",
          isTable: true,
          formatter: (row) => row.settleMethod?.label,
          minWidth: 100,
        },
        {
          dataIndex: "updatedName",
          title: "编辑人",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "updatedAt",
          title: "编辑时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "Actions",
          width: 220,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tableSlot: "actions",
        },
      ],

      //新增
      methodType: "add",
      confirmLoading: false,

      form: { parentId: "" },
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      formColumns: [
        {
          dataIndex: "name",
          isForm: true,
          title: "制造商名称",
          valueType: "input",
          clearable: true,
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请输入造商名称",
              trigger: "change",
            },
          ],
        },
        // {
        //   dataIndex: "code",
        //   isForm: true,
        //   title: "制造商编号",
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 8,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请输入造商编号",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "groupName",
          title: "所属集团",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "city",
          title: "所属省市区",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          formSlot: "city",
        },
        {
          dataIndex: "license",
          title: "营业执照名称",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        // {
        //   dataIndex: "paymentMethod",
        //   title: "付款机制",
        //   isForm: true,
        //   valueType: "select",
        //   clearable: true,
        //   formSpan: 8,
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(4000),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        // {
        //   dataIndex: "invoiceMethod",
        //   title: "开票机制",
        //   isForm: true,
        //   valueType: "select",
        //   clearable: true,
        //   formSpan: 8,
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(4500),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        {
          dataIndex: "accountType",
          title: "账户类型",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "bank",
          title: "开户银行",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "bankClient",
          title: "开户网点",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "bankAccount",
          title: "银行账号",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "tax",
          title: "税率(%)",
          isForm: true,
          valueType: "input-number",
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请输入税率",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "creditCode",
          title: "统一信用代码",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "ticketType",
          title: "开票类型",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(4300),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "settleMethod",
          title: "结算方式",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(4200),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择结算方式",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "legalPerson",
          title: "联系人",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "legalPersonTel",
          title: "电话",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "financeName",
          title: "财务姓名",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "financePhone",
          title: "财务电话",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "financeEmail",
          title: "财务邮箱",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "salesName",
          title: "销售经理姓名",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "salesPhone",
          title: "销售经理电话",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "afterSaleName",
          title: "售后姓名",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "afterSalePhone",
          title: "售后电话",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "address",
          title: "发货地址",
          isForm: true,
          valueType: "input",
          formSpan: 24,
        },
        {
          dataIndex: "licenseImg",
          title: "营业执照",
          isForm: true,
          formSpan: 24,
          formSlot: "licenseImg",
        },
      ],
    };
  },

  computed: {},

  watch: {},
  created() {},
  async mounted() {
    await this.getRegionDta();
    this.$refs.ProTable.refresh();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      manufacturerListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    async getRegionDta(node, resolve) {
      const result = await regionTreeApi();
      this.options1 = result.data;
    },
    /**
     * @description 获取省市区区域数据
     * @param node
     * @param {Function} resolve
     * @returns {Promise<void>}
     */
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 处理省市区数据
     * @param list
     * @returns {*}
     */
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    handleReginSelect(val) {
      this.form["regionCode"] = val[val.length - 1];
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      // 清除列表数据
      this.tagList = [];
      this.methodType = "add";
      this.addressCode = [];
      this.resetFrom();
      this.dialogVisible = true;
      this.form = {
        licenseImg: [],
      };
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
        data?.id ? (this.form.parentId = data.id) : (this.form.parentId = 0);
        this.dialogTitle = "新增供应/制造商";
      });
    },
    //响应新增
    create() {
      console.log(this.tagList);
      console.log(this.form);
      const params = { ...this.form };
      manufacturerAddApi(params)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑 - " + row.name;
      this.resetFrom();
      const clonedForm = cloneDeep(row);
      this.addressCode = Number(this.form.regionCode);
      this.form = {
        ...clonedForm,
        invoiceMethod: clonedForm.invoiceMethod?.value || null,
        paymentMethod: clonedForm.paymentMethod?.value || null,
        settleMethod: clonedForm.settleMethod?.value || null,
        ticketType: clonedForm.ticketType?.value || null,
      };
      if (!this.form.licenseImg) {
        this.$set(this.form, "licenseImg", []);
      }
      this.methodType = "edit";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应编辑
    update() {
      const params = { ...this.form };
      manufacturerEditApi(params)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    async handleInfo(row) {
      this.dialogTitle = "查看 - " + row.name;
      this.resetFrom();
      const clonedForm = cloneDeep(row);
      this.addressCode = Number(this.form.regionCode);
      this.form = {
        ...clonedForm,
        invoiceMethod: clonedForm.invoiceMethod?.value || null,
        paymentMethod: clonedForm.paymentMethod?.value || null,
        settleMethod: clonedForm.settleMethod?.value || null,
        ticketType: clonedForm.ticketType?.value || null,
      };
      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        manufacturerDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
    handleLicenseImgUploadSuccess(result) {
      this.form.licenseImg.push(result);
    },
    handleLicenseImgUploadRemove(file) {
      const index = this.form.licenseImg.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.form.licenseImg.splice(index, 1);
    },
  },
};
</script>
<style>
.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
