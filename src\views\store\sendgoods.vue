<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-24 18:25:50
 * @Description: 耗材发货单
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      :local-pagination="localPagination"
      :data="tableData"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增发货单
        </el-button>
      </template>

      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-circle-check"
            @click="handleInfo(slotProps.row)"
          >
            详情
          </el-button>

          <el-button
            v-if="slotProps.row.status?.value === 'pending'"
            size="mini"
            type="danger"
            icon="el-icon-circle-close"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            关闭发货单
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :confirm-text="methodType === 'add' ? '确认新增' : '保存'"
      :top="'10%'"
      :no-footer="methodType === 'info'"
      :confirm-button-disabled="isButtonDisabled"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <div style="margin-bottom: 20px">
        <label class="el-form-item__label" style="width: 110px">
          选择仓库 :
        </label>
        <el-select
          v-model="form.warehouseId"
          :disabled="methodType === 'info'"
          placeholder="请选择仓库"
        >
          <el-option
            v-for="item in warehouseList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <el-button
          v-if="methodType === 'add' && form.warehouseId"
          type="primary"
          plain
          style="width: 100px; padding: 12px 0; margin-left: 20px"
          @click="showModDialog"
        >
          选择出库单
        </el-button>
      </div>
      <div class="title-box">发货物品详情</div>
      <DataTable
        ref="ProTable1"
        :columns="columns1"
        :show-setting="false"
        :show-pagination="false"
        :show-search="false"
        :data="tableData1"
        sticky
        default-expand-all
        :height="550"
        row-key="id"
        :tree-props="{
          children: 'children',
          hasChildren: 'hasChildren',
        }"
        style="width: 100%; margin-top: 20px"
        :show-table-operator="false"
      >
        <template #actions="slotProps">
          <span
            v-if="methodType == 'add' && slotProps.row.children"
            class="fixed-width"
          >
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDeleteGoods(slotProps.row)"
            >
              删除
            </el-button>
          </span>
        </template>
      </DataTable>
      <div v-if="form.traces?.length > 0" style="overflow: hidden">
        <label class="el-form-item__label">发货物流信息 : </label>
      </div>
      <div>
        <el-timeline>
          <el-timeline-item
            v-for="item in form.traces"
            :key="item.id"
            :timestamp="item.providerStatus + '    ' + item.operatedAt"
          >
            {{ item.operationRemark }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </ProDrawer>

    <!-- 选择出库单 -->
    <ProDialog
      :value="showModelDialog"
      title="选择出库单"
      width="1200px"
      :confirm-loading="false"
      confirm-text="确认选择"
      top="20px"
      @ok="handleChooseDialogConfirm"
      @cancel="showModelDialog = false"
    >
      <OutWarehouse
        v-if="showModelDialog"
        ref="OutWarehouse"
        :warehouse-id="form.warehouseId"
        :choose-model-selection="chooseModelSelection"
        @choose="handleSelectionChange"
      ></OutWarehouse>
    </ProDialog>
  </div>
</template>
<script>
import {
  invoicePageApi,
  invoiceAddApi,
  invoiceDelApi,
  invoiceEditApi,
  warehouseListApi,
  verifyOutWarehouse,
  invoiceArticleList,
  invoiceInfoApi,
  invoiceEditAddApi,
} from "@/api/store";

import OutWarehouse from "@/views/store/outwarehouse.vue";

import { isEmpty, cloneDeep } from "lodash";

export default {
  name: "SendGoods",
  components: { OutWarehouse },
  mixins: [],
  props: {},
  data() {
    return {
      warehouseList: [],
      showModelDialog: false,
      chooseModelSelection: [],
      // 列表
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {},
      columns: [
        {
          dataIndex: "invoiceCode",
          isTable: true,
          isSearch: true,
          clearable: true,
          title: "发货单编号",
          valueType: "input",
        },
        {
          dataIndex: "outboundOrderNumber",
          title: "出库单号",
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "logisticsWaybillNumber",
          title: "关联物流单号",
          isSearch: true,
          clearable: true,
          isTable: true,
          valueType: "input",
        },
        {
          dataIndex: "logisticsType",
          title: "配送方式",
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [
            { value: "self", label: "自提" },
            { value: "iss", label: "闪送" },
            { value: "jdl", label: "京东物流" },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "logisticsProvider",
          title: "配送方式",
          isTable: true,

          formatter: (row) => row.logisticsProvider?.label,
        },
        {
          dataIndex: "status",
          title: "发货单状态",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [
            { value: "pending", label: "待接单" },
            { value: "shipped", label: "已发货" },
            { value: "canceled", label: "已取消" },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
          formatter: (row) => row.status?.label,
        },
        {
          dataIndex: "receiverCompany",
          title: "店铺名称",
          isTable: true,
          formatter: (row) => row.receiver?.company,
          minWidth: 120,
        },
        {
          dataIndex: "consigneeName",
          title: "收货人名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          clearable: true,
          formatter: (row) => row.receiver?.name,
        },
        {
          dataIndex: "consigneePhone",
          title: "收货人电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
          clearable: true,
          formatter: (row) => row.receiver?.mobile,
        },
        {
          dataIndex: "consigneeCompany",
          title: "店铺名称",
          isSearch: true,
          valueType: "input",
          clearable: true,
        },

        {
          dataIndex: "createdBy",
          title: "创建人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 180,
        },
      ],

      //新增
      methodType: "add",
      confirmLoading: false,
      form: {},
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},

      columns1: [
        {
          dataIndex: "outboundOrderNumber",
          title: "出库单号",
          width: 200,
          isTable: true,
        },
        {
          dataIndex: "associatedOrderNumber",
          title: "关联单号",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "consigneePhone",
          title: "收货人联系方式",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "expectedNumber",
          title: "应发货量",
          isTable: true,
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          formatter: (row) => row.storageArticle?.name,
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          formatter: (row) => row.storageArticle?.code,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          formatter: (row) => row.storageArticle?.numberOem,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          formatter: (row) => row.storageArticle?.manufacturerChannel.label,
        },

        {
          dataIndex: "Actions",
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      tableData1: [],
      isButtonDisabled: false,
      isChange: 0,
      // 配送方式
      logisticsProvider: "",
      availableLogisticsProviders: [
        { label: "京东快递", value: "jdl" },
        { label: "闪送", value: "iss" },
        { label: "自提", value: "self" },
        { label: "工程师带", value: "passing" },
      ],
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
    warehouseListApi({ status: 1401 }).then((res) => {
      this.warehouseList = res.data;
    });
  },
  methods: {
    //加载表格
    loadData(parameter) {
      const requestParameters = cloneDeep(
        Object.assign(this.queryParam, parameter)
      );
      delete requestParameters.productIdName;
      invoicePageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();

      // this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.tableData1 = [];
      this.chooseModelSelection = [];
      this.dialogTitle = "新增发货单";
      this.methodType = "add";
      this.isChange = 0;
      this.logisticsProvider = null;
      this.resetFrom();
      this.dialogVisible = true;

      // this.$nextTick((e) => {
      //   this.$refs["proform"].resetFormParam();
      // });
    },
    //响应新增
    create() {
      if (this.tableData1.length === 0) {
        this.$message.warning("请选择出库单！");
        return;
      }
      this.$confirm(" 是否确认新增出库单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.tableData1?.map((ele) => {
          delete ele.children;
          delete ele.id;
        });
        this.isButtonDisabled = true;
        // const args = {
        //   logisticsProvider: this.logisticsProvider,
        //   outWarehouseList: this.tableData1,
        // };
        // !this.isChange && delete args.logisticsProvider;
        // invoiceEditAddApi(args)
        //   .then(() => {
        //     this.$message.success("新增成功");
        //     this.dialogVisible = false;
        //     this.isButtonDisabled = false;
        //     this.isChange = 0;
        //     this.logisticsProvider = null;
        //     this.$refs.ProTable.refresh();
        //   })
        //   .finally(() => {
        //     this.confirmLoading = false;
        //     this.isButtonDisabled = false;
        //   });
        invoiceAddApi(this.tableData1)
          .then(() => {
            this.$message.success("新增成功");
            this.dialogVisible = false;
            this.isButtonDisabled = false;
            this.$refs.ProTable.refresh();
          })
          .finally(() => {
            this.confirmLoading = false;
            this.isButtonDisabled = false;
          });
      });
    },
    //触发编辑
    handleUpdate(row) {
      const obj = cloneDeep(row);
      obj.type = obj.type.label;
      obj.unit = obj.unit.value;
      obj.product = obj.machine;
      obj.repFrequency = obj.repFrequency.value;
      obj.spareLevel = obj.spareLevel.value;
      obj.inputVisible = false;
      obj.inputValue = "";
      obj.position = obj.position || [];
      this.tableData1 = [];

      this.chooseModelSelection = [];
      this.dialogTitle = "编辑";
      this.tableData1.push(obj);
      this.methodType = "edit";
      this.dialogVisible = true;
    },
    //响应编辑
    update() {
      if (this.tableData1.length === 0) {
        this.$message.warning("请选择零件、机型！");
        return;
      }
      let cango = true;
      this.tableData1.map((ele) => {
        if (!ele.isPm) {
          if (!ele.ch || !ele.en || !ele.unit || !ele.num) {
            cango = false;
          } else {
            cango = true;
          }
        } else {
          if (
            !ele.ch ||
            !ele.en ||
            !ele.unit ||
            !ele.num ||
            !ele.pmCycle ||
            !ele.correctedLifespan ||
            !ele.repFrequency ||
            !ele.spareLevel
          ) {
            cango = false;
          } else {
            cango = true;
          }
        }
      });
      if (!cango) {
        this.$message.warning("请完善表格！");
        return;
      }
      invoiceEditApi(this.tableData1)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    handleInfo(row) {
      this.tableData1 = [];
      this.dialogTitle = "查看";
      this.resetFrom();

      invoiceInfoApi(row.id).then((res) => {
        this.form = cloneDeep(res.data);
        this.form.invoiceItemList?.map((ele) => {
          ele.children = cloneDeep(ele.invoiceItemDetailList);
          ele.children?.map((el, index) => {
            el.id = "c" + index;
          });
          this.tableData1.push(ele);
        });
        this.methodType = "info";
        this.dialogVisible = true;
        // this.$nextTick((e) => {
        //   this.$refs["proform"].resetFormParam();
        // });
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("取消发货后，该发货单关闭，确认关闭?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        invoiceDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
    handleChange(val) {
      if (val && !this.tableData1.length) {
        this.$message.warning("请先选择出库单！");
        this.isChange = 0;
      }
      if (!val) {
        this.logisticsProvider = "";
        return;
      }

      const firstItem = this.tableData1[0];
      if (firstItem && firstItem.currentLogisticsProvider) {
        this.logisticsProvider = firstItem.currentLogisticsProvider.value || "";
      } else {
        this.logisticsProvider = "";
      }
    },
    showModDialog() {
      this.showModelDialog = true;
      this.$nextTick(() => {
        this.$refs.OutWarehouse.$refs.ProTable.refresh();
        this.$refs.OutWarehouse.$refs.ProTable.$refs.ProElTable.clearSelection();
        if (this.chooseModelSelection.length > 0) {
          this.chooseModelSelection.map((row) => {
            this.$refs.OutWarehouse.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
              row
            );
          });
        }
      });
    },
    handleChooseDialogConfirm() {
      verifyOutWarehouse(this.chooseModelSelection).then((res) => {
        if (res.data) {
          this.tableData1 = [];
          const args = {
            outWarehouseList: this.chooseModelSelection,
            logisticsProvider:
              this.chooseModelSelection[0].logisticsProvider?.value,
          };
          invoiceArticleList(args).then((res1) => {
            res1.data?.map((ele) => {
              ele.children = cloneDeep(ele.invoiceItemDetailList);
              ele.id = ele.associatedOrderNumber;
              ele.children?.map((el, index) => {
                el.id = "c" + index;
              });
              this.tableData1.push(ele);
            });

            this.showModelDialog = false;
          });
        }
      });
      // this.partData.push(...this.chooseModelSelection);
      // const idMap = {};
      // this.partData = this.partData.reduce((preVal, curVal) => {
      //   idMap[curVal.id] ? "" : (idMap[curVal.id] = preVal.push(curVal));
      //   return preVal;
      // }, []);
      // this.chooseModelData = [];
      // this.showPartDialog = false;
    },

    handleSelectionChange(val) {
      this.chooseModelSelection = val;
    },

    handleDeleteGoods(row) {
      this.tableData1.splice(
        this.tableData1.findIndex(
          (item) => item.outboundOrderNumber === row.outboundOrderNumber
        ),
        1
      );
      this.chooseModelSelection.splice(
        this.chooseModelSelection.findIndex(
          (item) => item.outWarehouseId === row.outboundOrderNumber
        ),
        1
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
