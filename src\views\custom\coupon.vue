<!--
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-25 10:24:06
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:50
 * @Description: 优惠券查询
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :height="497"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>总数量：{{ totalData?.total || 0 }}</div>
          <div>已使用数量{{ totalData?.used || 0 }}</div>
          <div>已过期数量{{ totalData?.expired || 0 }}</div>
          <div>未使用数量{{ totalData?.unUsed || 0 }}</div>
        </div>
      </template>
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";
import {
  getCustomerCouponByPageApi,
  couponDetailSummaryApi,
} from "@/api/customer";
import { cloneDeep } from "lodash";

export default {
  name: "IntegralInquire",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },

        {
          dataIndex: "ticketName",
          title: "优惠券名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "ticketType",
          title: "优惠券类型",
          formatter: (row) => row.ticketType?.label,
          isSearch: true,
          isTable: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "denomination",
          title: "面额",
          isSearch: true,
          isTable: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "sourceType",
          title: "来源",
          isTable: true,
          formatter: (row) => row.sourceType?.label,
          minWidth: 100,
        },
        {
          dataIndex: "useCode",
          title: "使用单号",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "useType",
          title: "使用类型",
          isTable: true,
          formatter: (row) => {
            return row.useType?.label;
          },
          minWidth: 100,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => {
            return row.status?.label;
          },
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "未使用",
              value: "ACQUIRED",
            },
            {
              label: "已使用",
              value: "USED",
            },
            {
              label: "已过期",
              value: "EXPIRED",
            },
          ],
          minWidth: 100,
        },
        {
          dataIndex: "createdBy",
          title: "创建人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
          minWidth: 100,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "expireDate",
          title: "过期时间",
          isTable: true,
          minWidth: 200,
        },
      ],
      totalData: {},
      tableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    //统计
    getTotalData(params) {
      couponDetailSummaryApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    /**
     * 加载表格数据
     * @param parameter
     */
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      getCustomerCouponByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows || [];
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData(requestParameters);
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
