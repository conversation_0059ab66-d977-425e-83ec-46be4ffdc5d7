import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { getApiBaseUrl } from '../utils/config';

// 创建axios实例，使用运行时配置
const apiClient = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 确保使用最新的API地址
    const currentBaseURL = getApiBaseUrl();
    if (currentBaseURL !== config.baseURL) {
      config.baseURL = currentBaseURL;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 处理成功响应
    if (response.data && response.data.code !== undefined) {
      // 处理自定义响应格式
      if (response.data.code === 200 || response.data.success) {
        return response.data;
      } else {
        return Promise.reject(new Error(response.data.message || '请求失败'));
      }
    }
    return response;
  },
  (error) => {
    // 处理错误响应
    if (error.response) {
      // 服务器返回错误状态码
      console.error('API错误:', error.response.status, error.response.data);
      
      // 处理特定状态码
      switch (error.response.status) {
        case 401:
          // 未授权处理
          console.warn('API未授权，可能需要登录');
          break;
        case 403:
          // 禁止访问处理
          console.warn('API禁止访问，权限不足');
          break;
        case 404:
          // 资源不存在
          console.warn('API资源不存在');
          break;
        case 500:
          // 服务器错误
          console.error('API服务器错误');
          break;
        default:
          console.error('API请求失败');
      }
    } else if (error.request) {
      // 请求发送但没有收到响应
      console.error('API无响应:', error.request);
    } else {
      // 请求配置出错
      console.error('API请求配置错误:', error.message);
    }
    
    return Promise.reject(error);
  }
);

// 通用GET请求
export const get = <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
  return apiClient.get<T>(url, config);
};

// 通用POST请求
export const post = <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
  return apiClient.post<T>(url, data, config);
};

// 通用PUT请求
export const put = <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
  return apiClient.put<T>(url, data, config);
};

// 通用DELETE请求
export const del = <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
  return apiClient.delete<T>(url, config);
};

// 分页响应类型
export interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  empty: boolean;
}

export default apiClient;
