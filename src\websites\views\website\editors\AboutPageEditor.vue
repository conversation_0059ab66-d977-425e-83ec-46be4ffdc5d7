<template>
  <div class="about-page-editor">
    <div class="space-y-8">
      <!-- 页面标题 -->
      <div class="page-title-section">
        <EditableContent
          v-model="content.title"
          display-class="editable-page-title"
          placeholder="点击编辑页面标题"
          @change="handleContentChange"
        />
      </div>

      <!-- 英雄区 -->
      <section class="bg-gray-800 text-white py-16 rounded-lg">
        <div class="text-center px-4">
          <EditableContent
            v-model="content.config.aboutHeroTitle"
            display-class="editable-hero-title"
            placeholder="页面主标题"
            @change="handleContentChange"
          />
          <EditableContent
            v-model="content.config.aboutHeroSubtitle"
            display-class="editable-hero-subtitle"
            placeholder="页面副标题"
            @change="handleContentChange"
          />
        </div>
      </section>

      <!-- 公司简介 -->
      <section class="bg-white rounded-lg p-8 shadow-sm">
        <h3 class="text-xl font-semibold mb-4">公司简介</h3>
        <EditableContent
          v-model="content.config.companyProfile"
          display-class="text-gray-600 leading-relaxed block"
          placeholder="公司简介内容"
          multiline
          @change="handleContentChange"
        />
        <p class="text-sm text-gray-500 mt-2">
          💡 提示：使用两个换行符（\n\n）来分割段落，前端会自动将每个段落分别显示
        </p>
      </section>

      <!-- 公司图片 -->
      <section class="bg-white rounded-lg p-8 shadow-sm">
        <div class="flex justify-between items-center mb-8">
          <h3 class="text-xl font-semibold">公司图片</h3>
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="openCompanyImageModal"
          >
            添加图片
          </el-button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="(image, index) in getCompanyImages()"
            :key="image.id"
            class="bg-gray-50 rounded-lg overflow-hidden relative group"
          >
            <!-- 操作按钮 -->
            <div class="absolute top-2 right-2 opacity-100 transition-opacity z-20">
              <el-button-group>
                <el-button
                  size="mini"
                  icon="el-icon-edit"
                  @click="editCompanyImage(index)"
                />
                <el-popconfirm
                  title="确定删除这张图片吗？"
                  @confirm="removeCompanyImage(index)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                  />
                </el-popconfirm>
              </el-button-group>
            </div>

            <!-- 图片显示 -->
            <div class="h-48 bg-gray-300 overflow-hidden cursor-pointer relative group" @click="previewCompanyImage(image, index)">
              <img
                :src="image.url"
                :alt="image.title"
                class="w-full h-full object-cover transition-transform group-hover:scale-105"
                @error="handleImageError"
              />
              <!-- 放大图标提示 -->
              <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                <div class="opacity-0 group-hover:opacity-100 transition-opacity bg-white bg-opacity-90 rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
                  <i class="el-icon-view text-gray-700 text-lg"></i>
                </div>
              </div>
            </div>

            <div class="p-4">
              <h4 class="font-semibold mb-2 text-sm">{{ image.title }}</h4>
              <p v-if="image.description" class="text-xs text-gray-600">{{ image.description }}</p>
            </div>
          </div>
        </div>

        <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
          <p class="text-sm text-blue-700">
            💡 提示：这些图片将在前端关于页面的公司简介旁边显示，支持1-N张图片的自适应布局
          </p>
        </div>
      </section>


      <!-- 企业文化 -->
      <section class="bg-gray-50 rounded-lg p-8">
        <div class="flex justify-between items-center mb-8">
          <h3 class="text-xl font-semibold">企业文化</h3>
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="openCultureModal"
          >
            添加文化项目
          </el-button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div
            v-for="(culture, index) in getCultureItems()"
            :key="`culture-${culture.id}-${index}`"
            class="bg-white rounded-lg shadow-lg p-6 text-center relative group"
          >
            <!-- 操作按钮 -->
            <div class="absolute top-2 right-2 opacity-100 transition-opacity z-20">
              <el-button-group>
                <el-button
                  size="mini"
                  icon="el-icon-edit"
                  @click="editCulture(index)"
                />
                <el-popconfirm
                  title="确定删除这个文化项目吗？"
                  @confirm="removeCulture(index)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                  />
                </el-popconfirm>
              </el-button-group>
            </div>

            <div class="text-4xl mb-4">{{ culture.icon }}</div>
            <h4 class="text-xl font-bold text-gray-900 mb-3">{{ culture.title }}</h4>
            <p class="text-gray-600">{{ culture.content }}</p>
          </div>
        </div>
      </section>

      <!-- 发展历程 -->
      <section class="bg-white rounded-lg p-8 shadow-sm">
        <div class="flex justify-between items-center mb-8">
          <h3 class="text-xl font-semibold">发展历程</h3>
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="openMilestoneModal"
          >
            添加里程碑
          </el-button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="(milestone, index) in getMilestones()"
            :key="`milestone-${index}`"
            class="bg-gray-50 rounded-lg overflow-hidden relative group"
          >
            <!-- 操作按钮 -->
            <div class="absolute top-2 right-2 opacity-100 transition-opacity z-20">
              <el-button-group>
                <el-button
                  size="mini"
                  icon="el-icon-edit"
                  @click="editMilestone(index)"
                />
                <el-popconfirm
                  title="确定删除这个里程碑吗？"
                  @confirm="removeMilestone(index)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                  />
                </el-popconfirm>
              </el-button-group>
            </div>

            <div class="h-48 bg-gray-300 overflow-hidden cursor-pointer" @click="previewMilestoneImage(milestone)">
              <img
                v-if="milestone.image"
                :src="milestone.image"
                :alt="`${milestone.year}年里程碑`"
                class="w-full h-full object-cover transition-transform group-hover:scale-105"
                @error="handleImageError"
              />
              <div v-else class="w-full h-full flex items-center justify-center bg-gray-200">
                <span class="text-gray-500">无图片</span>
              </div>
            </div>

            <div class="p-4">
              <p class="text-lg font-bold text-gray-800 mb-2">{{ milestone.year }}</p>
              <p class="text-sm text-gray-600 line-clamp-3">{{ milestone.description }}</p>
            </div>
          </div>
        </div>
      </section>


      <!-- 团队介绍 -->
      <section class="bg-white rounded-lg p-8 shadow-sm">
        <div class="flex justify-between items-center mb-8">
          <div>
            <h3 class="text-xl font-semibold mb-2">专业团队</h3>
            <EditableContent
              v-model="content.config.aboutTeamDescription"
              display-class="text-gray-600 block"
              placeholder="团队描述"
              @change="handleContentChange"
            />
          </div>
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="openTeamMemberModal"
          >
            添加团队成员
          </el-button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div
            v-for="(member, index) in getTeamMembers()"
            :key="`team-member-${member.id || index}-${member.name || 'member-' + index}`"
            class="bg-gray-50 rounded-lg p-6 text-center relative group"
          >
            <!-- 操作按钮 -->
            <div class="absolute top-2 right-2 opacity-100 transition-opacity z-20">
              <el-button-group>
                <el-button
                  size="mini"
                  icon="el-icon-edit"
                  @click="editTeamMember(index)"
                />
                <el-popconfirm
                  title="确定删除这个团队成员吗？"
                  @confirm="removeTeamMember(index)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                  />
                </el-popconfirm>
              </el-button-group>
            </div>

            <div class="w-20 h-20 rounded-full mx-auto mb-4 bg-gray-300 flex items-center justify-center overflow-hidden">
              <img
                v-if="member.avatar && member.avatar !== '/api/placeholder/150/150'"
                :src="member.avatar"
                :alt="member.name || '团队成员'"
                class="w-full h-full object-cover"
                @error="handleImageError"
              />
              <span v-else class="text-2xl">👤</span>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-1">{{ member.name || '未命名成员' }}</h4>
            <p class="text-blue-600 font-medium mb-2">{{ member.position || '职位待定' }}</p>
            <p class="text-sm text-gray-600 mb-1">{{ member.experience || '经验待补充' }}</p>
            <p class="text-sm text-gray-500">{{ member.specialty || '专业领域待完善' }}</p>
          </div>
        </div>
      </section>

      <!-- CTA区域 -->
      <section class="bg-blue-600 text-white rounded-lg p-12 text-center">
        <EditableContent
          v-model="content.config.aboutCtaTitle"
          display-class="text-3xl font-bold mb-4 block"
          placeholder="CTA标题"
          @change="handleContentChange"
        />
        <EditableContent
          v-model="content.config.aboutCtaSubtitle"
          display-class="text-xl mb-8 block"
          placeholder="CTA副标题"
          @change="handleContentChange"
        />
        <a
          href="/contact"
          @click.prevent
          class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold inline-block"
        >
          <EditableContent
            v-model="content.config.aboutCtaButtonText"
            placeholder="按钮文字"
            @change="handleContentChange"
          />
        </a>
      </section>
    </div>

    <!-- 团队成员编辑模态框 -->
    <el-dialog
      :title="editingMemberIndex >= 0 ? '编辑团队成员' : '添加团队成员'"
      :visible.sync="teamMemberModalVisible"
      width="600px"
      :modal="true"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="false"
      @close="closeTeamMemberModal"
    >
      <el-form :model="editingMember" label-width="100px" ref="teamMemberForm">
        <el-form-item label="姓名" prop="name" :rules="[{ required: true, message: '请输入姓名' }]">
          <el-input v-model="editingMember.name" placeholder="如：张工程师" />
        </el-form-item>
        <el-form-item label="职位" prop="position" :rules="[{ required: true, message: '请输入职位' }]">
          <el-input v-model="editingMember.position" placeholder="如：技术总监" />
        </el-form-item>
        <el-form-item label="工作经验" prop="experience" :rules="[{ required: true, message: '请输入工作经验' }]">
          <el-input v-model="editingMember.experience" placeholder="如：10年维修经验" />
        </el-form-item>
        <el-form-item label="专业特长" prop="specialty" :rules="[{ required: true, message: '请输入专业特长' }]">
          <el-input v-model="editingMember.specialty" placeholder="如：复印机故障诊断" />
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <ImageUploadInput
            v-model="editingMember.avatar"
            category="team"
            placeholder="头像URL或拖拽上传"
            :max-size="2 * 1024 * 1024"
            compact
          />
          <div class="form-item-tip">可选，留空则使用默认头像</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeTeamMemberModal">取消</el-button>
        <el-button type="primary" @click="saveTeamMember">保存</el-button>
      </div>
    </el-dialog>

    <!-- 里程碑编辑模态框 -->
    <el-dialog
      :title="editingMilestoneIndex >= 0 ? '编辑里程碑' : '添加里程碑'"
      :visible.sync="milestoneModalVisible"
      width="600px"
      :modal="true"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="false"
      @close="closeMilestoneModal"
    >
      <el-form :model="editingMilestone" label-width="100px" ref="milestoneForm">
        <el-form-item label="年份" prop="year" :rules="[{ required: true, message: '请输入年份' }]">
          <el-input v-model="editingMilestone.year" placeholder="如：2010" />
        </el-form-item>
        <el-form-item label="描述" prop="description" :rules="[{ required: true, message: '请输入描述' }]">
          <el-input
            type="textarea"
            v-model="editingMilestone.description"
            :rows="3"
            placeholder="详细描述这个里程碑..."
          />
        </el-form-item>
        <el-form-item label="里程碑图片" prop="image">
          <ImageUploadInput
            v-model="editingMilestone.image"
            category="milestone"
            placeholder="里程碑图片URL或拖拽上传"
            :max-size="5 * 1024 * 1024"
          />
          <div class="form-item-tip">可选，推荐尺寸400x300</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeMilestoneModal">取消</el-button>
        <el-button type="primary" @click="saveMilestone">保存</el-button>
      </div>
    </el-dialog>

    <!-- 公司图片编辑模态框 -->
    <el-dialog
      :title="editingImageIndex >= 0 ? '编辑图片' : '添加图片'"
      :visible.sync="companyImageModalVisible"
      width="600px"
      :modal="true"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="false"
      @close="closeCompanyImageModal"
    >
      <el-form :model="editingCompanyImage" label-width="100px" ref="companyImageForm">
        <el-form-item label="图片标题" prop="title" :rules="[{ required: true, message: '请输入图片标题' }]">
          <el-input v-model="editingCompanyImage.title" placeholder="如：公司前台" />
        </el-form-item>
        <el-form-item label="图片描述" prop="description">
          <el-input v-model="editingCompanyImage.description" placeholder="如：现代化的办公环境" />
        </el-form-item>
        <el-form-item label="图片" prop="url" :rules="[{ required: true, message: '请上传或输入图片URL' }]">
          <ImageUploadInput
            v-model="editingCompanyImage.url"
            category="company"
            :max-size="5 * 1024 * 1024"
            placeholder="拖拽图片到此处上传，或点击选择文件"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeCompanyImageModal">取消</el-button>
        <el-button type="primary" @click="saveCompanyImage">保存</el-button>
      </div>
    </el-dialog>

    <!-- 企业文化编辑模态框 -->
    <el-dialog
      :title="editingCultureIndex >= 0 ? '编辑企业文化' : '添加企业文化'"
      :visible.sync="cultureModalVisible"
      width="600px"
      :modal="true"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="false"
      @close="closeCultureModal"
    >
      <el-form :model="editingCulture" label-width="100px" ref="cultureForm">
        <el-form-item label="图标" prop="icon" :rules="[{ required: true, message: '请输入图标' }]">
          <el-input v-model="editingCulture.icon" placeholder="如：🎯" />
          <div class="form-item-tip">可以使用 emoji 或图标字体</div>
        </el-form-item>
        <el-form-item label="标题" prop="title" :rules="[{ required: true, message: '请输入标题' }]">
          <el-input v-model="editingCulture.title" placeholder="如：使命" />
        </el-form-item>
        <el-form-item label="内容" prop="content" :rules="[{ required: true, message: '请输入内容' }]">
          <el-input
            type="textarea"
            v-model="editingCulture.content"
            :rows="4"
            placeholder="详细描述企业文化内容..."
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeCultureModal">取消</el-button>
        <el-button type="primary" @click="saveCulture">保存</el-button>
      </div>
    </el-dialog>

    <!-- 图片放大预览模态框 -->
    <el-dialog
      :title="previewImageTitle || '图片预览'"
      :visible.sync="imagePreviewVisible"
      width="60%"
      :modal="true"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="true"
      custom-class="image-preview-modal"
      center
    >
      <div class="text-center p-4 relative">
        <!-- 导航按钮 - 左 - 只在查看公司图片时显示 -->
        <el-button
          v-if="getCompanyImages().length > 1 && getCompanyImages().some(img => img.url === previewImageUrl)"
          @click="goToPrevImage"
          class="nav-button nav-button-left"
          icon="el-icon-arrow-left"
          circle
        />

        <!-- 导航按钮 - 右 - 只在查看公司图片时显示 -->
        <el-button
          v-if="getCompanyImages().length > 1 && getCompanyImages().some(img => img.url === previewImageUrl)"
          @click="goToNextImage"
          class="nav-button nav-button-right"
          icon="el-icon-arrow-right"
          circle
        />

        <!-- 图片显示 -->
        <img
          :src="previewImageUrl"
          :alt="previewImageTitle"
          class="preview-image"
          @error="handleImageError"
          @click="imagePreviewVisible = false"
        />

        <!-- 图片信息 -->
        <div class="mt-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-1">
            {{ previewImageTitle }}
          </h3>
          <p class="text-gray-500 text-sm">
            点击图片或外区域关闭预览
          </p>
          <p v-if="getCompanyImages().length > 1 && getCompanyImages().some(img => img.url === previewImageUrl)" class="text-gray-400 text-xs mt-1">
            {{ currentImageIndex + 1 }} / {{ getCompanyImages().length }} | ← → 键切换图片 | ESC 键关闭
          </p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import EditableContent from '@/websites/components/website/EditableContent.vue'
import ImageUploadInput from '@/websites/components/website/ImageUploadInput.vue'

export default {
  name: 'AboutPageEditor',
  components: {
    EditableContent,
    ImageUploadInput
  },
  
  props: {
    content: {
      type: Object,
      required: true
    }
  },
  
  data() {
    return {
      // 模态框状态
      teamMemberModalVisible: false,
      milestoneModalVisible: false,
      companyImageModalVisible: false,
      cultureModalVisible: false,

      // 编辑状态
      editingMemberIndex: -1,
      editingMilestoneIndex: -1,
      editingImageIndex: -1,
      editingCultureIndex: -1,

      // 编辑数据
      editingMember: {
        id: null,
        name: '',
        position: '',
        experience: '',
        specialty: '',
        avatar: ''
      },
      editingMilestone: {
        year: '',
        title: '',
        description: '',
        image: ''
      },
      editingCompanyImage: {
        id: null,
        title: '',
        description: '',
        url: ''
      },
      editingCulture: {
        id: null,
        icon: '',
        title: '',
        content: ''
      },

      // 图片预览状态
      imagePreviewVisible: false,
      previewImageUrl: '',
      previewImageTitle: '',
      currentImageIndex: 0
    }
  },
  
  mounted() {
    this.initializeContent()
  },
  
  methods: {
    // 初始化内容
    initializeContent() {
      if (!this.content.config) {
        this.$set(this.content, 'config', {})
      }

      // 设置默认值，与React版本保持一致
      const defaults = {
        aboutHeroTitle: '关于我们',
        aboutHeroSubtitle: '专业、快速、诚信，客户至上',
        companyProfile: '我们是一家专业从事复印机、打印机等办公设备维修服务的企业，成立于2010年，拥有超过13年的行业经验。',
        aboutTeamDescription: '经验丰富的技师团队，为您提供专业服务',
        aboutCtaTitle: '选择我们，选择专业',
        aboutCtaSubtitle: '13年专业经验，值得您的信赖',
        aboutCtaButtonText: '立即联系我们',
        teamMembers: [
          {
            id: 1,
            name: "张工程师",
            position: "技术总监",
            experience: "10年维修经验",
            specialty: "复印机、打印机故障诊断",
            avatar: "/api/placeholder/150/150"
          },
          {
            id: 2,
            name: "李技师",
            position: "高级技师",
            experience: "8年维修经验",
            specialty: "激光打印机维修",
            avatar: "/api/placeholder/150/150"
          },
          {
            id: 3,
            name: "王师傅",
            position: "资深技师",
            experience: "12年维修经验",
            specialty: "喷墨打印机维修",
            avatar: "/api/placeholder/150/150"
          },
          {
            id: 4,
            name: "陈工程师",
            position: "维修工程师",
            experience: "6年维修经验",
            specialty: "设备保养维护",
            avatar: "/api/placeholder/150/150"
          }
        ],
        milestones: [
          {
            year: "2010",
            title: "2010年",
            description: "公司成立，专注于复印机维修服务",
            image: "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=300&fit=crop"
          },
          {
            year: "2013",
            title: "2013年",
            description: "业务扩展至打印机维修和设备保养",
            image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop"
          },
          {
            year: "2016",
            title: "2016年",
            description: "技师团队扩展到20人，服务能力大幅提升",
            image: "https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=400&h=300&fit=crop"
          },
          {
            year: "2019",
            title: "2019年",
            description: "引入先进检测设备，提高维修效率和质量",
            image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop"
          },
          {
            year: "2022",
            title: "2022年",
            description: "推出24小时快速响应服务，客户满意度显著提升",
            image: "https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=300&fit=crop"
          }
        ],
        companyImages: [
          { id: 1, url: "https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop", title: "公司前台", description: "现代化的办公环境" },
          { id: 2, url: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop", title: "维修车间", description: "专业的设备维修环境" },
          { id: 3, url: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=600&h=400&fit=crop", title: "团队合影", description: "专业的维修团队" }
        ],
        cultureItems: [
          { id: 1, icon: "🎯", title: "使命", content: "为客户提供专业、快速、可靠的办公设备维修服务，让每台设备都能发挥最佳性能。" },
          { id: 2, icon: "👁️", title: "愿景", content: "成为行业领先的办公设备维修服务提供商，以专业技术和优质服务赢得客户信赖。" },
          { id: 3, icon: "💎", title: "价值观", content: "诚信为本，专业至上，客户第一，持续改进，追求卓越。" }
        ]
      }

      Object.keys(defaults).forEach(key => {
        if (!this.content.config[key]) {
          this.$set(this.content.config, key, defaults[key])
        }
      })
    },

    // 处理内容变化
    handleContentChange() {
      this.$emit('change')
      this.$emit('update', this.content)
    },

    // 获取团队成员列表
    getTeamMembers() {
      return this.content.config?.teamMembers || []
    },

    // 获取里程碑列表
    getMilestones() {
      return this.content.config?.milestones || []
    },

    // 获取公司图片列表
    getCompanyImages() {
      return this.content.config?.companyImages || []
    },

    // 获取企业文化列表
    getCultureItems() {
      return this.content.config?.cultureItems || []
    },

    // 处理图片加载错误
    handleImageError(e) {
      e.target.src = 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop'
    },

    // 团队成员管理
    openTeamMemberModal() {
      this.editingMemberIndex = -1
      this.editingMember = {
        name: '',
        position: '',
        experience: '',
        specialty: '',
        avatar: ''
      }
      // 使用nextTick确保DOM更新后再显示对话框
      this.$nextTick(() => {
        this.teamMemberModalVisible = true
      })
    },

    editTeamMember(index) {
      this.editingMemberIndex = index
      const member = this.content.config.teamMembers[index]
      this.editingMember = { ...member }
      // 使用nextTick确保DOM更新后再显示对话框
      this.$nextTick(() => {
        this.teamMemberModalVisible = true
      })
    },

    closeTeamMemberModal() {
      this.teamMemberModalVisible = false
      this.editingMemberIndex = -1
      this.editingMember = {
        name: '',
        position: '',
        experience: '',
        specialty: '',
        avatar: ''
      }
    },

    saveTeamMember() {
      this.$refs.teamMemberForm.validate((valid) => {
        if (valid) {
          if (this.editingMemberIndex >= 0) {
            // 编辑现有成员
            this.$set(this.content.config.teamMembers, this.editingMemberIndex, { ...this.editingMember })
          } else {
            // 添加新成员
            this.content.config.teamMembers.push({ ...this.editingMember })
          }
          this.handleContentChange()
          this.closeTeamMemberModal()
        }
      })
    },

    removeTeamMember(index) {
      const members = [...this.getTeamMembers()]
      members.splice(index, 1)
      this.$set(this.content.config, 'teamMembers', members)
      this.handleContentChange()
    },

    // 里程碑管理
    openMilestoneModal() {
      this.editingMilestoneIndex = -1
      this.editingMilestone = {
        year: '',
        title: '',
        description: '',
        image: ''
      }
      // 使用nextTick确保DOM更新后再显示对话框
      this.$nextTick(() => {
        this.milestoneModalVisible = true
      })
    },

    editMilestone(index) {
      this.editingMilestoneIndex = index
      const milestone = this.content.config.milestones[index]
      this.editingMilestone = { ...milestone }
      // 使用nextTick确保DOM更新后再显示对话框
      this.$nextTick(() => {
        this.milestoneModalVisible = true
      })
    },

    closeMilestoneModal() {
      this.milestoneModalVisible = false
      this.editingMilestoneIndex = -1
      this.editingMilestone = {
        year: '',
        title: '',
        description: '',
        image: ''
      }
    },

    saveMilestone() {
      this.$refs.milestoneForm.validate((valid) => {
        if (valid) {
          // 自动生成title
          this.editingMilestone.title = this.editingMilestone.year

          if (this.editingMilestoneIndex >= 0) {
            // 编辑现有里程碑
            this.$set(this.content.config.milestones, this.editingMilestoneIndex, { ...this.editingMilestone })
          } else {
            // 添加新里程碑
            this.content.config.milestones.push({ ...this.editingMilestone })
          }
          this.handleContentChange()
          this.closeMilestoneModal()
        }
      })
    },

    removeMilestone(index) {
      const milestones = [...this.getMilestones()]
      milestones.splice(index, 1)
      this.$set(this.content.config, 'milestones', milestones)
      this.handleContentChange()
    },

    // 公司图片管理
    openCompanyImageModal() {
      this.editingImageIndex = -1
      this.editingCompanyImage = {
        id: null,
        title: '',
        description: '',
        url: ''
      }
      // 使用nextTick确保DOM更新后再显示对话框
      this.$nextTick(() => {
        this.companyImageModalVisible = true
      })
    },

    editCompanyImage(index) {
      this.editingImageIndex = index
      const image = this.content.config.companyImages[index]
      this.editingCompanyImage = { ...image }
      // 使用nextTick确保DOM更新后再显示对话框
      this.$nextTick(() => {
        this.companyImageModalVisible = true
      })
    },

    closeCompanyImageModal() {
      this.companyImageModalVisible = false
      this.editingImageIndex = -1
      this.editingCompanyImage = {
        id: null,
        title: '',
        description: '',
        url: ''
      }
    },

    saveCompanyImage() {
      this.$refs.companyImageForm.validate((valid) => {
        if (valid) {
          // 为新图片生成ID
          if (this.editingImageIndex < 0) {
            const maxId = Math.max(0, ...this.content.config.companyImages.map(img => img.id || 0))
            this.editingCompanyImage.id = maxId + 1
          }

          if (this.editingImageIndex >= 0) {
            // 编辑现有图片
            this.$set(this.content.config.companyImages, this.editingImageIndex, { ...this.editingCompanyImage })
          } else {
            // 添加新图片
            this.content.config.companyImages.push({ ...this.editingCompanyImage })
          }
          this.handleContentChange()
          this.closeCompanyImageModal()
        }
      })
    },

    removeCompanyImage(index) {
      const images = [...this.getCompanyImages()]
      images.splice(index, 1)
      this.$set(this.content.config, 'companyImages', images)
      this.handleContentChange()
    },

    // 企业文化管理
    openCultureModal() {
      this.editingCultureIndex = -1
      this.editingCulture = {
        id: null,
        icon: '',
        title: '',
        content: ''
      }
      // 使用nextTick确保DOM更新后再显示对话框
      this.$nextTick(() => {
        this.cultureModalVisible = true
      })
    },

    editCulture(index) {
      this.editingCultureIndex = index
      const culture = this.content.config.cultureItems[index]
      this.editingCulture = { ...culture }
      // 使用nextTick确保DOM更新后再显示对话框
      this.$nextTick(() => {
        this.cultureModalVisible = true
      })
    },

    closeCultureModal() {
      this.cultureModalVisible = false
      this.editingCultureIndex = -1
      this.editingCulture = {
        id: null,
        icon: '',
        title: '',
        content: ''
      }
    },

    saveCulture() {
      this.$refs.cultureForm.validate((valid) => {
        if (valid) {
          // 为新文化项生成ID
          if (this.editingCultureIndex < 0) {
            const maxId = Math.max(0, ...this.content.config.cultureItems.map(item => item.id || 0))
            this.editingCulture.id = maxId + 1
          }

          if (this.editingCultureIndex >= 0) {
            // 编辑现有文化项
            this.$set(this.content.config.cultureItems, this.editingCultureIndex, { ...this.editingCulture })
          } else {
            // 添加新文化项
            this.content.config.cultureItems.push({ ...this.editingCulture })
          }
          this.handleContentChange()
          this.closeCultureModal()
        }
      })
    },

    removeCulture(index) {
      const cultures = [...this.getCultureItems()]
      cultures.splice(index, 1)
      this.$set(this.content.config, 'cultureItems', cultures)
      this.handleContentChange()
    },

    // 处理图片加载错误
    handleImageError(event) {
      // 图片加载失败时隐藏图片，显示默认图标
      event.target.style.display = 'none'
      const parent = event.target.parentElement
      if (parent) {
        const icon = parent.querySelector('span')
        if (icon) {
          icon.style.display = 'inline'
        } else {
          // 如果没有默认图标，创建一个
          const defaultIcon = document.createElement('span')
          defaultIcon.className = 'text-2xl'
          defaultIcon.textContent = '👤'
          parent.appendChild(defaultIcon)
        }
      }
    },

    // 图片预览功能
    previewCompanyImage(image, index) {
      if (!image.url) return
      this.previewImageUrl = image.url
      this.previewImageTitle = image.title
      this.currentImageIndex = index
      this.imagePreviewVisible = true
    },

    previewMilestoneImage(milestone) {
      if (!milestone.image) return
      this.previewImageUrl = milestone.image
      this.previewImageTitle = `${milestone.year} - ${milestone.description}`
      this.currentImageIndex = 0
      this.imagePreviewVisible = true
    },

    // 切换到上一张图片（仅公司图片支持）
    goToPrevImage() {
      const companyImages = this.getCompanyImages()
      if (companyImages.length <= 1) return

      const prevIndex = this.currentImageIndex > 0 ? this.currentImageIndex - 1 : companyImages.length - 1
      const prevImage = companyImages[prevIndex]
      this.previewImageUrl = prevImage.url
      this.previewImageTitle = prevImage.title
      this.currentImageIndex = prevIndex
    },

    // 切换到下一张图片（仅公司图片支持）
    goToNextImage() {
      const companyImages = this.getCompanyImages()
      if (companyImages.length <= 1) return

      const nextIndex = this.currentImageIndex < companyImages.length - 1 ? this.currentImageIndex + 1 : 0
      const nextImage = companyImages[nextIndex]
      this.previewImageUrl = nextImage.url
      this.previewImageTitle = nextImage.title
      this.currentImageIndex = nextIndex
    }

  }
}
</script>

<style lang="scss" scoped>
.about-page-editor {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 16px 20px;

  .space-y-8 > * + * {
    margin-top: 2rem;
  }

  // 页面标题区域样式 - 与服务介绍页面保持一致
  .page-title-section {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 2rem;
  }

  // Tailwind-like utility classes
  .text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .text-xs { font-size: 0.75rem; line-height: 1rem; }
  .text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .text-5xl { font-size: 3rem; line-height: 1; }

  .font-bold { font-weight: 700; }
  .font-semibold { font-weight: 600; }
  .font-medium { font-weight: 500; }

  .text-center { text-align: center; }
  .text-white { color: white; }
  .text-gray-600 { color: #6b7280; }
  .text-gray-700 { color: #374151; }
  .text-gray-800 { color: #1f2937; }
  .text-gray-900 { color: #111827; }
  .text-gray-500 { color: #9ca3af; }
  .text-blue-600 { color: #2563eb; }
  .text-blue-700 { color: #1d4ed8; }

  .bg-gray-800 { background-color: #1f2937; }
  .bg-gray-50 { background-color: #f9fafb; }
  .bg-gray-300 { background-color: #d1d5db; }
  .bg-gray-200 { background-color: #e5e7eb; }
  .bg-white { background-color: white; }
  .bg-blue-600 { background-color: #2563eb; }
  .bg-blue-50 { background-color: #eff6ff; }

  .rounded-lg { border-radius: 0.5rem; }
  .rounded-full { border-radius: 9999px; }

  .p-8 { padding: 1.5rem; }
  .p-6 { padding: 1rem; }
  .p-4 { padding: 0.75rem; }
  .p-3 { padding: 0.5rem; }
  .p-12 { padding: 2.5rem; }
  .px-4 { padding-left: 0.75rem; padding-right: 0.75rem; }
  .px-8 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
  .py-16 { padding-top: 4rem; padding-bottom: 4rem; }

  .mb-1 { margin-bottom: 0.25rem; }
  .mb-2 { margin-bottom: 0.5rem; }
  .mb-3 { margin-bottom: 0.75rem; }
  .mb-4 { margin-bottom: 1rem; }
  .mb-6 { margin-bottom: 1.5rem; }
  .mb-8 { margin-bottom: 2rem; }
  .mt-2 { margin-top: 0.5rem; }
  .mt-4 { margin-top: 1rem; }
  .mx-auto { margin-left: auto; margin-right: auto; }

  .block { display: block; }
  .inline-block { display: inline-block; }
  .flex { display: flex; }
  .grid { display: grid; }

  .items-center { align-items: center; }
  .justify-between { justify-content: space-between; }
  .justify-center { justify-content: center; }

  .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .gap-6 { gap: 1.5rem; }

  .w-20 { width: 5rem; }
  .h-20 { height: 5rem; }
  .h-48 { height: 12rem; }
  .w-full { width: 100%; }
  .h-full { height: 100%; }
  .w-12 { width: 3rem; }
  .h-12 { height: 3rem; }

  .shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
  .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

  .border { border-width: 1px; }
  .border-blue-200 { border-color: #bfdbfe; }

  .leading-relaxed { line-height: 1.625; }

  .overflow-hidden { overflow: hidden; }
  .object-cover { object-fit: cover; }

  .relative { position: relative; }
  .absolute { position: absolute; }
  .top-2 { top: 0.5rem; }
  .right-2 { right: 0.5rem; }
  .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

  .z-20 { z-index: 20; }

  .opacity-100 { opacity: 1; }
  .opacity-0 { opacity: 0; }
  .bg-opacity-0 { background-color: rgba(0, 0, 0, 0); }
  .bg-opacity-20 { background-color: rgba(0, 0, 0, 0.2); }
  .bg-opacity-90 { background-color: rgba(255, 255, 255, 0.9); }

  .transition-opacity { transition-property: opacity; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
  .transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
  .transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }

  .group:hover .group-hover\:scale-105 { transform: scale(1.05); }
  .group:hover .group-hover\:opacity-100 { opacity: 1; }
  .group:hover .group-hover\:bg-opacity-20 { background-color: rgba(0, 0, 0, 0.2); }

  .cursor-pointer { cursor: pointer; }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  // Responsive classes
  @media (min-width: 768px) {
    .md\:text-5xl { font-size: 3rem; line-height: 1; }
    .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  }

  @media (min-width: 1024px) {
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  }

  // 修复对话框遮罩层问题
  ::v-deep .el-dialog__wrapper {
    z-index: 2000 !important;
  }

  ::v-deep .el-overlay {
    z-index: 2000 !important;
  }

  ::v-deep .el-dialog {
    z-index: 2001 !important;
  }

  .form-item-tip {
    color: #909399;
    font-size: 12px;
    margin-top: 4px;
  }
}

/* 图片预览模态框样式 */
::v-deep .image-preview-modal {
  .el-dialog {
    max-width: 600px;
    min-width: 400px;
  }

  .el-dialog__body {
    padding: 20px;
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .el-dialog {
      width: 95% !important;
      max-width: none;
      min-width: none;
      margin: 0 auto;
    }
  }
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: zoom-out;
}

.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  z-index: 10;

  &:hover {
    background: rgba(255, 255, 255, 1);
  }
}

.nav-button-left {
  left: 20px;
}

.nav-button-right {
  right: 20px;
}
</style>
