<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:50:01
 * @Description: 库品管理
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :local-pagination="localPagination"
      :data="tableData"
      :query-param="queryParam"
      :height="500"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-circle-check"
            @click="handleInfo(row, 'info')"
          >
            详情
          </el-button>
          <el-button
            v-if="row.status === 'CREATE'"
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(row, 'audit')"
          >
            审核
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="80%"
      :confirm-button-disabled="confirmLoading"
      :top="'10%'"
      :no-footer="methodType === 'info'"
      :no-confirm-footer="methodType === 'audit'"
      @cancel="cancelDialogVisible"
    >
      <ProForm
        v-if="dialogVisible"
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :open-type="methodType"
      >
        <template #status>
          {{
            form.status === "CREATE"
              ? "提交申请"
              : form.status === "WAIT_IN_WAREHOUSE"
              ? "待入库"
              : form.status === "REFUSE_IN_WAREHOUSE"
              ? "拒绝入库"
              : form.status === "DONE"
              ? "完成"
              : "/"
          }}</template
        >
        <template #location>
          <div class="title-box">领料明细</div>
          <DataTable
            :columns="columns1"
            :show-setting="false"
            :show-pagination="false"
            :show-search="false"
            row-key="id"
            :data="returnDetailList"
            sticky
            height="70vh"
          >
            <template #saleAttrVals="slotProps">
              <div
                v-for="attr in slotProps.row?.itemStore?.skuInfo?.saleAttrVals"
                :key="attr.val"
              >
                {{ attr.name }}:{{ attr.val }}
              </div>
            </template>
            <template #auditNum="{ row }">
              <el-input-number
                v-model="row.auditNum"
                style="width: 100%"
                size="small"
                :min="0"
                placeholder="请输入审核数量"
                controls-position="right"
                :disabled="methodType !== 'audit'"
                @change="(e) => handleRowNumChange(e, row)"
              ></el-input-number>
            </template>
            <!--<template #action="{ row }">-->
            <!--  <div class="fixed-width">-->
            <!--    <el-button-->
            <!--      type="btn3"-->
            <!--      size="mini"-->
            <!--      icon="el-icon-circle-check"-->
            <!--      :disabled="methodType === 'info'"-->
            <!--      @click="handleRowAudit(row)"-->
            <!--    >-->
            <!--      审核-->
            <!--    </el-button>-->
            <!--  </div>-->
            <!--</template>-->
          </DataTable>
        </template>
      </ProForm>
      <template #footer>
        <el-button
          type="danger"
          :loading="confirmLoading"
          @click="handleAuditOk('REJECT', 'warning')"
        >
          全部驳回
        </el-button>
        <el-button
          type="primary"
          :loading="confirmLoading"
          @click="handleAuditOk('WAIT_IN_WAREHOUSE', 'success')"
        >
          审核通过
        </el-button>
        <el-button @click="cancelDialogVisible">取消</el-button>
      </template>
    </ProDrawer>
    <!-- 审核 -->
    <!-- <ProDialog
      :value="dialogAuditVisible"
      :title="'审核'"
      width="600px"
      :top="'10%'"
      :no-footer="false"
      :confirm-text="'确定'"
      @ok="handleAuditDialogOk"
      @cancel="handleAuditDialogCancel"
    >
      <el-form ref="auditFormRef" :model="auditForm" label-width="120px">
        <el-form-item
          prop="status"
          label="审核状态:"
          :rules="[
            { required: true, message: '请选择审核状态', trigger: 'blur' },
          ]"
        >
          <el-radio-group v-model="auditForm.status">
            <el-radio label="REJECT">驳回</el-radio>
            <el-radio label="WAIT_IN_WAREHOUSE">同意</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </ProDialog> -->
  </div>
</template>
<script>
import {
  applyReturnPageApi,
  applyReturnInfoApi,
  applyReturnAuditApi,
} from "@/api/repair";

import { cloneDeep } from "lodash";

export default {
  name: "ReBack",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      tableData: [],
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      queryParam: {},
      columns: [
        {
          dataIndex: "code",
          title: "退料单编号",
          isTable: true,
        },
        {
          dataIndex: "keyword",
          title: "退料单编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "engineerName",
          title: "工程师姓名",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "engineerMobile",
          title: "工程师手机号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },

        {
          dataIndex: "createdAt",
          title: "发起时间",
          isTable: true,
        },

        {
          dataIndex: "status",
          title: "退料状态",
          isTable: true,
          formatter: (row) => {
            switch (row.status) {
              case "CREATE":
                return "提交申请";
              case "WAIT_IN_WAREHOUSE":
                return "待入库";
              case "REJECT":
                return "驳回";
              case "DONE":
                return "完成";
            }
          },
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "提交申请",
              value: "CREATE",
            },
            {
              label: "待入库",
              value: "WAIT_IN_WAREHOUSE",
            },
            {
              label: "驳回",
              value: "REJECT",
            },
            {
              label: "完成",
              value: "DONE",
            },
          ],
        },

        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 140,
        },
      ],
      columns1: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          formatter: (row) => row.itemStore?.articleCode,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          formatter: (row) => row.articleName,
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          formatter: (row) => row.itemStore?.itemName,
        },

        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          formatter: (row) => row.itemStore?.oemNumber,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku规格",
          isTable: true,
          tableSlot: "saleAttrVals",
        },
        {
          dataIndex: "num",
          title: "退料数量",
          isTable: true,
        },
        {
          dataIndex: "auditNum",
          title: "入库数量",
          isTable: true,
          tableSlot: "auditNum",
          width: 150,
        },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   tableSlot: "action",
        //   width: 100,
        // },
      ],
      //新增
      methodType: "add",
      confirmLoading: false,
      form: {},
      returnDetailList: [],
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      formColumns: [
        {
          isForm: true,
          dataIndex: "code",
          title: "退料单编号",
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "engineerName",
          title: "工程师姓名",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "engineerMobile",
          title: "工程师手机号",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "createdAt",
          title: "发起时间",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "status",
          title: "退料状态",
          isForm: true,
          valueType: "text",
          formSlot: "status",
          formSpan: 8,
        },
        {
          dataIndex: "code",
          title: "物流单号",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "location",
          title: "领料明细",
          formOtherSlot: "location",
          isForm: true,
          formSpan: 24,
        },
      ],
      dialogAuditVisible: false,
      auditForm: {},
      auditId: "",
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      applyReturnPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    handleAudit(row) {
      this.auditForm.id = row.id;
      this.dialogAuditVisible = true;
    },
    // handleAuditDialogOk() {
    //   this.$refs["auditFormRef"].validate((valid) => {
    //     if (valid) {
    //       applyReturnAuditApi(this.auditForm).then((res) => {
    //         this.$message.success("操作成功");
    //         this.refresh();
    //         this.dialogAuditVisible = false;
    //       });
    //     }
    //   });
    // },
    /**
     * 整单审核
     * @param status
     * @param type
     */
    handleAuditOk(status, type) {
      const statusText = status === "WAIT_IN_WAREHOUSE" ? "同意" : "驳回";
      this.$confirm(`此操作将${statusText}该退料申请，是否继续`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type,
      }).then(() => {
        this.confirmLoading = true;
        const args = {
          id: this.auditId,
          status,
          applyReturnDetails: this.returnDetailList,
        };
        applyReturnAuditApi(args)
          .then((res) => {
            this.$message.success("操作成功");
            this.refresh();
            this.dialogVisible = false;
          })
          .finally(() => {
            this.confirmLoading = false;
          });
      });
    },
    handleRowNumChange(e, row) {
      const { num, auditNum } = row;
      if (auditNum > num) {
        this.$message.error("审核数量不能大于领料数量");
        this.$nextTick(() => {
          row.auditNum = num;
        });
      }
    },
    cancelDialogVisible() {
      this.auditId = "";
      this.methodType = "add";
      this.dialogVisible = false;
    },
    handleAuditDialogCancel() {
      this.auditForm = {};
      this.dialogAuditVisible = false;
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    // 触发详情
    handleInfo(row, type) {
      this.auditId = row.id;
      this.dialogTitle = "退料单详情";
      this.resetFrom();
      applyReturnInfoApi(row.id).then((res) => {
        this.form = res.data;
        this.returnDetailList = this.form.applyReturnDetailList.map((item) => {
          return {
            ...item,
            auditNum: this.methodType === "audit" ? item.num : item.auditNum,
          };
        });
      });
      this.form.engineerMobile = row.engineerMobile;
      this.form.engineerName = row.engineerName;
      this.methodType = type;
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["ProForm"].resetFormParam();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.searchNumbers {
  width: 240px;
  height: 40px;
  display: flex;
  align-items: center;
  color: #dcdfe6;
}

.kuwei {
  width: 130px !important;

  ::v-deep .el-input__inner {
  }
}
</style>
