<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-25 14:24:19
 * @Description: 
 -->
<!--
 * @Description: 分享活动
 * @version: 
 * @Author: Lenle
 * @Date: 2025-02-11 11:19:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-25 14:24:19
-->
<template>
  <div>
    <el-button
      type="success"
      class="add-btn"
      size="mini"
      style="margin-bottom: 10px"
      icon="el-icon-plus"
      @click="addSaleRow()"
    >
      新增
    </el-button>
    <el-table
      :data="modelValue"
      style="width: 100%; margin-bottom: 20px"
      :height="400"
    >
      <el-table-column type="index" label="序号"> </el-table-column>
      <el-table-column prop="awardType" label="礼品类型">
        <template slot-scope="scope">
          <el-select
            v-model="scope.row.awardType"
            @change="changeType(scope.row)"
          >
            <el-option label="零件耗材" value="PART"></el-option>
            <el-option label="积分券" value="POINTS"></el-option>
            <el-option label="维修券" value="REPAIR"></el-option>
            <el-option label="购机券" value="MACHINE"></el-option>
            <el-option label="代金券" value="TICKET"></el-option>
            <el-option label="其他" value="OTHER"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="awardName" label="礼品名称">
        <template slot-scope="scope">
          <div
            v-if="scope.row.awardType == 'PART'"
            @click="checkSKU(scope.row, scope.$index)"
          >
            <el-button v-if="!scope.row.awardName" type="text">选择</el-button>
            <el-button v-else type="text">{{ scope.row.awardName }}</el-button>
          </div>
          <el-input v-else v-model="scope.row.awardName"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="articleCode" label="编码"> </el-table-column>
      <el-table-column prop="price" label="单价/券面额">
        <template slot-scope="scope">
          <span v-if="scope.row.awardType === 'REPAIR'">
            {{ scope.row.price }}
          </span>
          <el-input v-else v-model="scope.row.price" type="number"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="maxBuy" label="单个客户最多数量">
        <template slot-scope="scope">
          <el-input v-model.number="scope.row.maxBuy" type="number"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="quantity" label="总数量">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.quantity"
            type="number"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="limit" label="限制">
        <template slot-scope="scope">
          <div
            v-if="
              scope.row.awardType === 'REPAIR' ||
              scope.row.awardType === 'MACHINE' ||
              scope.row.awardType === 'TICKET'
            "
          >
            <el-button
              type="text"
              :disabled="false"
              @click="showLimitDialog(scope.row, scope.$index)"
            >
              限制条件
            </el-button>
          </div>
          <div v-else></div>
        </template>
      </el-table-column>
      <el-table-column prop="skuId" label="操作" width="80px">
        <template slot-scope="scope">
          <div class="fixed-width">
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="deleteRow(scope.row, scope.$index)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      custom-class="center-dialog"
      width="80vw"
      :visible.sync="skuVisible"
      append-to-body
      title="关联耗材零件"
    >
      <div>
        <ProTable
          ref="ProTable"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :columns="SKUcolumns"
          show-pagination
          :local-pagination="localPagination"
          :data="tableData"
          sticky
          :height="400"
          :show-selection="true"
          row-key="saleSkuId"
          :query-param="queryParam"
          @loadData="loadData"
          @handleSelected="selectStu"
        >
          <template #btn> 商品列表 </template>
          <template #type="slotProps">
            {{ slotProps.row.type.label }}
          </template>
          <template #picsUrl="{ row }">
            <el-image
              v-if="row.picsUrl && row.picsUrl.length > 0"
              style="max-width: 100px; height: 100px"
              :src="getPicsUrlImg(row)"
              :preview-src-list="[getPicsUrlImg(row)]"
            >
            </el-image>
          </template>
          <template #saleAttrVals="slotProps">
            <span
              v-for="(item, index) in slotProps.row.saleAttrVals"
              :key="index"
              style="border: 1px solid #ddd"
              >{{ item.name }}: {{ item.val }}
            </span>
          </template>

          <template #saleStatus="slotProps">
            {{ slotProps.row.saleStatus ? "已上架" : "未上架" }}
          </template>
        </ProTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="skuVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveStuData">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 优惠券使用限制条件 -->
    <el-dialog
      title="限制条件"
      :visible.sync="limitVisible"
      :close-on-click-modal="false"
      width="500px"
      append-to-body
    >
      <div
        v-if="
          currentRow.awardType === 'REPAIR' ||
          currentRow.awardType === 'MACHINE'
        "
      >
        <el-radio-group
          v-model="currentRow.limitType"
          @change="handleLimitType"
        >
          <el-radio label="GENERAL">通用</el-radio>
          <el-radio label="MODEL">机型限制</el-radio>
        </el-radio-group>
        <div
          v-if="currentRow.limitType === 'MODEL'"
          style="
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            gap: 10px;
            align-items: center;
          "
        >
          <span>机器型号：</span>
          <el-cascader
            v-model="currentRow.fullIdPath"
            :options="modelOptions"
            placeholder="请选择可使用该券机型"
            filterable
            clearable
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
              expandTrigger: 'click',
              multiple: true,
            }"
            leaf-only
            style="flex: 1"
            @change="handleProductChange"
          ></el-cascader>
        </div>
      </div>
      <div v-if="currentRow.awardType === 'TICKET'">
        <el-radio-group v-model="currentRow.limitType">
          <el-radio label="GENERAL">通用</el-radio>
          <el-radio label="AMOUNT">最低消费限制</el-radio>
        </el-radio-group>
        <div
          v-if="currentRow.limitType === 'AMOUNT'"
          style="
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            gap: 10px;
            align-items: center;
          "
        >
          <span>最低消费金额：</span>
          <el-input
            v-model="currentRow.minAmount"
            style="flex: 1"
            type="number"
            placeholder="请输入最低消费金额"
          >
            <template slot="append">元</template>
          </el-input>
        </div>
      </div>
      <div
        style="
          margin-top: 20px;
          display: flex;
          justify-content: space-between;
          gap: 10px;
          align-items: center;
        "
      >
        <span>过期时间：</span>
        <el-date-picker
          v-if="
            currentRow.awardType === 'REPAIR' ||
            currentRow.awardType === 'MACHINE' ||
            currentRow.awardType === 'TICKET'
          "
          v-model="currentRow.expireDate"
          style="flex: 1"
          type="date"
          placeholder="选择过期时间"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
        <div v-else></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="limitVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveLimitData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { cloneDeep, merge } from "lodash";
import { itemSummaryListApi } from "@/api/goods";
import { dictTreeByCodeApi2 } from "@/api/user";
import { productAllApi } from "@/api/dispose";
import Span from "@/views/custom/editCustom/span.vue";
export default {
  name: "ShareTable",
  components: { Span },
  model: {
    prop: "modelValue",
    event: "change",
  },
  props: {
    modelValue: {
      type: Array,
      default: () => [],
    },
    activityId: {
      default: 0,
      type: [String, Number],
    },
  },
  data() {
    return {
      skuVisible: false,
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {},
      SKUcolumns: [
        {
          dataIndex: "lastIds",
          title: "适用机型",
          isSearch: true,
          clearable: true,
          valueType: "product",
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isSearch: true,
          isTable: true,
          valueType: "input",
          clearable: true,
          minWidth: 150,
          width: 180,
        },

        {
          dataIndex: "itemCode",
          title: "商品编号",
          isSearch: true,
          valueType: "input",
          clearable: true,
          minWidth: 150,
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          width: 150,
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          width: 120,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          formSpan: 16,
          width: 180,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
        },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
          formatter: (row) => row.type?.label,
        },
        {
          dataIndex: "productPartTypeList",
          title: "物品小类",
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
      ],
      newStuTable: [],
      actRowIndex: {},
      selectRow: {},
      limitVisible: false,
      currentRow: {},
      currentIndex: null,
      modelOptions: [],
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.getProductThird();
  },
  methods: {
    //添加促销商品
    addSaleRow() {
      const resultList = cloneDeep(this.modelValue);
      resultList.push({
        activityId: this.activityId || 0,
        articleCode: "",
        awardName: "",
        awardType: "",
        maxBuy: "",
        price: "",
        quantity: "",
        skuId: "",
      });
      this.$emit("change", resultList);
    },
    showLimitDialog(row, index) {
      this.limitVisible = true;
      this.currentIndex = index;
      // 初始化当前行数据
      if (!row.limitType) {
        this.currentRow = {
          ...row,
          limitType: "GENERAL",
          limitInfo: [],
          minAmount: null,
          expireDate: "",
        };
      } else if (row.limitType.label) {
        this.currentRow = cloneDeep(row);
        this.currentRow.limitType = this.currentRow.limitType?.value;
        if (
          Array.isArray(this.currentRow.fullIdPath) &&
          this.currentRow.fullIdPath.length > 0
        ) {
          this.currentRow.fullIdPath = this.currentRow.fullIdPath.map(
            (path) => {
              const trimmedString = path.replace(/^\/|\/$/g, "");
              const parts = trimmedString.split("/");
              return parts.map((part) => part);
            }
          );
        }
      } else {
        this.currentRow = cloneDeep(row);
      }
    },
    saveLimitData() {
      const resultList = cloneDeep(this.modelValue);
      const row = resultList[this.currentIndex];
      resultList[this.currentIndex] = {
        ...row,
        limitType: this.currentRow.limitType,
        limitInfo: this.currentRow.limitInfo || [],
        fullIdPath: this.currentRow.fullIdPath || [],
        expireDate: this.currentRow.expireDate || "",
        minAmount:
          this.currentRow.limitType === "AMOUNT"
            ? this.currentRow.minAmount
            : null,
      };
      this.$emit("change", resultList);
      this.limitVisible = false;
    },
    deleteRow(row, index) {
      const resultList = cloneDeep(this.modelValue);
      resultList.splice(index, 1);
      // resultList = resultList.filter((o) => o.skuId != row.skuId);
      this.$emit("change", resultList);
    },
    //显示商品列表
    checkSKU(row, index) {
      this.skuVisible = true;
      this.actRowIndex = index;
      this.selectRow = row;
      this.queryParam = {};
      this.$nextTick(() => {
        this.$refs.ProTable.refresh();
      });
      // this.loadData({ pageNumber: 1, pageSize: 10 });
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const requestParameters = cloneDeep(this.queryParam);
      itemSummaryListApi({ ...requestParameters, saleStatus: "ON_SALE" })
        .then((res) => {
          this.tableData = res.data.rows;
          //     .map((item) => ({
          //   ...item,
          //   saleStatus: item.saleStatus === "ON_SALE",
          // }));
          this.localPagination.total = parseInt(res.data.total);
          this.$nextTick(() => {
            this.selectRow.skuId &&
              this.$refs.ProTable.setSelection([this.selectRow.skuId]);
            !this.selectRow.skuId && this.$refs.ProTable.setSelection([]);
          });
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    selectStu(selectedRows, row) {
      this.selectRow.articleCode = row.articleCode;
      this.selectRow.awardName = row.itemName;
      this.selectRow.price = row.saleUnitPrice;
      this.selectRow.skuId = row.saleSkuId;
      this.$refs.ProTable.setSelection([this.selectRow.skuId]);
    },
    saveStuData() {
      const resultList = cloneDeep(this.modelValue);
      const row = resultList[this.actRowIndex];
      resultList[this.actRowIndex] = merge({}, row, this.selectRow);
      this.$emit("change", resultList);
      this.skuVisible = false;
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.modelOptions = res.data;
      });
    },
    handleProductChange(arr) {
      this.currentRow.limitInfo = arr.map((item) => item[item.length - 1]);
    },
    handleLimitType() {
      this.currentRow.limitInfo = [];
      this.currentRow.fullIdPath = [];
      this.currentRow.minAmount = null;
    },
    //礼品类型切换，商品数据
    changeType(row) {
      //耗材，清空
      row.skuId = "";
      row.articleCode = "";
      if (row.awardType == "PART") {
        row.awardName = "";
        row.price = "";
      } else if (row.awardType == "REPAIR") {
        row.price = 1;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
:deep(.center-dialog) {
  .el-dialog__body {
    padding-top: 10px;
    .el-table__body-wrapper {
      overflow: auto;
    }
  }
  &.el-dialog {
    top: 50%;
    transform: translate(0, -50%);
    margin-top: 0 !important;
  }
  .has-gutter .el-checkbox {
    display: none;
  }
}
:deep(.dialog-proTable) {
  .table-page-search-submitButtons {
    display: inline-block;
    width: auto;
    margin-top: 0;
  }
  #search-form-wrap {
    width: calc(100% - 150px);
    display: inline-block;
    vertical-align: middle;
  }
}
</style>
