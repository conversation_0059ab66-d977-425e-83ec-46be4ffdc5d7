<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      row-key="label"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="550"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #deviceTree>
        <el-cascader
          v-model="productIdName"
          filterable
          clearable
          style="width: 250px"
          :options="productTreeOption"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          @change="handleProductTree"
        >
        </el-cascader>
      </template>
    </ProTable>
  </div>
</template>
<script>
import { dictTreeByCodeApi } from "@/api/user";
import { divideAmount, filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { customCostListApi } from "@/api/statisics";
import { productListApi } from "@/api/dispose";

export default {
  name: "CostInquire",
  mixins: [],
  props: {},
  data() {
    return {
      tableData: [],
      productIdName: "",
      productTreeOption: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        startDate: null,
        endDate: null,
        costDate: [],
      },

      columns: [
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          // valueType: "select",
          isSearch: true,
          clearable: true,
          valueType: "product",
          // width: 200,
          // searchSlot: "deviceTree",
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          width: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          width: 200,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "machine",
          title: "机型",
          isTable: true,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          valueType: "input",
          placeholder: "1号机： 1",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.deviceGroup?.label,
        },
        {
          dataIndex: "treatyTypes",
          title: "合约类型",
          valueType: "select",
          isSearch: true,
          isTable: true,
          multiple: true,
          clearable: true,
          option: [],
          formatter: (row) => row.treatyType.label,
          optionMth: () => dictTreeByCodeApi(1200),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请输入合约类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "regCliState",
          title: "安装客户端",
          valueType: "select",
          // isSearch: true,
          isTable: true,
          clearable: true,
          option: [
            { value: 1, label: "是" },
            { value: 0, label: "否" },
          ],
          formatter: (row) => (row.regCliState == 1 ? "是" : "否"),
        },
        {
          dataIndex: "workLaborAmount",
          title: "人工费",
          isTable: true,
          formatter: (row) => divideAmount(row.workLaborAmount, 100),
        },
        {
          dataIndex: "workPartAmount",
          title: "零件耗材费",
          isTable: true,
          formatter: (row) => divideAmount(row.workPartAmount, 100),
        },
        {
          dataIndex: "workAmount",
          title: "总维修费",
          isTable: true,
          formatter: (row) => divideAmount(row.workAmount, 100),
        },
        {
          dataIndex: "totalPrintNum",
          title: "总印量",
          isTable: true,
          // isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "cost",
          title: "成本（元/张）",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
          // startPlaceholder: "最小成本",
          // endPlaceholder: "最大成本",
        },
        {
          dataIndex: "cost",
          title: "成本",
          // isSearch: true,
          valueType: "inputRange",
          startPlaceholder: "最小成本",
          endPlaceholder: "最大成本",
        },
        {
          dataIndex: "costDate",
          title: "核算时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
        },
      ],
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
    this.computedDate();
    this.getProductTree();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );

      const result = [
        {
          startDate: null,
          endDate: null,
          data: parameter.costDate, // 成本核算——成本核算时间
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.costDate;
      customCostListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    computedDate() {
      const date = new Date(new Date());
      const year = date.getFullYear();
      const month = ("0" + (date.getMonth() + 1)).slice(-2);
      const day = ("0" + date.getDate()).slice(-2);
      const formattedTime = year + "-" + month + "-" + day;

      const sixMonthsAgo = new Date(date);
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      const pastYear = sixMonthsAgo.getFullYear();
      const pastMonth = ("0" + (sixMonthsAgo.getMonth() + 1)).slice(-2);
      const pastDay = ("0" + sixMonthsAgo.getDate()).slice(-2);
      const formattedPastTime = pastYear + "-" + pastMonth + "-" + pastDay;

      this.queryParam.startDate = formattedPastTime;
      this.queryParam.endDate = formattedTime;
      this.queryParam.costDate = [formattedPastTime, formattedTime];
    },
    async getProductTree() {
      try {
        const result = await productListApi({ pageNumber: 1, pageSize: 9999 });
        if (result.code === 200 && result.data) {
          this.productTreeOption = result.data;
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleProductTree(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
