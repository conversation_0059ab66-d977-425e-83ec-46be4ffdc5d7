<!--
 * @Description: 应用版本管理主页面
-->
<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">应用版本管理</h1>
        <p class="page-description">管理应用版本发布和分配情况</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-plus" @click="showPublishDialog">
          发布新版本
        </el-button>
        <el-dropdown @command="handleEmergencyCommand">
          <el-button type="danger">
            紧急操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="rollback">版本回退</el-dropdown-item>
            <el-dropdown-item command="pause">暂停所有更新</el-dropdown-item>
            <el-dropdown-item command="resume">恢复更新推送</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button icon="el-icon-refresh" @click="handleRefresh" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 版本管理区域 -->
    <el-card class="version-section">
      <div slot="header" class="section-header">
        <span>版本列表</span>
        <div class="header-right">
          <el-tag type="success">{{ versionList.length }} 个版本</el-tag>
          <el-button
            type="primary"
            icon="el-icon-refresh"
            @click="handleVersionListRefresh"
            size="mini"
            :loading="versionListLoading"
          >
            刷新
          </el-button>
        </div>
      </div>
      <VersionList
        ref="versionList"
        @edit="handleEdit"
        @delete="handleDelete"
        @toggle-force="handleToggleForce"
        @set-targeted="handleSetTargeted"
        @set-global="handleSetGlobal"
        @view-distributions="handleViewDistributions"
        @version-list-updated="handleVersionListUpdated"
      />
    </el-card>

    <!-- 分配关系区域 -->
    <el-card class="distribution-section">
      <div slot="header" class="section-header">
        <span>分配关系情况</span>
        <div class="search-bar">
          <el-switch v-model="showActiveOnly" active-text="仅激活" inactive-text="全部" />
          <el-button type="primary" icon="el-icon-refresh" @click="refreshDistributions" size="small">刷新</el-button>
        </div>
      </div>
      <DistributionList ref="distributionList" :show-active-only="showActiveOnly" />
    </el-card>

    <!-- 对话框组件 -->
    <PublishDialog :visible.sync="publishDialogVisible" @success="handlePublishSuccess" />
    <EditDialog :visible.sync="editDialogVisible" :version-data="currentEditVersion" @success="handleEditSuccess" />
    <EmergencyDialog :visible.sync="emergencyDialogVisible" :action-type="emergencyActionType" :version-list="versionList" @success="handleEmergencySuccess" />
    <TargetedReleaseDialog :visible.sync="targetedReleaseDialogVisible" :version-info="currentVersion" @success="handleTargetedReleaseSuccess" />
    <DistributionDialog :visible.sync="distributionDialogVisible" :version-info="currentVersion" />
  </div>
</template>

<script>
import VersionList from './components/VersionList.vue';
import PublishDialog from './components/PublishDialog.vue';
import EditDialog from './components/EditDialog.vue';
import EmergencyDialog from './components/EmergencyDialog.vue';
import TargetedReleaseDialog from './components/TargetedReleaseDialog.vue';
import DistributionDialog from './components/DistributionDialog.vue';
import DistributionList from './components/DistributionList.vue';
import {
  deleteVersion,
  toggleForceUpdate,
  setGlobalRelease
} from '@/appupdate/api/appVersion';

export default {
  name: 'AppUpdateManagement',
  components: {
    VersionList,
    PublishDialog,
    EditDialog,
    EmergencyDialog,
    TargetedReleaseDialog,
    DistributionDialog,
    DistributionList,
  },
  data() {
    return {
      publishDialogVisible: false,
      editDialogVisible: false,
      emergencyDialogVisible: false,
      targetedReleaseDialogVisible: false,
      distributionDialogVisible: false,
      currentEditVersion: null,
      currentVersion: null,
      emergencyActionType: '',
      versionList: [],
      loading: false,
      versionListLoading: false,
      showActiveOnly: false,
    };
  },
  methods: {
    showPublishDialog() {
      this.publishDialogVisible = true;
    },
    handlePublishSuccess() {
      this.$message.success('版本发布成功');
      this.publishDialogVisible = false;
      this.$refs.versionList.refresh();
    },
    handleEdit(version) {
      this.currentEditVersion = { ...version };
      this.editDialogVisible = true;
    },

    handleEditSuccess() {
      this.$message.success('版本信息更新成功');
      this.editDialogVisible = false;
      this.currentEditVersion = null;
      this.$refs.versionList.refresh();
    },
    async handleDelete(version) {
      try {
        await this.$confirm(`确认要删除版本 ${version.versionName} 吗？删除后不可恢复。`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });
        await deleteVersion(version.id);
        this.$message.success(`版本 ${version.versionName} 删除成功`);
        this.$refs.versionList.refresh();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除版本失败');
        }
      }
    },

    async handleToggleForce(version) {
      const action = version.adminForce ? '取消强制更新' : '设为强制更新';
      try {
        await this.$confirm(`确认要${action}吗？`, '确认操作', { type: 'warning' });
        await toggleForceUpdate(version.id, !version.adminForce);
        this.$message.success(`${action}成功`);
        this.$refs.versionList.refresh();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(`${action}失败`);
        }
      }
    },
    handleEmergencyCommand(command) {
      this.emergencyActionType = command;
      this.versionList = this.$refs.versionList.getVersionList();
      this.emergencyDialogVisible = true;
    },
    handleEmergencySuccess(message) {
      this.$message.success(message);
      this.emergencyDialogVisible = false;
      this.emergencyActionType = '';
      this.$refs.versionList.refresh();
    },
    handleRefresh() {
      this.$refs.versionList.refresh();
    },

    /**
     * 刷新版本列表
     */
    handleVersionListRefresh() {
      this.versionListLoading = true;
      this.$refs.versionList.refresh();
      // 模拟加载时间，实际应该在版本列表组件完成加载后设置为false
      setTimeout(() => {
        this.versionListLoading = false;
      }, 1000);
    },

    /**
     * 设置定向发布
     */
    handleSetTargeted(version) {
      this.currentVersion = version;
      this.targetedReleaseDialogVisible = true;
    },

    /**
     * 转为全局发布
     */
    async handleSetGlobal(version) {
      try {
        await this.$confirm(
          `确定将版本 ${version.versionName} 转为全局发布吗？转换后所有用户都可以接收到此更新。`,
          '转换确认',
          {
            confirmButtonText: '确定转换',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        await setGlobalRelease(version.id);
        this.$message.success('已转为全局发布');
        this.$refs.versionList.refresh();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('转换失败：' + error.message);
        }
      }
    },

    /**
     * 查看分发情况
     */
    handleViewDistributions(version) {
      this.currentVersion = version;
      this.distributionDialogVisible = true;
    },

    /**
     * 定向发布成功回调
     */
    handleTargetedReleaseSuccess() {
      this.$refs.versionList.refresh();
    },

    /**
     * 处理错误
     */
    handleError(_, message = '操作失败') {
      this.$message.error(message);
    },

    /**
     * 刷新分配关系列表
     */
    refreshDistributions() {
      if (this.$refs.distributionList) {
        this.$refs.distributionList.refresh();
      }
    },

    /**
     * 处理激活状态过滤变化
     */
    handleActiveFilterChange() {
      this.refreshDistributions();
    },

    /**
     * 处理版本列表更新
     */
    handleVersionListUpdated(versions) {
      this.versionList = versions || [];
    },

    /**
     * 处理刷新
     */
    handleRefresh() {
      this.loading = true;
      if (this.$refs.versionList) {
        this.$refs.versionList.refresh();
      }
      this.refreshDistributions();
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    },

    /**
     * 处理导出
     */
    handleExport() {
      this.$message.info('导出功能开发中...');
    },


  },
};
</script>



<style lang="scss" scoped>
.app-container {
  padding: 16px;
  box-sizing: border-box;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .page-title {
      margin: 0 0 4px 0;
      font-size: 22px;
      font-weight: 500;
      color: #303133;
    }
    .page-description {
      margin: 0;
      color: #909399;
      font-size: 13px;
    }
    .header-actions .el-button {
      margin-left: 8px;
    }
  }

  .version-section, .distribution-section {
    margin-bottom: 16px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-right {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .search-bar {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
}</style>

<!-- 针对当前页面的全局样式修复 -->
<style lang="scss">
/* 确保主容器有正确的滚动设置 */
.el-main.main {
  overflow: hidden !important;
  height: 100% !important;
}

/* 只在 appupdate 页面应用滚动修复 */
.app-container {
  max-height: calc(100vh - 120px) !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}


</style>
