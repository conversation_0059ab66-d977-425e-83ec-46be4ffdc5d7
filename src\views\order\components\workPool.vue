<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 14:06:56
 * @Description: 维修工单列表
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      :local-pagination="localPagination"
      :data="tableData"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <!-- <div>工单数：{{ totalData?.orderNum || 0 }}</div> -->
          <div>人工费用：{{ totalData?.laborAmount || 0 }}</div>
          <div>耗材费用：{{ totalData?.itemPay || 0 }}</div>
          <div>实付费用：{{ totalData?.totalPay || 0 }}</div>
          <div>减免费用：{{ totalData?.discountAmount || 0 }}</div>
          <div>总费用：{{ totalData?.totalAmount || 0 }}</div>
        </div>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 350px"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleChange"
        ></el-cascader>
      </template>
      <template #actions="{ row }">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleInfo(row)"
          >
            查看
          </el-button>
          <el-button
            v-if="row.status?.value === 'pending_orders'"
            type="primary"
            size="mini"
            icon="el-icon-thumb"
            @click="handleUpdate(row.id, row.productId, 'allot')"
          >
            分配工单
          </el-button>
          <el-button
            v-if="
              row.status.value === 'engineer_receive' ||
              row.status.value === 'engineer_departure' ||
              row.status.value === 'engineer_arrive'
            "
            type="primary"
            size="mini"
            icon="el-icon-news"
            @click="handleUpdate(row.id, row.productId, 'transfer')"
          >
            转派工单
          </el-button>

          <el-button
            v-if="
              row.status?.value !== 'close' && row.status?.value !== 'completed'
            "
            size="mini"
            type="danger"
            icon="el-icon-circle-close"
            @click="handleDelete(row.id)"
          >
            关闭工单
          </el-button>
        </span>
      </template>
    </ProTable>

    <!--  详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="70%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <div class="dialog-content-box">
        <div>
          <div class="boxa box0">
            <div class="tit-box">维修进度跟踪</div>
            <div class="sp-content jbxx-box steps-container">
              <el-steps :active="stepActive">
                <el-step
                  index="CREATE"
                  title="发起报修"
                  :description="form.createdAt"
                ></el-step>
                <el-step
                  index="ENGINEER_RECEIVE"
                  title="工程师接单"
                  :description="form.orderReceiveTime"
                ></el-step>
                <el-step
                  index="ENGINEER_DEPARTURE"
                  title="工程师出发"
                  :description="form.departureTime"
                ></el-step>

                <el-step
                  index="ENGINEER_ARRIVE"
                  title="到店维修"
                  :description="form.actualArriveTime"
                ></el-step>
                <el-step
                  index="WAIT_CONFIRM"
                  :description="form.sendReportTime"
                  title="提交维修报告"
                ></el-step>
                <el-step
                  index="DONE"
                  :description="form.completedAt"
                  title="已完成"
                ></el-step>
              </el-steps>
              <!-- 时间差显示 -->
              <div class="duration-labels">
                <div v-if="form.orderReceiveTime" class="duration-item">
                  {{ getTimeDiff(form.createdAt, form.orderReceiveTime) }}
                </div>
                <div v-if="form.departureTime" class="duration-item">
                  {{ getTimeDiff(form.orderReceiveTime, form.departureTime) }}
                </div>
                <div v-if="form.actualArriveTime" class="duration-item">
                  {{ getTimeDiff(form.departureTime, form.actualArriveTime) }}
                </div>
                <div v-if="form.sendReportTime" class="duration-item">
                  {{ getTimeDiff(form.actualArriveTime, form.sendReportTime) }}
                </div>
                <div v-if="form.completedAt" class="duration-item">
                  {{ getTimeDiff(form.sendReportTime, form.completedAt) }}
                </div>
              </div>
            </div>
          </div>
          <div class="boxa box1">
            <div class="tit-box">工单信息</div>
            <div class="card-box">
              <el-descriptions :column="2">
                <el-descriptions-item label="工单编号">
                  {{ form.code }}
                </el-descriptions-item>
                <el-descriptions-item label="客户编号">
                  {{ form.customer?.seqId }}
                </el-descriptions-item>
                <el-descriptions-item label="工单状态" :span="2">
                  {{ form.status?.label }}
                </el-descriptions-item>
                <el-descriptions-item label="报修人">
                  {{ form?.customerStaff }}
                </el-descriptions-item>
                <el-descriptions-item label="报修人电话">
                  {{ form?.contactPhone }}
                </el-descriptions-item>

                <el-descriptions-item label="店铺名称">
                  {{ form.customer?.name }}
                </el-descriptions-item>
                <el-descriptions-item label="店铺地址">
                  {{ form.customer?.address }}
                </el-descriptions-item>

                <el-descriptions-item label="设备组图" :span="2">
                  <div>
                    <el-image
                      class="imgs1"
                      :src="
                        form.deviceGroupImg && form.deviceGroupImg.url
                          ? form.deviceGroupImg.url
                          : require('../../../assets/images/top.png')
                      "
                      alt="设备"
                      :preview-src-list="[form.deviceGroupImg?.url]"
                    ></el-image>
                    <div
                      style="float: left; margin-left: 10px; font-weight: bold"
                    >
                      <div>{{ form.deviceGroup?.label }}</div>
                      <div>{{ form.productInfo }}</div>
                    </div>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="故障描述" :span="2">
                  {{ form.excDesc }}
                </el-descriptions-item>
                <el-descriptions-item label="故障照片" :span="2">
                  <div style="display: flex; width: 100%; flex-wrap: wrap">
                    <el-image
                      v-for="(item, index) in form.excPics"
                      :key="index"
                      class="imgs"
                      :src="item.url"
                      alt=""
                      :preview-src-list="[item.url]"
                    ></el-image>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="card-box">
              <el-descriptions :column="2">
                <!-- <el-descriptions-item label="报修发起时间">{{
                  form.createdAt
                }}</el-descriptions-item> -->
                <el-descriptions-item label="期望到店时间">
                  {{ form.expectArriveTime }}
                </el-descriptions-item>
                <el-descriptions-item label="预计到店时间">
                  {{ form.prospectArriveTime }}
                </el-descriptions-item>
                <el-descriptions-item label="接单工程师">
                  {{ form.engineerId?.name }}
                </el-descriptions-item>
                <!-- <el-descriptions-item label="维修工程师"></el-descriptions-item> -->
                <el-descriptions-item label="接单时长">
                  {{ form.orderReceiveTime }}
                </el-descriptions-item>
                <el-descriptions-item label="出发位置">
                  {{ form?.departureAddress }}
                </el-descriptions-item>
                <!--<el-descriptions-item-->
                <!--  label="确认工单时长"-->
                <!--&gt;</el-descriptions-item>-->
                <!-- <el-descriptions-item label="领料时长"></el-descriptions-item> -->
                <el-descriptions-item label="路途时长">
                  {{ form.travelTime }}
                </el-descriptions-item>
                <el-descriptions-item label="维修时长">
                  {{ form.fixTime }}
                </el-descriptions-item>
                <!-- <el-descriptions-item
                  label="确认报告时长"></el-descriptions-item> -->
                <!-- <el-descriptions-item label="付款时长"></el-descriptions-item> -->
                <el-descriptions-item v-if="form.repairReport" label="维修报告">
                  <el-button
                    type="primary"
                    size="small"
                    style="padding: 4px 8px"
                    @click="handleReport(form.id)"
                  >
                    查看
                  </el-button>
                </el-descriptions-item>
                <el-descriptions-item v-if="form.isAppeal" label="申诉次数">
                  {{ form.appealCount }}次
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="card-box">
              <el-descriptions :column="2">
                <el-descriptions-item label="上门费">
                  {{ form.visitPay }} 元
                </el-descriptions-item>
                <el-descriptions-item label="远程误工费">
                  {{ form.longWayVisitPay || 0 }} 元
                </el-descriptions-item>
                <el-descriptions-item label="零件更换费">
                  {{ form.actualReplacePay || 0 }} 元
                </el-descriptions-item>
                <el-descriptions-item label="维修诊断费">
                  {{ form.repairPay || 0 }} 元
                </el-descriptions-item>
                <el-descriptions-item label="工程师减免费用">
                  -{{ form.derateAmount || 0 }} 元
                </el-descriptions-item>
                <el-descriptions-item label="工程师加价">
                  {{ form.engineerAdditionalPay || 0 }}元
                </el-descriptions-item>

                <el-descriptions-item label="客户追加报酬">
                  {{ form.additionalPay || 0 }} 元
                </el-descriptions-item>
                <el-descriptions-item label="会员减免" :span="2">
                  -{{ form.discountAmount || 0 }} 元
                </el-descriptions-item>
                <!--   form.repairPay-->
                <el-descriptions-item label="技术咨询费用合计" :span="2">
                  <span style="color: #ff541e">¥ {{ form.laborCost }}元</span>
                </el-descriptions-item>
                <el-descriptions-item label="耗材费用合计" :span="2">
                  <span style="color: #ff541e">{{ form.itemPay }} 元</span>
                </el-descriptions-item>

                <el-descriptions-item label="总费用" :span="2">
                  <span style="color: #ff541e">
                    合计：¥ {{ form?.totalAmount }}元
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="实付费用" :span="2">
                  <span style="color: #ff541e">
                    合计：¥ {{ form?.totalPay || 0 }}元
                  </span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>

        <div v-if="form.isEvaluated" class="card-box evaluation-container">
          <div class="evaluate-content">
            <div class="evaluate-header">
              <i class="el-icon-star-on"></i>
              <span>服务评价</span>
            </div>

            <div class="evaluate-body">
              <div class="evaluate-item">
                <span class="label">专业能力:</span>
                <el-rate
                  v-model="form.workEvaluate.professional"
                  show-text
                  :texts="['非常差', '差', '一般', '好', '非常好']"
                  disabled
                ></el-rate>
              </div>

              <div class="evaluate-item">
                <span class="label">服务态度:</span>
                <el-rate
                  v-model="form.workEvaluate.service"
                  show-text
                  :texts="['非常差', '差', '一般', '好', '非常好']"
                  disabled
                ></el-rate>
              </div>

              <div class="evaluate-content-box">
                <div class="content-header">
                  <span class="label">评价内容:</span>
                </div>
                <div class="content-body">
                  {{ form.workEvaluate.content || "暂无评价内容" }}
                </div>
              </div>

              <div class="evaluate-item time-item">
                <span class="label">评价时间:</span>
                <span class="content">
                  {{ form.workEvaluate.createdAt || "暂无评价时间" }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="btn-box">
          <el-button
            v-if="
              form.status?.value != 'close' && form.status?.value != 'completed'
            "
            type="danger"
            @click="handleDelete(form.id)"
          >
            关闭工单
          </el-button>
          <el-button
            v-if="form.status?.value == 'pending_orders'"
            type="primary"
            @click="handleUpdate(form.id, form.productId, 'allot')"
          >
            指派其他工程师
          </el-button>
        </div>
      </div>
    </ProDrawer>
    <ProDialog
      :value="dialogVisible1"
      :title="allotDialogTitle"
      width="500px"
      :confirm-loading="confirmLoading"
      :confirm-text="allotType === 'allot' ? '确认指派' : '确认转派'"
      :top="'10%'"
      @ok="handleDialogOk"
      @cancel="dialogVisible1 = false"
    >
      <ProForm
        v-if="dialogVisible1"
        ref="proform"
        :form-param="form1"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      ></ProForm>
    </ProDialog>
    <ProDialog
      :value="dialogVisible3"
      :title="'关闭工单'"
      width="600px"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      @ok="handleDialogOk3"
      @cancel="dialogVisible3 = false"
    >
      <div style="margin-bottom: 20px">关闭后，此工单作废。</div>
      <el-form :model="form3" label-width="180px" style="width: 100%">
        <el-form-item label="是否需要收取店铺维修费" style="width: 100%">
          <el-radio-group
            v-model="form3.needPay"
            size="medium"
            style="width: 100%"
            @change="getPrice(true)"
          >
            <el-radio border :label="false">否</el-radio>
            <el-radio border :label="true">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form3.needPay" label="上门费" style="width: 100%">
          <el-input
            v-model="form3.visitPay"
            style="width: 80%"
            type="number"
            :min="0"
            @input="getPrice()"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="form3.needPay" label="维修费" style="width: 100%">
          <el-input
            v-model="form3.repairPay"
            style="width: 80%"
            type="number"
            :min="0"
            @input="getPrice()"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="form3.needPay" label="应付金额" style="width: 100%">
          {{ allPay }}元
        </el-form-item>
      </el-form>
    </ProDialog>
    <ProDialog
      :value="dialogVisible2"
      :title="'维修报告'"
      width="60%"
      :top="'0px'"
      :no-footer="true"
      @cancel="dialogVisible2 = false"
    >
      <div class="dialog-content-box">
        <div style="height: 700px; overflow: hidden; overflow-y: scroll">
          <div class="boxa box0">
            <div class="tit-box">故障描述</div>
            <div style="line-height: 20px; width: 80%">
              {{ formreport?.excDesc }}
            </div>
            <div style="display: flex; width: 100%; flex-wrap: wrap">
              <el-image
                v-for="(item, index) in formreport?.excDescPics"
                :key="index"
                class="imgs"
                :src="item.url"
                alt=""
                :preview-src-list="[item.url]"
              ></el-image>
            </div>
          </div>
          <div class="boxa box1">
            <div class="tit-box">解决措施</div>
            <div>
              <div style="line-height: 20px; width: 80%">
                {{ formreport?.resolveDesc }}
              </div>

              <div style="display: flex; width: 100%; flex-wrap: wrap">
                <el-image
                  v-for="(item, index) in formreport?.resolveDescPics"
                  :key="index"
                  class="imgs"
                  :src="item.url"
                  alt=""
                  :preview-src-list="[item.url]"
                ></el-image>
              </div>
            </div>
            <div class="tit-box">下次注意事项</div>
            <div>
              <div style="line-height: 20px; width: 80%">
                {{
                  formreport?.announcements ? formreport?.announcements : "无"
                }}
              </div>
            </div>
            <div class="tit-box">其他</div>
            <div style="line-height: 30px">
              现象分类：{{ formreport?.excType?.label }}
            </div>
            <div style="line-height: 30px">
              原因分类：{{ formreport?.reasonType?.label }}
            </div>
            <div style="line-height: 30px">
              处理类型：{{ formreport?.resolveType?.label }}
            </div>
            <div style="line-height: 30px">
              故障组件：{{ formreport?.excUnit?.label }}
            </div>
            <div style="line-height: 30px">
              黑白计数器：{{ formreport.blackWhiteCount }}
            </div>
            <div style="line-height: 30px">
              彩色计数器：{{ formreport.colorCount }}
            </div>
            <div style="line-height: 30px">
              上次维修后到目前的印量：{{ formreport.printCount }}
            </div>

            <div class="tit-box">更换耗材零件</div>
            <div>
              <DataTable
                ref="ProTable1"
                :columns="columns1"
                :show-setting="false"
                :show-pagination="false"
                :show-search="false"
                row-key="index"
                :data="tableData1"
                sticky
                :height="350"
                style="width: 100%; margin-top: 20px"
                :show-table-operator="false"
              >
                <template #picUrl="slotProps">
                  <el-image
                    :preview-src-list="[slotProps.row?.skuInfo?.picUrl[0]?.url]"
                    style="width: 100px; height: 100px"
                    :src="slotProps.row?.skuInfo?.picUrl[0]?.url"
                  ></el-image>
                </template>

                <template #saleAttrVals="slotProps">
                  <div
                    v-for="attr in slotProps.row?.skuInfo?.saleAttrVals"
                    :key="attr.val"
                  >
                    {{ attr.name }}:{{ attr.val }}
                  </div>
                </template>
              </DataTable>
            </div>
          </div>
        </div>
      </div>
    </ProDialog>
    <AppealStep
      ref="AppealStep"
      :value="appealValue"
      :title="appealTitle"
      @handleRowDetail="handleReport"
      @cancel="closeAppealDialog"
    />
  </div>
</template>
<script>
import {
  getWorkOrderByPageApi,
  WorkOrderInfoApi,
  closeOrderApi,
  changeEngineerApi,
  engineerListApi,
  getReportApi,
  calcPay,
  getWorkOrderSummary,
  changeOrderApi,
} from "@/api/repair";

import { cloneDeep } from "lodash";
import { dictTreeByCodeApi, getRoleByCode } from "@/api/user";
import { productAllApi } from "@/api/dispose";
import AppealStep from "@/views/order/components/appealStep.vue";
import { filterParam, filterParamRange, getTimeDiff } from "@/utils";

export default {
  name: "WorkPool",
  components: { AppealStep },
  mixins: [],
  props: {
    // serType: {
    //   type: [Array, String],
    //   default: () => [],
    // },
  },
  data() {
    return {
      allPay: 0,
      stepActive: 0,
      stepList: [
        "CREATE",
        "ENGINEER_RECEIVE",
        "ENGINEER_DEPARTURE",
        "ENGINEER_ARRIVE",
        "WAIT_CONFIRM",
        "DONE",
      ],
      options: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      productIdName: "",
      queryParam: {},
      columns: [
        {
          dataIndex: "code",
          title: "工单编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "phone",
          title: "手机号码",
          isTable: true,
          isSearch: true,
          width: 110,
          valueType: "input",
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组",
          formatter: (row) => row.deviceGroup?.label,
          isSearch: true,
          valueType: "input",
          placeholder: "1号机：1",
          isTable: true,
        },
        {
          dataIndex: "errorCode",
          title: "故障代码",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "status",
          title: "工单状态",
          valueType: "select",
          isTable: true,
          isSearch: true,
          multiple: true,
          // formatter: (row) => row.status?.label,
          formatter: (row) =>
            row.cancelStatus && row.status?.value === "close"
              ? "客户取消"
              : row.status?.label,
          option: [
            { label: "待接单", value: "pending_orders" },
            { label: "工单确认中", value: "customer_confirming" },
            { label: "工程师接单", value: "engineer_receive" },
            { label: "工程师出发", value: "engineer_departure" },
            { label: "工程师到达", value: "engineer_arrive" },
            { label: "待确认维修报告", value: "wait_confirmed_report" },
            { label: "已完成", value: "completed" },
            { label: "待结算", value: "to_be_settled" },
            { label: "待审核", value: "wait_audit" },
            { label: "客户取消", value: "close" },
            { label: "关闭", value: "close" },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },
        // {
        //   dataIndex: "treatyTypes",
        //   title: "合约类型",
        //   isTable: true,
        //   formatter: (row) => row.treatyType?.label,
        // },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => row.serType?.label,
          width: 80,
        },
        {
          dataIndex: "serTypes",
          title: "服务类型",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "散修",
              value: "SCATTERED",
            },
            {
              label: "购机不保",
              value: "NO_WARRANTY",
            },
            {
              label: "购机质保",
              value: "WARRANTY",
            },
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "质保服务",
              value: "QA",
            },
            {
              label: "质保含部件",
              value: "QA_COMPONENT",
            },
            {
              label: "维保服务",
              value: "MAINTENANCE",
            },
          ],
          // optionMth: () => dictTreeByCodeApi(1200),
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
        },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isTable: true,
          minWidth: 140,
        },
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          isSearch: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
        {
          dataIndex: "engineerId",
          title: "工程师",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [],
          formatter: (row) => row.engineerId.name,
        },
        {
          dataIndex: "createdAt",
          title: "下单日期",
          width: 150,
          isTable: true,
          valueType: "date-picker",
          pickerType: "date",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
        },
        {
          dataIndex: "payMode",
          title: "支付方式",
          isTable: true,
          formatter: (row) => row.payMode?.label,
          isSearch: true,
          valueType: "select",
          option: [
            { label: "微信支付", value: "WECHART" },
            { label: "线下支付", value: "OFFLINE" },
            // { label: "周期支付", value: "CYCLE" },
          ],
          minWidth: 100,
        },
        {
          dataIndex: "visitPay",
          title: "上门费",
          isTable: true,
        },
        {
          dataIndex: "longWayVisitPay",
          title: "远途费",
          isTable: true,
        },
        {
          dataIndex: "repairPay",
          title: "维修诊断费",
          width: 120,
          isTable: true,
        },
        {
          dataIndex: "itemPay",
          title: "维修耗材费",
          width: 120,
          formatter: (row) => (row.itemPay ? row.itemPay : 0),
          isTable: true,
        },
        {
          dataIndex: "replacePay",
          title: "零件更换费",
          width: 120,
          formatter: (row) => (row.replacePay ? row.replacePay : 0),
          isTable: true,
        },
        {
          dataIndex: "additionalPay",
          title: "客户追加费",
          width: 120,
          isTable: true,
        },
        {
          dataIndex: "engineerAdditionalPay",
          title: "工程师追加费",
          formatter: (row) =>
            row.engineerAdditionalPay ? row.engineerAdditionalPay : 0,
          width: 120,
          isTable: true,
        },
        {
          dataIndex: "discountAmount",
          title: "会员减免",
          isTable: true,
        },
        {
          dataIndex: "derateAmount",
          title: "工程师减免",
          width: 120,
          formatter: (row) => (row.derateAmount ? row.derateAmount : 0),
          isTable: true,
        },
        {
          dataIndex: "totalPay",
          title: "总费用",
          isTable: true,
        },
        {
          dataIndex: "isEvaluated",
          title: "有无评价",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "有",
              value: true,
            },
            {
              label: "无",
              value: false,
            },
          ],
        },
        {
          dataIndex: "createTimeStartDate",
          title: "下单日期",
          width: 150,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
        {
          dataIndex: "sendReportTime",
          title: "报告提交日期",
          width: 150,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
        {
          dataIndex: "completedAt",
          title: "维修完成日期",
          width: 150,
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
        },
        {
          dataIndex: "Actions",
          title: "操作",
          align: "left",
          fixed: "right",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 250,
        },
      ],
      // 工程师列表
      workerList: [],
      //新增
      methodType: "add",
      confirmLoading: false,
      form: {},
      dialogTitle: "",
      dialogVisible: false,
      form1: {},
      form3: {},
      dialogVisible1: false,
      allotDialogTitle: "指派工单",
      dialogVisible2: false,
      formreport: {},
      dialogVisible3: false,
      defaultFormParams: {},
      formcolumns: [],
      tableData1: [],
      columns1: [
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 120,
          formatter: (row) => row.itemStore?.oemNumber,
        },
        {
          dataIndex: "picUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picUrl",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          formatter: (row) => row.itemStore?.articleCode,
          width: 120,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku规格",
          isTable: true,
          width: 110,
          tableSlot: "saleAttrVals",
        },

        {
          dataIndex: "saleUnitPrice",
          title: "单价",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "num",
          title: "更换数量",
          width: 80,
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "location",
          title: "更换位置",
          width: 120,
          isTable: true,
          formatter: (row) =>
            Array.isArray(row.location) ? row.location.join("、") : "",
        },
        {
          dataIndex: "itemStore",
          title: "更换类型",
          isTable: true,
          width: 100,
          formatter: (row) => {
            switch (row.itemStore?.skuSource) {
              case "ENGINEER_APPLY":
                return "工程师外带";
              case "MALL":
                return "商城购入";
              case "APPLY":
                return "客户领料";
              case "CUSTOMER_REGISTER":
                return "客户外购登记";
              default:
                return "客户自配";
            }
          },
        },
      ],
      appealValue: false,
      appealTitle: "",
      totalData: {},
      allotType: "allot", // 工单分配类型： allot 分配  transfer 转派
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
    this.init();
    this.searchList();
  },
  methods: {
    getTimeDiff,
    getPrice(type) {
      const obj = {
        repairPay: this.form3.repairPay || 0,
        visitPay: this.form3.visitPay || 0,
        needPay: this.form3.needPay,
        id: this.form3.id,
      };
      if (!type) {
        setTimeout(() => {
          calcPay(obj).then((res) => {
            this.allPay = res.data;
          });
        }, 600);
      } else {
        calcPay(obj).then((res) => {
          this.allPay = res.data;
        });
      }
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
      dictTreeByCodeApi(2100).then((res) => {
        this.goodsTypeOptions = res.data;
      });
    },

    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const paramRange = [
        {
          createTimeStart: null,
          createTimeEnd: null,
          data: parameter.createTimeStartDate,
        },
        {
          sendReportTimeStart: null,
          sendReportTimeEnd: null,
          data: parameter.sendReportTime,
        },
        {
          completeTimeStart: null,
          completeTimeEnd: null,
          data: parameter.completedAt,
        },
      ];
      filterParamRange(this, this.queryParam, paramRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createTimeStartDate;
      delete requestParameters.sendReportTime;
      delete requestParameters.completedAt;
      getWorkOrderByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
      this.getTotalData(requestParameters);
    },
    getTotalData(params) {
      getWorkOrderSummary(params).then((res) => {
        this.totalData = res.data;
      });
    },
    // 搜索列表处理
    searchList() {
      // 工程师列表
      this.workerList = [];
      getRoleByCode("engineer", { pageNumber: 1, pageSize: 9999 }).then(
        (res) => {
          res.data.rows.map((item) => {
            this.workerList.push({
              value: item.id,
              label: item.name,
            });
          });
        }
      );
      this.columns.forEach((item) => {
        if (item.dataIndex === "engineerId") {
          item.option = this.workerList;
        }
      });
    },
    // 初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    // 触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    // 响应表单提交
    proSubmit(val) {
      // this.$confirm(
      //   `此操作会将工单${
      //     this.allotType === "allot" ? "指派" : "转派"
      //   }给该工程师, 是否继续?`,
      //   "提示",
      //   {
      //     confirmButtonText: "确定",
      //     cancelButtonText: "取消",
      //     type: "warning",
      //   }
      // ).then(() => {
      //
      // });
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      const args = {
        engineerId: this.form1.engineerId,
        id: this.form1.id,
      };
      const editApi =
        this.allotType === "allot" ? changeEngineerApi : changeOrderApi;
      editApi(args)
        .then(() => {
          this.$message.success(
            `${this.allotType === "allot" ? "指派" : "转派"}成功`
          );
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible1 = false;
          this.dialogVisible = false;
          this.allotType = "allot";
          this.$refs.ProTable.refresh();
        });
    },
    closedialog() {
      this.dialogVisible = false;
    },
    // 触发编辑
    handleUpdate(id, productId, type = "allot") {
      this.form1 = {};
      this.form1.id = id;
      this.allotType = type;
      this.allotDialogTitle = type === "allot" ? "指派工单" : "转派工单";
      this.methodType = "edit";
      this.dialogVisible1 = true;
      this.formcolumns = [
        {
          dataIndex: "engineerId",
          title: "维修工程师",
          isForm: true,

          valueType: "select",
          option: [],
          optionMth: () => engineerListApi(productId),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择维修工程师",
              trigger: "change",
            },
          ],
          formSpan: 24,
        },
      ];
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.code;
      this.resetFrom();
      WorkOrderInfoApi(row.id).then((res) => {
        this.form = cloneDeep(res.data);
        this.stepActive = this.stepList.indexOf(this.form.currentProcess) + 1;
        // if (this.form.currentProcess == "DONE") {
        //   this.stepActive += 1;
        // }
        this.methodType = "info";
        this.dialogVisible = true;
        // this.$nextTick((e) => {
        //   this.$refs["proform"].resetFormParam();
        // });
      });
    },
    handleDialogOk3() {
      closeOrderApi(this.form3)
        .then(() => {
          this.$message.success("关闭成功");
          this.dialogVisible3 = false;
        })
        .finally(() => {
          this.$refs.ProTable.refresh();
        });
    },
    // 响应删除
    handleDelete(id) {
      this.dialogVisible3 = true;
      this.form3 = { needPay: false, id: id, repairPay: 0, visitPay: 0 };
    },
    handleChange(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
    /**
     * @description 工单详情-查看维修报告
     * TODO:查看申诉流程-维修报告待完善
     * @param id
     */
    handleReport(id) {
      this.formreport = {};
      getReportApi(id).then((res) => {
        this.dialogVisible2 = true;
        this.formreport = cloneDeep(res.data.repairReport);
        this.tableData1 = res.data?.replaceOrder?.replaceDetailList;
      });
    },
    /**
     * TODO：待完成
     * @description 查看申诉过程
     * @param id
     */
    handleAppeal(id) {
      this.$message.error("功能开发中！");
      // this.appealValue = true;
    },
    closeAppealDialog() {
      this.appealValue = false;
    },
  },
};
</script>
<style>
.el-checkbox {
  line-height: 40px;
}

.el-collapse {
  border: none;
}

.el-collapse-item__header {
  border: none;
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-item__content {
  padding: 0;
}

.el-collapse-item:last-child {
  margin: auto;
}
</style>
<style lang="scss" scoped>
::v-deep .el-upload--picture-card,
::v-deep .el-upload-list__item {
  width: 120px;
  height: 120px;
}

.sp-content {
  padding: 10px;
  width: 90%;
  margin: auto;
}
.steps-container {
  position: relative;
  .duration-labels {
    position: absolute;
    top: 0;
    bottom: 0;
    display: flex;
    width: calc(100% - 112px);
    pointer-events: none;
  }

  .duration-item {
    width: 20%;
    font-size: 12px;
    color: #409eff;
    text-align: center;
  }
}

/* 确保连接线在时间标签的下层 */
:deep(.el-step__line) {
  z-index: 0;
}

.dialog-content-box {
  position: relative;
  height: 100%;
  overflow: scroll;
  padding-bottom: 80px;
  .steps-box {
    position: absolute;
    width: 80%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    z-index: 2;
  }

  .content-fix {
    height: calc(100vh - 110px);
    overflow: auto;
  }

  .tit-box {
    width: 100%;
    padding: 5px 10px;
    color: #409eff;
    position: relative;
    margin: 20px auto;
    font-size: 16px;
    font-weight: 800;

    &::before {
      content: "";
      width: 5px;
      height: 20px;
      background: #409eff;
      display: inline-block;
      position: absolute;
      left: -1px;
      top: 4px;
    }
  }
}
.card-box {
  border-bottom: 5px solid #f1eeee;
  margin: 10px 0;
  padding: 0 20px;
}
.evaluation-container {
  background: #fff;
  border-radius: 8px;
  //box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  .evaluate-content {
    padding: 20px 0;

    .evaluate-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;

      i {
        color: #ffc107;
        margin-right: 8px;
        font-size: 20px;
      }

      span {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .evaluate-body {
      .evaluate-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .label {
          width: 80px;
          color: #606266;
          font-size: 14px;
        }

        &.time-item {
          color: #909399;
          font-size: 13px;
        }
      }

      .evaluate-content-box {
        background: #f8f9fa;
        border-radius: 4px;
        margin: 15px 0;

        .content-header {
          padding: 10px 15px;
          border-bottom: 1px solid #ebeef5;

          .label {
            color: #606266;
            font-size: 14px;
          }
        }

        .content-body {
          padding: 15px;
          min-height: 60px;
          color: #606266;
          font-size: 14px;
          line-height: 1.6;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }
  }
}

:deep(.el-rate) {
  margin-left: 8px;

  .el-rate__text {
    font-size: 13px;
  }
}
.imgs {
  height: 120px;
  margin: 10px;
  max-width: calc((100% - 80px) / 4);
}
.imgs1 {
  height: 120px;
  width: 120px;
  float: left;
}
.btn-box {
  position: fixed;
  bottom: 0;
  width: 70%;
  background: #fff;
  right: 0;
  padding: 20px;
}
</style>
