<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:49
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      :height="500"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleEdit(null, 'add')"
        >
          新增机器采购
        </el-button>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            v-auth="['@ums:manage:machinePurchase:view']"
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
          <el-button
            v-if="
              row.status?.value === 'WAIT_APPROVE' &&
              row.orderStatus?.value !== 'CLOSE'
            "
            v-auth="['@ums:manage:machinePurchase:audit']"
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'check')"
          >
            复核
          </el-button>
          <el-button
            v-if="
              row.status?.value === 'WAIT_RECEIVE' ||
              row.status?.value === 'RECEIVING'
            "
            icon="el-icon-circle-check"
            @click="confirmReceipt(row, 'edit')"
          >
            确认收货
          </el-button>
          <el-button
            v-if="row.status?.value === 'SUCCESS'"
            icon="el-icon-warning-outline"
            @click="confirmReceipt(row, 'info')"
          >
            收货详情
          </el-button>
          <el-button
            v-if="
              row.status?.value !== 'CLOSED' &&
              row.status?.value !== 'REJECT' &&
              (row.payStatus?.value === 'NO' || row.payStatus?.value === 'PART')
            "
            v-auth="['@ums:manage:machinePurchase:pay']"
            icon="el-icon-postcard"
            @click="confirmReceipt(row, 'audit')"
          >
            付款
          </el-button>
          <!--<el-button-->
          <!--  v-if="-->
          <!--    row.status === '1' &&-->
          <!--    !row.canClose &&-->
          <!--    !(-->
          <!--      row.orderStatus?.value === 'COMPLETE' ||-->
          <!--      row.orderStatus?.value === 'CLOSE'-->
          <!--    )-->
          <!--  "-->
          <!--  type="btn3"-->
          <!--  icon="el-icon-warning-outline"-->
          <!--  @click="returnGoods(row)"-->
          <!--&gt;-->
          <!--  退货-->
          <!--</el-button>-->
          <el-button
            v-if="row.status?.value === 'WAIT_APPROVE'"
            v-auth="['@ums:manage:machinePurchase:del']"
            type="danger"
            size="mini"
            icon="el-icon-circle-close"
            @click="handleCloseOrder(row.id)"
          >
            关闭
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 新增机器采购 -->
    <addMachinePurchase
      ref="addMachinePurchase"
      :purchase-code="purchaseCode"
      @refresh="refresh"
    />
    <!-- 机器收货确认 -->
    <MachineReceiving
      ref="machineReceiving"
      :purchase-code="purchaseCode"
      @refresh="refresh"
    />
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { closeMachinePurchaseApi, getMachinePurchaseApi } from "@/api/procure";
import { warehouseListApi } from "@/api/store";
import { Message } from "element-ui";
import { filterParam, filterParamRange } from "@/utils";

export default {
  name: "MachinePurchase",
  components: {
    addMachinePurchase: () => import("./cpns/addMachinePurchase.vue"),
    MachineReceiving: () => import("./cpns/machineReceiving.vue"),
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "purchaseCode",
          title: "采购申请单编号",
          isTable: true,
          minWidth: 140,
        },
        {
          dataIndex: "purchaseCode",
          title: "申请单编号",
          isSearch: true,
          clearable: true,
          placeholder: "采购申请单编号",
          valueType: "input",
        },
        {
          dataIndex: "warehouseName",
          title: "采购仓库",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "warehouseId",
          isSearch: true,
          title: "仓库名称",
          valueType: "select",
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
        },
        {
          dataIndex: "number",
          title: "计划采购数量",
          isTable: true,
          minWidth: 110,
        },
        {
          dataIndex: "amount",
          title: "采购金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "deliveryTime",
          title: "期望发货时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "arrivalTime",
          title: "期望到货时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "status",
          title: "确认状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [
            {
              label: "待确认",
              value: "WAIT_APPROVE",
            },
            {
              label: "待收货",
              value: "WAIT_RECEIVE",
            },
            {
              label: "收货中",
              value: "RECEIVING",
            },
            {
              label: "已完成",
              value: "SUCCESS",
            },
            {
              label: "驳回",
              value: "REJECT",
            },
            {
              label: "关闭",
              value: "CLOSED",
            },
          ],
        },
        {
          dataIndex: "createdBy",
          title: "采购发起人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
          minWidth: 100,
        },
        {
          dataIndex: "createdAt",
          title: "采购发起时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "payStatus",
          title: "支付状态",
          isTable: true,
          formatter: (row) => row.payStatus?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "未付款",
              value: "NO",
            },
            {
              label: "已付款",
              value: "YES",
            },
            {
              label: "部分付款",
              value: "PART",
            },
          ],
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          fixed: "right",
          width: 260,
        },
      ],
      tableData: [],
      purchaseCode: "12", // 没发现用途，解决报错问题
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      getMachinePurchaseApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        });
    },
    // 确认收货
    confirmReceipt(row, type) {
      this.$refs.machineReceiving.show(row, type);
    },
    // 查看、复核
    handleEdit(row, type) {
      this.$refs.addMachinePurchase.show(row, type);
    },
    handleCloseOrder(id) {
      this.$confirm("是否关闭当前机器采购申请单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        closeMachinePurchaseApi(id).then((res) => {
          Message.success("关闭成功");
          this.refresh();
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
