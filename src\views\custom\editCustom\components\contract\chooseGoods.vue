<!--
 * @Author: wskg
 * @Date: 2025-01-15 17:18:17
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:49
 * @Description: 购机赠送服务
 -->
<template>
  <div class="app-container">
    <ProDialog
      :value="dialogVisible"
      title="合约赠送物品选择"
      width="75%"
      top="5%"
      :no-footer="true"
      @cancel="handleDialogCancel"
    >
      <ProTable
        ref="ProTable"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="columns"
        :local-pagination="localPagination"
        :row-key="
          (row) => {
            return row.saleSkuId;
          }
        "
        :data="tableData"
        :query-param="queryParam"
        :height="380"
        :show-setting="false"
        @loadData="loadData"
      >
        <template #picsUrl="slotProps">
          <img
            style="max-width: 100px; max-height: 100px"
            :src="getPicsUrlImg(slotProps.row)"
          />
        </template>
        <template #saleAttrVals="slotProps">
          <span
            v-for="(item, index) in slotProps.row.saleAttrVals"
            :key="index"
            style="border: 1px solid #ddd"
          >
            {{ item.name }}: {{ item.val }}
          </span>
        </template>
        <template #saleStatus="slotProps">
          {{ slotProps.row.saleStatus === "ON_SALE" ? "已上架" : "未上架" }}
        </template>
        <template #actions="{ row }">
          <span class="fixed-width">
            <el-button icon="el-icon-circle-check" @click="handleSelect(row)">
              确定选择
            </el-button>
          </span>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { classifyListApi, itemSummaryListApi } from "@/api/goods";
import { dictTreeByCodeApi2 } from "@/api/user";

export default {
  name: "ChooseGoods",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "itemName",
          title: "商品名称",
          clearable: true,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "categoryId",
          title: "商品分类",
          isSearch: true,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
        },
        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
          clearable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          width: 300,
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          width: 120,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          width: 180,
          valueType: "input",
        },
        {
          dataIndex: "lastIds",
          title: "适用机型",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "productPartTypeList",
          title: "物品小类",
          width: 150,
          isTable: false,
          isSearch: true,
          clearable: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "action",
          title: "操作",
          tooltip: false,
          isTable: true,
          tableSlot: "actions",
          width: 100,
        },
      ],
      tableData: [],
    };
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.queryParam = {};
          this.refresh();
        });
      }
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      // requestParameters.saleStatus = "ON_SALE";
      itemSummaryListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
    },
    init() {
      classifyListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.columns[1].option = (res.data.rows || []).map((item) => ({
          label: item.name,
          value: item.id,
        }));
      });
    },
    handleSelect(row) {
      this.$emit("confirmSelect", row);
    },
    handleDialogCancel() {
      this.$emit("update:dialogVisible", false);
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
