<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-07 09:58:18
 * @Description: 抄表对账单
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="抄表记录" name="抄表记录" lazy>
        <!--<Exhibition />-->
        <MeterData />
      </el-tab-pane>
      <el-tab-pane label="对账单" name="对账单" lazy>
        <!--<Receipt />-->
        <ReceiptedData />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// import Exhibition from "@/views/order/components/exhibition.vue";
import MeterData from "@/views/order/components/meter.vue";
// import Receipt from "@/views/order/components/receipt.vue";
import ReceiptedData from "@/views/order/components/receipted.vue";
export default {
  name: "Reports",
  components: { MeterData, ReceiptedData },
  data() {
    return {
      activeName: "抄表记录",
    };
  },
};
</script>

<style scoped lang="scss"></style>
