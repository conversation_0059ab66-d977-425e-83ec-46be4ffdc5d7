include:
  project: xiehai/gitlab-ci
  file: /node/linux.yml

💪生产环境管理web💯:
  extends: .node:linux
  only:
    - master
  variables:
    NODE_REMOTE_HOST: BY_PROD
    NODE_REMOTE_BACKUP: "true"
    NODE_VERSION: 16.14.2
    NODE_REMOTE_ROOT: /data/application/web

本印后台系统前端部署(232):
  extends: .node:linux
  # schedule中的变量 用于区分触发任务
  only:
    - schedule
  variables:
    NODE_REMOTE_HOST: YYB_232
    NODE_REMOTE_BACKUP: "true"
    NODE_VERSION: 16.14.2
    NODE_REMOTE_ROOT: /data/project/benyin/web
