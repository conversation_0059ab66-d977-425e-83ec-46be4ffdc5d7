<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-19 13:53:58
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-01 12:06:25
 * @Description: 工程师 - 工程师位置信息
 -->

<template>
  <div class="view app-container">
    <div class="container">
      <!-- 全屏按钮 -->
      <div class="fullscreen-btn" @click="toggleFullScreen">
        <i :class="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"></i>
      </div>
      <scale-box>
        <div
          class="dashboard-container"
          :style="{ padding: isFullscreen ? '12px' : '0' }">
          <el-row :gutter="12">
            <el-col :span="8">
              <div class="left-panel">
                <div class="box">
                  <div class="tit">维修工单实时信息</div>
                  <div class="box-nav">
                    <div class="work-order-list">
                      <div class="work-order-header">
                        <span>客户名称</span>
                        <span style="flex: 1.2">机型</span>
                        <span style="flex: 1.2">报修时间</span>
                        <span>工单状态</span>
                        <span style="flex: 0.8">工程师</span>
                      </div>
                      <div ref="workOrderContent" class="work-order-content">
                        <div
                          class="scroll-wrapper"
                          @mouseenter="pauseScroll('workOrder')"
                          @mouseleave="resumeScroll('workOrder')">
                          <div class="scroll-container">
                            <template v-for="(item, index) in workOrderData">
                              <!--    :class="{
                                  overdue: isOverdue(item),
                                  'repair-overdue': isRepairOverdue(item),
                                }"-->
                              <div
                                :key="index"
                                class="work-order-item"
                                @click="handleWorkOrderClick(item)">
                                <span style="padding: 5px; flex-wrap: wrap">
                                  {{ item.customerName }}
                                </span>
                                <span style="flex: 1.2">
                                  {{ item.productInfo }}
                                </span>
                                <span style="flex: 1.2">
                                  {{ item?.createdAt.slice(5) }}
                                </span>
                                <span>
                                  {{ item.status?.label }}
                                </span>
                                <span style="flex: 0.8">
                                  {{ item?.engineerId?.name }}
                                </span>
                              </div>
                            </template>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="box">
                  <div class="tit" @click="setAbnormalDay">
                    异常工单信息
                    <i v-if="!isFullscreen" class="el-icon-setting"></i>
                  </div>
                  <div class="box-nav">
                    <div class="work-order-list">
                      <div class="work-order-header">
                        <span>客户名称</span>
                        <span>机型</span>
                        <span>报修时间</span>
                        <span>异常内容</span>
                      </div>
                      <div ref="abnormalContent" class="work-order-content">
                        <div
                          class="scroll-wrapper"
                          @mouseenter="pauseScroll('abnormal')"
                          @mouseleave="resumeScroll('abnormal')">
                          <div class="scroll-container">
                            <template v-for="item in abnormalOrderData">
                              <div
                                :key="item.code"
                                class="work-order-item"
                                @click="handleWorkOrderClick(item)">
                                <span style="justify-content: center">
                                  {{ item.customerName }}
                                </span>
                                <span>{{ item.productInfo }}</span>
                                <span>
                                  {{ item?.reportTime.slice(5) }}
                                </span>
                                <!--:class="{-->
                                <!--overdue: isOverdue(item),-->
                                <!--'repair-overdue': isRepairOverdue(item),-->
                                <!--}"-->

                                <span
                                  style="justify-content: center"
                                  :class="{
                                    overdue: item.type === 1,
                                    'repair-overdue': item.type === 2,
                                    'normal-overdue': item.type === 3,
                                  }">
                                  {{ item.content }}
                                </span>
                              </div>
                            </template>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="box" style="margin-bottom: 0">
                  <div class="tit" @click="setEvaluateDay">
                    客户评价
                    <i v-if="!isFullscreen" class="el-icon-setting"></i>
                  </div>
                  <div class="box-nav">
                    <div class="work-order-list">
                      <div class="work-order-header">
                        <span>客户名称</span>
                        <span>机型</span>
                        <span>评价星级</span>
                        <span>评价内容</span>
                      </div>
                      <div ref="evaluateContent" class="work-order-content">
                        <div
                          class="scroll-wrapper"
                          @mouseenter="pauseScroll('evaluate')"
                          @mouseleave="resumeScroll('evaluate')">
                          <div class="scroll-container">
                            <template v-for="item in customerEvaluate">
                              <div
                                :key="item.productId"
                                class="work-order-item"
                                @click="handleWorkOrderClick(item)">
                                <span>{{ item.customerName }}</span>
                                <span>{{ item.productInfo }}</span>
                                <span class="rate-content">
                                  <span class="rate-item">
                                    <span class="rate-label">专业能力:</span>
                                    <el-rate
                                      v-model="item.professional"
                                      disabled
                                      text-color="#ff9900"
                                      score-template="{value}"
                                      size="small" />
                                  </span>
                                  <span class="rate-item">
                                    <span class="rate-label">服务态度:</span>
                                    <el-rate
                                      v-model="item.service"
                                      disabled
                                      text-color="#ff9900"
                                      score-template="{value}"
                                      size="small" />
                                  </span>
                                </span>
                                <span style="text-wrap: auto">
                                  {{ item.content }}
                                </span>
                              </div>
                            </template>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="16">
              <div class="center-panel">
                <div class="box" style="height: calc(100% + 15px)">
                  <!-- 工程师位置信息 -->
                  <div class="box-nav" style="padding: 0">
                    <EngineerMap
                      :work-order="workOrderData"
                      @refreshWorkOrder="startPolling" />
                  </div>
                </div>
                <div class="box" style="margin-bottom: 0">
                  <div v-if="!isFullscreen" class="time-selector">
                    <el-select
                      v-model="timeRange"
                      placeholder="选择时间范围"
                      @change="handleTimeRangeChange">
                      <el-option
                        label="月"
                        :value="1"
                        :selected="true"></el-option>
                      <el-option label="季度" :value="2"></el-option>
                      <el-option label="年" :value="3"></el-option>
                    </el-select>
                  </div>
                  <div ref="order" class="order-container"></div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </scale-box>
    </div>
    <WorkOrderDetail ref="workOrderDetail" />
  </div>
</template>

<script>
import EngineerMap from "@/views/engineer/components/engineerMap.vue";
import WorkOrderDetail from "@/views/engineer/components/workOrderDetail.vue";
import * as echarts from "echarts";
import ScaleBox from "vue2-scale-box";
import {
  getBigViewEvaluationData,
  getBigViewExceptionData,
  getBigViewMachineData,
  getBigViewWorkOrderData,
} from "@/api";
import { getWorkOrderByPageApi } from "@/api/repair";
export default {
  name: "BigDataView",
  components: {
    ScaleBox,
    EngineerMap,
    WorkOrderDetail,
  },
  data() {
    return {
      servicePoint: {},
      currentPoint: {},
      machineNum: {},
      currentMachineNum: {},
      cityChart: null,
      isFullscreen: false,

      animationFrameIds: {
        workOrder: null,
        abnormal: null,
        evaluate: null,
      },
      scrollSpeeds: {
        workOrder: 0.5,
        abnormal: 0.5,
        evaluate: 0.5,
      },

      scrollPositions: {
        workOrder: 0,
        abnormal: 0,
        evaluate: 0,
      },
      selectedProvince: 510000,
      // 数据轮询定时器
      timer: null,
      pollingInterval: 180000, // 轮询间隔，30秒
      isPollingPaused: false,
      orderChart: null,
      brandChart: null,
      monthlyData: {
        months: [
          "1月",
          "2月",
          "3月",
          "4月",
          "5月",
          "6月",
          "7月",
          "8月",
          "9月",
          "10月",
          "11月",
          "12月",
        ],
        sales: [75, 125, 119, 135, 123, 83, 75, 117, 121, 125, 129, 83],
      }, // 工单月度统计
      workOrderData: [], // 维修工单实时信息
      abnormalOrderData: [], // 异常工单信息
      customerEvaluate: [], // 客户评价
      customer: [],
      map: null,
      markerLayer: null,
      timeRange: parseInt(localStorage.getItem("timeRange") || 1),
      abnormalDay: parseInt(localStorage.getItem("abnormalDay") || "3"),
      evaluateDay: parseInt(localStorage.getItem("evaluateDay") || "3"),
    };
  },
  watch: {
    isFullscreen: {
      handler() {
        setTimeout(() => {
          this.orderChart.resize();
        }, 300);
      },
    },
  },
  async mounted() {
    await this.startPolling();
    this.$nextTick(() => {
      this.startAutoScroll();
      // 事件委托绑定
      const typeMap = {
        workOrder: "workOrderData",
        abnormal: "abnormalOrderData",
        evaluate: "customerEvaluate",
      };
      ["workOrder", "abnormal", "evaluate"].forEach((type) => {
        const wrapper =
          this.$refs[`${type}Content`]?.querySelector(".scroll-wrapper");
        if (wrapper && !wrapper._eventDelegated) {
          wrapper.addEventListener(
            "click",
            this.handleScrollWrapperClick.bind(this, typeMap[type])
          );
          wrapper._eventDelegated = true;
        }
      });
    });
  },
  beforeDestroy() {
    if (this.orderChart) {
      this.orderChart.dispose();
    }
    this.stopPolling();
    this.stopAutoScroll();
    window.removeEventListener("resize", () => {
      this.orderChart.resize();
    });
    // 清理所有动画帧
    Object.keys(this.animationFrameIds).forEach((type) => {
      if (this.animationFrameIds[type]) {
        cancelAnimationFrame(this.animationFrameIds[type]);
      }
    });
  },
  methods: {
    // 开始轮询
    async startPolling() {
      this.stopPolling();
      if (!this.isPollingPaused) {
        await this.getInitData(); // 立即执行一次
        await this.init();
        this.timer = setInterval(() => {
          this.getInitData();
        }, this.pollingInterval);
      }
    },

    // 停止轮询
    stopPolling() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    // 暂停轮询
    pausePolling() {
      this.isPollingPaused = true;
      this.stopPolling();
    },

    // 恢复轮询
    resumePolling() {
      this.isPollingPaused = false;
      this.startPolling();
    },

    init() {
      this.initOrderChart();
      document.addEventListener(
        "fullscreenchange",
        this.handleFullscreenChange
      );
    },
    initOrderChart() {
      if (this.orderChart) {
        this.orderChart.dispose();
      }
      this.orderChart = echarts.init(this.$refs.order);

      const option = {
        title: {
          text: "工单统计",
          textStyle: {
            color: "#1c1c1e",
            fontSize: 17,
            fontWeight: "600",
          },
          left: 10,
          top: 5,
          padding: [0, 0, 20, 0],
        },
        tooltip: {
          trigger: "axis",
          backgroundColor: "rgba(255, 255, 255, 0.9)",
          borderColor: "#ccc",
          borderWidth: 1,
          textStyle: {
            color: "#333",
            fontSize: 13,
          },
          formatter: function (params) {
            return `${params[0].name}<br/>${params[0].seriesName}：${params[0].value}`;
          },
          axisPointer: {
            type: "line",
            lineStyle: {
              // color: "#a0a0a0",
              width: 1,
              type: "dashed",
            },
          },
        },
        grid: {
          left: "1%",
          right: "3%",
          bottom: "2%",
          top: "30%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.monthlyData.months,
          axisLabel: {
            color: "#666",
            fontSize: 12,
            // rotate: 45,
            interval: 0,
            margin: 15,
            // padding: [0, 0, 0, 30],
          },
          axisLine: {
            lineStyle: {
              color: "#ccc",
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          name: "次",
          // nameGap: 25,
          nameTextStyle: {
            color: "#999",
            fontSize: 12,
            // padding: [0, 0, 0, 30],
          },
          axisLabel: {
            color: "#666",
            fontSize: 12,
            margin: 10,
            formatter: "{value}",
          },
          axisLine: {
            lineStyle: {
              color: "#ccc",
            },
          },
          splitLine: {
            lineStyle: {
              color: "#e0e0e0",
              type: "dashed",
            },
          },
        },
        series: [
          {
            name: "维修次数",
            type: "line",
            data: this.monthlyData.sales,
            smooth: true,
            symbol: "circle",
            symbolSize: 8,
            itemStyle: {
              color: "#007aff",
              shadowColor: "rgba(0, 122, 255, 0.5)",
              shadowBlur: 10,
            },
            lineStyle: {
              width: 3,
              color: "#007aff",
              shadowColor: "rgba(0, 122, 255, 0.4)",
              shadowBlur: 10,
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(0, 122, 255, 0.4)",
                },
                {
                  offset: 1,
                  color: "rgba(0, 122, 255, 0.05)",
                },
              ]),
            },
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
              color: "#333",
              fontSize: 12,
              fontWeight: "600",
            },
          },
        ],
      };
      this.orderChart.setOption(option);
      setTimeout(() => {
        this.orderChart.resize();
      }, 500);

      window.addEventListener("resize", () => {
        this.orderChart.resize();
      });
    },
    async getInitData() {
      try {
        // 获取维修工单列表
        await this.getWorkOrders();
        // 获取异常工单数据
        await this.getAbnormalOrders();
        await this.getCustomerEvaluations();
        await this.getWorkOrderCount();
      } catch (error) {
        console.log(error.message);
      }
    },
    async handleTimeRangeChange(val) {
      localStorage.setItem("timeRange", val.toString());
      this.timeRange = val;
      try {
        // 暂停轮询
        this.pausePolling();
        const result = await getBigViewWorkOrderData(this.timeRange);
        if (result.code === 200) {
          this.monthlyData = result.data;
          this.initOrderChart();
          // 恢复轮询
          this.resumePolling();
        }
      } catch (e) {
        console.log(e.message);
      }
    },
    setAbnormalDay() {
      this.$prompt("请输入返回异常工单数据的天数", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputValue: this.abnormalDay,
        inputPattern: /^[1-9]\d*$/, // 正整数校验
        inputErrorMessage: "请输入有效的正整数",
        type: "number",
        closeOnClickModal: false,
      }).then(async ({ value }) => {
        const days = parseInt(value);
        if (days <= 0) {
          this.$message.error("天数必须大于0");
          return;
        }
        localStorage.setItem("abnormalDay", days.toString());
        this.abnormalDay = days;
        this.$message.success(`已设置异常工单显示天数为 ${days} 天`);
        await this.getAbnormalOrders();
        // 重新初始化滚动
        this.reinitializeScroll("abnormal");
      });
    },
    setEvaluateDay() {
      this.$prompt("请输入返回客户评价数据的天数", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputValue: this.evaluateDay,
        inputPattern: /^[1-9]\d*$/, // 正整数校验
        inputErrorMessage: "请输入有效的正整数",
        type: "number",
        closeOnClickModal: false,
      }).then(async ({ value }) => {
        const days = parseInt(value);
        if (days <= 0) {
          this.$message.error("天数必须大于0");
          return;
        }
        localStorage.setItem("evaluateDay", days.toString());
        this.evaluateDay = days;
        this.$message.success(`已设置客户评价显示天数为 ${days} 天`);

        await this.getCustomerEvaluations();
        // 重新初始化滚动
        this.reinitializeScroll("evaluate");
      });
    },

    startWorkOrderScroll() {
      const container =
        this.$refs.workOrderContent?.querySelector(".scroll-wrapper");
      if (!container) return;

      const content = container.querySelector(".scroll-container");
      if (!content) return;
      if (content.offsetHeight <= container.clientHeight) {
        return;
      }
      const clone = content.cloneNode(true);
      container.appendChild(clone);

      let accumulatedScroll = 0;
      const animate = () => {
        accumulatedScroll += this.scrollSpeeds.workOrder;
        if (accumulatedScroll >= 1) {
          container.scrollTop += Math.floor(accumulatedScroll);
          accumulatedScroll = 0;
        }

        // 当滚动到克隆内容的末尾时，重置到原始内容的开始位置
        if (container.scrollTop >= content.offsetHeight) {
          container.scrollTop = 0;
        }

        this.animationFrameIds.workOrder = requestAnimationFrame(animate);
      };
      animate();
    },

    startAbnormalScroll() {
      const container =
        this.$refs.abnormalContent?.querySelector(".scroll-wrapper");
      if (!container) return;

      const content = container.querySelector(".scroll-container");
      if (!content) return;
      if (content.offsetHeight <= container.clientHeight) {
        return;
      }
      const clone = content.cloneNode(true);
      container.appendChild(clone);

      let accumulatedScroll = 0;
      const animate = () => {
        accumulatedScroll += this.scrollSpeeds.abnormal;
        if (accumulatedScroll >= 1) {
          container.scrollTop += Math.floor(accumulatedScroll);
          accumulatedScroll = 0;
        }

        // 当滚动到克隆内容的末尾时，重置到原始内容的开始位置
        if (container.scrollTop >= content.offsetHeight) {
          container.scrollTop = 0;
        }

        this.animationFrameIds.abnormal = requestAnimationFrame(animate);
      };
      animate();
    },

    startEvaluateScroll() {
      const container =
        this.$refs.evaluateContent?.querySelector(".scroll-wrapper");
      if (!container) return;

      const content = container.querySelector(".scroll-container");
      if (!content) return;

      if (content.offsetHeight <= container.clientHeight) {
        return;
      }

      const clone = content.cloneNode(true);
      container.appendChild(clone);

      let accumulatedScroll = 0;
      const animate = () => {
        accumulatedScroll += this.scrollSpeeds.evaluate;
        if (accumulatedScroll >= 1) {
          container.scrollTop += Math.floor(accumulatedScroll);
          accumulatedScroll = 0;
        }

        // 当滚动到克隆内容的末尾时，重置到原始内容的开始位置
        if (container.scrollTop >= content.offsetHeight) {
          container.scrollTop = 0;
        }

        this.animationFrameIds.evaluate = requestAnimationFrame(animate);
      };
      animate();
    },
    pauseScroll(type) {
      if (this.animationFrameIds[type]) {
        cancelAnimationFrame(this.animationFrameIds[type]);
        this.animationFrameIds[type] = null;
        const container =
          this.$refs[`${type}Content`]?.querySelector(".scroll-wrapper");
        if (container) {
          this.scrollPositions[type] = container.scrollTop;
        }
      }
    },

    resumeScroll(type) {
      if (!this.animationFrameIds[type]) {
        const container =
          this.$refs[`${type}Content`]?.querySelector(".scroll-wrapper");
        if (container) {
          // 检查是否需要滚动，如果不需要则清理克隆内容
          if (!this.shouldScroll(container)) {
            // 清理可能存在的克隆内容
            const content = container.querySelector(".scroll-container");
            const clone = container.querySelector(
              ".scroll-container:last-child"
            );
            if (clone && clone !== content) {
              clone.remove();
            }
            return;
          }

          container.scrollTop = this.scrollPositions[type];
          // 检查是否需要滚动
          if (this.shouldScroll(container)) {
            switch (type) {
              case "workOrder":
                this.startWorkOrderScroll();
                break;
              case "abnormal":
                this.startAbnormalScroll();
                break;
              case "evaluate":
                this.startEvaluateScroll();
                break;
            }
          }
        }
      }
    },

    shouldScroll(container) {
      if (!container) return false;
      const content = container.querySelector(".scroll-container");
      if (!content) return false;

      return content.offsetHeight > container.clientHeight;
    },

    startAutoScroll() {
      const workOrderContainer =
        this.$refs.workOrderContent?.querySelector(".scroll-wrapper");
      const abnormalContainer =
        this.$refs.abnormalContent?.querySelector(".scroll-wrapper");
      const evaluateContainer =
        this.$refs.evaluateContent?.querySelector(".scroll-wrapper");

      if (this.shouldScroll(workOrderContainer)) {
        this.startWorkOrderScroll();
      }
      if (this.shouldScroll(abnormalContainer)) {
        this.startAbnormalScroll();
      }
      if (this.shouldScroll(evaluateContainer)) {
        this.startEvaluateScroll();
      }
    },

    // 停止指定类型的滚动
    stopScroll(type) {
      if (this.animationFrameIds[type]) {
        cancelAnimationFrame(this.animationFrameIds[type]);
        this.animationFrameIds[type] = null;
      }

      // 清理克隆内容
      const container =
        this.$refs[`${type}Content`]?.querySelector(".scroll-wrapper");
      if (container) {
        const content = container.querySelector(".scroll-container");
        const clone = container.querySelector(".scroll-container:last-child");
        if (clone && clone !== content) {
          clone.remove();
        }
        // 重置滚动位置
        container.scrollTop = 0;
      }
    },

    // 重新初始化滚动
    reinitializeScroll(type) {
      // 先停止当前滚动
      this.stopScroll(type);

      // 等待DOM更新后重新检查是否需要滚动
      this.$nextTick(() => {
        const container =
          this.$refs[`${type}Content`]?.querySelector(".scroll-wrapper");
        if (container) {
          // 如果不需要滚动，确保清理克隆内容
          if (!this.shouldScroll(container)) {
            const content = container.querySelector(".scroll-container");
            const clone = container.querySelector(
              ".scroll-container:last-child"
            );
            if (clone && clone !== content) {
              clone.remove();
            }
          } else {
            // 需要滚动，启动相应的滚动方法
            switch (type) {
              case "workOrder":
                this.startWorkOrderScroll();
                break;
              case "abnormal":
                this.startAbnormalScroll();
                break;
              case "evaluate":
                this.startEvaluateScroll();
                break;
            }
          }
        }
      });
    },
    stopAutoScroll() {
      // 清理所有滚动定时器
      Object.keys(this.animationFrameIds).forEach((type) => {
        this.stopScroll(type);
      });
    },
    /**
     * @description 处理滚动容器点击事件: clone出来的数据会导致绑定点击事件失效，所以采用事件委托
     * @param type
     * @param event 事件源
     */
    handleScrollWrapperClick(type, event) {
      // 找到点击的工单
      let itemEl = event.target;
      while (itemEl && !itemEl.classList.contains("work-order-item")) {
        itemEl = itemEl.parentElement;
      }
      if (!itemEl) return;
      // 获取索引
      const items = Array.from(itemEl.parentElement.children).filter((el) =>
        el.classList.contains("work-order-item")
      );
      const index = items.indexOf(itemEl) % this[type].length;
      const data = this[type][index];
      if (data) {
        this.handleWorkOrderClick(data);
      }
    },
    async handleProvinceChange() {
      try {
        // 暂停轮询
        this.pausePolling();
        const result = await getBigViewMachineData(this.selectedProvince);
        if (result.code === 200) {
          this.machineData = result.data;
          // 恢复轮询
          this.resumePolling();
        }
      } catch (e) {
        console.log(e.message);
      }
    },
    // 获取工单列表数据
    async getWorkOrders() {
      try {
        const result = await getWorkOrderByPageApi({
          status: [
            "pending_orders",
            "engineer_receive",
            "engineer_departure",
            "engineer_arrive",
            "wait_confirmed_report",
          ],
          pageNumber: 1,
          pageSize: 100,
        });
        if (result.code === 200) {
          this.workOrderData = result.data.rows;
        }
      } catch (error) {
        this.workOrderData = [];
      }
    },
    // 获取异常工单数据
    async getAbnormalOrders() {
      try {
        const result = await getBigViewExceptionData(this.abnormalDay);
        if (result.code === 200) {
          this.abnormalOrderData = result.data;
        }
      } catch (e) {
        this.abnormalOrderData = [];
      }
    },
    // 获取客户评价数据
    async getCustomerEvaluations() {
      try {
        const result = await getBigViewEvaluationData(this.evaluateDay);
        if (result.code === 200) {
          this.customerEvaluate = result.data;
        }
      } catch (e) {
        this.customerEvaluate = [];
      }
    },
    // 获取工单数量统计
    async getWorkOrderCount() {
      try {
        const salesData = await getBigViewWorkOrderData(this.timeRange);
        this.monthlyData = salesData.data;
        // 更新各个图表的数据
        if (this.orderChart) {
          this.orderChart.setOption({
            xAxis: {
              data: this.monthlyData.months,
              animation: true,
              animationDuration: 1000,
            },
            series: [
              {
                data: this.monthlyData.sales,
                animation: true,
                animationDuration: 1000,
              },
            ],
          });
        }
      } catch (e) {
        this.monthlyData = {
          months: [],
          sales: [],
        };
      }
    },
    // 查看工单明细
    handleWorkOrderClick(item) {
      if (!item.code) {
        return;
      }
      this.$refs.workOrderDetail.show(item.code);
    },
    handleFullscreenChange() {
      this.isFullscreen = !!document.fullscreenElement;
    },
    toggleFullScreen() {
      const container = document.querySelector(".dashboard-container");

      if (!document.fullscreenElement) {
        container.requestFullscreen().catch((err) => {
          console.error(
            `Error attempting to enable fullscreen: ${err.message}`
          );
        });
        this.isFullscreen = true;
        document.body.classList.add("fullscreen");
      } else {
        document.exitFullscreen();
        this.isFullscreen = false;
        document.body.classList.remove("fullscreen");
      }
      this.$nextTick(() => {
        this.orderChart.resize();
      });
    },
    // 报修 -> 工程师到达超过4小时算超时
    isOverdue(item) {
      const parseDate = (str) =>
        new Date(str.replace("年", "-").replace("月", "-").replace("日", ""));

      const start = parseDate(item.createdAt);
      const end = item.actualArriveTime
        ? parseDate(item.actualArriveTime)
        : new Date();

      const hours = this.getEffectiveHours(start, end);
      return hours > 4;
    },
    // 到达 -> 提交报告时间超过4小时算超时
    isRepairOverdue(item) {
      const parseDate = (str) =>
        new Date(str.replace("年", "-").replace("月", "-").replace("日", ""));

      if (!item.actualArriveTime) return false;

      const start = parseDate(item.actualArriveTime);
      const end = item.sendReportTime
        ? parseDate(item.sendReportTime)
        : new Date();

      const hours = this.getEffectiveHours(start, end);
      return hours > 4;
    },
    // 计算有效工作时间（仅工作日 8:30~18:00）
    getEffectiveHours(start, end) {
      if (
        !(start instanceof Date) ||
        !(end instanceof Date) ||
        isNaN(start) ||
        isNaN(end)
      ) {
        console.error("无效时间参数");
        return 0;
      }

      const WORK_START_HOUR = 8;
      const WORK_START_MIN = 30;
      const WORK_END_HOUR = 18;
      let totalHours = 0;

      const current = new Date(start);

      while (current < end) {
        const day = current.getDay();

        // 排除周日（可加条件 day !== 6 排除周六）
        if (day !== 0) {
          const workStart = new Date(current);
          workStart.setHours(WORK_START_HOUR, WORK_START_MIN, 0, 0);

          const workEnd = new Date(current);
          workEnd.setHours(WORK_END_HOUR, 0, 0, 0);

          const effectiveStart = Math.max(
            current.getTime(),
            workStart.getTime()
          );
          const effectiveEnd = Math.min(end.getTime(), workEnd.getTime());

          if (effectiveStart < effectiveEnd) {
            totalHours += (effectiveEnd - effectiveStart) / 36e5; // 1000*60*60
          }
        }
        // 下一天 00:00
        current.setDate(current.getDate() + 1);
        current.setHours(0, 0, 0, 0);
      }
      return totalHours;
    },
  },
};
</script>
<style scoped lang="scss">
.container {
  position: relative;
  height: calc(100vh - 130px);
  min-height: 100%;
  background-color: #f2f2f7;

  ::v-deep .vue2-scale-box {
    width: 100% !important;
    height: 100% !important;
    position: unset !important;
    transform: none !important;
    transition: width 0.3s, height 0.3s;
  }
  .fullscreen-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    cursor: pointer;
    padding: 10px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
  .fullscreen .scale-box {
    width: 1920px;
    height: 1080px;
  }
}
li {
  list-style-type: none;
}
.box {
  position: relative;
  border-radius: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  //box-shadow: 0 0 2.1px rgba(0, 0, 0, 0.022), 0 0 3.6px rgba(0, 0, 0, 0.048),
  //  0 0 4.7px rgba(0, 0, 0, 0.073), 0 0 7px rgba(0, 0, 0, 0.09);
  box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.1);

  &:before {
    content: "";
    width: 100%;
    height: 1px;
    position: absolute;
    left: 0;
    bottom: -1px;
    top: -1px;
    opacity: 0.6;
  }
  .tit {
    padding: 10px 20px;
    border-bottom: 1px solid #e5e5ea;
    font-size: 17px;
    font-weight: 600;
    color: #1c1c1e;
    background-color: #fafafa;
    text-align: center;
    cursor: pointer;
    user-select: none;

    position: relative;
    &:before {
      position: absolute;
      content: "";
      width: 6px;
      height: 6px;
      border-radius: 10px;
      left: 10px;
      top: 18px;
    }
    &:after {
      width: 100%;
      height: 1px;
      content: "";
      position: absolute;
      left: 0;
      bottom: -1px;
      opacity: 0.6;
    }
  }
  .box-nav {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 5px;
    overflow: hidden;
  }
}

%panel-base {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dashboard-container {
  width: 100%;
  height: 100%;
  //min-height: 100vh;
  //padding: 10px;
  text-align: left;
  //background-color: #f2f2f7; // 同背景
  background-color: #f6f8f9;

  color: #1c1c1e;
  font-size: 16px;

  .work-order-list {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .work-order-header {
      display: flex;
      padding: 8px 6px;
      background: #fafafa;
      border-bottom: 1px solid #e5e5ea;

      span {
        flex: 1;
        text-align: center;
        color: #3a3a3c;
        font-size: 12px;

        &.rate-header {
          display: flex;
          flex-direction: column;
          gap: 2px;
          div {
            font-size: 11px;
            line-height: 1.1;
          }
        }
      }
    }

    .work-order-content {
      flex: 1;
      overflow: hidden;
      position: relative;
      height: 100%;

      .scroll-wrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-track {
          background-color: transparent;
        }
      }

      .scroll-container {
        width: 100%;
        min-height: 100%;

        .work-order-item {
          display: flex;
          padding: 8px 6px;
          border-bottom: 1px solid #e5e5ea;
          cursor: pointer;
          will-change: transform;

          span {
            display: flex;
            flex: 1;
            text-align: center;
            justify-content: center;
            font-size: 12px;
            color: #636366;
            white-space: wrap;
            overflow: hidden;
            text-overflow: ellipsis;
            align-items: center;

            &.rate-content {
              display: flex;
              flex: 1.2;
              flex-direction: column;
              gap: 2px;
              padding: 0 3px;

              .rate-item {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 4px;

                .rate-label {
                  flex: none;
                  width: auto;
                  font-size: 11px;
                  color: #8e8e93;
                  white-space: nowrap;
                }
              }

              ::v-deep .el-rate {
                height: 14px;
                line-height: 1;
                display: inline-flex;
                justify-content: flex-start;
                align-items: center;

                .el-rate__icon {
                  font-size: 12px;
                  margin-right: 1px;
                  color: #ffcc00;
                }
              }
            }
          }

          &:hover {
            background: #f9f9f9;
          }
        }

        .work-order-item {
          .normal-overdue {
            color: #e8b143;
            box-sizing: border-box;
            transition: all 0.3s ease;
          }
          .repair-overdue {
            position: relative;
            color: #faad14;
            box-sizing: border-box;
            transition: all 0.3s ease;
          }
          .overdue {
            box-sizing: border-box;
            position: relative;
            color: #ff4d4f;
          }
        }
      }
    }
  }

  ::v-deep .el-row {
    height: 100%;
  }

  ::v-deep .el-col {
    height: 100%;
  }
  .left-panel,
  .center-panel,
  .right-panel {
    @extend %panel-base;
  }
  .left-panel {
    .box {
      &:nth-child(1) {
        flex: 4;
        min-height: 0;
      }
      &:nth-child(2) {
        flex: 2.85;
        min-height: 0;
      }
      &:nth-child(3) {
        flex: 3;
        min-height: 0;
      }
    }
    .city-container {
      width: 100%;
      height: 100%;
    }
  }
  .center-panel {
    position: relative;
    .box {
      &:first-child {
        flex: 7;
        min-height: 0;
      }

      &:last-child {
        flex: 3;
        min-height: 0;
      }
    }
    ::v-deep .map-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      line-height: 0;
      .el-input {
        input {
          background: transparent;
          border: 1px solid #e5e5ea;
          color: #1c1c1e;
        }
      }
    }
    ::v-deep .time-selector {
      position: absolute;
      right: 0;
      top: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      line-height: 0;
      z-index: 10;
      .el-input {
        input {
          background: transparent;
          border: 1px solid #e5e5ea;
          color: #1c1c1e;
        }
      }
    }

    .map-container {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      pointer-events: auto;
      background: #ffffff;
      position: relative;
    }
    .order-container {
      width: 100%;
      height: 100%;
      min-width: 100%;
      padding: 10px;
      box-sizing: border-box;
      background: #ffffff;
    }
  }

  .right-panel {
    .box {
      &:nth-child(1) {
        flex: 3;
        min-height: 0;
      }
      &:nth-child(2) {
        flex: 3;
        min-height: 0;
      }
      &:nth-child(3) {
        flex: 4;
        min-height: 0;
      }
    }
    .machine-container {
      width: 100%;
      height: 100%;
      background: #ffffff;
    }
    .brand-container {
      width: 100%;
      height: 100%;
      background: #ffffff;
    }
  }
}

.map-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  pointer-events: auto;
  background: #f2f2f7;
  position: relative;
}
</style>
