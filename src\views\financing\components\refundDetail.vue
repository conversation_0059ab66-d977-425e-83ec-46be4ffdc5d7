<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-29 17:17:03
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-29 17:37:18
 * @Description: 退款明细
 -->
<template>
  <ProDialog
    :value="visibleDrawer"
    title="订单退款明细"
    no-footer
    @cancel="handleCloseDrawer"
  >
    <ProTable
      ref="ProTable"
      :show-search="false"
      :show-loading="false"
      :show-pagination="false"
      :show-setting="false"
      :columns="columns"
      :data="tableData"
      :height="350"
    ></ProTable>
  </ProDialog>
</template>

<script>
import { getRefundListApi } from "@/api/pay";

export default {
  name: "RefundDetail",
  data() {
    return {
      visibleDrawer: false,
      columns: [
        {
          dataIndex: "tradeOrderNumber",
          title: "订单编号",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "tradeOrderOrigin",
          title: "交易来源",
          isTable: true,
          formatter: (row) => row.tradeOrderOrigin?.label,
        },
        {
          dataIndex: "amount",
          title: "退款金额",
          isTable: true,
        },
        {
          dataIndex: "createdAt",
          title: "退款时间",
          isTable: true,
        },
        {
          dataIndex: "status",
          title: "退款状态",
          isTable: true,
          formatter: (row) => row.status?.label,
        },
      ],
      tableData: [],
    };
  },
  methods: {
    show(id) {
      getRefundListApi(id)
        .then((res) => {
          this.tableData = res.data;
        })
        .finally(() => {
          this.visibleDrawer = true;
        });
    },
    handleCloseDrawer() {
      this.visibleDrawer = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
