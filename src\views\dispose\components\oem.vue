<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-30 14:01:32
 * @Description: 零件列表
 -->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :layout="{ labelWidth: '160px' }"
      :columns="columns"
      :local-pagination="localPagination"
      :data="tableData"
      :height="400"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.productIdName"
          filterable
          clearable
          :options="options"
          style="width: 100%"
          collapse-tags
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>

      <template #brand="slotProps">
        <el-popover
          placement="bottom"
          title="当前设备关联产品树"
          width="400"
          trigger="click"
        >
          <div style="margin: 20px">
            <el-tree
              :data="deviceProductTree"
              :props="{
                children: 'children',
                label: 'name',
              }"
              default-expand-all
            ></el-tree>
          </div>

          <el-button slot="reference" @click="getBrand(slotProps.row.id)">
            查看品牌
          </el-button>
        </el-popover>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            选择
          </el-button>
        </span>
      </template>
    </ProTable>
  </div>
</template>
<script>
import { partListApi, productAllApi, partProductTreeApi } from "@/api/dispose";

export default {
  name: "Oem",
  components: {},
  mixins: [],
  props: {
    showDialog: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 列表
      options: [],
      deviceProductTree: [],
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      queryParam: {
        lastIds: [],
      },
      columns: [
        // {
        //   dataIndex: "fullIdPath",
        //   isSearch: true,
        //   clearable: true,
        //   searchSlot: "fullIdPath",
        //   title: "品牌名称",
        // },

        // {
        //   dataIndex: "brand",
        //   title: "产品树",
        //   isTable: true,
        //   tableSlot: "brand",
        // },

        {
          dataIndex: "oemNumber",
          title: "原厂零件编号（OEM）",
          isTable: true,
          isSearch: true,
          clearable: true,
          span: 4,
          valueType: "input",
          searchSpan: 8,
        },
        {
          dataIndex: "ch",
          title: "零件中/英名称",
          isSearch: true,
          clearable: true,
          span: 4,
          valueType: "input",
          searchSpan: 8,
        },
        {
          dataIndex: "ch",
          title: "零件中文名称",
          isTable: true,
        },
        {
          dataIndex: "en",
          title: "零件英文名称",
          isTable: true,
        },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
          formatter: (row) => row.type.label,
        },
        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          fixed: "right",
          tableSlot: "actions",
          width: 120,
        },
      ],
    };
  },
  // watch: {
  //   showDialog: {
  //     handler(val) {
  //       if (val) {
  //         this.queryParam = {};
  //         this.localPagination = {
  //           pageNumber: 1,
  //           pageSize: 10,
  //           total: 0,
  //         };
  //         this.$nextTick(() => {
  //           this.$refs.ProTable.$refs.ProElTable.doLayout();
  //         });
  //       }
  //     },
  //   },
  // },
  mounted() {
    productAllApi().then((res) => {
      this.options = res.data.rows;
      this.queryParam = {};
      this.$refs.ProTable.refresh();
    });
  },
  methods: {
    getBrand(id) {
      partProductTreeApi(id).then((res) => {
        this.deviceProductTree = res.data;
      });
    },
    handleSelect(item) {
      this.queryParam.lastIds = [];
      item.map((el) => {
        this.queryParam.lastIds.push(el[el.length - 1]);
      });
    },
    handleChange(item) {
      this.$set(this.form, "productIds", []);
      // console.log(this.$refs.ProductIds.getCheckedNodes(true))
      item.map((el) => {
        this.form.productIds.push(el[el.length - 1]);
      });
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      partListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },

    handleInfo(row) {
      if (!row.ch) {
        this.$message.error(
          "该零件没有中文名称，请前往设备和零件-零件列表将名称补全！"
        );
        return;
      }
      this.$emit("chooseOem", row);
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
