import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { NavBar, Skeleton } from 'antd-mobile';
import { 
  UnorderedListOutline,
  RightOutline,
} from 'antd-mobile-icons';
import { usePublicConfig } from '@/hooks/useWebsiteApi';
import { useWebsiteMenus } from '@/hooks/useWebsiteMenus';

interface MenuItemDef {
  key: string;
  label: string;
  path: string;
  icon?: React.ReactNode;
}

// Reusable component for consistent menu item styling
const MenuItemRow: React.FC<{
  label: string;
  icon: React.ReactNode;
  onClick: () => void;
  className?: string;
}> = ({ label, icon, onClick, className = '' }) => (
  <div
    onClick={onClick}
    className={`flex items-center justify-between cursor-pointer py-3 text-base ${className}`}
  >
    <span className="font-medium text-gray-900 whitespace-nowrap truncate">{label}</span>
    {icon}
  </div>
);

/**
 * 企业官网移动端头部组件
 * 层级设置：
 * - NavBar: z-index 9999 (最高层级，确保在地图控件之上)
 * - 下拉菜单: z-index 9998 (仅次于NavBar)
 */
export function MobileWebsiteHeader() {
  const navigate = useNavigate();
  const location = useLocation();
  const { data: config } = usePublicConfig();
  const [menuVisible, setMenuVisible] = useState(false);
  const [menuWidth, setMenuWidth] = useState<number | undefined>();

  // 动态菜单
  const { data: menuItems = [], isLoading: menuLoading } = useWebsiteMenus();

  // 获取当前页面标题
  const getCurrentTitle = () => {
    const currentPath = location.pathname;
    const activeItem = menuItems.find(item => 
      item.path === currentPath || (item.path !== '/' && currentPath.startsWith(item.path))
    );
    if (activeItem) return activeItem.label;
    // 若在根路径，使用第一项菜单名称作为标题
    if (currentPath === '/' && menuItems.length > 0) {
      return menuItems[0].label;
    }
    return config?.siteTitle || '复印机维修';
  };

  // 处理菜单项点击
  const handleMenuClick = (path: string) => {
    navigate(path);
    setMenuVisible(false);
  };

  // 渲染Logo
  const renderLogo = () => (
    <div 
      className="flex items-center space-x-2"
      onClick={() => navigate('/')}
    >
      {config?.headerLogoUrl?.trim() ? (
        <img
          src={config.headerLogoUrl.trim()}
          alt="logo"
          className="w-8 h-8 object-contain cursor-pointer"
          onError={(e) => (e.currentTarget.style.display = 'none')}
        />
      ) : (
        <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
          <span className="text-white font-bold text-sm">
            {(config?.siteTitle)?.charAt(0).toUpperCase() || 'F'}
          </span>
        </div>
      )}
      <span
        className="text-lg font-semibold text-gray-900 truncate"
        style={{ maxWidth: menuWidth ? `calc(100vw - ${menuWidth + 80}px)` : undefined }}
      >
        {config?.siteTitle || '复印机维修'}
      </span>
    </div>
  );

  // 在菜单数据就绪时预计算宽度（页面加载阶段）
  useEffect(() => {
    if (menuItems.length === 0) return;
    // 使用 canvas 测量文本宽度
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    ctx.font = '16px/1.5 "Helvetica Neue", Helvetica, Arial, sans-serif';
    let max = 0;
    menuItems.forEach(item => {
      const metrics = ctx.measureText(item.label);
      const width = metrics.width;
      if (width > max) max = width;
    });
    // 额外加上左右内边距（24px）和图标区域（24px）
    const calcWidth = Math.ceil(max + 48);
    setMenuWidth(calcWidth);
  }, [menuItems]);

  return (
    <div className="relative">
      {/* 移动端导航栏 - 最高层级，确保在地图控件之上 */}
      <NavBar
        className="mobile-nav-bar-container fixed top-0 left-0 right-0 bg-white border-b border-gray-200 shadow-sm"
        style={{ zIndex: 9999 }} // 最高层级，确保在地图控件之上
        back={null}
        left={renderLogo()}
        right={(
          <div style={{ width: menuWidth ? `${menuWidth}px` : undefined, marginRight: '-12px' }}>
            <MenuItemRow
              label={getCurrentTitle()}
              icon={<UnorderedListOutline fontSize={18} />}
              onClick={() => setMenuVisible(prev => !prev)}
              className="px-3"
            />
          </div>
        )}
      >
        {/* 空标题占位 */}
      </NavBar>

      {/* 折叠式浮动菜单 - 仅次于NavBar的层级 */}
      {menuVisible && (
        <div 
          className="mobile-dropdown-menu fixed right-0 top-11 bg-white border border-gray-200 shadow-lg"
          style={{ 
            width: menuWidth ? `${menuWidth}px` : undefined,
            zIndex: 9998 // 仅次于NavBar，确保在地图控件之上
          }}
        >
          <div className="divide-y divide-gray-100">
            {menuLoading ? (
              <div className="p-3 space-y-4">
                <Skeleton.Paragraph lineCount={3} animated />
              </div>
            ) : (
              menuItems.map((item: MenuItemDef) => (
                <MenuItemRow
                  key={item.path}
                  label={item.label}
                  icon={<RightOutline fontSize={16} className="text-gray-400" />}
                  onClick={() => handleMenuClick(item.path)}
                  className="px-3 hover:bg-gray-100"
                />
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
} 