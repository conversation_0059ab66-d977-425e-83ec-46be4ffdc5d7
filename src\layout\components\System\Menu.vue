<!--
 * @Description: 
 * @Autor: shh
 * @Date: 2022-07-20 15:09:00
 * @LastEditors: shh
 * @LastEditTime: 2023-10-24 10:47:29
-->
<template>
  <el-menu :default-active="routerActive" class="menu1" @open="handleOpen" @close="handleClose" @select="handleSelect">
    <template v-for="item in rouerList" v-if="!item.hidden">
      <el-submenu :index="item?.name" v-if="item?.children">
        <template slot="title">
          <!-- <img
            class="icon-img"
            :src="require('@/assets/images/lng/' + item?.meta?.icon + '.png')"
            alt=""
          /> -->

          <span>{{ item?.meta?.title }}</span>
        </template>
        <el-menu-item v-for="it in item?.children" v-if="!it.hidden" :key="it.name" :index="it?.name">{{ it?.meta?.title
        }}</el-menu-item>
      </el-submenu>
      <el-menu-item :index="item?.name" v-else>
        <img class="icon-img" :src="require('@/assets/images/lng/' + item?.meta?.icon + '.png')" alt="" />
        <span slot="title">{{ item?.meta?.title }}</span>
      </el-menu-item>
    </template>
  </el-menu>
</template>
<script>
export default {
  name: "",
  components: {},
  props: {
    config: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      routerActive: null,
      rouerList: this.$router.options.routes.filter((item) => {
        return item.name == "menu";
      })[0].children,
    };
  },
  computed: {},
  watch: {
    $route: {
      handler(val) {
        this.routerActive = val.name;
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    console.log(this.$route.path);
  },
  mounted() { },
  beforeDestroy() { },
  destroyed() { },

  methods: {
    handleOpen(key, keyPath) {
      console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath);
    },
    handleSelect(key, keyPath) {
      console.log(key, this.$route.path);
      if (this.$route.path !== "/" + key) {
        this.$router.push({ path: key });
      }
    },
  },
};
</script>
<style lang="scss">
.el-menu {
  margin: 20px !important;
  width: 250px;
  border: none !important;

  .el-submenu,
  .el-menu-item {
    min-height: 50px;
    margin: 10px 0;
    background: rgba(240, 244, 247, 0.2);
    border: 1px solid #d5dee9;
    font-size: 16px;
    font-family: ShiShangZhongHeiJianTi;
  }

  .el-submenu__title {
    font-size: 16px;
  }

  .el-menu-item.is-active {
    color: #fff;
    background: #1099bc;
  }

  .el-menu--inline {
    margin: 0 10px !important;
    width: 230px;
    border: none !important;
    border-top: 0.0625rem solid #d5dee9 !important;
  }

  .icon-img {
    width: 30px;
    margin-right: 10px;
  }
}

.el-submenu__icon-arrow {
  left: 40px !important;
  right: inherit !important;
}
</style>
