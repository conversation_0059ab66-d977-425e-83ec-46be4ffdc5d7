<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 16:10:49
 * @Description: 小程序商城分类管理
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'twoLevelList', hasChildren: 'hasChildren' }"
      :columns="columns"
      row-key="id"
      show-pagination
      :data="tableData"
      sticky
      default-expand-all
      :local-pagination="localPagination"
      :query-param="queryParam"
      :show-index="false"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增分类
        </el-button>
      </template>
      <template #status="{ row }">
        <el-switch
          v-model="row.status"
          active-color="#13ce66"
          inactive-color="#ff4949"
          @change="(value) => handleChangeStatus(value, row)"
        >
        </el-switch>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <!-- <el-button
            v-if="slotProps.row.parentId == 0"
            type="primary"
            size="mini"
            icon="el-icon-circle-plus"
            @click="handleAdd(slotProps.row)"
          >
            添加
          </el-button> -->

          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            详情
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>
          <!--<el-button-->
          <!--  v-if="slotProps.row.status != 'deleted'"-->
          <!--  size="mini"-->
          <!--  type="danger"-->
          <!--  icon="el-icon-delete"-->
          <!--  @click="handleDelete(slotProps.row, slotProps.index)"-->
          <!--&gt;-->
          <!--  删除-->
          <!--</el-button>-->
        </span>
      </template>
    </ProTable>

    <!-- 新增、编辑、详情框  -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="62%"
      :confirm-loading="confirmLoading"
      :confirm-btn-loading="confirmLoading"
      :top="'3%'"
      :no-footer="methodType === 'info'"
      :confirm-text="methodType === 'add' ? '确认新增' : '保存'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
      </ProForm>
      <div>
        <div>
          <div
            v-for="(item, index) in tagList"
            :key="index"
            class="classify-item"
          >
            <div class="classify-list">
              <el-input
                v-model="item.name"
                style="width: 200px"
                placeholder="请输入内容"
                :disabled="methodType === 'info'"
              ></el-input>
              <div class="classify-del">
                <!--<i-->
                <!--  v-if="methodType !== 'info'"-->
                <!--  class="el-icon-delete"-->
                <!--  @click="delTag(index)"-->
                <!--&gt;</i>-->
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-delete"
                  @click="delTag(index)"
                >
                  删除
                </el-button>
              </div>
            </div>
            <div>
              <div class="tag-list">
                <el-tag
                  v-for="(tag, ind) in item.value"
                  :key="tag"
                  :closable="methodType !== 'info'"
                  :disable-transitions="false"
                  @close="handleClose(index, ind)"
                >
                  {{ tag }}
                </el-tag>
              </div>
              <div style="margin-top: 10px">
                <el-input
                  v-if="item.inputVisible"
                  ref="saveTagInput"
                  v-model="item.inputValue"
                  :disabled="methodType === 'info'"
                  class="input-new-tag"
                  size="small"
                  @keyup.enter.native="handleInputConfirm(index)"
                  @blur="handleInputConfirm(index)"
                >
                </el-input>
                <el-button
                  v-else
                  :disabled="methodType === 'info'"
                  class="button-new-tag"
                  size="small"
                  @click="showInput(index)"
                  >+ 添加小类</el-button
                >
              </div>
            </div>
          </div>
        </div>
        <div v-if="methodType !== 'info'" style="margin-top: 20px">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-circle-plus"
            @click="addTag"
          >
            添加属性
          </el-button>
        </div>
        <div class="radio-box">
          <span>是否开启品牌/产品树搜索：</span>
          <el-radio
            v-model="form.isSwitch"
            :disabled="methodType === 'info'"
            :label="true"
            >是</el-radio
          >
          <el-radio
            v-model="form.isSwitch"
            :disabled="methodType === 'info'"
            :label="false"
            >否</el-radio
          >
        </div>
      </div>
    </ProDialog>
  </div>
</template>
<script>
import {
  classifyListApi,
  classifyAddApi,
  classifyDelApi,
  classifyEditApi,
  classifyInfoApi,
  classifyEnableApi,
} from "@/api/goods";

import { isEmpty, cloneDeep } from "lodash";

import { filterName, getAllParentArr } from "@/utils";

export default {
  name: "Classify",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      dynamicTags: ["标签一", "标签二", "标签三"],

      // 列表
      tableData: [],
      queryParam: {
        aduitState: null,
        name: null,
      },
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      columns: [
        {
          dataIndex: "name",
          isSearch: true,
          clearable: true,
          title: "关键字",
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "分类名称",
          isTable: true,
        },
        {
          dataIndex: "code",
          title: "分类编码",
          isTable: true,
        },
        {
          dataIndex: "status",
          title: "是否启用",
          isTable: true,
          tableSlot: "status",
        },
        {
          dataIndex: "sort",
          title: "排序",
          isTable: true,
        },
        {
          dataIndex: "Actions",
          width: 350,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      actionUrl: "",
      uploadlLading: false,
      uploadDialogVisible: false,
      //新增
      methodType: "add",
      confirmLoading: false,
      form: { parentId: "", isSwitch: false },
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {
        isSwitch: false,
      },
      formColumns: [
        {
          dataIndex: "name",
          isForm: true,
          title: "分类名称",
          valueType: "input",
          clearable: true,
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请输入分类名称",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "code",
          title: "分类编码",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请输入分类编码",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "sort",
          isForm: true,
          title: "排序",
          valueType: "input",
          inputType: "number",
          clearable: true,
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请输入排序",
              trigger: "change",
            },
          ],
        },
      ],
      tagList: [],
      allTagIdList: [],
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    addTag() {
      this.tagList.push({
        name: "",
        inputVisible: false,
        inputValue: "",
        value: [],
      });
    },
    delTag(index) {
      // 删除tag，需要把tag的id传给后端
      this.tagList.splice(index, 1);
      // this.handleDeleteTagById();
    },
    handleClose(index, ind) {
      this.tagList[index].value.splice(ind, 1);
      // this.handleDeleteTagById();
    },
    // 处理删除tag，需要把tag的id传给后端
    handleDeleteTagById() {
      this.form.delTagIds = [];
      const newIds = this.tagList.map((item) => {
        if (item.value.length > 0) {
          return item.value.map((it) => it.id);
        }
      });
      const diffIds = this.allTagIdList.filter(
        (item) => !newIds.flat(1).includes(item)
      );

      this.form.delTagIds = diffIds;
    },
    showInput(index) {
      this.tagList[index].inputVisible = true;
    },

    handleInputConfirm(index) {
      const inputValue = this.tagList[index].inputValue;
      if (inputValue) {
        this.tagList[index].value.push(inputValue);
        this.tagList[index].inputVisible = false;
        this.tagList[index].inputValue = "";
      }
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      classifyListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["ProForm"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      // 清除列表数据
      this.tagList = [];
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;

      this.$nextTick((e) => {
        this.$refs["ProForm"].resetFormParam();
        data?.id ? (this.form.parentId = data.id) : (this.form.parentId = 0);
        this.dialogTitle = "新增分类";
        // if (this.form.parentId == 0) {
        //   this.dialogTitle = "新增一级分类";
        // } else {
        //   this.dialogTitle = "新增二级分类";
        // }
      });
    },
    //响应新增
    create() {
      const tagList = this.tagList;
      const params = { ...this.form, tagList };
      classifyAddApi(params)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    async handleUpdate(row) {
      this.dialogTitle = "编辑 - " + row.name;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.tagList = await this.handleTagList(row.id);
      this.methodType = "edit";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["ProForm"].resetFormParam();
      });
    },
    //响应编辑
    update() {
      const tagList = this.tagList;
      const params = { ...this.form, tagList };
      classifyEditApi(params)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    async handleInfo(row) {
      this.dialogTitle = "查看 - " + row.name;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.tagList = await this.handleTagList(row.id);
      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["ProForm"].resetFormParam();
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        classifyDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
    handleChangeStatus(value, row) {
      classifyEnableApi(row.id, value)
        .then((res) => {
          this.$message.success("操作成功");
          this.$refs.ProTable.refresh();
        })
        .catch(() => {
          row.status = !value;
        });
    },
    // 处理tagList回显
    async handleTagList(id) {
      // 通过接口查询才能查到详情
      const res = await classifyInfoApi({
        id,
      });
      const list = res.data.tagList;
      this.allTagIdList = list.map((item) => item.id);
      return new Promise((resolve) => {
        if (list.length > 0) {
          // 需要设置每一项的 inputVisible 为false，才可以正常添加tag
          const arr = list.map((item) => {
            item.inputVisible = false;
            return item;
          });
          resolve(arr);
        } else {
          resolve([]);
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.button-new-tag {
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
.radio-box {
  margin: 20px 0;
}
::v-deep.classify-item {
  padding: 10px;
  border: 1px solid #f5f5f5;
  .classify-list {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    .classify-del {
      font-size: 18px;
      cursor: pointer;
      .el-button--text {
        color: #f56c6c !important;
      }
    }
  }
  &:last-child {
    margin-top: 10px;
  }
}

.tag-list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
</style>
