<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 16:52:23
 * @Description: 
 -->
<template>
  <div class="container">
    <!-- 全屏按钮 -->
    <div class="fullscreen-btn" @click="toggleFullScreen">
      <i :class="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"></i>
    </div>
    <scale-box>
      <div v-resize="handleContainerResize" class="dashboard-container">
        <el-row :gutter="12">
          <el-col :span="6">
            <div class="left-panel">
              <div class="box">
                <div class="tit">服务印量实时数据</div>
                <div class="box-nav">
                  <ul class="yl-list">
                    <li>
                      <div class="yl">
                        <number-dance :value="servicePoint?.blackQuantity" />
                        <!--{{ servicePoint?.blackQuantity || 0 }}-->
                      </div>
                      <span>黑色总印量</span>
                    </li>
                    <li>
                      <div class="yl">
                        <number-dance :value="servicePoint?.colorQuantity" />
                        <!--{{ servicePoint?.colorQuantity || 0 }}-->
                      </div>
                      <span>彩色总印量</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="box">
                <div class="tit">本月印量</div>
                <div class="box-nav">
                  <ul class="yl-list">
                    <li>
                      <div class="yl">
                        <number-dance :value="currentPoint?.blackQuantity" />
                        <!--{{ currentPoint?.colorQuantity || 0 }}-->
                      </div>
                      <span>黑色总印量</span>
                    </li>
                    <li>
                      <div class="yl">
                        <number-dance :value="currentPoint?.colorQuantity" />
                        <!--{{ currentPoint?.colorQuantity || 0 }}-->
                      </div>
                      <span>彩色总印量</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="box" style="margin-bottom: 0">
                <div class="tit">客户分布</div>
                <div class="box-nav">
                  <div ref="city" class="city-container"></div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="center-panel">
              <div class="box">
                <div v-if="!isFullscreen" class="map-controls">
                  <el-select
                    v-model="selectedProvince"
                    placeholder="选择省份"
                    @change="handleProvinceChange"
                  >
                    <el-option
                      v-for="item in provinces"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <!--<div ref="showTime" class="time">{{ currentTime }}</div>-->
                </div>
                <div class="box-nav">
                  <div ref="map" class="map-container"></div>
                </div>
              </div>
              <div class="box" style="margin-bottom: 0">
                <div v-if="!isFullscreen" class="time-selector">
                  <el-select
                    v-model="timeRange"
                    placeholder="选择时间范围"
                    style="width: 100px"
                    size="small"
                    @change="handleTimeRangeChange"
                  >
                    <!--<el-option label="年" value="year"></el-option>-->
                    <!--<el-option label="季度" value="quarter"></el-option>-->
                    <el-option
                      label="月"
                      :value="1"
                      :selected="true"
                    ></el-option>
                    <el-option label="日" :value="0"></el-option>
                  </el-select>
                </div>
                <div ref="order" class="order-container"></div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="right-panel">
              <div class="box">
                <div class="tit">装机服务数量</div>
                <div class="box-nav">
                  <ul class="yl-list">
                    <li>
                      <div class="yl">
                        <number-dance :value="machineNum?.blackQuantity" />
                        <!--{{ machineNum?.blackQuantity || 0 }}-->
                      </div>
                      <span>黑白机</span>
                    </li>
                    <li>
                      <div class="yl">
                        <number-dance :value="machineNum?.colorQuantity" />
                        <!--{{ machineNum?.colorQuantity || 0 }}-->
                      </div>
                      <span>彩机</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="box">
                <div class="tit">本月装机数量</div>
                <div class="box-nav">
                  <ul class="yl-list">
                    <li>
                      <div class="yl">
                        <number-dance
                          :value="currentMachineNum?.blackQuantity"
                        />
                        <!--{{ currentMachineNum?.blackQuantity || 0 }}-->
                      </div>
                      <span>黑白机</span>
                    </li>
                    <li>
                      <div class="yl">
                        <number-dance
                          :value="currentMachineNum?.colorQuantity"
                        />
                        <!--{{ currentMachineNum?.colorQuantity || 0 }}-->
                      </div>
                      <span>彩机</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="box" style="margin-bottom: 0">
                <div class="tit">重点机型</div>
                <div class="box-nav">
                  <div
                    ref="brandChart"
                    class="brand-container"
                    style="height: 100%"
                  ></div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </scale-box>
  </div>
</template>

<script>
import NumberDance from "@/views/home/<USER>/numberDance.vue";
import * as echarts from "echarts";
import ScaleBox from "vue2-scale-box";
import {
  getBigViewMachineData,
  getBigViewRealTimeData,
  getBigViewSalesData,
} from "@/api";
export default {
  name: "BigDataView",
  components: {
    ScaleBox,
    NumberDance,
  },
  data() {
    return {
      servicePoint: {},
      currentPoint: {},
      machineNum: {},
      currentMachineNum: {},
      myChart: null,
      cityChart: null,
      isFullscreen: false,
      timeRange: 0,
      selectedProvince: 510000,
      provinces: [
        { label: "全国", value: 100000 },
        { label: "北京市", value: 110000 },
        { label: "天津市", value: 120000 },
        { label: "河北省", value: 130000 },
        { label: "山西省", value: 140000 },
        { label: "内蒙古自治区", value: 150000 },
        { label: "辽宁省", value: 210000 },
        { label: "吉林省", value: 220000 },
        { label: "黑龙江省", value: 230000 },
        { label: "上海市", value: 310000 },
        { label: "江苏省", value: 320000 },
        { label: "浙江省", value: 330000 },
        { label: "安徽省", value: 340000 },
        { label: "福建省", value: 350000 },
        { label: "江西省", value: 360000 },
        { label: "山东省", value: 370000 },
        { label: "河南省", value: 410000 },
        { label: "湖北省", value: 420000 },
        { label: "湖南省", value: 430000 },
        { label: "广东省", value: 440000 },
        { label: "广西壮族自治区", value: 450000 },
        { label: "海南省", value: 460000 },
        { label: "重庆市", value: 500000 },
        { label: "四川省", value: 510000 },
        { label: "贵州省", value: 520000 },
        { label: "云南省", value: 530000 },
        { label: "西藏自治区", value: 540000 },
        { label: "陕西省", value: 610000 },
        { label: "甘肃省", value: 620000 },
        { label: "青海省", value: 630000 },
        { label: "宁夏回族自治区", value: 640000 },
        { label: "新疆维吾尔自治区", value: 650000 },
        { label: "台湾省", value: 710000 },
        { label: "香港", value: 810000 },
        { label: "澳门", value: 820000 },
      ],
      machineData: [
        // { name: "成都市", value: 120 },
        // { name: "眉山市", value: 60 },
        // { name: "阿坝藏族羌族自治州", value: 20 },
      ],
      // currentTime: "",
      timeInterval: null, // 时间定时器
      // 数据轮询定时器
      timer: null,
      pollingInterval: 1000 * 60 * 3, // 轮询间隔 3 分钟
      incrementTimer: null,
      isPollingPaused: false,
      orderChart: null,
      brandChart: null,
      brandData: [
        // { brand: "5300", value: 156 },
        // { brand: "7500", value: 142 },
        // { brand: "V170", value: 124 },
        // { brand: "9200", value: 98 },
        // { brand: "9500", value: 86 },
        // { brand: "维多利亚", value: 76 },
      ],
      customerDistribution: {
        // cities: [
        //   "成都",
        //   "德阳",
        //   "绵阳",
        //   "宜宾",
        //   "泸州",
        //   "内江",
        //   "自贡",
        //   "南充",
        // ],
        // serviceCustomers: [683, 234, 234, 523, 345, 320, 280, 271],
        // totalCustomers: [820, 332, 301, 634, 490, 430, 320, 345],
      },
      monthlyData: {
        // months: [
        //   "1月",
        //   "2月",
        //   "3月",
        //   "4月",
        //   "5月",
        //   "6月",
        //   "7月",
        //   "8月",
        //   "9月",
        //   "10月",
        //   "11月",
        //   "12月",
        // ],
        // sales: [
        //   156.5, 187.2, 235.8, 262.3, 217.6, 243.8, 268.5, 284.2, 298.7, 312.4,
        //   326.8, 358.5,
        // ],
      },
    };
  },
  async mounted() {
    await this.startPolling();
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    if (this.cityChart) {
      this.cityChart.dispose();
    }
    if (this.orderChart) {
      this.orderChart.dispose();
    }
    if (this.brandChart) {
      this.brandChart.dispose();
    }
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
    if (this.incrementTimer) {
      clearInterval(this.incrementTimer);
    }
    this.stopPolling();
  },
  methods: {
    // 开始轮询
    async startPolling() {
      this.stopPolling();
      if (!this.isPollingPaused) {
        await this.getInitData(); // 立即执行一次
        await this.init();
        this.timer = setInterval(() => {
          this.getInitData();
        }, this.pollingInterval);
      }
    },

    // 停止轮询
    stopPolling() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    // 暂停轮询
    pausePolling() {
      this.isPollingPaused = true;
      this.stopPolling();
    },

    // 恢复轮询
    resumePolling() {
      this.isPollingPaused = false;
      this.startPolling();
    },
    init() {
      this.initChart();
      this.initCityChart();
      this.initOrderChart();
      this.initBrandChart();
      // this.startUpdateTime();
      document.addEventListener(
        "fullscreenchange",
        this.handleFullscreenChange
      );
    },
    async initChart() {
      // 清除现有图表实例
      if (this.myChart) {
        this.myChart.dispose();
      }
      const provinceMap = {
        100000: "全国",
        110000: "北京",
        120000: "天津",
        130000: "河北",
        140000: "山西",
        150000: "内蒙古",
        210000: "辽宁",
        220000: "吉林",
        230000: "黑龙江",
        310000: "上海",
        320000: "江苏",
        330000: "浙江",
        340000: "安徽",
        350000: "福建",
        360000: "江西",
        370000: "山东",
        410000: "河南",
        420000: "湖北",
        430000: "湖南",
        440000: "广东",
        450000: "广西",
        460000: "海南",
        500000: "重庆",
        510000: "四川",
        520000: "贵州",
        530000: "云南",
        540000: "西藏",
        610000: "陕西",
        620000: "甘肃",
        630000: "青海",
        640000: "宁夏",
        650000: "新疆",
        710000: "台湾",
        810000: "香港",
        820000: "澳门",
      };

      const provinceName = provinceMap[this.selectedProvince];
      if (!provinceName) {
        console.error("Province not found for value:", this.selectedProvince);
        return;
      }
      const response = await fetch(
        `https://benyin-1315885374.cos.ap-chengdu.myqcloud.com/prod/region/${this.selectedProvince}.json`
      );
      const mapJSON = await response.json();
      this.myChart = echarts.init(this.$refs.map);
      echarts.registerMap(provinceName, mapJSON);

      // 配置项
      const option = {
        tooltip: {
          trigger: "item", // 鼠标悬停触发
          formatter: function (params) {
            return `${params.name}: ${params.value || 0}台`;
          },
          backgroundColor: "rgba(38, 69, 134, 0.8)",
          borderColor: "#16d6ff",
          textStyle: {
            color: "#fff",
          },
        },
        series: [
          {
            name: "机器数量",
            type: "map",
            map: provinceName,
            roam: true,
            label: {
              show: true,
              formatter: (params) => {
                return `${params.name}\n${params.value || 0}台`;
              },
              fontSize: 12,
              color: "#fff",
              textShadow: "0 0 5px rgba(0,0,0,0.5)",
            },
            itemStyle: {
              areaColor: "rgba(38, 69, 134, 0.3)",
              borderColor: "#27a9f4", // #16d6ff
              borderWidth: 1,
              shadowColor: "rgba(22, 214, 255, 0.2)",
              shadowBlur: 10,
            },
            emphasis: {
              label: {
                show: true,
                color: "#fff",
                fontSize: 14,
                fontWeight: "bold",
              },
              itemStyle: {
                areaColor: "#16d6ff",
                borderColor: "#fff",
                borderWidth: 1,
                shadowColor: "rgba(22, 214, 255, 0.5)",
                shadowBlur: 20,
              },
            },
            data: this.machineData,
          },
        ],
      };
      // 渲染地图
      this.myChart.setOption(option);
    },
    initCityChart() {
      if (this.cityChart) {
        this.cityChart.dispose();
      }
      this.cityChart = echarts.init(this.$refs.city);

      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "5%",
          right: "5%",
          top: "15%",
          bottom: "0%",
          containLabel: true,
        },
        legend: {
          data: ["总客户", "服务客户"],
          textStyle: {
            color: "#fff",
          },
          right: 10,
          top: 0,
        },
        xAxis: {
          type: "category",
          data: this.customerDistribution.citys,
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.3)",
            },
          },
          axisLabel: {
            color: "#fff",
            fontSize: 12,
            rotate: 45,
            interval: 0,
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.3)",
            },
          },
          axisLabel: {
            color: "#fff",
            fontSize: 12,
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.1)",
            },
          },
        },
        series: [
          {
            name: "总客户",
            type: "bar",
            data: this.customerDistribution.totalCustomers,
            barWidth: 10,
            itemStyle: {
              color: "rgba(169, 223, 150,1)",
              borderRadius: [20, 20, 0, 0],
            },
            label: {
              show: true,
              position: "top",
              // color: "rgba(255,255,255,0.2)",
              color: "#fff",
              fontSize: 12,
              // offset: [0, -20],
            },
            z: 1,
          },
          {
            name: "服务客户",
            type: "bar",
            data: this.customerDistribution.serviceCustomers,
            barWidth: 10,
            itemStyle: {
              color: "#1089E7",
              borderRadius: [20, 20, 0, 0],
            },
            label: {
              show: true,
              position: "top",
              color: "#fff",
              fontSize: 12,
            },
            z: 2,
          },
        ],
      };

      this.cityChart.setOption(option);
    },
    initOrderChart() {
      if (this.orderChart) {
        this.orderChart.dispose();
      }
      this.orderChart = echarts.init(this.$refs.order);

      const option = {
        title: {
          text: "销售金额",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
          left: 10,
          top: 5,
          padding: [0, 0, 20, 0], // 添加底部内边距
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            return `${params[0].name}<br/>${params[0].seriesName}：${params[0].value} 元`;
          },
          axisPointer: {
            type: "line",
            lineStyle: {
              color: "rgba(255,255,255,0.3)",
            },
          },
        },
        grid: {
          left: "3%",
          right: "3%",
          bottom: "2%",
          top: "30%", // 增加顶部间距
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: this.monthlyData.months,
          axisLabel: {
            color: "#fff",
            fontSize: 12,
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.3)",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "金额（元）",
          nameTextStyle: {
            color: "#fff",
            fontSize: 12,
            padding: [0, 0, 0, 0],
          },
          axisLabel: {
            color: "#fff",
            fontSize: 12,
            formatter: "{value}",
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.3)",
            },
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.1)",
            },
          },
        },
        series: [
          {
            name: "销售金额",
            type: "line",
            data: this.monthlyData.sales,
            smooth: true,
            symbol: "circle",
            symbolSize: 8,
            itemStyle: {
              color: "#37a2da",
            },
            lineStyle: {
              width: 3,
              shadowColor: "rgba(55,162,218,0.5)",
              shadowBlur: 10,
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(55,162,218,0.6)",
                },
                {
                  offset: 1,
                  color: "rgba(55,162,218,0.1)",
                },
              ]),
            },
            label: {
              show: true,
              position: "top",
              formatter: "{c} 元",
              color: "#fff",
              fontSize: 12,
            },
          },
        ],
      };
      this.orderChart.setOption(option);
    },
    initBrandChart() {
      if (this.brandChart) {
        this.brandChart.dispose();
      }
      this.brandChart = echarts.init(this.$refs.brandChart);

      const option = {
        grid: {
          left: "8%",
          right: "5%",
          top: "10%",
          bottom: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.3)",
            },
          },
          axisLabel: {
            color: "#fff",
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.1)",
            },
          },
        },
        yAxis: {
          type: "category",
          data: this.brandData.map((item) => item.brand),
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.3)",
            },
          },
          axisLabel: {
            color: "#fff",
          },
        },
        series: [
          {
            type: "bar",
            data: this.brandData.map((item) => item.value),
            barWidth: 15,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: "#16d6ff" },
                { offset: 1, color: "#3f7dff" },
              ]),
              borderRadius: [0, 15, 15, 0],
            },
            label: {
              show: true,
              position: "right",
              color: "#fff",
              fontSize: 12,
            },
          },
        ],
      };

      this.brandChart.setOption(option);
    },
    async getInitData() {
      try {
        const [realTimeData, salesData, machineData] = await Promise.all([
          getBigViewRealTimeData(),
          getBigViewSalesData(this.timeRange),
          getBigViewMachineData(this.selectedProvince),
        ]);
        const {
          brandData,
          currentMachineNum,
          machineNum,
          currentPoint,
          customerData,
          servicePoint,
        } = realTimeData.data;
        this.servicePoint = servicePoint;
        this.currentPoint = currentPoint;
        this.machineNum = machineNum;
        this.currentMachineNum = currentMachineNum;
        this.brandData = brandData;
        this.customerDistribution = customerData;
        this.machineData = machineData.data;
        this.monthlyData = salesData.data;
        // 更新各个图表的数据
        if (this.myChart) {
          this.myChart.setOption({
            series: [
              {
                data: this.machineData,
                animation: true,
                animationDuration: 1000,
              },
            ],
          });
        }

        if (this.cityChart) {
          this.cityChart.setOption({
            xAxis: {
              data: this.customerDistribution.citys,
            },
            series: [
              {
                data: this.customerDistribution.totalCustomers,
                animation: true,
                animationDuration: 1000,
              },
              {
                data: this.customerDistribution.serviceCustomers,
                animation: true,
                animationDuration: 1000,
              },
            ],
          });
        }

        if (this.orderChart) {
          this.orderChart.setOption({
            xAxis: {
              data: this.monthlyData.months,
              animation: true,
              animationDuration: 1000,
            },
            series: [
              {
                data: this.monthlyData.sales,
                animation: true,
                animationDuration: 1000,
              },
            ],
          });
        }

        if (this.brandChart) {
          this.brandChart.setOption({
            yAxis: {
              data: this.brandData.map((item) => item.brand),
              animation: true,
              animationDuration: 1000,
            },
            series: [
              {
                data: this.brandData.map((item) => item.value),
                animation: true,
                animationDuration: 1000,
              },
            ],
          });
        }
      } catch (error) {
        console.log(error.message);
      }
    },
    async handleTimeRangeChange() {
      try {
        // 暂停轮询
        this.pausePolling();
        const result = await getBigViewSalesData(this.timeRange);
        if (result.code === 200) {
          this.monthlyData = result.data;
          this.initOrderChart();
          // 恢复轮询
          this.resumePolling();
        }
      } catch (e) {
        console.log(e.message);
      }
    },
    async handleProvinceChange() {
      try {
        // 暂停轮询
        this.pausePolling();
        const result = await getBigViewMachineData(this.selectedProvince);
        if (result.code === 200) {
          this.machineData = result.data;
          await this.initChart();
          // 恢复轮询
          this.resumePolling();
        }
      } catch (e) {
        console.log(e.message);
      }
    },
    handleFullscreenChange() {
      this.isFullscreen = !!document.fullscreenElement;
    },
    toggleFullScreen() {
      const container = document.querySelector(".dashboard-container");
      if (!document.fullscreenElement) {
        container.requestFullscreen().catch((err) => {
          console.error(
            `Error attempting to enable fullscreen: ${err.message}`
          );
        });
        this.isFullscreen = true;
        document.body.classList.add("fullscreen");
      } else {
        document.exitFullscreen();
        this.isFullscreen = false;
        document.body.classList.remove("fullscreen");
      }
    },
    // startUpdateTime() {
    //   this.currentTime = new Date().toLocaleString();
    //   this.timeInterval = setInterval(() => {
    //     this.currentTime = new Date().toLocaleString();
    //   }, 1000);
    // },
    handleContainerResize(args) {
      this.$nextTick(() => {
        if (this.myChart) this.myChart.resize();
        if (this.cityChart) this.cityChart.resize();
        if (this.orderChart) this.orderChart.resize();
        if (this.brandChart) this.brandChart.resize();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  position: relative;
  height: calc(100vh - 130px);
  min-height: 100%;
  ::v-deep .vue2-scale-box {
    width: 100% !important;
    height: 100% !important;
    position: unset !important;
    transform: none !important;
    transition: width 0.3s, height 0.3s;
  }
  .fullscreen-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    cursor: pointer;
    padding: 10px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
  .fullscreen .scale-box {
    width: 1920px;
    height: 1080px;
  }
}
li {
  list-style-type: none;
}
.box {
  $border-color: rgba(7, 118, 181, 0.5);
  $shadow-color: rgba(7, 118, 181, 0.4);
  $gradient-color: #076ead;
  border: 1px solid rgba(7, 118, 181, 0.5);
  box-shadow: inset 0 0 10px $shadow-color;
  margin-bottom: 12px;
  position: relative;
  display: flex;
  flex-direction: column;
  &:before {
    content: "";
    width: 100%;
    height: 1px;
    position: absolute;
    left: 0;
    bottom: -1px;
    top: -1px;
    background: linear-gradient(to right, #076ead, #4ba6e0, #076ead);
    box-shadow: 0 0 5px rgba(131, 189, 227, 1);
    opacity: 0.6;
  }
  .tit {
    padding: 10px 10px 10px 25px;
    border-bottom: 1px solid rgba(7, 118, 181, 0.7);
    font-size: 16px;
    font-weight: 500;
    position: relative;
    &:before {
      position: absolute;
      content: "";
      width: 6px;
      height: 6px;
      background: rgba(22, 214, 255, 0.9);
      box-shadow: 0 0 5px rgba(22, 214, 255, 0.9);
      border-radius: 10px;
      left: 10px;
      top: 18px;
    }
    &:after {
      width: 100%;
      height: 1px;
      content: "";
      position: absolute;
      left: 0;
      bottom: -1px;
      background: linear-gradient(to right, #076ead, #4ba6e0, #076ead);
      box-shadow: 0 0 5px rgba(131, 189, 227, 1);
      opacity: 0.6;
    }
  }
  .box-nav {
    width: 100%;
    height: 100%; // 保证 box-nav 占满父容器高度

    display: flex;
    flex-direction: column;
    overflow: hidden;

    .yl-list {
      width: 100%;
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
      padding-left: 0;

      li {
        width: 50%;
        height: auto;
        text-align: center;
        list-style-type: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px;
        .yl {
          width: 100%;
          position: relative;
          aspect-ratio: 1;
          flex: 0 0 auto;
          max-width: 100px;
          margin: 0 auto 5px auto;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: calc(min(26px, 3vw));
          color: #fff32b;
          &:before,
          &:after {
            position: absolute;
            content: "";
            inset: 0;
            background-position: center;
            background-size: 100% 100%;
            border-radius: 50%;
          }
          &:before {
            background-image: url("@/assets/images/screen/img1.png");
            opacity: 0.3;
            animation: my-circle1 15s infinite linear;
          }
          &:after {
            inset: 7%;
            background-image: url("@/assets/images/screen/img2.png");
            opacity: 0.3;
            animation: my-circle 15s infinite linear;
          }
        }
        span {
          //opacity: 0.6;
          font-size: 14px;
          width: 100%;
          word-wrap: break-word;
          white-space: normal;
          //color: #fff;
        }
      }
    }
  }
}
%panel-base {
  height: 100%;
  display: flex;
  flex-direction: column;
  //gap: 20px;
}
.dashboard-container {
  width: 100%;
  height: 100%;
  //min-height: 100vh;
  padding: 12px;
  text-align: left;
  background-image: url("@/assets/images/screen/bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  color: #fff;
  font-size: 16px;
  ::v-deep .el-row {
    height: 100%;
  }

  ::v-deep .el-col {
    height: 100%;
  }
  .left-panel,
  .center-panel,
  .right-panel {
    @extend %panel-base;
  }
  .left-panel {
    .box {
      &:nth-child(1) {
        flex: 3;
        min-height: 0;
      }
      &:nth-child(2) {
        flex: 3;
        min-height: 0;
      }
      &:nth-child(3) {
        flex: 4;
        min-height: 0;
      }
      //&:first-child {
      //  flex: 4;
      //  min-height: 0;
      //}
      //
      //&:last-child {
      //  flex: 6;
      //  min-height: 0;
      //}
    }
    .city-container {
      width: 100%;
      height: 100%;
    }
  }
  .center-panel {
    position: relative;
    .box {
      &:first-child {
        flex: 7;
        min-height: 0;
      }

      &:last-child {
        flex: 3;
        min-height: 0;
      }
    }
    ::v-deep .map-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      line-height: 0;
      .el-input {
        input {
          background: transparent;
          border: 1px solid rgba(7, 118, 181, 0.5);
          color: #fff;
        }
      }
    }
    ::v-deep .time-selector {
      position: absolute;
      right: 0;
      top: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      line-height: 0;
      z-index: 10;
      .el-input {
        input {
          background: transparent;
          border: 1px solid rgba(7, 118, 181, 0.5);
          color: #fff;
        }
      }
    }

    .map-container {
      width: 100%;
      height: 100%;
      //min-height: 600px;
      border-radius: 8px;
      pointer-events: auto;
      background-image: url("@/assets/images/screen/bg3.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center center;
    }
    .order-container {
      width: 100%;
      height: 100%;
      //min-height: 300px;
      padding: 10px;
    }
  }
  .right-panel {
    .box {
      &:nth-child(1) {
        flex: 3;
        min-height: 0;
      }
      &:nth-child(2) {
        flex: 3;
        min-height: 0;
      }
      &:nth-child(3) {
        flex: 4;
        min-height: 0;
      }
    }
    .machine-container {
      width: 100%;
      height: 100%;
      //min-height: 200px;
    }
    .brand-container {
      width: 100%;
      height: 100%;
      //min-height: 200px;
    }
    .repair-list {
      height: 100%;
      //max-height: 250px;
      overflow: hidden;
      position: relative;
      padding: 10px;

      .repair-list-content {
        transition: transform 0.05s linear;
      }

      .repair-item {
        padding: 10px;
        border-bottom: 1px solid rgba(7, 118, 181, 0.3);
        display: flex;
        flex-direction: column;
        gap: 5px;

        &:last-child {
          border-bottom: none;
        }

        .repair-info,
        .repair-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .customer {
          color: #fff;
          font-size: 14px;
        }

        .location {
          color: rgba(255, 255, 255, 0.6);
          font-size: 12px;
        }

        .engineer {
          color: #16d6ff;
          font-size: 12px;
        }

        .duration {
          color: #ffdb5c;
          font-size: 12px;
        }
      }
    }
  }
}

@keyframes my-circle {
  to {
    transform: rotate(-360deg);
  }
}
@keyframes my-circle1 {
  to {
    transform: rotate(360deg);
  }
}

@keyframes scrollUp {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-100%);
  }
}
</style>
