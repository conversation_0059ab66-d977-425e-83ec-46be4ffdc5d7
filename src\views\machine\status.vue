<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:52
 * @Description: 状态上报
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :reserve-selection="true"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #btn>
        <!-- <el-button plain type="primary" class="add-btn" size="mini" icon="el-icon-share"
                    @click="exportCustom">导出</el-button> -->
        <!-- <el-button type="success" class="add-btn" size="mini" icon="el-icon-plus"
                    @click="dialogVisible = true">新建</el-button> -->
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>

      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
        </div>
      </template>
      <template #reportTime>
        <el-date-picker
          v-model="reportTime"
          style="width: 100%"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :format="'yyyy-MM-dd'"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </template>
    </ProTable>
    <!-- 新建弹窗 -->
    <!-- <ProDialog :value="addDialog" :title="'新建'" :confirm-text="'保存'" width="80%" :top="'50px'" @ok="handleAddDialogOk"
            @cancel="addDialog = false">
            <ProForm ref="addForm" :form-param="addForm" :form-list="addColumns" :confirm-loading="addFormLoading"
                :layout="{ formWidth: '100%', labelWidth: '140px' }" :open-type="'add'" @proSubmit="addFormSubmit">

            </ProForm>
        </ProDialog> -->
    <!-- 查看 -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="50%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <!-- <ProForm ref="proform_child1" :form-param="form" :form-list="columns" :confirm-loading="dialogLoading"
                :layout="{ formWidth: '50%', labelWidth: '140px' }" :open-type="'info'" :disabled="methodType == 'info'" :no-footer="true">
            </ProForm> -->
      <el-form
        ref="proform_child1"
        :model="form"
        :disabled="methodType == 'info'"
        :inline="true"
        label-width="140px"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="机器编号:" prop="deviceGroupId">
              <el-input
                v-model="form.deviceGroupId"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="店铺名称:" prop="customerName">
              <el-input
                v-model="form.customerName"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="客户编号:" prop="customerCode">
              <el-input
                v-model="form.customerCode"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="设备组名称:" prop="deviceGroup">
              <el-input
                v-model="form.deviceGroup"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="品牌:" prop="brand">
              <el-input v-model="form.brand" style="width: 240px"></el-input>
            </el-form-item>
            <el-form-item label="主机型号:" prop="machine">
              <el-input v-model="form.machine" style="width: 240px"></el-input>
            </el-form-item>
            <el-form-item label="设备状态:" prop="scCode">
              <el-input v-model="form.scCode" style="width: 240px"></el-input>
            </el-form-item>

            <el-form-item label="故障代码:" prop="scCode">
              <el-input v-model="form.scCode" style="width: 240px"></el-input>
            </el-form-item>
            <el-form-item label="卡纸描述:" prop="jamDescribe">
              <el-input
                v-model="form.jamDescribe"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="状态描述:" prop="stateDescribe">
              <el-input v-model="form.stateDescribe" type="textarea"></el-input>
            </el-form-item>
            <el-form-item label="数字代码:" prop="numCode">
              <el-input v-model="form.numCode" style="width: 240px"></el-input>
            </el-form-item>
            <el-form-item label="首次上报时间:" prop="reportTime">
              <el-input
                v-model="form.reportTime"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="上报次数:" prop="reportNum">
              <el-input
                v-model="form.reportNum"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="最后上报时间:" prop="updatedAt">
              <el-input
                v-model="form.updatedAt"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第1色计数器 (K):" prop="blackWhiteCounter">
              <el-input
                v-model="form.blackWhiteCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第2色计数器 (K):" prop="cyanCounter">
              <el-input
                v-model="form.cyanCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第3色计数器 (K):" prop="magentaCounter">
              <el-input
                v-model="form.magentaCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第4色计数器 (K):" prop="yellowCounter">
              <el-input
                v-model="form.yellowCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第5色计数器:" prop="fifthCounter">
              <el-input
                v-model="form.fifthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第6色计数器:" prop="sixthCounter">
              <el-input
                v-model="form.sixthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第7色计数器:" prop="seventhCounter">
              <el-input
                v-model="form.seventhCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第8色计数器:" prop="eightthCounter">
              <el-input
                v-model="form.eightthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第9色计数器:" prop="ninthCounter">
              <el-input
                v-model="form.ninthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第10色计数器:" prop="tenthCounter">
              <el-input
                v-model="form.tenthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第11色计数器:" prop="eleventhCounter">
              <el-input
                v-model="form.eleventhCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第12色计数器:" prop="twelfthCounter">
              <el-input
                v-model="form.twelfthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第13色计数器:" prop="thirteenthCounter">
              <el-input
                v-model="form.thirteenthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第14色计数器:" prop="fourteenthCounter">
              <el-input
                v-model="form.fourteenthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第15色计数器:" prop="fifteenthCounter">
              <el-input
                v-model="form.fifteenthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第16色计数器:" prop="sixteenthCounter">
              <el-input
                v-model="form.sixteenthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第17色计数器:" prop="seventeenthCounter">
              <el-input
                v-model="form.seventeenthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
            <el-form-item label="第18色计数器:" prop="eightteenthCounter">
              <el-input
                v-model="form.eightteenthCounter"
                style="width: 240px"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ProDrawer>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";

import { getStateApi } from "@/api/iot";
import { deepClone, filterParam, filterParamRange } from "@/utils";
import { exportExcel } from "@/utils/exportExcel";
import { productAllApi } from "@/api/dispose";
import { cloneDeep } from "lodash";

export default {
  name: "Status",
  components: { ProTable },
  data() {
    return {
      tableData: [],
      productIdName: [],
      options: [],
      columns: [
        {
          dataIndex: "deviceSeqId",
          title: "机器编号",
          isForm: true,
          isTable: true,
          isSearch: true,
          valueType: "input",
          clearable: true,
          width: 120,
        },
        {
          dataIndex: "kernelBrand",
          title: "实际品牌",
          isTable: true,
          valueType: "input",
          clearable: true,
          width: 120,
        },
        {
          dataIndex: "kernelType",
          title: "实际主机型号",
          isTable: true,
          valueType: "input",
          clearable: true,
          width: 150,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          isForm: true,
          valueType: "input",
          clearable: true,
          width: 120,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          isForm: true,
          valueType: "input",
          clearable: true,
          option: [],
          formatter: (row) => row.deviceGroup.label,
          optionskey: {
            label: "label",
            value: "value",
          },
          width: 100,
        },
        {
          dataIndex: "deviceGroupName",
          title: "设备组名称",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          isForm: true,
          valueType: "input",
          clearable: true,
        },
        {
          dataIndex: "machine",
          title: "主机型号",
          isTable: true,
          isForm: true,
          valueType: "input",
          clearable: true,
          width: 120,
        },
        {
          dataIndex: "numCode",
          title: "数字代码",
          isTable: true,
          clearable: true,
          isSearch: true,
          isForm: true,
          valueType: "input",
        },
        {
          dataIndex: "scCode",
          title: "故障代码",
          isTable: true,
          clearable: true,
          isForm: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "productList",
          title: "品牌/系列",
          placeholder: "品牌/产品树/系列",
          isSearch: true,
          valueType: "product",
        },

        // {
        //     dataIndex: "machineStateCode",
        //     title: "设备状态",
        //     isTable: true,
        //     clearable: true,
        //     isForm: true,
        //     isSearch: true,
        //     valueType: "input",
        // },
        {
          dataIndex: "stateDescribe",
          title: "状态描述",
          isTable: true,
          isSearch: true,
          clearable: true,
          isForm: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "reportTime",
          title: "上报时间",
          isTable: true,
          clearable: true,
          isSearch: true,
          searchSlot: "reportTime",
          isForm: true,
          width: 150,
          // valueType: "input",
          valueType: "date-picker",
          // formatter: (row) => row.reportTime.substring(0, 10),
          // pickerFormat: "yyyy-MM-dd",
          // attrs: { "value-format": "yyyy-MM-dd HH:mm:ss" },
        },
        {
          dataIndex: "reportNum",
          title: "上报次数",
          isTable: true,
          clearable: true,
          isForm: true,
          valueType: "input",
        },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          isTable: true,
          clearable: true,
          isForm: true,
          valueType: "input",
          width: 100,
        },
        {
          dataIndex: "cyanCounter",
          title: "彩色计数器",
          isTable: true,
          isForm: true,
          clearable: true,
          valueType: "input",
          width: 100,
        },
        {
          dataIndex: "fifthCounter",
          title: "五色计数器",
          isTable: true,
          clearable: true,
          isForm: true,
          valueType: "input",
          width: 100,
        },
        {
          dataIndex: "action",
          tableSlot: "action",
          width: 100,
          title: "操作",
          isTable: true,
          tooltip: false,
          isSearch: false,
          fixed: "right",
        },
      ],
      queryParam: {},
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      selection: [],
      addDialog: false,
      dialogVisible: false,
      dialogTitle: "查看",
      confirmLoading: false,

      addForm: {},
      form: {
        name: "444",
      },
      methodType: "info",
      reportTime: null,
    };
  },

  mounted() {
    this.getProductThird();
    this.refresh();
  },
  methods: {
    async loadData(params) {
      this.queryParam = filterParam(Object.assign({}, this.queryParam, params));
      const searchRange = [
        {
          reportTimeStart: null,
          reportTimeEnd: null,
          data: params.reportTime,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      try {
        const result = await getStateApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        this.$message.error(error.this.$message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    handleSelectionChange(val) {
      this.selection = val;
    },
    // 刷新
    refresh() {
      this.$refs.ProTable.refresh();
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },
    exportCustom() {
      if (this.selection.length === 0) {
        this.$message.warning("请选择要导出的客户数据");
        return;
      }
      const fieldMap = {};
      this.columns
        .filter((item) => item.isExport === true)
        .map((item) => {
          fieldMap[item.dataIndex] = item.title;
        });
      exportExcel(this.selection, fieldMap, "客户列表");
    },
    handleEdit(row) {
      console.log(row);
      this.dialogVisible = true;
      this.form = deepClone(row);
      this.form.deviceGroup = row.deviceGroup.label;
    },
    handleSelect(arr) {
      this.queryParam.productList = arr.map((item) => item[item.length - 1]);
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
        // this.$refs.ProTable.refresh();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.custom {
  width: 100%;
}

.demo-ruleForm {
  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item:nth-child(9)) {
    width: 100%;

    .el-form-item__content {
      width: 69%;
    }
  }
}
</style>
