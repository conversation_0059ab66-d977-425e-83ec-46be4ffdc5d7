{"name": "jcfx", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@stagewise/toolbar": "^0.5.2", "@turf/turf": "^6.5.0", "@types/crypto-js": "^4.2.2", "axios": "^0.27.2", "core-js": "^3.8.3", "cos-js-sdk-v5": "^1.4.21", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "echarts": "^5.4.0", "element-ui": "^2.15.12", "file-saver": "^2.0.5", "FileSaver": "^0.10.0", "html2canvas": "^1.4.1", "intro.js": "^7.2.0", "js-cookie": "^3.0.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.30.1", "nanoid": "^5.0.9", "nprogress": "^0.2.0", "ol": "^7.3.0", "postcss-px-to-viewport": "^1.1.1", "postcss-pxtorem": "^5.1.1", "qrcode": "^1.5.4", "qs": "^6.11.0", "rxjs": "^7.5.7", "sass": "^1.53.0", "sass-loader": "^13.0.2", "sass-resources-loader": "^2.2.5", "sm-crypto": "^0.3.11", "vue": "^2.6.14", "vue-codemirror-lite": "^1.0.4", "vue-drag-resize": "^1.5.4", "vue-grid-layout": "^2.4.0", "vue-json-excel": "^0.3.0", "vue-jsonp": "^2.0.0", "vue-resource": "^1.5.3", "vue-router": "3.5.3", "vue-ruler-tool": "^1.0.8", "vue-uuid": "^2.1.0", "vue2-scale-box": "^0.1.7", "vuedraggable": "^2.24.3", "vuex": "~3.6.2", "vuex-map-fields": "^1.4.1", "wangeditor": "^4.7.7", "webpack": "^5.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}