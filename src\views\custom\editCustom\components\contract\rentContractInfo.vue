<!--
 * @Author: wskg
 * @Date: 2025-01-15 11:59:00
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-26 15:28:28
 * @Description: 购机合同明细
 -->
<template>
  <div class="app-container">
    <ProDialog
      :value="dialogVisible"
      title="租赁合约明细"
      width="75%"
      top="1%"
      :confirm-btn-loading="confirmLoading"
      :no-footer="editType === 'info'"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="ProForm"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :form-param="form"
        :form-list="formColumns"
        :open-type="editType"
        @proSubmit="formSubmit"
      >
        <template #hostType>
          <el-select
            v-model="form.hostType"
            placeholder="请选择主机类型"
            style="width: 100%"
            size="small"
            :disabled="true"
          >
            <el-option
              v-for="item in hostTypeListOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
        <template #productId>
          <el-cascader
            ref="ProductIds"
            v-model="form.productId"
            :options="options"
            style="width: 100%"
            :show-all-levels="false"
            :disabled="true"
            size="small"
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
              expandTrigger: 'click',
            }"
            leaf-only
          ></el-cascader>
        </template>
        <template #deviceOn>
          <el-select
            v-model="form.deviceOn"
            style="width: 100%"
            size="small"
            placeholder="请选择设备新旧"
            :disabled="true"
          >
            <el-option
              v-for="item in deviceOnOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
        <template #hasGive>
          <el-radio-group
            v-model="form.hasGive"
            :disabled="editType === 'info'"
          >
            <el-radio :label="false"> 无赠送 </el-radio>
            <el-radio :label="true"> 有赠送 </el-radio>
          </el-radio-group>
        </template>
        <!-- 赠送服务 -->
        <template v-if="form.hasGive" #customerContractGives>
          <GiftService
            ref="giftServiceRef"
            v-model="form"
            :edit-type="editType"
          />
        </template>
        <template #addToDeviceGroup>
          <el-checkbox
            v-model="form.addToDeviceGroup"
            border
            :disabled="editType === 'info'"
          >
            添加到设备组
          </el-checkbox>
        </template>
        <template #deviceGroupId>
          <el-select
            v-model="form.deviceGroupId"
            placeholder="请选择设备组"
            style="width: 100%"
            clearable
            :disabled="editType === 'info'"
          >
            <el-option
              v-for="item in deviceGroupOptions"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
        <template #serviceInfo>
          <!-- 分期付款 -->
          <!--<InstallmentPayment-->
          <!--  v-if="form.settleMethod === 'INSTALLMENT'"-->
          <!--  v-model="form"-->
          <!--/>-->
          <!-- 服务类型 -->
          <!-- 服务类型：质保 -->
          <!--<WarrantyContract v-if="form.serType === 'WARRANTY'" v-model="form" />-->
          <!-- 服务类型：全/半保 -->
          <FullHalfGuaranteed
            v-if="getServiceType(form.serType)"
            ref="serviceRef"
            v-model="form"
            :service-type="form.serType"
            :contract-type="contractType"
            :edit-type="editType"
          />
          <!-- 服务类型: 其它 -->
          <OtherService
            v-if="form.serType === 'OTHER'"
            ref="otherRef"
            v-model="form"
            :edit-type="editType"
          />
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import FullHalfGuaranteed from "@/views/custom/editCustom/components/contract/fullHalfGuaranteed.vue";
import OtherService from "@/views/custom/editCustom/components/contract/otherService.vue";
import GiftService from "@/views/custom/editCustom/components/contract/giftService.vue";
import { addAmount, filterParam, subtractAmount } from "@/utils";
import { validatePositiveNumber } from "@/utils/validate";
import { cloneDeep } from "lodash";
import { productAllApi } from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";
import { getCustomerDeviceListApi } from "@/api/customer";

export default {
  name: "RentMachineInfo",
  components: { FullHalfGuaranteed, OtherService, GiftService },
  props: {
    isSupplement: {
      type: Boolean,
      default: false,
    },
    contractType: {
      type: String,
      default: "",
    },
    customerId: {
      type: String,
      default: "",
    },
    // editType: {
    //   type: String,
    //   default: "add",
    // },
  },
  data() {
    return {
      dialogVisible: false,
      // form
      confirmLoading: false,
      form: {},
      defaultForm: {
        contractBuyGive: {}, // 购机明细
        contractServeGive: {}, // 服务明细
      },
      editType: "add",
      formColumns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isForm: true,
          disabled: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isForm: true,
          disabled: true,
          formSlot: "hostType",
          formSpan: 6,
        },
        {
          dataIndex: "productId",
          title: "品牌/机型",
          isForm: false,
          // valueType: "text",
          formSlot: "productId",
          formSpan: 6,
        },
        // {
        //   dataIndex: "brand",
        //   title: "品牌",
        //   isForm: true,
        //   disabled: true,
        //   valueType: "text",
        //   formSpan: 4,
        // },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isForm: false,
          disabled: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isForm: true,
          formSpan: 6,
          formSlot: "deviceOn",
        },
        {
          dataIndex: "installAmount",
          title: "安装费用",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请输入安装费用",
              trigger: "blur",
            },
            {
              validator: validatePositiveNumber,
              message: "安装费用不能小于0",
            },
          ],
        },
        {
          dataIndex: "installDate",
          title: "安装日期",
          isForm: true,
          valueType: "date-picker",
          pickerType: "date",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请选择安装日期",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "depositAmount",
          title: "押金",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 6,
          attrs: {
            suffix: "元",
          },
          prop: [
            {
              required: true,
              message: "请输入押金",
              trigger: "blur",
            },
            {
              validator: validatePositiveNumber,
              message: "押金不能小于0",
            },
          ],
        },
        {
          title: "初始黑白计数器",
          dataIndex: "initBlackWhiteCounter",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "input",
          inputType: "number",
          prop: [
            {
              validator: validatePositiveNumber,
              message: "计数器不能小于0",
              trigger: "change",
            },
          ],
        },
        {
          title: "初始彩色计数器",
          dataIndex: "initColorCounter",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "input",
          inputType: "number",
          prop: [
            {
              validator: validatePositiveNumber,
              message: "计数器不能小于0",
              trigger: "change",
            },
          ],
        },
        {
          title: "初始五色计数器",
          dataIndex: "initFiveCounter",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "input",
          inputType: "number",
          prop: [
            {
              validator: validatePositiveNumber,
              message: "计数器不能小于0",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          effect: "light",
          tooltipContent:
            "服务类型的切换可能会清除一些影响程序运行的数据，因此请谨慎操作，避免随意更改服务类型。",

          option: [
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "其它",
              value: "OTHER",
            },
          ],
          prop: [
            {
              required: true,
              message: "请选择服务类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "addToDeviceGroup",
          title: "添加机器信息",
          isForm: true,
          formSlot: "addToDeviceGroup",
          formSpan: 6,
        },
        {
          dataIndex: "deviceGroupId",
          title: "添加到现有设备组",
          isForm: true,
          formSlot: "deviceGroupId",
          formSpan: 6,
        },
        {
          dataIndex: "hasGive",
          title: "有无赠送",
          isForm: true,
          formSlot: "hasGive",
          formSpan: 6,
        },

        {
          dataIndex: "customerContractGives",
          title: "赠送物品",
          isForm: true,
          formOtherSlot: "customerContractGives",
          formSlot: 24,
        },

        {
          dataIndex: "serviceInfo",
          title: "服务信息",
          isForm: true,
          formOtherSlot: "serviceInfo",
          formSpan: 24,
        },
      ],
      hostTypeListOptions: [],
      deviceOnOptions: [],
      options: [],
      deviceGroupOptions: [],
    };
  },
  watch: {
    isSupplement: {
      handler(val) {
        if (val) {
          this.updateIsFormColumn(
            this.formColumns,
            ["addToDeviceGroup", "deviceGroupId"],
            true
          );
        } else {
          this.updateIsFormColumn(
            this.formColumns,
            ["addToDeviceGroup", "deviceGroupId"],
            false
          );
        }
      },
      immediate: true,
    },
    // // 总销售金额
    // "form.fullAmount": {
    //   handler(val) {
    //     this.calculateArrearsAmount();
    //   },
    // },
    // // 定金
    // "form.depositAmount": {
    //   handler(val) {
    //     this.calculateArrearsAmount();
    //   },
    // },
    // // 折扣金额
    // "form.discountAmount": {
    //   handler(val) {
    //     this.calculateArrearsAmount();
    //   },
    // },
  },
  methods: {
    visible(val, type) {
      this.editType = type;
      this.resetFormParams().then(() => {
        const formParams = {
          ...this.defaultForm,
          ...val,
        };
        Object.keys(formParams).forEach((key) => {
          if (key === "hostType" || key === "deviceOn") {
            return;
          }
          formParams[key] = formParams[key]?.label
            ? formParams[key].value
            : formParams[key];
        });
        this.form = formParams;
        if (this.form.hostType === "2008") {
          this.updateIsFormColumn(this.formColumns, ["productId"], true);
        } else {
          this.updateIsFormColumn(this.formColumns, ["productInfo"], true);
        }
        if (this.form.hasGive === undefined || this.form.hasGive === null) {
          this.$set(this.form, "hasGive", false);
        }
        if (
          this.isSupplement &&
          this.editType === "edit" &&
          this.form.isSupplement !== undefined
        ) {
          this.$set(this.form, "addToDeviceGroup", true);
        }

        this.calculateArrearsAmount();
        this.dialogVisible = true;
        this.getProductType();
        this.getDeviceOnOptions();
        this.getProductTreeOptions();
        this.getCustomerDeviceList();
      });
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    formSubmit(val) {
      const validationPromises = [];
      if (this.$refs.serviceRef && this.$refs.serviceRef.$refs.formRef) {
        validationPromises.push(
          new Promise((resolve) => {
            this.$refs.serviceRef.$refs.formRef.validate((valid) => {
              resolve(valid);
            });
          })
        );
      }
      if (this.$refs.otherRef && this.$refs.otherRef.$refs.formRef) {
        validationPromises.push(
          new Promise((resolve) => {
            this.$refs.otherRef.$refs.formRef.validate((valid) => {
              resolve(valid);
            });
          })
        );
      }
      // 等待所有验证完成
      Promise.all(validationPromises).then((results) => {
        if (results.every((result) => result)) {
          const result = cloneDeep(this.form);
          console.log(result, 'result.settleMethod === "INSTALLMENT"');
          if (result.settleMethod === "INSTALLMENT") {
            if (!result.tradeOrderInstallments.length) {
              this.$message.error("请填写分期信息");
              return;
            }
            // 检查分期列表的总金额是否等于欠款金额
            const totalAmount = result.tradeOrderInstallments.reduce(
              (acc, cur) => {
                return addAmount(acc, cur.amount);
              },
              0
            );
            if (totalAmount !== result.arrearsAmount) {
              this.$message.error("分期金额总和必须等于欠款金额");
              return;
            }
          }
          if (result.serveSettleMethod === "INSTALLMENT") {
            if (!result.serverInstallments.length) {
              this.$message.error("请填写分期信息");
              return;
            }
            // 检查分期列表的总金额是否等于欠款金额
            const totalAmount = result.serverInstallments.reduce((acc, cur) => {
              return addAmount(acc, cur.amount);
            }, 0);
            if (totalAmount !== result.serveArrersAmount) {
              this.$message.error("分期金额总和必须等于尾款");
              return;
            }
          }
          // if (result.priceType === "LADDER") {
          //   if (!result.repairMonthlyPrices.length) {
          //     return this.$message.error("请填写阶梯价格");
          //   }
          // }
          this.$emit("confirmContractInfo", result);
          this.dialogVisible = false;
        } else {
          this.$message.error("请将合约信息填写完整");
        }
      });
    },
    submitForm() {
      const result = cloneDeep(this.form);
      if (result.settleMethod === "INSTALLMENT") {
        if (!result.tradeOrderInstallments.length) {
          this.$message.error("请填写分期信息");
          return;
        }
        // 检查分期列表的总金额是否等于欠款金额
        const totalAmount = result.tradeOrderInstallments.reduce((acc, cur) => {
          return addAmount(acc, cur.amount);
        }, 0);
        if (totalAmount !== result.arrearsAmount) {
          this.$message.error("分期金额总和必须等于欠款金额");
          return;
        }
      }
      this.$emit("confirmContractInfo", filterParam(result));
      this.dialogVisible = false;
    },
    async getProductTreeOptions() {
      try {
        const result = await productAllApi();
        if (result.code === 200) {
          this.options = result.data;
        }
      } catch (error) {
        this.options = [];
      }
    },
    async getProductType() {
      try {
        const result = await dictTreeByCodeApi(2000);
        if (result.code === 200) {
          this.hostTypeListOptions = result.data;
        }
      } catch (error) {
        this.hostTypeListOptions = [];
      }
    },
    async getDeviceOnOptions() {
      try {
        const result = await dictTreeByCodeApi(1100);
        if (result.code === 200) {
          this.deviceOnOptions = result.data;
        }
      } catch (error) {
        this.deviceOnOptions = [];
      }
    },
    getServiceType(type) {
      return ["RENT_HALF", "RENT_FULL", "PACKAGE_HALF", "PACKAGE_ALL"].includes(
        type
      );
    },
    handleDialogCancel() {
      this.updateIsFormColumn(
        this.formColumns,
        ["productId", "productInfo"],
        false
      );
      this.dialogVisible = false;
    },
    calculateArrearsAmount() {
      const fullAmount = this.form.fullAmount || 0;
      const depositAmount = this.form.depositAmount || 0;
      const discountAmount = this.form.discountAmount || 0;
      if (fullAmount) {
        let arrearsAmount = fullAmount;

        if (depositAmount) {
          arrearsAmount = subtractAmount(arrearsAmount, depositAmount);
        }
        if (discountAmount) {
          arrearsAmount = subtractAmount(arrearsAmount, discountAmount);
        }
        this.form.arrearsAmount = Math.max(arrearsAmount, 0);
      } else {
        this.form.arrearsAmount = 0;
      }
    },
    updateIsFormColumn(columns, keys, isForm) {
      columns.forEach((item) => {
        if (keys.includes(item.dataIndex)) {
          item.isForm = isForm;
        }
      });
    },
    // 获取用户设备组列表
    async getCustomerDeviceList() {
      if (!this.customerId) {
        return;
      }
      try {
        const result = await getCustomerDeviceListApi(this.customerId);
        if (result.code === 200) {
          this.deviceGroupOptions = result.data.map((item) => {
            return {
              label: `${item.deviceGroup?.label}/${item.productInfo}`,
              value: item.id,
            };
          });
        }
      } catch (e) {
        this.deviceGroupOptions = [];
      }
    },
    resetFormParams() {
      return new Promise((resolve) => {
        Object.keys(this.form).forEach((key) => {
          delete this.form[key];
        });
        resolve();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.billing-method {
  .mb-0 {
    margin-bottom: 0;
  }

  .installment-details {
    .el-input-number {
      width: 100%;
    }
  }
}
</style>
