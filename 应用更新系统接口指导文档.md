# 应用更新系统接口指导文档

## 1. 概述

本文档为Web端管理系统和Android客户端提供应用更新系统的接口使用指导，包括定向发布和全局发布功能的完整API调用说明。

## 2. 基础信息

### 2.1 服务器地址
- **开发环境**: `http://dev-api.example.com`
- **测试环境**: `http://test-api.example.com`
- **生产环境**: `https://api.example.com`

### 2.2 通用响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据内容
  },
  "timestamp": 1640995200000
}
```

### 2.3 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 3. Android端接口

### 3.1 检查应用更新

#### 接口信息
- **URL**: `GET /api/app/update`
- **权限**: 无需认证（匿名访问）
- **说明**: 检查是否有可用的应用更新

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| currentVersionCode | Integer | 是 | 当前应用版本号 | 1 |
| deviceId | String | 否 | 设备唯一标识 | "device_123456" |
| userId | String | 否 | 用户ID（支持定向更新） | "1730205532934926338" |

#### 请求示例

```bash
# 基础调用（全局更新）
GET /api/app/update?currentVersionCode=1&deviceId=device123

# 支持定向更新的调用
GET /api/app/update?currentVersionCode=1&deviceId=device123&userId=1730205532934926338
```

#### 响应示例

**有更新时的响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "hasUpdate": true,
    "versionName": "1.0.2",
    "versionCode": 2,
    "downloadUrl": "https://cos-url/app-v1.0.2.apk",
    "updateLog": "<h3>🎉 版本更新内容</h3><ul><li><strong>修复重要问题</strong>：解决了应用崩溃问题</li><li><strong>性能优化</strong>：提升了启动速度50%</li><li><strong>新增功能</strong>：支持夜间模式</li></ul>",
    "isForce": false,
    "fileSize": 28000000,
    "fileMd5": "abc123def456789"
  }
}
```

**无更新时的响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "hasUpdate": false
  }
}
```

#### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| hasUpdate | Boolean | 是否有更新 |
| versionName | String | 新版本名称 |
| versionCode | Integer | 新版本号 |
| downloadUrl | String | APK下载地址 |
| updateLog | String | 更新日志（支持HTML富文本格式） |
| isForce | Boolean | 是否强制更新 |
| fileSize | Long | 文件大小（字节） |
| fileMd5 | String | 文件MD5校验值 |

### 3.2 下载APK文件

#### 接口信息
- **URL**: `GET /api/app/download/{versionId}`
- **权限**: 无需认证
- **说明**: 下载指定版本的APK文件

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| versionId | Long | 是 | 版本ID（路径参数） |
| deviceId | String | 否 | 设备ID（用于统计） |

#### 请求示例

```bash
GET /api/app/download/123?deviceId=device123
```

#### 响应说明
- 成功时：重定向到实际的下载链接（COS存储）
- 失败时：返回错误信息

## 4. Web端管理接口

### 4.1 版本列表查询

#### 接口信息
- **URL**: `GET /api/admin/app-version/page`
- **权限**: 需要管理员权限
- **说明**: 分页查询版本列表

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码（默认1） |
| pageSize | Integer | 否 | 每页大小（默认10） |
| versionName | String | 否 | 版本名称（模糊查询） |
| isActive | Boolean | 否 | 是否启用 |
| isForce | Boolean | 否 | 是否强制更新 |

#### 请求示例

```bash
GET /api/admin/app-version/page?pageNum=1&pageSize=10&versionName=1.0
```

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "pageNum": 1,
    "pageSize": 10,
    "pages": 3,
    "list": [
      {
        "id": 123,
        "versionName": "1.0.2",
        "versionCode": 2,
        "apkFileName": "app-v1.0.2.apk",
        "fileSize": 28000000,
        "fileMd5": "abc123def456",
        "updateLog": "<h3>版本 1.0.2 更新说明</h3><ul><li>修复重要问题</li><li>性能优化</li></ul>",
        "isForce": false,
        "adminForce": false,
        "isActive": true,
        "releaseType": "GLOBAL",
        "downloadCount": 156,
        "createdAt": "2025-01-29 10:30:00",
        "updatedAt": "2025-01-29 15:20:00"
      }
    ]
  }
}
```

### 4.2 发布新版本

#### 接口信息
- **URL**: `POST /api/admin/app-version/publish`
- **权限**: 需要管理员权限
- **说明**: 发布新的应用版本

#### 请求参数

```json
{
  "versionName": "1.0.3",
  "versionCode": 3,
  "apkFileName": "app-v1.0.3.apk",
  "cosKey": "app/app-v1.0.3.apk",
  "cosUrl": "https://cos-url/app/app-v1.0.3.apk",
  "fileSize": 29000000,
  "fileMd5": "def456abc789",
  "updateLog": "<h3>版本 1.0.3 更新内容</h3><ul><li><strong>新增功能</strong>：支持多语言切换</li><li><strong>性能优化</strong>：内存使用优化30%</li><li><strong>界面改进</strong>：全新的用户界面设计</li></ul>",
  "isForce": false,
  "isActive": true
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": 124
}
```

### 4.3 设置定向发布

#### 接口信息
- **URL**: `POST /api/admin/app-version/{versionId}/targeted-release`
- **权限**: 需要管理员权限
- **说明**: 为指定版本设置定向发布

#### 请求参数

```json
{
  "userIds": ["1730205532934926338", "1730205532934926339"],
  "deviceIds": ["device123", "device456"],
  "groupIds": ["group001", "group002"],
  "overrideExisting": true
}
```

#### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userIds | Array[String] | 否 | 目标用户ID列表 |
| deviceIds | Array[String] | 否 | 目标设备ID列表 |
| groupIds | Array[String] | 否 | 目标用户组ID列表 |
| overrideExisting | Boolean | 否 | 是否覆盖现有分发关系 |

#### 请求示例

```bash
POST /api/admin/app-version/123/targeted-release
Content-Type: application/json

{
  "userIds": ["1730205532934926338", "1730205532934926339"],
  "deviceIds": ["device123", "device456"],
  "overrideExisting": true
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 4.4 转为全局发布

#### 接口信息
- **URL**: `PUT /api/admin/app-version/{versionId}/global-release`
- **权限**: 需要管理员权限
- **说明**: 将定向发布版本转为全局发布

#### 请求示例

```bash
PUT /api/admin/app-version/123/global-release
```

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 4.5 更新版本信息

#### 接口信息
- **URL**: `PUT /api/admin/app-version/{versionId}`
- **权限**: 需要管理员权限
- **说明**: 更新版本基本信息

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| versionName | String | 是 | 版本名称 |
| updateLog | String | 否 | 更新说明（支持HTML富文本格式） |
| isForce | Boolean | 否 | 是否强制更新 |
| isActive | Boolean | 否 | 是否启用 |

#### 请求示例

```bash
PUT /api/admin/app-version/123?versionName=1.0.3&updateLog=修复问题&isForce=false&isActive=true
```

### 4.6 设置强制更新

#### 接口信息
- **URL**: `PUT /api/admin/app-version/{versionId}/force`
- **权限**: 需要管理员权限
- **说明**: 设置或取消强制更新标志

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| force | Boolean | 是 | 是否强制更新 |

#### 请求示例

```bash
PUT /api/admin/app-version/123/force?force=true
```

### 4.7 删除版本

#### 接口信息
- **URL**: `DELETE /api/admin/app-version/{versionId}`
- **权限**: 需要管理员权限
- **说明**: 删除指定版本（逻辑删除）

#### 请求示例

```bash
DELETE /api/admin/app-version/123
```

### 4.8 查看版本分发情况

#### 接口信息
- **URL**: `GET /api/admin/app-version/{versionId}/distributions`
- **权限**: 需要管理员权限
- **说明**: 查看版本的分发关系

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "targetType": "USER",
      "targetId": "1730205532934926338",
      "targetName": "测试用户1",
      "assignTime": "2025-01-29 10:30:00",
      "isActive": true
    },
    {
      "id": 2,
      "targetType": "DEVICE",
      "targetId": "device123",
      "targetName": "测试设备",
      "assignTime": "2025-01-29 10:31:00",
      "isActive": true
    }
  ]
}
```

## 5. Android端集成指南

### 5.1 更新检查流程

#### 5.1.1 基础更新检查

```java
public class UpdateManager {
    private static final String BASE_URL = "https://api.example.com";

    public void checkUpdate(int currentVersionCode, String deviceId, String userId) {
        String url = BASE_URL + "/api/app/update";

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("currentVersionCode", currentVersionCode);
        if (deviceId != null) {
            params.put("deviceId", deviceId);
        }
        if (userId != null) {
            params.put("userId", userId);
        }

        // 发起网络请求
        OkHttpClient client = new OkHttpClient();
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            urlBuilder.addQueryParameter(entry.getKey(), String.valueOf(entry.getValue()));
        }

        Request request = new Request.Builder()
                .url(urlBuilder.build())
                .get()
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                // 处理网络错误
                handleUpdateCheckError(e);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    parseUpdateResponse(responseBody);
                } else {
                    handleUpdateCheckError(new Exception("HTTP " + response.code()));
                }
            }
        });
    }

    private void parseUpdateResponse(String responseBody) {
        try {
            JSONObject json = new JSONObject(responseBody);
            if (json.getInt("code") == 200) {
                JSONObject data = json.getJSONObject("data");
                boolean hasUpdate = data.getBoolean("hasUpdate");

                if (hasUpdate) {
                    UpdateInfo updateInfo = new UpdateInfo();
                    updateInfo.setVersionName(data.getString("versionName"));
                    updateInfo.setVersionCode(data.getInt("versionCode"));
                    updateInfo.setDownloadUrl(data.getString("downloadUrl"));
                    updateInfo.setUpdateLog(data.getString("updateLog")); // HTML富文本内容
                    updateInfo.setForce(data.getBoolean("isForce"));
                    updateInfo.setFileSize(data.getLong("fileSize"));
                    updateInfo.setFileMd5(data.getString("fileMd5"));

                    // 显示更新对话框
                    showUpdateDialog(updateInfo);
                } else {
                    // 无更新
                    onNoUpdateAvailable();
                }
            } else {
                handleUpdateCheckError(new Exception(json.getString("message")));
            }
        } catch (JSONException e) {
            handleUpdateCheckError(e);
        }
    }
}
```

#### 5.1.2 更新信息实体类

```java
public class UpdateInfo {
    private String versionName;
    private int versionCode;
    private String downloadUrl;
    private String updateLog;
    private boolean isForce;
    private long fileSize;
    private String fileMd5;

    // Getters and Setters
    public String getVersionName() { return versionName; }
    public void setVersionName(String versionName) { this.versionName = versionName; }

    public int getVersionCode() { return versionCode; }
    public void setVersionCode(int versionCode) { this.versionCode = versionCode; }

    public String getDownloadUrl() { return downloadUrl; }
    public void setDownloadUrl(String downloadUrl) { this.downloadUrl = downloadUrl; }

    public String getUpdateLog() { return updateLog; }
    public void setUpdateLog(String updateLog) { this.updateLog = updateLog; }

    public boolean isForce() { return isForce; }
    public void setForce(boolean force) { isForce = force; }

    public long getFileSize() { return fileSize; }
    public void setFileSize(long fileSize) { this.fileSize = fileSize; }

    public String getFileMd5() { return fileMd5; }
    public void setFileMd5(String fileMd5) { this.fileMd5 = fileMd5; }

    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }

    /**
     * 获取纯文本更新日志（去除HTML标签）
     * @return 纯文本更新日志
     */
    public String getPlainTextUpdateLog() {
        if (updateLog == null) return "";
        return updateLog.replaceAll("<[^>]*>", "").trim();
    }
}
```

### 5.2 APK下载和安装

#### 5.2.1 下载管理器

```java
public class DownloadManager {
    private Context context;
    private DownloadProgressListener progressListener;

    public interface DownloadProgressListener {
        void onProgress(int progress);
        void onSuccess(File apkFile);
        void onError(Exception e);
    }

    public void downloadApk(UpdateInfo updateInfo, DownloadProgressListener listener) {
        this.progressListener = listener;

        String downloadUrl = updateInfo.getDownloadUrl();
        String fileName = "app_update_" + updateInfo.getVersionCode() + ".apk";
        File downloadDir = new File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "updates");
        if (!downloadDir.exists()) {
            downloadDir.mkdirs();
        }
        File apkFile = new File(downloadDir, fileName);

        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();

        Request request = new Request.Builder()
                .url(downloadUrl)
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                if (progressListener != null) {
                    progressListener.onError(e);
                }
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    saveApkFile(response, apkFile, updateInfo);
                } else {
                    if (progressListener != null) {
                        progressListener.onError(new Exception("Download failed: " + response.code()));
                    }
                }
            }
        });
    }

    private void saveApkFile(Response response, File apkFile, UpdateInfo updateInfo) {
        try (InputStream inputStream = response.body().byteStream();
             FileOutputStream outputStream = new FileOutputStream(apkFile)) {

            long totalBytes = response.body().contentLength();
            long downloadedBytes = 0;
            byte[] buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                downloadedBytes += bytesRead;

                if (totalBytes > 0 && progressListener != null) {
                    int progress = (int) ((downloadedBytes * 100) / totalBytes);
                    progressListener.onProgress(progress);
                }
            }

            // 验证文件完整性
            if (verifyApkFile(apkFile, updateInfo.getFileMd5())) {
                if (progressListener != null) {
                    progressListener.onSuccess(apkFile);
                }
            } else {
                if (progressListener != null) {
                    progressListener.onError(new Exception("File verification failed"));
                }
            }

        } catch (IOException e) {
            if (progressListener != null) {
                progressListener.onError(e);
            }
        }
    }

    private boolean verifyApkFile(File apkFile, String expectedMd5) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            try (FileInputStream fis = new FileInputStream(apkFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    md.update(buffer, 0, bytesRead);
                }
            }

            StringBuilder sb = new StringBuilder();
            for (byte b : md.digest()) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString().equalsIgnoreCase(expectedMd5);
        } catch (Exception e) {
            return false;
        }
    }
}
```

#### 5.2.2 APK安装

```java
public class InstallManager {
    private Context context;

    public InstallManager(Context context) {
        this.context = context;
    }

    public void installApk(File apkFile) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            installApkForN(apkFile);
        } else {
            installApkBelowN(apkFile);
        }
    }

    @TargetApi(Build.VERSION_CODES.N)
    private void installApkForN(File apkFile) {
        Uri apkUri = FileProvider.getUriForFile(context,
                context.getPackageName() + ".fileprovider", apkFile);

        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

        try {
            context.startActivity(intent);
        } catch (ActivityNotFoundException e) {
            // 处理无法找到安装器的情况
            showInstallError("无法找到应用安装器");
        }
    }

    private void installApkBelowN(File apkFile) {
        Uri apkUri = Uri.fromFile(apkFile);

        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        try {
            context.startActivity(intent);
        } catch (ActivityNotFoundException e) {
            showInstallError("无法找到应用安装器");
        }
    }

    private void showInstallError(String message) {
        // 显示错误提示
        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
    }
}
```

### 5.3 富文本渲染支持

#### 5.3.1 富文本渲染组件

```java
public class RichTextView extends WebView {

    public RichTextView(Context context) {
        super(context);
        initWebView();
    }

    public RichTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initWebView();
    }

    private void initWebView() {
        WebSettings settings = getSettings();
        settings.setJavaScriptEnabled(false); // 安全考虑，禁用JS
        settings.setDefaultTextEncodingName("UTF-8");
        settings.setLoadWithOverviewMode(true);
        settings.setUseWideViewPort(true);

        // 设置背景透明
        setBackgroundColor(Color.TRANSPARENT);

        // 禁用滚动条
        setVerticalScrollBarEnabled(false);
        setHorizontalScrollBarEnabled(false);
    }

    /**
     * 设置富文本内容
     * @param htmlContent HTML格式的更新日志
     */
    public void setRichText(String htmlContent) {
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            loadData("暂无更新说明", "text/html; charset=UTF-8", null);
            return;
        }

        // 构建完整的HTML页面
        String html = buildHtmlPage(htmlContent);
        loadData(html, "text/html; charset=UTF-8", null);
    }

    private String buildHtmlPage(String content) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<meta name='viewport' content='width=device-width, initial-scale=1.0'>" +
                "<style>" +
                "body { " +
                "  font-family: 'Roboto', sans-serif; " +
                "  font-size: 14px; " +
                "  line-height: 1.6; " +
                "  color: #333; " +
                "  margin: 0; " +
                "  padding: 16px; " +
                "  background-color: transparent; " +
                "} " +
                "h1, h2, h3 { color: #2c3e50; margin-top: 0; margin-bottom: 12px; } " +
                "h3 { font-size: 16px; } " +
                "ul, ol { padding-left: 20px; margin-bottom: 12px; } " +
                "li { margin-bottom: 6px; } " +
                "p { margin-bottom: 12px; } " +
                "strong { color: #e74c3c; font-weight: bold; } " +
                "em { color: #3498db; font-style: italic; } " +
                "code { " +
                "  background-color: #f8f9fa; " +
                "  padding: 2px 4px; " +
                "  border-radius: 3px; " +
                "  font-family: 'Courier New', monospace; " +
                "  font-size: 12px; " +
                "} " +
                "blockquote { " +
                "  border-left: 4px solid #3498db; " +
                "  padding-left: 12px; " +
                "  margin: 12px 0; " +
                "  color: #7f8c8d; " +
                "} " +
                "</style>" +
                "</head>" +
                "<body>" +
                content +
                "</body>" +
                "</html>";
    }
}
```

### 5.4 更新对话框

#### 5.4.1 支持富文本的更新提示对话框

```java
public class UpdateDialogManager {

    public static void showUpdateDialog(Context context, UpdateInfo updateInfo,
                                      OnUpdateActionListener listener) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);

        // 自定义布局
        View dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_update_rich, null);

        TextView tvVersionName = dialogView.findViewById(R.id.tv_version_name);
        TextView tvFileSize = dialogView.findViewById(R.id.tv_file_size);
        RichTextView richTextView = dialogView.findViewById(R.id.rich_text_update_log);
        Button btnUpdate = dialogView.findViewById(R.id.btn_update);
        Button btnCancel = dialogView.findViewById(R.id.btn_cancel);

        tvVersionName.setText("发现新版本: " + updateInfo.getVersionName());
        tvFileSize.setText("安装包大小: " + updateInfo.getFormattedFileSize());

        // 设置富文本内容
        richTextView.setRichText(updateInfo.getUpdateLog());

        builder.setView(dialogView);
        AlertDialog dialog = builder.create();

        btnUpdate.setOnClickListener(v -> {
            dialog.dismiss();
            if (listener != null) {
                listener.onUpdate(updateInfo);
            }
        });

        if (updateInfo.isForce()) {
            // 强制更新，隐藏取消按钮
            btnCancel.setVisibility(View.GONE);
            dialog.setCancelable(false);
        } else {
            btnCancel.setOnClickListener(v -> {
                dialog.dismiss();
                if (listener != null) {
                    listener.onCancel();
                }
            });
        }

        dialog.show();
    }

    public interface OnUpdateActionListener {
        void onUpdate(UpdateInfo updateInfo);
        void onCancel();
    }
}
```

#### 5.4.2 富文本更新对话框布局文件

```xml
<!-- res/layout/dialog_update_rich.xml -->
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/bg_dialog_rounded">

    <TextView
        android:id="@+id/tv_version_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="发现新版本: 1.0.2"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="#2c3e50"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tv_file_size"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="安装包大小: 25.6 MB"
        android:textSize="14sp"
        android:textColor="#7f8c8d"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="更新内容："
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#2c3e50"
        android:layout_marginBottom="8dp" />

    <!-- 富文本显示区域 -->
    <com.yourpackage.widget.RichTextView
        android:id="@+id/rich_text_update_log"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/bg_rounded_border"
        android:padding="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="稍后更新"
            android:background="@drawable/btn_secondary"
            android:textColor="#7f8c8d"
            android:layout_marginEnd="12dp"
            android:paddingHorizontal="20dp"
            android:paddingVertical="10dp" />

        <Button
            android:id="@+id/btn_update"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="立即更新"
            android:background="@drawable/btn_primary"
            android:textColor="#ffffff"
            android:paddingHorizontal="20dp"
            android:paddingVertical="10dp" />

    </LinearLayout>

</LinearLayout>
```

#### 5.4.3 样式资源文件

```xml
<!-- res/drawable/bg_dialog_rounded.xml -->
<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android">
    <solid android:color="#ffffff" />
    <corners android:radius="12dp" />
    <stroke android:width="1dp" android:color="#e8e8e8" />
</shape>

<!-- res/drawable/bg_rounded_border.xml -->
<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android">
    <solid android:color="#f8f9fa" />
    <corners android:radius="8dp" />
    <stroke android:width="1dp" android:color="#dee2e6" />
</shape>

<!-- res/drawable/btn_primary.xml -->
<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android">
    <solid android:color="#3498db" />
    <corners android:radius="6dp" />
</shape>

<!-- res/drawable/btn_secondary.xml -->
<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android">
    <solid android:color="#ecf0f1" />
    <corners android:radius="6dp" />
    <stroke android:width="1dp" android:color="#bdc3c7" />
</shape>
```

### 5.5 完整使用示例

```java
public class MainActivity extends AppCompatActivity {
    private UpdateManager updateManager;
    private DownloadManager downloadManager;
    private InstallManager installManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        updateManager = new UpdateManager();
        downloadManager = new DownloadManager(this);
        installManager = new InstallManager(this);

        // 检查更新
        checkForUpdates();
    }

    private void checkForUpdates() {
        int currentVersionCode = getCurrentVersionCode();
        String deviceId = getDeviceId();
        String userId = getCurrentUserId(); // 如果支持定向更新

        updateManager.checkUpdate(currentVersionCode, deviceId, userId);
    }

    private void showUpdateDialog(UpdateInfo updateInfo) {
        UpdateDialogManager.showUpdateDialog(this, updateInfo,
            new UpdateDialogManager.OnUpdateActionListener() {
                @Override
                public void onUpdate(UpdateInfo updateInfo) {
                    startDownload(updateInfo);
                }

                @Override
                public void onCancel() {
                    // 用户取消更新
                }
            });
    }

    private void startDownload(UpdateInfo updateInfo) {
        // 显示下载进度对话框
        ProgressDialog progressDialog = new ProgressDialog(this);
        progressDialog.setTitle("正在下载更新");
        progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
        progressDialog.setMax(100);
        progressDialog.setCancelable(false);
        progressDialog.show();

        downloadManager.downloadApk(updateInfo, new DownloadManager.DownloadProgressListener() {
            @Override
            public void onProgress(int progress) {
                runOnUiThread(() -> progressDialog.setProgress(progress));
            }

            @Override
            public void onSuccess(File apkFile) {
                runOnUiThread(() -> {
                    progressDialog.dismiss();
                    installManager.installApk(apkFile);
                });
            }

            @Override
            public void onError(Exception e) {
                runOnUiThread(() -> {
                    progressDialog.dismiss();
                    Toast.makeText(MainActivity.this,
                        "下载失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    private int getCurrentVersionCode() {
        try {
            PackageInfo packageInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
            return packageInfo.versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            return 1;
        }
    }

    private String getDeviceId() {
        return Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
    }

    private String getCurrentUserId() {
        // 返回当前登录用户的ID，如果支持定向更新的话
        SharedPreferences prefs = getSharedPreferences("user_prefs", MODE_PRIVATE);
        return prefs.getString("user_id", null);
    }
}
```

## 6. Web端集成指南

### 6.1 富文本编辑器集成

#### 6.1.1 Quill富文本编辑器组件 (Vue)

```vue
<template>
  <div class="rich-editor-container">
    <!-- 富文本编辑器 -->
    <div class="editor-wrapper">
      <quill-editor
        v-model="updateLogContent"
        :options="editorOptions"
        @change="onEditorChange">
      </quill-editor>
    </div>

    <!-- 预览区域 -->
    <div class="preview-container" v-if="showPreview">
      <div class="preview-header">
        <h4>预览效果：</h4>
        <el-button size="mini" @click="showPreview = false">隐藏预览</el-button>
      </div>
      <div v-html="updateLogContent" class="rich-content-preview"></div>
    </div>

    <!-- 预览按钮 -->
    <div class="editor-actions" v-if="!showPreview">
      <el-button size="small" @click="showPreview = true">预览效果</el-button>
      <el-button size="small" @click="clearContent">清空内容</el-button>
    </div>
  </div>
</template>

<script>
import { quillEditor } from 'vue-quill-editor'
import 'quill/dist/quill.snow.css'

export default {
  name: 'RichTextEditor',
  components: {
    quillEditor
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入更新说明...'
    }
  },
  data() {
    return {
      updateLogContent: this.value,
      showPreview: false,
      editorOptions: {
        theme: 'snow',
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'],
            ['blockquote', 'code-block'],
            [{ 'header': 1 }, { 'header': 2 }, { 'header': 3 }],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'align': [] }],
            ['link'],
            ['clean']
          ]
        },
        placeholder: this.placeholder
      }
    }
  },
  watch: {
    value(newVal) {
      this.updateLogContent = newVal
    },
    updateLogContent(newVal) {
      this.$emit('input', newVal)
    }
  },
  methods: {
    onEditorChange(content) {
      this.updateLogContent = content
      this.$emit('change', content)
    },

    clearContent() {
      this.$confirm('确定要清空所有内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.updateLogContent = ''
      })
    }
  }
}
</script>

<style scoped>
.rich-editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-wrapper {
  min-height: 200px;
}

.editor-actions {
  padding: 10px;
  background-color: #f5f7fa;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.preview-container {
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f0f2f5;
  border-bottom: 1px solid #e4e7ed;
}

.preview-header h4 {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.rich-content-preview {
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
  line-height: 1.6;
}

.rich-content-preview h1,
.rich-content-preview h2,
.rich-content-preview h3 {
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 12px;
}

.rich-content-preview ul,
.rich-content-preview ol {
  padding-left: 20px;
  margin-bottom: 12px;
}

.rich-content-preview li {
  margin-bottom: 6px;
}

.rich-content-preview p {
  margin-bottom: 12px;
}

.rich-content-preview strong {
  color: #e74c3c;
  font-weight: bold;
}

.rich-content-preview code {
  background-color: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.rich-content-preview blockquote {
  border-left: 4px solid #3498db;
  padding-left: 12px;
  margin: 12px 0;
  color: #7f8c8d;
}
</style>
```

#### 6.1.2 在版本发布表单中使用富文本编辑器

```vue
<template>
  <el-dialog title="发布新版本" :visible.sync="dialogVisible" width="800px">
    <el-form :model="form" :rules="rules" ref="versionForm" label-width="120px">
      <!-- 其他表单项... -->

      <el-form-item label="更新说明" prop="updateLog">
        <rich-text-editor
          v-model="form.updateLog"
          placeholder="请输入版本更新说明，支持富文本格式..."
          @change="onUpdateLogChange">
        </rich-text-editor>
      </el-form-item>

      <!-- 其他表单项... -->
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm" :loading="submitting">发布版本</el-button>
    </div>
  </el-dialog>
</template>

<script>
import RichTextEditor from './RichTextEditor.vue'

export default {
  components: {
    RichTextEditor
  },
  data() {
    return {
      form: {
        versionName: '',
        versionCode: '',
        updateLog: '',
        isForce: false,
        isActive: true
      },
      rules: {
        updateLog: [
          { required: true, message: '请输入更新说明', trigger: 'blur' },
          { min: 10, message: '更新说明至少10个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    onUpdateLogChange(content) {
      // 富文本内容变化时的处理
      console.log('更新说明内容变化:', content)
    },

    async submitForm() {
      try {
        await this.$refs.versionForm.validate()

        const formData = {
          ...this.form,
          updateLog: this.form.updateLog // HTML格式的富文本内容
        }

        await this.$api.publishVersion(formData)
        this.$message.success('版本发布成功！')
        this.dialogVisible = false
        this.$emit('success')
      } catch (error) {
        this.$message.error('发布失败：' + error.message)
      }
    }
  }
}
</script>
```

### 6.2 版本管理页面

#### 6.2.1 版本列表组件 (React)

```jsx
import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Tag, Modal, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

const VersionManagement = () => {
  const [versions, setVersions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  const columns = [
    {
      title: '版本名称',
      dataIndex: 'versionName',
      key: 'versionName',
    },
    {
      title: '版本号',
      dataIndex: 'versionCode',
      key: 'versionCode',
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      render: (size) => formatFileSize(size),
    },
    {
      title: '发布类型',
      dataIndex: 'releaseType',
      key: 'releaseType',
      render: (type) => (
        <Tag color={type === 'GLOBAL' ? 'green' : 'blue'}>
          {type === 'GLOBAL' ? '全局发布' : '定向发布'}
        </Tag>
      ),
    },
    {
      title: '强制更新',
      dataIndex: 'isForce',
      key: 'isForce',
      render: (isForce) => (
        <Tag color={isForce ? 'red' : 'default'}>
          {isForce ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'success' : 'default'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '下载次数',
      dataIndex: 'downloadCount',
      key: 'downloadCount',
    },
    {
      title: '更新说明',
      dataIndex: 'updateLog',
      key: 'updateLog',
      width: 200,
      render: (updateLog) => (
        <div className="update-log-cell">
          <div
            className="preview-text"
            dangerouslySetInnerHTML={{
              __html: getPreviewText(updateLog)
            }}
          />
          <Button
            type="link"
            size="small"
            onClick={() => showUpdateLogModal(updateLog)}
          >
            查看详情
          </Button>
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          {record.releaseType === 'TARGETED' && (
            <Button
              type="link"
              onClick={() => handleSetGlobal(record.id)}
            >
              转全局
            </Button>
          )}
          {record.releaseType === 'GLOBAL' && (
            <Button
              type="link"
              onClick={() => handleSetTargeted(record.id)}
            >
              设定向
            </Button>
          )}
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    fetchVersions();
  }, [pagination.current, pagination.pageSize]);

  const fetchVersions = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/app-version/page?pageNum=${pagination.current}&pageSize=${pagination.pageSize}`);
      const result = await response.json();

      if (result.code === 200) {
        setVersions(result.data.list);
        setPagination({
          ...pagination,
          total: result.data.total
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('获取版本列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSetGlobal = async (versionId) => {
    try {
      const response = await fetch(`/api/admin/app-version/${versionId}/global-release`, {
        method: 'PUT',
      });
      const result = await response.json();

      if (result.code === 200) {
        message.success('已转为全局发布');
        fetchVersions();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  const handleSetTargeted = (versionId) => {
    // 打开定向发布设置对话框
    showTargetedReleaseModal(versionId);
  };

  const formatFileSize = (bytes) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  const getPreviewText = (html) => {
    if (!html) return '暂无说明';

    // 移除HTML标签，只显示纯文本预览
    const text = html.replace(/<[^>]*>/g, '');
    return text.length > 50 ? text.substring(0, 50) + '...' : text;
  };

  const showUpdateLogModal = (updateLog) => {
    Modal.info({
      title: '更新说明详情',
      width: 600,
      content: (
        <div
          style={{
            maxHeight: '400px',
            overflow: 'auto',
            padding: '10px',
            lineHeight: '1.6'
          }}
          dangerouslySetInnerHTML={{ __html: updateLog }}
        />
      ),
      okText: '关闭'
    });
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" icon={<PlusOutlined />}>
          发布新版本
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={versions}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
        onChange={(pag) => setPagination(pag)}
        rowKey="id"
      />
    </div>
  );
};

export default VersionManagement;
```

#### 6.2.2 版本列表样式

```css
.update-log-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.preview-text {
  flex: 1;
  margin-right: 10px;
  font-size: 12px;
  color: #666;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.preview-text h1,
.preview-text h2,
.preview-text h3 {
  display: inline;
  font-size: inherit;
  font-weight: bold;
  margin: 0;
}

.preview-text strong {
  color: #e74c3c;
  font-weight: bold;
}

.preview-text ul,
.preview-text ol {
  display: inline;
  list-style: none;
  padding: 0;
  margin: 0;
}

.preview-text li {
  display: inline;
}

.preview-text li:not(:last-child):after {
  content: '; ';
}
```

#### 6.2.3 定向发布设置组件

```jsx
import React, { useState } from 'react';
import { Modal, Form, Select, Input, Switch, message } from 'antd';

const { Option } = Select;
const { TextArea } = Input;

const TargetedReleaseModal = ({ visible, versionId, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const requestData = {
        userIds: values.userIds || [],
        deviceIds: values.deviceIds || [],
        groupIds: values.groupIds || [],
        overrideExisting: values.overrideExisting || false
      };

      const response = await fetch(`/api/admin/app-version/${versionId}/targeted-release`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();

      if (result.code === 200) {
        message.success('定向发布设置成功');
        form.resetFields();
        onSuccess();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('设置失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="设置定向发布"
      visible={visible}
      onCancel={onCancel}
      onOk={() => form.submit()}
      confirmLoading={loading}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          name="userIds"
          label="目标用户ID"
          help="输入用户ID，多个用户用逗号分隔"
        >
          <TextArea
            rows={3}
            placeholder="例如: 1730205532934926338,1730205532934926339"
          />
        </Form.Item>

        <Form.Item
          name="deviceIds"
          label="目标设备ID"
          help="输入设备ID，多个设备用逗号分隔"
        >
          <TextArea
            rows={3}
            placeholder="例如: device123,device456"
          />
        </Form.Item>

        <Form.Item
          name="groupIds"
          label="目标用户组"
          help="选择用户组"
        >
          <Select
            mode="multiple"
            placeholder="选择用户组"
            allowClear
          >
            <Option value="group001">测试组</Option>
            <Option value="group002">内部用户组</Option>
            <Option value="group003">VIP用户组</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="overrideExisting"
          label="覆盖现有分发关系"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TargetedReleaseModal;
```

### 6.3 API调用工具类

#### 6.3.1 HTTP客户端封装

```javascript
class ApiClient {
  constructor(baseURL = '/api') {
    this.baseURL = baseURL;
  }

  async request(url, options = {}) {
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // 添加认证token
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    try {
      const response = await fetch(`${this.baseURL}${url}`, config);
      const result = await response.json();

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.message || '请求失败');
      }
    } catch (error) {
      console.error('API请求错误:', error);
      throw error;
    }
  }

  // GET请求
  async get(url, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const fullUrl = queryString ? `${url}?${queryString}` : url;

    return this.request(fullUrl, {
      method: 'GET',
    });
  }

  // POST请求
  async post(url, data = {}) {
    return this.request(url, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // PUT请求
  async put(url, data = {}) {
    return this.request(url, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // DELETE请求
  async delete(url) {
    return this.request(url, {
      method: 'DELETE',
    });
  }
}

export default new ApiClient();
```

#### 6.3.2 版本管理API封装

```javascript
import ApiClient from './ApiClient';

export const VersionAPI = {
  // 获取版本列表
  async getVersionList(params) {
    return ApiClient.get('/admin/app-version/page', params);
  },

  // 发布新版本
  async publishVersion(versionData) {
    return ApiClient.post('/admin/app-version/publish', versionData);
  },

  // 更新版本信息
  async updateVersion(versionId, updateData) {
    return ApiClient.put(`/admin/app-version/${versionId}`, updateData);
  },

  // 删除版本
  async deleteVersion(versionId) {
    return ApiClient.delete(`/admin/app-version/${versionId}`);
  },

  // 设置定向发布
  async setTargetedRelease(versionId, targetData) {
    return ApiClient.post(`/admin/app-version/${versionId}/targeted-release`, targetData);
  },

  // 转为全局发布
  async setGlobalRelease(versionId) {
    return ApiClient.put(`/admin/app-version/${versionId}/global-release`);
  },

  // 设置强制更新
  async setForceUpdate(versionId, force) {
    return ApiClient.put(`/admin/app-version/${versionId}/force`, { force });
  },

  // 获取版本分发情况
  async getVersionDistributions(versionId) {
    return ApiClient.get(`/admin/app-version/${versionId}/distributions`);
  },
};
```

## 7. 富文本内容最佳实践

### 7.1 富文本内容规范

#### 7.1.1 推荐的HTML结构

```html
<!-- 推荐的更新日志HTML结构 -->
<h3>🎉 版本 1.0.3 更新内容</h3>
<ul>
  <li><strong>新增功能</strong>：支持多语言切换</li>
  <li><strong>性能优化</strong>：内存使用优化30%</li>
  <li><strong>界面改进</strong>：全新的用户界面设计</li>
  <li><strong>问题修复</strong>：修复了已知的崩溃问题</li>
</ul>

<h4>📋 详细说明</h4>
<p>本次更新重点关注用户体验和应用性能，主要改进包括：</p>
<ol>
  <li>多语言支持：新增英文、日文界面</li>
  <li>性能提升：启动速度提升<em>50%</em></li>
  <li>界面优化：采用Material Design 3.0设计规范</li>
</ol>

<blockquote>
  <p><strong>重要提示</strong>：本次更新后需要重新登录账户</p>
</blockquote>
```

#### 7.1.2 支持的HTML标签

| 标签 | 用途 | 示例 |
|------|------|------|
| `<h1>`, `<h2>`, `<h3>` | 标题 | `<h3>版本更新</h3>` |
| `<p>` | 段落 | `<p>这是一个段落</p>` |
| `<ul>`, `<ol>`, `<li>` | 列表 | `<ul><li>项目1</li></ul>` |
| `<strong>`, `<b>` | 粗体强调 | `<strong>重要</strong>` |
| `<em>`, `<i>` | 斜体强调 | `<em>强调内容</em>` |
| `<code>` | 代码 | `<code>version 1.0</code>` |
| `<blockquote>` | 引用 | `<blockquote>引用内容</blockquote>` |

#### 7.1.3 样式建议

```css
/* 推荐的富文本样式 */
.rich-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.rich-content h1, .rich-content h2, .rich-content h3 {
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 12px;
}

.rich-content h3 {
  font-size: 16px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 4px;
}

.rich-content ul, .rich-content ol {
  padding-left: 20px;
  margin-bottom: 12px;
}

.rich-content li {
  margin-bottom: 6px;
}

.rich-content strong {
  color: #e74c3c;
  font-weight: bold;
}

.rich-content em {
  color: #3498db;
  font-style: italic;
}

.rich-content code {
  background-color: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.rich-content blockquote {
  border-left: 4px solid #3498db;
  padding-left: 12px;
  margin: 12px 0;
  color: #7f8c8d;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
}
```

### 7.2 内容安全和验证

#### 7.2.1 HTML内容过滤

```javascript
// 前端HTML内容过滤函数
function sanitizeHtml(html) {
  // 允许的标签
  const allowedTags = ['h1', 'h2', 'h3', 'p', 'ul', 'ol', 'li', 'strong', 'b', 'em', 'i', 'code', 'blockquote'];

  // 创建临时DOM元素
  const temp = document.createElement('div');
  temp.innerHTML = html;

  // 递归过滤不允许的标签
  function filterNode(node) {
    if (node.nodeType === Node.ELEMENT_NODE) {
      if (!allowedTags.includes(node.tagName.toLowerCase())) {
        // 不允许的标签，保留文本内容
        const textNode = document.createTextNode(node.textContent);
        node.parentNode.replaceChild(textNode, node);
        return;
      }

      // 移除所有属性（防止XSS）
      while (node.attributes.length > 0) {
        node.removeAttribute(node.attributes[0].name);
      }

      // 递归处理子节点
      Array.from(node.childNodes).forEach(filterNode);
    }
  }

  Array.from(temp.childNodes).forEach(filterNode);
  return temp.innerHTML;
}
```

#### 7.2.2 后端内容验证

```java
// Java后端HTML内容验证
@Component
public class UpdateLogValidator {

    private static final int MAX_LENGTH = 10000; // 最大长度限制
    private static final Pattern SCRIPT_PATTERN = Pattern.compile("<script[^>]*>.*?</script>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    private static final Pattern STYLE_PATTERN = Pattern.compile("<style[^>]*>.*?</style>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

    public String validateAndSanitize(String updateLog) {
        if (updateLog == null || updateLog.trim().isEmpty()) {
            return "";
        }

        // 长度检查
        if (updateLog.length() > MAX_LENGTH) {
            throw new IllegalArgumentException("更新说明内容过长，最大支持" + MAX_LENGTH + "个字符");
        }

        // 移除危险标签
        String sanitized = updateLog;
        sanitized = SCRIPT_PATTERN.matcher(sanitized).replaceAll("");
        sanitized = STYLE_PATTERN.matcher(sanitized).replaceAll("");

        // 移除危险属性
        sanitized = sanitized.replaceAll("(?i)\\s*on\\w+\\s*=\\s*[\"'][^\"']*[\"']", "");
        sanitized = sanitized.replaceAll("(?i)\\s*javascript\\s*:", "");

        return sanitized.trim();
    }
}
```

## 8. 错误处理和最佳实践

### 8.1 Android端最佳实践

1. **网络请求超时设置**：建议连接超时30秒，读取超时60秒
2. **文件完整性验证**：下载完成后必须验证MD5值
3. **权限处理**：Android 6.0+需要动态申请存储权限
4. **安装权限**：Android 8.0+需要申请安装未知来源应用权限
5. **后台下载**：使用Service在后台下载，避免Activity销毁导致下载中断
6. **断点续传**：对于大文件，建议实现断点续传功能
7. **富文本渲染**：使用WebView渲染HTML内容，禁用JavaScript确保安全
8. **内容缓存**：对富文本内容进行适当缓存，提升显示性能

### 8.2 Web端最佳实践

1. **权限控制**：所有管理接口都需要验证管理员权限
2. **操作确认**：删除、强制更新等危险操作需要二次确认
3. **数据验证**：前端和后端都要进行数据格式验证
4. **错误提示**：提供友好的错误提示信息
5. **操作日志**：记录重要操作的审计日志
6. **批量操作**：支持批量设置定向发布、批量删除等功能
7. **富文本编辑**：提供所见即所得的富文本编辑器
8. **内容预览**：编辑时提供实时预览功能
9. **内容安全**：对用户输入的HTML内容进行安全过滤

### 8.3 通用注意事项

1. **版本号管理**：确保版本号严格递增
2. **回滚策略**：提供紧急回滚机制
3. **监控告警**：监控更新成功率、下载失败率等关键指标
4. **灰度发布**：重要版本建议先小范围发布测试
5. **兼容性测试**：确保新版本与旧版本数据兼容
6. **富文本兼容**：确保富文本内容在不同平台正确显示
7. **内容备份**：重要的更新说明内容应该有备份机制
8. **多语言支持**：考虑富文本内容的多语言版本管理

## 9. 富文本功能总结

### 9.1 功能特性

- ✅ **数据库支持**：TEXT字段完全支持HTML富文本存储
- ✅ **后端兼容**：现有API无需修改，完全向后兼容
- ✅ **Web端编辑**：提供完整的富文本编辑器组件
- ✅ **Web端显示**：支持富文本内容的渲染和预览
- ✅ **Android渲染**：使用WebView安全渲染HTML内容
- ✅ **内容安全**：提供HTML内容过滤和验证机制

### 9.2 实施优先级

1. **高优先级**：Web端富文本编辑器集成
2. **中优先级**：Android端富文本渲染支持
3. **低优先级**：高级富文本功能（图片、链接等）

### 9.3 技术要求

- **前端**：Vue.js + Quill编辑器 或 React + 富文本组件
- **Android**：WebView + 自定义HTML模板
- **安全**：HTML内容过滤和XSS防护
- **性能**：内容缓存和渲染优化

通过以上完善，应用更新系统将全面支持富文本更新说明，为用户提供更好的视觉体验和信息传达效果。
```
