<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-29 12:02:17
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 12:08:42
 * @Description: 
 -->
<template>
  <ProDrawer
    :value="showDrawer"
    :title="drawerTitle + '机器采购单'"
    size="85%"
    :destroy-on-close="true"
    :no-footer="editType !== 'add'"
    :confirm-button-disabled="formLoading"
    confirm-text="确认新增"
    @ok="handleDrawerOk"
    @cancel="handleCloseDrawer"
  >
    <div class="add-purchase-container">
      <ProForm
        ref="ProForm"
        :form-param="addForm"
        :form-list="columns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :open-type="editType"
        @proSubmit="formSubmit"
      >
        <template #purchaseCode>
          {{ purchaseCode }}
        </template>
        <template #purchaseGoods>
          <ProTable
            ref="purchaseGoodsTable"
            :row-key="(row) => row.articleCode"
            :layout="{ labelWidth: '120px' }"
            :data="purchaseGoodsData"
            :columns="chooseColumns"
            :height="400"
            show-index
            :show-setting="false"
            :show-search="false"
            :show-loading="false"
          >
            <template #btn>
              <div style="flex: 1">
                <el-form
                  ref="addForm"
                  :model="addForm"
                  :rules="supplierRules"
                  label-width="110px"
                >
                  <el-row :gutter="24">
                    <el-col :span="8">
                      <el-form-item label="供应商名称 : " prop="manufacturerId">
                        <el-select
                          v-model="addForm.manufacturerId"
                          style="width: 100%"
                          filterable
                          placeholder="请选择供应商"
                          :disabled="editType !== 'add'"
                          @change="handleSupplierChange"
                        >
                          <el-option
                            v-for="item in supplierList"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="制造商编号 : ">
                        <el-input
                          v-model="addForm.manufacturerCode"
                          disabled
                          placeholder="请先选择供应商"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-button
                        v-if="editType === 'add' || editType === 'audit'"
                        icon="el-icon-plus"
                        type="success"
                        @click="addPurchaseGoodsData"
                      >
                        新增一行
                      </el-button>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
            </template>
            <!-- 主机类型 -->
            <template #type="{ row }">
              <el-select
                v-model="row.hostType"
                :disabled="editType === 'info'"
                placeholder="请选择主机类型"
                style="width: 100%"
                size="small"
                @change="(e) => handleHostTypeChange(e, row)"
              >
                <el-option
                  v-for="item in hostTypeListOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
            <template #productId="{ row }">
              <div
                v-if="row.hostType !== '2008'"
                style="display: flex; justify-content: space-between; gap: 20px"
              >
                <el-input
                  v-model="row.productName"
                  disabled
                  size="small"
                  placeholder="请选择选配件"
                />
                <el-button
                  v-if="editType !== 'info'"
                  type="primary"
                  size="mini"
                  @click="handleSelectSpare(row)"
                >
                  选择
                </el-button>
              </div>
              <el-cascader
                v-else
                ref="ProductIds"
                v-model="row.productIdName"
                filterable
                clearable
                :options="options"
                style="width: 100%"
                size="small"
                placeholder="请选择机型/系列"
                :disabled="editType === 'info'"
                :props="{
                  label: 'name',
                  value: 'id',
                  children: 'children',
                  expandTrigger: 'click',
                  multiple: false,
                }"
                leaf-only
                @change="(e) => handleProductIdChange(e, row)"
              ></el-cascader>
            </template>
            <!-- 成色 -->
            <template #percentage="{ row }">
              <el-select
                v-model="row.percentage"
                size="small"
                placeholder="请选择成色"
                :disabled="editType === 'info'"
              >
                <el-option
                  v-for="item in percentageOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
            <!--<template #approveNum="{ row }">-->
            <!--  <el-input-number-->
            <!--    v-model="row.approveNum"-->
            <!--    :disabled="editType === 'info' && !checkBtn"-->
            <!--    :min="0"-->
            <!--    size="mini"-->
            <!--    style="width: 100%"-->
            <!--    @change="handleApproveNumChange"-->
            <!--  ></el-input-number>-->
            <!--</template>-->
            <template #price="{ row }">
              <el-input-number
                v-model="row.price"
                :disabled="editType === 'info' && !checkBtn"
                :controls="false"
                size="small"
                :min="0"
                style="width: 100%"
                @change="handlePriceChange"
              >
              </el-input-number>
            </template>
            <!-- 计划采购数量 -->
            <template #number="{ row }">
              <el-input-number
                v-model="row.number"
                :disabled="editType === 'info'"
                style="width: 100%"
                size="small"
                :min="1"
                controls-position="right"
                @change="handleNumChange"
              ></el-input-number>
            </template>
            <template #action="slotProps">
              <div
                v-if="editType === 'add' || editType === 'audit'"
                class="fixed-width"
              >
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDelete(slotProps.row, slotProps.index)"
                >
                  移除
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>

        <template #deliveryTime>
          <el-date-picker
            v-model="addForm.deliveryTime"
            type="date"
            :disabled="editType === 'info' && !checkBtn"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择期望发货时间"
          >
          </el-date-picker>
        </template>

        <template #arrivalTime>
          <el-date-picker
            v-model="addForm.arrivalTime"
            type="date"
            :disabled="editType === 'info' && !checkBtn"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择期望到货时间"
          >
          </el-date-picker>
        </template>
      </ProForm>
      <div v-if="checkBtn" class="dialog-footer1">
        <div class="btn-box">
          <el-button type="danger" @click="handelCheck('REJECT')">
            驳回
          </el-button>
          <el-button
            type="primary"
            :loading="reCheckLoading"
            @click="handelCheck('WAIT_RECEIVE')"
          >
            复核完成
          </el-button>
          <el-button @click="handleCloseDrawer">取消</el-button>
        </div>
      </div>
    </div>

    <!-- 选配件选择 -->
    <SparePart
      ref="sparePart"
      :dialog-visible.sync="sparePartDialog"
      :spare-type="hostType"
      @confirm="confirmSpare"
    />
  </ProDrawer>
</template>

<script>
import SparePart from "@/views/procure/cpns/sparePart.vue";
import { dictTreeByCodeApi } from "@/api/user";
import { manufacturerListApi, warehouseListApi } from "@/api/store";
import { productAllApi } from "@/api/dispose";
import {
  addMachinePurchaseApi,
  getMachinePurchaseDetailApi,
  updateMachinePurchaseApi,
  updatePurchaseApi,
} from "@/api/procure";
import { Message } from "element-ui";
import { mulAmount } from "@/utils";
export default {
  name: "AddMachinePurchase",
  components: { SparePart },
  props: {
    purchaseCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showDrawer: false,
      drawerTitle: "新增",
      addForm: {},
      columns: [
        {
          dataIndex: "warehouseId",
          title: "选择仓库",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择仓库",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "purchaseCode",
          title: "采购单编号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "purchaseGoods",
          title: "选择供应源",
          isForm: true,
          formOtherSlot: "purchaseGoods",
        },
        {
          dataIndex: "deliveryTime",
          title: "期望发货时间",
          isForm: true,
          formSlot: "deliveryTime",
          // valueType: "date-picker",
          // valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请输入期望发货时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "arrivalTime",
          title: "期望到货时间",
          isForm: true,
          formSlot: "arrivalTime",
          // valueType: "date-picker",
          // valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请输入期望发货时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "price",
          title: "总价",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
      ],
      hostTypeListOptions: [], // 主机类型选项
      percentageOptions: [], // 成色列表
      productListOptions: [], // 选配件
      manufacturerList: [],
      formLoading: false,
      editType: "add",
      purchaseGoodsQueryParam: {
        goodsType: [],
      },
      chooseColumns: [
        {
          dataIndex: "type",
          title: "主机类型",
          isTable: true,
          align: "center",
          tableSlot: "type",
          minWidth: 100,
        },
        {
          dataIndex: "productId",
          title: "机型/系列/选配件",
          isTable: true,
          align: "center",
          tableSlot: "productId",
          minWidth: 180,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          align: "center",
          tableSlot: "percentage",
          minWidth: 120,
        },
        // {
        //   dataIndex: "warehouseNumber",
        //   title: "库存量",
        //   isTable: true,
        //   align: "center",
        // },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          align: "center",
          tableSlot: "price",
          minWidth: 80,
        },
        {
          dataIndex: "number",
          title: "计划采购数量",
          isTable: true,
          align: "center",
          tableSlot: "number",
          minWidth: 80,
          // formatter: (row) => row.number,
        },
        // {
        //   dataIndex: "approveNum",
        //   title: "确认采购数量",
        //   isTable: true,
        //   width: 180,
        //   tableSlot: "approveNum",
        //   // formatter: (row) => row.number,
        // },
        {
          dataIndex: "purchasePrice",
          title: "采购金额",
          isTable: true,
          align: "center",
          minWidth: 100,
          // formatter: (row) => mulAmount(row.price, row.number),
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          align: "center",
          formatter: (row) => row.status?.label,
          minWidth: 60,
        },
        {
          dataIndex: "action",
          title: "操作",
          fixed: "right",
          minWidth: 100,
          align: "center",
          isTable: true,
          tableSlot: "action",
        },
      ],
      purchaseGoodsData: [],
      showChooseDialog: false,
      // 供应商Form
      supplierRules: {
        manufacturerId: [
          {
            required: true,
            message: "请选择供应商",
            trigger: "change",
          },
        ],
      },
      supplierList: [],
      checkBtn: false,
      productIdName: "",
      options: [],
      reCheckLoading: false,
      // 选配件弹窗
      sparePartDialog: false,
      hostType: "", // 主机类型
      selectedSparePart: null, // 当前操作行的数据
    };
  },
  watch: {
    purchaseGoodsData: {
      handler(val) {
        this.handleNumChange();
      },
    },
    "addForm.manufacturerId": {
      handler(val) {
        const findManufacturer = this.manufacturerList.find(
          (item) => item.id === val
        );
        if (findManufacturer) {
          this.addForm.manufacturerCode = findManufacturer?.code;
        }
      },
    },
  },
  mounted() {
    productAllApi().then((res) => {
      this.options = res.data;
    });
    manufacturerListApi().then((res) => {
      this.manufacturerList = res.data;
      this.supplierList = res.data.map((item) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
    });
  },
  methods: {
    handleDrawerOk() {
      this.$refs.ProForm.handleSubmit();
    },
    handleCloseDrawer() {
      delete this.addForm.manufacturerCode;
      this.showDrawer = false;
    },
    async show(row, type) {
      switch (type) {
        case "add":
          this.drawerTitle = "新增";
          break;
        case "edit":
          this.drawerTitle = "修改";
          break;
        case "check":
          this.drawerTitle = "复核";
          break;
        case "info":
          this.drawerTitle = "查看";
          break;
        default:
          break;
      }
      if (type === "check") {
        this.editType = "audit";
        this.checkBtn = true;
      } else {
        this.editType = type;
        this.checkBtn = false;
      }
      if (type !== "add") {
        this.chooseColumns = this.$options.data().chooseColumns;
        // if (this.editType !== "audit") {
        //   this.$options.data().chooseColumns.length ===
        //     this.chooseColumns.length &&
        //     (this.chooseColumns = this.chooseColumns.splice(
        //       0,
        //       this.chooseColumns.length - 2
        //     ));
        // }
        await this.getDetails(row.id);
      } else {
        this.addForm = {};
        this.purchaseGoodsData = [];
        // this.chooseColumns = this.$options.data().chooseColumns;
        // this.chooseColumns.splice(this.chooseColumns.length - 3, 1);
      }

      this.purchaseGoodsQueryParam = {};
      await this.getProductType();
      await this.getPercentage();
      this.showDrawer = true;
    },
    async formSubmit(val) {
      try {
        // const userInfo = JSON.parse(localStorage.getItem("userInfo"));
        // initiatorId: userInfo.id,
        //     initiatorName: userInfo.name,
        if (!this.purchaseGoodsData.length) {
          throw new Error("请添加采购商品");
        }
        this.purchaseGoodsData.forEach((item, index) => {
          if (!item.hostType) {
            throw new Error(`请选择第${index + 1}行主机类型`);
          }
          if (!item.productId) {
            throw new Error(`请选择第${index + 1}行机型/选配件`);
          }
          if (!item.percentage) {
            throw new Error(`请选择第${index + 1}行成色`);
          }
          if (!item.number) {
            throw new Error(`请输入第${index + 1}行采购数量`);
          }
        });
        this.$confirm("是否确认新增机器采购单?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          this.formLoading = true;
          const args = {
            ...this.addForm,
            purchaseDetailDtos: this.purchaseGoodsData,
          };
          const result = await addMachinePurchaseApi(args);
          if (result.code === 200) {
            Message.success("添加成功");
            this.handleCloseDrawer();
            this.$emit("refresh");
          }
          this.formLoading = false;
        });
      } catch (error) {
        Message.error(error.message);
      }
    },
    async getDetails(id) {
      try {
        const result = await getMachinePurchaseDetailApi(id);
        if (result.code === 200) {
          this.addForm = result.data;
          this.purchaseGoodsData = result.data.machinePurchaseDetails;
          this.purchaseGoodsData.forEach((item) => {
            Object.keys(item).forEach((key) => {
              if (key === "status") {
                return;
              }
              item[key] = item[key].value ? item[key].value : item[key];
            });
            if (item.hostType === "2008" && item.fullProductPath) {
              const trimmedStr = item.fullProductPath.trim("/");
              item.productIdName = trimmedStr.split("/").filter(Boolean);
            }
          });
          // 供应商编号回显
          const findManufacturer = this.manufacturerList.find(
            (item) => item.id === this.addForm.manufacturerId
          );
          if (findManufacturer) {
            this.addForm.manufacturerCode = findManufacturer?.code;
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    // 复核通过
    async handelCheck(status) {
      try {
        if (!this.purchaseGoodsData.length) {
          return this.$message.error("请添加采购商品");
        }
        this.purchaseGoodsData.forEach((item, index) => {
          if (!item.hostType) {
            throw new Error(`请选择第${index + 1}行主机类型`);
          }
          if (!item.productId) {
            throw new Error(`请选择第${index + 1}行机型/选配件`);
          }
          if (!item.percentage) {
            throw new Error(`请选择第${index + 1}行成色`);
          }
          if (!item.number) {
            throw new Error(`请输入第${index + 1}行采购数量`);
          }
        });
        this.$confirm("是否确认通过该机器采购单?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          try {
            this.reCheckLoading = true;
            this.formLoading = true;
            const args = {
              id: this.addForm.id,
              purchaseCode: this.addForm.purchaseCode,
              purchaseDetailDtos: this.purchaseGoodsData,
              status: status,
              warehouseId: this.addForm.warehouseId,
              manufacturerId: this.addForm.manufacturerId,
            };
            const result = await updateMachinePurchaseApi(args);
            if (result.code === 200) {
              Message.success("复核完成");
              this.$emit("refresh");
              this.handleCloseDrawer();
            }
          } finally {
            this.reCheckLoading = false;
            this.formLoading = false;
          }
        });
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleSupplierChange() {
      this.purchaseGoodsData = [];
    },
    // 新增一条采购单
    addPurchaseGoodsData() {
      if (!this.addForm.manufacturerId) {
        Message.error("请先选择供应商");
        return;
      }
      this.purchaseGoodsData.push({
        hostType: "", // 主机类型
        productId: "", // 设备Id
        manufacturerId: this.addForm.manufacturerId, // 供应商id
        number: 1, // 采购输入
        percentage: null, // 成色
        price: null, // 单价
      });
    },
    // 获取主机类型
    async getPercentage() {
      try {
        const result = await dictTreeByCodeApi(2500);
        if (result.code === 200) {
          this.percentageOptions = result.data;
        }
      } catch (error) {
        this.percentageOptions = [];
      }
    },
    async getProductType() {
      try {
        const result = await dictTreeByCodeApi(2000);
        if (result.code === 200) {
          this.hostTypeListOptions = result.data;
        }
      } catch (error) {
        this.hostTypeListOptions = [];
      }
    },
    confirmSpare(row) {
      this.$set(this.selectedSparePart, "productName", row.modeType);
      this.selectedSparePart.productId = row.id;
      this.sparePartDialog = false;
    },
    // 选择选配件
    handleSelectSpare(row) {
      this.selectedSparePart = null;
      if (!row.hostType && row.hostType !== "2008") {
        Message.warning("请选择主机类型");
        return;
      }
      this.selectedSparePart = row;
      this.hostType = row.hostType;
      this.sparePartDialog = true;
    },
    handlePriceChange() {
      this.handleNumChange();
    },
    /**
     * 计算单行价格
     * @param item
     */
    handleNumChange(item) {
      const addPrice = this.purchaseGoodsData.reduce((total, row) => {
        const price = Number(row.price) || 0;
        const planNumber = Number(row.number) || 0;
        row.purchasePrice = mulAmount(price, planNumber).toFixed(2);
        return total + mulAmount(price, planNumber);
      }, 0);
      this.addForm.price = addPrice.toFixed(2);
      this.$forceUpdate();
    },
    handleHostTypeChange(e, row) {
      row.productId = "";
      row.productIdName = [];
    },
    // 产品树
    handleProductIdChange(e, row) {
      if (e) {
        row.productId = e[e.length - 1];
      } else {
        row.productId = "";
      }
    },
    handleDelete(row, i) {
      this.purchaseGoodsData.splice(i, 1);
      this.handleNumChange();
    },
  },
};
</script>

<style lang="scss" scoped></style>
