<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:55
 * @Description: 角色管理
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增角色
        </el-button>
      </template>
      <template #type="slotProps">
        {{ slotProps.row.type.label }}
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-user-solid"
            @click="handleMember(slotProps.row)"
          >
            人员管理
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-setting"
            @click="handleLimit(slotProps.row)"
          >
            权限设置
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            详情
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>

          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>

    <!-- 新增、编辑、详情框  -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType === 'info'"
      :confirm-text="methodType === 'add' ? '确认新增' : '保存'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
      </ProForm>
    </ProDialog>
    <!-- 权限配置  -->
    <ProDialog
      :value="dialogVisibleQ"
      :title="'权限配置'"
      width="1200px"
      :confirm-loading="confirmLoading"
      :top="'5%'"
      :no-footer="methodType === 'info'"
      @ok="setLimit"
      @cancel="dialogVisibleQ = false"
    >
      <!-- <el-checkbox-group v-model="checkList">
        <el-checkbox :checked="item.checked" v-for="item in menuList" :label="item.id">{{ item.label }}</el-checkbox>
      </el-checkbox-group> -->
      <div style="height: 400px">
        <el-tabs
          v-model="terminal"
          style="float: left; height: 400px"
          tab-position="left"
          @tab-click="getmenuList"
        >
          <el-tab-pane
            v-for="item in menuTypeList"
            :key="item.id"
            :label="item.label"
            :name="item.value"
          >
          </el-tab-pane>
        </el-tabs>

        <!-- <el-tree style="width:80%;float: left;height: 400px;overflow-y: scroll;" ref="tree" default-expand-all
          :data="menuList" :props="props" show-checkbox node-key="id">
        </el-tree> -->
        <div style="width: 80%; float: left; height: 400px; overflow-y: scroll">
          <el-collapse
            v-for="item in menuList"
            :key="item.id"
            v-model="activeNames"
          >
            <el-collapse-item
              v-if="item.type.value == 'directory'"
              :name="item.id"
            >
              <template slot="title">
                <el-checkbox
                  v-model="item.check"
                  :indeterminate="item.isIndet"
                  @change="checkAll(item)"
                >
                  {{ item.label }} (目录)</el-checkbox
                >
              </template>
              <div
                v-for="it in item.children"
                :key="it.id"
                style="
                  padding-left: 20px;
                  border-bottom: 1px solid #ebeef5;
                  min-height: 40px;
                "
              >
                <el-checkbox v-model="it.check" @change="changeMenu(it)">
                  {{ it.label }} (菜单)</el-checkbox
                >
                <ul
                  v-if="it.children && it.children.length > 0"
                  class="tree-li"
                >
                  <li v-for="i in it.children" :key="i.id">
                    <el-checkbox v-model="i.check" @change="changeCheck"
                      >{{ i.label }} (按钮)</el-checkbox
                    >
                  </li>
                </ul>
              </div>
            </el-collapse-item>
            <div
              v-else
              style="
                padding-left: 20px;
                border-bottom: 1px solid #ebeef5;
                min-height: 40px;
              "
            >
              <el-checkbox v-model="item.check" @change="changeCheck">
                {{ item.label }} (菜单)</el-checkbox
              >
              <ul
                v-if="item.children && item.children.length > 0"
                class="tree-li"
              >
                <li v-for="i in item.children" :key="i.id">
                  <el-checkbox v-model="i.check" @change="changeCheck"
                    >{{ i.label }} (按钮)</el-checkbox
                  >
                </li>
              </ul>
            </div>
          </el-collapse>
        </div>
      </div>
    </ProDialog>
    <!-- 人员管理 -->
    <ChooseUser
      ref="ChooseUser"
      :dialog-visible="dialogVisibleR"
      :dialog-title="dialogTitleR"
      :role-id="roleId"
      :type="'role'"
      @delOk="delOk"
      @add="handleMemberAdd"
      @cancel="dialogVisibleR = false"
    ></ChooseUser>
    <UserList
      ref="UserList"
      :dialog-visible="dialogVisibleU"
      :role-id="roleId"
      @ok="memberAdd"
      @cancel="dialogVisibleU = false"
    ></UserList>
  </div>
</template>
<script>
import {
  roleListApi,
  roleAddApi,
  roleDelApi,
  roleEditApi,
  roleMemberAddApi,
  menuListApi,
  roleLimitApi,
  roleLimitGetApi,
  getTerminals,
} from "@/api/user";

import { isEmpty, cloneDeep } from "lodash";
import ChooseUser from "./components/ChooseUser.vue";
import UserList from "./components/UserList.vue";

import { filterName, getAllParentArr } from "@/utils";

export default {
  name: "Role",
  components: {
    ChooseUser,
    UserList,
  },
  mixins: [],
  props: {},
  data() {
    return {
      activeNames: [],
      terminal: "pc",
      menuTypeList: [],
      props: {
        label: "label",
        children: "children",
      },
      tableData: [],
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      queryParam: {
        aduitState: null,
        name: null,
      },
      columns: [
        {
          dataIndex: "name",
          title: "角色名称",
          isTable: true,
          span: 4,
        },
        {
          dataIndex: "keyword",
          title: "角色名称",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "code",
          title: "角色编码",
          isTable: true,
        },

        // {
        //   dataIndex: "isEnable",
        //   title: "状态",
        //   width: 200,
        //   isTable: true
        // },
        {
          dataIndex: "members",
          title: "成员人数",
          width: 200,
          isTable: true,
        },
        {
          dataIndex: "Actions",
          width: 400,
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      actionUrl: "",
      uploadlLading: false,
      uploadDialogVisible: false,
      //新增
      methodType: "add",
      confirmLoading: false,
      form: { parentId: "" },
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: { parentId: "" },
      formcolumns: [
        {
          dataIndex: "name",
          isForm: true,
          title: "角色名称",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入名称",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "code",
          isForm: true,
          title: "角色编码",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入路由",
              trigger: "change",
            },
          ],
        },
      ],
      //人员管理
      roleId: null,
      dialogTitleR: "",
      dialogVisibleR: false,
      dialogVisibleU: false,
      dialogVisibleQ: false,
      checkList: [],
      menuList: [],
      limitId: null,
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    this.$refs.ProTable.refresh();
    this.getMenuType();
  },
  methods: {
    // 删除节点数据方法
    remove(node, data) {
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex((d) => d.canEdit === data.canEdit);
      children.splice(index, 1);
    },
    getMenuType() {
      getTerminals().then((res) => {
        this.menuTypeList = res.data;
      });
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign({}, parameter);
      roleListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.dialogTitle = "新增角色";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;

      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应新增
    create() {
      roleAddApi(this.form)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑 - " + row.name;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.methodType = "edit";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应编辑
    update() {
      roleEditApi(this.form)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.name;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        roleDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
    handleLimit(data) {
      this.dialogVisibleQ = true;
      this.limitId = data.id;
      this.getmenuList(data);
    },
    getmenuList() {
      this.menuList = [];
      this.activeNames = [];
      menuListApi(this.terminal).then((res) => {
        this.menuList = res.data;
        roleLimitGetApi(this.limitId).then((res1) => {
          const arr = res1.data;
          this.menuList.map((ele) => {
            this.activeNames.push(ele.id);
            ele.chechchild = 0;

            // arr.includes(ele.id) ? ele.check = true : null

            if (ele.children && ele.children.length > 0) {
              ele.children.map((el) => {
                if (arr.includes(el.id)) {
                  ele.chechchild = ele.chechchild + 1;
                  ele.isIndet = true;
                  el.check = true;
                } else {
                  ele.isIndet = false;
                }
                if (el.children && el.children.length > 0) {
                  el.children.map((e) => {
                    arr.includes(e.id) ? (e.check = true) : null;
                  });
                }
              });
              if (ele.chechchild <= 0) {
                ele.check = false;
                ele.isIndet = false;
              }
              if (ele.chechchild > 0) {
                ele.check = true;
                ele.isIndet = true;
              }
              if (ele.children) {
                if (ele.chechchild == ele.children.length) {
                  ele.check = true;
                  ele.isIndet = false;
                }
              }
            } else {
              if (arr.includes(ele.id)) {
                ele.check = true;
              }
            }

            this.$forceUpdate(() => {
              this.menuList = cloneDeep(this.menuList);
            });
          });
        });
      });
    },
    setLimit() {
      this.checkList = [];
      this.menuList.map((ele) => {
        if (ele.check) {
          this.checkList.push(ele.id);
        }
        if (ele.children && ele.children.length > 0) {
          ele.children.map((el) => {
            if (el.check) {
              this.checkList.push(el.id);
            }
            if (el.children && el.children.length > 0) {
              el.children.map((e) => {
                if (e.check) {
                  this.checkList.push(e.id);
                }
              });
            }
          });
        }
      });

      roleLimitApi(this.limitId, this.checkList)
        .then(() => {
          this.$message.success("配置成功");
        })
        .finally(() => {
          this.dialogVisibleQ = false;
          this.$refs.ProTable.refresh();
        });
    },

    checkAll(item) {
      item.isIndet = false;
      if (item.check) {
        item.chechchild = item.children.length;
      } else {
        item.chechchild = 0;
      }
      if (item.children) {
        item.children.map((el) => {
          el.check = item.check;
          if (!item.check) {
            el.children.map((e) => {
              e.check = item.check;
            });
          }
        });
      }
      this.$forceUpdate(() => {
        this.menuList = cloneDeep(this.menuList);
      });
    },
    changeMenu(item) {
      this.menuList.map((ele) => {
        if (ele.id == item.parentId) {
          if (item.check) {
            ele.chechchild = ele.chechchild + 1;
          } else {
            ele.chechchild =
              ele.chechchild > 0 ? ele.chechchild - 1 : ele.chechchild;
          }
          if (ele.chechchild <= 0) {
            ele.check = false;
            ele.isIndet = false;
          }
          if (ele.chechchild > 0) {
            ele.check = true;
            ele.isIndet = true;
          }
          if (ele.children) {
            if (ele.chechchild == ele.children.length) {
              ele.check = true;
              ele.isIndet = false;
            }
          }
        }
      });
      if (item.children) {
        item.children.map((el) => {
          if (!item.check) {
            el.check = item.check;
          }
        });
      }

      this.$forceUpdate(() => {
        this.menuList = cloneDeep(this.menuList);
      });
    },
    changeCheck() {
      this.$forceUpdate(() => {
        this.menuList = cloneDeep(this.menuList);
      });
    },
    // ================
    handleMember(row) {
      this.dialogTitleR = "人员管理 - " + row.name;
      this.dialogVisibleR = true;
      this.roleId = row.id;
      setTimeout(() => {
        this.$refs.ChooseUser.$refs.ProTable.refresh();
      }, 200);
    },
    handleMemberAdd() {
      this.dialogVisibleU = true;
      setTimeout(() => {
        this.$refs.UserList.$refs.ProTable.refresh();
      }, 200);
    },
    delOk() {
      this.$refs.ProTable.refresh();
    },
    //响应新增
    memberAdd(data) {
      roleMemberAddApi(this.roleId, data).then(() => {
        this.$message.success("新增成功");
        this.dialogVisibleU = false;
        this.$refs.ChooseUser.$refs.ProTable.refresh();
        this.$refs.ProTable.refresh();
      });
    },
  },
};
</script>
<style>
.el-checkbox {
  line-height: 40px;
}

.el-collapse {
  border: none;
}

.el-collapse-item__header {
  border: none;
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-item__content {
  padding: 0;
}

.el-collapse-item:last-child {
  margin: auto;
}
</style>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tree-li {
  list-style: none;
  margin: 0;

  li {
    list-style: none;
    display: inline-block;
    margin-left: 20px;
  }
}
</style>
