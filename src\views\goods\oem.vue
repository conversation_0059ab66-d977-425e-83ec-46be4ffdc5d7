<!--
 * @Description: 零件列表
 * @Autor: shh
 * @Date: 2022-11-16 16:42:14
 * @LastEditors: shanhaihong <EMAIL>
 * @LastEditTime: 2023-12-14 15:36:34
-->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="550"
      :query-param="queryParam"
      :show-setting="false"
      @loadData="loadData"
    >
      <!-- <template #fullIdPath>
        <el-cascader
        filterable
          ref="ProductIds"
          clearable
          :options="options"
          style="width: 100%"
          v-model="queryParam.productIdName"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          @change="handleSelect"
          leafOnly
        ></el-cascader>
      </template>

      <template #brand="slotProps">
        <el-popover
          placement="bottom"
          title="当前设备关联产品树"
          width="400"
          trigger="click"
        >
          <div style="margin: 20px">
            <el-tree
              :data="deviceProductTree"
              :props="{
                children: 'children',
                label: 'name',
              }"
              default-expand-all
            ></el-tree>
          </div>

          <el-button slot="reference" @click="getBrand(slotProps.row.id)"
            >查看品牌</el-button
          >
        </el-popover>
      </template> -->
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            选择
          </el-button>
        </span>
      </template>
    </ProTable>
  </div>
</template>
<script>
import { storageInventoryListApi } from "@/api/goods";

import { isEmpty, cloneDeep } from "lodash";

export default {
  name: "OemManage",
  components: {},
  mixins: [],
  props: {
    warehouseId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      // 列表
      options: [],
      deviceProductTree: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        lastIds: [],
        warehouseId: "",
      },
      columns: [
        {
          dataIndex: "code",
          isSearch: true,
          clearable: true,
          isTable: true,
          title: "库品编号",
          width: 180,
          formSpan: 8,
          valueType: "input",
        },

        {
          dataIndex: "name",
          title: "库品名称",
          isTable: true,
          isSearch: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
        },
        {
          dataIndex: "warehouseName",
          title: "仓库名称",
          isTable: true,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          clearable: true,
          formatter: (row) => row.article.numberOem,
          formSpan: 8,
          valueType: "input",
        },
        // {
        //   dataIndex: "code",
        //   isSearch: true,
        //   clearable: true,
        //   isTable: true,
        //   title: "库品编号",
        //   width: 180,
        //   formSpan: 8,
        //   valueType: "input",
        // },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          formatter: (row) => row.article.manufacturerChannel.label,
        },
        {
          dataIndex: "outWarehouseNumber",
          title: "出库量",
          isTable: true,
        },
        {
          dataIndex: "runWarehouseNumber",
          title: "在途量",
          isTable: true,
        },
        {
          dataIndex: "sumWarehouseNumber",
          title: "库存量",
          isTable: true,
        },
        {
          dataIndex: "Actions",
          width: 120,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    // productAllApi().then((res) => {
    //   this.options = res.data.rows;
    // });
    this.queryParam.warehouseId = this.warehouseId;
    this.$refs.ProTable.refresh();
  },
  methods: {
    // getBrand(id) {
    //   partProductTreeApi(id).then((res) => {
    //     console.log(res);
    //     this.deviceProductTree = res.data;
    //   });
    // },
    handleSelect(item) {
      this.queryParam.lastIds = [];
      item.map((el) => {
        this.queryParam.lastIds.push(el[el.length - 1]);
      });
    },
    handleChange(item) {
      this.$set(this.form, "productIds", []);
      // console.log(this.$refs.ProductIds.getCheckedNodes(true))
      item.map((el) => {
        this.form.productIds.push(el[el.length - 1]);
      });
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      storageInventoryListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },

    handleInfo(row) {
      console.log(row);
      this.$emit("chooseOem", row);
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
