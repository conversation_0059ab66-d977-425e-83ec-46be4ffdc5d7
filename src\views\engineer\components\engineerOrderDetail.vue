<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-24 15:57:11
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-19 11:17:54
 * @Description: 工程师工单列表
 -->
<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :height="380"
      :data="tableData"
      @loadData="loadData"
    >
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row)">
            查看
          </el-button>
          <el-button icon="el-icon-circle-check" @click="handleTransfer(row)">
            转派工单
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 指派、转让工单 -->
    <ProDialog
      :value="dialogVisible"
      title="转派工单"
      width="600px"
      :confirm-loading="confirmLoading"
      confirm-text="确认转派"
      :top="'10%'"
      @ok="handleDialogOk"
      @cancel="dialogVisible = false"
    >
      <ProForm
        v-if="dialogVisible"
        ref="ProForm"
        :form-param="engineerForm"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        open-type="edit"
        @proSubmit="proSubmit"
      ></ProForm>
    </ProDialog>
    <WorkOrderDetail
      ref="workOrderDetail"
      @refreshWorkOrder="refreshWorkOrder"
    />
  </div>
</template>

<script>
import WorkOrderDetail from "@/views/engineer/components/workOrderDetail.vue";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  changeOrderApi,
  engineerListApi,
  getWorkOrderByPageApi,
} from "@/api/repair";

export default {
  name: "EngineerOrderDetail",
  components: { WorkOrderDetail },
  props: {
    engineerInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 工程师维修工单信息
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "工单编号",
          isTable: true,
          minWidth: 130,
        },
        {
          dataIndex: "customerName",
          title: "客户名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        // {
        //   dataIndex: "customerSeq",
        //   title: "客户编号",
        //   isTable: true,
        //   valueType: "input",
        //   minWidth: 120,
        // },
        {
          dataIndex: "phone",
          title: "手机号码",
          isTable: true,
          valueType: "input",
          minWidth: 90,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组",
          formatter: (row) => row.deviceGroup?.label,
          isTable: true,
          minWidth: 70,
        },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => row.serType?.label,
          minWidth: 70,
        },
        {
          dataIndex: "serTypes",
          title: "服务类型",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "散修",
              value: "SCATTERED",
            },
            {
              label: "全保",
              value: "ALL",
            },
            {
              label: "半保",
              value: "HALF",
            },
          ],
        },
        {
          dataIndex: "errorCode",
          title: "故障代码",
          isTable: true,
          // isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "status",
          title: "工单状态",
          valueType: "select",
          isTable: true,
          // isSearch: true,
          multiple: true,
          formatter: (row) =>
            row.cancelStatus && row.status?.value === "close"
              ? "客户取消"
              : row.status?.label,
          option: [
            { label: "待接单", value: "pending_orders" },
            // { label: "工单确认中", value: "customer_confirming" },
            { label: "工程师接单", value: "engineer_receive" },
            { label: "工程师出发", value: "engineer_departure" },
            { label: "工程师到达", value: "engineer_arrive" },
            { label: "待确认维修报告", value: "wait_confirmed_report" },
            // { label: "已完成", value: "completed" },
            // { label: "待结算", value: "to_be_settled" },
            // { label: "待审核", value: "wait_audit" },
            // { label: "客户取消", value: "close" },
            // { label: "关闭", value: "close" },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 80,
        },
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          isTable: true,
          valueType: "date-picker",
          pickerType: "date",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
          width: 150,
        },
        {
          dataIndex: "createTimeStartDate",
          title: "下单时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 165,
        },
        // {
        //   dataIndex: "sendReportTime",
        //   title: "报告提交时间",
        //   isTable: true,
        //   // isSearch: true,
        //   valueType: "date-picker",
        //   pickerType: "daterange",
        //   pickerFormat: "yyyy-MM-dd",
        //   valueFormat: "yyyy-MM-dd",
        //   width: 150,
        // },
      ],
      tableData: [],
      // 转派工单
      dialogVisible: false,
      confirmLoading: false,
      engineerForm: {},
      formColumns: [],
    };
  },
  mounted() {
    console.log(111);
    this.refresh();
  },
  methods: {
    show(info) {
      this.dialogVisible = true;
      this.queryParam = {};
      this.localPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      // this.engineerInfo = cloneDeep(info);
      this.$nextTick(() => {
        this.refresh();
      });
    },
    // 加载维修工单列表数据
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const paramRange = [
        {
          createTimeStart: null,
          createTimeEnd: null,
          data: parameter.createTimeStartDate,
        },
      ];
      filterParamRange(this, this.queryParam, paramRange);
      const requestParameters = cloneDeep(this.queryParam);
      requestParameters.status = [
        "pending_orders",
        "engineer_receive",
        "engineer_departure",
        "engineer_arrive",
        "wait_confirmed_report",
      ];
      requestParameters.engineerId = this.engineerInfo?.id;
      delete requestParameters.createTimeStartDate;
      getWorkOrderByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          if (parseInt(res.data.total) === 0) {
            this.$message.warning(
              "当前工程师暂无维修工单，即将跳转至工单列表页面。"
            );
            setTimeout(() => {
              this.$emit("switch");
            }, 2000);
          }
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
    },
    handleTransfer(row) {
      this.engineerForm.workOrderId = row.id;
      this.formColumns = [
        {
          clearboth: true,
          dataIndex: "engineerId",
          title: "工程师",
          isForm: true,
          formSpan: 24,
          valueType: "select",
          option: [],
          optionMth: () => engineerListApi(row.productId),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择工程师",
              trigger: "change",
            },
          ],
        },
      ];
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.$refs.workOrderDetail.show(row.code);
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    proSubmit(val) {
      this.$confirm(`此操作会将工单转派选择的工程师，是否继续？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const args = {
          engineerId: val.engineerId,
          id: val.workOrderId,
        };
        changeOrderApi(args).then(() => {
          this.$message.success("操作成功");
          this.engineerForm = {};
          this.dialogVisible = false;
          this.refreshWorkOrder();
        });
      });
    },
    refreshWorkOrder() {
      this.refresh();
      this.$emit("refreshWorkOrder");
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
