<!--
 * @Author: wskg
 * @Date: 2024-08-30 10:27:48
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:22
 * @Description: 客户 - 粘度分析
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>客户实际总数：{{ totalData?.customerNum || 0 }}</div>
          <div>交易客户总数：{{ totalData?.tradingNum || 0 }}</div>
          <div>访问客户总数：{{ totalData?.interviewNum || 0 }}</div>
          <div>搜索客户总数：{{ totalData?.searchNum || 0 }}</div>
          <div>拜访客户总数：{{ totalData?.visitNum || 0 }}</div>
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getCustomerViscosityByPageApi,
  getCustomerViscosityStatApi,
} from "@/api/customer";

export default {
  name: "Viscosity",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      columns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "enterDate",
          title: "入驻时间",
          isTable: true,
          formatter: (row) => this.formatDate(row.enterDate),
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          minWidth: 100,
        },
        {
          dataIndex: "recentlyCorrelation",
          title: "最近接触时间",
          isTable: true,
          align: "center",
          formatter: (row) => this.formatDate(row.recentlyCorrelation),
          width: 110,
        },
        {
          dataIndex: "recentlyTrading",
          title: "最近交易时间",
          isTable: true,
          align: "center",
          formatter: (row) => this.formatDate(row.recentlyTrading),
          width: 110,
        },
        {
          dataIndex: "recentlyInterview",
          title: "最近访问时间",
          isTable: true,
          align: "center",
          formatter: (row) => this.formatDate(row.recentlyInterview),
          width: 110,
        },
        {
          dataIndex: "recentlySearch",
          title: "最近搜索时间",
          isTable: true,
          align: "center",
          formatter: (row) => this.formatDate(row.recentlySearch),
          width: 110,
        },
        {
          dataIndex: "recentlyVisit",
          title: "最近拜访时间",
          isTable: true,
          align: "center",
          formatter: (row) => this.formatDate(row.recentlyVisit),
          width: 110,
        },
        {
          dataIndex: "tradingDays",
          title: "最近交易间隔(天)",
          isTable: true,
          align: "center",
          formatter: (row) => (row.tradingDays ? row.tradingDays : "/"),
          width: 130,
        },
        {
          dataIndex: "tradingDays",
          title: "最近交易间隔",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "recentlyReport",
          title: "最近数据上报时间",
          isTable: true,
          align: "center",
          formatter: (row) => this.formatDate(row.recentlyReport),
          width: 140,
        },
        {
          dataIndex: "interviewDays",
          title: "最近访问间隔(天)",
          isTable: true,
          align: "center",
          formatter: (row) => (row.interviewDays ? row.interviewDays : "/"),
          width: 130,
        },
        {
          dataIndex: "interviewDays",
          title: "最近访问间隔",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "searchDays",
          title: "最近搜索间隔(天)",
          isTable: true,
          align: "center",
          formatter: (row) => (row.searchDays ? row.searchDays : "/"),
          width: 130,
        },
        {
          dataIndex: "searchDays",
          title: "最近搜索间隔",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "reportDays",
          title: "最近上报间隔(天)",
          isTable: true,
          align: "center",
          formatter: (row) => (row.reportDays ? row.reportDays : "/"),
          width: 130,
        },
        {
          dataIndex: "reportDays",
          title: "最近上报间隔",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "visitDays",
          title: "最近拜访间隔(天)",
          isTable: true,
          align: "center",
          formatter: (row) => (row.visitDays ? row.visitDays : "/"),
          width: 130,
        },
        {
          dataIndex: "visitDays",
          title: "最近拜访间隔",
          isSearch: true,
          valueType: "inputRange",
        },
      ],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );

      const result = [
        {
          enterStartDate: null,
          enterEndDate: null,
          data: parameter.enterDate,
        },
        {
          tradingStartDays: null,
          tradingEndDays: null,
          data: parameter.tradingDays,
        },
        {
          interviewStartDays: null,
          interviewEndDays: null,
          data: parameter.interviewDays,
        },
        {
          searchStartDays: null,
          searchEndDays: null,
          data: parameter.searchDays,
        },
        {
          reportStartDays: null,
          reportEndDays: null,
          data: parameter.reportDays,
        },
        {
          visitStartDays: null,
          visitEndDays: null,
          data: parameter.visitDays,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.enterDate;
      delete requestParameters.tradingDays;
      delete requestParameters.interviewDays;
      delete requestParameters.searchDays;
      delete requestParameters.reportDays;
      delete requestParameters.visitDays;
      getCustomerViscosityByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData(requestParameters);
    },
    getTotalData(params) {
      getCustomerViscosityStatApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    formatDate(date) {
      return date ? date.slice(0, 10) : "/";
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  display: flex;
  justify-content: space-between;
  flex: 1;
}
</style>
