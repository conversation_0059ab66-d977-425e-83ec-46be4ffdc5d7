# Web端配置分发接口使用指南

## 📋 现有接口

### 1. 查询配置分发情况
```javascript
GET /logcontrol/config/assignments?targetType={目标类型}&keyword={关键词}
```

**使用方法：**
```javascript
// 查询所有分发关系
const getAllAssignments = async () => {
  const response = await fetch('/logcontrol/config/assignments');
  return response.json();
};

// 按目标类型查询
const getAssignmentsByType = async (targetType) => {
  const response = await fetch(`/logcontrol/config/assignments?targetType=${targetType}`);
  return response.json();
};

// 按关键词搜索
const searchAssignments = async (keyword) => {
  const response = await fetch(`/logcontrol/config/assignments?keyword=${keyword}`);
  return response.json();
};

// 组合查询
const getFilteredAssignments = async (targetType, keyword) => {
  const params = new URLSearchParams();
  if (targetType) params.append('targetType', targetType);
  if (keyword) params.append('keyword', keyword);
  
  const response = await fetch(`/logcontrol/config/assignments?${params}`);
  return response.json();
};
```

**响应数据格式：**
```javascript
[
  {
    "distributionId": 12345,              // 分发关系ID
    "configId": 1,                        // 配置ID
    "targetType": "DEVICE",               // 目标类型
    "targetId": "device001",              // 目标ID
    "targetName": "测试设备1",             // 目标名称
    "assignTime": "2025-08-01T10:30:00",  // 分配时间
    "configName": "production_config",    // 配置名称
    "assignedVersion": "1.0.5",           // 分配的配置版本
    "currentVersion": "1.0.3",            // 设备当前版本
    "distributionStatus": "ASSIGNED"      // 分发状态
  }
]
```

### 2. 批量分配配置
```javascript
POST /logcontrol/config/assign-batch
```

**使用方法：**
```javascript
const batchAssignConfig = async (configId, targets) => {
  const requestBody = {
    configSource: configId.toString(),
    sourceType: "CONFIG_ID",
    targets: targets,
    overrideExisting: false
  };
  
  const response = await fetch('/logcontrol/config/assign-batch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestBody)
  });
  
  return response.json();
};

// 使用示例
const targets = [
  {
    targetType: "USER",
    targetId: "user001",
    targetName: "张三"
  },
  {
    targetType: "DEVICE", 
    targetId: "device001",
    targetName: "测试设备1"
  }
];

batchAssignConfig(123, targets);
```

### 3. 单个分配接口
```javascript
// 为用户分配配置
POST /logcontrol/config/assign-to-user?userId={用户ID}&configId={配置ID}

// 为设备分配配置  
POST /logcontrol/config/assign-to-device?deviceId={设备ID}&configId={配置ID}
```

**使用方法：**
```javascript
// 为用户分配配置
const assignConfigToUser = async (userId, configId) => {
  const response = await fetch(`/logcontrol/config/assign-to-user?userId=${userId}&configId=${configId}`, {
    method: 'POST'
  });
  return response.json();
};

// 为设备分配配置
const assignConfigToDevice = async (deviceId, configId) => {
  const response = await fetch(`/logcontrol/config/assign-to-device?deviceId=${deviceId}&configId=${configId}`, {
    method: 'POST'
  });
  return response.json();
};
```

### 4. 移除配置分配
```javascript
DELETE /logcontrol/config/assignment/{targetType}/{targetId}
```

**使用方法：**
```javascript
const removeConfigAssignment = async (targetType, targetId) => {
  const response = await fetch(`/logcontrol/config/assignment/${targetType}/${targetId}`, {
    method: 'DELETE'
  });
  return response.json();
};

// 使用示例
removeConfigAssignment('USER', 'user001');
removeConfigAssignment('DEVICE', 'device001');
```

## 🚫 缺少的接口（需要新增）

基于前面讨论的可更改字段，我们需要新增以下接口：

### 1. 更新分发关系状态
```javascript
PUT /logcontrol/config/distribution/{distributionId}/status
```

### 2. 更新目标名称
```javascript
PUT /logcontrol/config/distribution/{distributionId}/name
```

### 3. 更换分发配置
```javascript
PUT /logcontrol/config/distribution/{distributionId}/config
```

## 💡 完整的Web端使用示例

```javascript
class ConfigDistributionManager {
  
  // 获取分发列表
  async getDistributions(filters = {}) {
    const params = new URLSearchParams();
    if (filters.targetType) params.append('targetType', filters.targetType);
    if (filters.keyword) params.append('keyword', filters.keyword);
    
    const response = await fetch(`/logcontrol/config/assignments?${params}`);
    return response.json();
  }
  
  // 批量分配
  async batchAssign(configId, targets, overrideExisting = false) {
    const response = await fetch('/logcontrol/config/assign-batch', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        configSource: configId.toString(),
        sourceType: "CONFIG_ID",
        targets: targets,
        overrideExisting: overrideExisting
      })
    });
    return response.json();
  }
  
  // 单个分配
  async assignToUser(userId, configId) {
    const response = await fetch(`/logcontrol/config/assign-to-user?userId=${userId}&configId=${configId}`, {
      method: 'POST'
    });
    return response.json();
  }
  
  async assignToDevice(deviceId, configId) {
    const response = await fetch(`/logcontrol/config/assign-to-device?deviceId=${deviceId}&configId=${configId}`, {
      method: 'POST'
    });
    return response.json();
  }
  
  // 移除分配
  async removeAssignment(targetType, targetId) {
    const response = await fetch(`/logcontrol/config/assignment/${targetType}/${targetId}`, {
      method: 'DELETE'
    });
    return response.json();
  }
  
  // 使用示例
  async example() {
    // 1. 获取所有设备的分发情况
    const deviceDistributions = await this.getDistributions({ targetType: 'DEVICE' });
    
    // 2. 批量分配配置给多个目标
    const targets = [
      { targetType: "USER", targetId: "user001", targetName: "张三" },
      { targetType: "DEVICE", targetId: "device001", targetName: "测试设备" }
    ];
    await this.batchAssign(123, targets);
    
    // 3. 为特定用户分配配置
    await this.assignToUser("user002", 456);
    
    // 4. 移除用户的配置分配
    await this.removeAssignment("USER", "user001");
  }
}
```

## 📊 React组件使用示例

```jsx
import React, { useState, useEffect } from 'react';
import { Table, Button, Select, Input, Space, message } from 'antd';

const ConfigDistributionPage = () => {
  const [distributions, setDistributions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({});

  const distributionManager = new ConfigDistributionManager();

  // 加载分发列表
  const loadDistributions = async () => {
    setLoading(true);
    try {
      const data = await distributionManager.getDistributions(filters);
      setDistributions(data);
    } catch (error) {
      message.error('加载失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDistributions();
  }, [filters]);

  // 移除分配
  const handleRemove = async (record) => {
    try {
      await distributionManager.removeAssignment(record.targetType, record.targetId);
      message.success('移除成功');
      loadDistributions();
    } catch (error) {
      message.error('移除失败');
    }
  };

  const columns = [
    { title: '目标类型', dataIndex: 'targetType', key: 'targetType' },
    { title: '目标ID', dataIndex: 'targetId', key: 'targetId' },
    { title: '目标名称', dataIndex: 'targetName', key: 'targetName' },
    { title: '配置名称', dataIndex: 'configName', key: 'configName' },
    { title: '分发状态', dataIndex: 'distributionStatus', key: 'distributionStatus' },
    { title: '分配时间', dataIndex: 'assignTime', key: 'assignTime' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button danger onClick={() => handleRemove(record)}>
          移除
        </Button>
      )
    }
  ];

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Select
          placeholder="选择目标类型"
          style={{ width: 120 }}
          onChange={(value) => setFilters({...filters, targetType: value})}
          allowClear
        >
          <Select.Option value="USER">用户</Select.Option>
          <Select.Option value="DEVICE">设备</Select.Option>
        </Select>

        <Input
          placeholder="搜索关键词"
          style={{ width: 200 }}
          onChange={(e) => setFilters({...filters, keyword: e.target.value})}
        />
      </Space>

      <Table
        columns={columns}
        dataSource={distributions}
        loading={loading}
        rowKey="distributionId"
      />
    </div>
  );
};

export default ConfigDistributionPage;
```

## 🔧 支持Web端更改的字段

### 可更改字段
1. **is_active** - 启用/停用分发关系
2. **target_name** - 编辑显示名称
3. **config_id** - 更换分发的配置

### 不可更改字段
- **target_type**, **target_id** - 影响唯一性
- **assign_time** - 历史记录
- **id**, **created_by**, **created_time**, **updated_time**, **deleted** - 系统字段

## 📝 接口总结

| 接口 | 方法 | 用途 | 状态 |
|------|------|------|------|
| `/logcontrol/config/assignments` | GET | 查询分发情况 | ✅ 已实现 |
| `/logcontrol/config/assign-batch` | POST | 批量分配配置 | ✅ 已实现 |
| `/logcontrol/config/assign-to-user` | POST | 为用户分配配置 | ✅ 已实现 |
| `/logcontrol/config/assign-to-device` | POST | 为设备分配配置 | ✅ 已实现 |
| `/logcontrol/config/assignment/{type}/{id}` | DELETE | 移除配置分配 | ✅ 已实现 |
| `/logcontrol/config/distribution/{id}/status` | PUT | 更新分发状态 | ❌ 需新增 |
| `/logcontrol/config/distribution/{id}/name` | PUT | 更新目标名称 | ❌ 需新增 |
| `/logcontrol/config/distribution/{id}/config` | PUT | 更换分发配置 | ❌ 需新增 |

## 🎯 使用建议

1. **查询优化**：使用 `targetType` 和 `keyword` 参数进行精确查询
2. **批量操作**：优先使用批量分配接口提高效率
3. **错误处理**：完善前端错误处理和用户提示
4. **状态管理**：实时更新分发状态，提供良好的用户体验
5. **权限控制**：根据用户权限控制操作按钮的显示
```
