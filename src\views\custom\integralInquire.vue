<!--
 * @Author: wskg
 * @Date: 2025-01-25 10:24:06
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:50
 * @Description: 积分查询
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :height="497"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";
import { getCustomerIntegralByPageApi } from "@/api/customer";
import { cloneDeep } from "lodash";

export default {
  name: "IntegralInquire",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        // {
        //   dataIndex: "regionPath",
        //   title: "省市区",
        //   isSearch: true,
        //   searchSlot: "regionPath",
        // },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },

        // {
        //   dataIndex: "province",
        //   title: "省",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "city",
        //   title: "市",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "area",
        //   title: "区",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "businessCode",
        //   title: "业务单号",
        //   isTable: true,
        //   minWidth: 150,
        // },
        {
          dataIndex: "source",
          title: "来源",
          isTable: true,
          formatter: (row) => row.source?.label,
          minWidth: 100,
        },
        {
          dataIndex: "points",
          title: "积分",
          isTable: true,
          formatter: (row) => {
            return row.type === 1 ? "+" + row.points : "-" + row.points;
          },
          minWidth: 100,
        },
        {
          dataIndex: "createdBy",
          title: "创建人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
          minWidth: 100,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
          minWidth: 200,
        },
      ],
      tableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    /**
     * 加载表格数据
     * @param parameter
     */
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      getCustomerIntegralByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
