<!--
 * @Author: wskg
 * @Date: 2024-08-31 10:09:46
 * @LastEditors: wskg
 * @LastEditTime: 2024-08-31 11:46:07
 * @Description: 客户 - 订单查询
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="变化统计" name="变化统计" lazy>
        <ChangeStatistics />
        <!-- 变化统计 -->
      </el-tab-pane>
      <el-tab-pane label="分客户统计" name="分客户统计" lazy>
        <OrderStatistics />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ChangeStatistics from "@/views/custom/components/changeStatistics.vue";
import OrderStatistics from "@/views/custom/components/orderStatistics.vue";
export default {
  name: "OrderInquire",
  components: {
    ChangeStatistics,
    OrderStatistics,
  },
  data() {
    return {
      activeName: "变化统计",
    };
  },
};
</script>

<style scoped lang="scss"></style>
