<!--
 * @Author: wskg
 * @Date: 2024-09-13 14:52:34
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 订单 - 机器统计
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :layout="{ labelWidth: '80px' }"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <div v-if="type === 'model'" class="title-box-right">
          <div>总数量：{{ totalData?.number || 0 }}</div>
          <div>总销售额：{{ totalData?.amount || 0 }}</div>
        </div>
        <div v-if="type === 'area'" class="title-box-right">
          <div>总数量：{{ totalData?.number || 0 }}</div>
          <div>总销售额：{{ totalData?.amount || 0 }}</div>
        </div>
        <div v-if="type === 'sale'" class="title-box-right">
          <div>总数量：{{ totalData?.number || 0 }}</div>
          <div>总销售额：{{ totalData?.amount || 0 }}</div>
        </div>
      </template>
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getMonthlyMachineSalesAreaStatApi,
  getMonthlyMachineSalesAreaSummaryStatApi,
  getMonthlyMachineSalesDistributionStatApi,
  getMonthlyMachineSalesDistributionSummaryStatApi,
  getMonthlyMachineSalesStatApi,
  getMonthlyMachineSalesSummaryStatApi,
} from "@/api/order";
import { productAllApi } from "@/api/dispose";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";

export default {
  name: "MachinesStat",
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "model",
    },
  },
  data() {
    return {
      productIdName: "",
      options: [],
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
    this.getProductThird();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.currMonth, // 月份
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      ["currMonth"].forEach((key) => delete requestParameters[key]);
      const editApi = this.getMethodApi(this.type);
      if (!editApi) {
        this.$refs.ProTable.listLoading = false;
        return;
      }
      editApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      const totalApi = this.getTotalDataApi(this.type);
      if (!totalApi) {
        this.totalData = {};
        return;
      }
      totalApi(requestParameters).then((res) => {
        this.totalData = res.data;
      });
    },
    getMethodApi(type) {
      switch (type) {
        case "model":
          return getMonthlyMachineSalesStatApi;
        case "area":
          return getMonthlyMachineSalesAreaStatApi;
        case "sale":
          return getMonthlyMachineSalesDistributionStatApi;
        default:
          return "";
      }
    },
    getTotalDataApi(type) {
      switch (type) {
        case "model":
          return getMonthlyMachineSalesSummaryStatApi;
        case "area":
          return getMonthlyMachineSalesAreaSummaryStatApi;
        case "sale":
          return getMonthlyMachineSalesDistributionSummaryStatApi;
        default:
          return "";
      }
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        this.queryParam.productIds.push(el[el.length - 1]);
      });
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
