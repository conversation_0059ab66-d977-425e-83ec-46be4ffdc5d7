<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:48
 * @Description: 
 -->
<template>
  <div>
    <ProDrawer
      :value="showDrawer"
      :title="drawerTitle"
      size="85%"
      :destroy-on-close="true"
      :no-confirm-footer="true"
      :confirm-button-disabled="formLoading"
      @ok="handleDrawerOk"
      @cancel="handleCloseDrawer"
    >
      <div class="add-purchase-container">
        <ProForm
          ref="ProForm"
          :form-param="addForm"
          :form-list="columns"
          :confirm-loading="formLoading"
          :layout="{ formWidth: '100%', labelWidth: '120px' }"
          :open-type="editType"
          @proSubmit="formSubmit"
        >
          <template #purchaseCode>
            {{ purchaseCode }}
          </template>
          <template #purchaseGoods>
            <ProTable
              ref="purchaseGoodsTable"
              :row-key="(row) => row.articleCode"
              :layout="{ labelWidth: '120px' }"
              :data="purchaseGoodsData"
              :columns="chooseColumns"
              :height="500"
              show-index
              :show-setting="false"
              :show-search="false"
              :show-loading="showProTableLoading"
            >
              <template #btn>
                <div style="flex: 1">
                  <el-form
                    ref="addForm"
                    :model="addForm"
                    :rules="supplierRules"
                    label-width="110px"
                  >
                    <el-row :gutter="24">
                      <el-col :span="8">
                        <el-form-item
                          label="供应商名称 : "
                          prop="manufacturerId"
                        >
                          <el-select
                            v-model="addForm.manufacturerId"
                            style="width: 100%"
                            filterable
                            placeholder="请选择供应商"
                            :disabled="editType !== 'add'"
                            @change="handleSupplierChange"
                          >
                            <el-option
                              v-for="item in supplierList"
                              :key="item.id"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item label="制造商编号 : ">
                          <el-input
                            v-model="addForm.manufacturerCode"
                            disabled
                            placeholder="请先选择供应商"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-button
                          v-if="editType === 'add'"
                          icon="el-icon-plus"
                          type="success"
                          @click="addPurchaseGoodsData"
                        >
                          新增一行
                        </el-button>
                      </el-col>
                    </el-row>
                  </el-form>
                </div>
                <div
                  class="title-box-right"
                  style="
                    flex: 0;
                    text-wrap: nowrap;
                    align-items: center;
                    flex-wrap: nowrap;
                  "
                >
                  <span>总价:{{ addForm.amount || 0 }}</span>
                  <span>待支付金额：{{ addForm.waitPayAmount || 0 }}</span>
                  <span>已支付金额：{{ addForm.paidAmount || 0 }}</span>
                  <span>退款金额:{{ addForm.refundAmount || 0 }}</span>
                </div>
              </template>

              <!-- 主机类型 -->
              <template #type="{ row }">
                <el-select
                  v-model="row.hostType"
                  :disabled="true"
                  placeholder="请选择主机类型"
                  style="width: 100%"
                  size="small"
                  @change="(e) => handleHostTypeChange(e, row)"
                >
                  <el-option
                    v-for="item in hostTypeListOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
              <template #productId="{ row }">
                <div
                  v-if="row.hostType !== '2008'"
                  style="
                    display: flex;
                    justify-content: space-between;
                    gap: 20px;
                  "
                >
                  <el-input
                    v-model="row.productName"
                    disabled
                    size="small"
                    placeholder="请选择选配件"
                  />
                  <!--<el-button-->
                  <!--  v-if="editType !== 'info'"-->
                  <!--  type="primary"-->
                  <!--  size="mini"-->
                  <!--  @click="handleSelectSpare(row)"-->
                  <!--&gt;-->
                  <!--  选择-->
                  <!--</el-button>-->
                </div>
                <el-cascader
                  v-else
                  ref="ProductIds"
                  v-model="row.productIdName"
                  filterable
                  clearable
                  :options="options"
                  style="width: 100%"
                  size="small"
                  placeholder="请选择机型/系列"
                  :disabled="true"
                  :show-all-levels="false"
                  :props="{
                    label: 'name',
                    value: 'id',
                    children: 'children',
                    expandTrigger: 'click',
                    multiple: false,
                  }"
                  leaf-only
                  @change="(e) => handleProductIdChange(e, row)"
                ></el-cascader>
              </template>
              <!-- 成色 -->
              <template #percentage="{ row }">
                <el-select
                  v-model="row.percentage"
                  size="small"
                  placeholder="请选择成色"
                  :disabled="true"
                >
                  <el-option
                    v-for="item in percentageOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
              <template #price="{ row }">
                <el-input-number
                  v-model="row.purchasePrice"
                  :controls="false"
                  :precision="2"
                  :disabled="
                    row.puschaseDetailStatus !== 'WAIT_RECEIVE' ||
                    editType !== 'edit'
                  "
                  size="small"
                  :min="0"
                  style="width: 100%"
                ></el-input-number>
              </template>
              <template #deviceOn="{ row }">
                <el-select
                  v-model="row.deviceOn"
                  style="width: 100%"
                  clearable
                  size="small"
                  :disabled="
                    row.puschaseDetailStatus !== 'WAIT_RECEIVE' ||
                    editType !== 'edit'
                  "
                >
                  <el-option
                    v-for="item in deviceOnOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
              <template #deviceStatus="{ row }">
                <el-select
                  v-model="row.deviceStatus"
                  style="width: 100%"
                  clearable
                  size="small"
                  :disabled="
                    row.puschaseDetailStatus !== 'WAIT_RECEIVE' ||
                    editType !== 'edit'
                  "
                >
                  <el-option
                    v-for="item in deviceStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
              <!-- 机器序列号 -->
              <template #deviceSequence="{ row }">
                <el-input
                  v-model="row.deviceSequence"
                  :disabled="
                    row.puschaseDetailStatus !== 'WAIT_RECEIVE' ||
                    editType !== 'edit'
                  "
                  size="small"
                  clearable
                  placeholder="请输入机器序列号"
                />
              </template>
              <!-- 标签型号 -->
              <template #tagName="{ row }">
                <el-input
                  v-model="row.tagName"
                  :disabled="
                    row.puschaseDetailStatus !== 'WAIT_RECEIVE' ||
                    editType !== 'edit'
                  "
                  size="small"
                  clearable
                  placeholder="请输入标签型号"
                />
              </template>
              <!-- 原机器编号 -->
              <template #originCode="{ row }">
                <el-input
                  v-model="row.originCode"
                  :disabled="
                    row.puschaseDetailStatus !== 'WAIT_RECEIVE' ||
                    editType !== 'edit'
                  "
                  size="small"
                  clearable
                  placeholder="请输入原机器编号"
                />
              </template>
              <!-- 储位 -->
              <template #location="{ row }">
                <el-input
                  v-model="row.location"
                  :disabled="
                    row.puschaseDetailStatus !== 'WAIT_RECEIVE' ||
                    editType !== 'edit'
                  "
                  size="small"
                  clearable
                  placeholder="请输入储位"
                />
              </template>
              <!-- 黑白计数器 -->
              <template #blackWhiteCounter="{ row }">
                <el-input-number
                  v-model="row.blackWhiteCounter"
                  :disabled="
                    row.puschaseDetailStatus !== 'WAIT_RECEIVE' ||
                    editType !== 'edit'
                  "
                  style="width: 100%"
                  size="small"
                  :controls="false"
                  :min="0"
                  placeholder="请输入黑白计数器"
                  controls-position="right"
                ></el-input-number>
              </template>
              <!-- 彩色计数器 -->
              <template #colorCounter="{ row }">
                <el-input-number
                  v-model="row.colorCounter"
                  :disabled="
                    row.puschaseDetailStatus !== 'WAIT_RECEIVE' ||
                    editType !== 'edit'
                  "
                  style="width: 100%"
                  size="small"
                  :controls="false"
                  :min="0"
                  placeholder="请输入彩色计数器"
                  controls-position="right"
                ></el-input-number>
              </template>
              <!-- 五色计数器 -->
              <template #fiveColourCounter="{ row }">
                <el-input-number
                  v-model="row.fiveColourCounter"
                  :disabled="
                    row.puschaseDetailStatus !== 'WAIT_RECEIVE' ||
                    editType !== 'edit'
                  "
                  style="width: 100%"
                  :controls="false"
                  size="small"
                  :min="0"
                  placeholder="请输入五色计数器"
                  controls-position="right"
                ></el-input-number>
              </template>
              <!-- 计划采购数量 -->
              <!--<template #number="{ row }">-->
              <!--  <el-input-number-->
              <!--    v-model="row.number"-->
              <!--    :disabled="editType === 'info'"-->
              <!--    style="width: 100%"-->
              <!--    size="small"-->
              <!--    :min="1"-->
              <!--    controls-position="right"-->
              <!--    @change="handleNumChange"-->
              <!--  ></el-input-number>-->
              <!--</template>-->
              <template #action="{ row }">
                <div class="fixed-width">
                  <el-button
                    v-if="
                      row.puschaseDetailStatus === 'WAIT_RECEIVE' &&
                      editType === 'edit'
                    "
                    size="mini"
                    icon="el-icon-circle-check"
                    @click="handleConfirm(row)"
                  >
                    确认收货
                  </el-button>
                </div>
              </template>
            </ProTable>
          </template>
          <template v-if="editType === 'audit'" #voucher>
            <div class="title-box">添加支付凭证</div>
            <div class="voucher-box">
              <el-row :gutter="20">
                <el-col :span="6">
                  <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                    "
                  >
                    <span>本次付款金额：</span>
                    <el-input
                      v-model="voucherForm.amount"
                      style="flex: 1"
                      type="number"
                      :disabled="editType === 'info'"
                      placeholder="请输入本次付款金额"
                    />
                  </div>
                </el-col>
                <el-col :span="18">
                  <div style="display: flex">
                    <span>凭证上传：</span>
                    <ProUpload
                      :file-list="voucherForm.payVouchers"
                      :limit="9"
                      :disabled="editType === 'info'"
                      @uploadSuccess="handleUploadSuccess"
                      @uploadRemove="handleUploadRemove"
                    />
                  </div>
                </el-col>
                <el-col :span="24">
                  <div
                    style="display: flex; align-items: center; margin-top: 20px"
                  >
                    <span>备注：</span>
                    <el-input
                      v-model="voucherForm.remark"
                      style="flex: 1"
                      type="textarea"
                      :autosize="{ minRows: 2, maxRows: 4 }"
                      maxlength="255"
                      show-word-limit
                      placeholder="请输入备注"
                      :disabled="editType === 'info'"
                    />
                  </div>
                </el-col>
              </el-row>
            </div>
          </template>
          <template v-if="editType !== 'edit' && isPay" #payRecord>
            <div class="title-box">支付记录</div>
            <ProTable
              ref="PayProTable"
              :show-search="false"
              :show-pagination="false"
              :show-loading="false"
              :show-setting="false"
              :columns="payColumns"
              :data="payTableData"
              :height="300"
            >
              <template #payVouchers="{ row }">
                <div class="voucher-box">
                  <el-image
                    v-for="(item, index) in row.payVouchers"
                    :key="index"
                    :src="item.url"
                    style="width: 100px; height: 100px; margin-right: 10px"
                    fit="contain"
                    :preview-src-list="getPreviewSrcList(row.payVouchers)"
                  >
                    >
                  </el-image>
                </div>
              </template>
            </ProTable>
          </template>
          <template #deliveryTime>
            <el-date-picker
              v-model="addForm.deliveryTime"
              type="date"
              :disabled="editType === 'info' && !checkBtn"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择期望发货时间"
            ></el-date-picker>
          </template>

          <template #arrivalTime>
            <el-date-picker
              v-model="addForm.arrivalTime"
              type="date"
              :disabled="editType === 'info' && !checkBtn"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择期望到货时间"
            ></el-date-picker>
          </template>
        </ProForm>
        <div v-if="checkBtn" class="dialog-footer1">
          <div class="btn-box">
            <el-button type="danger" @click="handelCheck('REJECT')">
              驳回
            </el-button>
            <el-button
              type="primary"
              :loading="reCheckLoading"
              @click="handelCheck('WAIT_RECEIVE')"
            >
              复核完成
            </el-button>
            <el-button @click="handleCloseDrawer">取消</el-button>
          </div>
        </div>
      </div>
      <template v-if="editType === 'audit' || editType === 'add'" #footer>
        <div v-if="editType === 'add'" class="footer">
          <el-button @click="handleDrawerOk">确认采购</el-button>
          <el-button @click="handleCloseDrawer">取消</el-button>
        </div>
        <div v-if="editType === 'audit'" class="footer">
          <el-button type="primary" :loading="auditLoading" @click="handlePay">
            确认上传凭证
          </el-button>
          <el-button @click="handleCloseDrawer">取消</el-button>
        </div>
      </template>
    </ProDrawer>
    <!-- 选配件选择 -->
    <SparePart
      ref="sparePart"
      :dialog-visible.sync="sparePartDialog"
      :spare-type="hostType"
      @confirm="confirmSpare"
    />
  </div>
</template>

<script>
import SparePart from "@/views/procure/cpns/sparePart.vue";
import { dictTreeByCodeApi } from "@/api/user";
import { manufacturerListApi, warehouseListApi } from "@/api/store";
import { productAllApi } from "@/api/dispose";
import {
  addMachinePurchaseApi,
  confirmReceiptMachineApi,
  getMachinePurchaseReceiveListApi,
  updateMachinePurchaseApi,
  uploadPaymentInfoApi,
} from "@/api/procure";
import { Message } from "element-ui";
import ProUpload from "@/components/ProUpload/index.vue";
export default {
  name: "MachineReceiving",
  components: { ProUpload, SparePart },
  props: {
    purchaseCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showDrawer: false,
      drawerTitle: "新增",
      addForm: {},
      columns: [
        {
          dataIndex: "warehouseId",
          title: "选择仓库",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择仓库",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "purchaseCode",
          title: "采购单编号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "purchaseGoods",
          title: "选择供应源",
          isForm: true,
          formOtherSlot: "purchaseGoods",
          formSpan: 24,
        },
        {
          dataIndex: "voucher",
          title: "凭证",
          isForm: true,
          formOtherSlot: "voucher",
          formSpan: 24,
        },
        {
          dataIndex: "payRecord",
          title: "支付记录",
          isForm: true,
          formOtherSlot: "payRecord",
          formSpan: 24,
        },
      ],
      showProTableLoading: false,
      hostTypeListOptions: [], // 主机类型选项
      percentageOptions: [], // 成色列表
      productListOptions: [], // 选配件
      manufacturerList: [],
      formLoading: false,
      editType: "add",
      purchaseGoodsQueryParam: {
        goodsType: [],
      },
      chooseColumns: [
        {
          dataIndex: "puschaseDetailStatus",
          title: "状态",
          isTable: true,
          align: "center",
          formatter: (row) => {
            switch (row.puschaseDetailStatus) {
              case "WAIT_APPROVE":
                return "待确认";
              case "WAIT_RECEIVE":
                return "待收货";
              case "SUCCESS":
                return "已收货";
              case "RETURN":
                return "采购退货";
              case "WAIT_IN":
                return "待入库";
              case "APPLY_RETURN":
                return "采购退货中";
              case "CANCEL":
                return "已退货";
              default:
                return "未知";
            }
          },
          minWidth: 100,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "type",
          title: "主机类型",
          isTable: true,
          align: "center",
          tableSlot: "type",
          minWidth: 100,
        },
        {
          dataIndex: "productId",
          title: "机型/系列/选配件",
          isTable: true,
          align: "center",
          tableSlot: "productId",
          minWidth: 150,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          align: "center",
          tableSlot: "percentage",
          minWidth: 120,
        },

        {
          dataIndex: "tagName",
          title: "标签型号",
          isTable: true,
          tableSlot: "tagName",
          align: "center",
          minWidth: 120,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          align: "center",
          // formatter: (row) => row.deviceOn?.label,
          tableSlot: "deviceOn",
          minWidth: 100,
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          align: "center",
          // formatter: (row) => row.deviceOn?.label,
          tableSlot: "deviceStatus",
          minWidth: 100,
        },
        // {
        //   dataIndex: "warehouseNumber",
        //   title: "库存量",
        //   isTable: true,
        //   align: "center",
        // },
        {
          dataIndex: "purchasePrice",
          title: "单价",
          isTable: true,
          align: "center",
          tableSlot: "price",
          minWidth: 100,
        },
        {
          dataIndex: "originCode",
          title: "原机器编号",
          isTable: true,
          align: "center",
          tableSlot: "originCode",
          minWidth: 120,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isTable: true,
          align: "center",
          tableSlot: "deviceSequence",
          minWidth: 120,
        },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          align: "center",
          tableSlot: "location",
          minWidth: 100,
        },
        // {
        //   dataIndex: "blackWhiteCounter",
        //   title: "黑白计数器",
        //   isTable: true,
        //   align: "center",
        //   tableSlot: "blackWhiteCounter",
        //   minWidth: 100,
        // },
        // {
        //   dataIndex: "colorCounter",
        //   title: "彩色计数器",
        //   isTable: true,
        //   align: "center",
        //   tableSlot: "colorCounter",
        //   minWidth: 100,
        // },
        // {
        //   dataIndex: "fiveColourCounter",
        //   title: "五色计数器",
        //   isTable: true,
        //   align: "center",
        //   tableSlot: "fiveColourCounter",
        //   minWidth: 100,
        // },
        {
          dataIndex: "action",
          title: "操作",
          fixed: "right",
          width: 100,
          align: "center",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
        },
      ],
      purchaseGoodsData: [],
      showChooseDialog: false,
      // 供应商Form
      supplierRules: {
        manufacturerId: [
          {
            required: true,
            message: "请选择供应商",
            trigger: "change",
          },
        ],
      },
      supplierList: [],
      checkBtn: false,
      productIdName: "",
      options: [],
      reCheckLoading: false,
      // 选配件弹窗
      sparePartDialog: false,
      hostType: "", // 主机类型
      selectedSparePart: null, // 当前操作行的数据
      deviceOnOptions: [],
      deviceStatusOptions: [],
      voucherForm: {
        amount: null,
        payVouchers: [],
        remark: "",
      }, // 付款信息
      auditLoading: false,
      payTableData: [],
      payColumns: [
        {
          dataIndex: "createdAt",
          title: "支付时间",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "createdBy",
          title: "支付人",
          formatter: (row) => row.createdBy?.name,
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "amount",
          title: "支付金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "payVouchers",
          title: "支付凭证",
          isTable: true,
          tableSlot: "payVouchers",
          minWidth: 350,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
          minWidth: 200,
        },
      ], // 支付记录
      isPay: true,
    };
  },
  watch: {
    // purchaseGoodsData: {
    //   handler(val) {
    //     this.handleNumChange();
    //   },
    // },
    "addForm.manufacturerId": {
      handler(val) {
        const findManufacturer = this.manufacturerList.find(
          (item) => item.id === val
        );
        if (findManufacturer) {
          this.addForm.manufacturerCode = findManufacturer?.code;
        }
      },
    },
  },
  mounted() {
    productAllApi().then((res) => {
      this.options = res.data;
    });
    dictTreeByCodeApi(1100).then((res) => {
      this.deviceOnOptions = res.data;
    });
    dictTreeByCodeApi(6600).then((res) => {
      this.deviceStatusOptions = res.data;
    });
    manufacturerListApi().then((res) => {
      this.manufacturerList = res.data;
      this.supplierList = res.data.map((item) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
    });
  },
  methods: {
    // 上传支付凭证
    handlePay() {
      // - +this.addForm.paidAmount
      if (+this.voucherForm.amount > +this.addForm.waitPayAmount) {
        this.$message.error("本次付款金额不能大于未付款金额");
        return;
      }
      this.$confirm("是否确认上传凭证?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.auditLoading = true;
        const args = {
          ...this.voucherForm,
          id: this.addForm.id,
        };
        uploadPaymentInfoApi(args)
          .then((res) => {
            if (res.code === 200) {
              this.$message.success("操作成功");
              this.handleCloseDrawer();
            }
          })
          .finally(() => {
            this.auditLoading = false;
          });
      });
    },
    handleDrawerOk() {
      this.$refs.ProForm.handleSubmit();
    },
    handleCloseDrawer() {
      delete this.addForm.manufacturerCode;
      this.showDrawer = false;
      this.$emit("refresh");
    },
    async show(row, type, isPay = true) {
      switch (type) {
        case "add":
          this.drawerTitle = "新增机器采购";
          break;
        case "edit":
          this.drawerTitle = "机器采购收货";
          break;
        case "check":
          this.drawerTitle = "复核机器采购单";
          break;
        case "info":
          this.drawerTitle = "机器采购明细";
          break;
        case "audit":
          this.drawerTitle = "支付机器采购单";
          break;
        default:
          break;
      }
      // if (type === "check") {
      //   this.editType = "audit";
      //   this.checkBtn = true;
      // } else {
      this.editType = type;
      this.checkBtn = false;
      // }
      if (type !== "add") {
        this.chooseColumns = this.$options.data().chooseColumns;
        // if (this.editType !== "audit") {
        //   this.$options.data().chooseColumns.length ===
        //     this.chooseColumns.length &&
        //     (this.chooseColumns = this.chooseColumns.splice(
        //       0,
        //       this.chooseColumns.length - 2
        //     ));
        // }
        this.voucherForm = {
          amount: null,
          payVouchers: [],
          remark: "",
        };
        this.isPay = isPay;
        await this.getDetails(row.id);
      } else {
        this.addForm = {};
        this.purchaseGoodsData = [];
        // this.chooseColumns = this.$options.data().chooseColumns;
        // this.chooseColumns.splice(this.chooseColumns.length - 3, 1);
      }

      this.purchaseGoodsQueryParam = {};
      await this.getProductType();
      await this.getPercentage();
      this.showDrawer = true;
    },
    async formSubmit(val) {
      try {
        this.formLoading = true;
        // const userInfo = JSON.parse(localStorage.getItem("userInfo"));
        // initiatorId: userInfo.id,
        //     initiatorName: userInfo.name,
        if (!this.purchaseGoodsData.length) {
          throw new Error("请添加采购商品");
        }
        this.purchaseGoodsData.forEach((item, index) => {
          if (!item.hostType) {
            throw new Error(`请选择第${index + 1}行主机类型`);
          }
          if (!item.productId) {
            throw new Error(`请选择第${index + 1}行机型/选配件`);
          }
          if (!item.percentage) {
            throw new Error(`请选择第${index + 1}行成色`);
          }
          if (!item.number) {
            throw new Error(`请输入第${index + 1}行采购数量`);
          }
        });
        const args = {
          ...this.addForm,
          purchaseDetailDtos: this.purchaseGoodsData,
        };
        const result = await addMachinePurchaseApi(args);
        if (result.code === 200) {
          Message.success("添加成功");
          this.handleCloseDrawer();
          this.$emit("refresh");
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.formLoading = false;
      }
    },
    async getDetails(id) {
      try {
        this.showProTableLoading = true;
        const result = await getMachinePurchaseReceiveListApi(id);
        if (result.code === 200) {
          this.addForm = result.data;
          this.voucherForm.amount = +this.addForm.waitPayAmount;
          // this.addForm.waitPayAmount - +this.addForm.paidAmount;
          // this.voucherForm.payVouchers = this.addForm.payVouchers;
          this.voucherForm.payVouchers = [];
          // this.voucherForm.remark = this.addForm.remark;
          this.voucherForm.remark = "";
          this.purchaseGoodsData = result.data.machines || [];
          this.payTableData = result.data.machinePurchasePays || [];
          this.purchaseGoodsData.forEach((item) => {
            if (!item.tagName && this.editType === "edit") {
              this.$set(
                item,
                "tagName",
                this.editType === "edit" ? item.productName || "" : ""
              );
            }
            Object.keys(item).forEach((key) => {
              if (typeof item[key] === "object" && item[key] !== null) {
                if (Object.keys(item[key]).length === 0) {
                  delete item[key];
                } else {
                  item[key] = item[key].value ? item[key].value : item[key];
                }
              } else {
                item[key] = item[key].value ? item[key].value : item[key];
              }
            });
            if (item.hostType === "2008" && item.fullProductPath) {
              const trimmedStr = item.fullProductPath.trim("/");
              item.productIdName = trimmedStr.split("/").filter(Boolean);
            } else if (item.hostType === "2008" && !item.fullProductPath) {
              item.productIdName = item.productId;
            }
          });
          this.$refs.purchaseGoodsTable &&
            (this.$refs.purchaseGoodsTable.listLoading = false);
          // 供应商编号回显
          const findManufacturer = this.manufacturerList.find(
            (item) => item.id === this.addForm.manufacturerId
          );
          if (findManufacturer) {
            this.addForm.manufacturerCode = findManufacturer?.code;
          }
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.showProTableLoading = false;
      }
    },
    // 复核通过
    async handelCheck(status) {
      try {
        if (!this.purchaseGoodsData.length) {
          return this.$message.error("请添加采购商品");
        }
        this.purchaseGoodsData.forEach((item, index) => {
          if (!item.hostType) {
            throw new Error(`请选择第${index + 1}行主机类型`);
          }
          if (!item.productId) {
            throw new Error(`请选择第${index + 1}行机型/选配件`);
          }
          if (!item.percentage) {
            throw new Error(`请选择第${index + 1}行成色`);
          }
          if (!item.number) {
            throw new Error(`请输入第${index + 1}行采购数量`);
          }
        });
        this.$confirm("是否确认通过该机器采购单?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.reCheckLoading = true;
            this.formLoading = true;
            const args = {
              id: this.addForm.id,
              purchaseCode: this.addForm.purchaseCode,
              purchaseDetailDtos: this.purchaseGoodsData,
              status: status,
            };
            const result = await updateMachinePurchaseApi(args);
            if (result.code === 200) {
              Message.success("复核完成");
              this.reCheckLoading = false;
              this.$emit("refresh");
              this.handleCloseDrawer();
            }
          })
          .finally(() => {
            this.reCheckLoading = false;
            this.formLoading = false;
          });
      } catch (error) {
        this.$message.error(error.message);
      }
    },
    getPreviewSrcList(list) {
      return list.map((item) => item.url);
    },
    handleSupplierChange() {
      this.purchaseGoodsData = [];
    },
    // 新增一条采购单
    addPurchaseGoodsData() {
      if (!this.addForm.manufacturerId) {
        Message.error("请先选择供应商");
        return;
      }
      this.purchaseGoodsData.push({
        hostType: "", // 主机类型
        productId: "", // 设备Id
        manufacturerId: this.addForm.manufacturerId, // 供应商id
        number: 1, // 采购输入
        percentage: null, // 成色
        price: null, // 单价
      });
    },
    // 获取主机类型
    async getPercentage() {
      try {
        const result = await dictTreeByCodeApi(2500);
        if (result.code === 200) {
          this.percentageOptions = result.data;
        }
      } catch (error) {
        this.percentageOptions = [];
      }
    },
    async getProductType() {
      try {
        const result = await dictTreeByCodeApi(2000);
        if (result.code === 200) {
          this.hostTypeListOptions = result.data;
        }
      } catch (error) {
        this.hostTypeListOptions = [];
      }
    },
    confirmSpare(row) {
      this.$set(
        this.selectedSparePart,
        "productName",
        row.machineNumber
          ? `${row.modeType} / ${row.machineNumber}`
          : row.modeType
      );
      this.selectedSparePart.productId = row.id;
      this.sparePartDialog = false;
    },
    // 选择选配件
    handleSelectSpare(row) {
      this.selectedSparePart = null;
      if (!row.hostType && row.hostType !== "2008") {
        Message.warning("请选择主机类型");
        return;
      }
      this.selectedSparePart = row;
      this.hostType = row.hostType;
      this.sparePartDialog = true;
    },
    // handlePriceChange() {
    //   this.handleNumChange();
    // },
    /**
     * 计算单行价格
     * @param item
     */
    // handleNumChange(item) {
    //   const addPrice = this.purchaseGoodsData.reduce((total, row) => {
    //     const price = Number(row.price) || 0;
    //     const planNumber = Number(row.number) || 0;
    //     row.purchasePrice = mulAmount(price, planNumber).toFixed(2);
    //     return total + mulAmount(price, planNumber);
    //   }, 0);
    //   this.addForm.price = addPrice.toFixed(2);
    //   this.$forceUpdate();
    // },
    handleHostTypeChange(e, row) {
      row.productId = "";
      row.productIdName = [];
    },
    // 产品树
    handleProductIdChange(e, row) {
      if (e) {
        row.productId = e[e.length - 1];
      } else {
        row.productId = "";
      }
    },
    // 单行确认收货
    handleConfirm(row) {
      if (!row.tagName) {
        return this.$message.warning("请输入标签型号");
      }
      if (!row.deviceOn) {
        return this.$message.warning("请选择设备新旧");
      }
      if (!row.deviceStatus) {
        return this.$message.warning("请选择设备状态");
      }
      this.$confirm("是否确认收到货?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          confirmReceiptMachineApi({
            id: this.addForm.id,
            machines: [row],
          }).then(async (res) => {
            this.$message.success("操作成功");
            await this.getDetails(this.addForm.id);
          });
        })
        .catch(() => {});
    },
    handleUploadSuccess(result) {
      if (!this.voucherForm.payVouchers) {
        this.$set(this.voucherForm, "payVouchers", []);
      }
      this.voucherForm["payVouchers"].push(result);
    },
    handleUploadRemove(file) {
      const index = this.voucherForm["payVouchers"].findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.voucherForm["payVouchers"].splice(index, 1);
    },
    handleDelete(row, i) {
      this.purchaseGoodsData.splice(i, 1);
      this.handleNumChange();
    },
  },
};
</script>

<style lang="scss" scoped></style>
