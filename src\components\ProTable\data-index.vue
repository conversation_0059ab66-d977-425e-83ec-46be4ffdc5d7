<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-10 09:44:22
 * @Description: 
 -->
<template>
  <div class="pro-table" :class="sticky ? 'sticky' : ''">
    <SearchForm
      v-if="showSearch"
      :search-list="columns.filter((i) => i.isSearch)"
      :query-param="queryParam"
      :loading="listLoading"
      @search="refresh"
    >
      <template #header>
        <slot name="searchHeader"></slot>
      </template>
      <template #footer>
        <slot name="searchFooter"></slot>
      </template>
    </SearchForm>

    <el-table
      ref="ProElTable"
      :key="key"
      v-loading="listLoading"
      style="overflow: hidden"
      :data="data"
      highlight-current-row
      v-bind="$attrs"
      :height="height"
      :tree-props="treeProps"
      @current-change="handleCurrentChange"
    >
      <el-table-column
        v-if="showSelection"
        type="selection"
        align="center"
        width="55"
      />
      <el-table-column
        v-if="showIndex"
        label="序号"
        width="50"
        align="center"
        type="index"
      />
      <el-table-column
        v-for="item in tableColumns"
        :key="item.dataIndex"
        :label="item.title"
        :min-width="item.minWidth"
        :width="item.width"
        :fixed="item.fixed"
        :align="item.align ? item.align : 'left'"
        :sortable="item.sortable || null"
        :prop="item.sortable ? item.dataIndex : null"
        :formatter="item.formatter"
      >
        <template v-if="!item.formatter" #default="scope">
          <span v-if="!item.tableSlot">{{ scope.row[item.dataIndex] }}</span>
          <span v-else-if="item.tableSlot === 'date'">
            {{ scope.row[item.dataIndex] | parseTime }}
          </span>
          <slot
            v-else
            :name="item.tableSlot"
            :row="scope.row"
            :index="scope.$index"
          ></slot>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="showPagination && localPagination.total"
      :page="localPagination.page"
      layout="total,prev, pager, next"
      :page-size="localPagination.pageSize"
      :total="localPagination.total"
      @pagination="loadData"
    />
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue"; // secondary package based on el-pagination
import SearchForm from "@/components/SearchForm/index.vue"; // SearchForm
import { mapState } from "vuex";
import { cloneDeep } from "lodash";

export default {
  name: "ProTable",
  components: {
    Pagination,
    SearchForm,
  },
  props: {
    treeProps: {
      type: Object,
      default: () => {
        return {};
      },
    },
    /* eslint-disable vue/require-default-prop */
    lazyLoad: {
      type: Boolean,
      default: false,
    },
    queryParam: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 顶部搜索
    showSearch: {
      type: Boolean,
      default: true,
    },
    height: {
      type: [Number, String],
      default: 250,
    },
    pageSize: {
      type: Number,
      default: 15,
    },
    showSetting: {
      type: Boolean,
      default: true,
    },
    showIndex: {
      type: Boolean,
      default: false,
    },
    sticky: {
      type: Boolean,
      default: false,
    },
    showSelection: {
      type: Boolean,
      default: false,
    },
    showPagination: {
      type: Boolean,
      default: true,
    },
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
    columns: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      search: "",
      ProElTable: "",
      key: 0,
      list: null,
      listLoading: false,
      localPagination: {
        total: 0,
        page: 1,
        pageSize: this.pageSize,
      },
      tableColumns: [],
      isMobile: false,
    };
  },
  computed: {
    ...mapState({
      isLogo: (state) => state.setting.isLogo,
      // 'routes': state => state.routes.routes
    }),
    menuBgColor() {
      return "";
    },
  },
  //   watch: {
  //        () => prop.lazyLoad,
  //       () => {
  //   if (!prop.lazyLoad) {
  //     initColumns()
  //     loadData()
  //   }
  // }

  //   },
  watch: {
    columns: {
      handler() {
        if (!this.lazyLoad) {
          this.initColumns();
        }
      },
      deep: true,
    },
    localPagination: {
      handler(val) {
        this.localPaginationNew = cloneDeep(val);
      },
      deep: true,
    },
  },
  created() {
    if (!this.lazyLoad) {
      this.initColumns();
      // this.loadData()
    }
  },
  methods: {
    initColumns() {
      this.tableColumns = cloneDeep(this.columns).filter((i) => i.isTable);
      // .map((i) => {
      //   return i
      // })
    },
    loadData(val) {
      if (val) {
        this.localPagination = cloneDeep(val);
      }
      this.$emit("loadData");
      // if (
      //     (typeof result === 'object' || typeof result === 'function') &&
      //     typeof result.then === 'function'
      // ) {
      //     result.then((r) => {
      //         if (!r || r.length === 0 || r.data.length === 0) {
      //             return null
      //         }
      //         // 此处可修改最终数据获取
      //         this.list = r.data.rows ? r.data.rows : r.data.list ? r.data.list : r.data

      //         this.localPagination.total = r.data.total ? parseInt(r.data.total) : 0
      //     })
      //         .finally(() => {
      //             this.listLoading = false
      //         })
      // }
    },
    refresh(bool = false) {
      if (bool) {
        this.localPagination.page = 1;
      }
      this.loadData();
    },
    updateTable() {
      key.value = key.value += 1;
    },
    handleCurrentChange(val) {
      this.$emit("CurrentChange", val);
    },
    doLayout() {
      this.$refs.ProElTable.doLayout();
    },
  },
};
</script>
<style></style>
