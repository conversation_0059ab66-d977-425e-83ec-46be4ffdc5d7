// 运行时配置管理工具

// 扩展 Window 接口
declare global {
  interface Window {
    APP_CONFIG?: {
      API_BASE_URL: string;
      APP_TITLE: string;
    };
  }
}

// 默认配置
const DEFAULT_CONFIG = {
  API_BASE_URL: 'http://localhost:8080/api',
  APP_TITLE: '复印机维修服务'
};

/**
 * 获取API基础URL
 */
export const getApiBaseUrl = (): string => {
  return window.APP_CONFIG?.API_BASE_URL || DEFAULT_CONFIG.API_BASE_URL;
};

/**
 * 获取应用标题
 */
export const getAppTitle = (): string => {
  return window.APP_CONFIG?.APP_TITLE || DEFAULT_CONFIG.APP_TITLE;
};
