<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-26 09:46:46
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-19 11:17:47
 * @Description: 
 -->
<template>
  <div class="app-container">
    <ProDialog
      :value="dialogVisible"
      title="维修工单列表"
      :no-footer="true"
      :is-destroy="true"
      width="75%"
      top="2%"
      @cancel="handleClose"
    >
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane :label="`已接单 - ${engineerInfo.name}`" name="first" lazy>
          <EngineerOrderDetail
            v-if="dialogVisible && activeName === 'first'"
            ref="engineerOrderDetail"
            :engineer-info="engineerInfo"
            @switch="switchTab"
            @refreshWorkOrder="refreshWorkOrder"
          />
        </el-tab-pane>
        <el-tab-pane label="维修工单 - 未结束" name="second" lazy>
          <PendingWorkOrder
            v-if="dialogVisible && activeName === 'second'"
            ref="pendingWorkOrder"
            :engineer-info="engineerInfo"
            @refreshWorkOrder="refreshWorkOrder"
          />
        </el-tab-pane>
      </el-tabs>
    </ProDialog>
  </div>
</template>

<script>
import EngineerOrderDetail from "@/views/engineer/components/engineerOrderDetail.vue";
import PendingWorkOrder from "@/views/engineer/components/pendingWorkOrder.vue";
export default {
  name: "WorkOrderInfo",
  components: {
    EngineerOrderDetail,
    PendingWorkOrder,
  },
  data() {
    return {
      activeName: "first",
      dialogVisible: false,
      engineerInfo: {},
    };
  },
  methods: {
    show(engineerInfo) {
      this.activeName = "first";
      this.dialogVisible = true;
      this.engineerInfo = engineerInfo;
    },
    switchTab() {
      this.activeName = "second";
    },
    refreshWorkOrder() {
      this.$emit("refreshWorkOrder");
      if (this.activeName === "first") {
        this.$refs.pendingWorkOrder &&
          this.$refs.pendingWorkOrder.$refs &&
          this.$refs.pendingWorkOrder.$refs.ProTable &&
          this.$refs.pendingWorkOrder.$refs.ProTable.refresh();
      }
      if (this.activeName === "second") {
        this.$refs.engineerOrderDetail &&
          this.$refs.engineerOrderDetail.$refs &&
          this.$refs.engineerOrderDetail.$refs.ProTable &&
          this.$refs.engineerOrderDetail.$refs.ProTable.refresh();
      }
    },
    handleClose() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
