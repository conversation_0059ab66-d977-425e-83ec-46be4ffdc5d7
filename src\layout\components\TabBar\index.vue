<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-28 10:54:02
 * @Description: 
 -->
<template>
  <div id="tabs-bar-container" class="tabs-bar-container horizontal">
    <el-tabs
      v-model="tabActive"
      type="card"
      class="tabs-content"
      @tab-click="handleTabClick"
      @tab-remove="handleTabRemove"
    >
      <el-tab-pane
        v-for="item in visitedRoutes"
        :key="item.path"
        :name="item.path"
        :closable="!isAffix(item)"
      >
        <template #label>
          <div class="item">
            <i :class="item.icon"></i>
            <span>
              {{ item.meta.title }}
            </span>
          </div>
        </template>
      </el-tab-pane>
    </el-tabs>
    <el-popover
      placement="bottom"
      width="auto"
      trigger="hover"
      @show="handleShow"
      @hide="handleHide"
    >
      <template #reference>
        <span class="more" :class="{ active: visible }" style="cursor: pointer">
          <i class="el-icon-s-operation"></i>
        </span>
      </template>
      <div
        v-for="(item, index) in commandList"
        :key="index"
        class="command-item"
        @click="handleCommand(item.command)"
      >
        <i :class="item.icon"></i>
        <span class="command-label">{{ item.text }}</span>
      </div>
    </el-popover>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
export default {
  name: "Layout",
  components: {},
  mixins: [],
  data() {
    return {
      affixTabs: [],
      tabActive: "",
      visible: false,
      commandList: [
        // {
        //   command: 'refreshRoute',
        //   text: '重新加载',
        //   icon: 'el-icon-refresh',
        // },
        {
          command: "closeOtherstabs",
          text: "关闭其它",
          icon: "el-icon-close",
        },
        {
          command: "closeLefttabs",
          text: "关闭左侧",
          icon: "el-icon-back",
        },
        {
          command: "closeRighttabs",
          text: "关闭右侧",
          icon: "el-icon-right",
        },
        {
          command: "closeAlltabs",
          text: "关闭所有",
          icon: "el-icon-minus",
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      collapse: "collapse",
      routes: "routes",
      visitedRoutes: "visitedRoutes",
    }),
  },
  watch: {
    $route: {
      handler(route) {
        this.initTabs();
        this.addTabs();
        let tabActive = "";
        this.visitedRoutes.forEach((item, index) => {
          if (item.path === this.$route.path) {
            tabActive = item.path;
          }
        });
        this.tabActive = tabActive;
      },
      immediate: true,
    },
  },
  methods: {
    async handleTabRemove(tabActive) {
      let view;
      this.visitedRoutes.forEach((item, index) => {
        if (tabActive === item.path) {
          view = item;
        }
      });
      const { visitedRoutes } = await this.$store.dispatch(
        "tabsBar/delRoute",
        view
      );
      if (this.isActive(view)) {
        this.toLastTag(visitedRoutes, view);
      }
    },
    handleTabClick(tab) {
      const route = this.visitedRoutes.filter((item, index) => {
        if (tab.index == index) return item;
      })[0];
      if (this.$route.path !== route.path) {
        this.$router.push({
          path: route.path,
          query: route.query,
          fullPath: route.fullPath,
        });
      } else {
        return false;
      }
    },
    isActive(route) {
      return route.path === this.$route.path;
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix;
    },
    filterAffixTabs(routes, basePath = "/") {
      let tabs = [];
      routes.forEach((route) => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path);
          tabs.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta },
          });
        }
        if (route.children) {
          const tempTabs = this.filterAffixTabs(route.children, route.path);
          if (tempTabs.length >= 1) {
            tabs = [...tabs, ...tempTabs];
          }
        }
      });
      return tabs;
    },
    initTabs() {
      const affixTabs = (this.affixTabs = this.filterAffixTabs(this.routes));
      for (const tag of affixTabs) {
        if (tag.name) {
          this.$store.dispatch("tabsBar/addVisitedRoute", tag);
        }
      }
    },
    addTabs() {
      const { name } = this.$route;
      if (name) {
        this.$store.dispatch("tabsBar/addVisitedRoute", this.$route);
      }
      return false;
    },
    handleCommand(command) {
      switch (command) {
        case "refreshRoute":
          this.refreshRoute();
          break;
        case "closeOtherstabs":
          this.closeOtherstabs();
          break;
        case "closeLefttabs":
          this.closeLefttabs();
          break;
        case "closeRighttabs":
          this.closeRighttabs();
          break;
        case "closeAlltabs":
          this.closeAlltabs();
          break;
      }
    },
    refreshRoute() {
      this.store.dispatch("setting/setRouterView", false);
      this.$nextTick(() => {
        this.store.dispatch("setting/setRouterView", true);
      });
      //  this.$baseEventBus.$emit('reloadrouter-view')
    },
    async closeSelectedTag(view) {
      const { visitedRoutes } = await this.$store.dispatch(
        "tabsBar/delRoute",
        view
      );
      if (this.isActive(view)) {
        this.toLastTag(visitedRoutes, view);
      }
    },
    async closeOtherstabs() {
      const view = await this.toThisTag();
      await this.$store.dispatch("tabsBar/delOthersRoutes", view);
    },
    async closeLefttabs() {
      const view = await this.toThisTag();
      await this.$store.dispatch("tabsBar/delLeftRoutes", view);
    },
    async closeRighttabs() {
      const view = await this.toThisTag();
      await this.$store.dispatch("tabsBar/delRightRoutes", view);
    },
    async closeAlltabs() {
      const view = await this.toThisTag();
      const { visitedRoutes } = await this.$store.dispatch(
        "tabsBar/delAllRoutes"
      );
      if (this.affixTabs.some((tag) => tag.path === view.path)) {
        return;
      }
      this.toLastTag(visitedRoutes, view);
    },
    toLastTag(visitedRoutes, view) {
      const latestView = visitedRoutes.slice(-1)[0];
      if (latestView) {
        this.$router.push(latestView);
      } else {
        this.$router.push("/");
      }
    },
    async toThisTag() {
      const view = this.visitedRoutes.filter((item, index) => {
        if (item.path === this.$route.fullPath) {
          return item;
        }
      })[0];
      if (this.$route.path !== view.path) this.$router.push(view);
      return view;
    },
    handleShow() {
      this.visible = true;
    },
    handleHide() {
      this.visible = false;
    },
  },
  // methods: {
  //   async handleTabRemove(tabActive) {
  //     let view
  //     this.visitedRoutes.forEach((item, index) => {
  //       if (tabActive == item.path) {
  //         view = item
  //       }
  //     })
  //     const { visitedRoutes } = await this.$store.dispatch(
  //       'tabsBar/delRoute',
  //       view
  //     )
  //     if (this.isActive(view)) {
  //       this.toLastTag(visitedRoutes, view)
  //     }
  //   },
  //   handleTabClick(tab) {
  //     const route = this.visitedRoutes.filter((item, index) => {
  //       if (tab.index == index) return item
  //     })[0]
  //     if (this.$route.path !== route.path) {
  //       this.$router.push({
  //         path: route.path,
  //         query: route.query,
  //         fullPath: route.fullPath,
  //       })
  //     } else {
  //       return false
  //     }
  //   },
  //   handleShow() {
  //     this.visible = true;
  //   },
  //   handleHide() {
  //     this.visible = false;
  //   },
  //   isAffix(tag) {
  //     return tag.meta && tag.meta.affix
  //   },
  // },
};
</script>

<style lang="scss" scoped>
.tabs-bar-container {
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: space-between;
  height: $base-tabs-bar-height;
  padding-right: $base-padding;
  padding-left: $base-padding;
  user-select: none;
  background: $base-color-white;
  border-top: 1px solid #f6f6f6;

  &.horizontal {
    padding: 0 40px;
  }

  :deep(.fold-unfold) {
    margin-right: $base-padding;
  }

  :deep(.el-tabs__item) {
    display: inline-flex;
    align-items: center;
  }

  .item {
    display: inline-flex;
    align-items: center;

    .menu-icon {
      display: flex;
      padding-right: $base-margin-5;
    }
  }

  .tabs-content {
    width: calc(100% - 90px);
    height: $base-tag-item-height;

    :deep(.el-tabs__nav-next, .el-tabs__nav-prev) {
      height: $base-tag-item-height;
      line-height: $base-tag-item-height;
    }

    :deep(.el-tabs__header) {
      border-bottom: 0;

      .el-tabs__nav {
        border: 0;

        .el-tabs__item {
          box-sizing: border-box;
          height: $base-tag-item-height;
          margin-right: $base-margin-5;
          line-height: $base-tag-item-height;
          border: none;
          border-radius: $base-border-radius;
          transition: padding 0.5s cubic-bezier(0.645, 0.045, 0.355, 1) !important;

          &.is-active {
            color: $base-color-primary;
            background: $base-color-primary-light9;
            border: none;
            border-bottom: 2px solid;
          }

          &:hover {
            color: $base-color-primary;
            background: $base-color-primary-light9;
            border: none;
            border-bottom: 2px solid;
          }
        }
      }
    }
  }
}

.command-item {
  display: flex;
  align-content: center;
  align-items: center;
  padding: 5px 10px;
  cursor: pointer;

  .command-label {
    padding-left: 5px;
  }

  &:hover {
    color: $base-color-primary;
    background-color: $base-color-primary-light9;
  }

  .icon {
    display: flex;
  }
}

.more {
  display: flex;
  align-content: center;
  align-items: center;
  color: $base-font-color;
  cursor: pointer;
  transition: all 0.5s;

  &.active {
    color: $base-color-primary !important;
    transform: rotate(180deg);
  }
}
</style>
