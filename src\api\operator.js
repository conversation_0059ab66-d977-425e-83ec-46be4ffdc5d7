/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-25 14:18:11
 * @Description:
 */
/*
 * @Description:
 * @Autor: shh
 * @Date: 2022-04-17 19:20:26
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-25 14:18:11
 */
import axios from "axios";
import { get, post, put, del, down } from "@/utils/request";

// ================== 主订单 ====================
export const operatorTradeOrderPageApi = (data) =>
  post(`/operator/trade-order/page`, data); // 订单分页
// 查询期结订单
export const getSaleCycleOrderPageApi = (data) =>
  get(`/operator/trade-order/cycle`, data);
// 机器销售单发货
export const operatorTradeOrderDeliveryApi = (data) =>
  post(`/operator/trade-order/delivery`, data);
export const operatorTradeOrderDetailApi = (id) =>
  get(`/operator/trade-order/detail/${id}`); // 查询订单详情
export const operatorTradeOrderCloseApi = (data) =>
  put(`/operator/trade-order/close`, data); // 关闭订单
export const optionsGetRegionApi = () => get(`/region/tree`); // 关闭订单

export const OrderTracesApi = (id) => get(`/operator/trade-order/traces/${id}`); // 查询订单物流
// 审核领料订单
export const operatorTradeOrderAuditApi = (data) =>
  put(`/operator/trade-order/audit`, data);
// 审核期结销售单
export const operatorTradeOrderSaleAuditApi = (data) =>
  put(`/operator/trade-order/auditOrder`, data);

// 根据用户ID获取收货地址
export const getAddressListApi = (customerId) =>
  get(`/customer/cust-address/${customerId}`);
//入库单关联单号详情查询
export const getInboundOrderDetailApi = (data) =>
  post(`/applyReturn/page`, data);
// 订单统计查询
export const getOrderStatisticsApi = (data) =>
  post(`/operator/trade-order/total`, data);
// 配送方式变更记录
export const getDeliveryMethodChangeRecordApi = (data) =>
  get(`/storage-invoice/logisticsChangeRecords`, data);
// 处理配送方式变更记录
export const handleDeliveryMethodChangeRecordApi = (
  id,
  isFeeProcessed = 1,
  processRemark
) =>
  put(
    `/storage-invoice/logisticsChangeRecords/${id}/${isFeeProcessed}?processRemark=${processRemark}`
  );

// ============================= 销售明细  ===============================
// 销售明细分页查询
export const getSaleDetailListApi = (data) =>
  post(`/operator/trade-order/orderDetail/page`, data);
// 销售明细统计数据
export const getSaleDetailStatisticsApi = (data) =>
  post(`/operator/trade-order/detailData`, data);

// ============================= 月度收入  ===============================
// 分页查询
export const monthIncomePageApi = (data) =>
  post(`/statistics/queryMonthIncomeList`, data);

// 统计数据
export const monthIncomeStatisticsApi = (data) =>
  post(`/month-income/total`, data);

// ============================= 月度支出  ===============================
// 分页查询
export const monthExpensesPageApi = (data) =>
  post(`/statistics/queryMonthExpenditureList`, data);
// 统计数据
export const monthExpensesStatisticsApi = (data) =>
  post(`/month-expenses/total`, data);
// 关闭机器销售单
export const closeMachineSaleOrderApi = (id) =>
  del(`/other-income-expenses/deleted/${id}`);
// ============================= 其他收入、支出   ===============================
// 分页查询
export const otherIncomePageApi = (data) =>
  post(`/other-income-expenses/monthStatics`, data);
// 新增收入登记
export const otherIncomeAddApi = (params) =>
  post(`/other-income-expenses/save`, params);
// 收入登记明细
export const otherIncomeDetailApi = (data) =>
  post(`/other-income-expenses/page`, data);
// 删除开单记录
export const otherIncomeDeleteApi = (id) =>
  del(`/other-income-expenses/deleted/${id}`);
// 登记详情
export const otherIncomeDetailOneApi = (data) =>
  post(`/other-income-expenses/details`, data);
// 删除开单数据
export const otherIncomeDeleteOneApi = (data) =>
  del(`/other-income/deleteOne`, data);
// 编辑开单数据
export const otherIncomeEditApi = (params) =>
  post("/other-income-expenses/save", params);
// 获取列表统计数据
export const otherIncomeStatApi = (data) =>
  post(`/other-income-expenses/monthStaticsSummary`, data);
