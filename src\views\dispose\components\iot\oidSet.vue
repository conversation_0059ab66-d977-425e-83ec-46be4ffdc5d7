<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:53
 * @Description: OID配置表（新）
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-index
      :show-selection="false"
      show-search
      show-loading
      show-pagination
      :reserve-selection="true"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd(null, 'add')"
        >
          新增配置
        </el-button>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleAdd(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 新建弹窗 -->
    <ProDialog
      :value="addDialog"
      :title="addType"
      :confirm-text="'保存'"
      width="50%"
      @ok="handleAddDialogOk"
      @cancel="handleAddDialogCancel"
    >
      <ProForm
        ref="addForm"
        :form-param="addForm"
        :form-list="addColumns"
        :confirm-loading="addFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="'add'"
        @proSubmit="addFormSubmit"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import { Message, MessageBox } from "element-ui";
import {
  getOidConfigNewApi,
  addOidConfigNewApi,
  editOidConfigNewApi,
  deleteOidNewApi,
} from "@/api/iot";
import { filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";

export default {
  name: "OidSet",
  components: { ProTable },
  data() {
    return {
      tableData: [],
      columns: [
        {
          dataIndex: "configType",
          title: "配置类型",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 80,
        },
        {
          dataIndex: "strType",
          title: "配置说明",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 80,
        },
        {
          dataIndex: "dealType",
          title: "处理类型",
          isTable: true,
          isSearch: true,
          valueType: "input",
          clearable: true,
          minWidth: 80,
        },
        {
          dataIndex: "oidName",
          title: "OID中文名",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 80,
        },
        {
          dataIndex: "keyName",
          title: "键名",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "valueType",
          title: "值类型",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 80,
        },
        {
          dataIndex: "sort",
          title: "排序",
          isTable: true,
          minWidth: 60,
        },
        {
          dataIndex: "mainOid",
          title: "主OID",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "secondOid",
          title: "副OID",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "matchData",
          title: "匹配数据",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "updatedAt",
          title: "上传时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 140,
        },
      ],
      addType: "新建",
      queryParam: {},
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      selection: [],
      addDialog: false,
      addForm: {},
      addFormLoading: false,
      addColumns: [
        {
          dataIndex: "configType",
          title: "配置类型",
          isForm: true,
          formSpan: 12,
          valueType: "input",
        },
        {
          dataIndex: "strType",
          title: "配置说明",
          isForm: true,
          formSpan: 12,
          valueType: "input",
        },
        {
          dataIndex: "dealType",
          title: "处理类型",
          isForm: true,
          formSpan: 12,
          valueType: "input",
        },
        {
          dataIndex: "oidName",
          title: "OID中文名",
          isForm: true,
          formSpan: 12,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入OID中文名",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "keyName",
          title: "键名",
          isForm: true,
          formSpan: 12,
          valueType: "input",
        },
        {
          dataIndex: "valueType",
          title: "值类型",
          isForm: true,
          formSpan: 12,
          valueType: "input",
        },
        {
          dataIndex: "sort",
          title: "排序",
          isForm: true,
          formSpan: 12,
          valueType: "input",
        },
        {
          dataIndex: "mainOid",
          title: "主OID",
          isForm: true,
          formSpan: 12,
          valueType: "input",
        },
        {
          dataIndex: "secondOid",
          title: "副OID",
          isForm: true,
          formSpan: 12,
          valueType: "input",
        },
        {
          dataIndex: "matchData",
          title: "配对数据",
          isForm: true,
          formSpan: 12,
          valueType: "input",
        },
        {
          dataIndex: "description",
          title: "描述",
          isForm: true,
          formSpan: 24,
          wordlimit: 255,
          valueType: "input",
          inputType: "textarea",
        },
      ],
    };
  },

  mounted() {
    this.refresh();
  },
  methods: {
    handleDelete(row) {
      MessageBox.confirm("Oid删除后，数据将不可恢复。确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const result = await deleteOidNewApi(row.id);
          if (result.code === 200) {
            Message.success("删除成功");
            this.localPagination = {
              pageNumber: 1,
              pageSize: 10,
              total: 0,
            };
            this.$nextTick(() => {
              this.refresh();
            });
          }
        })
        .catch(() => {
          console.log("取消删除");
        });
    },
    handleAdd(row, type) {
      if (type === "add") {
        this.addType = "新建";
      } else {
        this.addType = "编辑";
        this.addForm = cloneDeep(row);
      }
      this.addDialog = true;
    },
    async loadData(params) {
      try {
        this.queryParam = {
          ...this.queryParam,
          ...params,
        };
        const res = [
          {
            createdAtStartTime: null,
            createdAtEndTime: null,
            data: params.updatedAt,
          },
        ];
        filterParamRange(this, this.queryParam, res);
        const requestParameters = cloneDeep(this.queryParam);
        delete requestParameters.updatedAt;
        const result = await getOidConfigNewApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    handleSelectionChange(val) {
      this.selection = val;
    },
    // 刷新
    refresh() {
      this.$refs.ProTable.refresh();
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },
    // 新建保存
    handleAddDialogOk() {
      this.$refs.addForm.handleSubmit();
    },
    handleAddDialogCancel() {
      this.addDialog = false;
      this.$nextTick(() => {
        this.addForm = {};
      });
    },
    async addFormSubmit(val) {
      if (this.addType == "新建") {
        try {
          this.addFormLoading = true;
          const result = await addOidConfigNewApi(val);
          if (result.code === 200) {
            Message.success("添加成功");
            this.addDialog = false;
            this.$refs.ProTable.refresh();
          }
        } catch (err) {
          Message.error(err.message);
        } finally {
          this.addFormLoading = false;
        }
      } else {
        try {
          this.addFormLoading = true;
          const result = await editOidConfigNewApi(val);
          if (result.code === 200) {
            Message.success("编辑成功");
            this.addDialog = false;
            this.$refs.ProTable.refresh();
          }
        } catch (err) {
          Message.error(err.message);
        } finally {
          this.addFormLoading = false;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.custom {
  width: 100%;
}
</style>
