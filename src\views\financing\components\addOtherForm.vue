<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-17 16:14:50
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-25 10:25:20
 * @Description: 
 -->
<template>
  <div class="app-container">
    <el-form
      ref="editForm"
      :model="editForm"
      :rules="addRules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <!--<el-col :span="6">-->
        <!--  <el-form-item-->
        <!--    :label="type === 'revenue' ? '收入类型：' : '支出类型：'"-->
        <!--    prop="type"-->
        <!--  >-->
        <!--    &lt;!&ndash;<el-input&ndash;&gt;-->
        <!--    &lt;!&ndash;  v-model="editForm.type"&ndash;&gt;-->
        <!--    &lt;!&ndash;  placeholder="请输入收入类型"&ndash;&gt;-->
        <!--    &lt;!&ndash;  clearable&ndash;&gt;-->
        <!--    &lt;!&ndash;/>&ndash;&gt;-->
        <!--    <el-select-->
        <!--      v-model="editForm.type"-->
        <!--      placeholder="请选择类型"-->
        <!--      style="width: 100%"-->
        <!--      clearable-->
        <!--    >-->
        <!--      <el-option-->
        <!--        v-for="item in typeList"-->
        <!--        :key="item.value"-->
        <!--        :label="item.label"-->
        <!--        :value="item.value"-->
        <!--      />-->
        <!--    </el-select>-->
        <!--  </el-form-item>-->
        <!--</el-col>-->

        <el-col :span="6">
          <el-form-item label="开单时间：" prop="placeOrder">
            <el-date-picker
              v-model="editForm.placeOrder"
              style="width: 100%"
              type="datetime"
              placeholder="请选择开单时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="18" style="display: flex">
          <el-col :span="8" style="padding-left: 0; padding-right: 15px">
            <el-form-item label="客户名称：" prop="customerName">
              <el-input
                v-model="editForm.customerName"
                disabled
                placeholder="请输入客户名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-button
              type="success"
              size="small"
              @click="selectUserFn('customer')"
            >
              选择客户
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="selectUserFn('provider')"
            >
              选择供应商
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="selectUserFn('staff')"
            >
              选择员工
            </el-button>
          </el-col>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品名：" prop="articleName">
            <el-input v-model="editForm.articleName" placeholder="请输入品名" />
          </el-form-item>
          <!--<el-col :span="12" style="padding-right: 15px">-->
          <!--  <el-button-->
          <!--    style="display: flex; gap: 10px; margin-bottom: 10px"-->
          <!--    type="success"-->
          <!--    size="small"-->
          <!--    @click="selectUserFn('item')"-->
          <!--  >-->
          <!--    选择物品-->
          <!--  </el-button>-->
          <!--</el-col>-->
        </el-col>
        <el-col :span="18">
          <el-form-item label="摘要说明：" prop="remark">
            <el-input
              v-model="editForm.remark"
              placeholder="请输入摘要说明"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="数量：" prop="num">
            <el-input
              v-model="editForm.num"
              placeholder="请输入物品数量"
              type="number"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="单价：" prop="price">
            <el-input
              v-model="editForm.price"
              placeholder="请输入单价"
              type="number"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="总金额：">
            <el-input
              v-model="editForm.totalAmount"
              disabled
              placeholder="总金额根据数量和单价计算"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item
            :label="type === 'revenue' ? '实收金额' : '支出金额'"
            prop="actualAmount"
          >
            <el-input
              v-model="editForm.actualAmount"
              placeholder="请输入实收金额"
              type="number"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 选择客户、供应商、员工、物品弹窗 -->
    <ProDialog
      :value="selectDialogVisible"
      :title="selectDialogTitle"
      width="70%"
      top="2%"
      :no-footer="true"
      @cancel="selectDialogVisible = false"
    >
      <ProTable
        ref="ProTable"
        :query-param="queryParam"
        :local-pagination="localPagination"
        :columns="columns"
        :data="tableData"
        height="400"
        @loadData="loadData"
      >
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button icon="el-icon-success" @click="confirmSelect(row)">
              确认选择
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam, mulAmount } from "@/utils";
import { cloneDeep } from "lodash";
import { getCustomerByPageApi } from "@/api/customer";
import { manufacturerListApi } from "@/api/manufacturer";
import { storageInventoryPage } from "@/api/store";
import { dictTreeByCodeApi, userListApi } from "@/api/user";

export default {
  name: "AddOtherForm",
  props: {
    value: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      addRules: {
        // type: [
        //   {
        //     required: true,
        //     message: "请输入收入类型",
        //     trigger: "change",
        //   },
        // ],
        placeOrder: [
          {
            required: true,
            message: "请选择开单时间",
            trigger: "change",
          },
        ],
        remark: [
          {
            required: true,
            message: "请输入摘要说明",
            trigger: "change",
          },
        ],
        // customerName: [
        //   {
        //     required: true,
        //     message: "请选择客户名称",
        //     trigger: "change",
        //   },
        // ],
        // articleName: [
        //   {
        //     required: true,
        //     message: "请输入品名",
        //     trigger: "change",
        //   },
        // ],
        num: [
          {
            required: true,
            message: "请输入物品数量",
            trigger: "change",
          },
        ],
        price: [
          {
            required: true,
            message: "请输入单价",
            trigger: "change",
          },
        ],
        actualAmount: [
          {
            required: true,
            message: "请输入实收金额",
            trigger: "change",
          },
        ],
      },
      selectDialogVisible: false,
      selectDialogTitle: "供应商",
      selectType: "customer", // 选择类型： customer, provider, staff, item
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [],
      tableData: [],
      typeList: [],
    };
  },

  computed: {
    editForm: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    "editForm.num": "calcAmount",
    "editForm.price": "calcAmount",
  },
  mounted() {
    dictTreeByCodeApi(7100).then((res) => {
      this.typeList = res.data;
    });
  },
  methods: {
    // 选择登记用户: customer, provider, staff, item
    selectUserFn(type) {
      this.selectType = type;
      if (type !== "item") {
        this.editForm.customerType =
          type === "customer"
            ? 1
            : type === "provider"
            ? 2
            : type === "staff"
            ? 3
            : null;
      }
      this.selectDialogTitle = `选择${
        type === "customer"
          ? "客户"
          : type === "provider"
          ? "供应商"
          : type === "staff"
          ? "员工"
          : type === "item"
          ? "物品"
          : ""
      }`;
      if (type === "customer") {
        this.columns = [
          {
            dataIndex: "seqId",
            title: "客户编号",
            isTable: true,
            isSearch: true,
            isExport: true,
            valueType: "input",
            clearable: true,
            minWidth: 150,
          },
          {
            dataIndex: "name",
            title: "客户名称",
            isTable: true,
            isSearch: true,
            valueType: "input",
            minWidth: 180,
          },
          {
            dataIndex: "province",
            title: "省份",
            isTable: true,
          },
          {
            dataIndex: "city",
            title: "市区",
            isTable: true,
          },
          {
            dataIndex: "area",
            title: "区县",
            isTable: true,
          },
          {
            dataIndex: "phone",
            title: "手机号",
            isTable: false,
            isSearch: true,
            valueType: "input",
            inputType: "number",
          },
          {
            dataIndex: "createdAt",
            title: "入驻时间",
            isTable: true,
            width: 150,
          },
          {
            dataIndex: "action",
            title: "操作",
            isTable: true,
            tooltip: false,
            tableSlot: "action",
            width: 110,
          },
        ];
      } else if (type === "provider") {
        this.columns = [
          {
            dataIndex: "code",
            title: "制造商编号",
            isTable: true,
            isSearch: true,
            clearable: true,
            valueType: "input",
            minWidth: 100,
          },
          {
            dataIndex: "name",
            title: "制造商简称",
            isTable: true,
            isSearch: true,
            clearable: true,
            valueType: "input",
            minWidth: 150,
          },
          {
            dataIndex: "legalPerson",
            title: "联系人",
            isTable: true,
            isSearch: true,
            valueType: "input",
            minWidth: 120,
          },
          {
            dataIndex: "legalPersonTel",
            title: "电话",
            isTable: true,
            isSearch: true,
            valueType: "input",
            minWidth: 120,
          },
          {
            dataIndex: "action",
            title: "操作",
            isTable: true,
            tooltip: false,
            tableSlot: "action",
            width: 110,
          },
        ];
      } else if (type === "staff") {
        this.columns = [
          {
            dataIndex: "name",
            title: "关键字",
            isSearch: true,
            valueType: "input",
            placeholder: "用户名/用户账号/手机号",
          },
          {
            dataIndex: "code",
            title: "用户账号",
            isTable: true,
          },
          {
            dataIndex: "name",
            title: "用户名",
            isTable: true,
          },

          {
            dataIndex: "mobileNumber",
            title: "手机号",
            isTable: true,
          },
          {
            dataIndex: "sex",
            title: "性别",
            isTable: true,
            width: 200,
            formatter: (row) => row.sex?.label,
          },

          {
            dataIndex: "action",
            title: "操作",
            isTable: true,
            tooltip: false,
            tableSlot: "action",
            width: 110,
          },
        ];
      } else if (type === "item") {
        this.columns = [
          {
            dataIndex: "productIds",
            title: "适用机型",
            isSearch: true,
            valueType: "product",
          },
          {
            dataIndex: "code",
            title: "物品编号",
            isTable: true,
            isSearch: true,
            valueType: "input",
            minWidth: 160,
          },
          {
            dataIndex: "name",
            title: "物品名称",
            isTable: true,
            isSearch: true,
            valueType: "input",
            minWidth: 150,
          },
          {
            dataIndex: "partBrand",
            title: "品牌",
            isTable: true,
            isSearch: true,
            valueType: "input",
            minWidth: 80,
          },
          {
            dataIndex: "numberOem",
            title: "OEM编号",
            isTable: true,
            isSearch: true,
            valueType: "input",
            minWidth: 130,
          },
          {
            dataIndex: "type",
            title: "物品类型",
            isTable: true,
            formatter: (row) => row.type?.label,
            isSearch: true,
            valueType: "select",
            optionMth: () => dictTreeByCodeApi(2100),
            option: [],
            optionskey: {
              label: "label",
              value: "value",
            },
            minWidth: 80,
          },
          // {
          //   dataIndex: "manufacturerGoodsCode",
          //   title: "原系统物品编号",
          //   isTable: true,
          //   isSearch: true,
          //   valueType: "input",
          //   minWidth: 120,
          // },
          // {
          //   dataIndex: "manufacturerGoodsName",
          //   title: "原系统物品名称",
          //   isTable: true,
          //   isSearch: true,
          //   valueType: "input",
          //   minWidth: 150,
          // },
          {
            dataIndex: "manufacturerChannel",
            title: "制造商渠道",
            isTable: true,
            formatter: (row) => row.manufacturerChannel?.label,
            isSearch: true,
            valueType: "select",
            option: [],
            optionMth: () => dictTreeByCodeApi(2200),
            optionskey: {
              label: "label",
              value: "value",
            },
            width: 90,
          },
          {
            dataIndex: "costPrice",
            title: "成本价",
            isTable: true,
            width: 80,
          },
          {
            dataIndex: "price",
            title: "销售价",
            isTable: true,
            width: 80,
          },
          {
            dataIndex: "location",
            title: "储位",
            isTable: true,
            valueType: "input",
            width: 100,
          },
          {
            dataIndex: "action",
            title: "操作",
            isTable: true,
            tooltip: false,
            tableSlot: "action",
            width: 110,
          },
        ];
      }
      this.queryParam = {};
      this.localPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.tableData = [];
      this.selectDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.ProTable.refresh();
      });
    },
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      const type = this.selectType;
      const editApi =
        type === "customer"
          ? getCustomerByPageApi
          : type === "provider"
          ? manufacturerListApi
          : type === "staff"
          ? userListApi
          : type === "item"
          ? storageInventoryPage
          : "";
      if (!editApi) {
        this.$message.error("获取数据失败，请重新选择");
        return;
      }
      editApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    confirmSelect(row) {
      if (this.selectType === "customer") {
        this.$set(this.editForm, "customerId", row.id);
        this.$set(this.editForm, "customerName", row.name);
        this.$set(this.editForm, "customerSeq", row.seqId);
      } else if (this.selectType === "provider") {
        this.$set(this.editForm, "customerId", row.id);
        this.$set(this.editForm, "customerName", row.name);
        this.$set(this.editForm, "customerSeq", row.code);
      } else if (this.selectType === "staff") {
        this.$set(this.editForm, "customerId", row.id);
        this.$set(this.editForm, "customerName", row.name);
        this.$set(this.editForm, "customerSeq", row.code);
      } else if (this.selectType === "item") {
        this.$set(this.editForm, "articleId", row.id);
        this.$set(this.editForm, "articleName", row.name);
        this.$set(this.editForm, "price", row.price);
      }
      this.selectDialogVisible = false;
    },
    calcAmount() {
      const num = this.editForm.num;
      const price = this.editForm.price;
      if (
        num !== undefined &&
        price !== undefined &&
        !isNaN(num) &&
        !isNaN(price) &&
        num !== "" &&
        price !== ""
      ) {
        this.editForm.totalAmount = mulAmount(
          this.editForm.num,
          this.editForm.price
        ).toFixed(2);
      } else {
        this.editForm.totalAmount = null;
      }
    },
  },
};
</script>

<style scoped lang="scss"></style>
