<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-09-13 17:36:54
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-09-14 14:45:48
 * @FilePath: \benyin-web\src\views\order\components\salesReturn.vue
 * @Description: 
 * 
-->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :local-pagination="localPagination"
      :data="tableData"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <!-- <div v-if="type === 'detail'" class="title-box-right"></div> -->
        <div v-if="type === 'item'" class="title-box-right">
          <div>购买数量：{{ totalData?.itemNum || 0 }}</div>
          <div>购买金额：{{ totalData?.actualPayAmount || 0 }}</div>
          <div>退货数量：{{ totalData?.reverseItemNum || 0 }}</div>
          <div>退货金额：{{ totalData?.refundAmount || 0 }}</div>
        </div>
        <div v-if="type === 'provider'" class="title-box-right">
          <div>购买数量：{{ totalData?.itemNum || 0 }}</div>
          <div>购买金额：{{ totalData?.actualPayAmount || 0 }}</div>
          <div>退货数量：{{ totalData?.reverseItemNum || 0 }}</div>
          <div>退货金额：{{ totalData?.refundAmount || 0 }}</div>
        </div>
        <div v-if="type === 'customer'" class="title-box-right">
          <div>购买数量：{{ totalData?.itemNum || 0 }}</div>
          <div>购买金额：{{ totalData?.actualPayAmount || 0 }}</div>
          <div>退货数量：{{ totalData?.reverseItemNum || 0 }}</div>
          <div>退货金额：{{ totalData?.refundAmount || 0 }}</div>
        </div>
        <!--<div v-if="type === 'series'" class="title-box-right"></div>-->
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getCustomerReturnStatApi,
  getCustomerReturnSummaryStatApi,
  getItemReturnStatApi,
  getReturnListApi,
  getReturnSummaryStatApi,
  getSeriesReturnStatApi,
  getSupplierReturnStatApi,
  getSupplierReturnSummaryStatApi,
} from "@/api/order";
import { productAllApi } from "@/api/dispose";

export default {
  name: "SalesReturn",
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "detail",
    },
  },

  data() {
    return {
      productIdName: "",
      options: [],
      queryParam: {},
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
    this.getProductThird();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.currMonth,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      ["currMonth"].forEach((key) => delete requestParameters[key]);
      const editApi = this.getMethodApi(this.type);
      if (!editApi) {
        this.$refs.ProTable.listLoading = false;
        return;
      }
      editApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      const totalApi = this.getTotalDataApi(this.type);
      if (!totalApi) {
        this.totalData = {};
        return;
      }
      totalApi(requestParameters).then((res) => {
        this.totalData = res.data;
      });
    },
    getMethodApi(type) {
      switch (type) {
        case "detail":
          return getReturnListApi;
        case "item":
          return getItemReturnStatApi;
        case "provider":
          return getSupplierReturnStatApi;
        case "customer":
          return getCustomerReturnStatApi;
        case "series":
          return getSeriesReturnStatApi;
        default:
          return "";
      }
    },
    getTotalDataApi(type) {
      switch (type) {
        case "detail":
          return "";
        case "item":
          return getReturnSummaryStatApi;
        case "provider":
          return getSupplierReturnSummaryStatApi;
        case "customer":
          return getCustomerReturnSummaryStatApi;
        case "series":
          return "";
        default:
          return "";
      }
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        this.queryParam.productIds.push(el[el.length - 1]);
      });
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
