<template>
  <div class="container">
    <!-- 重新计算 -->
    <ProDialog
      :value="reloadDrawer"
      title="重新统计印量"
      width="60%"
      :confirm-loading="false"
      :no-footer="true"
      @cancel="reloadDrawer = false"
    >
      <div class="tit-boxs">
        <el-button type="success" @click="showDialogFn">选择客户信息</el-button>
      </div>
      <el-form
        ref="proform_child1"
        label-width="140px"
        class="demo-ruleForm"
        :rules="
          reloadForm.colorAdjust || reloadForm.blackWhiteAdjust
            ? reloadFormRules
            : reloadFormRulesReset
        "
        :model="reloadForm"
      >
        <el-row>
          <el-col :span="24" style="display: flex; flex-wrap: wrap">
            <el-form-item label="客户编码:">
              <el-input
                v-model="reloadForm.seqId"
                placeholder="请输入客户编码"
                style="width: 300px"
                disabled
              ></el-input>
            </el-form-item>
            <el-form-item label="设备组:" prop="deviceGroupId">
              <el-select
                v-model="reloadForm.deviceGroupId"
                style="width: 300px"
                placeholder="请选择设备组"
              >
                <el-option
                  v-for="item in deviceGroupList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="年月:" prop="cycle">
              <el-date-picker
                v-model="reloadForm.cycle"
                style="width: 300px"
                type="month"
                placeholder="选择年月"
                format="yyyy-MM"
                value-format="yyyy-MM"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="footer-btn">
        <el-button
          type="primary"
          :disabled="!reloadForm.seqId"
          @click="calculate"
        >
          确认重新统计
        </el-button>
        <el-button @click="reloadDrawer = false"> 取消 </el-button>
      </div>
    </ProDialog>
    <!--  选择客户信息  -->
    <ProDialog
      :value="showDialog"
      title="客户信息"
      width="60%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <ProTable
        ref="ProTables"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="creatColumns"
        show-pagination
        row-key="id"
        :local-pagination="localPaginations"
        :data="customTableData"
        sticky
        :query-param="queryParams"
        :height="430"
        :show-setting="false"
        @loadData="loadData1"
      >
        <template #actions="slotProps">
          <span class="fixed-width">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-edit-outline"
              @click="sureSelectCustom(slotProps.row)"
            >
              确定
            </el-button>
          </span>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import {
  getCustomerByPageApi,
  getCustomerDeviceGroupByPageApi,
} from "@/api/customer";
import { rePrintListApi } from "@/api/statisics";

export default {
  name: "ReloadMeter",
  data() {
    return {
      reloadDrawer: false,
      reloadForm: {},
      showDialog: false,
      deviceGroupList: [],
      creatColumns: [
        {
          dataIndex: "name",
          title: "店铺名称",
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "店铺名称",
          isTable: true,
        },
        {
          dataIndex: "seqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
        },

        // {
        //   dataIndex: "shopRecruitment",
        //   title: "店铺名称",
        //   isTable: true,
        // },
        {
          dataIndex: "phone",
          title: "电话号码",
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      localPaginations: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      customTableData: [],
      queryParams: {},
      deviceGroupPagination: {
        pageNumber: 1,
        pageSize: 9999,
      },
      reloadFormRules: {
        cycle: [{ required: true, message: "请选择校准日期", trigger: "blur" }],
        deviceGroupId: [
          { required: true, message: "请选择需校准设备组", trigger: "blur" },
        ],
      },
      reloadFormRulesReset: {
        cycle: [
          { required: false, message: "请选择校准日期", trigger: "blur" },
        ],
        deviceGroupId: [
          { required: false, message: "请选择需校准设备组", trigger: "blur" },
        ],
      },
    };
  },
  mounted() {},
  methods: {
    show() {
      this.reloadDrawer = true;
    },
    showDialogFn() {
      this.queryParams = {};
      this.localPaginations.pageNumber = 1;
      this.showDialog = true;
      this.$nextTick((e) => {
        this.$refs.ProTables.refresh();
      });
    },
    loadData1(parameter) {
      const requestParameters = Object.assign(this.queryParams, parameter);
      getCustomerByPageApi(requestParameters)
        .then((res) => {
          this.customTableData = res.data.rows;
          this.localPaginations = {
            pageNumber: parameter.pageNumber,
            pageSize: parameter.pageSize,
            total: +res.data.total,
          };
        })
        .finally(() => {
          this.$refs.ProTables
            ? (this.$refs.ProTables.listLoading = false)
            : null;
        });
    },
    // 确认用户
    async sureSelectCustom(row) {
      row.consigneeName = row.name;
      row.consigneePhone = row.tel;
      this.reloadForm = {};

      this.$nextTick((e) => {
        this.reloadForm = row;
      });
      const result = await getCustomerDeviceGroupByPageApi({
        ...this.deviceGroupPagination,
        customerId: row.id,
      });
      if (result.code === 200 && result.data) {
        const data = result.data.rows;
        this.deviceGroupList = data.map((item) => {
          return {
            value: item.id,
            label: `${item.deviceGroup.label} - ${item.productInfo}`,
          };
        });
      }
      this.showDialog = false;
    },
    calculate() {
      console.log(this.reloadForm);
      const { seqId, cycle, deviceGroupId, blackWhiteAdjust, colorAdjust } =
        this.reloadForm;

      this.$refs["proform_child1"].validate(async (valid) => {
        if (valid) {
          const params = {
            seqId,
            cycle,
            deviceGroupId,
            blackWhiteAdjust,
            colorAdjust,
          };
          await rePrintListApi(params)
            .then((res) => {
              this.reloadDrawer = false;
              if (res.code === 200) {
                this.$message.success(res.message);
                this.reloadForm = {};
              }
            })
            .catch((err) => {
              this.$message.error(err || err.message || "计算失败");
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.footer-btn {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}
.tit-boxs {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px 7px;
  font-size: 16px;
  font-weight: 800;
}
</style>
