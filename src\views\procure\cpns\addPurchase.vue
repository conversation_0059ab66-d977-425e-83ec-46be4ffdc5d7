<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:58:44
 * @Description: 
 -->
<template>
  <ProDrawer
    :value="showDrawer"
    :title="drawerTitle + '物品采购单'"
    size="85%"
    :destroy-on-close="true"
    :no-footer="editType !== 'add'"
    :confirm-button-disabled="formLoading"
    confirm-text="确定新增"
    @ok="handleDrawerOk"
    @cancel="handleCloseDrawer"
  >
    <div class="add-purchase-container">
      <ProForm
        ref="ProForm"
        :form-param="addForm"
        :form-list="columns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :open-type="editType"
        @proSubmit="formSubmit"
      >
        <template #purchaseCode>
          {{ purchaseCode }}
        </template>
        <template #purchaseGoods>
          <ProTable
            ref="purchaseGoodsTable"
            :row-key="(row) => row.articleCode"
            :data="purchaseGoodsData"
            :columns="chooseColumns"
            height="64vh"
            show-index
            :show-search="false"
            :show-loading="false"
          >
            <template v-if="editType === 'add' || editType === 'audit'" #btn>
              <el-button
                type="success"
                class="add-btn"
                size="mini"
                icon="el-icon-plus"
                @click="handleChooseSupply()"
              >
                选择供应源
              </el-button>
            </template>
            <template #number="{ row }">
              <el-input-number
                v-model="row.planNum"
                :disabled="editType === 'info'"
                style="width: 100px"
                size="small"
                :min="0"
                :controls="false"
                placeholder="采购数量"
                @change="handleNumChange"
              ></el-input-number>
            </template>
            <template #approveNum="{ row }">
              <el-input-number
                v-model="row.approveNum"
                :disabled="editType === 'info' && !checkBtn"
                :min="0"
                size="small"
                controls-position="right"
                style="width: 100px"
                placeholder="确认采购数量"
                @change="handleApproveNumChange"
              ></el-input-number>
            </template>
            <template #price="{ row }">
              <div class="fixed-width">
                <span>{{ row.price }}</span>
                <el-button
                  v-if="
                    (editType === 'info' || editType === 'audit') &&
                    row.articleType === 1
                  "
                  type="primary"
                  style="margin-left: 10px"
                  @click="editArticleType(row)"
                >
                  价格明细
                </el-button>
              </div>
            </template>
            <template #machine="slotProps">
              <el-popover
                placement="bottom"
                title=""
                width="700"
                trigger="click"
              >
                <div style="margin: 20px; height: 400px; overflow-y: scroll">
                  <el-descriptions
                    class="margin-top"
                    title="适用机型"
                    :column="1"
                    border
                  >
                    <el-descriptions-item
                      v-for="item in slotProps.row.machine"
                      :key="item.id"
                    >
                      <template slot="label">品牌/系列/机型</template>
                      {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>

                <el-button slot="reference" type="text" size="mini">
                  适用机型
                </el-button>
              </el-popover>
            </template>
            <template #action="slotProps">
              <div
                v-if="editType === 'add' || editType === 'audit'"
                class="fixed-width"
              >
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDelete(slotProps.row, slotProps.index)"
                >
                  移除
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>

        <template #deliveryTime>
          <el-date-picker
            v-model="addForm.deliveryTime"
            type="date"
            :disabled="editType === 'info' && !checkBtn"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择期望发货时间"
          ></el-date-picker>
        </template>

        <template #arrivalTime>
          <el-date-picker
            v-model="addForm.arrivalTime"
            type="date"
            :disabled="editType === 'info' && !checkBtn"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择期望到货时间"
          ></el-date-picker>
        </template>
      </ProForm>
      <div v-if="checkBtn" class="dialog-footer1">
        <div class="btn-box">
          <el-button
            type="primary"
            :disabled="reCheckLoading"
            @click="handelCheck()"
          >
            复核完成
          </el-button>
          <el-button @click="handleCloseDrawer">取消</el-button>
        </div>
      </div>
    </div>

    <!-- 选择供应源弹窗 -->
    <ProDialog
      :value="showChooseDialog"
      title="选择供应源"
      width="90%"
      :confirm-loading="false"
      confirm-text="确认选择"
      top="10px"
      :no-footer="false"
      @ok="handleDialogConfirm"
      @cancel="showChooseDialog = false"
    >
      <!-- :row-key="(row) => row.storageArticle.code" -->
      <ProTable
        ref="supplyTable"
        :row-key="(row) => row.id"
        :data="supplyData"
        :columns="purchaseGoodsColumns"
        :height="400"
        show-index
        :query-param="purchaseGoodsQueryParam"
        :show-pagination="true"
        :local-pagination="supplyLocalPagination"
        :show-search="true"
        :show-loading="true"
        :show-selection="true"
        :reserve-selection="true"
        @handleSelectionChange="handleSelectionSupply"
        @loadData="loadSupplyData"
      >
        <!--@handleSelected="handleSelected"-->
        <template #fullIdPath>
          <el-cascader
            ref="ProductIds"
            v-model="productIdName"
            filterable
            clearable
            :options="options"
            collapse-tags
            style="width: 100%"
            :props="{
              label: 'name',
              value: 'fullIdPath',
              children: 'children',
              expandTrigger: 'click',
              multiple: true,
            }"
            leaf-only
            @change="handleChange"
          ></el-cascader>
        </template>
        <template #machine="slotProps">
          <el-popover placement="bottom" title="" width="700" trigger="click">
            <div style="margin: 20px; height: 400px; overflow-y: scroll">
              <el-descriptions
                class="margin-top"
                title="适用机型"
                :column="1"
                border
              >
                <el-descriptions-item
                  v-for="item in slotProps.row.productTreeDtoList"
                  :key="item.id"
                >
                  <template slot="label">品牌/系列/机型</template>
                  {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <el-button slot="reference" type="text" size="mini">
              适用机型
            </el-button>
          </el-popover>
        </template>
      </ProTable>
    </ProDialog>

    <!-- 编辑价格明细弹窗 -->
    <ProDialog
      :value="articleTypeDialog"
      title="物品价格明细"
      width="55%"
      :confirm-text="'确认'"
      :confirm-loading="false"
      @ok="handleArticleTypeConfirm"
      @cancel="articleTypeDialog = false"
    >
      <ProTable
        ref="articleTypeTable"
        row-key="id"
        :query-param="articleTypeQueryParam"
        :columns="articleTypeColumns"
        :data="articleTypeData"
        :local-pagination="articleTypeLocalPagination"
        :height="400"
        :show-loading="false"
        show-pagination
        sticky
      >
        <template #actions="{ row }">
          <el-button
            type="primary"
            size="mini"
            :disabled="editType === 'info'"
            icon="el-icon-edit-outline"
            @click="handleUpdatePrice(row)"
          >
            修改
          </el-button>
        </template>
      </ProTable>
    </ProDialog>
  </ProDrawer>
</template>

<script>
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi2, dictTreeByCodeApi } from "@/api/user";
import { warehouseListApi, manufacturerListApi } from "@/api/store";
import { productAllApi } from "@/api/dispose";
import {
  pageSupplierApi,
  getStorageApi,
  addPurchaseApi,
  getPurchaseApi,
  updatePurchaseApi,
} from "@/api/procure";
import { Message } from "element-ui";
import { filterParam, mulAmount } from "@/utils";
export default {
  name: "AddPurchase",
  props: {
    purchaseCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showDrawer: false,
      drawerTitle: "新增",
      addForm: {},
      goodsTypeOptions: [],
      columns: [
        {
          dataIndex: "warehouseId",
          title: "选择仓库",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择仓库",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "purchaseCode",
          title: "采购单编号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "purchaseGoods",
          title: "选择供应源",
          isForm: true,
          formOtherSlot: "purchaseGoods",
        },
        {
          dataIndex: "deliveryTime",
          title: "期望发货时间",
          isForm: true,
          formSlot: "deliveryTime",
          // valueType: "date-picker",
          // valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请输入期望发货时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "arrivalTime",
          title: "期望到货时间",
          isForm: true,
          formSlot: "arrivalTime",
          // valueType: "date-picker",
          // valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请输入期望发货时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "price",
          title: "总价",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
      ],
      formLoading: false,
      editType: "add",
      purchaseGoodsQueryParam: {},
      purchaseGoodsColumns: [
        {
          dataIndex: "fullIdPath",
          isSearch: true,
          clearable: true,
          searchSlot: "fullIdPath",
          title: "适用机型",
          valueType: "select",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          width: 180,
          isSearch: true,
          valueType: "input",
          formatter: (row) => row.storageArticle.code,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.storageArticle.name,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          width: 180,
          isSearch: true,
          valueType: "input",
          formatter: (row) => row.storageArticle.numberOem,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          clearable: true,
          isTable: true,
          isSearch: true,
          width: 120,
          valueType: "select",
          formatter: (row) => row.storageArticle?.manufacturerChannel?.label,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "manufacturerName",
          title: "制造商名称",
          isTable: true,
          width: 180,
          formatter: (row) => row.manufacturer.name,
        },

        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          optionskey: {
            label: "label",
            value: "value",
          },
          formatter: (row) => row.storageArticle.type?.label,
        },
        {
          dataIndex: "partBomUnit",
          title: "所属单元",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
          formatter: (row) => row.storageArticle.partBomUnit,
          optionMth: () => dictTreeByCodeApi(3200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "productTreeId",
        //   isSearch: true,
        //   clearable: true,
        //   title: "适用机型",
        //   valueType: "input",
        // },
        {
          dataIndex: "machine",
          title: "适用机型",
          isTable: true,
          width: 120,
          tableSlot: "machine",
        },
        {
          dataIndex: "manufacturer",
          title: "供应商名称",
          isTable: true,
          formatter: (row) => row.manufacturer.name,
        },
        {
          dataIndex: "manufacturerId",
          title: "供应商名称",
          width: 150,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => manufacturerListApi(),
          optionskey: {
            label: "name",
            value: "id",
          },
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
      ],
      chooseColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          minWidth: 160,
        },
        {
          dataIndex: "articleName",
          isSearch: true,
          clearable: true,
          title: "物品名称",
          valueType: "input",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          minWidth: 150,
          isTable: true,
        },
        {
          dataIndex: "machine",
          isTable: true,
          title: "适用机型",
          valueType: "input",
          width: 120,
          tableSlot: "machine",
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          width: 220,
          isTable: true,
          sortable: true,
        },
        {
          dataIndex: "warehouseNumber",
          title: "库存量",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          tableSlot: "price",
          width: 100,
        },
        {
          dataIndex: "planNum",
          title: "计划采购数量",
          isTable: true,
          width: 120,
          tableSlot: "number",
          // formatter: (row) => row.number,
        },
        {
          dataIndex: "approveNum",
          title: "确认采购数量",
          isTable: true,
          width: 120,
          tableSlot: "approveNum",
          // formatter: (row) => row.number,
        },
        {
          dataIndex: "purchasePrice",
          title: "采购金额",
          isTable: true,
          // formatter: (row) => mulAmount(row.price, row.number),
        },
        {
          dataIndex: "action",
          title: "操作",
          width: 100,
          isTable: true,
          tooltip: false,
          tableSlot: "action",
        },
      ],
      purchaseGoodsData: [],
      showChooseDialog: false,
      supplyData: [],
      supplyLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      supplySelectionData: [],
      checkBtn: false,
      productIdName: "",
      options: [],
      articleTypeDialog: false,
      articleTypeData: [],
      articleTypeColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "采购价格",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 120,
          tableSlot: "actions",
        },
      ],
      articleTypeQueryParam: {},
      articleTypeLocalPagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      reCheckLoading: false,
    };
  },
  mounted() {
    productAllApi().then((res) => {
      this.options = res.data;
    });
    dictTreeByCodeApi(2100).then((res) => {
      this.goodsTypeOptions = res.data;
    });
  },
  methods: {
    handleDrawerOk() {
      this.$refs.ProForm.handleSubmit();
    },
    handleCloseDrawer() {
      this.showDrawer = false;
    },
    async show(row, type) {
      switch (type) {
        case "add":
          this.drawerTitle = "新增";
          break;
        case "edit":
          this.drawerTitle = "修改";
          break;
        case "check":
          this.drawerTitle = "复核";
          break;
        case "info":
          this.drawerTitle = "查看";
          break;
        default:
          break;
      }
      if (type === "check") {
        this.editType = "audit";
        this.checkBtn = true;
      } else {
        this.editType = type;
        this.checkBtn = false;
      }
      this.supplySelectionData = [];
      if (type !== "add") {
        this.chooseColumns = this.$options.data().chooseColumns;
        if (this.editType !== "audit") {
          this.$options.data().chooseColumns.length ===
            this.chooseColumns.length &&
            (this.chooseColumns = this.chooseColumns.splice(
              0,
              this.chooseColumns.length - 2
            ));
        }
        await this.getDetails(row.id);
      } else {
        this.addForm = {};
        this.purchaseGoodsData = [];
        this.chooseColumns = this.$options.data().chooseColumns;
        this.chooseColumns.splice(this.chooseColumns.length - 3, 1);
      }

      this.purchaseGoodsQueryParam = {};
      this.showDrawer = true;
    },
    async formSubmit(val) {
      try {
        const userInfo = JSON.parse(localStorage.getItem("userInfo"));
        this.purchaseGoodsData.forEach((item) => {
          if (!item.planNum) {
            throw new Error("请输入采购数量");
          }
        });
        this.$confirm("是否确认新增物品采购单?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.formLoading = true;
            const args = {
              ...this.addForm,
              purchaseGoods: this.purchaseGoodsData,
              initiatorId: userInfo.id,
              initiatorName: userInfo.name,
              purchaseType: "PROCESS",
            };
            const result = await addPurchaseApi(args);
            if (result.code === 200) {
              Message.success("添加成功");
              this.handleCloseDrawer();
              this.$emit("refresh");
            }
          })
          .finally(() => {
            this.formLoading = false;
          });
      } catch (error) {
        Message.error(error);
      }
    },
    handleChooseSupply() {
      if (!this.addForm.warehouseId) {
        Message.warning("请选择仓库");
        return;
      }
      this.showChooseDialog = true;
      this.purchaseGoodsQueryParam = {};
      this.supplyLocalPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.$nextTick(() => {
        this.supplyRefresh();
      });
    },
    loadSupplyData(params) {
      this.purchaseGoodsQueryParam = filterParam(
        Object.assign({}, this.purchaseGoodsQueryParam, params)
      );
      const requestParameters = cloneDeep(this.purchaseGoodsQueryParam);
      pageSupplierApi({ ...requestParameters, status: 1 })
        .then((res) => {
          this.supplyData = res.data.rows;
          this.supplyLocalPagination.total = +res.data.total;
          const temp = cloneDeep(this.supplySelectionData);
          if (temp.length > 0) {
            this.$refs.supplyTable.$refs.ProElTable.clearSelection();
            temp.map((row) => {
              this.$refs.supplyTable.$refs.ProElTable.toggleRowSelection(
                row,
                true
              );
            });
          }
        })
        .finally(() => {
          this.$refs.supplyTable &&
            (this.$refs.supplyTable.listLoading = false);
        });
      // try {
      //   const data = {
      //     ...params,
      //     ...this.purchaseGoodsQueryParam,
      //     status: 1,
      //   };
      //   data.goodsType && delete data.goodsType;
      //   const result = await pageSupplierApi(data);
      //   if (result.code === 200) {
      //     this.supplyData = result.data.rows;
      //     this.supplyLocalPagination = {
      //       pageNumber: params.pageNumber,
      //       pageSize: params.pageSize,
      //       total: +result.data.total,
      //     };
      //     const temp = cloneDeep(this.supplySelectionData);
      //     if (temp.length > 0) {
      //       this.$refs.supplyTable.$refs.ProElTable.clearSelection();
      //       temp.map((row) => {
      //         this.$refs.supplyTable.$refs.ProElTable.toggleRowSelection(
      //           row,
      //           true
      //         );
      //       });
      //     }
      //   }
      // } catch (error) {
      //   Message.error(error.message);
      // } finally {
      //   this.$refs.supplyTable && (this.$refs.supplyTable.listLoading = false);
      // }
    },
    supplyRefresh() {
      this.$refs.supplyTable.refresh();
    },
    async handleDialogConfirm() {
      this.showChooseDialog = false;
      if (this.addForm.price === undefined) {
        this.addForm.price = 0;
      }
      let addPrice = 0;
      try {
        const args = this.supplySelectionData.map((item) => {
          addPrice += Number(item.price);
          return {
            warehouseId: this.addForm.warehouseId,
            articleId: item.storageArticle.id,
            articleCode: item.storageArticle.code,
            articleName: item.storageArticle.name,
            oemNumber: item.storageArticle.numberOem,
            machine: item.productTreeDtoList,
            manufacturerId: item.manufacturer.id,
            manufacturerName: item.manufacturer.name,
            price: item.price,
            number: 1,
          };
        });
        const result = await getStorageApi(args);
        if (result.code === 200) {
          this.showChooseDialog = false;
          this.purchaseGoodsData = result.data;
          this.purchaseGoodsData.forEach((item) => {
            item.number = 0;
          });
          this.addForm.price = addPrice.toFixed(2);
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 组合商品价格核算
     * @param row
     */
    editArticleType(row) {
      this.articleTypeData = row.priceDistribution || [];
      this.articleTypeDialog = true;
    },
    /**
     * @description 更新组合物品价格
     * @param row
     */
    handleUpdatePrice(row) {
      this.$prompt("请输入修改的金额", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        showInput: true,
        closeOnClickModal: false,
        inputPlaceholder: "请输入修改金额",
        inputType: "number",
        inputValue: row.price,
        inputErrorMessage: "请输入修改金额",
        inputValidator: (value) => {
          if (!value) {
            return "请输入修改金额";
          }
        },
      })
        .then(({ value }) => {
          this.$nextTick(() => {
            // row.price = value;
            this.$set(row, "price", value);
          });
        })
        .catch(() => {});
    },
    handleArticleTypeConfirm() {
      this.articleTypeDialog = false;
    },
    handleSelectionSupply(val) {
      this.supplyData.forEach((row) => {
        const selected = this.supplySelectionData.find(
          (item) => item.id === row.id
        );
        const target = val.find((item) => item.id === row.id);
        if (!selected && target) {
          this.supplySelectionData.push(cloneDeep(row));
        } else if (selected && !target) {
          const index = this.supplySelectionData.findIndex(
            (item) => item.id === row.id
          );
          this.supplySelectionData.splice(index, 1);
        }
      });
    },
    handleSelected(selection, row) {},
    async getDetails(id) {
      try {
        const result = await getPurchaseApi(id);
        if (result.code === 200) {
          this.addForm = result.data;
          this.purchaseGoodsData = result.data.addParam;
          // this.supplySelectionData = this.purchaseGoodsData.map((item) => {
          //   return {
          //     ...item,
          //     storageArticle: {
          //       code: item.articleCode,
          //       name: item.articleName,
          //       id: item.articleId,
          //     },
          //     manufacturer: {
          //       id: item.manufacturerId,
          //       name: item.manufacturerName,
          //     },
          //   };
          // });
          // this.purchaseGoodsData = result.data.purchaseGoods;
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    async handelCheck() {
      const validatePlanNum = this.purchaseGoodsData.every((item) => {
        return item.planNum !== null && item.planNum >= 0;
      });
      if (!validatePlanNum) {
        Message.error("请确认计划采购数量");
        return;
      }
      const validateApproveNum = this.purchaseGoodsData.every((item) => {
        return item.approveNum !== null && item.approveNum >= 0;
      });
      if (!validateApproveNum) {
        Message.error("请确认采购数量");
        return;
      }
      const flag = await this.$refs.ProForm.$refs.ProForm.validate();
      if (!flag) return;
      this.$confirm(`是否确认通过该耗材采购单`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        this.reCheckLoading = true;
        try {
          const args = {
            id: this.addForm.id,
            purchaseCode: this.addForm.purchaseCode,
            purchaseGoods: this.purchaseGoodsData,
            purchaseType: this.addForm.purchaseType.value,
          };
          const result = await updatePurchaseApi(args);
          if (result.code === 200) {
            Message.success("复核完成");
            this.reCheckLoading = false;
            this.$emit("refresh");
            this.handleCloseDrawer();
          }
        } finally {
          this.reCheckLoading = false;
        }
      });
    },
    handleNumChange(item) {
      const addPrice = this.purchaseGoodsData.reduce((total, row) => {
        const price = Number(row.price) || 0;
        const planNumber = Number(row.planNum) || 0;
        row.purchasePrice = mulAmount(price, planNumber).toFixed(2);
        return total + mulAmount(price, planNumber);
      }, 0);
      this.addForm.price = addPrice.toFixed(2);
      this.$forceUpdate();
    },
    handleApproveNumChange(value) {
      const addPrice = this.purchaseGoodsData.reduce((total, row) => {
        const price = Number(row.price) || 0;
        const approveNum = Number(row.approveNum) || 0;
        row.purchasePrice = mulAmount(price, approveNum).toFixed(2);
        return total + mulAmount(price, approveNum);
      }, 0);
      this.addForm.price = addPrice.toFixed(2);
      this.$forceUpdate();
    },
    handleChange(item) {
      this.purchaseGoodsQueryParam.productTreeId = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.purchaseGoodsQueryParam.productTreeId.push(id);
      });
    },
    handleDelete(row, i) {
      console.log(row, "row");
      this.supplySelectionData.splice(
        this.supplySelectionData.findIndex(
          (item) => item.manufacturerId === row.manufacturerId
        ),
        1
      );
      this.purchaseGoodsData.splice(i, 1);
      this.handleNumChange();
      // this.purchaseGoodsData.splice(
      //   this.purchaseGoodsData.findIndex((item) => item.id === row.id),
      //   1
      // );
    },
  },
};
</script>

<style lang="scss" scoped></style>
