<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 批量分配配置对话框
-->
<template>
  <el-dialog
    title="批量分配配置"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    :modal="false"
  >
    <el-form :model="form" :rules="rules" ref="form" label-width="100px">
      <el-form-item label="选择配置" prop="configSource">
        <el-select v-model="form.configSource" placeholder="选择配置" style="width: 100%" clearable>
          <el-option
            v-for="config in filteredConfigs"
            :key="config.id"
            :value="String(config.id)"
            :label="config.configName"
          >
            <div class="option-content-inline">
              <span class="option-title">{{ config.configName }}</span>
              <el-tag
                :type="config.isActive ? 'success' : 'info'"
                size="mini"
                class="option-status-tag"
              >
                {{ config.isActive ? '激活' : '未激活' }}
              </el-tag>
              <span class="option-separator">|</span>
              <span class="option-detail" v-if="config.logUploadInterval">
                上传间隔: {{ formatInterval(config.logUploadInterval) }}
              </span>
              <span class="option-detail" v-else style="color: #F56C6C;">
                上传间隔: 未设置
              </span>
            </div>
          </el-option>
        </el-select>
        <div v-if="filteredConfigs.length === 0" style="color: #909399; font-size: 12px; margin-top: 5px;">
          暂无可用配置
        </div>
      </el-form-item>

      <!-- <el-form-item label="覆盖现有配置" prop="overrideExisting">
        <el-switch
          v-model="form.overrideExisting"
          active-text="是"
          inactive-text="否"
        />
        <div class="form-tip">
          <i class="el-icon-info"></i>
          <span v-if="form.overrideExisting" style="color: #E6A23C;">
            ⚠️ 将替换目标已有的配置分配（删除分配后会回退到默认配置）
          </span>
          <span v-else style="color: #67C23A;">
            ✅ 只为没有配置的目标分配新配置
          </span>
        </div>
      </el-form-item> -->
      
      <el-form-item label="分配类型" prop="targetType">
        <el-radio-group v-model="form.targetType">
          <el-radio label="USER">用户</el-radio>
          <el-radio label="DEVICE">设备</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="目标选择" prop="targets">
        <div class="target-selection">
          <div class="selection-header">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索..."
              prefix-icon="el-icon-search"
              size="small"
              style="width: 200px"
              @input="handleSearch"
            />
            <el-button size="small" @click="selectAll">全选</el-button>
            <el-button size="small" @click="clearAll">清空</el-button>
          </div>
          
          <div class="selection-content">
            <div class="target-list">
              <template v-if="filteredTargets.length > 0">
                <el-checkbox
                  v-for="item in filteredTargets"
                  :key="item.id"
                  :value="isTargetSelected(item)"
                  @change="handleTargetChange(item, $event)"
                  class="target-item"
                >
                  <div class="target-info">
                    <div class="target-name">
                      {{ item.displayName || item.name || '未知目标' }}
                    </div>
                  </div>
                </el-checkbox>
              </template>
              <div v-else class="empty-state">
                <el-empty
                  :image-size="60"
                  :description="form.targetType === 'USER' ? '暂无用户数据' : '暂无设备数据'"
                />
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        确定分配 ({{ form.targets.length }})
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { deviceApi } from '@/logcontrol/api/deviceApi'
import { userListApi } from '@/api/user'

export default {
  name: 'BatchAssignDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },

    configs: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      searchKeyword: '',
      allTargets: [],
      form: {
        sourceType: 'CONFIG_ID',
        configSource: '',
        overrideExisting: true,  // 默认启用覆盖模式
        targetType: 'USER',
        targets: []
      },
      rules: {
        configSource: [
          { required: true, message: '请选择配置', trigger: 'change' }
        ],
        targetType: [
          { required: true, message: '请选择分配类型', trigger: 'change' }
        ],
        targets: [
          { required: true, message: '请选择分配目标', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },

    // 过滤后的配置数据
    filteredConfigs() {
      return this.configs || []
    },


    filteredTargets() {
      if (!this.searchKeyword) return this.allTargets
      
      return this.allTargets.filter(item => {
        const name = item.name || item.username || item.deviceId || ''
        const id = item.id || ''
        return name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
               id.toLowerCase().includes(this.searchKeyword.toLowerCase())
      })
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.resetForm()
        this.loadTargets()
      }
    },
    'form.targetType'() {
      this.form.targets = []
      this.loadTargets()
    }
  },
  methods: {
    async loadTargets() {
      try {
        if (this.form.targetType === 'USER') {
          // 加载用户列表
          const response = await userListApi({ pageNumber: 1, pageSize: 1000 })

          if (response.code === 200 && response.data && response.data.rows) {
            this.allTargets = response.data.rows.map(user => {
              const mappedUser = {
                id: user.id,
                name: user.name,
                code: user.code,
                username: user.code, // 使用code作为username
                displayName: `${user.name || '未知用户'} (${user.code || '无编码'})` // 显示名称和编码
              }
              return mappedUser
            })
          } else {
            this.allTargets = []
          }
        } else {
          // 加载设备列表
          const response = await deviceApi.getDeviceList()
          if (response.code === 200 && response.data) {
            this.allTargets = response.data.map(device => ({
              id: device.id,
              name: device.deviceId,
              deviceId: device.deviceId,
              brand: device.brand,
              model: device.model,
              osVersion: device.osVersion,
              displayName: `${device.deviceId} (${device.model} ${device.osVersion})` // 显示设备ID、型号、系统版本
            }))
          } else {
            this.allTargets = []
          }
        }
      } catch (error) {
        this.$message.error('加载目标列表失败')
        this.allTargets = []
      }
    },
    
    handleSearch() {
      // 搜索逻辑在computed中处理
    },
    
    selectAll() {
      this.form.targets = [...this.filteredTargets.map(item => ({
        id: item.id,
        name: item.displayName || item.name || '未知目标'
      }))]
    },

    clearAll() {
      this.form.targets = []
    },

    // 检查目标是否已选中
    isTargetSelected(item) {
      return this.form.targets.some(target => target.id === item.id)
    },

    // 处理目标选择变化
    handleTargetChange(item, checked) {
      const target = {
        id: item.id,
        name: item.displayName || item.name || '未知目标'
      }

      if (checked) {
        if (!this.isTargetSelected(item)) {
          this.form.targets.push(target)
        }
      } else {
        this.form.targets = this.form.targets.filter(t => t.id !== item.id)
      }
    },

    // 获取目标显示名称
    getTargetDisplayName(item) {
      if (this.form.targetType === 'USER') {
        return item.name || item.username || item.code
      } else {
        return item.deviceId || item.name
      }
    },
    
    handleConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 构建符合后端接口要求的数据结构
          const assignData = {
            configSource: this.form.configSource,
            sourceType: this.form.sourceType,
            overrideExisting: this.form.overrideExisting,
            targets: this.form.targets.map(target => ({
              targetType: this.form.targetType,
              targetId: target.id,
              targetName: target.name
            }))
          }

          this.$emit('confirm', assignData)
        }
      })
    },



    // 格式化间隔时间
    formatInterval(interval) {
      if (!interval || interval === 0) return '未设置'

      const seconds = Number(interval)
      if (isNaN(seconds)) return '格式错误'

      if (seconds < 60) return `${seconds}秒`

      const minutes = Math.floor(seconds / 60)
      if (minutes < 60) return `${minutes}分钟`

      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60

      if (remainingMinutes > 0) {
        return `${hours}小时${remainingMinutes}分钟`
      }
      return `${hours}小时`
    },
    
    handleClose() {
      this.dialogVisible = false
    },
    
    resetForm() {
      this.form = {
        sourceType: 'CONFIG_ID',
        configSource: '',
        overrideExisting: true,  // 默认启用覆盖模式
        targetType: 'USER',
        targets: []
      }
      this.searchKeyword = ''
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 下拉选项样式 - 单行显示
.option-content-inline {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;

  .option-title {
    font-weight: 500;
    color: #303133;
    flex-shrink: 0;
  }

  .option-status-tag {
    flex-shrink: 0;
  }

  .option-separator {
    color: #DCDFE6;
    font-size: 12px;
    flex-shrink: 0;
  }

  .option-detail {
    color: #8492a6;
    font-size: 12px;
    flex-shrink: 0;
  }
}

.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #606266;

  i {
    margin-right: 4px;
  }
}

.target-selection {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  
  .selection-header {
    padding: 10px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fafafa;
  }
  
  .selection-content {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    
    .target-list {
      display: flex;
      flex-direction: column;
      align-items: flex-start; // 确保左对齐

      .target-item {
        margin-bottom: 8px;
        width: 100%;
        text-align: left; // 确保文本左对齐

        .target-info {
          margin-left: 8px;
          text-align: left; // 确保信息左对齐

          .target-name {
            font-weight: 500;
            color: #303133;
            text-align: left; // 确保名称左对齐
            line-height: 1.4;
          }
        }
      }

      .empty-state {
        width: 100%;
        text-align: center;
        padding: 20px;
        color: #909399;
      }
    }
  }
}
</style>
