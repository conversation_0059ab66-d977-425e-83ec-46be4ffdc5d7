<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-21 09:31:13
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <!--<el-tab-pane label="应收款汇总" name="first" lazy>-->
      <!--  <Summary />-->
      <!--</el-tab-pane>-->
      <el-tab-pane label="耗材应收款" name="second" lazy>
        <Consumables />
      </el-tab-pane>
      <el-tab-pane label="机器应收款" name="third" lazy>
        <Equipment />
      </el-tab-pane>
      <el-tab-pane label="维修应收款" name="fourth" lazy>
        <Maintenance />
      </el-tab-pane>
      <el-tab-pane label="抄表应收款" name="fifth" lazy>
        <MeterReading />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// import Summary from "./Summary.vue";
import Consumables from "./components/statistics/Consumables.vue";
import Equipment from "./components/statistics/Equipment.vue";
import Maintenance from "./components/statistics/Maintenance.vue";
import MeterReading from "./components/statistics/MeterReading.vue";

export default {
  name: "Receivables",
  components: { Consumables, Equipment, Maintenance, MeterReading },
  data() {
    return {
      activeName: "second",
    };
  },
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss"></style>
