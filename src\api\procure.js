/*
 * @Author: yang<PERSON><PERSON>
 * @Date: 2023-12-06 09:51:23
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-18 15:22:33
 * @Description: 采购
 */
import { get, post, put, del, down } from "@/utils/request";

// 询价单Api
export const addInquiryApi = (data) => post("/inquiry/add", data);

export const getByArticleIdApi = (data) =>
  post("/inquiry/getByArticleId", data);

export const pageInquiryApi = (data) => post("/inquiry/page", data);

export const updateInquiryApi = (data) => put("/inquiry/updateDetail", data);

export const deleteInquiryApi = (id) => del(`/inquiry/${id}`);

export const articleInquiryOrderPageApi = (data) =>
  get("/storage-article/inquiryOrderPage", data);
// 数据模板下载
export const downTemplateApi = () => down("/inquiry/downloadTemplate");
// 询价单导入
export const importInquiryApi = "/inquiry/import";

// 供应源管理

export const addSupplierApi = (data) => post("/supply-source", data);

export const pageSupplierApi = (data) => post("/supply-source/page", data);

export const updateSupplierApi = (data) => put("/supply-source", data);

export const deleteSupplierApi = (id) => del(`/supply-source/${id}`);

// 修改状态
export const updateSupplierStatusApi = (data) =>
  put("/supply-source/updateStatus", data);

// =========采购单申请单管理=========

// 添加
export const addPurchaseApi = (data) => post("/purchase-order", data);
// 分页查询
export const pagePurchaseApi = (data) => post("/purchase-order/page", data);
// 复核
export const updatePurchaseApi = (data) => post("/purchase-order/audit", data);
// 详情
export const getPurchaseApi = (id) =>
  get(`/purchase-order/getOne`, {
    id,
  });
// 关闭申请单
export const closePurchaseApi = (id) => put(`/purchase-order/close?id=${id}`);
// 获取待收货列表
export const getReceiveListApi = (purchaseCode) =>
  get(`/purchase-order/getReceiveList?purchaseCode=${purchaseCode}`);
// 采购申请单确认收货
export const confirmReceiptApi = (data) =>
  post("/purchase-order/receive", data);
// 采购申请单收货明细
export const getReceiveDetailApi = (purchaseCode) =>
  get(`/purchase-order/getReceiveInfo?purchaseCode=${purchaseCode}`);
// 查询库存量
export const getStorageApi = (data) =>
  post("/purchase-order/getRemWarehouseNumber", data);

export const queryByArticleIdsApi = (idList) =>
  post("/supply-source/queryByArticleIds", { idList });

// =============================  机器采购管理  =======================
// 分页查询
export const getMachinePurchaseApi = (data) =>
  post("/machine/purchase/page", data);
// 详情
export const getMachinePurchaseDetailApi = (id) =>
  get(`/machine/purchase/${id}`);
// 添加机器采购
export const addMachinePurchaseApi = (data) => post("/machine/purchase", data);
// 复核
export const updateMachinePurchaseApi = (data) =>
  put("/machine/purchase/audit", data);
// 关闭机器采购申请单
export const closeMachinePurchaseApi = (id) => del(`/machine/purchase/${id}`);
// 确认收货机器列表信息
export const getMachinePurchaseReceiveListApi = (id) =>
  get(`/machine/purchase/receiveInfo/${id}`);
// 确认收货入库
export const confirmReceiptMachineApi = (data) =>
  post("/machine/purchase/receive", data);
// 上传支付信息
export const uploadPaymentInfoApi = (data) =>
  put("/machine/purchase/pay", data);
// =============================  机器退货管理  =======================
// 机器退货列表
export const getMachineReturnApi = (data) => post("/machine/return/page", data);
// 新增机器退货
export const addMachineReturnApi = (data) => post("/machine/return", data);
// 机器退货明细
export const getMachineReturnDetailApi = (id) => get(`/machine/return/${id}`);
// 审核机器退货单
export const updateMachineReturnApi = (data) =>
  put("/machine/return/audit", data);
// 关闭机器退货单
export const closeMachineReturnApi = (id) => del(`/machine/return/${id}`);

// ========================  采购预警  ===========================
// 分页查询
export const getPurchaseWarningApi = (data) =>
  post("/purchase-require/page", data);
// 修改
export const updatePurchaseWarningApi = (data) =>
  put("/purchase-require", data);
// 删除
export const deletePurchaseWarningApi = (id) => del(`/purchase-require/${id}`);
// 生成采购单
export const addPurchaseOrderApi = (data) =>
  post("/purchase-require/purchase", data);
// 批量删除
export const deletePurchaseWarningBatchApi = (data) =>
  del("/purchase-require/batch", data);
// 全部删除
export const deletePurchaseWarningAllApi = () => del("/purchase-require/all");
// 根据仓库重新刷新
export const refreshPurchaseWarningByWarehouseApi = (warehouseId) =>
  post(`/purchase-require/refresh/${warehouseId}`);
export const refreshPurchaseWarningApi = () =>
  post(`/purchase-require/refresh`);

// ========================  采购付款单  ==========================
// 列表查询
export const pagePurchasePaymentApi = (data) =>
  post("/purchase/payment/page", data);
// 查看详情
export const getPurchasePaymentApi = (id) => get(`/purchase/payment/${id}`);
// 提交付款单
export const addPurchasePaymentApi = (data) =>
  post("/purchase/payment/saveVoucher", data);
// 付款单审核
export const auditPurchasePaymentApi = (data) =>
  put("/purchase/payment/audit", data);

// 合并付款单
export const mergePurchasePaymentApi = (data) =>
  put("/purchase/payment/mergePayment", data);
// 关闭
export const closePurchasePaymentApi = (id) =>
  put(`/purchase/payment/close/${id}`);

// ========================= 采购明细  ==============================
export const pagePurchaseDetailApi = (data) =>
  post("/purchase-order/detailPage", data);

// ========================  采购统计  ============================
// 按月统计
export const getPurchaseMonthStatisticsApi = (data) =>
  post("/purchase-order/monthlyPage", data);
// 按机型统计
export const getPurchaseModelStatisticsApi = (data) =>
  post("/purchase-order/mechinePage", data);
// 按物品编号
export const getPurchaseArticleStatisticsApi = (data) =>
  post("/purchase-order/articlePage", data);
// 按供应商
export const getPurchaseSupplierStatisticsApi = (data) =>
  post("/purchase-order/supplierPage", data);
// 合计统计
export const getPurchaseTotalStatisticsApi = (data) =>
  post("/purchase-order/summary", data);

//===========================  退货统计  ============================
// 按供应商
export const getReturnSupplierStatisticsApi = (data) =>
  post("/manufacterReturn/supplierPage", data);
// 按机型统计
export const getReturnModelStatisticsApi = (data) =>
  post("/manufacterReturn/mechinePage", data);
// 按零件编号
export const getReturnArticleStatisticsApi = (data) =>
  post("manufacterReturn/articlePage", data);

//===========================  零件申请  ============================
// 列表
export const getArticleApplyApi = (data) => post("/part-require/page", data);
