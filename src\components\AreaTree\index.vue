<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-29 12:02:17
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-24 09:38:49
 * @Description: 
 -->
<template>
  <div class="app-container">
    <el-cascader
      ref="areaIds"
      v-model="areaIdName"
      style="width: 100%"
      class="area-id"
      filterable
      :clearable="clearable"
      :collapse-tags="collapseTags"
      :options="areaOptions"
      :props="options"
      :placeholder="placeholder"
      leaf-only
      @change="handleChange"
    ></el-cascader>
  </div>
</template>

<script>
import { regionTreeApi } from "@/api/store";

export default {
  name: "AreaTree",
  model: {
    prop: "modelValue",
    event: "change",
  },
  props: {
    multiple: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    collapseTags: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: <PERSON>olean,
      default: true,
    },
    modelValue: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      areaIdName: [],
      options: {
        label: "name",
        value: "id",
        children: "children",
        expandTrigger: "click",
        multiple: this.multiple,
      },
      areaOptions: [],
    };
  },
  mounted() {
    this.getareaOptions();
  },
  methods: {
    getareaOptions() {
      regionTreeApi().then((res) => {
        const areaTree = res.data;
        this.areaOptions = areaTree;
        if (this.modelValue && this.modelValue.length > 0) {
          this.areaIdName = this.modelValue.map((o) => {
            return this.getParentList(o);
          });
        } else {
          this.areaIdName = [];
        }
      });
    },
    //查找并返回所有父级行政区
    getParentList(code) {
      const result = [];
      //省级
      const obj = this.areaOptions.find((o) => {
        const pCode = o.code + "";
        return pCode.substring(0, 2) == code.substring(0, 2);
      });
      result.push(obj.code);
      //市级
      const cityList = obj.children;
      const first = cityList[0].code + "";
      if (first.substring(4, 6) == "00") {
        const cityObj = cityList.find(
          (o) => (o.code + "").substring(0, 4) == code.substring(0, 4)
        );
        cityObj && result.push(cityObj.code);
      }
      //和区县
      result.push(Number(code));
      return result;
    },
    handleChange(val) {
      let result = null;
      if (this.multiple) {
        result = val.map((item) => item[item.length - 1]);
      } else {
        result = val[val.length - 1];
      }
      this.$emit("change", result);
    },
  },
};
</script>

<style scoped lang="scss">
:deep(.area-id.is-disabled) {
  .el-cascader__tags {
    max-height: 200px;
    overflow: auto;
  }
}
</style>
