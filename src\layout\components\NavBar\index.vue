<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-31 15:00:50
 * @Description: 
 -->
<template>
  <div class="nav-bar-container">
    <el-row :gutter="15">
      <el-col v-if="mode !== ''" :xs="4" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="left-panel">
          <!--<i :class="collapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'" :title="collapse ? '展开' : '收起'" class="fold-unfold"-->
          <!--  @click="handleCollapse"></i>-->
          <template v-if="isBreadcrumb">
            <Breadcrumb class="hidden-xs-only" />
          </template>
        </div>
      </el-col>
      <el-col :xs="20" :sm="12" :md="12" :lg="12" :xl="12">
        <RightPanel />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Breadcrumb from "../Breadcrumb/index.vue";
import RightPanel from "./RightPanel.vue";

import { isEmpty, cloneDeep } from "lodash";
import { mapActions, mapGetters } from "vuex";

export default {
  name: "NavBar",
  components: {
    Breadcrumb,
    RightPanel,
  },
  mixins: [],
  props: {
    isCollapse: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mode: "vertical",
      // 主题 默认配置theme1
      // 注意⚠️ ：修改默认主题时，记得同步修改 element-variables.scss
      // 文件中的 $base-color-primary 默认值，否则不生效！！！
      theme: "theme2",
      // 主题配置
      themeOptions: {
        theme1: { menuBgColor: "#ffffff", primary: "#7e9cff" },
        theme2: { menuBgColor: "#293246", primary: "#7e9cff" },
        theme3: { menuBgColor: "#ffffff", primary: "#08a17e" },
        theme4: { menuBgColor: "#293246", primary: "#08a17e" },
        theme5: { menuBgColor: "#ffffff", primary: "#f45555" },
        theme6: { menuBgColor: "#293246", primary: "#f45555" },
      },
      // 是否固定头部
      fixedHead: true,
      // 是否显示全屏
      fullScreen: true,
      // 是否显示刷新
      refresh: true,
      // 是否显示通知
      notice: true,
      // 是否显示面包导航
      isBreadcrumb: true,
      // 是否显示logo
      isLogo: true,
      // 是否显示标签
      tag: true,
    };
  },

  computed: {
    ...mapGetters({
      collapse: "collapse", // 是否展开菜单
    }),
  },

  watch: {},
  created() {},
  mounted() {},
  methods: {
    ...mapActions({
      changeCollapse: "settings/changeCollapse",
    }),
    handleCollapse() {
      this.changeCollapse();
    },
    handleClose() {},
  },
};
</script>

<style lang="scss" scoped>
.nav-bar-container {
  position: relative;
  height: $base-nav-bar-height;
  padding-right: $base-padding;
  overflow: hidden;
  user-select: none;
  background: $base-color-white;
  box-shadow: $base-box-shadow;

  .left-panel {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-items: center;
    height: $base-nav-bar-height;

    .fold-unfold {
      color: $base-color-gray;
      cursor: pointer;
    }

    .fold {
      padding: $base-padding-20-10;
    }

    :deep(.breadcrumb-container) {
      margin-left: $base-margin-10;
    }
  }
}
</style>
