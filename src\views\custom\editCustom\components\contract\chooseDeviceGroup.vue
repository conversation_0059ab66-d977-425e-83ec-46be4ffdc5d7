<template>
  <div class="app-container">
    <!-- 选择机器/选配件 -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="70%"
      :top="'2%'"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProTable
        ref="ProTable"
        :row-key="(row) => row.machineNum"
        :query-param="queryParam"
        :local-pagination="localPagination"
        :columns="columns"
        :data="tableData"
        :show-selection="true"
        :height="400"
        @loadData="loadData"
        @handleSelectionChange="handleSelectionChange"
      >
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { getCustomerDeviceGroupByPageApi } from "@/api/customer";

export default {
  name: "ChooseDispatchMachine",
  props: {
    customerId: {
      type: String,
      default: "",
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    selectedData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 选择机器/选配件
      dialogTitle: "选择签约设备组",
      queryParam: {},
      defaultQueryParam: {
        // status: ["ON_SALE"],
        // isSale: true,
      },
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 10,
      },
      columns: [
        {
          title: "设备组编号",
          dataIndex: "deviceSeqId",
          isTable: true,
          width: 150,
        },
        {
          title: "设备组名称",
          dataIndex: "deviceGroup",
          isTable: true,
          formatter: (row) => row.deviceGroup?.label,
        },
        {
          title: "品牌/机型",
          dataIndex: "productInfo",
          isTable: true,
        },
        {
          title: "设备状态",
          dataIndex: "deviceStatus",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
        },
        {
          title: "启动状态",
          dataIndex: "status",
          isTable: true,
          formatter: (row) => (row.status ? "启用" : "禁用"),
        },
        {
          title: "移动端可见",
          dataIndex: "dataShowState",
          isTable: true,
          formatter: (row) => (row.dataShowState ? "是" : "否"),
        },
        {
          title: "安装客户端",
          dataIndex: "regCliState",
          isTable: true,
          formatter: (row) => (row.regCliState == "1" ? "是" : "否"),
        },
        {
          title: "服务类型",
          dataIndex: "serType",
          isTable: true,
          formatter: (row) => row.serType?.label,
        },
        {
          title: "负责工程师",
          dataIndex: "operatName",
          isTable: true,
        },
      ],
      tableData: [],
      selectionData: [],
    };
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.ProTable.refresh();
        });
      }
    },
  },
  mounted() {},
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      requestParameters.serTypes = ["SCATTERED", "NO_WARRANTY"];
      getCustomerDeviceGroupByPageApi({
        ...requestParameters,
        customerId: this.customerId,
      })
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          this.$refs.ProTable.$refs.ProElTable.clearSelection();
          if (this.selectedData.length) {
            this.selectedData.forEach((row) => {
              this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
                row,
                true
              );
            });
          }
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleDialogOk() {
      // 将selectionData中的id值清除
      this.selectionData.forEach((item) => {
        item.deviceGroupId = item.id;
        delete item.id;
        delete item.serType;
      });
      this.$emit("confirmDispatch", this.selectionData);
    },
    handleDialogCancel() {
      this.$emit("update:dialogVisible", false);
    },
    handleSelectionChange(row) {
      this.selectionData = row;
    },
  },
};
</script>

<style scoped lang="scss"></style>
