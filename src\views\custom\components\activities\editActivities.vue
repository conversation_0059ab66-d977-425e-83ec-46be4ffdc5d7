<!--
 * @Description: 
 * @version: 
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-10 10:46:51
 * @LastEditors: <PERSON>le
 * @LastEditTime: 2025-02-14 10:41:01
-->
<template>
  <ProDrawer
    :value="showDrawer"
    :title="title"
    size="80%"
    :no-footer="false"
    :destroy-on-close="true"
    :no-confirm-footer="true"
    @cancel="cancel"
  >
    <!---->
    <el-row>
      <el-form
        v-if="title"
        ref="activitiesForm"
        :model="formData"
        label-width="140px"
        :disabled="formType == 'info'"
      >
        <el-col :lg="6" :xs="24" :sm="12">
          <el-form-item
            label="活动名称："
            prop="activityName"
            :rules="[
              { required: true, message: '请输入活动名称', trigger: 'change' },
            ]"
          >
            <el-input v-model="formData.activityName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :xs="24" :sm="12">
          <el-form-item
            label="活动类型："
            prop="activityType"
            :rules="[
              { required: true, message: '请选择活动类型', trigger: 'change' },
            ]"
          >
            <el-select
              v-model="formData.activityType"
              style="width: 100%"
              @change="changeActivityType"
            >
              <el-option label="特价活动" value="DISCOUNT"></el-option>
              <el-option label="分享活动" value="SHARE"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :xs="24" :sm="12">
          <el-form-item
            label="开始时间："
            prop="startTime"
            :rules="[
              {
                required: true,
                message: '请选择活动开始时间',
                trigger: 'change',
              },
            ]"
          >
            <el-date-picker
              v-model="formData.startTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期"
              style="width: 100%; max-width: 240px"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :xs="24" :sm="12">
          <el-form-item
            label="结束时间："
            prop="endTime"
            :rules="[
              {
                required: true,
                message: '请选择活动结束时间',
                trigger: 'change',
              },
            ]"
          >
            <el-date-picker
              v-model="formData.endTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期"
              type="datetime"
              style="width: 100%; max-width: 240px"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="24" :xs="24" :sm="24">
          <el-form-item label="：">
            <template #label>
              <el-tooltip
                class="item"
                effect="dark"
                content="不选择区域范围则是全国范围内都可参与"
                placement="top-start"
              >
                <div>区域范围<i class="el-icon-question"></i>：</div>
              </el-tooltip>
            </template>
            <AreaTree
              v-model="formData.activityRange"
              style="width: 100%"
              placeholder="请选择区域范围"
              :multiple="true"
              :collapse-tags="formType == 'info' ? false : true"
              :clearable="true"
              @change="(e) => handleProductChange(e, 'activityRange')"
            />
          </el-form-item>
        </el-col>

        <el-col :lg="24" :xs="24" :sm="24">
          <el-form-item
            label="活动介绍："
            prop="description"
            :rules="[
              { required: true, message: '请输入活动介绍', trigger: 'change' },
            ]"
          >
            <el-input
              v-model="formData.description"
              type="textarea"
              style="width: 100%"
              :autosize="{ minRows: 4, maxRows: 8 }"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :xs="24" :sm="12">
          <el-form-item
            label="费用预算："
            prop="budgetAmount"
            :rules="[
              { required: true, message: '请填写费用预算', trigger: 'change' },
            ]"
          >
            <el-input
              v-model="formData.budgetAmount"
              type="number"
              style="width: 100%; max-width: 240px"
              ><span slot="suffix">元</span></el-input
            >
          </el-form-item>
        </el-col>
        <el-col :lg="6" :xs="12" :sm="24">
          <el-form-item
            label="商城首页小海报："
            prop="promotionPic"
            :rules="[
              {
                required: true,
                message: '请上传商城展示小海报',
                trigger: 'change',
              },
            ]"
          >
            <ProUpload
              :file-list="formData.promotionPic"
              type="image"
              style="padding-left: 0"
              :limit="1"
              :multiple="false"
              @uploadSuccess="
                (e) => handlePhotoUploadSuccess(e, 'promotionPic')
              "
              @uploadRemove="(e) => handlePhotoUploadRemove(e, 'promotionPic')"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :xs="12" :sm="24">
          <el-form-item
            label="活动大海报："
            prop="mainPic"
            :rules="[
              {
                required: true,
                message: '请上传活动大海报',
                trigger: 'change',
              },
            ]"
          >
            <ProUpload
              :file-list="formData.mainPic"
              type="image"
              :limit="1"
              style="padding-left: 0"
              :multiple="false"
              @uploadSuccess="(e) => handlePhotoUploadSuccess(e, 'mainPic')"
              @uploadRemove="(e) => handlePhotoUploadRemove(e, 'mainPic')"
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="formData.activityType == 'SHARE'"
          :lg="24"
          :xs="12"
          :sm="24"
        >
          <el-form-item label="有无特价活动：" prop="hasDiscount">
            <el-radio v-model="formData.hasDiscount" border :label="true">
              有特价活动
            </el-radio>
            <el-radio v-model="formData.hasDiscount" border :label="false">
              无特价活动
            </el-radio>
            <!--<div>-->
            <!--  <el-switch-->
            <!--    v-model="formData.hasDiscount"-->
            <!--    style="display: block"-->
            <!--    active-color="#13ce66"-->
            <!--    inactive-color="#ff4949"-->
            <!--    active-text="有特价"-->
            <!--    inactive-text="无特价"-->
            <!--  >-->
            <!--  </el-switch>-->
            <!--</div>-->
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <SkuTable
            v-if="formData.activityType == 'DISCOUNT'"
            v-model="formData.activitySkus"
            :activity-id="formData.id"
          ></SkuTable>
          <!-- 分享获得礼品 -->
          <ShareTable
            v-else-if="formData.activityType == 'SHARE'"
            v-model="formData.activityAwards"
            :activity-id="formData.id"
          ></ShareTable>
        </el-col>
      </el-form>
    </el-row>
    <template #footer>
      <div v-if="formType != 'info'">
        <el-button
          v-if="formData.status == 'STASH' || formData.status == 'REJECT'"
          type="primary"
          @click="submitForm('STASH')"
        >
          暂存
        </el-button>
        <el-button type="primary" @click="submitForm('WAIT_APPROVE')">
          提交审核
        </el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </ProDrawer>
</template>
<script>
import ProUpload from "@/components/ProUpload/index.vue";
import AreaTree from "@/components/AreaTree/index.vue";
import { updateActivitiesApi, getActivitiesDetailApi } from "@/api/customer";
import { Message } from "element-ui";
import SkuTable from "./skuTable.vue";
import ShareTable from "./shareTable.vue";
import { cloneDeep } from "lodash";
export default {
  name: "ComponentName",
  components: { ProUpload, AreaTree, SkuTable, ShareTable },
  props: {},
  data() {
    return {
      formType: "",
      showDrawer: false,
      title: "",
      formData: {
        activityAwards: [],
        activityName: "",
        activityRange: [],
        activitySkus: [],
        activityType: "",
        budgetAmount: 0,
        code: "",
        description: "",
        endTime: "",
        mainPic: [],
        promotionPic: [],
        startTime: "",
        status: "STASH", //CLOSE,ENDED,IN_PROGRESS,NOT_STARTED,REJECT,STASH,WAIT_APPROVE
      },
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    handleProductChange(val) {
      this.formData.activityRange = val;
    },
    cancel() {
      this.showDrawer = false;
    },
    open(type, row) {
      this.showDrawer = true;
      this.formType = type;
      if (type == "add") {
        this.formData = {
          activityAwards: [],
          activityName: "",
          activityRange: [],
          activitySkus: [],
          activityType: "",
          budgetAmount: 0,
          code: "",
          description: "",
          endTime: "",
          mainPic: [],
          promotionPic: [],
          startTime: "",
          status: "STASH", //CLOSE,ENDED,IN_PROGRESS,NOT_STARTED,REJECT,STASH,WAIT_APPROVE
        };
        this.title = "新增活动";
      } else {
        getActivitiesDetailApi(row.id).then(({ data }) => {
          data.activityType = data.activityType.value;
          data.status = data.status.value;
          if (data.activityAwards && data.activityAwards.length > 0) {
            data.activityAwards = data.activityAwards.map((o) => {
              o.awardType = o.awardType.value;
              return o;
            });
          }
          this.formData = data;
          this.title = "活动编号-" + data.code;
        });
      }
    },
    handlePhotoUploadSuccess(result, type) {
      if (!this.formData[type]) {
        this.$set(this.formData, type, []);
      }
      this.formData[type].push(result);
    },

    handlePhotoUploadRemove(file, type) {
      const index = this.formData[type].findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formData[type].splice(index, 1);
    },
    submitForm(status) {
      this.$refs.activitiesForm.validate((valid) => {
        if (valid) {
          const submitForm = cloneDeep(this.formData);
          submitForm.activityAwards.forEach((item) => {
            delete item.fullIdPath;
          });
          submitForm.status = status;
          updateActivitiesApi(submitForm).then((res) => {
            this.$emit("refresh", true);
            Message.success(res.message);
            this.cancel();
          });
        }
      });
    },
    changeActivityType() {
      this.formData.activityAwards = [];
      this.formData.activitySkus = [];
      this.$set(this.formData, "hasDiscount", false);
    },
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-input) {
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type="number"] {
    -moz-appearance: textfield;
  }
}
</style>
