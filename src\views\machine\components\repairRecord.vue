<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 17:58:30
 * @Description: 
 -->
<template>
  <el-tabs v-model="activeName" type="card">
    <el-tab-pane label="自修记录" name="自修记录" lazy>
      <MineRepair />
    </el-tab-pane>
    <el-tab-pane label="报修记录" name="报修记录" lazy>
      <MaintenanceOrder />
    </el-tab-pane>
    <!--    <el-tab-pane label="换件记录" name="换件记录" lazy>-->
    <!--      <PartRecord />-->
    <!--         换件记录: 旧   -->
    <!--            <PartAccount />-->
    <!--    </el-tab-pane>-->
    <el-tab-pane label="用料记录" name="用料记录" lazy>
      <Used />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
// 自修记录表
import MineRepair from "@/views/machine/components/mineRepair.vue";
// 统计维修记录表
import MaintenanceOrder from "@/views/machine/components/maintenanceOrder.vue";
// 换件记录
// import PartRecord from "@/views/machine/components/partRecord.vue";
// 零件更换统计表
// import PartAccount from "@/views/machine/components/partAccount.vue";
import Used from "@/views/custom/used.vue";
export default {
  name: "RepairRecord",
  components: { MineRepair, MaintenanceOrder, Used },
  data() {
    return {
      activeName: "自修记录",
    };
  },
};
</script>

<style scoped lang="scss"></style>
