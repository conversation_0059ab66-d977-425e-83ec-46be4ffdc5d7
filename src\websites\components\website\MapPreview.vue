<template>
  <div
    class="map-preview-component"
    :style="{ height: height }"
  >
    <!-- 地图容器始终存在，通过CSS控制显示 -->
    <div ref="mapContainer" class="map-container" :class="{ 'map-hidden': loading || error }"></div>

    <!-- 加载状态覆盖层 -->
    <div v-if="loading" class="map-loading">
      <i class="el-icon-loading"></i>
      <span>地图加载中...</span>
    </div>

    <!-- 错误状态覆盖层 -->
    <div v-else-if="error" class="map-error">
      <i class="el-icon-warning"></i>
      <span>{{ error }}</span>
    </div>

    <!-- 导航按钮 -->
    <div v-if="showNavigateButton && coords && !loading && !error" class="navigate-button">
      <el-button size="small" type="primary" @click="handleNavigate">
        <i class="el-icon-location"></i>
        导航
      </el-button>
    </div>
  </div>
</template>

<script>
import { useTencentMapKey } from '@/websites/hooks/useTencentMapKey'

/** 腾讯地图脚本加载 Promise，确保全局仅加载一次 */
let tMapScriptLoading = null

/**
 * 动态加载腾讯地图 JS SDK
 */
function loadTencentMapScript(mapKey) {
  if (typeof window !== 'undefined' && window.TMap) {
    return Promise.resolve()
  }
  if (!tMapScriptLoading) {
    tMapScriptLoading = new Promise((resolve, reject) => {
      const script = document.createElement('script')
      // 如果没有配置key，直接抛出错误
      if (!mapKey) {
        reject(new Error('腾讯地图API Key未配置，请在后台配置页面设置'))
        return
      }
      script.src = `https://map.qq.com/api/gljs?v=1.exp&libraries=service&key=${mapKey}`
      script.async = true
      script.onload = () => resolve()
      script.onerror = () => reject(new Error('腾讯地图脚本加载失败'))
      document.head.appendChild(script)
    })
  }
  return tMapScriptLoading
}

export default {
  name: 'MapPreview',
  props: {
    address: {
      type: String,
      required: true
    },
    height: {
      type: [Number, String],
      default: 320
    },
    showNavigateButton: {
      type: Boolean,
      default: true
    }
  },
  setup() {
    // 获取腾讯地图key
    const { mapKey, loading: keyLoading, error: keyError } = useTencentMapKey()

    return {
      tencentMapKey: mapKey,
      keyLoading,
      keyError
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      coords: null,
      mapInstance: null,
      markerLayer: null
    }
  },
  watch: {
    address: {
      handler: 'initMap',
      immediate: false // 改为false，避免在组件未完全挂载时执行
    },
    tencentMapKey: {
      handler: 'initMap'
    }
  },
  mounted() {
    // 在组件挂载后初始化地图
    this.$nextTick(() => {
      if (this.address) {
        this.initMap()
      }
    })
  },
  beforeDestroy() {
    this.destroyMap()
  },
  methods: {
    async initMap() {
      // 先销毁之前的地图实例
      this.destroyMap()

      if (!this.address) {
        this.loading = false
        this.error = '地址为空'
        return
      }

      // 等待key加载完成
      if (this.keyLoading) {
        return
      }

      if (this.keyError) {
        this.error = `腾讯地图Key获取失败: ${this.keyError}`
        this.loading = false
        return
      }

      // 检查是否有配置key
      if (!this.tencentMapKey || this.tencentMapKey.trim() === '') {
        this.error = '腾讯地图API Key未配置，请联系管理员在后台配置页面设置'
        this.loading = false
        return
      }

      this.loading = true
      this.error = null

      try {
        // 等待DOM完全渲染
        await this.$nextTick()

        // 再次检查DOM元素是否存在
        if (!this.$refs.mapContainer) {
          await new Promise(resolve => setTimeout(resolve, 100))
          await this.$nextTick()
        }

        await this.loadRealMap()
      } catch (err) {
        console.error('地图初始化失败:', err)
        this.error = err.message || '地图加载失败'
      } finally {
        this.loading = false
      }
    },

    async loadRealMap() {
      try {
        await loadTencentMapScript(this.tencentMapKey)
        const { TMap } = window
        if (!TMap) throw new Error('腾讯地图脚本未正确加载')

        // 检查地理编码服务是否可用
        if (!TMap.service || !TMap.service.Geocoder) {
          throw new Error('腾讯地图地理编码服务不可用')
        }

        const geocoder = new TMap.service.Geocoder()
        const geocodeResult = await geocoder.getLocation({ address: this.address })

        if (!geocodeResult || !geocodeResult.result || !geocodeResult.result.location) {
          throw new Error('地址解析失败')
        }

        const { location } = geocodeResult.result
        const center = new TMap.LatLng(location.lat, location.lng)
        this.coords = { lat: location.lat, lng: location.lng }

        // 检查DOM容器是否存在
        if (!this.$refs.mapContainer) {
          throw new Error('地图容器DOM元素不存在')
        }

        // 检查容器是否在DOM中
        if (!document.contains(this.$refs.mapContainer)) {
          throw new Error('地图容器不在DOM树中')
        }

        // 确保容器有唯一ID
        const containerId = `map-container-${Date.now()}`
        this.$refs.mapContainer.id = containerId

        // 等待DOM更新
        await this.$nextTick()

        // 创建地图实例 - 使用容器ID字符串
        const mapOptions = {
          center: center,
          zoom: 16
        }

        // 检查是否有MapTypeId枚举
        if (TMap.MapTypeId && TMap.MapTypeId.NORMAL) {
          mapOptions.mapTypeId = TMap.MapTypeId.NORMAL
        }

        // 使用容器ID字符串而不是DOM元素
        try {
          this.mapInstance = new TMap.Map(containerId, mapOptions)
        } catch (mapError) {
          // 尝试使用DOM元素方式
          this.mapInstance = new TMap.Map(this.$refs.mapContainer, mapOptions)
        }

        // 创建标记点 - 使用更兼容的方式
        try {
          // 等待地图实例完全初始化
          await new Promise(resolve => setTimeout(resolve, 100))

          this.markerLayer = new TMap.MultiMarker({
            id: `marker-layer-${Date.now()}`,
            map: this.mapInstance,
            geometries: [{
              id: `marker-${Date.now()}`,
              position: center
            }]
          })
        } catch (markerError) {
          // 标记点创建失败不影响地图显示
        }

      } catch (error) {
        await this.loadMapPlaceholder()
      }
    },

    async loadMapPlaceholder() {
      // 模拟地图加载
      await new Promise(resolve => setTimeout(resolve, 500))

      // 设置模拟坐标
      this.coords = { lat: 39.90923, lng: 116.397428 }

      // 显示占位符内容
      if (this.$refs.mapContainer) {
        this.$refs.mapContainer.innerHTML = `
          <div class="map-placeholder-content">
            <div class="map-icon">📍</div>
            <div class="map-address">${this.address}</div>
            <div class="map-hint">地图预览</div>
          </div>
        `
      }
    },

    handleNavigate() {
      if (!this.address) return

      // 检测是否为移动设备
      const isMobile = this.isMobileDevice()

      if (isMobile) {
        // 移动端显示导航应用选择
        this.showNavigationOptions()
      } else {
        // 桌面端直接使用腾讯地图网页版
        const { lat, lng } = this.coords || {}
        const uri = lat && lng
          ? `https://apis.map.qq.com/uri/v1/routeplan?type=drive&to=${encodeURIComponent(this.address)}&tocoord=${lat},${lng}&policy=0&referer=benyin`
          : `https://map.qq.com/m/search/${encodeURIComponent(this.address)}`
        window.open(uri, '_blank')
      }
    },

    // 检测是否为移动设备
    isMobileDevice() {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera
      return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
    },

    // 显示导航应用选择
    showNavigationOptions() {
      const encodedAddress = encodeURIComponent(this.address)
      const { lat, lng } = this.coords || {}
      const userAgent = navigator.userAgent

      // 构建导航应用列表
      const apps = []

      // iOS设备添加Apple Maps
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        apps.push({
          name: 'Apple 地图',
          icon: '🍎',
          url: lat && lng
            ? `http://maps.apple.com/?daddr=${lat},${lng}&dirflg=d`
            : `http://maps.apple.com/?q=${encodedAddress}&dirflg=d`
        })
      }

      // 添加其他地图应用
      apps.push(
        {
          name: '百度地图',
          icon: '🗺️',
          url: lat && lng
            ? `baidumap://map/navi?location=${lat},${lng}&query=${encodedAddress}&src=benyin`
            : `baidumap://map/place/search?query=${encodedAddress}`
        },
        {
          name: '高德地图',
          icon: '🧭',
          url: lat && lng
            ? `androidamap://navi?sourceApplication=benyin&lat=${lat}&lon=${lng}&dev=0&style=2`
            : `androidamap://poi?sourceApplication=benyin&keywords=${encodedAddress}&dev=0`
        },
        {
          name: '腾讯地图',
          icon: '📍',
          url: lat && lng
            ? `qqmap://map/routeplan?type=drive&to=${encodedAddress}&tocoord=${lat},${lng}&policy=0&referer=benyin`
            : `qqmap://map/search?keyword=${encodedAddress}&referer=benyin`
        }
      )

      // 创建选择弹窗内容
      const h = this.$createElement
      const appButtons = apps.map(app =>
        h('div', {
          class: 'nav-app-item',
          style: {
            padding: '12px 16px',
            borderBottom: '1px solid #eee',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          },
          on: {
            click: () => {
              this.openNavigationApp(app)
            }
          }
        }, [
          h('span', { style: { fontSize: '18px' } }, app.icon),
          h('span', app.name)
        ])
      )

      this.$msgbox({
        title: '选择导航应用',
        message: h('div', { style: { textAlign: 'left' } }, appButtons),
        showCancelButton: true,
        cancelButtonText: '取消',
        showConfirmButton: false,
        customClass: 'navigation-modal'
      })
    },

    // 打开导航应用
    openNavigationApp(app) {
      try {
        // 尝试打开原生应用
        const link = document.createElement('a')
        link.href = app.url
        link.style.display = 'none'
        document.body.appendChild(link)

        // 监听页面可见性变化，判断是否成功打开了原生应用
        let appOpened = false
        const handleVisibilityChange = () => {
          if (document.hidden) {
            appOpened = true
          }
        }

        document.addEventListener('visibilitychange', handleVisibilityChange)

        link.click()
        document.body.removeChild(link)

        // 延迟检查是否成功打开原生应用，如果没有则打开网页版
        setTimeout(() => {
          document.removeEventListener('visibilitychange', handleVisibilityChange)
          if (!appOpened) {
            // 打开对应的网页版地图
            const webUrls = {
              '百度地图': `https://map.baidu.com/search/?querytype=s&wd=${encodeURIComponent(this.address)}`,
              '高德地图': `https://www.amap.com/search?query=${encodeURIComponent(this.address)}`,
              '腾讯地图': `https://map.qq.com/m/search/${encodeURIComponent(this.address)}`,
              'Apple 地图': app.url // Apple地图本身就是网页版
            }
            const webUrl = webUrls[app.name]
            if (webUrl) {
              window.open(webUrl, '_blank')
            }
          }
        }, 2000)

      } catch (error) {
        console.warn('打开导航应用失败，使用网页版:', error)
        // 降级到网页版
        const webUrls = {
          '百度地图': `https://map.baidu.com/search/?querytype=s&wd=${encodeURIComponent(this.address)}`,
          '高德地图': `https://www.amap.com/search?query=${encodeURIComponent(this.address)}`,
          '腾讯地图': `https://map.qq.com/m/search/${encodeURIComponent(this.address)}`,
          'Apple 地图': app.url
        }
        const webUrl = webUrls[app.name]
        if (webUrl) {
          window.open(webUrl, '_blank')
        }
      }
    },

    destroyMap() {
      try {
        if (this.markerLayer) {
          this.markerLayer.destroy()
          this.markerLayer = null
        }
        if (this.mapInstance) {
          this.mapInstance.destroy()
          this.mapInstance = null
        }
        // 清理DOM容器内容
        if (this.$refs.mapContainer) {
          this.$refs.mapContainer.innerHTML = ''
          this.$refs.mapContainer.id = ''
        }
      } catch (error) {
        console.warn('地图销毁时出现错误:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.map-preview-component {
  width: 100%;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;

  .map-container {
    width: 100%;
    height: 100%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &.map-hidden {
      visibility: hidden;
    }

    .map-placeholder-content {
      text-align: center;
      color: #666;

      .map-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      .map-address {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
      }

      .map-hint {
        font-size: 14px;
        color: #999;
      }
    }
  }

  .map-loading, .map-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    color: #666;
    z-index: 10;

    i {
      font-size: 24px;
      margin-bottom: 8px;
    }
  }

  .map-error {
    color: #f56c6c;
  }

  .navigate-button {
    position: absolute;
    bottom: 16px;
    right: 16px;
    z-index: 5;
  }
}

// 导航弹窗样式（全局样式）
:global(.navigation-modal) {
  .el-message-box__content {
    padding: 0 !important;
  }

  .nav-app-item {
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }

    &:last-child {
      border-bottom: none !important;
    }
  }
}
</style>
