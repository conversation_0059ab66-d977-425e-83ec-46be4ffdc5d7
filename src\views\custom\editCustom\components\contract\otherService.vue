<!--
 * @Author: wskg
 * @Date: 2025-01-15 15:30:19
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 维保合约
 -->
<template>
  <div class="warranty-contract">
    <el-form
      ref="formRef"
      :model="infoData"
      label-width="140px"
      :rules="formRules"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="合约金额：">
            <el-input
              v-model="infoData.packageAmount"
              type="number"
              placeholder="请输入合约金额"
              :min="0"
              style="width: 100%"
              :disabled="editType === 'info'"
            >
              <template #suffix> 元 </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="合约开始时间：" prop="startTime">
            <el-date-picker
              v-model="infoData.startTime"
              style="width: 100%"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择合约开始时间"
              :disabled="editType === 'info'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="合约截止时间：" prop="endTime">
            <el-date-picker
              v-model="infoData.endTime"
              style="width: 100%"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择合约截止时间"
              :disabled="editType === 'info'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item>
            <el-input
              v-if="serviceType === 'buy'"
              v-model="infoData.buyRemark"
              type="textarea"
              placeholder="请输入内容"
              style="width: 100%"
              :disabled="editType === 'info'"
              maxlength="500"
              show-word-limit
              :rows="8"
            />
            <el-input
              v-else
              v-model="infoData.serveRemark"
              type="textarea"
              placeholder="请输入内容"
              style="width: 100%"
              :disabled="editType === 'info'"
              maxlength="500"
              show-word-limit
              :rows="8"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "SustainService",
  components: {},
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    editType: {
      type: String,
      default: "add",
    },
    serviceType: {
      type: String,
      default: "OTHER",
    },
  },
  data() {
    return {
      dialogVisible: false,
      formRules: {
        packageAmount: [
          {
            required: true,
            message: "请输入合约金额",
            trigger: "blur",
          },
        ],
        startTime: [
          {
            required: true,
            message: "请选择合约开始时间",
            trigger: "blur",
          },
        ],
        endTime: [
          {
            required: true,
            message: "请选择合约截止时间",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (value) {
                if (new Date(value) < new Date(this.infoData.startTime)) {
                  callback(new Error("合约截止时间不能小于合约开始时间"));
                } else {
                  callback();
                }
              } else {
                callback(new Error("请选择合约截止时间"));
              }
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  computed: {
    infoData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  created() {
    const defaultValues = {
      packageAmount: 0,
      startTime: "", // 合约开始日期
      endTime: "", // 合约截止日期
    };
    this.$emit("input", { ...defaultValues, ...this.value });
  },
  methods: {},
};
</script>

<style scoped lang="scss"></style>
