<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-06-07 10:28:37
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-13 10:16:57
 * @Description: 其它开支/收入明细
 -->

<template>
  <div>
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="75%"
      :no-footer="true"
      @cancel="closeDrawer">
      <ProTable
        ref="ProTable"
        :query-param="queryParam"
        :local-pagination="localPagination"
        height="65vh"
        :columns="columns"
        :data="tableData"
        @loadData="loadData">
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button icon="el-icon-view" @click="handleEdit(row)">
              查看
            </el-button>
            <!--<el-button-->
            <!--  type="danger"-->
            <!--  icon="el-icon-delete"-->
            <!--  @click="handleDelete(row)"-->
            <!--&gt;-->
            <!--  删除-->
            <!--</el-button>-->
          </div>
        </template>
      </ProTable>
    </ProDrawer>
    <ProDialog
      :value="dialogVisible"
      title="开单登记明细"
      width="80%"
      :no-footer="true"
      top="2%"
      @cancel="closeDialog">
      <ProTable
        ref="DetailTable"
        :query-param="detailQueryParam"
        :local-pagination="detailLocalPagination"
        :height="350"
        :columns="detailColumns"
        :data="detailTableData"
        @loadData="loadDetailData">
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button icon="el-icon-edit" @click="handleEditDetail(row)">
              编辑
            </el-button>
            <el-button
              type="danger"
              icon="el-icon-delete"
              @click="handleDeleteDetail(row)">
              删除
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDialog>
    <ProDialog
      :value="editDialogVisible"
      title="编辑开单登记内容"
      width="60%"
      confirm-text="确认修改"
      :confirm-btn-loading="editDialogLoading"
      @ok="handleDialogEdit"
      @cancel="editDialogVisible = false">
      <AddOtherForm ref="addOtherForm" v-model="editForm" :type="type" />
    </ProDialog>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { filterParam, filterParamRange } from "@/utils";
import {
  closeMachineSaleOrderApi,
  otherIncomeDeleteApi,
  otherIncomeDeleteOneApi,
  otherIncomeDetailApi,
  otherIncomeDetailOneApi,
  otherIncomeEditApi,
} from "@/api/operator";
import AddOtherForm from "@/views/financing/components/addOtherForm.vue";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "OtherAccountDetail",
  components: { AddOtherForm },
  props: {
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      infoData: {},
      drawerVisible: false,
      drawerTitle: "明细",
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "开单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          searchSpan: 6,
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isTable: true,
        },
        {
          dataIndex: "actualAmount",
          title: this.type === "revenue" ? "实收总金额" : "支出总金额",
          isTable: true,
        },
        {
          dataIndex: "placeOrderName",
          title: "开单人",
          isTable: true,
        },
        {
          dataIndex: "username",
          title: "开单人",
          isSearch: true,
          valueType: "input",
          searchSpan: 6,
        },
        {
          dataIndex: "placeOrder",
          title: "开单时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          searchSpan: 6,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          // isSearch: true,
          // valueType: "date-picker",
          // pickerType: "daterange",
          // pickerFormat: "yyyy-MM-dd",
          // valueFormat: "yyyy-MM-dd",
          // searchSpan: 6,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 100,
        },
      ],
      tableData: [],
      // 明细
      dialogVisible: false,
      detailQueryParam: {},
      detailLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      detailColumns: [
        // {
        //   dataIndex: "type",
        //   title: this.type === "revenue" ? "收入类型" : "支出类型",
        //   isTable: true,
        //   formatter: (row) => row.type?.label,
        //   isSearch: true,
        //   valueType: "select",
        //   optionMth: () => dictTreeByCodeApi(7100),
        //   option: [],
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        {
          dataIndex: "customerName",
          title: "客户名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 110,
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 110,
        },
        {
          dataIndex: "articleName",
          title: "品名",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isTable: true,
        },
        {
          dataIndex: "actualAmount",
          title: this.type === "revenue" ? "实收金额" : "支出金额",
          isTable: true,
        },
        {
          dataIndex: "remark",
          title: "摘要说明",
          isTable: true,
          width: 200,
        },
        {
          dataIndex: "updatedBy",
          title: "编辑人",
          isTable: true,
          formatter: (row) => row.updatedBy?.name,
          width: 80,
        },
        {
          dataIndex: "placeOrder",
          title: "开单时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 160,
        },
      ],
      detailTableData: [],
      // 编辑开单内容
      editDialogVisible: false,
      editForm: {},
      editDialogLoading: false,
      isDel: false,
    };
  },
  methods: {
    show(row) {
      this.infoData = cloneDeep(row);
      this.drawerTitle = `${row.monthly} - 开单明细`;
      this.drawerVisible = true;
      this.queryParam = {};
      this.$nextTick(() => {
        this.refresh();
      });
    },
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startCreateTime: null,
          endCreateTime: null,
          data: parameter.placeOrder,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.placeOrder;
      requestParameters.id = this.infoData.id;
      requestParameters.monthly = this.infoData.monthly;
      requestParameters.businessType = this.type === "revenue" ? 1 : 2;
      otherIncomeDetailApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    loadDetailData(parameter) {
      this.detailQueryParam = filterParam(
        Object.assign({}, this.detailQueryParam, parameter)
      );
      const requestParameters = cloneDeep(this.detailQueryParam);
      requestParameters.businessType = this.type === "revenue" ? "1" : "2";
      requestParameters.monthly = this.infoData.monthly;
      otherIncomeDetailOneApi(requestParameters)
        .then((res) => {
          this.detailTableData = res.data.rows;
          this.detailLocalPagination.total = +res.data.total;
          if (this.isDel && this.detailTableData.length === 0) {
            this.closeDialog();
          }
        })
        .finally(() => {
          this.$refs.DetailTable &&
            (this.$refs.DetailTable.listLoading = false);
          this.isDel = false;
        });
    },
    handleEdit(row) {
      this.dialogVisible = true;
      this.detailQueryParam = {};
      this.detailQueryParam.code = row.code;
      this.$nextTick(() => {
        this.$refs.DetailTable.refresh();
      });
    },
    //
    handleEditDetail(row) {
      this.editForm = cloneDeep(row);
      this.editForm.type = this.editForm.type.value;
      this.editDialogVisible = true;
    },
    handleDialogEdit() {
      this.$refs.addOtherForm.$refs.editForm.validate((valid) => {
        if (valid) {
          this.$confirm("是否确认编辑该条开单数据？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.editDialogLoading = true;
            const result = cloneDeep(this.editForm);
            delete result.createdBy;
            delete result.createdAt;
            delete result.updatedBy;
            delete result.updatedAt;
            otherIncomeEditApi([{ ...result }])
              .then(() => {
                this.$message.success("操作成功");
                this.editForm = {};
                this.editDialogVisible = false;
                this.$refs.DetailTable.refresh();
              })
              .finally(() => {
                this.editDialogLoading = false;
              });
          });
        }
      });
    },
    handleDeleteDetail(row) {
      this.$confirm("此操作将删除该条开单内容，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        closeMachineSaleOrderApi(row.id).then((res) => {
          this.$message.success("删除成功");
          this.isDel = true;
          this.$refs.DetailTable.refresh();
          this.refresh();
        });
      });
    },
    handleDelete(row) {
      this.$confirm("此操作将删除该条开单记录，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        otherIncomeDeleteApi({ id: row.id, type: this.type }).then((res) => {
          this.$message.success("删除成功");
          this.refresh();
        });
      });
    },
    closeDrawer() {
      this.drawerVisible = false;
      this.$emit("refresh");
    },
    closeDialog() {
      this.dialogVisible = false;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
