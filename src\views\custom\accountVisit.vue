<template>
  <div class="accountVisit">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
    </ProTable>
  </div>
</template>

<script>
import { selfRepairApi } from "@/api/repair";

import { cloneDeep } from "lodash";

export default {
  name: "AccountVisit",
  data() {
    return {
      columns: [
        {
          dataIndex: "test",
          title: "手机号码",
          isTable: true,
          isSearch: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
          // option: [],
        },
        {
          dataIndex: "test1",
          title: "访问日期",
          isTable: true,
          isSearch: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
          // option: [],
        },
        {
          dataIndex: "test2",
          title: "访问页面",
          isTable: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
          // option: [],
        },
        {
          dataIndex: "test3",
          title: "开始时间",
          isTable: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
          // option: [],
        },
        {
          dataIndex: "test4",
          title: "结束时间",
          isTable: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
          // option: [],
        },
        {
          dataIndex: "test4",
          title: "创建时间",
          isTable: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
          // option: [],
        },
      ],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      tableData: [],
      queryParam: {},
      form: {},
    };
  },
  mounted() {
    // this.$refs.ProTable.refresh();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      selfRepairApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;

          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化 表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
  },
};
</script>

<style></style>
