/**
 * 错误处理工具函数
 */

export interface ApiError {
  code?: number;
  message: string;
  details?: any;
}

/**
 * 处理API错误
 */
export function handleApiError(error: any): ApiError {
  // 如果是网络错误
  if (!error.response) {
    return {
      code: 0,
      message: '网络连接失败，请检查网络设置',
      details: error
    };
  }

  // 如果是HTTP错误
  const { status, data } = error.response;
  
  switch (status) {
    case 400:
      return {
        code: 400,
        message: data?.message || '请求参数错误',
        details: data
      };
    case 401:
      return {
        code: 401,
        message: '未授权访问，请重新登录',
        details: data
      };
    case 403:
      return {
        code: 403,
        message: '权限不足，无法访问',
        details: data
      };
    case 404:
      return {
        code: 404,
        message: '请求的资源不存在',
        details: data
      };
    case 500:
      return {
        code: 500,
        message: '服务器内部错误，请稍后重试',
        details: data
      };
    default:
      return {
        code: status,
        message: data?.message || '请求失败，请稍后重试',
        details: data
      };
  }
}

/**
 * 记录错误日志
 */
export function logError(error: any, context?: string) {
  const timestamp = new Date().toISOString();
  const errorInfo = {
    timestamp,
    context,
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name
    }
  };
  
  console.error('[Error Log]', errorInfo);
  
  // 在生产环境中，可以将错误发送到日志服务
  if (process.env.NODE_ENV === 'production') {
    // TODO: 发送到错误监控服务
    // sendToErrorService(errorInfo);
  }
}

/**
 * 创建用户友好的错误消息
 */
export function createUserFriendlyMessage(error: any): string {
  const apiError = handleApiError(error);
  
  // 根据错误类型返回用户友好的消息
  switch (apiError.code) {
    case 0:
      return '网络连接失败，请检查网络后重试';
    case 400:
      return '输入信息有误，请检查后重试';
    case 401:
      return '登录已过期，请重新登录';
    case 403:
      return '权限不足，无法执行此操作';
    case 404:
      return '请求的内容不存在';
    case 500:
      return '服务暂时不可用，请稍后重试';
    default:
      return apiError.message || '操作失败，请稍后重试';
  }
}

/**
 * 重试函数
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any;

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      logError(error, `Retry attempt ${i + 1}/${maxRetries}`);

      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
  }

  throw lastError;
}

/**
 * 从错误对象中提取错误消息
 */
export function extractErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  return '未知错误';
}
