<!--
 * @Author: wskg
 * @Date: 2024-08-15 10:41:44
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 13:57:02
 * @Description: 采购明细
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <!--<template #fullIdPath>-->
      <!--  <el-cascader-->
      <!--    ref="ProductIds"-->
      <!--    v-model="productIdName"-->
      <!--    filterable-->
      <!--    clearable-->
      <!--    :options="options"-->
      <!--    style="width: 400px"-->
      <!--    :props="{-->
      <!--      label: 'name',-->
      <!--      value: 'fullIdPath',-->
      <!--      children: 'children',-->
      <!--      expandTrigger: 'click',-->
      <!--      multiple: true,-->
      <!--    }"-->
      <!--    leaf-only-->
      <!--    collapse-tags-->
      <!--    @change="handleSelect"-->
      <!--  ></el-cascader>-->
      <!--</template>-->
      <!-- 物品大小类 -->
      <template #goodsType>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.type"
          filterable
          :options="goodsTypeOptions"
          style="width: 100%"
          :props="{
            label: 'label',
            value: 'value',
            children: 'children',
            expandTrigger: 'click',
          }"
          clearable
          leaf-only
          @change="handleChangeType"
        ></el-cascader>
      </template>
      <template #machine="slotProps">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in slotProps.row?.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-button slot="reference" type="text" size="mini">
            适用机型
          </el-button>
        </el-popover>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { productAllApi } from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";
import { pagePurchaseDetailApi } from "@/api/procure";

export default {
  name: "ProcureDetail",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          dataIndex: "manufacturerOrderCode",
          title: "供应商订单编号",
          isTable: true,
          minWidth: 160,
        },
        {
          dataIndex: "manufacturerOrderCode",
          title: "订单编号",
          placeholder: "供应商订单编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 200,
        },
        {
          dataIndex: "type",
          title: "物品大小类",
          isSearch: true,
          searchSlot: "goodsType",
        },

        {
          dataIndex: "machine",
          title: "适用机型",
          isTable: true,
          tableSlot: "machine",
          width: 100,
        },
        {
          dataIndex: "productIds",
          title: "适用机型",
          isSearch: true,
          clearable: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
        {
          dataIndex: "type",
          title: "物品大小类",
          isTable: true,
          formatter: (row) => row.type?.label,
          minWidth: 100,
        },

        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.manufacturerChannel?.label,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "采购数量",
          isTable: true,
        },
        {
          dataIndex: "refundNum",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "receiveNum",
          title: "实收数量",
          isTable: true,
        },
        // {
        //   dataIndex: "amount",
        //   title: "采购金额",
        //   isTable: true,
        // },
        {
          dataIndex: "refundAmount",
          title: "退货金额",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "实付金额",
          isTable: true,
        },
      ],
      tableData: [],
      details: {},
      productIdName: "",
      options: [],
      goodsTypeOptions: [],
    };
  },
  mounted() {
    this.refresh();
    this.init();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          returnTimeStart: null,
          returnTimeEnd: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      pagePurchaseDetailApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleChangeType(val) {
      this.$set(this.queryParam, "type", val[val.length - 1]);
    },
    init() {
      // productAllApi().then((res) => {
      //   this.options = res.data;
      // });
      dictTreeByCodeApi(2100).then((res) => {
        this.goodsTypeOptions = res.data;
      });
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
    refresh() {
      // this.$refs.ProTable.listLoading = false;
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.statistics {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex: 1;
  gap: 20px;
  margin-right: 20px;
  div {
    text-wrap: nowrap;
    font-weight: bold;
    font-size: 16px;
    color: red;
    text-align: left;
  }
}
</style>
