<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-16 09:45:07
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-17 21:31:32
 * @Description: 月度统计
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-rule
      :columns="columns"
      :data="tableData"
      @loadData="loadData">
      <template #rule>
        <div class="rules-tips">
          <h3 class="rule-title">工程师月度统计规则（按工单下单日期）</h3>

          <div class="rule-item">
            <div class="rule-condition">
              <span class="rule-number">统计周期：</span>
              <span class="rule-text">
                以工单
                <span class="highlight">下单日期</span>
                为准，例如统计 1 月数据，即 1月1日 00:00:00 至 1月31日 23:59:59
              </span>
            </div>
          </div>

          <ol>
            <li>
              <div class="rule-item">
                <div class="rule-condition">
                  <span class="rule-number">
                    工程师工单状态定义（共9种状态）：
                  </span>
                  <div class="rule-text">
                    <span class="warning">待接单</span>
                    、
                    <span class="warning">工程师接单</span>
                    、
                    <span class="warning">工程师出发</span>
                    、
                    <span class="warning">工程师到达</span>
                    、
                    <span class="warning">待确认维修报告</span>
                    、
                    <span class="warning">已完成</span>
                    、
                    <span class="warning">待结算</span>
                    、
                    <span class="warning">待审核</span>
                    、
                    <span class="warning">关闭</span>
                  </div>
                </div>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <div class="rule-condition">
                  <span class="rule-number">工程师工单数据统计：</span>
                </div>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  总报修次数：
                  <span class="highlight">所有状态</span>
                  的工单数量
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  总接单次数：
                  <span class="warning">工程师接单</span>
                  、
                  <span class="warning">工程师出发</span>
                  、
                  <span class="warning">工程师到达</span>
                  、
                  <span class="warning">待确认维修报告</span>
                  、
                  <span class="warning">已完成</span>
                  、
                  <span class="warning">待结算</span>
                  、
                  <span class="warning">待审核</span>
                  状态的工单数量
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  总提交报告次数：
                  <span class="warning">待确认维修报告</span>
                  、
                  <span class="warning">已完成</span>
                  、
                  <span class="warning">待结算</span>
                  、
                  <span class="warning">待审核</span>
                  状态的工单数量
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  总确认次数：
                  <span class="warning">已完成</span>
                  、
                  <span class="warning">待结算</span>
                  、
                  <span class="warning">待审核</span>
                  状态的工单数量
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">收入与印量统计：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  抄表总收入： 所有
                  <span class="highlight">抄表实收</span>
                  合计（数据来源：
                  <span class="warning">机器毛利</span>
                  页面）
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  黑白印量： 所有设备的
                  <span class="highlight">黑白印量</span>
                  总和（数据来源：
                  <span class="warning">机器毛利</span>
                  页面）
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  彩色印量：所有设备的
                  <span class="highlight">彩色印量</span>
                  总和（数据来源：
                  <span class="warning">机器毛利</span>
                  页面）
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">散客维修统计：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  散客维修次数：
                  <span class="highlight">服务类型为散修</span>
                  的工单中，状态为
                  <span class="warning">待确认维修报告</span>
                  、
                  <span class="warning">已完成</span>
                  、
                  <span class="warning">待结算</span>
                  、
                  <span class="warning">待审核</span>
                  的数量
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  散客维修费用：
                  <span class="highlight">服务类型为散修</span>
                  且状态为
                  <span class="warning">已完成</span>
                  的工单中
                  <span class="highlight">维修诊断费</span>
                  合计
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  维修耗材费：
                  <span class="highlight">服务类型为散修</span>
                  且状态为
                  <span class="warning">已完成</span>
                  的工单中
                  <span class="highlight">耗材费用</span>
                  合计
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  总收入（散修）：
                  <span class="highlight">服务类型为散修</span>
                  且状态为
                  <span class="warning">已完成</span>
                  的工单中
                  <span class="highlight">总费用</span>
                  合计
                </span>
              </div>
            </li>
          </ol>
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 详情 -->
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="80%"
      :no-footer="true"
      @cancel="handleDrawerClose">
      <ProTable
        ref="detailTable"
        :query-param="detailQueryParam"
        :local-pagination="detailLocalPagination"
        :show-setting="false"
        show-rule
        :columns="detailColumns"
        :data="detailTableData"
        :use-infinite-scroll="true"
        :has-more="hasMore"
        @loadData="handleLoadData">
        <template #btn>
          <div
            v-if="statLoading"
            class="title-box-right"
            style="font-size: 14px; gap: 10px">
            <div>接单次数: {{ totalData?.receiveNums || 0 }}</div>
            <div>超时到达次数: {{ totalData?.arriveOutTimeNums || 0 }}</div>
            <div>未确认工单数: {{ totalData?.unconfirmedNums || 0 }}</div>
            <div>抄表总收入: {{ totalData?.actualReceipt || 0 }}</div>
            <div>黑白印量: {{ totalData?.blackNums || 0 }}</div>
            <div>彩色印量: {{ totalData?.colorNums || 0 }}</div>
            <div>总维修次数: {{ totalData?.repairNums || 0 }}</div>
            <div>散客维修次数: {{ totalData?.scatteredNums || 0 }}</div>
            <div>维修诊断费: {{ totalData?.repairPay || 0 }}</div>
            <div>维修耗材费: {{ totalData?.itemPay || 0 }}</div>
            <div>总收入: {{ totalData?.totalPay || 0 }}</div>
          </div>
          <div v-else class="title-box-right" style="gap: 5px">
            <i class="el-icon-loading"></i>
            正在加载统计数据
          </div>
        </template>
        <template #rule>
          <div class="rules-tips">
            <h3 class="rule-title">各项数据统计规则</h3>
            <div class="rule-item">
              <div class="rule-condition">
                <span class="rule-number">接单次数：</span>
                <span class="rule-text">
                  统计周期内
                  <span class="highlight">工程师</span>
                  <span class="warning">有效接单</span>
                  数量，其中
                  <span class="warning">待接单</span>
                  和
                  <span class="warning">关闭</span>
                  状态工单不纳入统计
                </span>
              </div>
            </div>
            <div class="rule-item">
              <div class="rule-condition">
                <span class="rule-number">超时达到次数：</span>
                <span class="rule-text">
                  统计周期内
                  <span class="highlight">工程师</span>
                  从
                  <span class="highlight">接单</span>
                  到
                  <span class="highlight">确认到达</span>
                  超过
                  <span class="warning">4小时</span>
                  的纳入统计
                </span>
              </div>
            </div>
            <div class="rule-item">
              <div class="rule-condition">
                <span class="rule-number">未确认工单数量：</span>
                <span class="rule-text">
                  统计周期内
                  <span class="highlight">工程师</span>
                  提交
                  <span class="highlight">维修报告</span>
                  ，
                  <span class="highlight">客户</span>
                  尚未
                  <span class="warning">确认维修报告</span>
                  的纳入统计
                </span>
              </div>
            </div>
            <div class="rule-item">
              <div class="rule-condition">
                <span class="rule-number">抄表收入：</span>
                <span class="rule-text">
                  统计周期内
                  <span class="highlight">工程师负责客户</span>
                  的
                  <span class="warning">抄表实收总额</span>
                  （数据来源：
                  <span class="warning">机器毛利</span>
                  页面）
                </span>
              </div>
            </div>
            <div class="rule-item">
              <div class="rule-condition">
                <span class="rule-number">抄表黑白印量：</span>
                <span class="rule-text">
                  统计周期内
                  <span class="highlight">工程师负责客户</span>
                  的
                  <span class="warning">抄表黑白印量总和</span>
                  （数据来源：
                  <span class="warning">机器毛利</span>
                  页面）
                </span>
              </div>
            </div>
            <div class="rule-item">
              <div class="rule-condition">
                <span class="rule-number">抄表彩色印量：</span>
                <span class="rule-text">
                  统计周期内
                  <span class="highlight">工程师负责客户</span>
                  的
                  <span class="warning">抄表彩色印量总和</span>
                  （数据来源：
                  <span class="warning">机器毛利</span>
                  页面）
                </span>
              </div>
            </div>
            <div class="rule-item">
              <div class="rule-condition">
                <span class="rule-number">总维修次数：</span>
                <span class="rule-text">
                  统计周期内
                  <span class="highlight">工程师</span>
                  的
                  <span class="warning">有效维修次数</span>
                  ，工单状态为
                  <!--<span class="warning">待确认维修报告</span>、-->
                  <span class="warning">待结算</span>
                  、
                  <span class="warning">待审核</span>
                  、
                  <span class="warning">已完成</span>
                  的纳入统计
                </span>
              </div>
            </div>
            <div class="rule-item">
              <div class="rule-condition">
                <span class="rule-number">散客维修次数：</span>
                <span class="rule-text">
                  统计周期内
                  <span class="highlight">工程师</span>
                  <span class="warning">散修服务</span>
                  的维修次数， 工单状态为
                  <!--<span class="warning">待确认维修报告</span>、-->
                  <span class="warning">待结算</span>
                  、
                  <span class="warning">待审核</span>
                  、
                  <span class="warning">已完成</span>
                  的纳入统计
                </span>
              </div>
            </div>
            <div class="rule-item">
              <div class="rule-condition">
                <span class="rule-number">维修诊断费：</span>
                <span class="rule-text">
                  统计周期内
                  <span class="highlight">工程师</span>
                  <span class="warning">散修服务</span>
                  的
                  <span class="highlight">已完成工单</span>
                  维修收入
                  <span class="highlight">（维修诊断费）</span>
                </span>
              </div>
            </div>
            <div class="rule-item">
              <div class="rule-condition">
                <span class="rule-number">维修耗材费：</span>
                <span class="rule-text">
                  统计周期内
                  <span class="highlight">工程师</span>
                  <span class="warning">散修服务</span>
                  的
                  <span class="highlight">已完成工单</span>
                  耗材收入
                  <span class="highlight">（维修耗材费）</span>
                </span>
              </div>
            </div>
            <div class="rule-item">
              <div class="rule-condition">
                <span class="rule-number">总收入：</span>
                <span class="rule-text">
                  统计周期内
                  <span class="highlight">工程师</span>
                  <span class="warning">散修服务</span>
                  的
                  <span class="highlight">已完成工单</span>
                  总收入
                  <span class="highlight">（ 维修诊断费 + 维修耗材费）</span>
                </span>
              </div>
            </div>
          </div>
        </template>
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button icon="el-icon-view" @click="handleDetail(row)">
              查看
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDrawer>
    <!-- 工程师接单明细 -->
    <ProDialog
      :value="dialogVisible"
      width="60%"
      top="6%"
      :no-footer="true"
      @cancel="handleDialogClose">
      <el-descriptions :title="dialogTitle" :column="3" border>
        <el-descriptions-item label="工程师账号" :span="3">
          {{ formParam?.engineerCode }}
        </el-descriptions-item>
        <!--<el-descriptions-item label="工程师名称">-->
        <!--  {{ formParam?.engineerName }}-->
        <!--</el-descriptions-item>-->
        <el-descriptions-item label="接单次数">
          {{ formParam?.receiveNums }}
        </el-descriptions-item>
        <el-descriptions-item label="出发次数">
          {{ formParam?.departureTimeNum }}
        </el-descriptions-item>
        <el-descriptions-item label="到达次数">
          {{ formParam?.arriveNums }}
        </el-descriptions-item>
        <el-descriptions-item label="超时到达次数">
          {{ formParam?.arriveOutTimeNums }}
        </el-descriptions-item>
        <el-descriptions-item label="提交报告次数">
          {{ formParam?.submitReportNum }}
        </el-descriptions-item>
        <el-descriptions-item label="客户确认次数">
          {{ formParam?.confirmedNum }}
        </el-descriptions-item>
        <el-descriptions-item label="未确认工单次数">
          {{ formParam?.unconfirmedNums }}
        </el-descriptions-item>
        <el-descriptions-item label="平均接单次数">
          {{ formParam?.receiveTime }}
        </el-descriptions-item>
        <el-descriptions-item label="平均路上时长(分钟)">
          {{ formParam?.onRoadTime }}
        </el-descriptions-item>
        <el-descriptions-item label="平均维修时长(分钟)">
          {{ formParam?.repairTime }}
        </el-descriptions-item>
        <el-descriptions-item label="平均报告确认时长(分钟)">
          {{ formParam?.confirmTime }}
        </el-descriptions-item>
        <el-descriptions-item label="抄表收入">
          {{ formParam?.actualReceipt }}
        </el-descriptions-item>
        <el-descriptions-item label="黑白印量">
          {{ formParam?.blackNums }}
        </el-descriptions-item>
        <el-descriptions-item label="彩色印量">
          {{ formParam?.colorNums }}
        </el-descriptions-item>
        <el-descriptions-item label="总维修次数">
          {{ formParam?.repairNums }}
        </el-descriptions-item>
        <el-descriptions-item label="散客维修次数">
          {{ formParam?.scatteredNums }}
        </el-descriptions-item>
        <el-descriptions-item label="维修诊断费">
          {{ formParam?.repairPay }}
        </el-descriptions-item>
        <el-descriptions-item label="维修耗材费">
          {{ formParam?.itemPay }}
        </el-descriptions-item>
        <el-descriptions-item label="总收入">
          {{ formParam?.totalPay }}
        </el-descriptions-item>
      </el-descriptions>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  engineerMonthStatApi,
  engineerMonthStatDetailApi,
  engineerMonthStatSummaryApi,
} from "@/api/repair";

export default {
  name: "EngineerMonSum",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "monthly",
          title: "年月",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          valueFormat: "yyyy-MM",
          pickerFormat: "yyyy-MM",
          width: 80,
        },
        {
          dataIndex: "repairNums",
          title: "总报修次数",
          isTable: true,
        },
        {
          dataIndex: "receiveNums",
          title: "总接单次数",
          isTable: true,
        },
        {
          dataIndex: "submitReportNum",
          title: "总提交报告次数",
          isTable: true,
        },
        {
          dataIndex: "confirmedNum",
          title: "总确认次数",
          isTable: true,
        },
        {
          dataIndex: "actualReceipt",
          title: "抄表总收入",
          isTable: true,
        },
        {
          dataIndex: "blackNums",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorNums",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "scatteredNums",
          title: "散客维修次数",
          isTable: true,
        },
        {
          dataIndex: "repairPay",
          title: "散客维修费用",
          isTable: true,
        },
        {
          dataIndex: "itemPay",
          title: "维修耗材费",
          isTable: true,
        },
        {
          dataIndex: "totalPay",
          title: "总支付",
          isTable: true,
        },

        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 100,
        },
      ],
      tableData: [],
      editType: "info",
      drawerVisible: false,
      drawerTitle: "",
      // 明细数据
      detailQueryParam: {
        monthly: null,
      },
      detailLocalPagination: {
        pageNumber: 1,
        pageSize: 15,
        total: 0,
      },
      detailColumns: [
        {
          dataIndex: "engineerCode",
          title: "工程师账号",
          isTable: true,
          width: 90,
        },
        {
          dataIndex: "engineerName",
          title: "工程师名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          searchSpan: 6,
          width: 90,
        },
        {
          dataIndex: "receiveNums",
          title: "接单次数",
          isTable: true,
          width: 80,
        },
        // {
        //   dataIndex: "overTimeOrderNum",
        //   title: "超时接单次数",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        //   searchSpan: 6,
        // },
        {
          dataIndex: "arriveOutTimeNums",
          title: "超时到达次数",
          isTable: true,
          isSearch: true,
          valueType: "input",
          inputType: "number",
          searchSpan: 6,
        },
        {
          dataIndex: "unconfirmedNums",
          title: "未确认工单数",
          isTable: true,
        },
        {
          dataIndex: "actualReceipt",
          title: "抄表收入",
          isTable: true,
        },
        {
          dataIndex: "blackNums",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorNums",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "repairNums",
          title: "总维修次数",
          isTable: true,
        },
        {
          dataIndex: "scatteredNums",
          title: "散客维修次数",
          isTable: true,
        },
        {
          dataIndex: "repairPay",
          title: "维修诊断费",
          isTable: true,
        },
        {
          dataIndex: "itemPay",
          title: "维修耗材费",
          isTable: true,
        },
        {
          dataIndex: "totalPay",
          title: "总收入",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 100,
        },
      ],
      detailTableData: [],
      hasMore: true,
      statLoading: false,
      totalData: {},
      // 明细
      dialogVisible: false,
      dialogTitle: "接单明细",
      formParam: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startMonthly: null,
          endMonthly: null,
          data: parameter.monthly,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.monthly;
      engineerMonthStatApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.detailQueryParam = {};
      this.editType = type;
      this.drawerTitle = `${row.monthly} - 月度统计详情`;
      this.detailLocalPagination = {
        pageNumber: 1,
        pageSize: 15,
        total: 0,
      };
      this.hasMore = true;
      this.drawerVisible = true;
      this.detailQueryParam.monthly = row.monthly;
      this.$nextTick(() => {
        this.$refs.detailTable.refresh();
      });
    },
    // 加载明细数据
    handleLoadData(parameter) {
      this.detailQueryParam = filterParam(
        Object.assign({}, this.detailQueryParam, parameter)
      );
      const requestParameters = cloneDeep(this.detailQueryParam);
      // requestParameters.monthly = this.monthly;
      engineerMonthStatDetailApi(requestParameters)
        .then((res) => {
          // this.detailTableData = res.data.rows;
          if (parameter.pageNumber === 1) {
            this.detailTableData = res.data.rows;
            this.detailLocalPagination.pageNumber = 1;
          } else {
            this.detailTableData = [...this.detailTableData, ...res.data.rows];
            this.detailLocalPagination.pageNumber = parameter.pageNumber;
          }
          this.detailLocalPagination.total = +res.data.total;
          // 判断是否还有更多数据
          this.hasMore =
            this.detailTableData.length < this.detailLocalPagination.total;
          this.$refs.detailTable && this.$refs.detailTable.resetScrolling();
        })
        .finally(() => {
          this.$refs.detailTable &&
            (this.$refs.detailTable.listLoading = false);
        });
      this.getTotalData(requestParameters);
    },
    handleDetail(row) {
      this.dialogTitle = `${this.monthly} - ${row.engineerName} - 月度统计明细`;
      this.formParam = cloneDeep(row);
      this.dialogVisible = true;
    },
    getTotalData(params) {
      this.statLoading = false;
      engineerMonthStatSummaryApi(params)
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    handleDrawerClose() {
      this.drawerVisible = false;
      this.monthly = null;
      this.formParam = {};
    },
    handleDialogClose() {
      this.formParam = {};
      this.dialogVisible = false;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
