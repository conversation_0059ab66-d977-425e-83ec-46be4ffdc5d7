<template>
  <div class="masked-config-field">
    <!-- 显示模式 -->
    <div 
      v-if="!isEditing" 
      :class="['field-display', className, { 'field-changed': isChanged }]"
      @click="startEdit"
    >
      <span v-if="displayValue" class="masked-value">{{ displayValue }}</span>
      <span v-else class="field-placeholder">{{ placeholder || '点击编辑' }}</span>
      <i v-if="isChanged" class="el-icon-edit-outline field-changed-icon"></i>
      <i class="el-icon-lock security-icon"></i>
    </div>

    <!-- 编辑模式 -->
    <el-dialog
      :title="`编辑${label || '配置'}`"
      :visible.sync="isEditing"
      width="500px"
      :before-close="handleCancel"
      append-to-body
    >
      <div class="edit-form">
        <el-form :model="editForm" :rules="rules" ref="editForm" label-width="80px">
          <el-form-item :label="label" prop="value">
            <el-input
              v-model="editForm.value"
              placeholder="请输入完整的API Key"
              :maxlength="maxLength"
              show-word-limit
              @keydown.native="handleKeyDown"
              ref="inputRef"
            />
          </el-form-item>
          
          <!-- 字段描述 -->
          <div v-if="description" class="field-description">
            {{ description }}
          </div>
          
          <!-- 脱敏提示 -->
          <div class="mask-hint">
            提示：请输入完整的API Key，保存后将脱敏显示为 {{ value ? getMaskedValue(value) : 'ABC***********XYZ' }} 格式
          </div>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving" :disabled="!hasChanges">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'MaskedConfigField',
  props: {
    // 字段值
    value: {
      type: [String, Number, Boolean],
      default: ''
    },
    // 字段标签
    label: {
      type: String,
      default: ''
    },
    // 占位符
    placeholder: {
      type: String,
      default: ''
    },
    // 字段描述
    description: {
      type: String,
      default: ''
    },
    // 是否必填
    required: {
      type: Boolean,
      default: false
    },
    // 最大长度
    maxLength: {
      type: Number,
      default: null
    },
    // 验证模式
    pattern: {
      type: RegExp,
      default: null
    },
    // 验证错误信息
    patternMessage: {
      type: String,
      default: '格式不正确'
    },
    // 自定义样式类
    className: {
      type: String,
      default: ''
    },
    // 是否已更改
    isChanged: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEditing: false,
      saving: false,
      editForm: {
        value: ''
      }
    }
  },
  computed: {
    displayValue() {
      return this.value ? this.getMaskedValue(this.value) : ''
    },
    hasChanges() {
      return this.editForm.value !== this.value && this.editForm.value !== ''
    },
    rules() {
      const rules = []
      
      if (this.required) {
        rules.push({
          required: true,
          message: `请输入${this.label}`,
          trigger: 'blur'
        })
      }
      
      if (this.pattern) {
        rules.push({
          pattern: this.pattern,
          message: this.patternMessage,
          trigger: 'blur'
        })
      }
      
      return { value: rules }
    }
  },
  methods: {
    // 脱敏显示函数
    getMaskedValue(val) {
      if (!val || val.length <= 6) return val
      // 显示前3位和后3位，中间用*替代
      const start = val.substring(0, 3)
      const end = val.substring(val.length - 3)
      const middle = '*'.repeat(Math.min(12, val.length - 6))
      return `${start}${middle}${end}`
    },
    startEdit() {
      // 对于脱敏字段，不直接设置原值，而是设置空字符串让用户重新输入
      this.editForm.value = ''
      this.isEditing = true
      // 自动聚焦到输入框
      this.$nextTick(() => {
        if (this.$refs.inputRef) {
          this.$refs.inputRef.focus()
        }
      })
    },
    handleCancel() {
      this.isEditing = false
      this.editForm.value = ''
    },
    async handleSave() {
      try {
        await this.$refs.editForm.validate()
        this.saving = true
        
        // 触发值更改事件
        this.$emit('change', this.editForm.value.trim())
        
        // 模拟保存延迟
        await new Promise(resolve => setTimeout(resolve, 300))
        
        this.isEditing = false
        this.$message.success(`${this.label}已更新`)
      } catch (error) {
        console.error('验证失败:', error)
      } finally {
        this.saving = false
      }
    },
    // 处理快捷键
    handleKeyDown(e) {
      if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault()
        this.handleSave()
      } else if (e.key === 'Escape') {
        e.preventDefault()
        this.handleCancel()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.masked-config-field {
  .field-display {
    position: relative;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    min-height: 20px;
    display: inline-flex;
    align-items: center;
    
    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
      border: 1px dashed #409eff;
    }
    
    &.field-changed {
      background-color: rgba(245, 166, 35, 0.1);
      border: 1px solid #f5a623;
      
      .field-changed-icon {
        margin-left: 4px;
        color: #f5a623;
        font-size: 12px;
      }
    }
    
    .masked-value {
      font-family: monospace;
      font-size: 14px;
      letter-spacing: 1px;
    }
    
    .field-placeholder {
      color: #c0c4cc;
      font-style: italic;
    }
    
    .security-icon {
      margin-left: 8px;
      color: #67c23a;
      font-size: 12px;
    }
  }
  
  .edit-form {
    .field-description {
      margin-top: 8px;
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
    }
    
    .mask-hint {
      margin-top: 8px;
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
      background: #f5f7fa;
      padding: 8px;
      border-radius: 4px;
    }
  }
  
  .dialog-footer {
    text-align: right;
  }
}
</style>
