<!--
 * @Author: wskg
 * @Date: 2024-08-14 15:18:21
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-01 11:52:21
 * @Description: 供应商 - 退货单
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
          <el-button
            v-if="row.refundStatus?.value === 'WAIT_CONFIRM'"
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'edit')"
          >
            确认退货
          </el-button>
          <el-button
            v-if="row.refundStatus?.value === 'WAIT_RECEIVE'"
            size="mini"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'audit')"
          >
            确认收货
          </el-button>
          <el-button
            v-if="
              row.refundStatus?.value === 'WAIT_REFUND' ||
              row.refundStatus?.value === 'SUCCESS'
            "
            size="mini"
            icon="el-icon-discount"
            @click="returnMoney(row)"
          >
            {{ row.refundStatus?.value === "SUCCESS" ? "退款详情" : "退款" }}
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="providerDrawer"
      :title="drawerTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :method-type="methodType"
      :no-footer="methodType === 'info'"
      :no-confirm-footer="true"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '110px' }"
        :open-type="methodType"
      >
        <template #refundType>
          {{ form.refundType?.label }}
        </template>
        <template #refundStatus>
          {{ form.refundStatus?.label }}
        </template>
        <template #orderDetails>
          <div class="title-box" style="margin: 0">退货清单</div>
          <ProTable
            ref="itemsProTable"
            :row-key="(row) => row.id"
            :columns="itemsColumns"
            :data="itemsTableData"
            :local-pagination="itemsLocalPagination"
            :height="300"
            :show-setting="false"
            :show-search="false"
            :show-loading="false"
            sticky
          >
            <template #applicableModels="{ row }">
              <el-popover
                placement="bottom"
                title=""
                width="700"
                trigger="click"
              >
                <div style="margin: 20px; height: 400px; overflow-y: scroll">
                  <el-descriptions
                    class="margin-top"
                    title="适用机型"
                    :column="1"
                    border
                  >
                    <el-descriptions-item
                      v-for="item in row.productTreeDtoList"
                      :key="item.id"
                    >
                      <template slot="label">品牌/系列/机型</template>
                      {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <el-button slot="reference" size="mini">适用机型</el-button>
              </el-popover>
            </template>
          </ProTable>
        </template>
      </ProForm>
      <!-- 物流信息 -->
      <div class="m-t-8">
        <p class="title-p m-b-12">物流信息</p>
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in form.returnRecords"
            :key="index"
            :timestamp="activity.createdAt"
          >
            {{ activity.title }}{{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </div>
      <template #footer>
        <el-button
          v-if="form.refundStatus?.value === 'WAIT_CONFIRM'"
          type="danger"
          @click="confirmReturnOrder('CLOSED')"
        >
          驳回
        </el-button>
        <el-button
          v-if="form.refundStatus?.value === 'WAIT_CONFIRM'"
          type="primary"
          @click="confirmReturnOrder()"
        >
          确认退货单
        </el-button>
        <!-- form.refundStatus?.value === 'WAIT_RETURN' || -->
        <el-button
          v-if="form.refundStatus?.value === 'WAIT_RECEIVE'"
          type="primary"
          @click="confirmReceive"
        >
          确认收货
        </el-button>

        <el-button @click="closeDrawer">取消</el-button>
      </template>
    </ProDrawer>
    <!--  退款  -->
    <Drawback ref="drawback" @refresh="refresh" />
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getReturnDetailApi,
  providerConfirmReceiveApi,
  providerConfirmReturnApi,
  supplierReturnListApi,
} from "@/api/manufacturer";
import { Message } from "element-ui";
import Drawback from "./components/drawback.vue";
export default {
  name: "ProviderReturn",
  components: { Drawback },
  data() {
    return {
      methodType: "add",
      queryParam: {},
      columns: [
        {
          dataIndex: "code",
          title: "退货单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "manufacturerOrderCode",
          title: "关联订单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "receiveCompany",
          title: "公司",
          isTable: true,
          isSearch: false,
          valueType: "input",
          minWidth: 160,
        },
        // {
        //   dataIndex: "phone",
        //   title: "下单手机号",
        //   isTable: true,
        // },
        {
          dataIndex: "refundType",
          title: "退款类型",
          isTable: true,
          formatter: (row) => row.refundType?.label,
          // isSearch: true,
          // valueType: "select",
          // option: [
          //   {
          //     label: "现金退款",
          //     value: "CASH",
          //   },
          //   {
          //     label: "冲抵货款",
          //     value: "GOODS",
          //   },
          // ],
        },
        {
          dataIndex: "refundStatus",
          title: "退货状态",
          isTable: true,
          formatter: (row) => row.refundStatus?.label,
          isSearch: true,
          clearable: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "待确认",
              value: "WAIT_CONFIRM",
            },
            {
              label: "退货中",
              value: "WAIT_RETURN",
            },
            {
              label: "待收货",
              value: "WAIT_RECEIVE",
            },
            {
              label: "退款中",
              value: "WAIT_REFUND",
            },
            {
              label: "部分退款",
              value: "PART_REFUND",
            },

            {
              label: "完成退款",
              value: "SUCCESS",
            },
            {
              label: "关闭",
              value: "CLOSED",
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "status",
        //   title: "审核状态",
        //   isTable: true,
        //   formatter: (row) => row.status?.label,
        //   isSearch: true,
        //   clearable: true,
        //   valueType: "select",
        //   option: [
        //     {
        //       label: "待确认",
        //       value: 0,
        //     },
        //     {
        //       label: "已确认",
        //       value: 1,
        //     },
        //   ],
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        {
          dataIndex: "createdBy",
          title: "退货人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          formatter: (row) => row.createdBy?.name,
        },
        {
          dataIndex: "initiatorPhone",
          title: "退货人手机号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "createdAt",
          title: "退货时间",
          isTable: true,
          width: 150,
          // isSearch: true,
          // valueType: "date-picker",
          // pickerType: "datetimerange",
          // pickerFormat: "yyyy-MM-dd HH:mm:ss",
          // valueFormat: "yyyy-MM-dd HH:mm:ss",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 280,
          tableSlot: "actions",
        },
      ],
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      providerDrawer: false,
      drawerTitle: "查看 - ",
      confirmLoading: false,
      formLoading: false,
      form: {},
      formColumns: [
        // {
        //   dataIndex: "code",
        //   title: "付款单号",
        //   isForm: true,
        //   formSpan: 24,
        //   valueType: "text",
        // },
        // {
        //   dataIndex: "refundStatus",
        //   title: "退货状态",
        //   isForm: true,
        //   formSpan: 6,
        //   valueType: "text",
        // },
        {
          dataIndex: "manufacturerOrderCode",
          title: "关联订单编号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "orderAmount",
          title: "订单金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "payAmount",
          title: "支付金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "createdAt",
          title: "申请时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },

        {
          dataIndex: "refundType",
          title: "退款类型",
          isForm: true,
          formSpan: 6,
          formSlot: "refundType",
        },
        {
          dataIndex: "refundStatus",
          title: "退货状态",
          isForm: true,
          formSpan: 6,
          formSlot: "refundStatus",
        },
        {
          dataIndex: "amount",
          title: "申请退款金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "reason",
          title: "申请原因",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "account",
          title: "公司",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },

        {
          dataIndex: "initiatorName",
          title: "下单人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorPhone",
          title: "下单手机号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorTime",
          title: "下单时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "trackNumber",
          title: "关联物流单号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        // {
        //   dataIndex: "provinceName",
        //   title: "省",
        //   isForm: true,
        //   formSpan: 6,
        //   valueType: "text",
        // },
        // {
        //   dataIndex: "cityName",
        //   title: "市",
        //   isForm: true,
        //   formSpan: 6,
        //   valueType: "text",
        // },
        // {
        //   dataIndex: "countyName",
        //   title: "区",
        //   isForm: true,
        //   formSpan: 6,
        //   valueType: "text",
        // },
        {
          dataIndex: "orderAddress",
          title: "发货地址",
          isForm: true,
          formSpan: 18,
          valueType: "text",
        },
        {
          dataIndex: "account",
          title: "账户名",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入账户名",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "bank",
          title: "开户银行",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入开户银行",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "bankClient",
          title: "开户网点",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入开户网点",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "bankAccount",
          title: "银行账号",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入银行账号",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "consignee",
          title: "收货人",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入收货人",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "consigneePhone",
          title: "收货人电话",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入收货人电话",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "consigneeAddress",
          title: "收货地址",
          isForm: true,
          formSpan: 16,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入收货地址",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "orderDetails",
          title: "物品清单",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "orderDetails",
        },
      ],
      itemsColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
        // {
        //   dataIndex: "approveNum",
        //   title: "发货数量",
        //   isTable: true,
        // },
        {
          dataIndex: "number",
          title: "退货数量",
          isTable: true,
        },
        // {
        //   dataIndex: "amount",
        //   title: "订单金额",
        //   isTable: true,
        // },
        {
          dataIndex: "amount",
          title: "退货金额",
          isTable: true,
        },
      ],
      itemsTableData: [],
      itemsLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      supplierReturnListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.form = {};
      this.methodType = type;
      this.drawerTitle =
        type === "info" ? `查看 - ${row.code}` : `确认退货 - ${row.code}`;
      getReturnDetailApi(row.id).then((res) => {
        const data = res.data;
        this.form = {
          ...data,
          // refundStatus: data.refundStatus?.label,
        };
        this.itemsTableData = res.data.manufacturerReturnGoods;
      });
      this.providerDrawer = true;
    },
    // 退货单确认
    confirmReturnOrder(status) {
      const confirmAction = (successMessage) => {
        const params = {
          ...this.form,
          refundStatus: status !== undefined ? status : this.form.refundStatus,
          manufacturerReturnGoods: this.itemsTableData,
        };
        providerConfirmReturnApi(params).then((res) => {
          Message.success(successMessage);
          this.refresh();
          this.closeDrawer();
        });
      };
      if (status) {
        confirmAction("驳回成功");
      } else {
        this.$refs.ProForm.handleSubmit().then(() => {
          this.$confirm(`是否确认该退货单`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            confirmAction("确认成功");
          });
        });
      }
    },
    // confirmReturnOrder(status) {
    //   if (status) {
    //     const params = {
    //       ...this.form,
    //       refundStatus: status,
    //     };
    //     providerConfirmReturnApi(params).then((res) => {
    //       Message.success("驳回成功");
    //       this.closeDrawer();
    //     });
    //     return;
    //   }
    //   this.$refs.ProForm.handleSubmit().then((res) => {
    //     this.$confirm(`是否${status ? "驳回" : "确认"}该退货单`, "提示", {
    //       confirmButtonText: "确定",
    //       cancelButtonText: "取消",
    //       type: "warning",
    //     }).then(() => {
    //       const params = {
    //         ...this.form,
    //         manufacturerReturnGoods: this.itemsTableData,
    //       };
    //       providerConfirmReturnApi(params).then((res) => {
    //         Message.success("确认成功");
    //         this.closeDrawer();
    //       });
    //     });
    //   });
    // },
    // 确认收货
    confirmReceive() {
      providerConfirmReceiveApi(this.form.id).then((res) => {
        Message.success("确认收货成功");
        this.refresh();
        this.closeDrawer();
      });
    },
    // 退款
    returnMoney(row) {
      const type = row.refundStatus?.value === "SUCCESS" ? "info" : "edit";
      this.$refs.drawback.show(row, type);
    },
    refresh() {
      this.$refs.ProTable.refresh();
      // this.$refs.ProTable.listLoading = false;
    },
    closeDrawer() {
      this.form = {};
      this.providerDrawer = false;
    },
  },
};
</script>

<style scoped lang="scss">
.title-p {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
</style>
