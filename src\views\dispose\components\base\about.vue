<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-15 14:16:26
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-30 18:02:33
 * @FilePath: src/views/dispose/components/about.vue
 * @Description: 平台介绍
 * 
-->

<template>
  <div class="app-container">
    <div class="edit">
      <WangeEditor ref="WangeEditorRef" :content="introduce" :height="600" />
    </div>
    <div>
      <el-button
        type="primary"
        size="small"
        style="margin-top: 20px"
        @click="handleSubmit"
      >
        保存平台介绍
      </el-button>
    </div>
  </div>
</template>

<script>
import WangeEditor from "@/components/ProWangeEditor/index.vue";
import { setBaseCompanyInfoApi } from "@/api";
export default {
  name: "About",
  components: {
    WangeEditor,
  },
  props: {
    introduce: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      content: "",
      platformIntroduce: "",
    };
  },
  watch: {
    introduce: {
      handler(val) {
        this.$refs.WangeEditorRef?.echo(val);
      },
    },
  },
  methods: {
    handleSubmit() {
      const result = this.$refs.WangeEditorRef.getContent();
      if (!this.id) {
        this.$message.error("请先保存收款配置信息");
        return;
      }
      this.$emit("save", { platformIntroduce: result });
      // setBaseCompanyInfoApi({ id: this.id, platformIntroduce: result }).then(
      //   (res) => {
      //     console.log(res);
      //     this.$message.success("配置保存成功");
      //   }
      // );
    },
  },
};
</script>

<style scoped lang="scss">
.edit {
  text-align: left;
}
</style>
