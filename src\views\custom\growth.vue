<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <!--      <template #btn>-->
      <!--        <el-button type="success" icon="el-icon-refresh" size="mini"-->
      <!--          >重新计算</el-button-->
      <!--        >-->
      <!--      </template>-->
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { getCustomerGrowthByPageApi } from "@/api/customer";

export default {
  name: "Growth",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "year",
          title: "年",
          isTable: true,
        },
        {
          dataIndex: "month",
          title: "月",
          isTable: true,
        },
        {
          dataIndex: "addNum",
          title: "当月新增客户数",
          isTable: true,
        },
        {
          dataIndex: "currNum",
          title: "当月客户数",
          isTable: true,
        },
        {
          dataIndex: "visitNum",
          title: "当月访问数",
          isTable: true,
        },
        {
          dataIndex: "searchNum",
          title: "当月搜索数",
          isTable: true,
        },
      ],
      tableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      getCustomerGrowthByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data;
          this.localPagination.total = Number(res.data.length);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    refresh() {
      this.$refs.ProTable.listLoading = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
