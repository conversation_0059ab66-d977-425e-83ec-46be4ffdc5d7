<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-25 13:58:21
 * @Description: 耗材销售单
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <div class="box">
          <div class="left">
            <el-button
              type="success"
              class="add-btn"
              size="mini"
              icon="el-icon-plus"
              @click="createdOreder()"
            >
              创建订单
            </el-button>
          </div>
          <div class="title-box-right">
            <div>总订单数量：{{ totalData?.orderNum || 0 }}</div>
            <div>总商品金额：{{ totalData?.itemAmount || 0 }}</div>
            <div>总配送费：{{ totalData?.shippingFeeAmount || 0 }}</div>
            <div>总订单金额：{{ totalData?.totalAmount || 0 }}</div>
          </div>
        </div>
      </template>
      <template #type="slotProps">
        {{ slotProps.row.type.label }}
      </template>
      <!-- <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"></el-cascader>
      </template> -->
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleInfo(row, 'info')"
          >
            查看
          </el-button>
          <!-- <el-button icon="el-icon-edit" @click="handleInfo(row, 'edit')">
            编辑
          </el-button> -->
          <el-button icon="el-icon-printer" @click="printTicket(row)">
            打印出货单
          </el-button>
          <el-button
            v-if="
              (row.orderType.value === 'APPLY' ||
                (row.orderType.value === 'SALE' &&
                  row.payMode.value === 'CYCLE') ||
                row.orderType.value === 'GIFT') &&
              row.orderStatus === 'WAIT_AUDIT'
            "
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(row, 'audit')"
          >
            审核
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      v-loading="infoLoading"
      :value="dialogVisible"
      :title="dialogTitle"
      size="95%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="editType === 'info'"
      :no-confirm-footer="true"
      @cancel="dialogVisible = false"
    >
      <el-steps :active="active" finish-status="success" class="steps-box">
        <el-step title="买家下单"></el-step>
        <el-step title="买家付款"></el-step>
        <el-step title="发货"></el-step>
        <el-step title="买家确认收货"></el-step>
      </el-steps>
      <!-- 买家下单 -->
      <div v-if="orderInfo" ref="dialogContent" class="order-fix">
        <!--<el-button type="primary" class="image-btn" @click="toImage">-->
        <!--  生成图片-->
        <!--</el-button>-->
        <!-- <p class="text-p m-b-8">买家还有<span>10分00秒</span>支付订单。</p> -->
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="订单信息"
            :column="4"
            border
          >
            <el-descriptions-item label="订单状态">
              {{ getOrderStatusChinese(orderInfo.tradeOrder.orderStatus) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单编号">
              {{ orderInfo.tradeOrder.orderNum }}
            </el-descriptions-item>
            <el-descriptions-item label="配送方式">
              {{ orderInfo?.tradeOrder?.logisticsProvider?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="关联客户">
              {{ orderInfo.companyName }}
            </el-descriptions-item>
            <el-descriptions-item label="下单用户">
              {{ orderInfo.buyerName }}
            </el-descriptions-item>
            <el-descriptions-item label="客户等级">
              {{ orderInfo?.tradeOrder?.customerLevel?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="下单手机号">
              {{ orderInfo?.tradeOrder?.consigneePhone }}
            </el-descriptions-item>
            <el-descriptions-item label="支付方式">
              {{ orderInfo?.tradeOrder?.payMode?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="支付金额（元）">
              {{ orderInfo.tradeOrder.actualAmount }}
            </el-descriptions-item>
            <el-descriptions-item label="营业执照名称" :span="3">
              {{ orderInfo?.tradeOrder?.license }}
            </el-descriptions-item>
            <el-descriptions-item label="收货地址" :span="4">
              {{ orderInfo?.tradeOrder?.consigneeFullAddress }}
            </el-descriptions-item>

            <el-descriptions-item
              v-if="orderInfo.tradeOrder?.orderStatus === 'WAIT_PAY'"
              label="关闭订单"
            >
              <el-button type="primary" size="mini" @click="showGbddDialog()">
                关闭订单
              </el-button>
            </el-descriptions-item>
            <!--            <el-descriptions-item label="是否有退款单">-->
            <!--              {{ orderInfo?.tradeOrder?.isReversPending ? "是" : "否" }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item-->
            <!--              v-if="orderInfo?.tradeOrder?.isReversPending"-->
            <!--              label="查看退款单"-->
            <!--            >-->
            <!--              <el-button-->
            <!--                type="primary"-->
            <!--                size="mini"-->
            <!--                @click="showRefundDialog(orderInfo)"-->
            <!--                >查看退款单</el-button-->
            <!--              >-->
            <!--            </el-descriptions-item>-->
            <!--          </el-descriptions>-->
            <!-- <p class="text-p">
            <label class="p-label">订单状态：</label>
            <span class="p-content red">{{
              getOrderStatusChinese(orderInfo.tradeOrder.orderStatus)
            }}</span>
          </p>
          <div class="content-fixed">
            <p class="text-p">
              <label class="p-label">订单编号：</label>
              <span class="p-content">{{ orderInfo.tradeOrder.orderNum }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">支付金额（元）：</label>
              <span class="p-content red">{{
                orderInfo.tradeOrder.payAmount
              }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">关联客户：</label>
              <a class="p-a" href="#">{{ orderInfo.companyName }}</a>
            </p>
          </div>
          <div class="content-fixed">
            <p class="text-p">
              <label class="p-label">下单用户：</label>
              <span class="p-content">{{ orderInfo.buyerName }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">下单手机号：</label>
              <span class="p-content">{{
                orderInfo.tradeOrder.consigneePhone
              }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">支付方式：</label>
              <span class="p-content">微信</span>
            </p>
          </div>
          <p class="text-p">
            <label class="p-label">收货地址：</label>
            <span class="p-content">{{
              orderInfo.tradeOrder.consigneeAddress
            }}</span>
          </p>
          <p class="btn-p">
            <el-button
              v-if="orderInfo.tradeOrder.orderStatus == 'WAIT_PAY'"
              @click="showGbddDialog()"
              >关闭订单</el-button
            >
          </p> -->
          </el-descriptions>
        </div>
        <!-- 商品信息 -->
        <div class="m-t-8">
          <p class="tit-box m-b-12">商品信息</p>
          <ProTable
            ref="ProSPXXTable"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="spxxColumns"
            :show-pagination="false"
            :show-loading="false"
            :data="orderInfo.tradeOrder.tradeOrderDetailList || []"
            :show-setting="false"
            :show-search="false"
            :show-table-operator="false"
            sticky
            :height="300"
          >
            <template #storageArticle="{ row }">
              {{ row.storageArticle?.minUnit }}
            </template>
            <template #actualUnitPrice="{ row }">
              <span
                v-if="
                  editType === 'info' ||
                  orderInfo.tradeOrder?.orderType?.value === 'APPLY'
                "
              >
                {{ row.actualUnitPrice }}
              </span>

              <el-input
                v-if="
                  isCycleAudit(
                    orderInfo.tradeOrder?.orderType?.value,
                    orderInfo.tradeOrder?.payMode?.value
                  )
                "
                v-model="row.actualUnitPrice"
                type="number"
                placeholder="请输入商品单价"
                @change="(e) => handleInputChange(e, row)"
              ></el-input>
            </template>
          </ProTable>
          <div class="text-content">
            <p class="text-p">
              <label class="p-label">订单商品金额（元）：</label>
              <span class="p-content">
                {{ orderInfo.tradeOrder.actualGoodsAmount }}
              </span>
            </p>

            <p class="text-p">
              <label class="p-label">差异金额（元）：</label>
              <span class="p-content">
                {{
                  orderInfo.tradeOrder.discountAmount
                    ? mulAmount(
                        orderInfo.tradeOrder.discountAmount,
                        -1
                      ).toFixed(2)
                    : "0.00"
                }}
              </span>
              <!-- <el-input
                v-if="
                  isCycleAudit(
                    orderInfo.tradeOrder?.orderType?.value,
                    orderInfo.tradeOrder?.payMode?.value
                  )
                "
                v-model="orderInfo.tradeOrder.discountAmount"
                style="width: 50%"
                type="number"
                placeholder="请输入减免金额"
                @input="(e) => handleDiscountInputChange(e, row)"
              ></el-input> -->
            </p>

            <p class="text-p">
              <label class="p-label">订单运费（元）：</label>
              <span
                v-if="
                  editType === 'info' ||
                  orderInfo.tradeOrder?.orderType?.value === 'APPLY'
                "
                class="p-content"
              >
                {{ orderInfo.tradeOrder.shippingFee }}
              </span>
              <el-input
                v-if="
                  isCycleAudit(
                    orderInfo.tradeOrder?.orderType?.value,
                    orderInfo.tradeOrder?.payMode?.value
                  )
                "
                v-model="orderInfo.tradeOrder.shippingFee"
                style="width: 50%"
                type="number"
                placeholder="请输入订单运费"
                min="0"
                @input="
                  (e) => handleShippingFeeInputChange(e, orderInfo.tradeOrder)
                "
              ></el-input>
            </p>

            <!--            <p class="text-p">-->
            <!--              <label class="p-label">应收款（元）：</label>-->
            <!--              <span class="p-content">{{-->
            <!--                orderInfo.tradeOrder.actualGoodsAmount-->
            <!--              }}</span>-->
            <!--            </p>-->
            <!--            <p class="text-p">-->
            <!--              <label class="p-label">已收款（元）：</label>-->
            <!--              <span class="p-content">{{-->
            <!--                orderInfo.tradeOrder.shippingFee-->
            <!--              }}</span>-->
            <!--            </p>-->
            <!--            <p class="text-p">-->
            <!--              <label class="p-label">申请退货退款（元）：</label>-->
            <!--              <span class="p-content">{{-->
            <!--                orderInfo.tradeOrder.refundAmount-->
            <!--              }}</span>-->
            <!--            </p>-->
            <!--            <p class="text-p">-->
            <!--              <label class="p-label">已退货退款（元）：</label>-->
            <!--              <span class="p-content">{{-->
            <!--                orderInfo.tradeOrder.refundAmount-->
            <!--              }}</span>-->
            <!--            </p>-->

            <p class="text-p">
              <label class="p-label">实收款（元）：</label>
              <span class="p-content">
                {{ orderInfo.tradeOrder.actualAmount }}
                <!-- {{ computedActualAmount(orderInfo.tradeOrder.actualAmount) }} -->
              </span>
            </p>
            <p v-if="orderInfo.tradeOrder.buyerRemark" class="text-p m-b-8">
              <label class="p-label">订单备注：</label>
              <span class="p-content">
                {{ orderInfo.tradeOrder.buyerRemark }}
              </span>
            </p>
          </div>
        </div>

        <!-- 交易明细 -->
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="交易明细"
            :column="3"
            border
          >
            <el-descriptions-item label="订单来源">
              {{ getOrderStatusText(orderInfo.tradeOrder.orderSource) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单创建时间">
              {{ orderInfo.tradeOrder.createdAt }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderInfo.tradeOrder.payTime"
              label="订单支付时间"
            >
              {{ orderInfo.tradeOrder.payTime }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderInfo.deliveryTime"
              label="订单发货时间"
            >
              {{ orderInfo.deliveryTime }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderInfo.tradeOrder.finishTime"
              label="订单确认收货时间"
            >
              {{ orderInfo.tradeOrder.finishTime }}
            </el-descriptions-item>
          </el-descriptions>
          <!-- <p class="tit-box m-b-12">交易明细</p> -->
          <!-- <div class="content-fixed">
            <p class="text-p">
              <label class="p-label">订单来源：</label>
              <span class="p-content">销售订单</span>
            </p>
            <p class="text-p">
              <label class="p-label">订单创建时间：</label>
              <span class="p-content">{{
                orderInfo.tradeOrder.createdAt
              }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">订单支付时间：</label>
              <span class="p-content">{{ orderInfo.tradeOrder.payTime }}</span>
            </p>
          </div>
          <div class="content-fixed">
            <p class="text-p">
              <label class="p-label">订单发货时间：</label>
              <span class="p-content">{{
                orderInfo.tradeOrder.expectedDeliveryTime
              }}</span>
            </p>
          </div>
          <p class="text-p">
            <label class="p-label">订单确认收货时间：</label>
            <span class="p-content">{{ orderInfo.tradeOrder.finishTime }}</span>
          </p> -->
        </div>
        <!-- 支付凭证 -->
        <!--        <div-->
        <!--          v-if="orderInfo.tradeOrder.payMode?.value === 'OFFLINE'"-->
        <!--          class="m-t-8"-->
        <!--        >-->
        <!--          <p class="tit-box m-b-12">支付凭证</p>-->
        <!--          <div class="img-content">-->
        <!--            <div class="image-view">-->
        <!--              <div-->
        <!--                v-for="(item, index) in orderInfo?.voucherImg"-->
        <!--                :key="index"-->
        <!--                class="img-list"-->
        <!--              >-->
        <!--                <el-image-->
        <!--                  :preview-src-list="[item]"-->
        <!--                  style="width: 100px; height: 100px"-->
        <!--                  :src="item"-->
        <!--                ></el-image>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
        <!-- 物流信息 -->
        <div
          v-if="tradeInfo?.length > 0"
          class="m-t-8 box-box"
          style="clear: both"
        >
          <p class="tit-box m-b-12">物流信息</p>
          <div style="overflow: hidden">
            <div
              v-for="(item, index) in tradeInfo"
              :key="index"
              style="float: left; margin-right: 20px"
              @click="changeTrade(item)"
            >
              <div
                :class="
                  tradeInfoDetail.waybillNumber == item.waybillNumber
                    ? 'trade active'
                    : 'trade'
                "
              >
                <text class="iconfont iconbaoguo"></text>
                <div class="info">
                  <div>{{ item.packageName }}</div>
                  <div>共{{ item.expectedNumber }}件</div>
                </div>
              </div>
            </div>
          </div>
          <div class="tradedetail">
            <div>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'jdl'"
                class="iconfont iconsr_jingdong"
                style="color: red; font-size: 50rpx"
              ></text>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'iss'"
                class="iconfont iconshansonghuise"
                style="color: #ee822f; font-size: 50rpx"
              ></text>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'self'"
                class="iconfont iconziti"
                style="color: #ee822f; font-size: 50rpx"
              ></text>

              <text style="margin-left: 20rpx">
                {{ tradeInfoDetail?.logisticsProvider?.label }}:{{
                  tradeInfoDetail?.waybillNumber
                }}
              </text>
            </div>
            <div style="margin-top: 30px">
              <el-timeline>
                <el-timeline-item
                  v-for="item in tradeInfoDetail.traces"
                  :key="item.id"
                  :timestamp="item.providerStatus + '    ' + item.operatedAt"
                >
                  {{ item.operationRemark }}
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div v-if="editType === 'audit'" class="footer-btn">
          <el-button
            type="danger"
            @click="handleAudit(orderInfo.tradeOrder, 'REFUSE')"
          >
            驳回
          </el-button>
          <el-button
            type="primary"
            @click="handleAudit(orderInfo.tradeOrder, 'APPROVE')"
          >
            审核通过
          </el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
        </div>
      </template>
    </ProDrawer>
    <!-- 关闭订单弹框 -->
    <ProDialog
      :value="dialogGbddVisible"
      :title="'操作提示'"
      width="600px"
      :top="'10%'"
      :no-footer="false"
      @ok="handleGbddDialogOk"
      @cancel="dialogGbddVisible = false"
    >
      <div class="m-b-12">关闭后，此笔订单将失效，确定关闭？</div>
      <el-form ref="gbddFormRef" :model="gbddForm" label-width="50px">
        <el-form-item
          prop="closeReason"
          label="说明"
          :rules="[{ required: true, message: '请填写说明', trigger: 'blur' }]"
        >
          <el-input v-model="gbddForm.closeReason" type="textarea"></el-input>
        </el-form-item>
      </el-form>
    </ProDialog>
    <ProDialog
      :value="showDialog"
      title="客户信息"
      width="80%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <ProTable
        ref="ProTables"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="creatColumns"
        :local-pagination="localPaginations"
        :data="tableData1"
        :query-param="queryParams"
        :height="400"
        :show-setting="false"
        @loadData="loadData1"
      >
        <template #actions="slotProps">
          <span class="fixed-width">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-success"
              @click="sureSelectCustom(slotProps.row)"
            >
              确定
            </el-button>
          </span>
        </template>
      </ProTable>
    </ProDialog>
    <!--    选择商品信息-->
    <ProDialog
      :value="showDialog2"
      title="商品信息"
      width="80%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="showDialog2 = false"
    >
      <ProTable
        ref="ProTable2"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="creatColumn2"
        show-pagination
        :row-key="
          (row) => {
            return row.saleSkuId;
          }
        "
        :local-pagination="localPagination2"
        :data="tableData2"
        show-selection
        sticky
        :query-param="queryParam2"
        :height="380"
        :show-setting="false"
        @loadData="loadData2"
        @handleSelectionChange="handleAllSelectionChange"
      >
        <template #picsUrl="slotProps">
          <img
            style="max-width: 100px; max-height: 100px"
            :src="getPicsUrlImg(slotProps.row)"
          />
        </template>
        <template #saleAttrVals="slotProps">
          <span
            v-for="(item, index) in slotProps.row.saleAttrVals"
            :key="index"
            style="border: 1px solid #ddd"
          >
            {{ item.name }}: {{ item.val }}
          </span>
        </template>
        <template #fullIdPath>
          <el-cascader
            ref="ProductIds"
            v-model="productIdName"
            filterable
            clearable
            collapse-tags
            :options="options"
            style="width: 100%"
            :props="{
              label: 'name',
              value: 'fullIdPath',
              children: 'children',
              expandTrigger: 'click',
              multiple: true,
            }"
            leaf-only
            @change="handleChange"
          ></el-cascader>
        </template>
        <template #saleStatus="slotProps">
          {{ slotProps.row.saleStatus == "ON_SALE" ? "已上架" : "未上架" }}
        </template>
        <!--<template #actions="slotProps">-->
        <!--  <span class="fixed-width">-->
        <!--    <el-button-->
        <!--      size="mini"-->
        <!--      type="primary"-->
        <!--      icon="el-icon-zoom-out"-->
        <!--      @click="topageFn(slotProps.row)"-->
        <!--    >-->
        <!--      查看-->
        <!--    </el-button>-->
        <!--  </span>-->
        <!--</template>-->
      </ProTable>
      <div
        style="
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <el-button type="primary" @click="sureSelectGoodsFn">
          确认选择
        </el-button>
        <el-button @click="showDialog2 = false">关闭</el-button>
      </div>
    </ProDialog>

    <ProDialog
      :value="showDialogAudit"
      width="80%"
      title="审核订单"
      :no-footer="true"
      @cancel="showDialogAudit = false"
    >
      <el-descriptions title="订单信息" border>
        <el-descriptions-item label="用户名">
          {{ auditData.consigneeName }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          {{ auditData.consigneePhone }}
        </el-descriptions-item>
        <el-descriptions-item label="订单编号">
          {{ auditData.orderNum }}
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag size="small">
            {{ auditData.orderStatus === "WAIT_AUDIT" ? "待审核" : "已审核" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订单金额">
          {{ auditData.actualGoodsAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="联系地址">
          {{ auditData.consigneeFullAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="支付方式">
          <el-tag size="small">
            {{ auditData.payMode?.label }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="携带方式">
          <el-tag size="small">
            {{ auditData.logisticsProvider?.label }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订单创建时间">
          {{ auditData.orderTime }}
        </el-descriptions-item>
      </el-descriptions>

      <p class="tit-box m-b-12">商品信息</p>
      <!-- :data="auditData.tradeOrderDetailList || []" -->
      <ProTable
        ref="ProSPXXTable"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="spxxColumns"
        :show-pagination="false"
        :show-loading="false"
        :show-setting="false"
        :data="auditData.tradeOrderDetailList || []"
        :show-search="false"
        :show-table-operator="false"
        sticky
        :height="200"
      >
        <template #storageArticle="{ row }">
          {{ row.storageArticle?.minUnit }}
        </template>
      </ProTable>

      <div class="footer-btn">
        <el-popconfirm
          title="确定通过该物料吗？"
          confirm-button-text="通过"
          confirm-button-type="success"
          placement="top"
          @confirm="handleAudit(auditData, 'APPROVE')"
        >
          <el-button slot="reference" type="primary">通过</el-button>
        </el-popconfirm>
        <el-popconfirm
          title="确定驳回该物料吗？"
          confirm-button-text="驳回"
          confirm-button-type="warning"
          placement="top"
          @confirm="handleAudit(auditData, 'REFUSE')"
        >
          <el-button slot="reference" type="warning">驳回</el-button>
        </el-popconfirm>
      </div>
    </ProDialog>

    <ProDrawer
      :value="addDialog"
      :title="'创建订单'"
      size="85%"
      :confirm-loading="confirmLoadings"
      :top="'10%'"
      :no-footer="true"
      @cancel="canceladdDialog()"
    >
      <el-steps
        align-center
        :active="actives"
        finish-status="success"
        class="steps-box"
      >
        <el-step title="客户/商品选择"></el-step>
        <el-step title="订单预览"></el-step>
      </el-steps>
      <div v-show="actives == 0" style="margin-top: 10px">
        <div class="tit-boxs">
          <el-button type="success" size="small" @click="showDialogFn(0)">
            选择客户信息
          </el-button>
        </div>
        <el-form
          ref="proform_child1"
          :model="calculateFormrr"
          :disabled="methodType == 'info'"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="客户编码:" prop="customerSeqId">
                <el-input
                  v-model="calculateFormrr.customerSeqId"
                  placeholder="请选择客户信息"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="店铺名称:" prop="customerName">
                <el-input
                  v-model="calculateFormrr.customerName"
                  placeholder="请选择客户信息"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="收货地址:"
                prop="addressId"
                :rules="[
                  {
                    required: true,
                    trigger: 'change',
                    message: '请选择收货地址',
                  },
                ]"
              >
                <el-select
                  v-model="calculateFormrr.addressId"
                  style="width: 100%"
                  placeholder="请选择收货地址"
                >
                  <el-option
                    v-for="item in addressList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="员工名称:" prop="consigneeName">
                <el-input
                  v-model="calculateFormrr.consigneeName"
                  placeholder="请选择客户信息"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="员工电话:" prop="consigneePhone">
                <el-input
                  v-model="calculateFormrr.consigneePhone"
                  placeholder="请选择客户信息"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <div class="tit-boxs" style="margin-bottom: 0">
          <el-button type="success" size="small" @click="showDialogFn(1)">
            选择商品信息
          </el-button>
        </div>
        <ProTable
          ref="ProTable2s"
          :columns="creatColumn4"
          :row-key="(row) => row.saleSkuId"
          :data="surenewArr"
          sticky
          :show-setting="false"
          :show-loading="false"
          :height="460"
        >
          <template #picsUrl="{ row }">
            <el-image
              v-if="row.picsUrl && row.picsUrl.length > 0"
              style="width: 100px; height: 100px"
              :src="getPicsUrlImg(row)"
            />
          </template>
          <template #saleAttrVals="slotProps">
            <span
              v-for="(item, index) in slotProps.row.saleAttrVals"
              :key="index"
              style="border: 1px solid #ddd"
            >
              {{ item.name }}: {{ item.val }}
            </span>
          </template>
          <template #saleStatus="slotProps">
            {{ slotProps.row.saleStatus == "ON_SALE" ? "已上架" : "未上架" }}
          </template>
          <template #buyNum="{ row }">
            <el-input-number
              v-model="row.buyNum"
              controls-position="right"
              style="width: 100%"
              size="small"
              :min="1"
              :step="1"
            ></el-input-number>
          </template>
          <template #actions="slotProps">
            <span class="fixed-width">
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                @click="surenewArr.splice(slotProps.index, 1)"
              >
                删除
              </el-button>
            </span>
          </template>
        </ProTable>

        <div class="drawer-footer">
          <el-button type="primary" @click="sureSelectGoods()">
            下一步
          </el-button>
          <el-button @click="canceladdDialog()">关闭</el-button>
        </div>
      </div>
      <div v-show="actives == 1" style="margin-top: 10px">
        <div class="tit-box" style="margin-top: 0">客户信息</div>
        <div>
          <el-form
            ref="proform_child1"
            :model="dataInfo"
            :disabled="methodType == 'info'"
            label-width="100px"
            class="demo-ruleForm"
          >
            <el-row>
              <el-col :span="6">
                <el-form-item label="客户编码:" prop="customerSeqId">
                  <el-input
                    v-model="dataInfo.customerSeqId"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="店铺名称:" prop="customerName">
                  <el-input
                    v-model="dataInfo.customerName"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="收货人:" prop="consigneeName">
                  <el-input
                    v-model="dataInfo.consigneeName"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="联系电话:" prop="consigneePhone">
                  <el-input
                    v-model="dataInfo.consigneePhone"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="详细地址:" prop="consigneeAddress">
                  <el-input
                    v-model="dataInfo.consigneeAddress"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  label="配送方式:"
                  prop="consigneeName"
                  :rules="[
                    {
                      required: true,
                      trigger: 'change',
                      message: '请选择配送方式',
                    },
                  ]"
                >
                  <el-select
                    v-model="psfsData"
                    placeholder="请选择配送方式"
                    style="width: 100%"
                    @change="sureSelectGoods()"
                  >
                    <el-option
                      v-for="item in dataInfo.availableLogisticsProviders"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="支付方式:">
                  <el-select
                    v-model="payModel"
                    placeholder="请选择支付方式"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in dataInfo.availablePayModeProviders"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="tit-box" style="margin: 0">商品信息</div>
        <div style="padding: 5px 10px; margin: 0 auto">
          <ProTable
            ref="ProTable3"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="creatColumn3"
            show-pagination
            row-key="id"
            :data="tableData3"
            sticky
            :show-setting="false"
            :height="420"
          >
            <template #picsUrl="slotProps">
              <img
                style="max-width: 100px; max-height: 100px"
                :src="getPicsUrlImgs(slotProps.row.saleSkuInfo)"
              />
            </template>
            <template #saleAttrVals="{ row }">
              <span
                v-for="(item, index) in row.saleSkuInfo.saleAttrVals"
                :key="index"
                style="border: 1px solid #ddd"
              >
                {{ item.name }}: {{ item.val }}
              </span>
            </template>
            <!-- 成交价 -->
            <template #actualUnitPrice="{ row }">
              <el-input-number
                v-model="row.actualUnitPrice"
                controls-position="right"
                :min="0"
                :precision="2"
                placeholder="实际成交价"
                style="width: 100%"
              ></el-input-number>
            </template>
          </ProTable>
        </div>
        <div class="totalNumber">
          <div class="totalNumber-list">
            订单商品总金额（元）：{{ dataInfo.actualGoodsAmount }}
          </div>
          <div class="totalNumber-list">
            差异金额（元）：{{
              dataInfo.discountAmount
                ? mulAmount(dataInfo.discountAmount, -1).toFixed(2)
                : "0.00"
            }}
          </div>
          <div class="totalNumber-list">
            订单运费（元）：{{ dataInfo.shippingFee }}
          </div>
          <div class="totalNumber-list">
            <!--应收（元）：{{ dataInfo.actualAmount }}-->
            应收（元）：{{ calculateActualAmount }}
          </div>
        </div>
        <div class="drawer-footer">
          <el-button type="primary" @click="actives = 0">上一步</el-button>
          <el-button
            :loading="confirmOrderLoading"
            type="primary"
            @click="sureOrderFn()"
          >
            确认订单
          </el-button>
        </div>
      </div>
    </ProDrawer>
    <PrintOrder
      :visible.sync="printOrderVisible"
      :order-info="printOrderInfo"
    />
  </div>
</template>
<script>
import {
  operatorTradeOrderPageApi,
  operatorTradeOrderDetailApi,
  operatorTradeOrderCloseApi,
  OrderTracesApi,
  optionsGetRegionApi,
  operatorTradeOrderAuditApi,
  operatorTradeOrderSaleAuditApi,
  getAddressListApi,
  getOrderStatisticsApi,
} from "@/api/operator";
import {
  getCustomerStaffPageApi,
  itemSummaryListApi,
  classifyListApi,
  getPreviewPageApi,
  getCreatePageApi,
} from "@/api/goods";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { productAllApi } from "@/api/dispose";
import { dictTreeByCodeApi, dictTreeByCodeApi2 } from "@/api/user";
import { addAmount, filterParamRange, mulAmount } from "@/utils";
import { cloneDeep } from "lodash";
import { Loading, Message } from "element-ui";
import html2canvas from "html2canvas";
import PrintOrder from "./printOrder.vue";
import { set } from "nprogress";
export default {
  name: "Orders",
  components: {
    PrintOrder,
  },
  data() {
    return {
      showDialogAudit: false,
      auditData: {},
      showDialog: false,
      showDialog2: false,
      psfsData: "jdl",
      payModel: "WECHART",
      newArr: [],
      surenewArr: [],
      calculateFormrr: {},
      // 列表
      productIdName: [],
      options: [],
      actives: 0,
      creatColumns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        // {
        //   dataIndex: "shopRecruitment",
        //   title: "店铺名称",
        //   isTable: true,
        // },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "staffName",
          title: "员工名称",
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "员工名称",
          isTable: true,
        },
        {
          dataIndex: "staffPhone",
          title: "员工电话",
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "tel",
          title: "员工电话",
          isTable: true,
        },
        {
          dataIndex: "role",
          title: "角色",
          isTable: true,
          formatter: (row) => row.role?.label,
        },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      localPaginations: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      creatColumn2: [
        {
          dataIndex: "fullIdPath",
          isSearch: true,
          clearable: true,
          searchSlot: "fullIdPath",
          title: "适用机型",
          valueType: "select",
        },
        {
          dataIndex: "categoryId",
          title: "商品分类",
          isSearch: true,
          formSpan: 8,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },

        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          minWidth: 150,
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          clearable: true,
          isSearch: true,
          formSpan: 16,
          width: 150,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          clearable: true,
          isSearch: true,
          formSpan: 16,
          width: 180,
          valueType: "input",
        },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
          minWidth: 80,
        },
        {
          dataIndex: "unitList",
          title: "所属单元",
          width: 150,
          isSearch: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(3200),
          formatter: (row) => row.spareLevel?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "productPartTypeList",
          title: "物品小类",
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          formatter: (row) => row.spareLevel?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          title: "库存数量",
          dataIndex: "inventoryNum",
          isTable: true,
          minWidth: 80,
        },
        // {
        //   dataIndex: "Actions",
        //
        //   fixed: "right",
        //   title: "操作",
        //   align: "left",
        //   isTable: true,
        //   tableSlot: "actions",
        //   minWidth: 100,
        // },
      ],
      creatColumn4: [
        {
          dataIndex: "fullIdPath",
          isSearch: false,
          clearable: true,
          searchSlot: "fullIdPath",
          title: "适用机型",
          valueType: "select",
        },
        {
          dataIndex: "categoryId",
          title: "商品分类",
          isSearch: false,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          clearable: true,
          isTable: true,
          isSearch: false,
          formSpan: 16,
          valueType: "input",
        },

        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
          clearable: true,
          isSearch: false,
          formSpan: 16,
          valueType: "input",
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          clearable: true,
          isSearch: false,
          formSpan: 16,
          width: 180,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          clearable: true,
          isSearch: false,
          formSpan: 16,
          width: 180,
          valueType: "input",
        },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
        },
        {
          dataIndex: "unitList",
          title: "所属单元",
          width: 150,
          isTable: false,
          isSearch: false,
          clearable: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(3200),
          formatter: (row) => row.spareLevel?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "productPartTypeList",
          title: "物品小类",
          width: 150,
          isTable: false,
          isSearch: false,
          clearable: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          formatter: (row) => row.spareLevel?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          title: "商品数量",
          dataIndex: "buyNum",
          isTable: true,
          tableSlot: "buyNum",
          width: 120,
        },
        {
          dataIndex: "Actions",
          width: 100,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      localPagination2: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      creatColumn3: [
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "articleCode",
          title: "物品编码",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          minWidth: 120,
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 140,
        },
        {
          title: "商品数量",
          dataIndex: "itemNum",
          isTable: true,
          clearable: true,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "商品单价（元）",
          isTable: true,
        },
        {
          dataIndex: "discountAmount",
          title: "减免费用",
          isTable: true,
        },
        {
          dataIndex: "payAmount",
          title: "小计（元）",
          isTable: true,
        },
        {
          dataIndex: "actualUnitPrice",
          title: "成交价",
          isTable: true,
          tableSlot: "actualUnitPrice",
          width: 180,
        },
      ],
      tableData1: [],
      tableData2: [],
      tableData3: [],
      queryParams: {},
      queryParam2: {
        lastIds: null,
      },
      confirmLoadings: false,
      optionsGetRegion: [],
      addForm: {},
      addFormLoading: false,
      addDialog: false,
      active: 0,
      // 收货地址列表
      addressList: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        orderNum: "",
        orderTypes: ["SALE"],
      },
      columns: [
        {
          dataIndex: "orderStatusList",
          title: "订单状态",
          isTable: true,
          formSpan: 8,
          isSearch: true,
          multiple: true,
          valueType: "select",
          formatter: (row) => {
            switch (row.orderStatus) {
              case "WAIT_PAY":
                return "待支付";
              case "WAIT_DELIVER":
                return "待发货";
              case "WAIT_RECEIVE":
                return "待收货";
              case "WAIT_AUDIT":
                return "待审核";
              case "SUCCESS":
                return "已完成";
              case "CLOSED":
                return "已取消";
            }
          },
          option: [
            { label: "待支付", value: "WAIT_PAY" },
            { label: "待发货", value: "WAIT_DELIVER" },
            { label: "待收货", value: "WAIT_RECEIVE" },
            // { label: "待审核", value: "WAIT_AUDIT" },
            { label: "已完成", value: "SUCCESS" },
            { label: "已取消", value: "CLOSED" },
          ],
        },
        {
          dataIndex: "orderNum",
          title: "订单号",
          isSearch: true,
          valueType: "input",
        },
        // {
        //   dataIndex: "orderType",
        //   title: "订单类型",
        //   isTable: true,
        //   formatter: (row) => row.orderType?.label,
        // },
        // {
        //   dataIndex: "orderTypes",
        //   title: "订单类型",
        //   isSearch: true,
        //   multiple: true,
        //   valueType: "select",
        //   option: [
        //     { label: "销售订单", value: "SALE" },
        //     { label: "领料单", value: "APPLY" },
        //     { label: "赠品订单", value: "GIFT" },
        //   ],
        // },
        {
          dataIndex: "payTime",
          title: "支付时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          clearable: true,
        },
        {
          dataIndex: "logisticsProviders",
          title: "配送方式",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            { label: "京东快递", value: "jdl" },
            { label: "闪送", value: "iss" },
            { label: "自提", value: "self" },
            { label: "工程师带", value: "passing" },
          ],
        },
        {
          dataIndex: "orderNum",
          title: "订单号",
          width: 180,
          isTable: true,
        },
        // {
        //   dataIndex: "orderType",
        //   title: "订单类型",
        //   clearable: true,
        //   isTable: true,
        //   isSearch: true,
        //   formatter: (row) => row.orderType?.label,
        //   valueType: "select",
        //   option: [
        //     { label: "销售订单", value: "SALE" },
        //     { label: "领料单", value: "APPLY" },
        //   ],
        // },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          minWidth: 150,
        },
        // {
        //   dataIndex: "province",
        //   title: "省",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "city",
        //   title: "市",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "area",
        //   title: "区",
        //   isTable: true,
        // },
        {
          dataIndex: "license",
          title: "营业执照名称",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "consigneeFullAddress",
          title: "收货地址",
          isTable: true,
          minWidth: 210,
        },
        {
          dataIndex: "payModes",
          title: "支付方式",
          isSearch: true,
          valueType: "select",
          multiple: true,
          // optionMth: () => dictTreeByCodeApi(4200),
          option: [
            { label: "微信支付", value: "WECHART" },
            { label: "线下支付", value: "OFFLINE" },
            { label: "期结", value: "CYCLE" },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },

        {
          dataIndex: "consigneePhone",
          title: "手机号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "logisticsProvider",
          title: "配送方式",
          isTable: true,
          formatter: (row) => row.logisticsProvider?.label,
        },
        {
          dataIndex: "payMode",
          title: "支付方式",
          isTable: true,
          formatter: (row) => row.payMode?.label,
        },
        {
          dataIndex: "actualGoodsAmount",
          title: "商品金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "shippingFee",
          title: "配送费",
          isTable: true,
        },
        {
          dataIndex: "actualAmount",
          title: "订单金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "reverseAmount",
          title: "退款金额",
          isTable: true,
          minWidth: 100,
        },

        // {
        //   dataIndex: "waterNum",
        //   title: "流水号",
        //   isTable: true,
        // },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          width: 150,
        },
        {
          dataIndex: "payTime",
          title: "支付时间",
          isTable: true,
          width: 150,
          formatter: (row) => (row.payTime ? row.payTime : "/"),
        },

        // {
        //   dataIndex: "regionPath",
        //   title: "省市区",
        //   isSearch: true,
        //   searchSlot: "regionPath",
        // },
        {
          dataIndex: "isReverse",
          title: "是否退货",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: 1,
            },
            {
              label: "否",
              value: 0,
            },
          ],
        },
        {
          dataIndex: "Actions",
          width: 170,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      //新增
      methodType: "add",
      editType: "info",
      confirmLoading: false,
      dialogTitle: "",
      dialogVisible: false,
      form: {},
      spxxColumns: [
        {
          dataIndex: "itemName",
          isTable: true,
          title: "商品名称",
          minWidth: 100,
        },
        // {
        //   dataIndex: "itemId",
        //   isTable: true,
        //   title: "商品编号",
        // },
        {
          dataIndex: "code",
          isTable: true,
          title: "物品编号",
          formatter: (row) => row.storageArticle?.code,
          minWidth: 120,
        },
        {
          dataIndex: "name",
          isTable: true,
          title: "物品名称",
          formatter: (row) => row.storageArticle?.name,
          minWidth: 120,
        },
        {
          dataIndex: "numberOem",
          isTable: true,
          title: "OEM编号",
          formatter: (row) => row.storageArticle?.numberOem,
          minWidth: 100,
        },
        {
          dataIndex: "manufacturerChannel",
          isTable: true,
          title: "制造渠道",
          formatter: (row) => row.storageArticle?.manufacturerChannel.label,
        },
        {
          dataIndex: "saleUnitPrice",
          isTable: true,
          title: "商品单价（元）",
          minWidth: 100,
        },
        {
          dataIndex: "actualUnitPrice",
          isTable: true,
          title: "成交价（元）",
          tableSlot: "actualUnitPrice",
          minWidth: 100,
        },
        {
          dataIndex: "itemNum",
          isTable: true,
          title: "购买数量",
        },
        {
          dataIndex: "deliveryNum",
          isTable: true,
          title: "已发数量",
        },
        {
          dataIndex: "receiveNum",
          isTable: true,
          title: "已收数量",
        },
        {
          dataIndex: "reverseNum",
          isTable: true,
          title: "已退数量",
        },
        {
          dataIndex: "storageArticle",
          isTable: true,
          title: "单位",
          tableSlot: "storageArticle",
        },
        {
          dataIndex: "discountAmount",
          isTable: true,
          title: "差异金额（元）",
          formatter: (row) =>
            row.discountAmount
              ? mulAmount(row.discountAmount, -1).toFixed(2)
              : "0.00",
          minWidth: 100,
        },
        // {
        //   dataIndex: "payAmount",
        //   isTable: true,
        //   title: "小计（元）",
        // },
        {
          dataIndex: "actualPayAmount",
          isTable: true,
          title: "小计（元）",
        },
      ],
      dialogGbddVisible: false,
      gbddForm: {
        closeReason: null,
      },
      tradeInfo: [],
      tradeInfoDetail: {},
      orderInfo: null,
      infoLoading: false,
      saleSkuBuyParams: [],
      dataInfo: {},
      minUnit: "个",
      totalData: {},
      confirmOrderLoading: false,
      fullAddressList: [],
      printOrderVisible: false,
      printOrderInfo: null,
    };
  },
  computed: {
    calculateActualAmount() {
      let result = 0;
      result = this.tableData3.reduce((acc, cur) => {
        return addAmount(acc, mulAmount(cur.actualUnitPrice, cur.itemNum));
      }, this.dataInfo.shippingFee ?? 0);

      return result;
    },
  },
  created() {
    this.optionsGetRegionAFn();
  },
  mounted() {
    if (this.$route.query.id) {
      this.queryParam.orderNum = this.$route.query.id;
    }
    this.$refs.ProTable?.refresh();
  },
  methods: {
    mulAmount,
    isCycleAudit(orderType, payMode) {
      return (
        this.editType === "audit" && orderType === "SALE" && payMode === "CYCLE"
      );
    },
    handleInputChange(val, row) {
      row.payAmount = mulAmount(val, row.itemNum) - Number(row.discountAmount);

      const { tradeOrderDetailList, shippingFee, discountAmount } =
        this.orderInfo.tradeOrder;

      const totalPayAmount = tradeOrderDetailList.reduce(
        (total, item) => total + Number(item.payAmount),
        0
      );

      this.orderInfo.tradeOrder.actualAmount = (
        parseFloat(totalPayAmount) +
        parseFloat(shippingFee) -
        parseFloat(discountAmount)
      ).toFixed(2);
    },
    handleShippingFeeInputChange(e, tradeOrder) {
      // 确保输入值 e 是有效的数字
      const shippingFee = parseFloat(e);
      if (isNaN(shippingFee)) {
        Message.error("请输入有效的数字");
        return;
      }

      // 获取相关数据
      const { tradeOrderDetailList, discountAmount } = tradeOrder;

      // 计算总支付金额
      const totalPayAmount = tradeOrderDetailList.reduce(
        (total, item) => total + Number(item.payAmount),
        0
      );
      // 更新实际金额
      this.orderInfo.tradeOrder.actualAmount = (
        parseFloat(totalPayAmount) +
        shippingFee -
        parseFloat(discountAmount)
      ).toFixed(2);
    },
    // 审核订单
    async handleAudit(row, status) {
      const confirmText = status === "APPROVE" ? "通过" : "驳回";
      const confirmType = status === "APPROVE" ? "success" : "warning";

      const result = await this.$confirm(
        `此操作将${confirmText}该订单的审核, 是否继续?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: confirmType,
        }
      ).catch(() => {});

      if (!result) return;

      const apiMap = {
        APPLY: operatorTradeOrderAuditApi,
        SALE: operatorTradeOrderSaleAuditApi,
        GIFT: operatorTradeOrderAuditApi,
      };

      const api = apiMap[row.orderType.value];
      if (!api) {
        Message.error("不支持该订单类型的审核");
        return;
      }

      const params = {
        ...(row.orderType.value === "APPLY" || row.orderType.value === "GIFT"
          ? { id: row.id }
          : { tradeOrder: row }),
        auditStatus: status,
        remark: row.remark,
      };

      try {
        const response = await api(params);
        if (response.code === 200) {
          this.handleSuccess(status);
        } else {
          Message.error("审核失败");
        }
      } catch (e) {
        Message.error("系统错误");
      }
    },

    handleSuccess(status) {
      const message = status === "APPROVE" ? "审核通过" : "已驳回";
      Message.success(message);
      this.$refs.ProTable.refresh();
      this.dialogVisible = false;
    },
    // async handleAudit(row, status) {
    //   const confirmText = status === "APPROVE" ? "通过" : "驳回";
    //   const confirmType = status === "APPROVE" ? "success" : "warning";
    //   this.$confirm(`此操作将${confirmText}该订单的审核, 是否继续?`, "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: confirmType,
    //   }).then(async () => {
    //     if (row.orderType.value === "APPLY" && row.payMode.value === "CYCLE") {
    //       const params = {
    //         id: row.id,
    //         auditStatus: status,
    //         remark: row.remark,
    //       };
    //       try {
    //         const result = await operatorTradeOrderAuditApi(params);
    //         if (result.code === 200) {
    //           if (status === "APPROVE") {
    //             Message.success("审核通过");
    //           } else {
    //             Message.success("已驳回");
    //           }

    //           this.$refs.ProTable.refresh();
    //           this.dialogVisible = false;
    //         }
    //       } catch (e) {
    //         Message.error("系统错误");
    //       }
    //     } else if (
    //       row.orderType.value === "SALE" &&
    //       row.payMode.value === "CYCLE"
    //     ) {
    //       const params = {
    //         tradeOrder: row,
    //         auditStatus: status,
    //         remark: row.remark,
    //       };
    //       try {
    //         const result = await operatorTradeOrderSaleAuditApi(params);
    //         if (result.code === 200) {
    //           if (status === "APPROVE") {
    //             Message.success("审核通过");
    //           } else {
    //             Message.success("已驳回");
    //           }
    //           this.$refs.ProTable.refresh();
    //           this.dialogVisible = false;
    //         }
    //       } catch (e) {
    //         Message.error("系统错误");
    //       }
    //     }
    //   });
    // },
    // 审核领料订单
    showAuditDialog(row) {
      this.showDialogAudit = true;
      this.auditData = row;
      operatorTradeOrderDetailApi(row.orderNum).then((res) => {
        this.auditData = res.data.tradeOrder;
      });
    },
    /**
     * TODO: 待完成
     * @description 查看退款单
     * @param orderInfo
     */
    showRefundDialog(orderInfo) {
      this.$message.warning("功能开发中");
    },
    // 获取统计数据
    getOrderStatisticsFn(params) {
      getOrderStatisticsApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    // 创建订单
    createdOreder() {
      this.addDialog = true;
      this.addressList = [];
      this.$refs.ProTable2s?.refresh();
      // this.surenewArr = [{}];
      this.$nextTick((e) => {
        this.$refs.ProTable2s
          ? (this.$refs.ProTable2s.listLoading = false)
          : null;
      });
    },
    // 确认订单
    sureOrderFn() {
      if (!this.tableData3.length) {
        this.$message.error("商品信息不存在，请重新选择");
        this.actives = 0;
        return;
      }
      const find = this.tableData3.find(
        (item) =>
          item.actualUnitPrice === undefined || item.actualUnitPrice === null
      );
      if (find) {
        this.$message.error(`请填写${find.itemName}的成交价`);
        return;
      }
      this.$confirm("确认客户和商品信息无误并创建销售单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.confirmOrderLoading = true;
        const saleSkuBuyParams = [];
        this.tableData3.forEach((item) => {
          saleSkuBuyParams.push({
            saleSkuId: item.saleSkuId,
            buyNum: item.itemNum,
            actualUnitPrice: item.actualUnitPrice,
          });
        });
        const args = {
          buyerId: this.calculateFormrr.id,
          customerId: this.calculateFormrr.customerId,
          logisticsProvider: this.psfsData,
          mobile: this.calculateFormrr.tel,
          id: this.calculateFormrr.ids,
          saleSkuBuyParams: saleSkuBuyParams,
          addressId: this.calculateFormrr.addressId,
          payMode: this.payModel, // 默认支付方式
          isMechine: false,
        };
        getCreatePageApi(args)
          .then((res) => {
            this.$message.success("创建订单成功");
            this.addDialog = false;
            this.actives = 0;
            this.saleSkuBuyParams = [];
            this.calculateFormrr = {};
            this.showDialog = false;
            this.showDialog2 = false;
            this.psfsData = "jdl";
            this.payModel = "WECHART";
            this.newArr = [];
            this.surenewArr = [];
            this.productIdName = [];
            this.$refs.ProTable?.refresh();
          })
          .finally(() => {
            this.confirmOrderLoading = false;
          });
      });
    },
    // 选中的数据
    handleAllSelectionChange(row) {
      this.newArr = cloneDeep(row);
    },
    handleCalculateSubmit() {},
    canceladdDialog() {
      this.addDialog = false;
      this.actives = 0;
      this.optionsGetRegion = [];
      this.calculateFormrr = {};
      this.surenewArr = [];
      this.newArr = [];
      // this.$router.push({ name: "orders" });
      this.$refs.ProTable?.refresh();
      this.$refs.ProTable2?.refresh();
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    getPicsUrlImgs(row) {
      return row?.picUrl?.[0]?.url;
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
      classifyListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.creatColumn2[1].option = (res.data.rows || []).map((item) => ({
          label: item.name,
          value: item.id,
        }));
      });
    },
    handleChange(item) {
      this.queryParam2.lastIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam2.lastIds.push(id);
      });
    },
    // 确认用户
    async sureSelectCustom(row) {
      row.consigneeName = row.name;
      row.consigneePhone = row.tel;
      this.calculateFormrr = {};
      this.addressList = [];
      // 获取用户可用地址
      try {
        const res = await getAddressListApi(row.customerId);
        this.fullAddressList = res.data;
        res.data.forEach((item) => {
          this.addressList.push({
            value: item.id,
            label: item.address,
          });
        });
      } finally {
        setTimeout(() => {
          this.fullAddressList.forEach((item) => {
            if (item.isDefault) {
              this.$set(this.calculateFormrr, "addressId", item.id);
            }
          });
        }, 300);
      }

      this.$nextTick((e) => {
        // 以服务的方式调用的 Loading 需要异步关闭
        this.calculateFormrr = row;
      });
      this.showDialog = false;
    },
    resetFrom() {
      this.dataInfo = cloneDeep(this.dataInfo);
      // this.deviceProductTree = [];
    },
    // 确认商品
    sureSelectGoodsFn() {
      if (this.newArr.length === 0) {
        this.$message.warning("请选择商品");
        return;
      }
      if (this.surenewArr.length > 0) {
        this.newArr.forEach((item) => {
          if (
            !this.surenewArr.some((val) => val.saleSkuId === item.saleSkuId)
          ) {
            // item.buyNum = 1;
            this.$set(item, "buyNum", 1);
            this.surenewArr.push(item);
          }
        });
      } else {
        this.newArr.forEach((item) => {
          // item.buyNum = 1;
          this.$set(item, "buyNum", 1);
        });
        this.surenewArr = this.newArr;
      }
      this.showDialog2 = false;
      setTimeout(() => {
        this.$refs.ProTable2.$refs.ProElTable.doLayout();
      }, 300);
      this.$refs.ProTable2s
        ? (this.$refs.ProTable2s.listLoading = false)
        : null;
    },
    // 确认商品
    sureSelectGoods() {
      if (Object.keys(this.calculateFormrr).length === 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      if (!this.calculateFormrr.addressId) {
        this.$message.error("请选择收货地址");
        return;
      }
      if (this.newArr.length == 0) {
        this.$message.warning("请选择商品信息");
        return;
      }
      const loadingInstance = Loading.service({
        text: "正在加载中...", //显示在加载图标下方的加载文案
        background: "rgba(0, 0, 0, 0.3)", //遮罩背景色
      });
      this.confirmLoadings = true;
      this.saleSkuBuyParams = [];
      this.surenewArr.forEach((item) => {
        this.saleSkuBuyParams.push({
          saleSkuId: item.saleSkuId,
          buyNum: item.buyNum,
        });
      });
      this.actives = 1;
      getPreviewPageApi({
        buyerId: this.calculateFormrr.id,
        customerId: this.calculateFormrr.customerId,
        logisticsProvider: this.psfsData,
        mobile: this.calculateFormrr.tel,
        saleSkuBuyParams: this.saleSkuBuyParams,
        addressId: this.calculateFormrr.addressId,
        isMechine: false,
      })
        .then((res) => {
          res.data.customerName = this.calculateFormrr.customerName;
          res.data.customerSeqId = this.calculateFormrr.customerSeqId;
          this.dataInfo = res.data;
          this.tableData3 = res.data.tradeOrderDetailList;
          this.calculateFormrr.consigneeAddress = res.data.consigneeAddress;
          this.calculateFormrr.ids = res.data.id;
          this.$nextTick((e) => {
            // 以服务的方式调用的 Loading 需要异步关闭
            loadingInstance.close();
          });
          this.confirmLoadings = false;
        })
        .finally(() => {
          this.$refs.ProTable3
            ? (this.$refs.ProTable3.listLoading = false)
            : null;
          setTimeout(() => {
            this.$refs.ProTable3.$refs.ProElTable.doLayout();
          }, 300);
          this.$nextTick((e) => {
            // 以服务的方式调用的 Loading 需要异步关闭
            loadingInstance.close();
          });
        });
    },

    optionsGetRegionAFn() {
      optionsGetRegionApi().then((res) => {
        this.optionsGetRegion = res.data;
      });
    },
    showDialogFn(val) {
      if (val) {
        this.init();
        this.queryParam2 = {};
        this.showDialog2 = true;
        this.localPagination2 = {
          pageNumber: 1,
          pageSize: 10,
          total: 0,
        };
        this.$nextTick((e) => {
          this.$refs.ProTable2.refresh();
          this.$refs.ProTable2.$refs.ProElTable.clearSelection();
        });
      } else {
        this.showDialog = true;
        this.queryParams = {};
        this.localPaginations = {
          pageNumber: 1,
          pageSize: 10,
          total: 0,
        };
        this.$nextTick((e) => {
          this.$refs.ProTables.refresh();
        });
      }
    },
    loadData1(parameter) {
      const requestParameters = Object.assign(this.queryParams, parameter);
      getCustomerStaffPageApi(requestParameters)
        .then((res) => {
          this.tableData1 = res.data.rows;
          this.localPaginations = {
            pageNumber: parameter.pageNumber,
            pageSize: parameter.pageSize,
            total: +res.data.total,
          };
        })
        .finally(() => {
          this.$refs.ProTables
            ? (this.$refs.ProTables.listLoading = false)
            : null;
        });
    },
    loadData2(parameter) {
      const requestParameters = Object.assign(this.queryParam2, parameter);
      requestParameters.saleStatus = "ON_SALE";
      itemSummaryListApi(requestParameters)
        .then((res) => {
          this.tableData2 = res.data.rows;
          this.localPagination2 = {
            pageNumber: parameter.pageNumber,
            pageSize: parameter.pageSize,
            total: +res.data.total,
          };
          setTimeout(() => {
            this.$refs.ProTable2.$refs.ProElTable.doLayout();
          }, 300);
          this.$nextTick(() => {
            this.tableData2.forEach((row) => {
              if (
                this.surenewArr.some((item) => row.saleSkuId === item.saleSkuId)
              ) {
                this.$refs.ProTable2.$refs.ProElTable.toggleRowSelection(
                  row,
                  true
                );
              }
            });
          });
        })
        .finally(() => {
          this.$refs.ProTable2
            ? (this.$refs.ProTable2.listLoading = false)
            : null;
        });
    },
    //加载表格
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const result = [
        {
          orderTimeStart: null,
          orderTimeEnd: null,
          data: parameter.createdAt,
        },
        {
          payTimeStart: null,
          payTimeEnd: null,
          data: parameter.payTime,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      delete requestParameters.payTime;
      // if (Array.isArray(requestParameters.orderStatusList)) {
      //   requestParameters.orderStatusList =
      //     requestParameters.orderStatusList.join(",");
      // } else {
      //   requestParameters.orderStatusList = [
      //     requestParameters.orderStatusList,
      //   ].join(",");
      // }
      operatorTradeOrderPageApi(requestParameters)
        .then((res) => {
          const result = [];
          this.localPagination.total = parseInt(res.data.total);
          if (res.data.rows.length === 0) {
            this.tableData = [];
            return;
          }

          res.data.rows.forEach(async (item, index) => {
            const code = item.consigneeRegionCode;
            result[index] = item;
            if (result.length === res.data.rows.length) {
              this.tableData = result;
            }
          });
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getOrderStatisticsFn(requestParameters);
    },
    // nijianchun000
    // 打印票据
    printTicket(row) {
      this.printOrderInfo = row;
      this.printOrderVisible = true;
    },
    // 触发详情
    handleInfo(row, type) {
      this.confirmLoading = true;
      this.dialogTitle =
        type === "info" ? "查看 - " + row.orderNum : "编辑 - " + row.orderNum;
      this.editType = type;
      this.dialogVisible = true;
      this.infoLoading = true;
      operatorTradeOrderDetailApi(row.orderNum)
        .then((res) => {
          this.orderInfo = res.data;
        })
        .finally((_) => {
          this.infoLoading = false;
          this.confirmLoading = false;
        });
      OrderTracesApi(row.orderNum).then((res) => {
        this.tradeInfo = res.data;
        this.tradeInfoDetail = this.tradeInfo[0];
      });
    },
    changeTrade(data) {
      this.tradeInfoDetail = data;
    },
    /**
     * @description 获取省市区区域数据
     * @param node
     * @param {Function} resolve
     * @returns {Promise<void>}
     */
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 处理省市区数据
     * @param list
     * @returns {*}
     */
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    // 通过省市区的code反显全称
    async getRegionFullName(code) {
      if (!code) return "/";
      const thirdCode = code + "";
      const firstCode = thirdCode.substring(0, 2) + "0000";
      const secondCode = thirdCode.substring(0, 4) + "00";
      let firstName = "";
      let secondName = "";
      let thirdName = "";
      try {
        const firstResult = await getProvinceListApi();
        if (firstResult.code === 200 && firstResult.data) {
          firstResult.data.forEach((item) => {
            if (item.code == firstCode) {
              firstName = item.name;
            }
          });
        }
        const secondResult = await getRegionByCodeApi(firstCode);
        if (secondResult.code === 200 && secondResult.data) {
          secondResult.data.forEach((item) => {
            if (item.code == secondCode) {
              secondName = item.name;
            }
          });
        }
        const thirdResult = await getRegionByCodeApi(secondCode);
        if (thirdResult.code === 200 && thirdResult.data) {
          thirdResult.data.forEach((item) => {
            if (item.code == thirdCode) {
              thirdName = item.name;
            }
          });
        }
        return `${firstName}/${secondName}/${thirdName}`;
      } catch (error) {
        Message.error(error.message);
        return "/";
      }
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    /**
     * 显示关闭订单弹框
     */
    showGbddDialog() {
      this.dialogGbddVisible = true;
      this.gbddForm = this.$options.data().gbddForm;
    },
    handleGbddDialogOk() {
      this.$refs["gbddFormRef"].validate((valid) => {
        if (valid) {
          this.dialogGbddVisible = false;
          operatorTradeOrderCloseApi({
            ...this.gbddForm,
            tradeOrderNum: this.orderInfo.tradeOrder.orderNum,
          })
            .then((res) => {
              this.$message.success("关闭订单成功");
            })
            .finally((_) => {
              this.dialogVisible = false;
            });
        } else {
          return false;
        }
      });
    },
    /**
     * 获取订单状态
     */
    getOrderStatusChinese(orderStatus) {
      let value = "";
      switch (orderStatus) {
        case "CLOSED":
          value = "订单关闭";
          this.active = null;
          break;
        case "PAID":
          value = "已支付";
          this.active = 2;
          break;
        case "SUCCESS":
          value = "交易成功";
          this.active = 4;
          break;
        case "WAIT_DELIVER":
          value = "待发货";
          this.active = 2;
          break;
        case "WAIT_PAY":
          value = "待支付";
          this.active = 1;
          break;
        case "WAIT_AUDIT":
          value = "待审核";
          this.active = 1;
          break;
        case "WAIT_RECEIVE":
          value = "待收货";
          this.active = 3;
          break;
      }
      return value;
    },

    getOrderStatusText(orderStatus) {
      let value = "";
      switch (orderStatus) {
        case "ITEM":
          value = "商品直接下单";
          break;
        case "CART":
          value = "购物车下单";
          break;
        case "SELLER_CREATE":
          value = "卖家代客下单";
          break;
        case "CONTRACT_GIFT":
          value = "签约赠送";
          break;
        case "ACTIVYTY_GIFT":
          value = "活动奖励";
          break;
      }
      return value;
    },

    toImage() {
      const loading = Loading.service({
        fullscreen: true,
        text: "正在生成图片...",
      });
      // this.dialogTableHeight = 60 + 34 * this.editData.offerPriceDetails.length;
      this.$nextTick(() => {
        html2canvas(this.$refs.dialogContent).then((canvas) => {
          this.getWaterMark("本印猫", canvas);
          const dataURL = canvas.toDataURL("image/png");
          const a = document.createElement("a");
          a.href = dataURL;
          document.body.append(a);
          a.download = `订单-${this.editData.name}.png`;
          a.click();
          document.body.removeChild(a);
          // this.dialogTableHeight = 400;
          loading.close();
        });
      });
    },
    getWaterMark(str, canvas) {
      const ctx = canvas.getContext("2d");
      ctx.font = "24px Microsoft YaHei";
      ctx.fillStyle = "rgba(0, 0, 0, 0.2)";
      ctx.textAlign = "left";
      ctx.textBaseline = "top";
      ctx.translate(0, -(canvas.width / 2));
      ctx.rotate((20 * Math.PI) / 180);
      // ctx.fillText('str', 200, 400)
      for (let i = -1000; i < canvas.width; i++) {
        for (let j = -1000; j < canvas.height; j++) {
          ctx.fillText(str, i * 150, j * 150);
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.drawer-footer {
  width: 100%;
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  //position: fixed;
  //bottom: 0;
  //background-color: #fff;
  //padding: 10px 0;
  //z-index: 9999;
}
.box {
  display: flex;
  justify-content: space-between;
  flex: 1;
}
.footer-btn {
  display: flex;
  justify-content: center;
  //gap: 20px;
  margin-top: 20px;
}
.box-box {
  .tradedetail {
    width: 100%;
    height: 100%;
    clear: both;
    margin-top: 20px;
  }

  .trade {
    width: 140px;
    padding: 10px 0;
    text-align: center;
    border: 1px solid #555555;
    border-radius: 20px;
    cursor: pointer;
  }

  .info {
    display: inline-block;

    text {
      display: block;
      text-align: left;
    }
  }

  .iconbaoguo {
    font-size: 40px;
  }

  .trade.active {
    border: 1px solid #ee822f;
    color: #ee822f;
  }
}

::v-deep .el-upload--picture-card,
::v-deep .el-upload-list__item {
  width: 120px;
  height: 120px;
}

.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.steps-box {
  position: relative;
  width: 80%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  z-index: 2;
}

.order-fix {
  margin-left: 20px;
  font-size: 14px;

  .red {
    color: #d14b50;
  }

  .text-content {
    display: flex;
    justify-content: space-between;
    .text-p {
      //&.right {
      //  position: relative;
      //  left: 85%;
      //  top: 0;
      //}

      color: #606266;

      .p-label {
        color: #606266;
        font-weight: 700;
      }

      //margin-top: 15px;
      margin: 30px 0;
    }
  }

  .content-fixed {
    display: flex;
    justify-content: space-between;

    .text-p {
      flex: 1;
      display: flex;
    }
  }

  .btn-p {
    margin-top: 15px;

    .el-button {
      padding: 8px 29px;
    }
  }

  .order-border-box {
    border: dashed 1px #ccc;
    padding: 10px;
  }

  .title-p {
    background: #d9d9d9;
    color: #232323;
    padding: 5px;
  }
}

.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}

.tit-boxs {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 0 7px 20px;
  font-size: 16px;
  font-weight: 800;

  // &::before {
  //   content: "";
  //   width: 5px;
  //   height: 20px;
  //   background: #409eff;
  //   display: inline-block;
  //   position: absolute;
  //   left: -1px;
  //   top: 4px;
  // }
}

.totalNumber {
  width: 100%;
  display: flex;
  align-items: center;

  padding: 0 20px;
  box-sizing: border-box;
  color: #606266;
  line-height: 26px;
  font-weight: 700;

  .totalNumber-list {
    margin-right: 30px;
  }
}
</style>
