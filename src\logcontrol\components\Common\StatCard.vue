<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 统计卡片组件
-->
<template>
  <el-card class="stat-card" :class="{ 'loading': loading }">
    <div class="stat-content">
      <div class="stat-icon" :style="{ backgroundColor: color + '20', color: color }">
        <i :class="icon"></i>
      </div>
      <div class="stat-info">
        <div class="stat-value">
          <span v-if="!loading">{{ formatValue(value) }}</span>
          <span v-else>--</span>
          <span class="stat-unit" v-if="unit">{{ unit }}</span>
        </div>
        <div class="stat-title">{{ title }}</div>
        <div class="stat-trend" v-if="trend !== undefined">
          <i :class="trendIcon" :style="{ color: trendColor }"></i>
          <span :style="{ color: trendColor }">{{ Math.abs(trend) }}%</span>
          <span class="trend-text">{{ trendText }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'StatCard',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [Number, String],
      default: 0
    },
    unit: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      required: true
    },
    color: {
      type: String,
      default: '#409EFF'
    },
    trend: {
      type: Number,
      default: undefined
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    trendIcon() {
      if (this.trend > 0) return 'el-icon-top'
      if (this.trend < 0) return 'el-icon-bottom'
      return 'el-icon-minus'
    },
    trendColor() {
      if (this.trend > 0) return '#67C23A'
      if (this.trend < 0) return '#F56C6C'
      return '#909399'
    },
    trendText() {
      if (this.trend > 0) return '较昨日'
      if (this.trend < 0) return '较昨日'
      return '与昨日持平'
    }
  },
  methods: {
    formatValue(value) {
      if (typeof value === 'number') {
        if (value >= 10000) {
          return (value / 10000).toFixed(1) + 'w'
        } else if (value >= 1000) {
          return (value / 1000).toFixed(1) + 'k'
        }
        return value.toString()
      }
      return value
    }
  }
}
</script>

<style lang="scss" scoped>
.stat-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &.loading {
    opacity: 0.6;
  }
  
  .stat-content {
    display: flex;
    align-items: center;
    height: 100%;
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      
      i {
        font-size: 28px;
      }
    }
    
    .stat-info {
      flex: 1;
      
      .stat-value {
        font-size: 28px;
        font-weight: 600;
        color: #303133;
        line-height: 1;
        margin-bottom: 4px;
        
        .stat-unit {
          font-size: 16px;
          font-weight: normal;
          color: #909399;
          margin-left: 4px;
        }
      }
      
      .stat-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 4px;
      }
      
      .stat-trend {
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
        
        .trend-text {
          color: #909399;
        }
      }
    }
  }
}
</style>
