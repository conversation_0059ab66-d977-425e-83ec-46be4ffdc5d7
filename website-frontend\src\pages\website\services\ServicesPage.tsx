import { Helmet } from 'react-helmet-async';
import { Spin } from 'antd';
import { useServices } from '@/hooks/useWebsiteApi';

export default function ServicesPage() {
  const { data: servicesData, isLoading, error } = useServices();



  // 加载状态处理
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" spinning={true}>
          <div className="p-8">服务信息加载中...</div>
        </Spin>
      </div>
    );
  }

  // 错误状态处理
  if (error) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-red-600 mb-2">API调用失败</h3>
          <p className="text-gray-600 mb-4">无法获取服务页面数据</p>
          <p className="text-sm text-gray-500">错误信息: {error?.message || '未知错误'}</p>
        </div>
      </div>
    );
  }

  // 修复：正确解包API响应数据 - 处理包装的响应格式
  const content = (servicesData as any)?.data || servicesData;
  const seoMeta = content?.seoMeta || (servicesData as any)?.seoMeta;


  
  // 安全地获取页面各字段的值
  const getServiceValue = (key: string, defaultValue: any): any => {
    if (!content?.content) return defaultValue;
    try {
      const parsedContent = JSON.parse(content.content);
      return parsedContent.config?.[key] || defaultValue;
    } catch (e) {
      return defaultValue;
    }
  };

  const serviceItems = getServiceValue('serviceItems', [
    { icon: "🖨️", title: "复印机维修", description: "专业维修各品牌复印机，快速诊断问题" },
    { icon: "🖨️", title: "打印机维修", description: "修复各种打印机故障" },
    { icon: "🔧", title: "设备保养", description: "定期保养维护，延长设备使用寿命" },
    { icon: "🛠️", title: "耗材更换", description: "提供原装和兼容耗材" }
  ]);
  
  const heroTitle = getServiceValue('servicesHeroTitle', '专业可靠的设备维修服务');
  const heroSubtitle = getServiceValue('servicesHeroSubtitle', '我们提供全方位的设备维修解决方案');
  const mainDescription = getServiceValue('servicesMainDescription', '我们拥有专业的技术团队和先进的维修设备，为您提供高质量、快速、可靠的维修服务。');
  const processTitle = getServiceValue('servicesProcessTitle', '我们的服务流程');
  const processSteps = getServiceValue('servicesProcessSteps', '故障诊断,维修方案制定,专业维修,质量检测,售后保障');
  const advantageTitle = getServiceValue('servicesAdvantageTitle', '选择我们的优势');
  const advantageList = getServiceValue('servicesAdvantageList', '专业技术团队,快速响应服务,质量保障承诺,合理收费标准,完善售后服务');
  const ctaTitle = getServiceValue('servicesCtaTitle', '需要专业维修服务？');
  const ctaSubtitle = getServiceValue('servicesCtaSubtitle', '立即联系我们，获得快速专业的解决方案');
  const ctaButtonText = getServiceValue('servicesCtaButtonText', '立即咨询');
  
  const steps = processSteps.split(',').map((step: string) => step.trim());
  const advantages = advantageList.split(',').map((advantage: string) => advantage.trim());

  return (
    <>
      <Helmet>
        <title>{seoMeta?.title || '服务介绍 - 复印机维修服务'}</title>
        <meta name="description" content={seoMeta?.description || '专业的复印机、打印机维修服务'} />
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* 英雄区 */}
        <section className="bg-gray-800 text-white py-16 text-center min-h-[276px]">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">{heroTitle}</h1>
            <p className="text-xl">{heroSubtitle}</p>
          </div>
        </section>

        <div className="container mx-auto px-4 py-6 md:py-12">
          {/* 主要描述 */}
          <section className="mb-16 text-center">
            <div className="max-w-4xl mx-auto">
              <p className="text-lg text-gray-700 leading-relaxed">{mainDescription}</p>
            </div>
          </section>

          {/* 服务项目 */}
          <section className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">服务项目</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {serviceItems.map((item: any, index: number) => (
                <div key={index} className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl hover:-translate-y-1 transition-all">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
                  <p className="text-gray-600">{item.description}</p>
              </div>
              ))}
            </div>
          </section>

          {/* 服务流程 */}
          <section className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">{processTitle}</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {steps.map((step: string, index: number) => (
                <div key={index} className="text-center">
                  <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <span className="text-blue-600 font-bold text-xl">{index + 1}</span>
                  </div>
                  <h3 className="font-semibold text-gray-900">{step}</h3>
                </div>
              ))}
            </div>
          </section>

          {/* 服务优势 */}
          <section className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">{advantageTitle}</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
              {advantages.map((advantage: string, index: number) => (
                <div key={index} className="bg-white rounded-lg shadow-md p-6 text-center hover:shadow-xl hover:-translate-y-1 transition-all">
                  <div className="text-blue-600 text-3xl mb-4">✓</div>
                  <h3 className="font-semibold text-gray-900">{advantage}</h3>
                </div>
              ))}
            </div>
          </section>

          {/* CTA */}
          <section className="text-center bg-blue-600 text-white rounded-lg p-12">
            <h2 className="text-3xl font-bold mb-4">{ctaTitle}</h2>
            <p className="text-xl mb-8">{ctaSubtitle}</p>
            <a href="/contact" className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              {ctaButtonText}
            </a>
          </section>
        </div>
      </div>
    </>
  );
} 