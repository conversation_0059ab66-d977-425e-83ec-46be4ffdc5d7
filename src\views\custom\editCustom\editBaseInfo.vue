<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-04 15:43:17
 * @Description: 
 -->
<template>
  <div class="edit-base-info">
    <ProForm
      ref="ProForm"
      :form-param="formParam"
      :form-list="columns"
      :confirm-loading="formLoading"
      :layout="{ formWidth: '100%', labelWidth: '140px' }"
      :open-type="type"
      @proSubmit="formSubmit"
    >
      <template #location>
        <div class="location">
          <el-input
            v-model="formParam.longitude"
            :disabled="type === 'info'"
            type="number"
            placeholder="请输入经度"
          ></el-input>
          <span>-</span>
          <el-input
            v-model="formParam.latitude"
            :disabled="type === 'info'"
            type="number"
            placeholder="请输入维度"
          ></el-input>
        </div>
      </template>

      <template #regionAddress>
        <div class="location">
          <el-cascader
            v-model="addressCode"
            :disabled="type === 'info'"
            style="width: 470px"
            filterable
            clearable
            :options="options1"
            :props="{
              label: 'name',
              value: 'code',
              children: 'children',
              expandTrigger: 'click',
            }"
            @change="handleReginChange"
          ></el-cascader>
          <span></span>
          <el-input
            v-model="formParam.address"
            :disabled="type === 'info'"
            placeholder="请输入详细地址"
            @change="handleRegionAddress"
          ></el-input>
        </div>
      </template>
      <template #position>
        <el-button
          :disabled="type === 'info'"
          icon="el-icon-location-information"
          circle
          @click="handleAddress"
        ></el-button>
      </template>
      <!-- <div class="sp-content editor-box">
        <div v-html="111"></div>
        <ProWangeEditor ></ProWangeEditor>
      </div> -->
      <template #shopRecruitmentImg>
        <ProUpload
          :file-list="formParam.shopRecruitmentImg"
          :type="type"
          :limit="5"
          :multiple="true"
          @uploadSuccess="handleShopRecruitmentUploadSuccess"
          @uploadRemove="handleShopRecruitmentUploadRemove"
        />
        <span v-if="type !== 'info'"
          >仅支持上传png、jpg格式且10M大小内的图片。</span
        >
      </template>

      <!-- <template #licenseImg>
        <ProUpload
          :file-list="formParam.licenseImg"
          :type="type"
          :limit="1"
          @uploadSuccess="handleLicenseImgUploadSuccess"
          @uploadRemove="handleLicenseImgUploadRemove"
        />
        <span v-if="type !== 'info'"
          >仅支持上传png、jpg格式且10M大小内的图片。</span
        >
      </template> -->
    </ProForm>
    <div v-if="type !== 'info'" class="dialog-footer1">
      <div class="btn-box">
        <el-button type="primary" @click="handleOk">保存</el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </div>
    <TXMap ref="map" @confirm="confirmAddress" />
  </div>
</template>

<script>
import ProForm from "@/components/ProForm/index.vue";
import ProUpload from "@/components/ProUpload/index.vue";
import TXMap from "@/components/map/TXMap.vue";
import {
  addCustomerApi,
  updateCustomerApi,
  getGroupListApi,
  memberLevelApi,
} from "@/api/customer";
import { dictTreeByCodeApi } from "@/api/user";
import { regionTreeApi } from "@/api/store";
import { Message } from "element-ui";
export default {
  name: "EditBaseInfo",
  components: { ProForm, ProUpload, TXMap },
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
    baseInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      options1: [],
      formParam: {},
      columns: [
        // {
        //   dataIndex: "seqId",
        //   title: "客户编号",
        //   isForm: true,
        //   formSpan: 8,
        //   valueType: "text",
        // },
        {
          dataIndex: "shopRecruitment",
          title: "店招简称",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
        },
        {
          dataIndex: "subbranch",
          title: "分店号",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
        },
        {
          dataIndex: "source",
          proWangeEditorContent: null,
          title: "客户来源",
          isForm: true,
          clearable: true,
          formSpan: 8,
          disabled: false,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(2300),
          optionskey: {
            label: "label",
            value: "value",
          },
        },

        // {
        //   dataIndex: "name",
        //   title: "用户名称",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 8,
        //   valueType: "input",
        //   prop: [
        //     {
        //       required: true,
        //       message: "请输入用户名称",
        //       trigger: "change",
        //     },
        //   ],
        // },

        // {
        //   dataIndex: "license",
        //   title: "营业执照名称",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 8,
        //   valueType: "input",
        // },
        {
          dataIndex: "businessStatus",
          title: "经营状态",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(200),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择经营状态",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "status",
          title: "客户状态",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(100),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "industryAttr",
          title: "行业属性",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(300),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择行业属性",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "type",
          title: "客户类型",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(400),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "legalPerson",
          title: "法人姓名",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
        },
        {
          dataIndex: "legalPersonTel",
          title: "法人电话",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入法人电话",
          //     trigger: "blur",
          //   },
          // ],
        },

        // {
        //   dataIndex: "captcha",
        //   title: "店铺验证码",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 8,
        //   valueType: "input",
        // },

        // {
        //   dataIndex: "creditCode",
        //   title: "统一信用代码",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 8,
        //   valueType: "input",
        // },

        // {
        //   dataIndex: "legalNativePlace",
        //   title: "法人籍贯",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 8,
        //   valueType: "select",
        //   option: [
        //     {
        //       value: 1,
        //       label: "本地籍",
        //     },
        //     {
        //       value: 2,
        //       label: "湖南籍",
        //     },
        //     {
        //       value: 3,
        //       label: "外地籍",
        //     },
        //   ],
        // },
        // {
        //   dataIndex: "taxpayer",
        //   title: "纳税人识别号",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 8,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "shopSize",
        //   title: "店面大小",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 8,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "shopPersonNum",
        //   title: "店面人数",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 8,
        //   valueType: "input",
        // },
        {
          dataIndex: "groupId",
          title: "关联集团",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
          optionMth: () => getGroupListApi(),
          optionskey: {
            label: "name",
            value: "id",
          },
        },
        {
          dataIndex: "membershipLevel",
          title: "客户等级",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
          optionMth: () => memberLevelApi(),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "location",
          title: "店铺定位",
          isForm: true,
          formSpan: 8,
          formSlot: "location",
        },
        // {
        //   dataIndex: "machineNum",
        //   title: "机器数量",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 8,
        //   valueType: "input",
        // },
        {
          dataIndex: "address",
          title: "详细地址",
          isForm: true,
          formSpan: 16,
          formSlot: "regionAddress",
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入详细地址",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "precisionAddress",
          title: "门牌号",
          isForm: true,
          formSpan: 8,
          valueType: "input",
        },
        {
          dataIndex: "position",
          title: "定位",
          isForm: true,
          formSpan: 2,
          formSlot: "position",
        },
        // {
        //   dataIndex: "createdAt",
        //   title: "入住平台时间",
        //   isForm: true,
        //   formSpan: 8,
        //   valueType: "date-picker",
        //   inputType: "datetime",
        //   pickerFormat: "yyyy-MM-dd HH:mm:ss",
        //   valueFormat: "yyyy-MM-dd HH:mm:ss",
        // },
        // {
        //   dataIndex: "cliMechineNum",
        //   title: "客户端安装数量",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 8,
        //   valueType: "input",
        // },
        {
          dataIndex: "businessScope",
          title: "备注",
          isForm: true,
          clearable: true,
          formSpan: 22,
          valueType: "input",
          inputType: "textarea",
        },
        // {
        //   dataIndex: "shopRecruitmentImg",
        //   title: "店招照片",
        //   isForm: true,
        //   formSpan: 24,
        //   formSlot: "shopRecruitmentImg",
        // },
      ],
      formLoading: false,
      addressCode: [],
    };
  },
  async mounted() {
    await this.getRegion();
    if (this.type === "add") {
      this.formParam = {
        shopRecruitment: "未登店名",
        source: "2301",
        businessStatus: "201",
        type: "401",
        status: "102",
        industryAttr: "301",
      };
    }
    if (this.baseInfo) {
      this.formParam = this.baseInfo ? this.baseInfo : {};
      // 处理区域地址码
      this.addressCode = this.formParam.regionCode;

      // 处理经纬度地址
      await this.handleLocation();
      await this.handleRegionAddress();
    }
    // 处理图片属性
    this.formParam.shopRecruitmentImg ||
      this.$set(this.formParam, "shopRecruitmentImg", []);
    this.formParam.licenseImg || this.$set(this.formParam, "licenseImg", []);
  },
  methods: {
    async formSubmit(val) {
      try {
        const editApi =
          this.type === "add" ? addCustomerApi : updateCustomerApi;
        this.formLoading = true;
        const args = {
          ...val,
          legalPersonTel: val.legalPersonTel
            ? val.legalPersonTel.replace(/\s/g, "")
            : "",
          regionCode: this.addressCode,
        };
        delete args.location;
        delete args.regionAddress;

        const result = await editApi(this.removeEmptyAttr(args));
        if (result.code === 200) {
          // this.loadData()
          Message.success("保存成功");
          this.$emit("refresh");
          this.handleClose();
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.formLoading = false;
      }
    },
    loadData() {},
    handleAddress() {
      this.$refs.map.show(this.formParam.location, this.formParam.address);
    },
    // 确认地址选取
    confirmAddress(e) {
      this.$set(this.formParam, "address", e.address);
      this.$set(this.formParam, "latitude", e.location.latitude);
      this.$set(this.formParam, "longitude", e.location.longitude);
      this.addressCode = e.regionCode;
      this.$set(this.formParam, "location", {
        latitude: e.location.latitude,
        longitude: e.location.longitude,
      });
    },
    handleOk() {
      this.$refs.ProForm.handleSubmit();
    },
    handleClose() {
      this.$emit("closeDrawer");
    },
    async getRegion(node, resolve) {
      const result = await regionTreeApi();
      this.options1 = result.data;
    },
    handleReginChange(val) {
      this.formParam.regionCode = val[val.length - 1];
      this.handleRegionAddress();
    },
    handleLocation(e) {
      // if (this.formParam.latitude && this.formParam.longitude) {
      //   this.formParam.location = `${this.formParam.latitude},${this.formParam.longitude}`;
      // } else {
      //   this.formParam.location = "";
      // }
      if (this.formParam.location) {
        this.$set(this.formParam, "latitude", this.formParam.location.latitude);
        this.$set(
          this.formParam,
          "longitude",
          this.formParam.location.longitude
        );
      } else {
        this.$set(this.formParam, "latitude", "");
        this.$set(this.formParam, "longitude", "");
      }
    },
    handleRegionAddress() {
      if (this.formParam.regionCode && this.formParam.address) {
        this.formParam[
          "regionAddress"
        ] = `${this.formParam.regionCode},${this.formParam.address}`;
      } else {
        this.formParam["regionAddress"] = "";
      }
    },

    // 图片处理
    handleShopRecruitmentUploadSuccess(result) {
      this.formParam.shopRecruitmentImg.push(result);
    },
    handleShopRecruitmentUploadRemove(file) {
      const index = this.formParam.shopRecruitmentImg.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParam.shopRecruitmentImg.splice(index, 1);
    },
    handleLicenseImgUploadSuccess(result) {
      this.formParam.licenseImg.push(result);
    },
    handleLicenseImgUploadRemove(file) {
      const index = this.formParam.licenseImg.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParam.licenseImg.splice(index, 1);
    },
    removeEmptyAttr(obj) {
      for (const key in obj) {
        if (obj[key] === "" || obj[key] === null || obj[key] === undefined) {
          delete obj[key];
        }

        // if (obj[key] instanceof Array && obj[key].length === 0) {
        //   delete obj[key];
        // }

        if (
          Object.prototype.toString.call(obj[key]) === "[object Object]" &&
          Object.keys(obj[key]).length === 0
        ) {
          delete obj[key];
        }
      }
      return obj;
    },
  },
};
</script>

<style lang="scss" scoped>
.edit-base-info {
  height: 100%;
  overflow: auto;
  padding-bottom: 50px;

  .location {
    display: flex;

    & > span {
      display: block;
      margin: 0 10px;
    }
  }
}
</style>
