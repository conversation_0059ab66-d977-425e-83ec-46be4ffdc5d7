<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <!--      <template #btn>-->
      <!--        <el-button-->
      <!--          type="success"-->
      <!--          size="mini"-->
      <!--          icon="el-icon-refresh"-->
      <!--          @click="reModify"-->
      <!--          >重新计算</el-button-->
      <!--        >-->
      <!--      </template>-->
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { productAllApi } from "@/api/dispose";
import { getLifeStatisticsApi } from "@/api/machine";

export default {
  name: "LifeStatistics",
  data() {
    return {
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      queryParam: {},
      columns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          width: 160,
          valueType: "input",
        },
        {
          dataIndex: "articleName",
          title: "零件名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "location",
          title: "颜色位置",
          isTable: true,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "minLifeSpan",
          title: "最小寿命",
          isTable: true,
        },
        {
          dataIndex: "maxLifeSpan",
          title: "最大寿命",
          isTable: true,
        },
        {
          dataIndex: "avgLifeSpan",
          title: "平均寿命",
          isTable: true,
        },
        {
          dataIndex: "counter",
          title: "统计次数",
          isTable: true,
        },

        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        // {
        //   dataIndex: "serial",
        //   title: "系列",
        //   isTable: true,
        // },
        {
          dataIndex: "productIds",
          title: "系列",
          isTable: true,
          isSearch: true,
          minWidth: 150,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
      ],
      tableData: [],
      productIdName: [],
      options: [],
    };
  },
  mounted() {
    this.refresh();
    this.getProductThird();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      getLifeStatisticsApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
    },
    // 修改状态
    reModify() {},
    refresh() {
      this.$refs.ProTable.refresh();
      // this.$refs.ProTable.listLoading = false;
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        this.queryParam.productIds.push(el[el.length - 1]);
      });
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
