<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-17 14:23:15
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-23 16:22:42
 * @Description: 其他收入支出添加
 -->
<template>
  <div class="app-container">
    <ProDrawer
      :value="drawerVisible"
      :title="drawerVisibleTitle"
      size="75%"
      :no-confirm-footer="true"
      :confirm-button-disabled="confirmLoading"
      confirm-text="确认开单"
      @ok="handleDrawerOk"
      @cancel="handleDrawerClose">
      <ProForm
        ref="ProFrom"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :form-param="formParam"
        :form-list="formColumns"
        :open-type="editType"
        :confirm-loading="confirmLoading">
        <template #addAccount>
          <AddOtherForm ref="addOtherForm" v-model="addForm" :type="type" />
          <el-col :span="24" style="text-align: left; margin-top: 10px">
            <div style="display: flex; justify-content: center; gap: 10px">
              <el-button
                id="step1"
                type="primary"
                size="small"
                @click="handleAddTableData">
                添加到下表
              </el-button>
              <el-button size="small" @click="resetForm">重置</el-button>
            </div>
          </el-col>
        </template>
        <template #detail>
          <ProTable
            ref="ProTable"
            :show-loading="false"
            :show-search="false"
            :show-pagination="false"
            :show-setting="false"
            :show-rule="true"
            :columns="columns"
            :data="tableData">
            <template #rule>
              <div class="rules-tips">
                <h4 class="rule-title">
                  温馨提示：当仅需录入单条记录时，您可选择以下任一方式操作：
                </h4>
                <div class="rule-item">
                  1、完成表单填写后直接点击
                  <span class="action">确认开单</span>
                  按钮提交
                </div>
                <div class="rule-item">
                  2、先将填写数据,点击
                  <span class="action">添加</span>
                  按钮添加至
                  <span class="warning">下方表格</span>
                  ，待确认无误后点击
                  <span class="action">确认开单</span>
                  按钮提交
                </div>
                <div class="rule-item">
                  3、在录入过程中，若需
                  <span class="warning">修改数据</span>
                  ，可点击表格中
                  <span class="action">修改</span>
                  按钮进行修改
                </div>
                <div
                  style="
                    display: flex;
                    justify-content: center;
                    margin-top: 10px;
                  ">
                  <el-button type="primary" size="small" @click="handleGuide">
                    操作引导
                  </el-button>
                </div>
              </div>
            </template>
            <template #action="{ row, index }">
              <div class="fixed-width">
                <el-button
                  id="step2"
                  icon="el-icon-edit"
                  @click="handleEdit(row, index)">
                  修改
                </el-button>
                <el-button
                  icon="el-icon-delete"
                  type="danger"
                  @click="handleDelete(row, index)">
                  移除
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
      <template #footer>
        <div v-if="editType !== 'info'">
          <el-button
            :loading="confirmLoading"
            id="step3"
            type="primary"
            @click="handleDrawerOk">
            确认开单
          </el-button>
          <el-button @click="handleDrawerClose">取消</el-button>
        </div>
      </template>
    </ProDrawer>
    <ProDialog
      :value="editDialogVisible"
      title="编辑开单登记内容"
      width="60%"
      confirm-text="确认修改"
      @ok="handleDialogEdit"
      @cancel="editDialogVisible = false">
      <AddOtherForm ref="editOtherForm" v-model="editForm" :type="type" />
    </ProDialog>
  </div>
</template>

<script>
import AddOtherForm from "@/views/financing/components/addOtherForm.vue";
import { cloneDeep } from "lodash";
import { otherIncomeAddApi } from "@/api/operator";

export default {
  name: "AddOtherAccount",
  components: { AddOtherForm },
  props: {
    // 区分是收入还是支出
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      drawerVisible: false,
      drawerVisibleTitle: "",
      editType: "info",
      confirmLoading: false,
      formParam: {},
      formColumns: [
        {
          dataIndex: "add",
          title: "添加",
          isForm: true,
          formOtherSlot: "addAccount",
          formSpan: 24,
        },
        {
          dataIndex: "detail",
          title: "详情",
          isForm: true,
          formOtherSlot: "detail",
          formSpan: 24,
        },
      ],
      // 添加登记form
      addForm: {},
      columns: [
        // {
        //   dataIndex: "type",
        //   title: this.type === "revenue" ? "收入类型" : "支出类型",
        //   isTable: true,
        //   formatter: (row) => {
        //     switch (row.type) {
        //       case "7101":
        //         return "耗材";
        //       case "7102":
        //         return "机器";
        //       case "7103":
        //         return "抄表";
        //       case "7104":
        //         return "维修";
        //       default:
        //         return "";
        //     }
        //   },
        // },
        {
          dataIndex: "customerName",
          title: "客户名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "articleName",
          title: "品名",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isTable: true,
        },
        {
          dataIndex: "actualAmount",
          title: this.type === "revenue" ? "实收金额" : "支出金额",
          isTable: true,
        },
        {
          dataIndex: "remark",
          title: "摘要说明",
          isTable: true,
          width: 200,
        },
        {
          dataIndex: "placeOrder",
          title: "开单时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 160,
        },
      ],
      tableData: [],
      editForm: {}, // 表格数据编辑表单内容
      editIndex: -1, // 编辑索引
      editDialogVisible: false,
      introOption: {
        // 参数对象
        prevLabel: "上一步",
        nextLabel: "下一步",
        skipLabel: "跳过",
        doneLabel: "完成",
        tooltipClass: "intro-tooltip" /* 引导说明文本框的样式 */,
        // highlightClass: 'intro-highlight', /* 说明高亮区域的样式 */
        exitOnEsc: true /* 是否使用键盘Esc退出 */,
        exitOnOverlayClick: false /* 是否允许点击空白处退出 */,
        keyboardNavigation: true /* 是否允许键盘来操作 */,
        showBullets: false /* 是否使用点显示进度 */,
        showProgress: false /* 是否显示进度条 */,
        scrollToElement: true /* 是否滑动到高亮的区域 */,
        overlayOpacity: 0.5, // 遮罩层的透明度 0-1之间
        positionPrecedence: [
          "bottom",
          "top",
          "right",
          "left",
        ] /* 当位置选择自动的时候，位置排列的优先级 */,
        disableInteraction: true /* 是否禁止与元素的相互关联 */,
        hidePrev: true /* 是否在第一步隐藏上一步 */,
        // hideNext: true, /* 是否在最后一步隐藏下一步 */
        steps: [] /* steps步骤，可以写个工具类保存起来 */,
      },
    };
  },

  methods: {
    show(row, type) {
      this.editType = type;
      this.drawerVisibleTitle =
        this.type === "revenue" ? "收入开单登记" : "支出开单登记";
      if (type === "add") {
        this.resetAddForm();
      }
      this.drawerVisible = true;
    },
    handleGuide() {
      this.initGuide();
    },
    initGuide() {
      // 绑定标签元素的选择器数组
      this.introOption.steps = [
        {
          title: "点击添加",
          element: "#step1",
          intro: `填写完整输入框内容后，点击”添加到下表“按钮，将填写内容添加到下方表格。`,
        },
        {
          title: "内容编辑",
          element: "#step2",
          intro: `当填写的信息需要修改时点击表格中操作栏的”编辑“按钮，可以重新对填写的数据进行修改`,
        },
        {
          title: "确认开单",
          element: "#step3",
          intro:
            "当需要开单的内容填写完整后，点击”确认开单“按钮，将最终数据提交，完成开单信息登记。",
        },
      ];
      this.$intro()
        .setOptions(this.introOption)
        // 点击结束按钮后执行的事件
        .oncomplete(() => {
          console.log("点击结束按钮后执行的事件");
        })
        // 点击跳过按钮后执行的事件
        .onexit(() => {
          console.log("点击跳过按钮后执行的事件");
        })
        // 确认完毕之后执行的事件
        .onbeforeexit(() => {
          console.log("确认完毕之后执行的事件");
        })
        .start();
    },
    // 确认编辑
    handleDialogEdit() {
      this.$refs.editOtherForm.$refs.editForm.validate((valid) => {
        if (valid) {
          const result = cloneDeep(this.editForm);

          // 索引边界检查
          if (this.editIndex >= 0 && this.editIndex < this.tableData.length) {
            this.$set(this.tableData, this.editIndex, result);
            this.$message.success("修改成功");
          } else {
            this.$message.error("无效的编辑,请重新填写编辑内容");
          }
          this.editIndex = -1;
          this.editForm = {};
          this.editDialogVisible = false;
        }
      });
    },
    handleEdit(row, index) {
      this.editForm = cloneDeep(row);
      this.editIndex = index;
      this.editDialogVisible = true;
    },
    handleDelete(row, index) {
      this.$confirm("是否确认移除该条数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.tableData.splice(index, 1);
      });
    },
    async handleDrawerOk() {
      // 验证基本输入
      if (!this.tableData.length && !Object.keys(this.addForm).length) {
        this.$message.warning("请先填写开单信息");
        return;
      }
      console.log(this.tableData);
      console.log(this.addForm);
      try {
        if (!this.tableData.length) {
          console.log(1);
          await this.validateAndSubmitForm();
        } else {
          console.log(2);
          await this.confirmAndSubmitData();
        }
      } catch (err) {
        console.log(err);
        // this.$message.warning("请先填写开单信息");
      }
    },

    // 验证表单并提交
    async validateAndSubmitForm() {
      return new Promise((resolve, reject) => {
        this.$refs.addOtherForm.$refs.editForm.validate(async (valid) => {
          if (!valid) return reject();
          try {
            await this.showConfirmation();
            this.confirmLoading = true;
            const args = {
              ...this.addForm,
              businessType: this.type === "revenue" ? 1 : 2,
            };
            this.tableData.push(args);
            await this.submitData();
            resolve();
          } catch (error) {
            reject(error);
          } finally {
            this.confirmLoading = false;
          }
        });
      });
    },

    // 显示确认对话框
    async showConfirmation() {
      return this.$confirm(
        "请仔细核对开单信息，确认无误后提交。提交后将无法修改，是否继续？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      );
    },

    // 提交数据到服务器
    async submitData() {
      this.confirmLoading = true;
      const response = await otherIncomeAddApi(this.tableData);
      this.confirmLoading = false;
      this.$message.success("操作成功");
      this.$emit("refresh");
      this.handleDrawerClose();
      return response;
    },

    // 直接确认并提交现有数据
    async confirmAndSubmitData() {
      await this.showConfirmation();
      await this.submitData();
    },
    // handleDrawerOk() {
    //   if (!this.tableData.length && !Object.keys(this.addForm).length) {
    //     this.$message.warning("请先填写开单信息");
    //     return;
    //   }
    //   try {
    //     this.confirmLoading = true;
    //     if (!this.tableData.length) {
    //       this.$refs.addOtherForm.$refs.editForm.validate((valid) => {
    //         if (valid) {
    //           this.$confirm(
    //             "请仔细核对开单信息，确认无误后提交。提交后将无法修改，是否继续？",
    //             "提示",
    //             {
    //               confirmButtonText: "确定",
    //               cancelButtonText: "取消",
    //               type: "warning",
    //             }
    //           ).then(() => {
    //             console.log(this.addForm, "this.addForm");
    //             const args = {
    //               ...this.addForm,
    //               businessType: this.type === "revenue" ? 1 : 2,
    //             };
    //             this.tableData.push(args);
    //             console.log(this.tableData);
    //             otherIncomeAddApi(this.tableData).then((res) => {
    //               this.$message.success("操作成功");
    //               this.$emit("refresh");
    //               this.handleDrawerClose();
    //             });
    //           });
    //         }
    //       });
    //     } else {
    //       this.$confirm(
    //         "请仔细核对开单信息，确认无误后提交。提交后将无法修改，是否继续？",
    //         "提示",
    //         {
    //           confirmButtonText: "确定",
    //           cancelButtonText: "取消",
    //           type: "warning",
    //         }
    //       ).then(() => {
    //         console.log(this.tableData);
    //         otherIncomeAddApi(this.tableData).then((res) => {
    //           this.$message.success("操作成功");
    //           this.$emit("refresh");
    //           this.handleDrawerClose();
    //         });
    //       });
    //     }
    //   } finally {
    //     this.confirmLoading = false;
    //   }
    // },

    handleDrawerClose() {
      this.addForm = {};
      this.tableData = [];
      this.editForm = {};
      this.drawerVisible = false;
    },
    handleAddTableData() {
      this.$refs.addOtherForm.$refs.editForm.validate((valid) => {
        if (valid) {
          const args = {
            ...this.addForm,
            businessType: this.type === "revenue" ? 1 : 2,
          };
          this.tableData.push(cloneDeep(args));
          this.resetForm();
        }
      });
    },
    resetForm() {
      // if (!Object.keys(this.addForm).length) {
      //   this.$refs.addOtherForm.$refs.editForm.clearValidate();
      // } else {
      //   this.$refs.addOtherForm.$refs.editForm.resetFields();
      // }
      // this.$refs.addOtherForm.$refs.editForm.resetFields();
      this.addForm = {
        // businessType: null,
        // type: null,
        placeOrder: null,
        remark: null,
        customerName: null,
        customerSeq: null,
        articleName: null,
        num: null,
        price: null,
        totalAmount: null,
        actualAmount: null,
      };
    },
    resetAddForm() {
      this.addForm = {
        businessType: null,
        // type: null,
        placeOrder: null,
        remark: null,
        customerName: null,
        customerSeq: null,
        articleName: null,
        num: null,
        price: null,
        totalAmount: null,
        actualAmount: null,
      };
    },
  },
};
</script>

<style scoped lang="scss"></style>
