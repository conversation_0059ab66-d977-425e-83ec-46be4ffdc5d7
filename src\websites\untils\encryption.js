/**
 * 前端AES解密工具
 * 与后端加密算法保持一致：AES/ECB/PKCS5Padding
 */

import CryptoJS from 'crypto-js'

// 默认加密密钥（与后端保持一致）
const DEFAULT_ENCRYPTION_KEY = 'BenYin2025ConfigEncryptSecureKey'

/**
 * 获取加密密钥
 * 优先使用环境变量，降级到默认密钥
 */
function getEncryptionKey() {
  // 安全地访问环境变量，避免在某些环境中process未定义的问题
  try {
    if (typeof process !== 'undefined' && process.env && process.env.VUE_APP_CONFIG_ENCRYPT_KEY) {
      return process.env.VUE_APP_CONFIG_ENCRYPT_KEY
    }
  } catch (e) {
    // 环境变量不可用，使用默认密钥
  }
  
  // 始终返回默认密钥（在当前实现中，前后端使用相同的固定密钥）
  return DEFAULT_ENCRYPTION_KEY
}

/**
 * AES解密函数
 * @param {string} encryptedText Base64编码的加密文本
 * @returns {string} 解密后的明文，解密失败时返回原文本
 */
export function decryptConfigValue(encryptedText) {
  if (!encryptedText || encryptedText.trim().length === 0) {
    return encryptedText
  }

  try {
    const key = CryptoJS.enc.Utf8.parse(getEncryptionKey())
    
    // 使用ECB模式解密（与后端保持一致）
    const decrypted = CryptoJS.AES.decrypt(encryptedText, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    })
    
    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8)
    
    // 检查解密结果是否有效
    if (decryptedText && decryptedText.length > 0) {
      return decryptedText
    } else {
      // 解密结果为空，可能是未加密的数据
      return encryptedText
    }
  } catch (error) {
    // 解密失败时返回原文本，兼容未加密的历史数据
    return encryptedText
  }
}

/**
 * 检查文本是否为Base64格式
 * @param {string} str 待检查的字符串
 * @returns {boolean} 是否为Base64格式
 */
export function isBase64Encoded(str) {
  if (!str || str.length === 0) {
    return false
  }
  
  // Base64字符集检查
  if (!/^[A-Za-z0-9+/]*={0,2}$/.test(str)) {
    return false
  }
  
  // 长度检查（Base64编码后长度应该是4的倍数）
  if (str.length % 4 !== 0) {
    return false
  }
  
  // 尝试解码测试
  try {
    const decoded = atob(str)
    // 如果解码成功且不是空字符串，认为是Base64
    return decoded.length > 0
  } catch (e) {
    return false
  }
}

/**
 * 检查是否为腾讯地图Key格式
 * @param {string} key 待检查的Key
 * @returns {boolean} 是否为有效的腾讯地图Key格式
 */
export function isTencentMapKeyFormat(key) {
  if (!key || typeof key !== 'string') {
    return false
  }
  
  // 腾讯地图Key通常格式：XXXXX-XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
  // 或者其他格式，长度通常在20-50字符之间
  if (key.length < 20 || key.length > 50) {
    return false
  }
  
  // 检查是否包含腾讯地图Key的典型特征
  // 通常包含大写字母、数字和连字符
  if (!/^[A-Z0-9\-]+$/i.test(key)) {
    return false
  }
  
  return true
}

/**
 * 处理从后端获取的腾讯地图Key
 * @param {string} rawKey 原始Key数据
 * @returns {string} 处理后的Key
 */
export function processMapKey(rawKey) {
  if (!rawKey || typeof rawKey !== 'string') {
    return ''
  }

  // 如果已经是有效的腾讯地图Key格式，直接返回
  if (isTencentMapKeyFormat(rawKey)) {
    return rawKey
  }

  // 如果看起来是加密的（Base64格式），尝试解密
  if (isBase64Encoded(rawKey)) {
    const decryptedKey = decryptConfigValue(rawKey)
    if (isTencentMapKeyFormat(decryptedKey)) {
      return decryptedKey
    }
  }

  // 如果不是标准格式但长度合理，可能是其他格式的Key，直接返回
  if (rawKey.length >= 20 && rawKey.length <= 50) {
    return rawKey
  }

  return ''
}

/**
 * 与React项目完全一致的解密处理函数
 * 专门用于处理从getSensitiveConfigEncrypted获取的密文
 * @param {string} encryptedKey 加密的Key
 * @returns {string} 解密后的Key
 */
export function decryptMapKey(encryptedKey) {
  if (!encryptedKey) {
    return ''
  }

  // 直接解密，与React项目逻辑完全一致
  return decryptConfigValue(encryptedKey)
}
