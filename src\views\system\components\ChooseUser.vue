<!--
 * @Description: 部门/角色 人员管理
 * @Autor: shh
 * @Date: 2022-11-16 16:42:14
 * @LastEditors: shanhaihong <EMAIL>
 * @LastEditTime: 2023-12-01 14:57:25
-->
<template>
  <!-- 人员管理  -->
  <ProDrawer
    :value="dialogVisible"
    :title="dialogTitle"
    size="70%"
    :no-footer="true"
    @cancel="handleCancel"
  >
    <ProTable
      ref="ProTable"
      :columns="columns"
      :show-pagination="true"
      :query-param="queryParam"
      :data="tableData"
      :local-pagination="localPagination"
      sticky
      :height="400"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="primary"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增人员
        </el-button>
      </template>
      <template #sex="slotProps">
        {{ slotProps.row.sex.label }}
      </template>
      <template #type="slotProps">
        {{ slotProps.row.type.label }}
      </template>
      <template #state="slotProps">
        {{ slotProps.row.state.label }}
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            移除
          </el-button>
        </span>
      </template>
    </ProTable>
  </ProDrawer>
</template>
<script>
import {
  roleMemberApi,
  roleMemberDelApi,
  departMemberApi,
  departMemberDelApi,
} from "@/api/user";

export default {
  name: "ChooseUser",
  components: {},
  mixins: [],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    dialogTitle: {
      type: String,
      default: "",
    },
    roleId: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      tableData: [],
      selectDataall: [],
      selectData: [],
      queryParam: {
        isFilter: true,
        spareName: null,
      },
      columns: [
        {
          dataIndex: "name",
          title: "姓名",
          isTable: true,
          isSearch: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "mobileNumber",
          title: "电话",
          isTable: true,
        },
        {
          dataIndex: "sex",
          tableSlot: "sex",
          title: "性别",
          isTable: true,
        },
        {
          dataIndex: "type",
          tableSlot: "type",
          title: "类型",
          isTable: true,
        },
        {
          dataIndex: "state",
          tableSlot: "state",
          title: "状态",
          isTable: true,
        },
        {
          dataIndex: "Actions",
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
    };
  },

  computed: {},

  watch: {},
  created() {},

  mounted() {},
  methods: {
    loadData(parameter) {
      this.tableData = [];
      // this.localPagination.total = 0;
      this.queryParam.spareName = parameter.spareName;
      const requestParameters = Object.assign({}, parameter, this.queryParam);
      if (this.type == "role") {
        roleMemberApi(this.roleId, requestParameters)
          .then((res) => {
            this.tableData = res.data.rows;
            this.localPagination.total = parseInt(res.data.total);
          })
          .finally(() => {
            this.$refs.ProTable
              ? (this.$refs.ProTable.listLoading = false)
              : null;
          })
          .catch(() => {});
      } else if (this.type == "depart") {
        departMemberApi(this.roleId, requestParameters)
          .then((res) => {
            this.tableData = res.data.rows;
            this.localPagination.total = parseInt(res.data.total);
          })
          .finally(() => {
            this.$refs.ProTable
              ? (this.$refs.ProTable.listLoading = false)
              : null;
          })
          .catch(() => {});
      }
    },

    handleOk() {
      let arr = [];
      if (this.selectData.length > 0) {
        this.selectData.forEach((item) => {
          arr.push({
            spareId: item.id,
            spareCode: item.spareCode,
            spareName: item.spareName,
            spareNum: item.spareNum,
            spareRemark: item.spareRemark,
            spareUnit: item.spareUnit,
            spareUnitPrice: item.spareUnitPrice,
            expectNum: item.expectNum ? item.expectNum : null,
            devicetypeid: item.devicetypeid ? item.devicetypeid : null,
          });
        });
      } else {
        arr = [];
      }
      this.queryParam = {};
      this.$emit("ok", arr);
    },
    handleCancel() {
      this.queryParam = {};
      this.$emit("cancel");
    },
    handleAdd() {
      this.$emit("add");
    },
    //响应删除
    handleDelete(data) {
      this.$confirm("确认移除该人员?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        if (this.type == "role") {
          roleMemberDelApi(this.roleId, data.id)
            .then(() => {
              this.$emit("delOk");
              this.$message.success("移除成功");
            })
            .finally(() => {
              this.$refs.ProTable.refresh();
            });
        } else if (this.type == "depart") {
          departMemberDelApi(this.roleId, data.id)
            .then(() => {
              this.$message.success("移除成功");
            })
            .finally(() => {
              this.$refs.ProTable.refresh();
            });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
