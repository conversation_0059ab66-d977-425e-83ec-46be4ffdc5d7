<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-30 18:43:00
 * @Description: 耗材盘点
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          icon="el-icon-plus"
          type="success"
          size="mini"
          @click="handleAdd"
        >
          新增耗材盘点单
        </el-button>
      </template>
      <template #actions="{ row }">
        <span class="fixed-width">
          <el-button icon="el-icon-view" @click="handleViewOrEdit(row, 'info')">
            查看
          </el-button>
          <el-button
            v-auth="['@ums:manage:inventory:download']"
            @click="handleExport(row)"
          >
            导出盘点数据
          </el-button>
          <el-button
            v-if="row.stockStatus?.value === 'WAIT_APPROVE'"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleViewOrEdit(row, 'audit')"
          >
            复核
          </el-button>
          <!--<el-button-->
          <!--  v-if="row.stockStatus.value === 'PASS'"-->
          <!--  @click="handleStash(row)"-->
          <!--&gt;-->
          <!--  一键入库-->
          <!--</el-button>-->
          <el-button
            v-if="
              row.stockStatus?.value === 'STASH' ||
              row.stockStatus?.value === 'REJECT'
            "
            icon="el-icon-circle-check"
            @click="handleViewOrEdit(row, 'edit')"
          >
            继续盘点
          </el-button>
          <el-button
            v-if="row.stockStatus?.value === 'PASS'"
            type="danger"
            icon="el-icon-error"
            @click="handleLoss(row)"
          >
            报损报溢
          </el-button>
          <el-button
            v-if="
              row.stockStatus?.value === 'STASH' ||
              row.stockStatus?.value === 'REJECT'
            "
            type="danger"
            icon="el-icon-circle-close"
            @click="handleClose(row.id)"
          >
            关闭
          </el-button>
        </span>
      </template>
    </ProTable>
    <ProDrawer
      :value="checkInventory"
      :title="checkTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :no-footer="editType === 'info'"
      :no-confirm-footer="true"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="editForm"
        :form-param="editForm"
        :form-list="columns"
        :confirm-loading="editFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="editType"
      >
        <template #type>
          <div>{{ editForm.type?.label }}</div>
        </template>
        <template #createdBy>
          <div>{{ editForm.createdBy }}</div>
        </template>
        <template #inventoryDetailList>
          <div class="inventory-table">
            <p class="tit-box">耗材盘点</p>
            <ProTable
              ref="inventoryProTable"
              :row-key="(row) => row.articleCode"
              :columns="inventoryColumns"
              :data="groupedInventoryTableData"
              :local-pagination="detailLocalPagination"
              :query-param="inventoryQueryParam"
              :show-loading="inventoryLoading"
              :show-search="inventorySearch"
              :show-setting="inventorySearch"
              height="50vh"
              @loadData="tempLoadInventoryData"
            >
              <!--editType === 'info' ? loadDetailData : loadInventoryData-->
              <template #btn>
                <el-button
                  v-if="editType === 'add'"
                  type="success"
                  size="mini"
                  @click="handleCheck"
                >
                  盘点
                </el-button>
                <el-button
                  v-if="editType === 'add'"
                  v-auth="['@ums:manage:inventory:download']"
                  type="success"
                  :loading="exportLoading"
                  icon="el-icon-download"
                  size="mini"
                  @click="handleExportStock"
                >
                  导出库存数据
                </el-button>
                <div
                  v-if="editType === 'edit' || editType === 'add'"
                  class="title-box-right"
                >
                  <div>
                    库存数量：{{ inventoryTotalData?.inventoryNum || 0 }}
                  </div>
                  <div>
                    库存金额：{{ inventoryTotalData?.inventoryAmount || 0 }}
                  </div>
                </div>
              </template>
              <template #location="{ row }">
                <el-input
                  v-if="editType !== 'info'"
                  v-model="row.location"
                  placeholder="储位"
                  size="small"
                  style="width: 100%"
                  :disabled="editType === 'info' || editType === 'audit'"
                  @change="handleLocationChange(row)"
                />
                <div v-else>{{ row?.location }}</div>
              </template>
              <template #manufacturerChannel="{ row }">
                <el-select
                  v-if="editType !== 'info'"
                  v-model="row.manufacturerChannel"
                  :disabled="editType === 'info' || editType === 'audit'"
                  size="small"
                  style="width: 100%"
                  placeholder="制造渠道"
                  @change="(e) => handleManufacturerChannelChange(e, row)"
                >
                  <el-option
                    v-for="item in manufacturerChannelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div v-else>
                  {{ row.manufacturerChannel?.label }}
                </div>
              </template>
              <template #picture="{ row }">
                <el-image
                  v-if="
                    row.imageFiles &&
                    Array.isArray(row.imageFiles) &&
                    row.imageFiles.length > 0
                  "
                  style="width: 100px; height: 100px"
                  :src="row.imageFiles[0].url"
                  :preview-src-list="[row.imageFiles[0].url]"
                />
                <div v-else>暂无</div>
              </template>
              <template #batchInfo="{ row }">
                <el-popover trigger="hover" placement="top">
                  <div class="content-popover">
                    <div v-for="item in row.batchInfo" :key="item.batchCode">
                      <p>批次号：{{ item.batcheCode }}</p>
                      <p>价格：{{ item.price }}</p>
                      <p>数量：{{ item.num }}</p>
                    </div>
                  </div>
                  <div slot="reference" class="name-wrapper">
                    <div>{{ batchCodeText(row.batchInfo) }}</div>
                  </div>
                </el-popover>
              </template>

              <template #checkAction="{ row }">
                <div v-if="editType !== 'info' && editType !== 'audit'" class="check-action-cell">
                  <div v-if="!getCheckStatus(row.articleCode).isChecked" class="quick-check">
                    <el-input-number
                      v-model="quickCheckValues[row.articleCode]"
                      :min="0"
                      size="small"
                      placeholder="输入总数"
                      style="width: 100px; margin-right: 8px"
                      @blur="quickConfirm(row.articleCode)"
                      @keydown.enter.native="quickConfirm(row.articleCode)"
                    />
                    <el-button size="small" @click="openDetailModal(row.articleCode)">详情</el-button>
                  </div>
                  <div v-else class="checked-status">
                    <el-tag :type="getCheckStatus(row.articleCode).type" size="small">
                      {{ getCheckStatus(row.articleCode).text }} ({{ getCheckStatus(row.articleCode).totalCheckedNum }})
                    </el-tag>
                    <el-button size="small" type="text" @click="openDetailModal(row.articleCode)">编辑</el-button>
                  </div>
                </div>
                <div v-else>-</div>
              </template>
            </ProTable>
          </div>
        </template>
      </ProForm>
      <template #footer>
        <div
          v-if="!(editType === 'info' || editType === 'audit')"
          class="footer"
        >
          <el-button
            :loading="stagingBtnLoading"
            type="info"
            @click="handleSubmit('staging')"
          >
            暂存
          </el-button>
          <el-button
            :loading="submitBtnLoading"
            type="primary"
            @click="confirmSubmit"
          >
            确认提交
          </el-button>
          <el-button @click="closeDrawer">关闭</el-button>
        </div>
        <div v-if="editType === 'audit'" class="footer">
          <el-button type="danger" @click="handleAudit('REJECT', 'error')">
            驳回
          </el-button>
          <el-button type="primary" @click="handleAudit('PASS', 'success')">
            审核通过
          </el-button>
          <el-button @click="closeDrawer">取消</el-button>
        </div>
      </template>
    </ProDrawer>
    <ProDrawer
      :value="checkLosses"
      :title="'报损报溢'"
      size="80%"
      :confirm-loading="confirmLoading"
      :no-footer="true"
      @cancel="closeLossesDrawer"
    >
      <ProForm
        ref="editForm"
        :form-param="lossesForm"
        :form-list="columns"
        :confirm-loading="editFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="editType"
      >
        <template #type>
          <div>{{ lossesForm.type?.label }}</div>
        </template>
        <template #createdBy>
          <div>{{ lossesForm.createdBy }}</div>
        </template>
        <template #lossesDetailList>
          <ProTable
            ref="lossesProTable"
            :columns="lossesColumns"
            :data="lossesTableData"
            :local-pagination="lossesLocalPagination"
            :row-key="(row) => row.id"
            :show-loading="false"
            :show-setting="false"
            :show-search="false"
            show-pagination
            :height="600"
            sticky
            @loadData="loadLossesData"
          >
            <template #picture="{ row }">
              <el-image
                v-if="
                  row.imageFiles &&
                  Array.isArray(row.imageFiles) &&
                  row.imageFiles.length > 0
                "
                style="width: 100px; height: 100px"
                :src="row?.imageFiles[0]?.url"
                :preview-src-list="[row?.imageFiles[0]?.url]"
              />
              <div v-else>暂无</div>
            </template>
            <template #batchInfo="{ row }">
              <el-popover trigger="hover" placement="top">
                <div class="content-popover">
                  <div v-for="item in row.batchInfo" :key="item.batchCode">
                    <p>批次号：{{ item.batcheCode }}</p>
                    <p>价格：{{ item.price }}</p>
                    <p>数量：{{ item.num }}</p>
                  </div>
                </div>
                <div slot="reference" class="name-wrapper">
                  <div>{{ batchCodeText(row.batchInfo) }}</div>
                </div>
              </el-popover>
            </template>
            <template #stockNum="{ row }">
              <el-input-number
                v-model="row.stockNum"
                :min="0"
                size="small"
                style="width: 100%"
                controls-position="right"
                placeholder="盘点数量"
                :disabled="editType === 'info' || editType === 'audit'"
                @change="handleStockNumChange(row)"
              />
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>

    <!-- 盘点详情模态框 -->
    <el-dialog
      :visible.sync="checkDetailModal"
      title="盘点详情"
      width="60%"
      :close-on-click-modal="false"
      @close="closeDetailModal"
    >
      <div class="check-detail-content">
        <!-- 物品主信息 -->
        <div class="item-info">
          <el-image
            v-if="currentCheckItem.imageFiles && currentCheckItem.imageFiles.length > 0"
            :src="currentCheckItem.imageFiles[0].url"
            style="width: 80px; height: 80px; border-radius: 8px; margin-right: 16px"
            fit="cover"
          />
          <div class="item-details">
            <h4>{{ currentCheckItem.articleName }}</h4>
            <p>物品编码: {{ currentCheckItem.articleCode }}</p>
            <p>OEM号: {{ currentCheckItem.numberOem }}</p>
            <p>制造渠道: {{ currentCheckItem.manufacturerChannel?.label }}</p>
          </div>
        </div>

        <!-- 盘点总数输入 -->
        <div class="total-check-section">
          <label>盘点总数</label>
          <el-input-number
            v-model="modalTotalCheckNum"
            :min="0"
            placeholder="手动输入实盘总数"
            style="width: 200px"
          />
          <p v-if="feedbackMessage" class="feedback-message">{{ feedbackMessage }}</p>
        </div>

        <!-- 批次详情 -->
        <div class="batch-section">
          <h5>批次明细</h5>
          <div v-for="batch in currentCheckItem.batches" :key="batch.batcheCode" class="batch-item">
            <div class="batch-info">
              <div class="batch-field">
                <label>批次号</label>
                <span>{{ batch.batcheCode }}</span>
              </div>
              <div class="batch-field">
                <label>库存数量</label>
                <span>{{ batch.num }}</span>
              </div>
              <div class="batch-field">
                <label>盘点数量</label>
                <el-input-number
                  v-model="batch.checkedNum"
                  :min="0"
                  size="small"
                  @change="updateBatchDifference(batch)"
                />
              </div>
              <div class="batch-field">
                <label>差异</label>
                <span :class="getDifferenceClass(batch.difference)">{{ getDifferenceText(batch.difference) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDetailModal">取消</el-button>
        <el-button type="primary" @click="confirmDetailCheck">确认盘点</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getWarehouseInventoryListApi,
  checkWarehouseInventoryApi,
  warehouseListApi,
  saveWarehouseInventoryApi,
  submitWarehouseInventoryApi,
  checkWarehouseInventoryDetailApi,
  lossAndOverflowPageApi,
  deleteWarehouseInventoryApi,
  exportWarehouseConsumableInventoryApi,
  batchInWarehouseApi,
  getWarehouseInventoryDetailListApi,
  getWarehouseInventoryStatisticsApi,
  storageExportApi,
} from "@/api/store";
import { filterParam, filterParamRange, transformFormParams } from "@/utils";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
import { Message } from "element-ui";
import { handleExcelExport } from "@/utils/exportExcel";
export default {
  name: "ConsumableInventory",
  data() {
    return {
      editType: "info", // info:查看  edit: 编辑  add:新增 audit:审核
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      tableData: [],
      columns: [
        {
          title: "盘点单号",
          dataIndex: "code",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 130,
        },
        // {
        //   title: "盘点单号",
        //   dataIndex: "code",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 24,
        // },
        {
          title: "归属仓库",
          dataIndex: "warehouseName",
          isTable: true,
          isSearch: true,
          valueType: "select",
          isForm: true,
          disabled: false,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择归属仓库",
              trigger: "change",
            },
          ],
          formSpan: 6,
          width: 120,
        },
        {
          title: "仓库类型",
          dataIndex: "type",
          isTable: true,
          formatter: (row) => row.type?.label,
          isSearch: true,
          clearable: true,
          isForm: true,
          disabled: false,
          valueType: "select",
          formSlot: "type",
          formSpan: 6,
          option: [],
          optionMth: () => dictTreeByCodeApi(1300),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          title: "库存数量",
          dataIndex: "inventoryNum",
          isTable: true,
        },
        {
          title: "库存金额",
          dataIndex: "inventoryAmount",
          isTable: true,
          minWidth: 100,
        },
        {
          title: "盘点数量",
          dataIndex: "stockNum",
          isTable: true,
        },
        {
          title: "盘点金额",
          dataIndex: "stockAmount",
          isTable: true,
          minWidth: 100,
        },
        {
          title: "盘点人",
          dataIndex: "createdBy",
          isTable: true,
          isSearch: true,
          isForm: true,
          disabled: false,
          valueType: "input",
          formSlot: "createdBy",
          align: "center",
          formSpan: 6,
          formatter: (row) => row.createdBy?.name,
          width: 100,
        },
        {
          title: "盘点日期",
          dataIndex: "createdAt",
          isForm: true,
          formSpan: 6,
          clearable: true,
          valueType: "date-picker",
          pickerType: "datetime",
          disabled: true,
        },
        {
          title: "盘点日期",
          dataIndex: "createdAt",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          width: 150,
        },
        {
          title: "耗材盘点",
          dataIndex: "inventoryDetail",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "inventoryDetailList",
        },
        {
          title: "报损报溢",
          dataIndex: "lossesDetailList",
          isForm: false,
          formSpan: 24,
          formSlot: "lossesDetailList",
        },
        {
          title: "复核人",
          dataIndex: "recheckName",
          isTable: true,
          align: "center",
          width: 100,
        },
        {
          title: "复核时间",
          dataIndex: "recheckAt",
          isTable: true,
          width: 150,
        },
        {
          title: "状态",
          dataIndex: "stockStatus",
          isTable: true,
          formatter: (row) => row.stockStatus?.label,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [
            {
              label: "待审核",
              value: "WAIT_APPROVE",
            },
            {
              label: "通过",
              value: "PASS",
            },
            {
              label: "驳回",
              value: "REJECT",
            },
            {
              label: "暂存",
              value: "STASH",
            },
            {
              label: "关闭",
              value: "CLOSE",
            },
          ],
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 340,
        },
      ],
      checkInventory: false,
      checkTitle: "查看 —— ",
      confirmLoading: false,
      editForm: {},
      editFormLoading: false,
      inventoryQueryParam: {},
      stagingBtnLoading: false,
      submitBtnLoading: false,
      inventoryColumns: [
        {
          title: "储位",
          dataIndex: "location",
          isTable: true,
          tableSlot: "location",
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          title: "物品编号",
          dataIndex: "articleCode",
          isTable: true,
          isSearch: true,
          tooltip: false,
          valueType: "input",
          width: 160,
        },
        {
          title: "图片",
          dataIndex: "picture",
          isTable: true,
          tableSlot: "picture",
          width: 100,
        },
        {
          title: "物品名称",
          dataIndex: "articleName",
          isTable: true,
          width: 140,
          tooltip: false,
          isSearch: true,
          valueType: "input",
        },
        {
          title: "OEM编号",
          dataIndex: "numberOem",
          isTable: true,
          isSearch: true,
          valueType: "input",
          tooltip: false,
          width: 120,
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          isTable: true,
          tooltip: false,
          width: 120,
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商名称",
          isSearch: true,
          placeholder: "制造商物品名称",
          valueType: "input",
        },
        {
          dataIndex: "manufacturerGoodsCode",
          title: "制造商物品编号",
          isTable: true,
          tooltip: false,
          width: 120,
        },
        {
          dataIndex: "manufacturerGoodsCode",
          title: "制造商编号",
          isSearch: true,
          placeholder: "制造商物品编号",
          valueType: "input",
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          // formatter: (row) => row.manufacturerChannel?.label,
          tableSlot: "manufacturerChannel",
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
          width: 100,
        },
        {
          dataIndex: "inPrice",
          title: "采购单价",
          isTable: true,
          width: 100,
        },
        {
          title: "批次号",
          dataIndex: "batchCode",
          isTable: true,
          tooltip: false,
          width: 100,
          // tableSlot: "batchInfo",
        },
        {
          title: "库存数量",
          dataIndex: "num",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          align: "center",
          fixed: "right",
        },
        {
          title: "盘点操作",
          dataIndex: "checkAction",
          isTable: true,
          width: 200,
          tableSlot: "checkAction",
          fixed: "right",
        },
        {
          dataIndex: "isTake",
          title: "是否盘点",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "isNormal",
          title: "是否正常",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
      ],
      inventoryTotalData: {},
      inventoryLoading: true,
      inventorySearch: true,
      inventoryTableData: [],
      groupedInventoryTableData: [], // 分组后用于表格显示的数据
      processedInventoryData: {}, // 按articleCode分组的数据
      checkStatus: {}, // 盘点状态跟踪
      detailLocalPagination: {
        pageSize: 99,
        pageNumber: 1,
        total: 0,
      },
      cacheInventoryData: [],
      checkLosses: false,
      lossesQueryParam: {},
      lossesForm: {},
      lossesLocalPagination: {
        pageSize: 99,
        pageNumber: 1,
        total: 0,
      },

      lossesColumns: [
        {
          title: "物品编号",
          dataIndex: "articleCode",
          isTable: true,
          width: 150,
        },
        {
          title: "物品名称",
          dataIndex: "articleName",
          isTable: true,
          width: 120,
        },
        {
          title: "OEM编号",
          dataIndex: "numberOem",
          isTable: true,
          width: 150,
        },
        {
          title: "批次号",
          dataIndex: "batchInfo",
          isTable: true,
          tooltip: false,
          tableSlot: "batchInfo",
        },
        {
          title: "图片",
          dataIndex: "picture",
          isTable: true,
          tableSlot: "picture",
          width: 150,
        },
        {
          title: "单位",
          dataIndex: "unit",
          isTable: true,
        },
        {
          title: "单价",
          dataIndex: "price",
          isTable: true,
        },

        {
          title: "金额",
          dataIndex: "inventoryAmount",
          isTable: true,
        },
        {
          title: "库存数量",
          dataIndex: "inventoryNum",
          isTable: true,
        },
        {
          title: "库存盘点",
          dataIndex: "stockNum",
          isTable: true,
          // tableSlot: "stockNum",
        },
        {
          title: "损益值",
          dataIndex: "discrepancyNum",
          isTable: true,
        },
        {
          title: "损益金额",
          dataIndex: "discrepancyAmount",
          isTable: true,
        },
      ],
      lossesTableData: [],
      manufacturerChannelOptions: [],
      takeStockId: "",
      tempLoadInventoryData: null,
      exportLoading: false,
      // 盘点详情模态框相关
      checkDetailModal: false,
      currentCheckItem: {},
      modalTotalCheckNum: 0,
      feedbackMessage: "",
      quickCheckValues: {}, // 快速盘点输入值
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
    this.getManufacturerChannelOptions();
  },
  methods: {
    // 列表数据
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startTime: null,
          endTime: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      getWarehouseInventoryListApi({ ...requestParameters, stockType: 0 })
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    // 盘点数据
    loadInventoryData(parameter) {
      this.inventoryQueryParam = filterParam(
        Object.assign({}, this.inventoryQueryParam, parameter)
      );
      const searchRange = [
        {
          minNum: null,
          maxNum: null,
          data: parameter.num,
        },
      ];
      filterParamRange(this, this.inventoryQueryParam, searchRange);
      const requestParameters = cloneDeep(this.inventoryQueryParam);
      delete requestParameters.inventoryNum;
      const params = {
        ...requestParameters,
        stockType: 0,
        warehouseId: this.editForm.warehouseId
          ? this.editForm.warehouseId
          : this.editForm.warehouseName,
        takeStockId: this.editForm.id || this.takeStockId || null,
      };
      checkWarehouseInventoryApi(params)
        .then((res) => {
          // 原始数据处理
          const rawData = res.data.rows;
          this.inventoryTableData = rawData;

          // 数据分组处理：按 articleCode 分组
          const groupedData = rawData.reduce((acc, item) => {
            const code = item.articleCode;
            if (!acc[code]) {
              acc[code] = {
                ...item, // 使用第一个遇到的项目作为基础信息
                batches: [],
                totalInventoryNum: 0,
                totalInventoryAmount: 0,
              };
            }

            // 处理批次信息 - 从 batchInfo 数组中获取批次数据
            if (item.batchInfo && Array.isArray(item.batchInfo)) {
              item.batchInfo.forEach(batch => {
                acc[code].batches.push({
                  id: item.id,
                  batcheCode: batch.batcheCode || 'N/A',
                  num: batch.num || 0,
                  price: parseFloat(batch.price || 0),
                  checkedNum: batch.num || 0, // 默认盘点数量等于库存数量
                  difference: 0
                });
                acc[code].totalInventoryNum += (batch.num || 0);
                // 计算批次金额：数量 * 单价
                acc[code].totalInventoryAmount += (batch.num || 0) * parseFloat(batch.price || 0);
              });
            } else {
              // 如果没有 batchInfo，使用原有逻辑作为后备
              acc[code].batches.push({
                id: item.id,
                batcheCode: item.batchCode || 'N/A',
                num: item.num || 0,
                price: parseFloat(item.price || 0),
                checkedNum: item.stockNum || item.num || 0,
                difference: 0
              });
              acc[code].totalInventoryNum += (item.num || 0);
              acc[code].totalInventoryAmount += parseFloat(item.inventoryAmount || 0);
            }

            return acc;
          }, {});

          this.processedInventoryData = groupedData;

          // 生成分组后的表格显示数据
          let groupedTableData = Object.values(groupedData).map(item => ({
            ...item,
            // 使用总数量和总金额
            num: item.totalInventoryNum,
            inventoryAmount: item.totalInventoryAmount.toFixed(2),
            // 批次信息显示为批次数组
            batchInfo: item.batches.map(batch => ({
              batcheCode: batch.batcheCode,
              price: batch.price,
              num: batch.num
            })),
            // 添加批次号显示（显示第一个批次号，如果有多个则显示"多批次"）
            batchCode: item.batches.length === 1 ? item.batches[0].batcheCode : `多批次(${item.batches.length})`
          }));

          // 应用搜索过滤
          if (this.inventoryQueryParam.articleCode) {
            groupedTableData = groupedTableData.filter(item =>
              item.articleCode.toLowerCase().includes(this.inventoryQueryParam.articleCode.toLowerCase())
            );
          }
          if (this.inventoryQueryParam.articleName) {
            groupedTableData = groupedTableData.filter(item =>
              item.articleName.toLowerCase().includes(this.inventoryQueryParam.articleName.toLowerCase())
            );
          }
          if (this.inventoryQueryParam.numberOem) {
            groupedTableData = groupedTableData.filter(item =>
              item.numberOem && item.numberOem.toLowerCase().includes(this.inventoryQueryParam.numberOem.toLowerCase())
            );
          }
          if (this.inventoryQueryParam.manufacturerGoodsName) {
            groupedTableData = groupedTableData.filter(item =>
              item.manufacturerGoodsName && item.manufacturerGoodsName.toLowerCase().includes(this.inventoryQueryParam.manufacturerGoodsName.toLowerCase())
            );
          }
          if (this.inventoryQueryParam.manufacturerGoodsCode) {
            groupedTableData = groupedTableData.filter(item =>
              item.manufacturerGoodsCode && item.manufacturerGoodsCode.toLowerCase().includes(this.inventoryQueryParam.manufacturerGoodsCode.toLowerCase())
            );
          }
          if (this.inventoryQueryParam.location) {
            groupedTableData = groupedTableData.filter(item =>
              item.location && item.location.toLowerCase().includes(this.inventoryQueryParam.location.toLowerCase())
            );
          }

          this.groupedInventoryTableData = groupedTableData;

          // 处理缓存数据
          const cacheInventoryMap = this.cacheInventoryData.reduce(
            (map, item) => {
              map[item.articleCode] = item.stockNum;
              return map;
            },
            {}
          );
          this.inventoryTableData.forEach((item) => {
            if (
              Object.prototype.hasOwnProperty.call(
                cacheInventoryMap,
                item.articleCode
              )
            ) {
              item.stockNum = cacheInventoryMap[item.articleCode];
            }
          });
          this.inventoryTableData.forEach((item) => {
            return transformFormParams(item);
          });
          // 更新分页总数为分组后的数量
          this.detailLocalPagination.total = this.groupedInventoryTableData.length;
        })
        .finally(() => {
          this.$refs.inventoryProTable
            ? (this.$refs.inventoryProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getInventoryDetailList(params);
    },
    // 获取盘点单统计数据
    getInventoryDetailList(params) {
      getWarehouseInventoryStatisticsApi(params).then((res) => {
        this.inventoryTotalData = res.data;
      });
    },
    // 加载盘点明细列表
    loadDetailData(parameter) {
      this.inventoryQueryParam = filterParam(
        Object.assign({}, this.inventoryQueryParam, parameter)
      );
      const requestParameters = cloneDeep(this.inventoryQueryParam);
      const args = {
        ...requestParameters,
        takeStockId: this.editForm.id,
      };
      getWarehouseInventoryDetailListApi(args)
        .then((res) => {
          this.inventoryTableData = res.data.rows;
          this.detailLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.inventoryProTable
            ? (this.$refs.inventoryProTable.listLoading = false)
            : null;
        });
      // this.getInventoryDetailList(args);
    },
    // 报损报溢数据
    loadLossesData(parameter) {
      this.lossesQueryParam = filterParam(
        Object.assign({}, this.lossesQueryParam, parameter)
      );
      const requestParameters = cloneDeep(this.lossesQueryParam);
      lossAndOverflowPageApi({
        ...requestParameters,
        takeStockCode: this.lossesForm.code,
      })
        .then((res) => {
          this.lossesTableData = res.data.rows;
          this.lossesLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.lossesProTable
            ? (this.$refs.lossesProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    /**
     * @description  新增
     */
    handleAdd() {
      this.reset();
      this.editType = "add";
      this.checkTitle = "新增盘点";
      this.columns.forEach((item, index) => {
        if (
          item.dataIndex === "createdBy" ||
          item.dataIndex === "type" ||
          item.dataIndex === "lossesDetailList"
        ) {
          this.columns[index].isForm = false;
        }
        if (item.dataIndex === "inventoryDetail") {
          this.columns[index].isForm = true;
        }
        if (item.dataIndex === "warehouseName") {
          this.columns[index].disabled = false;
        }
      });
      this.inventoryColumns.forEach((item, index) => {
        if (
          item.dataIndex === "isTake" ||
          item.dataIndex === "isNormal" ||
          item.dataIndex === "inventoryNum"
        ) {
          this.inventoryColumns[index].isSearch = true;
        }
      });
      this.checkInventory = true;
      this.tempLoadInventoryData = this.loadInventoryData;
      this.$nextTick(() => {
        this.$refs.inventoryProTable
          ? (this.$refs.inventoryProTable.listLoading = false)
          : null;
      });
    },
    /**
     * @description 库存数量
     * @param {Object} row
     */
    handleStockNumChange(row) {
      if (row.stockNum < 0) {
        row.stockNum = 0;
        this.$message.error("盘点数量不能小于0！");
      }
      const isFind = this.cacheInventoryData.some(
        (item) =>
          item.articleCode === row.articleCode &&
          item.batchCode === row.batchCode
      );
      console.log("isFind", isFind);
      if (!isFind) {
        this.cacheInventoryData.push(row);
      } else {
        this.cacheInventoryData.forEach((item, index) => {
          if (
            item.articleCode === row.articleCode &&
            item.batchCode === row.batchCode
          ) {
            this.cacheInventoryData[index].stockNum = row.stockNum;
          }
        });
      }
    },
    handleLocationChange(row) {
      const isFind = this.cacheInventoryData.some(
        (item) =>
          item.articleCode === row.articleCode &&
          item.batchCode === row.batchCode
      );
      if (!isFind) {
        this.cacheInventoryData.push(row);
      } else {
        this.cacheInventoryData.forEach((item, index) => {
          if (
            item.articleCode === row.articleCode &&
            item.batchCode === row.batchCode
          ) {
            this.cacheInventoryData[index].location = row.location;
          }
        });
      }
    },
    handleManufacturerChannelChange(e, row) {
      const isFind = this.cacheInventoryData.some(
        (item) =>
          item.articleCode === row.articleCode &&
          item.batchCode === row.batchCode
      );
      if (!isFind) {
        this.cacheInventoryData.push(row);
      } else {
        this.cacheInventoryData.forEach((item, index) => {
          if (
            item.articleCode === row.articleCode &&
            item.batcheCode === row.batcheCode
          ) {
            this.cacheInventoryData[index].manufacturerChannel = e;
          }
        });
      }
    },
    // 暂存 Or 提交
    handleSubmit(type) {
      this.cacheInventoryData = this.cacheInventoryData.filter(
        (item) => item !== null && item !== undefined && item !== ""
      );
      if (type === "submit") {
        this.submitBtnLoading = true;
      } else {
        this.stagingBtnLoading = true;
      }
      const params = {
        stockType: 0,
        id: this.editForm.id || this.takeStockId,
        warehouseId: this.editForm.warehouseId
          ? this.editForm.warehouseId
          : this.editForm.warehouseName,
        takeDetails: this.cacheInventoryData,
      };
      const editApi =
        type === "submit"
          ? submitWarehouseInventoryApi
          : saveWarehouseInventoryApi;
      editApi(params)
        .then((res) => {
          this.$message.success(type === "submit" ? "保存成功" : "暂存成功");
          if (type === "submit") {
            this.closeDrawer();
            this.$refs.ProTable.refresh();
          } else {
            this.takeStockId = res.data;
            this.$refs.inventoryProTable.refresh();
            this.cacheInventoryData = [];
          }
        })
        .finally(() => {
          this.submitBtnLoading = false;
          this.stagingBtnLoading = false;
        });
    },
    // 盘点仓库
    handleCheck() {
      this.detailLocalPagination = { pageSize: 99, pageNumber: 1, total: 0 };
      this.tempLoadInventoryData = this.loadInventoryData;
      this.$refs.editForm.handleSubmit().then(() => {
        // 加载耗材数据
        this.$refs.inventoryProTable.refresh();
      });
    },
    // 审核
    handleAudit(status, type) {
      const confirmText = status === "PASS" ? "通过" : "驳回";
      this.$confirm(
        `此操作将${confirmText}对该盘点内容的审核, 是否继续?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: type,
        }
      ).then(() => {
        const params = {
          id: this.editForm.id,
          stockStatus: status,
        };
        checkWarehouseInventoryDetailApi(params).then((res) => {
          this.$refs.ProTable.refresh();
          this.closeDrawer();
          this.$message.success(res.message);
        });
      });
    },
    /**
     * @description  查看 Or 继续盘点
     * @param {Object} row
     * @param {String} type info:查看  edit: 继续盘点
     */
    handleViewOrEdit(row, type) {
      this.detailLocalPagination = { pageSize: 99, pageNumber: 1, total: 0 };
      this.reset();
      this.editType = type;
      switch (type) {
        case "info":
          this.checkTitle = `盘点单号：${row.code} — 查看`;
          break;
        case "edit":
          this.checkTitle = `盘点单号：${row.code} — 继续盘点`;
          break;
        case "audit":
          this.checkTitle = `盘点单号：${row.code} — 复核`;
          break;
        default:
          this.checkTitle = `盘点单号：${row.code} — 查看`;
      }
      this.columns.forEach((item, index) => {
        if (item.dataIndex === "inventoryDetail") {
          this.columns[index].isForm = true;
        }
        if (item.dataIndex === "lossesDetailList") {
          this.columns[index].isForm = false;
        }
        if (type === "edit") {
          if (item.dataIndex === "warehouseName") {
            this.columns[index].disabled = true;
          }
        }
      });
      if (type === "info") {
        this.inventoryLoading = false;
        this.inventoryColumns.forEach((item, index) => {
          if (
            item.dataIndex === "isTake" ||
            item.dataIndex === "isNormal" ||
            item.dataIndex === "inventoryNum"
          ) {
            this.inventoryColumns[index].isSearch = false;
          }
        });
        // this.inventorySearch = false;
        // getWarehouseInventoryDetailApi(row.id)
        //   .then((res) => {
        //     this.editForm = res.data;
        //     this.editForm.createdBy = this.editForm.createdBy?.name;
        //     this.editForm.warehouseName = this.editForm.warehouseId;
        //     // this.inventoryTableData = this.editForm.takeDetails || [];
        //   })
        //   .finally(() => {
        //     this.checkInventory = true;
        //   });
        // this.checkInventory = true;
        // return;
      } else {
        this.inventoryColumns.forEach((item, index) => {
          if (
            item.dataIndex === "isTake" ||
            item.dataIndex === "isNormal" ||
            item.dataIndex === "inventoryNum"
          ) {
            this.inventoryColumns[index].isSearch = true;
          }
        });
      }
      this.editForm = cloneDeep(row);
      this.editForm.createdBy = row.createdBy?.name;
      this.checkInventory = true;
      this.tempLoadInventoryData =
        type === "info" || type === "audit"
          ? this.loadDetailData
          : this.loadInventoryData;
      this.$nextTick(() => {
        this.$refs.inventoryProTable.refresh();
      });
    },
    confirmSubmit() {
      this.$confirm("此操作将会进入盘点数据复核, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.handleSubmit("submit");
        })
        .catch(() => {});
    },
    // 入库
    handleStash(row) {
      this.$confirm("此操作将会把本次盘点结果入库, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        batchInWarehouseApi(row.id).then((res) => {
          this.$message.success("入库成功");
        });
      });
    },
    batchCodeText(info) {
      return (
        info &&
        info.reduce((acc, cur) => {
          return acc + (acc ? "、" : "") + cur.batcheCode;
        }, "")
      );
    },
    handleExport(row) {
      this.$confirm("此操作将导出耗材盘点详情, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          exportWarehouseConsumableInventoryApi,
          { takeStockId: row.id },
          `耗材盘点单${row.code}数据`
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    handleExportStock() {
      this.$confirm("此操作将导出所有耗材仓库数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          storageExportApi,
          this.requestParameters,
          "耗材仓库数据",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    // 报损报溢
    handleLoss(row) {
      this.editType = "audit";
      this.lossesForm = cloneDeep(row);
      this.lossesForm.createdBy = row.createdBy?.name;
      this.columns.forEach((item, index) => {
        if (item.dataIndex === "lossesDetailList") {
          this.columns[index].isForm = true;
        }
        if (item.dataIndex === "inventoryDetail") {
          this.columns[index].isForm = false;
        }
      });
      this.checkLosses = true;
      this.$nextTick(() => {
        this.$refs.lossesProTable.refresh();
      });
    },
    // 关闭当前盘点
    handleClose(id) {
      this.$confirm("此操作将关闭改盘点单, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteWarehouseInventoryApi(id).then((res) => {
          Message.success("操作成功");
          this.localPagination = {
            pageNumber: 1,
            pageSize: 10,
            total: 0,
          };
          this.$nextTick(() => {
            this.$refs.ProTable.refresh();
          });
        });
      });
    },
    getManufacturerChannelOptions() {
      dictTreeByCodeApi(2200).then((res) => {
        this.manufacturerChannelOptions = res.data;
      });
    },
    closeDrawer() {
      this.reset();
      this.checkInventory = false;
      this.inventoryTotalData = {};
    },
    closeLossesDrawer() {
      this.reset();
      this.checkLosses = false;
    },
    reset() {
      this.editForm = {};
      this.inventoryQueryParam = {};
      this.cacheInventoryData = [];
      this.inventoryTableData = [];
      this.groupedInventoryTableData = [];
      this.processedInventoryData = {};
      this.checkStatus = {};
      this.quickCheckValues = {};
      this.takeStockId = "";
      this.inventoryLoading = true;
      this.inventorySearch = true;
      this.columns.forEach((item, index) => {
        if (item.dataIndex === "createdBy" || item.dataIndex === "type") {
          this.columns[index].isForm = true;
        }
      });
    },
    // 获取盘点状态
    getCheckStatus(articleCode) {
      const status = this.checkStatus[articleCode];
      if (!status) {
        return { isChecked: false, text: '待盘点', type: 'info', totalCheckedNum: 0 };
      }
      return {
        isChecked: true,
        text: status.state === 'ok' ? '已盘点' : '有差异',
        type: status.state === 'ok' ? 'success' : 'warning',
        totalCheckedNum: status.totalCheckedNum
      };
    },
    // 快速确认盘点
    quickConfirm(articleCode) {
      const quickCheckValue = this.quickCheckValues[articleCode];

      // 如果输入框为空，则不执行任何操作
      if (quickCheckValue === undefined || quickCheckValue === null || quickCheckValue === '') {
        return;
      }

      if (isNaN(quickCheckValue)) {
        this.$message.error('请输入有效的盘点总数！');
        return;
      }

      const item = this.processedInventoryData[articleCode];
      if (!item) return;

      if (quickCheckValue === item.totalInventoryNum) {
        // 数量相符，自动确认
        const newStatus = {
          state: 'ok',
          totalCheckedNum: quickCheckValue,
          batches: {}
        };

        item.batches.forEach(batch => {
          newStatus.batches[batch.batcheCode] = {
            checkedNum: batch.num,
            remark: '快速确认'
          };
          // 更新缓存数据 - 需要找到对应的原始数据项
          this.updateCacheInventoryDataByBatch(articleCode, batch.batcheCode, batch.num);
        });

        this.checkStatus[articleCode] = newStatus;
        this.$forceUpdate(); // 强制更新UI
      } else {
        // 数量不符，打开模态框并预填充总数
        this.openDetailModal(articleCode, quickCheckValue);
      }
    },
    // 打开详情模态框
    openDetailModal(articleCode, prefilledTotal = null) {
      const item = this.processedInventoryData[articleCode];
      if (!item) return;

      this.currentCheckItem = { ...item };
      const currentStatus = this.checkStatus[articleCode];

      // 如果从快速确认通道来，则使用其值；否则使用已保存的值
      if (prefilledTotal !== null) {
        this.modalTotalCheckNum = prefilledTotal;
      } else {
        this.modalTotalCheckNum = currentStatus?.totalCheckedNum || item.totalInventoryNum;
      }

      // 初始化批次盘点数据
      this.currentCheckItem.batches.forEach(batch => {
        batch.checkedNum = currentStatus?.batches?.[batch.batcheCode]?.checkedNum || batch.num;
        this.updateBatchDifference(batch);
      });

      this.feedbackMessage = '';
      this.checkDetailModal = true;
    },
    // 关闭详情模态框
    closeDetailModal() {
      this.checkDetailModal = false;
      this.currentCheckItem = {};
      this.modalTotalCheckNum = 0;
      this.feedbackMessage = '';
    },
    // 更新批次差异
    updateBatchDifference(batch) {
      const difference = (batch.checkedNum || 0) - batch.num;
      batch.difference = difference;
    },
    // 获取差异样式类
    getDifferenceClass(difference) {
      if (difference === 0) return 'difference-normal';
      if (difference > 0) return 'difference-surplus';
      return 'difference-loss';
    },
    // 获取差异文本
    getDifferenceText(difference) {
      if (difference === 0) return '无差异';
      if (difference > 0) return `盘盈 ${difference}`;
      return `盘亏 ${Math.abs(difference)}`;
    },
    // 确认详情盘点
    confirmDetailCheck() {
      const articleCode = this.currentCheckItem.articleCode;
      const batches = this.currentCheckItem.batches;

      if (!this.modalTotalCheckNum || isNaN(this.modalTotalCheckNum)) {
        this.feedbackMessage = '请输入盘点总数！';
        return;
      }

      let batchTotal = 0;
      let totalDifference = 0;
      let allBatchInputsValid = true;

      const newStatus = {
        state: 'ok',
        totalCheckedNum: this.modalTotalCheckNum,
        batches: {}
      };

      batches.forEach(batch => {
        const checkedNum = batch.checkedNum;

        if (checkedNum === undefined || checkedNum === null || isNaN(checkedNum)) {
          allBatchInputsValid = false;
        } else {
          batchTotal += checkedNum;
          totalDifference += checkedNum - batch.num;
          newStatus.batches[batch.batcheCode] = {
            checkedNum: checkedNum,
            remark: ''
          };
          // 更新缓存数据
          this.updateCacheInventoryDataByBatch(articleCode, batch.batcheCode, checkedNum);
        }
      });

      if (!allBatchInputsValid) {
        this.feedbackMessage = '请填写所有批次的盘点数量！';
        return;
      }

      if (batchTotal !== this.modalTotalCheckNum) {
        this.feedbackMessage = `批次数量总和(${batchTotal})与盘点总数(${this.modalTotalCheckNum})不符！`;
        return;
      }

      if (totalDifference !== 0) {
        newStatus.state = 'error';
      }

      this.checkStatus[articleCode] = newStatus;
      this.closeDetailModal();
      this.$forceUpdate(); // 强制更新UI
    },
    // 更新缓存库存数据
    updateCacheInventoryData(articleCode, batchCode, stockNum) {
      const isFind = this.cacheInventoryData.some(
        (item) => item.articleCode === articleCode && item.batchCode === batchCode
      );

      if (!isFind) {
        // 从原始数据中找到对应的项目
        const originalItem = this.inventoryTableData.find(
          item => item.articleCode === articleCode && item.batchCode === batchCode
        );
        if (originalItem) {
          this.cacheInventoryData.push({
            ...originalItem,
            stockNum: stockNum
          });
        }
      } else {
        this.cacheInventoryData.forEach((item, index) => {
          if (item.articleCode === articleCode && item.batchCode === batchCode) {
            this.cacheInventoryData[index].stockNum = stockNum;
          }
        });
      }
    },
    // 根据批次信息更新缓存库存数据
    updateCacheInventoryDataByBatch(articleCode, batcheCode, stockNum) {
      // 在原始数据中查找包含该批次的记录
      const originalItems = this.inventoryTableData.filter(item =>
        item.articleCode === articleCode &&
        item.batchInfo &&
        item.batchInfo.some(batch => batch.batcheCode === batcheCode)
      );

      if (originalItems.length > 0) {
        const originalItem = originalItems[0]; // 取第一个匹配的记录

        const isFind = this.cacheInventoryData.some(
          (item) => item.articleCode === articleCode &&
          item.batchInfo &&
          item.batchInfo.some(batch => batch.batcheCode === batcheCode)
        );

        if (!isFind) {
          // 创建新的缓存项，只包含当前批次
          const newCacheItem = {
            ...originalItem,
            stockNum: stockNum,
            batchCode: batcheCode, // 设置当前批次号
            // 只保留当前批次的信息
            batchInfo: originalItem.batchInfo.filter(batch => batch.batcheCode === batcheCode)
          };
          this.cacheInventoryData.push(newCacheItem);
        } else {
          // 更新现有缓存项
          this.cacheInventoryData.forEach((item, index) => {
            if (item.articleCode === articleCode &&
                item.batchInfo &&
                item.batchInfo.some(batch => batch.batcheCode === batcheCode)) {
              this.cacheInventoryData[index].stockNum = stockNum;
            }
          });
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  //margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
::v-deep.fixed-width {
  .el-button {
    i {
      margin-right: 2px;
    }
  }
}
.content-popover {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.footer {
  display: flex;
  justify-content: center;
  gap: 10px;
}

/* 盘点操作相关样式 */
.check-action-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-check {
  display: flex;
  align-items: center;
}

.checked-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 盘点详情模态框样式 */
.check-detail-content {
  padding: 16px 0;
}

.item-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.item-details h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.item-details p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.total-check-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
}

.total-check-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.feedback-message {
  margin-top: 8px;
  font-size: 14px;
  color: #f56565;
  font-weight: 500;
}

.batch-section h5 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.batch-item {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.batch-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  align-items: center;
}

.batch-field label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.batch-field span {
  font-size: 14px;
  font-weight: 500;
}

.difference-normal {
  color: #10b981;
}

.difference-surplus {
  color: #3b82f6;
}

.difference-loss {
  color: #ef4444;
}
</style>
