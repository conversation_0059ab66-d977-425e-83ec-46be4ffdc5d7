// API基础配置和类型
export * from './api';

// 前端展示API
export * from './websitePublic';

// 管理后台API服务类
import { get, post, put, del, PageResponse } from './api';
import {
  WebsiteContent,
  WebsiteImage,
  WebsiteInquiry,
  WebsiteContentCreateDto,
  WebsiteContentUpdateDto,
  WebsiteContentPageQuery,
  WebsiteImageUploadDto,
  WebsiteImagePageQuery,
  WebsiteInquirySubmitDto,
  WebsiteInquiryPageQuery,
  WebsiteInquiryUpdateDto,
  WebsiteConfigDto,
  CosCredentials,
  InquiryStatistics
} from '../types/api';

/**
 * 网站内容管理API服务类
 */
export class WebsiteContentApi {
  
  /**
   * 创建内容
   */
  static async createContent(data: WebsiteContentCreateDto): Promise<WebsiteContent> {
    const response = await post<WebsiteContent>('/website-content', data);
    return response.data;
  }

  /**
   * 更新内容
   */
  static async updateContent(data: WebsiteContentUpdateDto): Promise<WebsiteContent> {
    const response = await put<WebsiteContent>(`/website-content/${data.id}`, data);
    return response.data;
  }

  /**
   * 根据ID获取内容详情
   */
  static async getContentById(id: number): Promise<WebsiteContent> {
    const response = await get<WebsiteContent>(`/website-content/${id}`);
    return response.data;
  }

  /**
   * 删除内容
   */
  static async deleteContent(id: number): Promise<void> {
    await del(`/website-content/${id}`);
  }

  /**
   * 分页查询内容
   */
  static async getContentPage(query: WebsiteContentPageQuery): Promise<PageResponse<WebsiteContent>> {
    const response = await get<PageResponse<WebsiteContent>>('/website-content/page', { params: query });
    return response.data;
  }

  /**
   * 发布内容
   */
  static async publishContent(id: number): Promise<void> {
    await post(`/website-content/publish/${id}`);
  }

  /**
   * 批量删除内容
   */
  static async batchDeleteContent(ids: number[]): Promise<void> {
    await del('/website-content/batch', { data: { ids } });
  }

  /**
   * 根据类型获取第一条已发布内容
   */
  static async getFirstByType(type: string): Promise<WebsiteContent> {
    const response = await get<WebsiteContent>(`/website-content/first-by-type?type=${type}`);
    return response.data;
  }
}

/**
 * 网站图片管理API服务类
 */
export class WebsiteImageApi {
  
  /**
   * 获取COS上传凭证
   */
  static async getCosCredentials(): Promise<CosCredentials> {
    const response = await get<CosCredentials>('/website-image/cos-credentials');
    return response.data;
  }

  /**
   * 记录图片上传信息
   */
  static async recordImageUpload(data: WebsiteImageUploadDto): Promise<WebsiteImage> {
    const response = await post<WebsiteImage>('/website-image/upload', data);
    return response.data;
  }

  /**
   * 分页查询图片
   */
  static async getImagePage(query: WebsiteImagePageQuery): Promise<PageResponse<WebsiteImage>> {
    const response = await get<PageResponse<WebsiteImage>>('/website-image/page', { params: query });
    return response.data;
  }

  /**
   * 删除图片
   */
  static async deleteImage(id: number): Promise<void> {
    await del(`/website-image/${id}`);
  }

  /**
   * 批量删除图片
   */
  static async batchDeleteImages(ids: number[]): Promise<void> {
    await del('/website-image/batch', { data: { ids } });
  }
}

/**
 * 网站咨询管理API服务类
 */
export class WebsiteInquiryApi {
  
  /**
   * 提交咨询（匿名访问）
   */
  static async submitInquiry(data: WebsiteInquirySubmitDto): Promise<WebsiteInquiry> {
    const response = await post<WebsiteInquiry>('/website-inquiry/submit', data);
    return response.data;
  }

  /**
   * 分页查询咨询
   */
  static async getInquiryPage(query: WebsiteInquiryPageQuery): Promise<PageResponse<WebsiteInquiry>> {
    const response = await get<any>('/website-inquiry/page', { params: query });
    if (response.data?.code === 200) {
      const rawData = response.data.data;
      const total = parseInt(rawData.total || '0', 10);
      const size = query.size || 10;
      const current = query.current || 1;

      const records = (rawData.rows || []).map((row: any) => ({
        ...row,
        repliedBy: row.repliedBy || null,
      }));

      return {
        records,
        total,
        size,
        current,
        pages: Math.ceil(total / size),
      };
    }
    throw new Error(response.data?.message || '获取咨询分页失败');
  }

  /**
   * 根据ID获取咨询详情
   */
  static async getInquiryById(id: number): Promise<WebsiteInquiry> {
    const response = await get<WebsiteInquiry>(`/website-inquiry/${id}`);
    return response.data;
  }

  /**
   * 更新咨询信息
   */
  static async updateInquiry(data: WebsiteInquiryUpdateDto): Promise<WebsiteInquiry> {
    const response = await put<WebsiteInquiry>('/website-inquiry/update', data);
    return response.data;
  }

  /**
   * 删除咨询
   */
  static async deleteInquiry(id: number): Promise<void> {
    await del(`/website-inquiry/${id}`);
  }

  /**
   * 获取咨询统计信息
   */
  static async getInquiryStatistics(): Promise<InquiryStatistics> {
    const response = await get<any>('/website-inquiry/statistics');
    if (response.data?.code === 200) {
      const data = response.data.data;
      return {
        totalCount: Object.values(data.statusCount || {}).reduce((sum: number, val: any) => sum + parseInt(val as string || '0', 10), 0),
        pendingCount: parseInt(data.pendingCount || '0', 10),
        processingCount: parseInt(data.statusCount?.PROCESSING || '0', 10),
        repliedCount: parseInt(data.statusCount?.COMPLETED || data.statusCount?.REPLIED || '0', 10),
        closedCount: parseInt(data.statusCount?.CLOSED || '0', 10),
        todayCount: parseInt(data.todayCount || '0', 10),
        weekCount: 0,
        monthCount: 0,
      };
    }
    throw new Error(response.data?.message || '获取咨询统计失败');
  }
}

/**
 * 网站配置管理API服务类
 */
export class WebsiteConfigApi {
  
  /**
   * 获取公开配置（匿名访问）
   */
  static async getPublicConfig(): Promise<WebsiteConfigDto> {
    const response = await get<WebsiteConfigDto>('/website-config/public');
    if (response.data && typeof response.data === 'object' && 'code' in response.data && 'data' in response.data) {
      console.log('WebsiteConfigApi: 检测到 RestResponse 格式', response.data);
      return (response.data as any).data;
    }
    return response.data;
  }

  /**
   * 获取所有配置
   */
  static async getAllConfig(): Promise<WebsiteConfigDto> {
    const response = await get<WebsiteConfigDto>('/website-config');
    return response.data;
  }

  /**
   * 更新配置
   */
  static async updateConfig(data: Partial<WebsiteConfigDto>): Promise<WebsiteConfigDto> {
    try {
      const response = await put<any>('/website-config', data);
      // 使用类型断言处理响应
      const responseObj = response as any;
      if (typeof responseObj.code === 'number' && responseObj.code !== 200) {
        console.error('配置更新失败:', response.message || '未知错误');
        throw new Error(response.message || '配置更新失败');
      }
      // 返回响应数据，可能在data字段中
      return response.data || response;
    } catch (error) {
      console.error('配置更新请求失败:', error);
      throw error;
    }
  }

  /**
   * 根据key获取配置值
   */
  static async getConfigByKey(key: string): Promise<string> {
    const response = await get<string>(`/website-config/value/${key}`);
    return response.data;
  }

  /**
   * 设置配置值
   */
  static async setConfigByKey(key: string, value: string): Promise<void> {
    await post(`/website-config/key/${key}`, { value });
  }

  /**
   * 获取配置加密状态
   */
  static async getEncryptionStatus(): Promise<string[]> {
    const response = await get<string[]>('/website-config/encryption-status');
    // 处理RestResponse格式
    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return (response.data as any).data || [];
    }
    return response.data || [];
  }

  /**
   * 迁移配置到加密存储
   */
  static async migrateToEncryption(): Promise<string> {
    const response = await post<string>('/website-config/migrate-encryption');
    // 处理RestResponse格式
    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return (response.data as any).data || '迁移完成';
    }
    return response.data || '迁移完成';
  }

  /**
   * 获取敏感配置的密文值（前端安全传输用）
   */
  static async getSensitiveConfigEncrypted(configKey: string): Promise<string | null> {
    try {
      const response = await get<string>(`/website-config/sensitive/${configKey}`);
      // 处理RestResponse格式
      if (response.data && typeof response.data === 'object' && 'data' in response.data) {
        return (response.data as any).data || null;
      }
      return response.data || null;
    } catch (error: any) {
      // 获取敏感配置失败，静默处理
      return null;
    }
  }
}

// 创建API实例
export const websiteContentApi = WebsiteContentApi;
export const websiteImageApi = WebsiteImageApi;
export const websiteInquiryApi = WebsiteInquiryApi;
export const websiteConfigApi = WebsiteConfigApi; 