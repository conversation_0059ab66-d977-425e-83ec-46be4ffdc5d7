<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 13:38:10
 * @Description: 
 -->
<template>
  <div class="admin-container">
    <template>
      <el-container>
        <Menu class="hidden-xs-only" />
        <el-container class="container" :class="{ collapsed: collapse }">
          <el-header
            class="header"
            :class="{ fixed: fixedHeader, notag: !tag, collapsed: collapse }"
            height="60px"
          >
            <NavBar />
            <template v-if="tag">
              <TabBar />
            </template>
          </el-header>
          <el-main class="main" :class="{ fixed: fixedHeader, notag: !tag }">
            <!-- <AppMain /> -->
            <transition name="fade-transform" mode="out-in">
              <keep-alive
                ref="alive"
                :include="cacheComponents"
                :exclude="['Home', 'WorkMap']"
              >
                <router-view :key="routerKey" />
              </keep-alive>
            </transition>
          </el-main>
        </el-container>
      </el-container>
      <el-backtop />
    </template>
  </div>
</template>

<script>
import NavBar from "./components/NavBar/index.vue";
import Menu from "./components/Menu/index.vue";
import TabBar from "./components/TabBar/index.vue";
import { mapGetters } from "vuex";

export default {
  name: "Layout",
  components: {
    Menu,
    NavBar,
    TabBar,
  },
  mixins: [],
  data() {
    return {
      tag: true,
      currentRoute: "",
    };
  },
  computed: {
    ...mapGetters({
      collapse: "collapse",
      visitedRoutes: "visitedRoutes",
    }),
    cacheComponents() {
      return this.visitedRoutes.map(
        (item) => item.matched[1].components.default.name
      );
    },
    routerKey() {
      return this.$route.path;
    },
    key() {
      return this.$route.path + Math.random();
    },
    sidebar() {
      return this.$store.state.app.sidebar;
    },
    device() {
      return this.$store.state.app.device;
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
  },
};
</script>

<style lang="scss" scoped>
.admin-container {
  position: relative;
  background-color: $base-content-bg-color;
  transition: width $base-transition-time-4 ease-in-out;
  -webkit-transition: width $base-transition-time-4 ease-in-out;
  -moz-transition: width $base-transition-time-4 ease-in-out;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  will-change: width;

  .container {
    position: absolute;
    right: 0;
    left: 265px;
    transition: all $base-transition-time-4 ease-in-out;
    -webkit-transition: all $base-transition-time-4 ease-in-out;
    -moz-transition: all $base-transition-time-4 ease-in-out;
    transform: translateX(0);
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    width: calc(100% - 265px);
    will-change: width, transform;
    min-height: 100vh;

    &.collapsed {
      width: calc(100% - 72px);
    }
  }

  .header {
    padding: 0;
    left: 265px;
    transition: transform $base-transition-time-4 ease-in-out;
    -webkit-transition: transform $base-transition-time-4 ease-in-out;
    -moz-transition: transform $base-transition-time-4 ease-in-out;
    transform: translateX(0);
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    will-change: transform;

    &.fixed {
      position: fixed;
      top: 0;
      right: 0;
      z-index: $base-z-index-999;
    }
  }

  .main {
    padding: 0;
    transition: transform $base-transition-time-4 ease-in-out;
    -webkit-transition: transform $base-transition-time-4 ease-in-out;
    -moz-transition: transform $base-transition-time-4 ease-in-out;
    transform: translateX(0);
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    will-change: transform;
  }

  .collapsed {
    .container {
      transform: translateX(-193px);
      -webkit-transform: translateX(-193px);
      -moz-transform: translateX(-193px);
    }
    .header {
      transform: translateX(-193px);
      -webkit-transform: translateX(-193px);
      -moz-transform: translateX(-193px);
    }
    .main {
      transform: translateX(-193px);
      -webkit-transform: translateX(-193px);
      -moz-transform: translateX(-193px);
    }
  }

  .main {
    position: relative;
    top: $base-main-vertical-top;
    overflow-y: auto;

    &.fixed {
      top: $base-main-fixed-top;
    }

    &[class="el-main main fixed notag"] {
      top: $base-main-vertical-fixed-notag-top;
    }

    &[class="el-main main notag"] {
      top: $base-main-notag-top;
    }

    background-color: $base-content-bg-color;
  }
}
</style>
