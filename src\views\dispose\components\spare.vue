<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:52
 * @Description: 选配件列表 
 -->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增选配件
        </el-button>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.productIdName"
          filterable
          clearable
          :options="options"
          style="width: 500px"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>

      <template #brand="slotProps">
        <el-popover
          placement="bottom"
          title="当前设备关联产品树"
          width="400"
          trigger="click"
        >
          <div style="margin: 20px">
            <el-tree
              :data="deviceProductTree"
              :props="{
                children: 'children',
                label: 'name',
              }"
              default-expand-all
            ></el-tree>
          </div>

          <el-button
            slot="reference"
            type="text"
            size="small"
            @click="getBrand(slotProps.row.id)"
          >
            查看品牌
          </el-button>
        </el-popover>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            详情
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType == 'info'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
        <template #fullIdPath>
          <el-cascader
            ref="ProductIds"
            v-model="form.productIdName"
            filterable
            :disabled="methodType == 'info'"
            :options="options"
            style="width: 100%"
            :value="form.productIdName"
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
              expandTrigger: 'click',
              multiple: true,
            }"
            clearable
            leaf-only
            @change="handleChange"
          ></el-cascader>
        </template>
      </ProForm>
    </ProDrawer>
  </div>
</template>
<script>
import {
  accessoryListApi,
  accessoryAddApi,
  accessoryDelApi,
  accessoryEditApi,
  productAllApi,
  accessoryProductTreeApi,
  accessoryProductPathApi,
} from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";

import { isEmpty, cloneDeep } from "lodash";

export default {
  name: "Spare",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      // 列表
      deviceProductTree: [],
      tableData: [],
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      queryParam: {
        lastIds: [],
      },
      columns: [
        {
          dataIndex: "lastIds",
          isSearch: true,
          clearable: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
          title: "品牌名称",
        },

        {
          dataIndex: "brand",
          title: "产品树",
          isTable: true,
          tableSlot: "brand",
        },

        // {
        //   dataIndex: "name",
        //   title: "选配件名称",
        //   isSearch: true,
        //   clearable: true,
        //   span: 4,
        //   valueType: "input"
        // },

        // {
        //   dataIndex: "type",
        //   title: "设备类型",
        //   isTable: true,
        //   // isSearch: true,
        //   // valueType: 'select',
        //   // clearable: true,
        //   // formSpan: 8,
        //   // option: [],
        //   formatter: (row) => row.type.label
        //   // optionMth: () => dictTreeByCodeApi(800),
        //   // optionskey: {
        //   //   label: 'label',
        //   //   value: 'value',
        //   // },
        // },
        {
          dataIndex: "type",
          title: "选配件类型",
          isTable: true,
          formatter: (row) => row.type.label,
          isSearch: true,
          clearable: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(2000),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "modeType",
          title: "配件型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "machineNumber",
          title: "机器编号",
          isTable: true,
        },
        {
          dataIndex: "Actions",
          width: 240,
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],

      //新增
      methodType: "add",
      confirmLoading: false,
      form: {},
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      formcolumns: [
        {
          dataIndex: "productIds",
          isForm: true,
          clearable: true,
          formSlot: "fullIdPath",
          title: "品牌/产品树信息",
          valueType: "select",
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请选择品牌/产品树",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "modeType",
          isForm: true,
          title: "配件型号",
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          clearboth: true,
          dataIndex: "type",
          title: "选配件类型",
          isForm: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(2000),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择生产类型",
              trigger: "change",
            },
          ],
        },

        {
          dataIndex: "machineNumber",
          isForm: true,
          title: "机器编号",
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "voltage",
          title: "电压",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },

        {
          dataIndex: "hostSize",
          title: "主机尺寸",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "weight",
          title: "重量（KG）",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "power",
          title: "功率（W）",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "current",
          title: "电流（A)",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "minSheetSize",
          title: "最小纸张尺寸",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "maxSheetSize",
          title: "最大纸张尺寸",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "minSheetWeight",
          title: "最小纸张重量",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },

        {
          dataIndex: "maxSheetWeight",
          title: "最大纸张重量",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "maxOutput",
          title: "最大输出容量",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "maxImgSize",
          title: "最大图像尺寸",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "mediaIdentify",
          title: "介质识别单元",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "supportSheet",
          title: "支持纸张类型",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "supportMedia",
          title: "可支持介质",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
      ],
      //字典项
      roleId: null,
      dialogTitleR: "",
      dialogVisibleR: false,
      dialogVisibleU: false,
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    productAllApi().then((res) => {
      this.options = res.data;
      this.$refs.ProTable.refresh();
    });
  },
  methods: {
    getBrand(id) {
      accessoryProductTreeApi(id).then((res) => {
        this.deviceProductTree = res.data;
      });
    },
    handleSelect(item) {
      this.queryParam.lastIds = [];
      item.map((el) => {
        this.queryParam.lastIds.push(el[el.length - 1]);
      });
    },
    handleChange(item) {
      this.$set(this.form, "productIds", []);
      // console.log(this.$refs.ProductIds.getCheckedNodes(true))
      item.map((el) => {
        this.form.productIds.push(el[el.length - 1]);
      });
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = cloneDeep(
        Object.assign(this.queryParam, parameter)
      );
      delete requestParameters.productIdName;
      accessoryListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.dialogTitle = "新增选配件";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;

      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应新增
    create() {
      const obj = cloneDeep(this.form);
      delete obj.productIdName;
      accessoryAddApi(obj)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑";
      this.resetFrom();
      this.form = cloneDeep(row);

      this.form.type = this.form.type.value;
      this.$set(this.form, "productIdName", []);
      const productArr = [];
      const productidArr = [];
      accessoryProductPathApi(row.id).then((res) => {
        console.log(res);
        res.data.map((el) => {
          const arr = el.split("/");
          arr.shift();
          productidArr.push(arr[arr.length - 1]);
          productArr.push(arr);
        });
      });

      this.methodType = "edit";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
        setTimeout(() => {
          this.$set(this.form, "productIdName", productArr);
          this.$set(this.form, "productIds", productidArr);
          console.log(this.form.productIdName);
          console.log(this.form.productIds);
        }, 300);
      });
    },
    //响应编辑
    update() {
      const obj = cloneDeep(this.form);
      delete obj.productIdName;
      accessoryEditApi(obj)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看";
      this.resetFrom();
      this.form = cloneDeep(row);
      this.form.type = this.form.type.value;
      this.$set(this.form, "productIdName", []);
      const productArr = [];
      const productidArr = [];
      accessoryProductPathApi(row.id).then((res) => {
        console.log(res);
        res.data.map((el) => {
          const arr = el.split("/");
          arr.shift();
          productidArr.push(arr[arr.length - 1]);
          productArr.push(arr);
        });
      });

      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
        setTimeout(() => {
          this.$set(this.form, "productIdName", productArr);
          this.$set(this.form, "productIds", productidArr);
          console.log(this.form.productIdName);
          console.log(this.form.productIds);
        }, 300);
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        accessoryDelApi(data.id).then(() => {
          this.$message.success("删除成功");
          this.localPagination = {
            pageNumber: 1,
            pageSize: 10,
            total: 0,
          };
          this.$nextTick(() => {
            this.$refs.ProTable.refresh();
          });
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
