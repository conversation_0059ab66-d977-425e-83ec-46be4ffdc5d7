<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-01 12:01:56
 * @Description: 机器维修报价
 -->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      :layout="{ labelWidth: '170px' }"
      :local-pagination="localPagination"
      :data="tableData"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </template>
      <template #machine="slotProps">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in brandInfoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <el-button
            slot="reference"
            type="text"
            size="mini"
            @click="brandInfo(slotProps.row.id)"
            >适用机型</el-button
          >
        </el-popover>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleChange"
        ></el-cascader>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            v-if="slotProps.row.auditStatus == 'WAIT_AUDIT'"
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            审核
          </el-button>
          <!---->
          <el-button
            v-if="slotProps.row.auditStatus == 'WAIT_AUDIT'"
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>

          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>

    <!-- 新增、编辑、详情框  -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :confirm-loading="confirmLoading"
      :top="'3%'"
      :no-footer="methodType === 'info'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '160px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
        <template #fullIdPath1>
          <div style="max-height: 300px; overflow-y: scroll">
            <el-descriptions class="margin-top" :column="1" border>
              <el-descriptions-item
                v-for="item in form.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </template>
        <template #fullIdPath>
          <el-cascader
            ref="ProductIds"
            v-model="productIdName1"
            :value="productIdName1"
            :disabled="methodType === 'info'"
            filterable
            clearable
            collapse-tags
            :options="options"
            style="width: 100%"
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
              expandTrigger: 'click',
              multiple: true,
            }"
            leaf-only
            @change="handleChange1"
          ></el-cascader>
        </template>
      </ProForm>
      <div v-if="methodType === 'info'" class="btn-box">
        <div class="success-button" @click="handleOk('APPROVE', form.id)">
          同意
        </div>
        <div class="cancel-button m-l-40" @click="handleOk('REFUSE', form.id)">
          拒绝
        </div>
      </div>
    </ProDialog>
  </div>
</template>
<script>
import {
  repairPriceByPageApi,
  repairPriceAddApi,
  repairPriceDelApi,
  repairPriceEditApi,
  repairPriceInfoApi,
  repairPriceAuditApi,
} from "@/api/repair";

import { isEmpty, cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
import { productAllApi } from "@/api/dispose";
import { filterName, filterParam, getAllParentArr } from "@/utils";

export default {
  name: "ModelFee",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      options: [],
      tableData: [],
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      brandInfoList: [],
      queryParam: {},
      productIdName: [],
      productIdName1: [],
      columns: [
        {
          dataIndex: "productIdList",
          title: "品牌/机型",
          isTable: true,
          isSearch: true,
          span: 4,
          valueType: "product",
          tableSlot: "machine",
          searchSpan: 6,
          minWidth: 100,
        },
        {
          dataIndex: "visitPrice",
          title: "基础上门费",
          minWidth: 100,
          isTable: true,
        },
        // {
        //   dataIndex: "visitPriceStart",
        //   title: "最低基础上门费",
        //   isSearch: true,
        //   valueType: "input",
        //   inputType: "number",
        //   searchSpan: 6,
        // },
        // {
        //   dataIndex: "visitPriceEnd",
        //   title: "最高基础上门费",
        //   isSearch: true,
        //   valueType: "input",
        //   inputType: "number",
        //   searchSpan: 6,
        // },
        {
          dataIndex: "normalPriceStart",
          title: "最低普通维修费",
          isSearch: true,
          valueType: "input",
          inputType: "number",
          searchSpan: 6,
        },
        // {
        //   dataIndex: "normalPriceEnd",
        //   title: "最高普通维修费",
        //   isSearch: true,
        //   valueType: "input",
        //   inputType: "number",
        //   searchSpan: 6,
        // },
        {
          dataIndex: "discountPriceStart",
          title: "最低客户端客户维修折扣",
          isSearch: true,
          valueType: "input",
          inputType: "number",
          searchSpan: 6,
        },
        // {
        //   dataIndex: "discountPriceEnd",
        //   title: "最高客户端客户维修折扣",
        //   isSearch: true,
        //   valueType: "input",
        //   inputType: "number",
        //   searchSpan: 6,
        // },
        {
          dataIndex: "vipPriceStart",
          title: "最低VIP客户维修折扣",
          isSearch: true,
          valueType: "input",
          inputType: "number",
          searchSpan: 6,
        },
        {
          dataIndex: "vipPriceEnd",
          title: "最高VIP客户维修折扣",
          isSearch: true,
          valueType: "input",
          inputType: "number",
          searchSpan: 6,
        },
        {
          dataIndex: "checkPay",
          title: "维修诊断费",
          minWidth: 120,
          isTable: true,
        },
        {
          dataIndex: "replacePay",
          title: "零件更换费",
          minWidth: 120,
          isTable: true,
        },
        {
          dataIndex: "registerPrice",
          minWidth: 120,
          isTable: true,
          title: "登记客户折扣费",
        },
        {
          dataIndex: "discountPrice",
          title: "客户端客户维修折扣",
          minWidth: 150,
          isTable: true,
        },
        {
          dataIndex: "signingPrice",
          minWidth: 120,
          isTable: true,
          title: "签约客户折扣费",
        },
        {
          dataIndex: "vipPrice",
          title: "VIP客户维修折扣",
          minWidth: 140,
          isTable: true,
        },
        {
          dataIndex: "auditStatus",
          title: "审核状态",
          minWidth: 80,
          isTable: true,
          clearable: true,
          valueType: "select",
          isSearch: true,
          formatter: (row) =>
            row.auditStatus === "WAIT_AUDIT"
              ? "待审核"
              : row.auditStatus === "APPROVE"
              ? "已通过"
              : "已拒绝",

          span: 4,
          option: [
            {
              label: "待审核",
              value: "WAIT_AUDIT",
            },
            {
              label: "已通过",
              value: "APPROVE",
            },
            {
              label: "已拒绝",
              value: "REFUSE",
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
          searchSpan: 6,
        },

        {
          dataIndex: "editBy",
          title: "编辑人",
          minWidth: 100,
          formatter: (row) => row.editBy.name,
          isTable: true,
        },
        {
          dataIndex: "updatedAt",
          title: "最近编辑时间",
          width: 150,
          isTable: true,
        },
        {
          dataIndex: "auditBy",
          title: "审核人",
          width: 100,
          formatter: (row) => row.auditBy.name,
          isTable: true,
        },
        {
          dataIndex: "auditTime",
          title: "审核时间",
          width: 150,
          isTable: true,
        },
        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 150,
        },
      ],

      //新增
      methodType: "add",
      confirmLoading: false,

      form: {},
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      formcolumns: [],
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
      dictTreeByCodeApi(2100).then((res) => {
        this.goodsTypeOptions = res.data;
      });
      this.$refs.ProTable.refresh();
    },

    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      if (
        !requestParameters.auditStatus ||
        requestParameters.auditStatus == ""
      ) {
        delete requestParameters.auditStatus;
      }
      repairPriceByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.productIdName1 = [];
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.dialogTitle = "新增费用";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;
      this.formcolumns = [
        {
          dataIndex: "productIdList",
          isForm: true,
          title: "品牌/机型",
          formSlot: "fullIdPath",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请选择品牌/机型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "visitPrice",
          isForm: true,
          title: "基础上门费",
          valueType: "input-number",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入基础上门费",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "checkPay",
          isForm: true,
          title: "维修诊断费",
          valueType: "input-number",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入维修诊断费",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "replacePay",
          isForm: true,
          title: "零件更换费",
          valueType: "input-number",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入零件更换费",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "registerPrice",
          isForm: true,
          title: "登记客户折扣费",
          valueType: "input-number",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入登记客户折扣费",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "discountPrice",
          isForm: true,
          title: "客户端客户维修折扣",
          valueType: "input-number",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入客户端客户维修折扣",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "signingPrice",
          isForm: true,
          title: "签约客户折扣费",
          valueType: "input-number",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入签约客户折扣费",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "vipPrice",
          isForm: true,
          title: "VIP客户维修折扣",
          valueType: "input-number",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入VIP客户维修折扣",
              trigger: "change",
            },
          ],
        },
      ];
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应新增
    create() {
      repairPriceAddApi(this.form)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑费用";
      this.resetFrom();
      repairPriceInfoApi(row.id).then((res) => {
        this.form = cloneDeep(res.data);
        this.productIdName1 = [];
        const productArr = [];
        // const productidArr = [];
        this.form.productTreeDtoList.map((el) => {
          const arr = el.fullIdPath.split("/");
          arr.shift();
          // productidArr.push(arr[arr.length - 1]);
          productArr.push(arr);
        });
        this.form.editBy = this.form.editBy.name;
        this.methodType = "edit";
        this.formcolumns = [
          {
            dataIndex: "productIdList",
            isForm: true,
            title: "品牌/机型",
            formSlot: "fullIdPath",
            formSpan: 24,
            prop: [
              {
                required: true,
                message: "请选择品牌/机型",
                trigger: "change",
              },
            ],
          },
          {
            dataIndex: "visitPrice",
            isForm: true,
            title: "基础上门费",
            valueType: "input-number",
            clearable: true,
            formSpan: 24,
            prop: [
              {
                required: true,
                message: "请输入基础上门费",
                trigger: "change",
              },
            ],
          },
          {
            dataIndex: "checkPay",
            isForm: true,
            title: "维修诊断费",
            valueType: "input-number",
            clearable: true,
            formSpan: 24,
            prop: [
              {
                required: true,
                message: "请输入维修诊断费",
                trigger: "change",
              },
            ],
          },
          {
            dataIndex: "replacePay",
            isForm: true,
            title: "零件更换费",
            valueType: "input-number",
            clearable: true,
            formSpan: 24,
            prop: [
              {
                required: true,
                message: "请输入零件更换费",
                trigger: "change",
              },
            ],
          },
          {
            dataIndex: "registerPrice",
            isForm: true,
            title: "登记客户折扣费",
            valueType: "input-number",
            clearable: true,
            formSpan: 24,
            prop: [
              {
                required: true,
                message: "请输入登记客户折扣费",
                trigger: "change",
              },
            ],
          },
          {
            dataIndex: "discountPrice",
            isForm: true,
            title: "客户端客户维修折扣",
            valueType: "input-number",
            clearable: true,
            formSpan: 24,
            prop: [
              {
                required: true,
                message: "请输入客户端客户维修折扣",
                trigger: "change",
              },
            ],
          },

          {
            dataIndex: "signingPrice",
            isForm: true,
            title: "签约客户折扣费",
            valueType: "input-number",
            clearable: true,
            formSpan: 24,
            prop: [
              {
                required: true,
                message: "请输入签约客户折扣费",
                trigger: "change",
              },
            ],
          },
          {
            dataIndex: "vipPrice",
            isForm: true,
            title: "VIP客户维修折扣",
            valueType: "input-number",
            clearable: true,
            formSpan: 24,
            prop: [
              {
                required: true,
                message: "请输入VIP客户维修折扣",
                trigger: "change",
              },
            ],
          },
        ];
        this.dialogVisible = true;
        this.$nextTick((e) => {
          this.productIdName1 = cloneDeep(productArr);
          this.$refs["proform"].resetFormParam();
        });
      });
    },
    //响应编辑
    update() {
      delete this.form.auditBy;
      delete this.form.editBy;
      delete this.form.productTreeDtoList;
      repairPriceEditApi(this.form)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },
    brandInfo(id) {
      repairPriceInfoApi(id).then((res) => {
        this.brandInfoList = res.data.productTreeDtoList;
      });
    },
    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "费用审核";
      this.resetFrom();
      repairPriceInfoApi(row.id).then((res) => {
        this.form = cloneDeep(res.data);

        // const productArr = [];
        // const productidArr = [];
        // this.form.productTreeDtoList.map((el) => {
        //   const arr = el.fullIdPath.split("/");
        //   arr.shift();
        //   // productidArr.push(arr[arr.length - 1]);
        //   productArr.push(arr);
        // });
        this.form.editBy = this.form.editBy.name;
        this.methodType = "info";
        this.formcolumns = [
          {
            dataIndex: "productIdList",
            isForm: true,
            title: "品牌/机型",
            formSlot: "fullIdPath1",
            formSpan: 24,
            prop: [
              {
                required: true,
                message: "请选择品牌/机型",
                trigger: "change",
              },
            ],
          },
          {
            dataIndex: "visitPrice",
            isForm: true,
            title: "基础上门费",
            valueType: "text",
            formSpan: 24,
          },
          {
            dataIndex: "checkPay",
            isForm: true,
            title: "维修诊断费",
            valueType: "text",
            formSpan: 24,
          },
          {
            dataIndex: "replacePay",
            isForm: true,
            title: "零件更换费",
            valueType: "text",
            formSpan: 24,
          },
          {
            dataIndex: "registerPrice",
            isForm: true,
            title: "登记客户折扣费",
            valueType: "text",
            formSpan: 24,
          },
          {
            dataIndex: "discountPrice",
            isForm: true,
            title: "客户端客户维修折扣",
            valueType: "text",
            formSpan: 24,
          },
          {
            dataIndex: "signingPrice",
            isForm: true,
            title: "签约客户折扣费",
            valueType: "text",
            formSpan: 24,
          },
          {
            dataIndex: "vipPrice",
            isForm: true,
            title: "VIP客户维修折扣",
            valueType: "text",
            formSpan: 24,
          },

          {
            dataIndex: "editBy",
            title: "编辑人",
            formSpan: 24,
            valueType: "text",
            isForm: true,
          },
          {
            dataIndex: "updatedAt",
            title: "最近编辑时间",
            formSpan: 24,
            valueType: "text",
            isForm: true,
          },
        ];
        this.dialogVisible = true;
        this.$nextTick((e) => {
          // this.productIdName1 = cloneDeep(productArr);
          this.$refs["proform"].resetFormParam();
        });
      });
    },
    handleOk(type, id) {
      repairPriceAuditApi({ auditStatus: type, id: id })
        .then(() => {
          this.$message.success("审核成功");
        })
        .finally(() => {
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        repairPriceDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
    handleChange(item) {
      this.queryParam.productIdList = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIdList.push(id);
      });
    },
    handleChange1(item) {
      this.$set(this.form, "productIdList", []);
      // this.form.productIdList = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.form.productIdList.push(id);
      });
    },
  },
};
</script>
<style>
.el-checkbox {
  line-height: 40px;
}

.el-collapse {
  border: none;
}

.el-collapse-item__header {
  border: none;
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-item__content {
  padding: 0;
}

.el-collapse-item:last-child {
  margin: auto;
}
</style>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tree-li {
  list-style: none;
  margin: 0;

  li {
    list-style: none;
    display: inline-block;
    margin-left: 20px;
  }
}
</style>
