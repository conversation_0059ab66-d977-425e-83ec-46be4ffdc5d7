<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-params="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <div v-if="type === 'consumables'" class="box">
          <div>总库存数量：{{ totalData?.inventoryNum || 0 }}</div>
          <div>总库存金额：{{ totalData?.inventoryAmount || 0 }}</div>
        </div>
      </template>
      <template #img="slotProps">
        <el-image
          v-if="
            slotProps.row.imageFiles && slotProps.row.imageFiles.length !== 0
          "
          style="width: 100px; height: 100px"
          :src="slotProps.row?.imageFiles?.[0].url"
        ></el-image>
        <div v-else>暂无</div>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          collapse-tags
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #isUnsalable="{ row }">
        <!--        <el-switch-->
        <!--          v-model="row.isUnsalable"-->
        <!--          active-color="#13ce66"-->
        <!--          :active-value="false"-->
        <!--          :inactive-value="true"-->
        <!--          @change="(e) => changIdleStatus(e, row)"-->
        <!--        >-->
        <!--        </el-switch>-->
        <el-select
          v-model="row.isUnsalable"
          placeholder="请选择呆滞状态"
          size="mini"
          @change="(e) => changIdleStatus(e, row)"
        >
          <el-option
            v-for="item in idleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { productAllApi } from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";
import {
  getStagnantListApi,
  getStagnantTotalApi,
  updateStagnantApi,
} from "@/api/store";
import { Message } from "element-ui";

export default {
  name: "IdleItem",
  props: {
    type: {
      type: String,
      default: "consumables",
    },
    columns: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      tableData: [],
      productIdName: "",
      options: [],
      idleOptions: [
        {
          value: false,
          label: "正常",
        },
        {
          value: true,
          label: "呆滞",
        },
      ],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
    this.init();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startDate: null,
          endDate: null,
          data: parameter.finalSalesTime,
        },
        {
          minInventoryNum: null,
          maxInventoryNum: null,
          data: parameter.inventoryNum,
        },
        {
          minUnsaleableNum: null,
          maxUnsaleableNum: null,
          data: parameter.unsaleableNum,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.finalSalesTime;
      getStagnantListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getStagnantTotalApi(requestParameters);
    },
    changIdleStatus(val, row) {
      updateStagnantApi({ id: row.id, isUnsalable: val }).then((res) => {
        Message.success("状态修改成功");
      });
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    getStagnantTotalApi(params) {
      getStagnantTotalApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  display: flex;
  flex: 1;
  justify-content: flex-end;
  margin-right: 20px;
  gap: 50px;
  font-size: 16px;
  color: #6488cf;
}
</style>
