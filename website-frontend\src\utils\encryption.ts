/**
 * 加密解密工具函数
 * 与react-modules项目保持完全一致的AES加密实现
 */

import CryptoJS from 'crypto-js';

// 默认加密密钥（与后端保持一致）
const DEFAULT_ENCRYPTION_KEY = 'BenYin2025ConfigEncryptSecureKey';

/**
 * 获取加密密钥
 * 优先使用环境变量，降级到默认密钥
 */
function getEncryptionKey(): string {
  // 安全地访问环境变量，避免在某些环境中process未定义的问题
  try {
    if (typeof process !== 'undefined' && process.env && process.env.REACT_APP_CONFIG_ENCRYPT_KEY) {
      return process.env.REACT_APP_CONFIG_ENCRYPT_KEY;
    }
  } catch (e) {
    // 环境变量不可用，使用默认密钥
  }

  // 始终返回默认密钥（在当前实现中，前后端使用相同的固定密钥）
  return DEFAULT_ENCRYPTION_KEY;
}

/**
 * AES解密函数
 * @param encryptedText Base64编码的加密文本
 * @returns 解密后的明文，解密失败时返回原文本
 */
export function decryptConfigValue(encryptedText: string): string {
  if (!encryptedText || encryptedText.trim().length === 0) {
    return encryptedText;
  }

  try {
    const key = CryptoJS.enc.Utf8.parse(getEncryptionKey());

    // 使用ECB模式解密（与后端保持一致）
    const decrypted = CryptoJS.AES.decrypt(encryptedText, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });

    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);

    // 检查解密结果是否有效
    if (decryptedText && decryptedText.length > 0) {
      return decryptedText;
    } else {
      // 解密结果为空，可能是未加密的数据
      return encryptedText;
    }
  } catch (error) {
    // 解密失败时返回原文本，兼容未加密的历史数据
    return encryptedText;
  }
}

/**
 * AES加密函数
 * @param plainText 明文
 * @returns Base64编码的加密文本
 */
export function encryptConfigValue(plainText: string): string {
  if (!plainText || plainText.trim().length === 0) {
    return plainText;
  }

  try {
    const key = CryptoJS.enc.Utf8.parse(getEncryptionKey());

    // 使用ECB模式加密（与后端保持一致）
    const encrypted = CryptoJS.AES.encrypt(plainText, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });

    return encrypted.toString();
  } catch (error) {
    // 加密失败时返回原文本
    return plainText;
  }
}

/**
 * 检查字符串是否为Base64编码格式
 */
function isBase64Encoded(str: string): boolean {
  try {
    return btoa(atob(str)) === str;
  } catch {
    return false;
  }
}

/**
 * 检查是否为腾讯地图Key格式
 */
function isTencentMapKeyFormat(key: string): boolean {
  // 腾讯地图Key通常是32位字符串，包含字母和数字
  return /^[A-Za-z0-9]{20,50}$/.test(key);
}

/**
 * 处理从后端获取的腾讯地图Key
 * @param rawKey 原始Key数据
 * @returns 处理后的Key
 */
export function processMapKey(rawKey: string): string {
  if (!rawKey || typeof rawKey !== 'string') {
    return '';
  }

  // 如果已经是有效的腾讯地图Key格式，直接返回
  if (isTencentMapKeyFormat(rawKey)) {
    return rawKey;
  }

  // 如果看起来是加密的（Base64格式），尝试解密
  if (isBase64Encoded(rawKey)) {
    const decryptedKey = decryptConfigValue(rawKey);
    if (isTencentMapKeyFormat(decryptedKey)) {
      return decryptedKey;
    }
  }

  // 如果不是标准格式但长度合理，可能是其他格式的Key，直接返回
  if (rawKey.length >= 20 && rawKey.length <= 50) {
    return rawKey;
  }

  return '';
}

/**
 * 检查值是否已加密
 */
export function isEncrypted(value: string): boolean {
  return isBase64Encoded(value);
}
