<!--
 * @Author: wskg
 * @Date: 2024-09-12 15:03:40
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:55
 * @Description: 订单  - 订单明细
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>总数量：{{ totalData?.itemNum || 0 }}</div>
          <div>总运费：{{ totalData?.shippingFee || 0 }}</div>
          <div>总金额：{{ totalData?.payAmount || 0 }}</div>
          <div>总优惠金额：{{ totalData?.discountAmount || 0 }}</div>
          <div>总实付金额：{{ totalData?.actualPayAmount || 0 }}</div>
        </div>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 350px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { productAllApi } from "@/api/dispose";
import { Message } from "element-ui";
import {
  getSaleDetailListApi,
  getSaleDetailStatisticsApi,
} from "@/api/operator";

export default {
  name: "OrderDetail",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          dataIndex: "productIds",
          title: "机型",
          isSearch: true,
          valueType: "product",
          // searchSlot: 'fullIdPath'
        },
        {
          dataIndex: "orderStatusList",
          title: "订单状态",
          isSearch: true,
          isTable: true,
          formSpan: 8,
          valueType: "select",
          multiple: true,
          formatter: (row) => {
            switch (row.orderStatus) {
              case "WAIT_PAY":
                return "待支付";
              case "WAIT_DELIVER":
                return "待发货";
              case "WAIT_RECEIVE":
                return "待收货";
              case "WAIT_AUDIT":
                return "待审核";
              case "SUCCESS":
                return "已完成";
              case "CLOSED":
                return "已取消";
            }
          },
          option: [
            { label: "待支付", value: "WAIT_PAY" },
            { label: "待发货", value: "WAIT_DELIVER" },
            { label: "待收货", value: "WAIT_RECEIVE" },
            { label: "待审核", value: "WAIT_AUDIT" },
            { label: "已完成", value: "SUCCESS" },
            { label: "已取消", value: "CLOSED" },
          ],
        },
        {
          dataIndex: "orderType",
          title: "销售类型",
          isTable: true,
          formatter: (row) => row.orderType?.label,
          isSearch: true,
          valueType: "select",
          option: [
            { label: "销售订单", value: "SALE" },
            { label: "领料单", value: "APPLY" },
          ],
        },
        {
          dataIndex: "orderNum",
          title: "订单号",
          width: 180,
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },

        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "itemNum",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "单价",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "payAmount",
          title: "总金额",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          width: 100,
        },
        {
          dataIndex: "discountAmount",
          title: "差异金额",
          isTable: true,
          formatter: (row) =>
            row.discountAmount ? (row.discountAmount * -1).toFixed(2) : "0.00",
        },
        // {
        //   dataIndex: "shippingFee",
        //   title: "配送费",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        // },
        {
          dataIndex: "actualPayAmount",
          title: "实付金额",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          width: 100,
        },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "payTime",
          title: "支付时间",
          isTable: true,
          // isSearch: true,
          // valueType: "date-picker",
          // pickerType: "datetimerange",
          // pickerFormat: "yyyy-MM-dd HH:mm:ss",
          // valueFormat: "yyyy-MM-dd HH:mm:ss",
          width: 150,
        },
      ],
      tableData: [],
      totalData: [],
      productIdName: [],
      options: [],
    };
  },
  mounted() {
    this.refresh();
    this.getProductThird();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          gePayAmount: null,
          lePayAmount: null,
          data: parameter.payAmount,
        },
        {
          geActualPayAmount: null,
          leActualPayAmount: null,
          data: parameter.actualPayAmount,
        },
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      getSaleDetailListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
      this.getTotalData(requestParameters);
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    getTotalData(params) {
      getSaleDetailStatisticsApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        this.queryParam.productIds.push(el[el.length - 1]);
      });
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
