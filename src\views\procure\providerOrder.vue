<!--
 * @Author: wskg
 * @Date: 2024-08-07 14:18:39
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-03 11:39:45
 * @Description: 采购付款单
 -->
<template>
  <div class="purchase-order view app-container">
    <ProTable
      ref="ProTable"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
          <el-button
            v-if="row.status?.value === 'WAIT_AUDIT'"
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'audit')"
          >
            审核
          </el-button>
          <el-button
            v-if="
              row.status?.value === 'WAIT_PAY' ||
              row.status?.value === 'PART_PAY'
            "
            type="primary"
            size="mini"
            icon="el-icon-postcard"
            @click="handleEdit(row, 'edit')"
          >
            付款
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="paymentDrawer"
      size="80%"
      :title="paymentDrawerTitle"
      :confirm-loading="confirmLoading"
      :method-type="methodType"
      :confirm-text="'提交'"
      :no-confirm-footer="true"
      :no-footer="methodType === 'info'"
      @cancel="close"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
      >
        <template #purchaseItems>
          <DataTable
            ref="itemsProTable"
            :row-key="(row) => row.id"
            :columns="itemsColumns"
            :data="itemsTableData"
            :local-pagination="itemsLocalPagination"
            :height="400"
            :show-setting="false"
            :show-search="false"
            :show-loading="false"
            default-expand-all
            sticky
            :tree-props="{
              children: 'purchaseOrderGoods',
              hasChildren: 'hasChildren',
            }"
            :show-table-operation="false"
          >
            <template #applicableModels="{ row }">
              <el-popover
                v-if="!row?.manufacturerOrderCode"
                placement="bottom"
                title=""
                width="700"
                trigger="click"
              >
                <div style="margin: 20px; height: 400px; overflow-y: scroll">
                  <el-descriptions
                    class="margin-top"
                    title="适用机型"
                    :column="1"
                    border
                  >
                    <el-descriptions-item
                      v-for="item in row.productTreeDtoList"
                      :key="item.id"
                    >
                      <template slot="label"> 品牌/系列/机型 </template>
                      {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <el-button slot="reference" size="mini">适用机型</el-button>
              </el-popover>
            </template>
            <!--            <template #voucherNumber="{ row }">-->
            <!--              <el-input-->
            <!--                v-if="!row?.purchaseOrderGoods"-->
            <!--                v-model="row.receiptCode"-->
            <!--                :disabled="!(methodType === 'edit')"-->
            <!--                clearable-->
            <!--              ></el-input>-->
            <!--            </template>-->
            <!--            <template #actions>-->
            <!--              <el-button size="mini">加入退货单</el-button>-->
            <!--            </template>-->
          </DataTable>
        </template>
        <template #payVoucher>
          <ProTable
            ref="voucherProTable"
            :row-key="(row) => row.id"
            :columns="voucherColumns"
            :data="voucherTableData"
            :height="400"
            :show-setting="false"
            :show-search="false"
            :show-loading="false"
            sticky
          >
            <template #btn>
              <el-button
                v-if="methodType === 'edit'"
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="handleAddVoucher"
                >添加凭证</el-button
              >
            </template>
            <template #voucherNumber="{ row }">
              <el-input
                v-model="row.receiptCode"
                placeholder="请输入电子回单号码"
                clearable
                :disabled="!(methodType === 'edit')"
              ></el-input>
            </template>
            <template #amount="{ row }">
              <el-input-number
                v-model="row.amount"
                placeholder="请输入付款金额"
                style="width: 100%"
                :controls="false"
                :min="0"
                :disabled="!(methodType === 'edit')"
                clearable
              ></el-input-number>
            </template>
            <template #remark="{ row }">
              <el-input
                v-model="row.remark"
                placeholder="请输入付款备注"
                clearable
                :disabled="!(methodType === 'edit')"
              ></el-input>
            </template>
            <template #voucher="{ row }">
              <ProUpload
                :file-list="row.voucherImg"
                :type="methodType"
                :limit="2"
                :drag="true"
                @uploadSuccess="(e) => handleLicenseImgUploadSuccess(e, row)"
                @uploadRemove="(e) => handleLicenseImgUploadRemove(e, row)"
              />
            </template>
            <template #actions="slotProps">
              <div v-if="methodType === 'edit'" class="fixed-width">
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleRemoveVoucher(slotProps)"
                  >移除</el-button
                >
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
      <template #footer>
        <el-button
          v-if="methodType === 'edit'"
          type="primary"
          @click="handleSubmitOrder"
          >提交</el-button
        >
        <el-button
          v-if="methodType === 'audit'"
          type="danger"
          @click="handleAudit('REJECT')"
          >驳回</el-button
        >
        <el-button
          v-if="methodType === 'audit'"
          type="primary"
          @click="handleAudit('WAIT_PAY')"
          >审核通过</el-button
        >
        <el-button @click="close">取消</el-button>
      </template>
    </ProDrawer>
  </div>
</template>

<script>
import {
  addPurchasePaymentApi,
  auditPurchasePaymentApi,
  getPurchasePaymentApi,
  mergePurchasePaymentApi,
  pagePurchasePaymentApi,
} from "@/api/procure";
import { divideAmount, filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import ProUpload from "@/components/ProUpload/index.vue";
import { Message } from "element-ui";
export default {
  name: "PaymentOrder",
  components: { ProUpload },
  data() {
    return {
      methodType: "add",
      queryParam: {},
      tableData: [],
      columns: [
        // {
        //   dataIndex: "manufacterOrderCodes",
        //   title: "供应商订单编号",
        //   isTable: true,
        //   width: 180,
        // },
        // {
        //   dataIndex: "manufacterOrderCodes",
        //   title: "订单编号",
        //   placeholder: "供应商订单编号",
        //   isSearch: true,
        //   valueType: "input",
        //   width: 180,
        // },
        {
          dataIndex: "code",
          title: "付款单号",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "amount",
          title: "采购金额",
          isTable: true,
          // formatter: (row) => divideAmount(row.amount, 100),
        },
        {
          dataIndex: "refundAmount",
          title: "退货金额",
          isTable: true,
        },
        // {
        //   dataIndex: "",
        //   title: "退货单号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   width: 150,
        // },
        {
          dataIndex: "actureAmount",
          title: "应付金额",
          isTable: true,
        },
        {
          dataIndex: "status",
          title: "审核状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            {
              label: "完成付款",
              value: "COMPLETED",
            },
            {
              label: "待付款",
              value: "WAIT_PAY",
            },
            {
              label: "部分付款",
              value: "PART_PAY",
            },
            {
              label: "待审核",
              value: "WAIT_AUDIT",
            },
            {
              label: "驳回",
              value: "REJECT",
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "refundStatus",
          title: "退货状态",
          isTable: true,
          formatter: (row) => {
            switch (row.refundStatus) {
              case 0:
                return "无";
              case 1:
                return "已退货";
              case 2:
                return "部分退货";
              default:
                return "无";
            }
          },
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            {
              label: "无",
              value: 0,
            },
            {
              label: "已退货",
              value: 1,
            },
            {
              label: "部分退货",
              value: 2,
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "invoiceStatus",
          title: "开票状态",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formatter: (row) => (row.invoiceStatus === 1 ? "已开票" : "未开票"),
          option: [
            {
              label: "未开票",
              value: 0,
            },
            {
              label: "已开票",
              value: 1,
            },
          ],
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "paymentName",
          title: "付款人",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "paymentTime",
          title: "付款时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          width: 150,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
          width: 200,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          fixed: "right",
          tableSlot: "action",
          width: 160,
        },
      ],
      localPagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      paymentDrawer: false,
      paymentDrawerTitle: "",
      confirmLoading: false,
      formLoading: false,
      form: {},
      formColumns: [
        {
          dataIndex: "code",
          title: "付款单号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "auditName",
          title: "审核人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商简称",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "license",
          title: "营业执照名称",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "bank",
          title: "开户行",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "bankClient",
          title: "开户网点",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "bankAccount",
          title: "银行卡号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "ticketType",
          title: "开票类型",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "amount",
          title: "总金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },

        {
          dataIndex: "purchaseItems",
          title: "采购物品",
          isForm: true,
          formSpan: 24,
          formSlot: "purchaseItems",
        },
        {
          dataIndex: "payVoucher",
          title: "凭证",
          isForm: true,
          formSpan: 24,
          formSlot: "payVoucher",
        },
        // {
        //   dataIndex: "remark",
        //   title: "备注",
        //   isForm: true,
        //   formSpan: 24,
        //   valueType: "input",
        //   inputType: "textarea",
        //   autosize: {
        //     minRows: 2,
        //     maxRows: 4,
        //   },
        // },
      ],
      itemsColumns: [
        {
          dataIndex: "manufacturerOrderCode",
          title: "供应商订单编号",
          isTable: true,
          minWidth: 200,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "applicableModels",
          title: "适用机型",
          isTable: true,
          width: 110,
          tableSlot: "applicableModels",
        },
        {
          dataIndex: "inventoryLevels",
          title: "库存量",
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
        {
          dataIndex: "approveNum",
          title: "可采购量",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "批准数量",
          isTable: true,
        },
        {
          dataIndex: "refundNum",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "金额",
          isTable: true,
          width: 120,
        },
        // {
        //   dataIndex: "receiptCode",
        //   title: "电子回单号码",
        //   isTable: true,
        //   width: 200,
        //   tableSlot: "voucherNumber",
        // },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   width: 130,
        //   tableSlot: "actions",
        // },
      ],
      itemsTableData: [],
      itemsLocalPagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      voucherColumns: [
        {
          dataIndex: "receiptCode",
          title: "电子回单号码",
          isTable: true,
          width: 250,
          tableSlot: "voucherNumber",
        },
        {
          dataIndex: "amount",
          title: "付款金额",
          isTable: true,
          width: 150,
          align: "center",
          tableSlot: "amount",
        },
        {
          dataIndex: "voucher",
          title: "付款凭证",
          isTable: true,
          // width: 450,
          tableSlot: "voucher",
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
          width: 250,
          tableSlot: "remark",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          width: 80,
          tableSlot: "actions",
        },
      ],
      voucherTableData: [],
      voucherImg: [],
      selectedPayment: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          initiatorTimeStart: null,
          initiatorTimeEnd: null,
          data: parameter.paymentTime,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.paymentTime;
      pagePurchasePaymentApi(requestParameters).then((res) => {
        this.tableData = res.data.rows;
        this.localPagination.total = Number(res.data.total);
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      });
    },

    handleEdit(row, type) {
      this.form = {};
      this.methodType = type;
      this.paymentDrawerTitle = this.getDrawerTitle(type, row.purchaseCodes);
      getPurchasePaymentApi(row.id).then((res) => {
        const data = res.data;
        this.form = {
          ...data,
          ticketType: data.ticketType.label,
        };
        this.itemsTableData = res.data.purchasePaymentItemVos;
        this.voucherTableData = res.data.purchasePayVouchers;
      });
      this.paymentDrawer = true;
    },
    getDrawerTitle(type, purchaseCode) {
      switch (type) {
        case "audit":
          return `审核 - ${purchaseCode}`;
        case "edit":
          return `付款 - ${purchaseCode}`;
        default:
          return `查看 - ${purchaseCode}`;
      }
    },
    // 添加支付凭证
    handleAddVoucher() {
      this.voucherTableData.push({
        receiptCode: "",
        amount: null,
        remark: "",
        voucherImg: [],
      });
    },
    // 提交付款单
    handleSubmitOrder() {
      if (!this.isVoucherValid()) {
        return this.$message.error("请上传付款凭证");
      }
      // if (!this.isReceiptCodeValid()) {
      //   return this.$message.error("至少填写一个电子回单号码");
      // }
      const params = {
        code: this.form.code,
        id: this.form.id,
        remark: this.form.remark,
        purchaseGoods: this.itemsTableData,
        purchasePayVoucherList: this.voucherTableData,
      };
      addPurchasePaymentApi(params).then((res) => {
        this.close();
        this.$message.success("付款成功");
        this.$refs.ProTable.refresh();
      });
    },
    isVoucherValid() {
      return this.voucherTableData.some(
        (item) =>
          item.receiptCode &&
          item.receiptCode.trim() !== "" &&
          item.voucherImg.length > 0
      );
    },
    isReceiptCodeValid() {
      return this.itemsTableData.some((item) =>
        item.purchaseOrderGoods.some(
          (el) => el.receiptCode && el.receiptCode.trim() !== ""
        )
      );
    },
    // 审核付款单
    handleAudit(type) {
      const confirmTitle = type === "WAIT_PAY" ? "通过" : "驳回";
      this.$confirm(`确认${confirmTitle}该付款单的审核吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const params = {
          id: this.form.id,
          status: type,
        };
        auditPurchasePaymentApi(params).then((res) => {
          this.close();
          this.$refs.ProTable.refresh();
          if (type === "WAIT_PAY") {
            this.$message.success("审核通过");
          } else {
            this.$message.error("审核未通过");
          }
        });
      });
    },
    handleRemoveVoucher({ index }) {
      this.voucherTableData.splice(index, 1);
    },
    close() {
      this.form = {};
      this.paymentDrawer = false;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    handleLicenseImgUploadSuccess(result, row) {
      row.voucherImg.push(result);
    },
    handleLicenseImgUploadRemove(file, row) {
      const index = row.voucherImg.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      row.voucherImg.splice(index, 1);
    },
  },
};
</script>
