<!--
 * @Author: wskg
 * @Date: 2024-09-06 17:59:20
 * @LastEditors: wskg
 * @LastEditTime: 2024-09-07 17:00:34
 * @Description: 机器 - 分布统计
-->

<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :data="tableData"
      :columns="columns"
      @loadData="loadData"
    >
      <template #btn>
        <div v-if="type === 'machine'" class="title-box-right">
          <div>总数量：{{ machineTotalData?.totalNum || 0 }}</div>
          <div>总印量：{{ machineTotalData?.printCount || 0 }}</div>
          <div>耗材总营业额：{{ machineTotalData?.orderPay || 0 }}</div>
          <div>维修费总营业额：{{ machineTotalData?.workPay || 0 }}</div>
          <div>机器总营业额：{{ machineTotalData?.mechinePay || 0 }}</div>
        </div>
        <div v-if="type === 'part'" class="title-box-right">
          <div>总数：{{ localPagination.total || 0 }}</div>
        </div>
      </template>
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
      <template #productId>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          collapse-tags
          @change="handleSelectForm"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";
import { productAllApi } from "@/api/dispose";
import {
  getAccessoryDistributionApi,
  getMachineDistributionApi,
  getMachineDistributionByTypeApi,
} from "@/api/machine";

export default {
  name: "MachineOrPartDistribute",
  props: {
    type: {
      type: String,
      default: "machine",
    },
    columns: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      productIdName: "",
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      options: [],
      machineTotalData: {},
    };
  },
  mounted() {
    this.refresh();
    this.init();
  },
  methods: {
    async loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      const editApi = this.getMethodsApi(this.type);
      editApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      if (this.type === "machine") {
        this.getMachineTotalData(requestParameters);
      }
    },
    getMethodsApi(type) {
      switch (type) {
        case "machine":
          return getMachineDistributionApi;
        // case "part":
        //   return getAccessoryDistributionApi;
        default:
          return getMachineDistributionApi;
      }
    },
    getMachineTotalData(params) {
      getMachineDistributionByTypeApi(params).then((res) => {
        this.machineTotalData = res.data;
      });
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    /**
     * @description 获取省市区区域数据
     * @param node
     * @param {Function} resolve
     * @returns {Promise<void>}
     */
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 处理省市区数据
     * @param list
     * @returns {*}
     */
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleSelectForm(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
