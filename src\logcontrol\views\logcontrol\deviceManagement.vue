<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 设备管理页面 - 基于后端完善版实现
-->
<template>
  <div class="device-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">设备管理</h1>
        <p class="page-description">管理和监控设备信息</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <i class="el-icon-refresh"></i>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 高级搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline label-width="80px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="品牌">
              <el-input v-model="searchForm.brand" placeholder="请输入品牌" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="安卓版本">
              <el-input v-model="searchForm.manufacturer" placeholder="请输入安卓版本" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="系统类型">
              <el-select v-model="searchForm.osType" placeholder="选择系统类型" clearable style="width: 100%">
                <el-option label="Android" value="Android" />
                <el-option label="iOS" value="iOS" />
                <el-option label="HarmonyOS" value="HarmonyOS" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="设备类型">
              <el-select v-model="searchForm.isEmulator" placeholder="选择设备类型" clearable style="width: 100%">
                <el-option label="真机" :value="false" />
                <el-option label="模拟器" :value="true" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="用户编码">
              <el-input v-model="searchForm.userCode" placeholder="请输入用户编码" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="应用版本">
              <el-input v-model="searchForm.appVersion" placeholder="请输入应用版本" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="SDK版本">
              <div class="sdk-version-range">
                <el-input
                  v-model="searchForm.minSdkVersion"
                  placeholder="最小版本号"
                  style="width: 100px"
                  @input="handleNumberInput($event, 'minSdkVersion')"
                  @keypress="handleKeyPress"
                />
                <span class="version-separator">-</span>
                <el-input
                  v-model="searchForm.maxSdkVersion"
                  placeholder="最大版本号"
                  style="width: 100px"
                  @input="handleNumberInput($event, 'maxSdkVersion')"
                  @keypress="handleKeyPress"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 设备列表 -->
    <el-card class="device-list-card">
      <div slot="header">
        <span>设备列表 (总计: {{ total }})</span>
      </div>

      <el-table :data="devices" v-loading="loading" stripe>
        <!-- 基础标识 -->
        <el-table-column prop="deviceId" label="设备ID" width="140" show-overflow-tooltip />
        <el-table-column prop="userCode" label="用户编码" width="90" />
        <el-table-column prop="userName" label="用户名" width="70" />

        <!-- 设备信息 -->
        <el-table-column prop="brand" label="品牌" width="80" />
        <el-table-column prop="model" label="型号" width="170" show-overflow-tooltip />

        <!-- 系统信息 -->
        <el-table-column prop="osType" label="系统类型" width="110">
          <template slot-scope="scope">
            <el-tag :type="getOsTypeColor(scope.row.osType)" size="mini">
              {{ scope.row.osType || '未知' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="osVersion" label="系统版本" width="170" show-overflow-tooltip />
        <el-table-column prop="appVersion" label="应用版本" width="80" />

        <!-- 硬件信息 -->
        <el-table-column prop="totalMemory" label="总内存" width="70">
          <template slot-scope="scope">
            {{ formatMemorySize(scope.row.totalMemory) }}
          </template>
        </el-table-column>

        <!-- 设备状态 -->
        <el-table-column prop="isEmulator" label="设备类型" width="75">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isEmulator ? 'warning' : 'success'" size="mini">
              {{ scope.row.isEmulator ? '模拟器' : '真机' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isRooted" label="Root状态" width="78">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isRooted ? 'danger' : 'success'" size="mini">
              {{ scope.row.isRooted ? '已Root' : '未Root' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 权限配置 -->
        <el-table-column prop="currentConfigVersion" label="配置版本" width="75" />

        <!-- 最后更新时间 -->
        <el-table-column prop="lastUpdateTime" label="最后更新" width="150" show-overflow-tooltip />

        <!-- 操作列 - 固定在右侧 -->
        <el-table-column label="操作" width="160" fixed="right">
          <template slot-scope="scope">
            <el-button size="small" @click="handleViewDetail(scope.row)">详情</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 设备详情对话框 -->
    <device-detail-dialog
      :visible.sync="detailDialog"
      :device="selectedDevice"
    />
  </div>
</template>

<script>
import { deviceApi } from '@/logcontrol/api/deviceApi'
import DeviceDetailDialog from '@/logcontrol/components/DeviceManagement/DeviceDetailDialog.vue'

export default {
  name: 'DeviceManagement',
  components: {
    DeviceDetailDialog
  },
  data() {
    return {
      loading: false,
      devices: [],
      total: 0,
      searchForm: {
        brand: '',
        manufacturer: '',
        osType: '',
        minSdkVersion: '',
        maxSdkVersion: '',
        isEmulator: null,
        networkType: '',
        language: '',
        userCode: '',
        appVersion: ''
      },
      detailDialog: false,
      selectedDevice: null
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.loadDevices()
    },

    // 加载设备列表
    async loadDevices() {
      this.loading = true
      try {
        const response = await deviceApi.getDeviceList()
        this.devices = response.data || []
        this.total = this.devices.length
      } catch (error) {
        console.error('加载设备列表失败:', error)
        this.$message.error('加载设备列表失败')
        // 使用模拟数据作为降级方案
        this.devices = this.generateMockDevices()
        this.total = this.devices.length
      } finally {
        this.loading = false
      }
    },

    // 高级搜索
    async handleSearch() {
      this.loading = true
      try {
        const response = await deviceApi.advancedSearch(this.searchForm)
        this.devices = response.data || []
        this.total = this.devices.length
      } catch (error) {
        console.error('搜索设备失败:', error)
        this.$message.error('搜索设备失败')
      } finally {
        this.loading = false
      }
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        brand: '',
        manufacturer: '',
        osType: '',
        minSdkVersion: '',
        maxSdkVersion: '',
        isEmulator: null,
        networkType: '',
        language: '',
        userCode: '',
        appVersion: ''
      }
      this.loadDevices()
    },

    // 查看设备详情
    handleViewDetail(device) {
      this.selectedDevice = device
      this.detailDialog = true
    },

    // 删除设备
    async handleDelete(device) {
      try {
        await this.$confirm('确定要删除这个设备信息吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await deviceApi.deleteDevice(device.id)
        this.$message.success('删除成功')
        this.loadDevices()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除设备失败:', error)
          this.$message.error('删除失败')
        }
      }
    },



    // 刷新数据
    async refreshData() {
      try {
        await this.initData()
        this.$message.success('数据刷新成功')
      } catch (error) {
        console.error('刷新数据失败:', error)
        this.$message.error('数据刷新失败')
      }
    },

    // 获取系统类型颜色
    getOsTypeColor(osType) {
      const colorMap = {
        'Android': 'success',
        'iOS': 'primary',
        'HarmonyOS': 'warning'
      }
      return colorMap[osType] || 'info'
    },

    // 格式化内存大小
    formatMemorySize(bytes) {
      if (!bytes) return '未知'
      const size = parseInt(bytes)
      if (size >= 1024 * 1024 * 1024) {
        return `${(size / (1024 * 1024 * 1024)).toFixed(1)}GB`
      } else if (size >= 1024 * 1024) {
        return `${(size / (1024 * 1024)).toFixed(1)}MB`
      }
      return `${size}B`
    },

    // 处理数字输入
    handleNumberInput(value, field) {
      // 只允许数字
      const numericValue = value.replace(/[^0-9]/g, '')

      // 限制最大值为99
      if (numericValue && parseInt(numericValue) > 99) {
        this.searchForm[field] = '99'
      } else {
        this.searchForm[field] = numericValue
      }
    },

    // 处理按键输入
    handleKeyPress(event) {
      // 只允许数字键、退格键、删除键、方向键等
      const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab']
      const isNumber = /^[0-9]$/.test(event.key)

      if (!isNumber && !allowedKeys.includes(event.key)) {
        event.preventDefault()
      }
    },

    // 生成模拟设备数据
    generateMockDevices() {
      return [
        {
          id: 1,
          deviceId: 'device001',
          userId: '1730200832705589250',
          brand: 'Samsung',
          model: 'Galaxy S21',
          manufacturer: 'Samsung',
          osType: 'Android',
          osVersion: 'Android 11',
          sdkVersion: 30,
          screenResolution: '1080x2400',
          screenDensity: 3.55,
          totalMemory: '8589934592',
          availableStorage: '32212254720',
          cpuAbi: 'arm64-v8a',
          isRooted: false,
          isEmulator: false,
          networkType: 'WiFi',
          language: 'zh-CN',
          timeZone: 'Asia/Shanghai',
          collectCount: 156,
          userName: '张三',
          userCode: 'BYYKP',
          appVersion: '1.0-debug',
          firstCollectTime: '2025-01-20 10:00:00',
          lastUpdateTime: '2025-01-22 15:30:25',
          currentConfigVersion: '1.0.0',
          currentConfigDetails: '{"configId": 1, "logLevel": "INFO", "configName": "default", "maxLogFiles": 5}',
          permissionsInfo: '{"CAMERA": true, "STORAGE": true, "LOCATION": true}'
        },
        {
          id: 2,
          deviceId: 'bea7c767de543843',
          userId: '1730200832705589251',
          brand: 'HUAWEI',
          model: 'ADY-AL10',
          manufacturer: 'HUAWEI',
          osType: 'HarmonyOS',
          osVersion: '12 (API 31)',
          sdkVersion: 31,
          screenResolution: '1256x2627',
          screenDensity: 3.375,
          totalMemory: '12137299968',
          availableStorage: '407571238912',
          cpuAbi: 'arm64-v8a',
          isRooted: false,
          isEmulator: false,
          networkType: 'WiFi',
          language: 'zh_CN_#Hans',
          timeZone: 'Asia/Shanghai',
          collectCount: 1,
          userName: '王季春',
          userCode: 'B0000001',
          appVersion: '1.0-debug',
          firstCollectTime: '2025-07-30 19:14:56',
          lastUpdateTime: '2025-07-30 19:27:27',
          currentConfigVersion: '1.0.0',
          currentConfigDetails: '{"configId": 1, "logLevel": "INFO", "configName": "default", "collectTime": 1753874844949, "maxLogFiles": 5, "enableLocationLog": true, "logUploadInterval": 3600, "locationLogInterval": 3000}',
          permissionsInfo: '{"CAMERA": false, "STORAGE": false, "LOCATION": true, "GPS_ENABLED": true, "NETWORK_STATE": true, "BACKGROUND_LOCATION": true, "NETWORK_LOCATION_ENABLED": true}'
        },
        {
          id: 3,
          deviceId: 'device003',
          brand: 'Xiaomi',
          model: 'Mi 11',
          manufacturer: 'Xiaomi',
          osType: 'Android',
          osVersion: 'Android 11',
          sdkVersion: 30,
          screenResolution: '1440x3200',
          screenDensity: 5.15,
          cpuAbi: 'arm64-v8a',
          isEmulator: true,
          networkType: 'WiFi',
          language: 'en-US',
          timeZone: 'America/New_York',
          availableStorage: '128GB',
          collectCount: 234,
          userName: '王五',
          userCode: 'XMWW',
          appVersion: '1.5-beta',
          lastUpdateTime: '2025-01-22 16:45:33'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.device-management {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      .page-title {
        margin: 0;
        font-size: 20px;
        color: #303133;
        font-weight: 500;
      }

      .page-description {
        margin: 5px 0 0 0;
        color: #909399;
        font-size: 14px;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .search-card {
    margin-bottom: 20px;

    .el-form {
      .el-row {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .el-form-item {
        margin-bottom: 8px;

        .el-form-item__label {
          font-weight: 500;
          color: #606266;
        }
      }

      // SDK版本范围样式
      .sdk-version-range {
        display: flex;
        align-items: center;

        .version-separator {
          margin: 0 8px;
          color: #909399;
          font-weight: 500;
        }

        // 数字输入框样式
        .el-input {
          .el-input__inner {
            text-align: center;
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-weight: 500;

            &::placeholder {
              text-align: center;
              font-size: 12px;
              color: #c0c4cc;
            }

            &:focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
          }
        }
      }
    }
  }

  .device-list-card {
    .el-table {
      font-size: 13px;

      // 固定列样式优化
      .el-table__fixed-right {
        box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
      }

      // 操作按钮样式
      .el-button {
        margin-right: 4px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
