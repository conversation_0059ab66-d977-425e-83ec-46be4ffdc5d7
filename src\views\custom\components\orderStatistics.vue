<!--
 * @Author: wskg
 * @Date: 2024-08-31 10:12:11
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:22
 * @Description: 客户 - 订单查询 - 分客户查询
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      :height="550"
      sticky
      @loadData="loadData"
    >
    </ProTable>
  </div>
</template>
<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { getCustomerOrderByCustomerApi } from "@/api/customer";

export default {
  name: "OrderStatistics",
  data() {
    return {
      tableData: [],
      productIdName: "",
      productTreeOption: [],
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      queryParam: {
        startDate: null,
        endDate: null,
        costDate: [],
      },
      columns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          width: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          minWidth: 120,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
          attrs: { "value-format": "yyyy-MM" },
          width: 100,
        },

        {
          dataIndex: "totalAmount",
          title: "总消费金额",
          isTable: true,
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "orderAmount",
          title: "订单耗材金额",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "workPartAmount",
          title: "工单耗材金额",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "receiptAmount",
          title: "抄表费",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "orderNum",
          title: "订单数",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "workNum",
          title: "工单数",
          isSearch: true,
          valueType: "inputRange",
        },

        {
          dataIndex: "blackWhiteCount",
          title: "黑白印量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "colorCount",
          title: "彩色印量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "totalCount",
          title: "总印量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "mechineNum",
          title: "机器数量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "orderNum",
          title: "订单数",
          isTable: true,
          sortable: true,
        },
        {
          dataIndex: "orderAmount",
          title: "订单耗材",
          isTable: true,
        },
        {
          dataIndex: "workPartAmount",
          title: "工单耗材",
          isTable: true,
        },
        {
          dataIndex: "workNum",
          title: "工单数",
          isTable: true,
          sortable: true,
        },
        {
          dataIndex: "workLaborAmount",
          title: "工单费",
          isTable: true,
          sortable: true,
        },
        {
          dataIndex: "receiptAmount",
          title: "抄表费",
          isTable: true,
        },
      ],
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );

      const result = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.currMonth,
        },
        {
          beginTotalAmount: null,
          endTotalAmount: null,
          data: parameter.totalAmount, // 总金额
        },
        {
          beginOrderAmount: null,
          endOrderAmount: null,
          data: parameter.orderAmount, // 订单耗材金额
        },
        {
          beginRepairAmount: null,
          endRepairAmount: null,
          data: parameter.workPartAmount, // 工单耗材金额
        },
        {
          beginReceiptAmount: null,
          endReceiptAmount: null,
          data: parameter.receiptAmount, // 抄表费
        },
        {
          beginOrderNum: null,
          endOrderNum: null,
          data: parameter.orderNum, // 订单数
        },
        {
          beginWorkNum: null,
          endWorkNum: null,
          data: parameter.workNum, // 工单数
        },
        {
          beginBlackWhiteCount: null,
          endBlackWhiteCount: null,
          data: parameter.blackWhiteCount, // 黑白印量
        },
        {
          beginColorCount: null,
          endColorCount: null,
          data: parameter.colorCount, // 彩色印量
        },
        {
          beginTotalCount: null,
          endTotalCount: null,
          data: parameter.totalCount, // 总印量
        },
        {
          beginMechineNum: null,
          endMechineNum: null,
          data: parameter.mechineNum, // 机器数
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.currMonth;
      delete requestParameters.totalAmount;
      delete requestParameters.orderAmount;
      delete requestParameters.workPartAmount;
      delete requestParameters.receiptAmount;
      delete requestParameters.orderNum;
      delete requestParameters.workNum;
      delete requestParameters.blackWhiteCount;
      delete requestParameters.colorCount;
      delete requestParameters.totalCount;
      delete requestParameters.mechineNum;
      getCustomerOrderByCustomerApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped></style>
