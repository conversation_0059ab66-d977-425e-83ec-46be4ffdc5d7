<template>
  <div class="main">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>机器台数：{{ totalData?.machineCount || 0 }}</div>
          <div>黑白总印量：{{ totalData?.blackWhiteCount || 0 }}</div>
          <div>彩色总印量：{{ totalData?.colorCount || 0 }}</div>
          <div>总印量：{{ totalData?.totalCount || 0 }}</div>
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getPrintStatisticsApi,
  getPrintStatisticsByTypePageApi,
} from "@/api/machine";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "AMachineMonth",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "deviceSeqId",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.deviceGroup?.label,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(700),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "machine",
          title: "机型",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "blackWhiteCount",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorCount",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "totalCount",
          title: "总印量",
          isTable: true,
        },
        {
          dataIndex: "dataSource",
          title: "统计方式",
          isTable: true,
          formatter: (row) => row.dataSource?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "计数器上报",
              value: "IOT",
            },
            {
              label: "维修工单",
              value: "REPAIR",
            },
          ],
        },
        // {
        //   dataIndex: "status",
        //   title: "统计状态",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [],
        // },
        {
          dataIndex: "finalDate",
          title: "截止时间",
          isTable: true,
        },
      ],
      tableData: [],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.currMonth,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.currMonth;
      getPrintStatisticsApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData(requestParameters);
    },
    getTotalData(params) {
      getPrintStatisticsByTypePageApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
      // this.$refs.ProTable.listLoading = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
