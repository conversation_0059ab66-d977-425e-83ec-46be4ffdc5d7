<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-07-23 13:29:10
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 17:11:00
 * @Description: 创建领料单
 -->
<template>
  <div class="container">
    <ProDrawer
      :value="drawerVisible"
      :title="'创建领料单'"
      size="85%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="cancelDialog"
    >
      <el-steps
        align-center
        :active="actives"
        finish-status="success"
        class="steps-box"
      >
        <el-step title="客户/商品选择"></el-step>
        <el-step title="订单预览"></el-step>
      </el-steps>
      <div v-show="actives === 0" style="margin-top: 10px">
        <div class="tit-boxs">
          <el-button type="success" size="small" @click="showDialogFn(0)">
            选择客户信息
          </el-button>
        </div>
        <el-form
          ref="proform_child1"
          :model="calculateForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="客户编码:" prop="customerSeqId">
                <el-input
                  v-model="calculateForm.customerSeqId"
                  placeholder="请选择客户信息"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="店铺名称:" prop="customerName">
                <el-input
                  v-model="calculateForm.customerName"
                  placeholder="请选择客户信息"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="收货地址:"
                prop="addressId"
                :rules="[
                  {
                    required: true,
                    trigger: 'change',
                    message: '请选择收货地址',
                  },
                ]"
              >
                <el-select
                  v-model="calculateForm.addressId"
                  style="width: 100%"
                  placeholder="请选择收货地址"
                >
                  <el-option
                    v-for="item in addressList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="员工名称:" prop="consigneeName">
                <el-input
                  v-model="calculateForm.consigneeName"
                  placeholder="请选择客户信息"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="员工电话:" prop="consigneePhone">
                <el-input
                  v-model="calculateForm.consigneePhone"
                  placeholder="请选择客户信息"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="领料设备:"
                prop="deviceGroupId"
                :rules="[
                  {
                    required: true,
                    trigger: 'change',
                    message: '请选择领料设备',
                  },
                ]"
              >
                <el-select
                  v-model="calculateForm.deviceGroupId"
                  clearable
                  placeholder="请选择领料设备"
                  style="width: 100%"
                  no-data-text="该客户没有签约设备"
                  @change="handleDeviceChange"
                >
                  <el-option
                    v-for="item in deviceOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <div class="tit-boxs" style="margin-bottom: 0">
          <el-button type="success" size="small" @click="showDialogFn(1)">
            选择商品信息
          </el-button>
        </div>
        <ProTable
          ref="ProTable2s"
          :columns="creatColumn4"
          :row-key="(row) => row.saleSkuId"
          :data="tableData"
          sticky
          :show-setting="false"
          :show-loading="false"
          :height="460"
        >
          <template #picsUrl="{ row }">
            <el-image
              v-if="row.picsUrl && row.picsUrl.length > 0"
              style="width: 100px; height: 100px"
              :src="getPicsUrlImg(row)"
            />
          </template>
          <template #saleAttrVals="slotProps">
            <span
              v-for="(item, index) in slotProps.row.saleAttrVals"
              :key="index"
              style="border: 1px solid #ddd"
            >
              {{ item.name }}: {{ item.val }}
            </span>
          </template>
          <template #saleStatus="slotProps">
            {{ slotProps.row.saleStatus === "ON_SALE" ? "已上架" : "未上架" }}
          </template>
          <template #buyNum="{ row }">
            <el-input-number
              v-model="row.buyNum"
              controls-position="right"
              style="width: 100%"
              size="small"
              :min="1"
              :step="1"
            ></el-input-number>
          </template>
          <template #actions="slotProps">
            <span class="fixed-width">
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                @click="tableData.splice(slotProps.index, 1)"
              >
                删除
              </el-button>
            </span>
          </template>
        </ProTable>

        <div class="drawer-footer">
          <el-button type="primary" @click="sureSelectGoods()">
            下一步
          </el-button>
          <el-button @click="cancelDialog()">关闭</el-button>
        </div>
      </div>
      <div v-show="actives === 1" style="margin-top: 10px">
        <div class="tit-box" style="margin-top: 0">客户信息</div>
        <div>
          <el-form
            ref="proform_child1"
            :model="dataInfo"
            label-width="100px"
            class="demo-ruleForm"
          >
            <el-row>
              <el-col :span="6">
                <el-form-item label="客户编码:" prop="customerSeqId">
                  <el-input
                    v-model="dataInfo.customerSeqId"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="店铺名称:" prop="customerName">
                  <el-input
                    v-model="dataInfo.customerName"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="收货人:" prop="consigneeName">
                  <el-input
                    v-model="dataInfo.consigneeName"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="联系电话:" prop="consigneePhone">
                  <el-input
                    v-model="dataInfo.consigneePhone"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="详细地址:" prop="consigneeAddress">
                  <el-input
                    v-model="dataInfo.consigneeAddress"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  label="配送方式:"
                  prop="consigneeName"
                  :rules="[
                    {
                      required: true,
                      trigger: 'change',
                      message: '请选择配送方式',
                    },
                  ]"
                >
                  <el-select
                    v-model="express"
                    placeholder="请选择配送方式"
                    style="width: 100%"
                    @change="sureSelectGoods()"
                  >
                    <el-option
                      v-for="item in dataInfo.availableLogisticsProviders"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="支付方式:">
                  <el-select
                    v-model="payModel"
                    placeholder="请选择支付方式"
                    style="width: 100%"
                    disabled
                  >
                    <el-option
                      v-for="item in dataInfo.availablePayModeProviders"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="tit-box" style="margin: 0">商品信息</div>
        <div style="padding: 5px 10px; margin: 0 auto">
          <ProTable
            ref="ProTable3"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="creatColumn3"
            show-pagination
            row-key="id"
            :data="tableData3"
            sticky
            :show-setting="false"
            :height="420"
          >
            <template #picsUrl="slotProps">
              <img
                style="max-width: 100px; max-height: 100px"
                :src="getPicsUrlImgs(slotProps.row.saleSkuInfo)"
              />
            </template>
            <template #saleAttrVals="{ row }">
              <span
                v-for="(item, index) in row.saleSkuInfo.saleAttrVals"
                :key="index"
                style="border: 1px solid #ddd"
              >
                {{ item.name }}: {{ item.val }}
              </span>
            </template>
            <!-- 成交价 -->
            <template #actualUnitPrice="{ row }">
              <el-input-number
                v-model="row.actualUnitPrice"
                controls-position="right"
                :min="0"
                :precision="2"
                placeholder="实际成交价"
                style="width: 100%"
              ></el-input-number>
            </template>
          </ProTable>
        </div>
        <div class="totalNumber">
          <div class="totalNumber-list">
            订单商品总金额（元）：{{ dataInfo.actualGoodsAmount }}
          </div>
          <div class="totalNumber-list">
            差异金额（元）：{{
              dataInfo.discountAmount
                ? mulAmount(dataInfo.discountAmount, -1).toFixed(2)
                : "0.00"
            }}
          </div>
          <div class="totalNumber-list">
            订单运费（元）：{{ dataInfo.shippingFee }}
          </div>
          <div class="totalNumber-list">
            <!--应收（元）：{{ dataInfo.actualAmount }}-->
            应收（元）：{{ calculateActualAmount }}
          </div>
        </div>
        <div class="drawer-footer">
          <el-button type="primary" @click="actives = 0">上一步</el-button>
          <el-button
            :loading="confirmOrderLoading"
            type="primary"
            @click="sureOrderFn()"
          >
            确认领料单
          </el-button>
        </div>
      </div>
    </ProDrawer>
    <ProDialog
      :value="customerDialog"
      title="客户信息"
      width="80%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="customerDialog = false"
    >
      <ProTable
        ref="ProTables"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="customerColumns"
        :local-pagination="localPagination"
        :data="customerTableData"
        :query-param="queryParams"
        :show-setting="false"
        :height="400"
        @loadData="loadData1"
      >
        <template #actions="slotProps">
          <span class="fixed-width">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-success"
              @click="sureSelectCustom(slotProps.row)"
            >
              确定
            </el-button>
          </span>
        </template>
      </ProTable>
    </ProDialog>
    <!--    选择商品信息-->
    <ProDialog
      :value="goodsDialog"
      title="商品信息"
      width="80%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="goodsDialog = false"
    >
      <ProTable
        ref="ProTable2"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="goodsColumns"
        :row-key="(row) => row.saleSkuId"
        :local-pagination="localPaginationGoods"
        :data="goodsTableData"
        show-selection
        :query-param="queryParamGoods"
        :height="380"
        @loadData="loadData2"
        @handleSelectionChange="handleAllSelectionChange"
      >
        <template #btn>
          <el-button
            type="primary"
            size="mini"
            @click="$refs.ProTable2.$refs.ProElTable.clearSelection()"
          >
            清空选择
          </el-button>
        </template>
        <template #picsUrl="slotProps">
          <img
            style="max-width: 100px; max-height: 100px"
            :src="getPicsUrlImg(slotProps.row)"
          />
        </template>
        <template #saleAttrVals="slotProps">
          <span
            v-for="(item, index) in slotProps.row.saleAttrVals"
            :key="index"
            style="border: 1px solid #ddd"
          >
            {{ item.name }}: {{ item.val }}
          </span>
        </template>
        <template #saleStatus="slotProps">
          {{ slotProps.row.saleStatus === "ON_SALE" ? "已上架" : "未上架" }}
        </template>
      </ProTable>
      <div
        style="
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <el-button type="primary" @click="sureSelectGoodsFn">
          确认选择
        </el-button>
        <el-button @click="goodsDialog = false">关闭</el-button>
      </div>
    </ProDialog>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import {
  classifyListApi,
  getCreatePageApi,
  getCustomerStaffPageApi,
  getPreviewPageApi,
  itemSummaryListApi,
} from "@/api/goods";
import { getAddressListApi } from "@/api/operator";
import { addAmount, filterParam, mulAmount } from "@/utils";
import { dictTreeByCodeApi, dictTreeByCodeApi2 } from "@/api/user";
import { cloneDeep } from "lodash";
import { productAllApi } from "@/api/dispose";
import { getCustomerDeviceListApi } from "@/api/customer";

export default {
  name: "CreateApplyOrder",
  data() {
    return {
      drawerVisible: false,
      confirmLoading: false,
      actives: 0,
      calculateForm: {},
      creatColumn3: [
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "articleCode",
          title: "物品编码",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          minWidth: 120,
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 140,
        },
        {
          title: "商品数量",
          dataIndex: "itemNum",
          isTable: true,
          clearable: true,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "商品单价（元）",
          isTable: true,
        },
        {
          dataIndex: "discountAmount",
          title: "减免费用",
          isTable: true,
        },
        {
          dataIndex: "payAmount",
          title: "小计（元）",
          isTable: true,
        },
        {
          dataIndex: "actualUnitPrice",
          title: "成交价",
          isTable: true,
          tableSlot: "actualUnitPrice",
          width: 180,
        },
      ],
      tableData3: [],
      methodType: "add",
      tableData: [],
      creatColumn4: [
        {
          dataIndex: "fullIdPath",
          isSearch: false,
          clearable: true,
          searchSlot: "fullIdPath",
          title: "适用机型",
          valueType: "select",
        },
        {
          dataIndex: "categoryId",
          title: "商品分类",
          isSearch: false,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          clearable: true,
          isTable: true,
          isSearch: false,
          formSpan: 16,
          valueType: "input",
        },

        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
          clearable: true,
          isSearch: false,
          formSpan: 16,
          valueType: "input",
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          clearable: true,
          isSearch: false,
          formSpan: 16,
          width: 180,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          clearable: true,
          isSearch: false,
          formSpan: 16,
          width: 180,
          valueType: "input",
        },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
        },
        {
          dataIndex: "unitList",
          title: "所属单元",
          width: 150,
          isTable: false,
          isSearch: false,
          clearable: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(3200),
          formatter: (row) => row.spareLevel?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "productPartTypeList",
          title: "物品小类",
          width: 150,
          isTable: false,
          isSearch: false,
          clearable: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          formatter: (row) => row.spareLevel?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          title: "商品数量",
          dataIndex: "buyNum",
          isTable: true,
          tableSlot: "buyNum",
          width: 120,
        },
        {
          dataIndex: "Actions",
          width: 100,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      addressList: [],
      dataInfo: {},
      confirmOrderLoading: false,
      express: "jdl",
      payModel: "CYCLE",
      deviceOptions: [],
      deviceOptionsList: [],
      // 客户信息
      customerDialog: false,
      customerColumns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        // {
        //   dataIndex: "shopRecruitment",
        //   title: "店铺名称",
        //   isTable: true,
        // },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "staffName",
          title: "员工名称",
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "员工名称",
          isTable: true,
        },
        {
          dataIndex: "staffPhone",
          title: "员工电话",
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "tel",
          title: "员工电话",
          isTable: true,
        },
        {
          dataIndex: "role",
          title: "角色",
          isTable: true,
          formatter: (row) => row.role?.label,
        },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      queryParams: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      customerTableData: [],
      // 商品信息
      goodsDialog: false,
      goodsColumns: [
        {
          dataIndex: "fullIdPath",
          isSearch: true,
          title: "适用机型",
          valueType: "product",
        },
        {
          dataIndex: "categoryId",
          title: "商品分类",
          isSearch: true,
          formSpan: 8,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },

        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          minWidth: 150,
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          clearable: true,
          isSearch: true,
          formSpan: 16,
          width: 150,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          clearable: true,
          isSearch: true,
          formSpan: 16,
          width: 180,
          valueType: "input",
        },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
          minWidth: 80,
        },
        {
          dataIndex: "unitList",
          title: "所属单元",
          width: 150,
          isSearch: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(3200),
          formatter: (row) => row.spareLevel?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "productPartTypeList",
          title: "物品小类",
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          formatter: (row) => row.spareLevel?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          title: "库存数量",
          dataIndex: "inventoryNum",
          isTable: true,
          minWidth: 80,
        },
      ],
      queryParamGoods: {},
      localPaginationGoods: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      goodsTableData: [],
      selectGoodsData: [],
      currentDeviceGroup: {}, // 当前选择设备组信息
      serTypeList: [
        "BUY_FULL",
        "BUY_HALF",
        "RENT_FULL",
        "RENT_HALF",
        "ALL",
        "HALF",
        "PACKAGE_ALL",
        "PACKAGE_HALF",
        "FINANCING_FULL",
        "FINANCING_HALF",
      ],
      halfServerList: [
        "BUY_HALF",
        "RENT_HALF",
        "HALF",
        "PACKAGE_HALF",
        "FINANCING_HALF",
      ],
      halWarned: false,
    };
  },
  computed: {
    calculateActualAmount() {
      let result = 0;
      result = this.tableData3.reduce((acc, cur) => {
        return addAmount(acc, mulAmount(cur.actualUnitPrice, cur.itemNum));
      }, this.dataInfo.shippingFee ?? 0);

      return result;
    },
  },
  mounted() {},
  methods: {
    // 创建订单
    show() {
      this.deviceOptions = [];
      this.deviceOptionsList = [];
      this.addressList = [];
      this.drawerVisible = true;
    },
    mulAmount,
    loadData1(parameter) {
      this.queryParams = filterParam(
        Object.assign({}, this.queryParams, parameter)
      );
      const requestParameters = cloneDeep(this.queryParams);
      getCustomerStaffPageApi(requestParameters)
        .then((res) => {
          this.customerTableData = res.data.rows;
          this.localPagination = {
            pageNumber: parameter.pageNumber,
            pageSize: parameter.pageSize,
            total: +res.data.total,
          };
        })
        .finally(() => {
          this.$refs.ProTables
            ? (this.$refs.ProTables.listLoading = false)
            : null;
        });
    },
    loadData2(parameter) {
      this.queryParamGoods = filterParam(
        Object.assign({}, this.queryParamGoods, parameter)
      );
      const requestParameters = cloneDeep(this.queryParamGoods);
      requestParameters.saleStatus = "ON_SALE";
      itemSummaryListApi(requestParameters)
        .then((res) => {
          this.goodsTableData = res.data.rows;
          this.localPaginationGoods = {
            pageNumber: parameter.pageNumber,
            pageSize: parameter.pageSize,
            total: +res.data.total,
          };
          setTimeout(() => {
            this.$refs.ProTable2.$refs.ProElTable.doLayout();
          }, 300);
          this.$nextTick(() => {
            this.goodsTableData.forEach((row) => {
              if (
                this.tableData.some((item) => row.saleSkuId === item.saleSkuId)
              ) {
                this.$refs.ProTable2.$refs.ProElTable.toggleRowSelection(
                  row,
                  true
                );
              }
            });
          });
        })
        .finally(() => {
          this.$refs.ProTable2
            ? (this.$refs.ProTable2.listLoading = false)
            : null;
        });
    },
    handleAllSelectionChange(rows) {
      const currentDeviceGroupSerType = this.currentDeviceGroup.serType?.value;
      this.$nextTick(() => {
        // 检查服务类型：半保客户不能领取碳粉墨水
        const invalidRows = rows.filter((row) => {
          const typeValue = row.type?.value;
          return (
            this.halfServerList.includes(currentDeviceGroupSerType) &&
            typeValue === "2102"
          );
        });
        if (invalidRows.length > 0) {
          invalidRows.forEach((row) => {
            this.$refs.ProTable2.$refs.ProElTable.toggleRowSelection(
              row,
              false
            );
          });

          if (!this.halWarned) {
            this.$message.error("半保客户不能领取碳粉墨水");
            this.halWarned = true;
            setTimeout(() => {
              this.halWarned = false;
            }, 3000);
          }
        }
        this.selectGoodsData = cloneDeep(
          rows.filter((row) => !invalidRows.includes(row))
        );
      });
    },
    sureOrderFn() {
      if (!this.tableData3.length) {
        this.$message.error("商品信息不存在，请重新选择");
        this.actives = 0;
        return;
      }
      const find = this.tableData3.find(
        (item) =>
          item.actualUnitPrice === undefined || item.actualUnitPrice === null
      );
      if (find) {
        this.$message.error(`请填写${find.itemName}的成交价`);
        return;
      }
      this.$confirm("确认客户和商品信息无误并创建领料单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.confirmOrderLoading = true;
        const saleSkuBuyParams = [];
        this.tableData3.forEach((item) => {
          saleSkuBuyParams.push({
            saleSkuId: item.saleSkuId,
            buyNum: item.itemNum,
            actualUnitPrice: item.actualUnitPrice,
            deviceGroupId: item.deviceGroupId,
          });
        });
        const args = {
          buyerId: this.calculateForm.id,
          customerId: this.calculateForm.customerId,
          logisticsProvider: this.express,
          mobile: this.calculateForm.tel,
          id: this.calculateForm.ids,
          saleSkuBuyParams: saleSkuBuyParams,
          addressId: this.calculateForm.addressId,
          payMode: this.payModel, // 默认支付方式
          isMechine: false,
          orderType: "APPLY",
        };
        getCreatePageApi(args)
          .then((res) => {
            this.$message.success("领料单创建成功");
            this.drawerVisible = false;
            this.actives = 0;
            this.saleSkuBuyParams = [];
            this.calculateForm = {};
            this.customerDialog = false;
            this.goodsDialog = false;
            this.express = "jdl";
            this.payModel = "CYCLE";
            this.selectGoodsData = [];
            this.tableData = [];
            this.$emit("refresh");
          })
          .finally(() => {
            this.confirmOrderLoading = false;
          });
      });
    },
    cancelDialog() {
      this.drawerVisible = false;
      this.actives = 0;
      this.optionsGetRegion = [];
      this.calculateForm = {};
      this.tableData = [];
      this.selectGoodsData = [];
      this.$refs.ProTable2?.refresh();
    },
    showDialogFn(val) {
      if (val) {
        if (!this.calculateForm.deviceGroupId) {
          this.$message.error("请先选择领料设备");
          return;
        }
        this.currentDeviceGroup = this.deviceOptionsList.find(
          (item) => item.id === this.calculateForm.deviceGroupId
        );
        if (!this.currentDeviceGroup) {
          this.$message.error("未找到当前设备组信息，请重新选择");
          return;
        }
        this.init();
        this.queryParamGoods = {};
        this.goodsDialog = true;
        this.localPaginationGoods = {
          pageNumber: 1,
          pageSize: 10,
          total: 0,
        };
        this.$nextTick((e) => {
          this.$refs.ProTable2.refresh();
          this.$refs.ProTable2.$refs.ProElTable.clearSelection();
        });
      } else {
        this.customerDialog = true;
        this.queryParams = {};
        this.localPaginations = {
          pageNumber: 1,
          pageSize: 10,
          total: 0,
        };
        this.$nextTick((e) => {
          this.$refs.ProTables.refresh();
        });
      }
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
      classifyListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.goodsColumns[1].option = (res.data.rows || []).map((item) => ({
          label: item.name,
          value: item.id,
        }));
      });
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    getPicsUrlImgs(row) {
      return row?.picUrl?.[0]?.url;
    },
    // 确认商品
    sureSelectGoodsFn() {
      if (this.selectGoodsData.length === 0) {
        this.$message.warning("请选择商品");
        return;
      }
      if (this.tableData.length > 0) {
        this.selectGoodsData.forEach((item) => {
          if (!this.tableData.some((val) => val.saleSkuId === item.saleSkuId)) {
            // item.buyNum = 1;
            this.$set(item, "buyNum", 1);
            this.tableData.push(item);
          }
        });
      } else {
        this.selectGoodsData.forEach((item) => {
          // item.buyNum = 1;
          this.$set(item, "buyNum", 1);
        });
        this.tableData = this.selectGoodsData;
      }
      this.goodsDialog = false;
      setTimeout(() => {
        this.$refs.ProTable2.$refs.ProElTable.doLayout();
      }, 300);
    },
    // 确认商品
    sureSelectGoods() {
      if (Object.keys(this.calculateForm).length === 0) {
        this.$message.warning("请选择客户信息");
        return;
      }
      if (!this.calculateForm.addressId) {
        this.$message.error("请选择收货地址");
        return;
      }
      if (!this.calculateForm.deviceGroupId) {
        this.$message.error("请选择领料设备");
        return;
      }
      if (this.tableData.length === 0) {
        this.$message.warning("请选择需领取的商品");
        return;
      }
      const loadingInstance = Loading.service({
        text: "正在加载中...", //显示在加载图标下方的加载文案
        background: "rgba(0, 0, 0, 0.3)", //遮罩背景色
      });
      this.confirmLoading = true;
      this.saleSkuBuyParams = [];
      this.tableData.forEach((item) => {
        this.saleSkuBuyParams.push({
          saleSkuId: item.saleSkuId,
          buyNum: item.buyNum,
          deviceGroupId: this.calculateForm.deviceGroupId,
        });
      });
      this.actives = 1;
      getPreviewPageApi({
        buyerId: this.calculateForm.id,
        customerId: this.calculateForm.customerId,
        logisticsProvider: this.express,
        mobile: this.calculateForm.tel,
        saleSkuBuyParams: this.saleSkuBuyParams,
        addressId: this.calculateForm.addressId,
        isMechine: false,
      })
        .then((res) => {
          res.data.customerName = this.calculateForm.customerName;
          res.data.customerSeqId = this.calculateForm.customerSeqId;
          this.dataInfo = res.data;
          this.tableData3 = res.data.tradeOrderDetailList;
          this.calculateForm.consigneeAddress = res.data.consigneeAddress;
          this.calculateForm.ids = res.data.id;
          this.$nextTick((e) => {
            // 以服务的方式调用的 Loading 需要异步关闭
            loadingInstance.close();
          });
          this.confirmLoading = false;
        })
        .finally(() => {
          this.$refs.ProTable3
            ? (this.$refs.ProTable3.listLoading = false)
            : null;
          setTimeout(() => {
            this.$refs.ProTable3.$refs.ProElTable.doLayout();
          }, 300);
          this.$nextTick((e) => {
            // 以服务的方式调用的 Loading 需要异步关闭
            loadingInstance.close();
          });
        });
    },
    // 确认用户
    async sureSelectCustom(row) {
      row.consigneeName = row.name;
      row.consigneePhone = row.tel;
      this.calculateForm = {};
      this.addressList = [];
      // 获取用户可用地址
      try {
        const res = await getAddressListApi(row.customerId);
        this.fullAddressList = res.data;
        res.data.forEach((item) => {
          this.addressList.push({
            value: item.id,
            label: item.address,
          });
        });
        this.getCustomerProductList(row.customerId);
      } finally {
        setTimeout(() => {
          this.fullAddressList.forEach((item) => {
            if (item.isDefault) {
              this.$set(this.calculateForm, "addressId", item.id);
            }
          });
        }, 300);
      }

      this.$nextTick((e) => {
        // 以服务的方式调用的 Loading 需要异步关闭
        this.calculateForm = row;
      });
      this.customerDialog = false;
    },
    handleDeviceChange() {
      this.tableData = [];
    },
    getCustomerProductList(customerId) {
      if (!customerId) {
        return;
      }
      getCustomerDeviceListApi(customerId).then((res) => {
        this.deviceOptionsList = res.data;
        this.deviceOptions = res.data
          .filter((o) => this.serTypeList.includes(o.serType.value))
          .map((item) => {
            return {
              // label: item.deviceGroup.label + " / " + item.productInfo,
              label: `${item.deviceGroup?.label} / ${item.productInfo}（${item.serType?.label}）`,
              value: item.id,
            };
          });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.drawer-footer {
  width: 100%;
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.box {
  display: flex;
  justify-content: space-between;
  flex: 1;
}

::v-deep .el-upload--picture-card,
::v-deep .el-upload-list__item {
  width: 120px;
  height: 120px;
}

.steps-box {
  position: relative;
  width: 80%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  z-index: 2;
}

.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}

.tit-boxs {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 0 7px 20px;
  font-size: 16px;
  font-weight: 800;
}

.totalNumber {
  width: 100%;
  display: flex;
  align-items: center;

  padding: 0 20px;
  box-sizing: border-box;
  color: #606266;
  line-height: 26px;
  font-weight: 700;

  .totalNumber-list {
    margin-right: 30px;
  }
}
</style>
