<!--
 * @Author: wskg
 * @Date: 2024-08-31 16:22:37
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:51
 * @Description: 换件记录
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      row-key="label"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="550"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #deviceTree>
        <el-cascader
          v-model="queryParam.deviceGroupName"
          filterable
          clearable
          style="width: 250px"
          :options="productTreeOption"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          @change="handleProductTree"
        >
        </el-cascader>
      </template>

      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-edit-outline"
            @click="showDetail(slotProps.row)"
          >
            详情
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 查看零件更换详情 -->
    <ProDrawer
      class="margin-top"
      :value="unfoldDrawer"
      size="40%"
      :title="drawerTitle"
      :top="'10%'"
      :no-footer="true"
      @cancel="closeDrawer"
    >
      <div class="order-border-box">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="品牌">{{
            partData.brand
          }}</el-descriptions-item>
          <el-descriptions-item label="机型">{{
            partData.machine
          }}</el-descriptions-item>
          <el-descriptions-item label="设备组名称" span="2">{{
            partData.deviceGroup?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="合约类型">{{
            partData.treatyType?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="是否安装客户端">{{
            partData.regCliState == 0 ? "否" : "是"
          }}</el-descriptions-item>
          <el-descriptions-item label="零件名称">{{
            partData.articleName
          }}</el-descriptions-item>
          <el-descriptions-item label="物品编码">{{
            partData.articleCode
          }}</el-descriptions-item>
          <el-descriptions-item label="零件数量">{{
            partData.num
          }}</el-descriptions-item>
          <el-descriptions-item label="是否为PM件" span="2">{{
            partData.isPm ? "是" : "否"
          }}</el-descriptions-item>
          <el-descriptions-item label="本次维修类型">{{
            partData.currRepairType?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="上次维修类型">{{
            partData.lastRepairType?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="本次更换日期">{{
            partData.currReplaceDate
          }}</el-descriptions-item>

          <el-descriptions-item label="上次更换日期">{{
            partData.lastReplaceDate
          }}</el-descriptions-item>
          <el-descriptions-item label="本次维修工单号">
            <el-link
              type="primary"
              @click="
                getOrderInfo(
                  partData.currWorkCode,
                  partData.currRepairType?.value
                )
              "
              >{{ partData.currWorkCode }}</el-link
            >
          </el-descriptions-item>
          <el-descriptions-item label="上次维修工单号">
            <el-link
              type="primary"
              @click="
                getOrderInfo(
                  partData.lastWorkCode,
                  partData.lastRepairType?.value
                )
              "
              >{{ partData.lastWorkCode }}</el-link
            >
          </el-descriptions-item>
          <el-descriptions-item label="间隔天数" span="2">
            {{ partData.invtervalDays }}天
          </el-descriptions-item>
          <el-descriptions-item label="起始黑白打印数">{{
            partData.blackWhiteInception
          }}</el-descriptions-item>
          <el-descriptions-item label="起始彩色打印数">{{
            partData.colorInception
          }}</el-descriptions-item>
          <el-descriptions-item label="截止黑白打印数">{{
            partData.blackWhiteCutoff
          }}</el-descriptions-item>
          <el-descriptions-item label="截止彩色打印数">{{
            partData.colorCutoff
          }}</el-descriptions-item>
          <el-descriptions-item label="黑白印量">{{
            partData.blackWhiteCount
          }}</el-descriptions-item>
          <el-descriptions-item label="彩色印量" span="2">{{
            partData.colorCount
          }}</el-descriptions-item>
          <el-descriptions-item label="总打印量">{{
            partData.totalCount
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </ProDrawer>

    <!-- 商品订单详情框  -->
    <ProDrawer
      :value="orderdialogVisible"
      :title="'商品订单详情'"
      size="60%"
      :top="'10%'"
      :no-footer="true"
      @cancel="orderdialogVisible = false"
    >
      <!-- 自修登记信息 -->
      <div v-if="selfOrderInfo" class="order-fix">
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="自修登记信息"
            :column="3"
            border
          >
            <el-descriptions-item label="设备编号">
              {{ selfOrderInfo?.deviceGroupId }}
            </el-descriptions-item>
            <el-descriptions-item label="工单号" span="2">
              {{ selfOrderInfo?.code }}
            </el-descriptions-item>
            <el-descriptions-item label="机型">
              {{ selfOrderInfo?.customerDeviceGroup.productInfo }}
            </el-descriptions-item>
            <el-descriptions-item label="设备组名称">
              {{ selfOrderInfo?.customerDeviceGroup.deviceGroup.label }}
            </el-descriptions-item>
            <el-descriptions-item label="设备组状态">
              {{ selfOrderInfo?.customerDeviceGroup.deviceStatus.label }}
            </el-descriptions-item>
            <el-descriptions-item label="维修人">
              {{ selfOrderInfo?.customerStaff.name }}
            </el-descriptions-item>
            <el-descriptions-item label="维修人手机号" span="2">
              {{ selfOrderInfo?.customerStaff.tel }}
            </el-descriptions-item>
            <el-descriptions-item label="黑白印量">
              {{ selfOrderInfo?.blackWhiteCount }}
            </el-descriptions-item>
            <el-descriptions-item label="彩色印量">
              {{ selfOrderInfo?.colorCount }}
            </el-descriptions-item>
            <el-descriptions-item label="上次维修到目前印量">
              {{ selfOrderInfo?.printCount }}
            </el-descriptions-item>
            <el-descriptions-item label="原因描述" span="3">
              {{ selfOrderInfo?.excDesc }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 商品信息 -->
        <div class="m-t-8">
          <p class="tit-box m-b-12">商品信息</p>
          <ProTable
            ref="ProSPXXTable"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="spxxColumns"
            :show-pagination="false"
            :show-loading="false"
            :data="selfOrderInfo.listReplaceDetails || []"
            :show-setting="false"
            :show-search="false"
            :show-table-operator="false"
            sticky
            :height="200"
          >
          </ProTable>
        </div>
      </div>
      <!-- 维修工单信息 -->
      <div v-if="workOrderInfo" class="order-fix">
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="维修工单信息"
            :column="3"
            border
          >
            <el-descriptions-item label="设备编号">
              {{ workOrderInfo?.deviceGroupId }}
            </el-descriptions-item>
            <el-descriptions-item label="工单号">
              {{ workOrderInfo?.code }}
            </el-descriptions-item>
            <el-descriptions-item label="合约类型">
              {{ workOrderInfo?.treatyType?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="机型">
              {{ workOrderInfo?.productInfo }}
            </el-descriptions-item>
            <el-descriptions-item label="设备组名称">
              {{ workOrderInfo?.deviceGroup?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="店铺名称">
              {{ workOrderInfo?.customer?.shopRecruitment }}
            </el-descriptions-item>
            <el-descriptions-item label="店铺名称">
              {{ workOrderInfo?.customer?.name }}
            </el-descriptions-item>
            <el-descriptions-item label="维修工程师">
              {{ workOrderInfo?.engineerId?.name }}
            </el-descriptions-item>
            <el-descriptions-item label="总金额（元）">
              {{ workOrderInfo?.totalPay ? workOrderInfo?.totalPay : 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="上门费用（元）">
              {{ workOrderInfo?.visitPay ? workOrderInfo?.visitPay : 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="维修费用（元）">
              {{ workOrderInfo?.repairPay ? workOrderInfo?.repairPay : 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="远途费（元）">
              {{
                workOrderInfo?.longWayVisitPay
                  ? workOrderInfo?.longWayVisitPay
                  : 0
              }}
            </el-descriptions-item>
            <el-descriptions-item label="换件费用（元）">
              {{ workOrderInfo?.replacePay ? workOrderInfo?.replacePay : 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="维修耗材费（元）">
              {{ workOrderInfo?.itemPay ? workOrderInfo?.itemPay : 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="工程师追加费用（元）">
              {{
                workOrderInfo?.engineerAdditionalPay
                  ? workOrderInfo?.engineerAdditionalPay
                  : 0
              }}
            </el-descriptions-item>
            <el-descriptions-item label="支付方式">
              {{ workOrderInfo?.payMode?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="原因描述" span="3">
              {{ workOrderInfo?.excDesc }}
            </el-descriptions-item>
            <el-descriptions-item label="故障码" span="3">
              {{ workOrderInfo?.errorCode }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 商品信息 -->
        <div class="m-t-8">
          <p class="tit-box m-b-12">故障照片</p>
          <div class="image-view">
            <div
              v-for="(item, index) in workOrderInfo?.excPics"
              :key="index"
              class="img-list"
            >
              <img :src="item.url" alt="" srcset="" />
              <div class="mask" @click="previewImg(item.url, index)"></div>
            </div>
          </div>
          <!--          <ProTable-->
          <!--            ref="ProSPXXTable"-->
          <!--            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"-->
          <!--            :columns="spxxColumns"-->
          <!--            :show-pagination="false"-->
          <!--            :show-loading="false"-->
          <!--            :data="selfOrderInfo.listReplaceDetails || []"-->
          <!--            :show-setting="false"-->
          <!--            :show-search="false"-->
          <!--            :show-table-operator="false"-->
          <!--            sticky-->
          <!--            :height="200"-->
          <!--          >-->
          <!--          </ProTable>-->
        </div>
      </div>
    </ProDrawer>

    <!-- 预览大图 -->
    <ProDialog
      :no-footer="true"
      :value="isShowPreviewImg"
      @cancel="isShowPreviewImg = false"
    >
      <div class="preview-img">
        <img :src="previewImgUrl" alt="" srcset="" />
      </div>
    </ProDialog>
  </div>
</template>
<script>
import {
  printChangeReplaceListApi,
  printChangeDetailListApi,
  printSelfDetailListApi,
  printWorkDetailListApi,
} from "@/api/statisics";
import { cloneDeep } from "lodash";
import { filterParam, filterParamRange } from "@/utils";
import { productListApi } from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";
import { detailInfoApi, pcListPageApi } from "@/api/repair";
import { operatorTradeOrderDetailApi } from "@/api/operator";
export default {
  name: "PartCount",
  mixins: [],
  props: {},
  data() {
    return {
      productIdName: "",
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      productTreeOption: [],
      queryParam: {},
      columns: [
        {
          dataIndex: "deviceGroupName",
          title: "品牌/机型",
          valueType: "select",
          isSearch: true,
          clearable: true,
          // width: 200,
          searchSlot: "deviceTree",
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          width: 140,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          width: 140,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          valueType: "select",
          isTable: true,
          width: 70,
        },
        {
          dataIndex: "machine",
          title: "机型",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          valueType: "input",
          isTable: true,
          isSearch: true,
          placeholder: "1号机： 1",
          formatter: (row) => row.deviceGroup.label,
        },
        {
          dataIndex: "treatyTypes",
          title: "合约类型",
          valueType: "select",
          isSearch: true,
          isTable: true,
          multiple: true,
          clearable: true,
          option: [],
          formatter: (row) => row.treatyType.label,
          optionMth: () => dictTreeByCodeApi(1200),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请输入合约类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "regCliState",
          title: "安装客户端",
          valueType: "select",
          isSearch: true,
          isTable: true,
          clearable: true,
          option: [
            { value: "1", label: "是" },
            { value: "0", label: "否" },
          ],
          formatter: (row) => (row.regCliState == "1" ? "是" : "否"),
        },
        // {
        //   dataIndex: "currRepairType",
        //   title: "维修类型",
        //   isTable: true,
        //   formatter: (row) => row.currRepairType?.label,
        // },
        {
          dataIndex: "articleName",
          title: "零件名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 200,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "blackRange",
          title: "黑白印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "colorRange",
          title: "彩色印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "totalRange",
          title: "总印量",
          isSearch: true,
          valueType: "inputRange",
        },

        {
          dataIndex: "saleUnitPrice",
          title: "商品单价",
          isTable: true,
        },

        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "invtervalDays",
          title: "间隔天数",
          isTable: true,
        },
        {
          dataIndex: "blackWhiteCount",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorCount",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "totalCount",
          title: "总打印量",
          width: 150,
          isTable: true,
        },
        {
          dataIndex: "Actions",
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tableSlot: "actions",
        },
      ],
      workerList: [],
      unfoldDrawer: false,
      drawerTitle: "",
      partData: {},
      orderdialogVisible: false,
      selfOrderInfo: null,
      workOrderInfo: null,
      spxxColumns: [
        {
          dataIndex: "itemName",
          isTable: true,
          title: "商品名称",
        },
        {
          dataIndex: "itemId",
          isTable: true,
          title: "商品编号",
        },
        // {
        //   dataIndex: "code",
        //   isTable: true,
        //   title: "物品编号",
        //   formatter: (row) => row.storageArticle?.code,
        // },
        {
          dataIndex: "numberOem",
          isTable: true,
          title: "OEM编号",
          formatter: (row) => row.productPart?.oemNumber,
        },
        {
          dataIndex: "isPm",
          isTable: true,
          title: "PM件",
          formatter: (row) => (row.isPm ? "是" : "否"),
        },
        // {
        //   dataIndex: "manufacturerChannel",
        //   isTable: true,
        //   title: "制造渠道",
        //   formatter: (row) => row.storageArticle?.manufacturerChannel.label,
        // },
        {
          dataIndex: "location",
          isTable: true,
          title: "零件位置",
        },

        {
          dataIndex: "num",
          isTable: true,
          title: "数量",
        },
      ],
      remarkDialog: false,
      remarkForm: {},
      remarkFormLoading: false,
      remarkColumns: [
        {
          dataIndex: "remarks",
          title: "备注",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          attrs: {
            rows: 6,
          },
        },
      ],
      isShowPreviewImg: false,
      previewImgUrl: "",
    };
  },

  computed: {},

  watch: {},
  created() {},

  mounted() {
    this.$refs.ProTable.refresh();
    this.getProductTree();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );

      const result = [
        {
          beginBlackWhite: null,
          endBlackWhite: null,
          data: parameter.blackRange,
        },
        {
          beginColor: null,
          endColor: null,
          data: parameter.colorRange,
        },
        {
          beginCount: null,
          endCount: null,
          data: parameter.totalRange,
        },
      ];
      filterParamRange(this, this.queryParam, result);

      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.deviceGroupName;
      delete requestParameters.blackRange;
      delete requestParameters.colorRange;
      delete requestParameters.totalRange;
      printChangeReplaceListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    showDetail(row) {
      printChangeDetailListApi(row.id).then((res) => {
        this.partData = res.data;
      });

      this.drawerTitle = "查看 - " + row.customerName;
      this.unfoldDrawer = true;
    },
    /**
     * @description 关联工单号订单详情
     * @param code 工单号
     * @param type 订单类型 SELF 自修 WORK工单
     */
    getOrderInfo(code, type) {
      this.selfOrderInfo = null;
      this.workOrderInfo = null;
      if (type === "SELF") {
        printSelfDetailListApi(code).then((res) => {
          this.selfOrderInfo = res.data;
          this.orderdialogVisible = true;
        });
      } else if (type === "WORK") {
        printWorkDetailListApi(code).then((res) => {
          this.workOrderInfo = res.data;
          this.orderdialogVisible = true;
        });
      }
    },
    // 预览大图
    previewImg(url, i) {
      this.previewImgUrl = url;
      this.isShowPreviewImg = true;
    },
    /**
     * 获取订单状态
     */
    getOrderStatusChinese(orderStatus) {
      let value = "";
      switch (orderStatus) {
        case "CLOSED":
          value = "订单关闭";
          this.active = null;
          break;
        case "PAID":
          value = "已支付";
          this.active = 2;
          break;
        case "SUCCESS":
          value = "交易成功";
          this.active = 4;
          break;
        case "WAIT_DELIVER":
          value = "待发货";
          this.active = 2;
          break;
        case "WAIT_PAY":
          value = "待支付";
          this.active = 1;
          break;
        case "WAIT_RECEIVE":
          value = "待收货";
          this.active = 3;
          break;
      }
      return value;
    },
    closeDrawer() {
      this.unfoldDrawer = false;
    },
    async getProductTree() {
      try {
        const result = await productListApi({ pageNumber: 1, pageSize: 9999 });
        if (result.code === 200 && result.data) {
          this.productTreeOption = result.data;
        }
      } catch (error) {
        console.log(error);
      }
    },
    formSubmit(val) {
      console.log(val);
    },
    handleProductTree(item) {
      console.log(item);
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.image-view {
  width: 100%;
  padding: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  .img-list {
    width: 135px;
    height: 135px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    /* 鼠标经过有遮罩效果 */
    &:hover {
      position: relative;
      .mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &::after {
          content: "+";
          font-size: 36px;
          color: #fff;
        }
      }
    }
  }
}
.preview-img {
  width: 100%;
  height: 100%;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
