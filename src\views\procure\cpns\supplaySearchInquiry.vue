<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-26 15:28:51
 * @Description: 选择询价单
 -->
<template>
  <ProDialog
    :value="showInquiryDialog"
    title="选择询价单"
    width="85%"
    :confirm-loading="false"
    confirm-text="确认选择"
    top="50px"
    :no-footer="false"
    @ok="handleInquiryDialogConfirm"
    @cancel="showInquiryDialog = false"
  >
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :data="tableData"
      :columns="columns"
      show-selection
      :height="400"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleChange"
        ></el-cascader>
      </template>
      <template #goodsType>
        <el-cascader
          ref="ProductIds"
          v-model="goodsType"
          filterable
          :options="goodsTypeOptions"
          style="width: 100%"
          :props="{
            label: 'label',
            value: 'value',
            children: 'children',
            expandTrigger: 'click',
          }"
          clearable
          leaf-only
          @change="handleChangeGoodsType"
        ></el-cascader>
      </template>
      <template #machine="slotProps">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in slotProps.row.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <el-button slot="reference" type="text" size="mini">
            适用机型
          </el-button>
        </el-popover>
      </template>
    </ProTable>
  </ProDialog>
</template>

<script>
import { MessageBox, Message } from "element-ui";
import { dictTreeByCodeApi } from "@/api/user";
import { productAllApi } from "@/api/dispose";
import { pageInquiryApi } from "@/api/procure";
import { manufacturerListApi } from "@/api/store";
import { isEmpty, cloneDeep } from "lodash";
import { filterParam } from "@/utils";

export default {
  props: {
    selectedData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      goodsType: null,
      // 选择询价单弹框
      showInquiryDialog: false,
      tableData: [],
      queryParam: {},
      productIdName: "",
      options: [],
      goodsTypeOptions: [],
      columns: [
        {
          dataIndex: "fullIdPath",
          isSearch: true,
          clearable: true,
          searchSlot: "fullIdPath",
          title: "适用机型",
          valueType: "select",
        },
        {
          dataIndex: "type",
          isSearch: true,
          clearable: true,
          title: "物品大小类",
          valueType: "select",
          searchSlot: "goodsType",
        },
        {
          dataIndex: "manufacturerId",
          title: "供应商名称",
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => manufacturerListApi(),
          optionskey: {
            label: "name",
            value: "id",
          },
        },

        {
          dataIndex: "articleName",
          title: "物品名称",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
          formatter: (row) => row.storageArticle.code,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          width: 180,
          formatter: (row) => row.storageArticle.numberOem,
        },
        // {
        //   dataIndex: "manufacturerGoodsName",
        //   title: "制造商物品名称",
        //   isTable: true,
        //   width: 180,
        //   formatter: (row) => row.storageArticle.manufacturerGoodsName,
        // },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          width: 180,
          formatter: (row) => row.storageArticle?.name,
        },
        // {
        //   dataIndex: "partBrand",
        //   title: "品牌",
        //   isTable: true,
        //   width: 120,
        //   formatter: (row) => row.storageArticle.partBrand,
        // },
        {
          dataIndex: "machine",
          title: "适用机型",
          isTable: true,
          width: 120,
          tableSlot: "machine",
        },
        {
          dataIndex: "categLite",
          title: "物品大小类",
          isTable: true,
          width: 100,
          formatter: (row) => row.storageArticle?.type?.label,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          width: 100,
          formatter: (row) => row.storageArticle.manufacturerChannel.label,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          width: 80,
          formatter: (row) => row.storageArticle.unit,
        },
        {
          dataIndex: "num",
          title: "需求数量",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          width: 100,
        },

        {
          dataIndex: "createdAt",
          title: "报价日期",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "expiresTime",
          title: "有效期截至",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "comment",
          title: "备注",
          isTable: true,
          width: 120,
        },
      ],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      currentSelcetd: [],
    };
  },
  mounted() {},
  methods: {
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
        this.$refs.ProTable.refresh();
      });
      dictTreeByCodeApi(2100).then((res) => {
        this.goodsTypeOptions = res.data;
      });
    },
    loadData(params) {
      this.queryParam = filterParam(Object.assign({}, this.queryParam, params));
      this.tableData = [];
      const requestParams = cloneDeep(this.queryParam);
      const data = {
        ...requestParams,
        articleCode: params.articleCode
          ? params.articleCode
          : this.queryParam.articleCode,
      };
      pageInquiryApi(data)
        .then((res) => {
          this.queryParam.articleCode = "";
          this.tableData = res.data.rows;
          this.localPagination = {
            pageNumber: params.pageNumber,
            pageSize: params.pageSize,
            total: +res.data.total,
          };

          if (this.selectedData.length > 0) {
            this.$refs.ProTable.$refs.ProElTable.clearSelection();
            this.selectedData.map((row) => {
              this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
                row,
                true
              );
            });
          }
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    show(data, id) {
      this.queryParam = {};
      this.productIdName = "";
      this.goodsType = "";
      this.queryParam.articleCode = id;
      this.currentSelcetd = this.selectedData;
      this.showInquiryDialog = true;
      this.$nextTick(() => {
        this.$refs.ProTable.$refs.ProElTable.clearSelection();
      });
      setTimeout(() => {
        this.init();
      });
    },
    // 提交
    proSubmit(data) {
      this.showInquiryDialog = false;
    },
    // 选择表格数据
    handleSelectionChange(val) {
      this.currentSelcetd = cloneDeep(val);
    },
    handleSelectedAll(row) {
      this.$message.error("暂不支持全选，请单条选择数据");
      this.currentSelcetd = [];
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },
    handleSelected(selection, row) {
      const index = selection.findIndex((item) => item.id === row.id);
      if (index >= 0) {
        this.currentSelcetd.push(cloneDeep(row));
      } else {
        this.currentSelcetd.splice(index, 1);
      }
    },
    handleInquiryDialogConfirm() {
      this.showInquiryDialog = false;
      this.$emit("addData", this.currentSelcetd);
    },
    handleChangeGoodsType(item) {
      this.queryParam.type = item[item.length - 1];
    },
    handleChange(item) {
      this.queryParam.productTreeId = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productTreeId.push(id);
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
