/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-25 14:52:43
 * @Description:
 */
import Cookies from "js-cookie";

const TokenKey = "token";

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  localStorage.setItem("removeToken", 0);
  return Cookies.set(TokenKey, token);
}

export function removeToken() {
  localStorage.setItem("removeToken", 1);
  return Cookies.remove(TokenKey);
}

// import setting from '@/config/setting.js'
// const { tokenStorage } = setting;
// import Cookies from 'js-cookie';
// export function getAccessToken() {
//   return Cookies.get(tokenStorage);
// }

// export function setAccessToken(accessToken) {
//   return Cookies.set(tokenStorage, accessToken);
// }

// export function removeAccessToken() {
//   return Cookies.remove(tokenStorage);
// }

export function getPagePermit(pagePath) {
  const arr = JSON.parse(localStorage.getItem("systemMenu"));
  if (!arr || !pagePath) return [];
  let res = [];
  function findInChildren(arr, pagePath) {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].value == pagePath) {
        if (arr[i].children && arr[i].children.length > 0) {
          const permits = arr[i].children.filter((item) => item.permit);
          res = permits;
        }
        return true;
      } else {
        arr[i].children && findInChildren(arr[i].children, pagePath);
      }
    }
    return false;
  }
  findInChildren(arr, pagePath);
  return res;
}
