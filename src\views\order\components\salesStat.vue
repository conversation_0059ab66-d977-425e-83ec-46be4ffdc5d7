<!--
 * @Author: wskg
 * @Date: 2024-09-03 14:40:45
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 14:27:43
 * @Description: 订单 - 销售统计
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <div
          v-if="type === 'monthSale'"
          class="title-box-right"
          style="font-size: 14px; gap: 16px"
        >
          <div>总销售额：{{ totalData?.totalAmount || 0 }}</div>
          <div>机器销售额：{{ totalData?.mechineAmount || 0 }}</div>
          <div>耗材销售额：{{ totalData?.orderAmount || 0 }}</div>
          <div>维修耗材费用：{{ totalData?.workPartAmount || 0 }}</div>
          <div>维修人工费用：{{ totalData?.workLaborAmount || 0 }}</div>
          <div>包月人工费用：{{ totalData?.monthlyAmount || 0 }}</div>
          <div>全/半包费用：{{ totalData?.receiptAmount || 0 }}</div>
        </div>
        <div
          v-if="type === 'consumableStat'"
          class="title-box-right"
          style="font-size: 14px; gap: 16px"
        >
          <div>总销售额：{{ totalData?.totalAmount || 0 }}</div>
          <div>购买次数：{{ totalData?.orderCount || 0 }}</div>
          <div>购买客户数：{{ totalData?.customerNum || 0 }}</div>
        </div>
        <div
          v-if="type === 'repairStat'"
          class="title-box-right"
          style="font-size: 14px; gap: 16px"
        >
          <div>维修金额：{{ totalData?.totalPay || 0 }}</div>
          <div>维修次数：{{ totalData?.repairCount || 0 }}</div>
          <div>维修时长：{{ totalData?.repairTimeStr || 0 }}</div>
        </div>
        <div
          v-if="type === 'meterStat'"
          class="title-box-right"
          style="font-size: 14px; gap: 16px"
        >
          <div>总抄表费：{{ totalData?.totalPay || 0 }}</div>
          <div>黑白总印量：{{ totalData?.blackPrintCount || 0 }}</div>
          <div>黑白抄表费：{{ totalData?.blackPrintAmount || 0 }}</div>
          <div>机器数量：{{ totalData?.mechineCount || 0 }}</div>
          <div>彩色总印量：{{ totalData?.colorPrintCount || 0 }}</div>
          <div>彩色抄表费：{{ totalData?.colorPrintAmount || 0 }}</div>
        </div>
        <div
          v-if="type === 'machineStat'"
          class="title-box-right"
          style="font-size: 14px; gap: 16px"
        >
          <div>总销售额：{{ totalData?.totalAmount || 0 }}</div>
          <div>黑白机销售额：{{ totalData?.blackAmount || 0 }}</div>
          <div>机器数量：{{ totalData?.blackNum || 0 }}</div>
          <div>彩机销售额：{{ totalData?.colorAmount || 0 }}</div>
          <div>机器数量：{{ totalData?.colorNum || 0 }}</div>
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getMonthlyConsumableStatApi,
  getMonthlyConsumableStatSummaryApi,
  getMonthlyMachineStatApi,
  getMonthlyMachineStatSummaryApi,
  getMonthlyMeterStatApi,
  getMonthlyMeterStatSummaryApi,
  getMonthlyRepairStatApi,
  getMonthlyRepairStatSummaryApi,
  getMonthlySalesStatApi,
  getMonthlySalesStatSummaryApi,
} from "@/api/order";

export default {
  name: "SalesStat",
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "monthSale",
    },
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.currMonth, // 月份
        },
        {
          startMonth: null,
          endMonth: null,
          data: parameter.cycle, // 月份
        },
        {
          beginTotalAmount: null,
          endTotalAmount: null,
          data: parameter.totalAmount, // 总金额
        },
        {
          beginMechineAmount: null,
          endMechineAmount: null,
          data: parameter.mechineAmount, // 机器销售
        },
        {
          beginOrderAmount: null,
          endOrderAmount: null,
          data: parameter.orderAmount, // 商城耗材
        },
        {
          beginPartAmount: null,
          endPartAmount: null,
          data: parameter.workPartAmount, // 维修耗材
        },
        {
          beginLaborAmount: null,
          endLaborAmount: null,
          data: parameter.workLaborAmount, // 维修人工
        },
        {
          beginMonthAmount: null,
          endMonthAmount: null,
          data: parameter.monthlyAmount, // 包月人工
        },
        {
          beginReceiptAmount: null,
          endReceiptAmount: null,
          data: parameter.receiptAmount, // 全/半包抄表费
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      [
        "currMonth",
        "cycle",
        "totalAmount",
        "mechineAmount",
        "orderAmount",
        "workPartAmount",
        "workLaborAmount",
        "monthlyAmount",
        "receiptAmount",
      ].forEach((key) => delete requestParameters[key]);
      const editApi = this.getMethodApi(this.type);
      if (!editApi) {
        this.$refs.ProTable.listLoading = false;
        return;
      }
      editApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      const totalApi = this.getTotalDataApi(this.type);
      if (!totalApi) {
        this.totalData = {};
        return;
      }
      totalApi(requestParameters).then((res) => {
        this.totalData = res.data;
      });
    },
    getMethodApi(type) {
      switch (type) {
        case "monthSale":
          return getMonthlySalesStatApi;
        case "consumableStat":
          return getMonthlyConsumableStatApi;
        case "repairStat":
          return getMonthlyRepairStatApi;
        case "meterStat":
          return getMonthlyMeterStatApi;
        case "machineStat":
          return getMonthlyMachineStatApi;
        default:
          return "";
      }
    },
    getTotalDataApi(type) {
      switch (type) {
        case "monthSale":
          return getMonthlySalesStatSummaryApi;
        case "consumableStat":
          return getMonthlyConsumableStatSummaryApi;
        case "repairStat":
          return getMonthlyRepairStatSummaryApi;
        case "meterStat":
          return getMonthlyMeterStatSummaryApi;
        case "machineStat":
          return getMonthlyMachineStatSummaryApi;
        default:
          return "";
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
