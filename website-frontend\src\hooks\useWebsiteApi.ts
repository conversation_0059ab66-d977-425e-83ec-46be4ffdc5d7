import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { websitePublicApi } from '../services/websitePublic';
import { extractErrorMessage } from '../utils/errorHandler';
import {
  WebsiteContentApi,
  WebsiteImageApi,
  WebsiteInquiryApi,
  WebsiteConfigApi
} from '../services';
import {
  WebsiteContentType,
  WebsiteImageCategory,
  WebsiteContentCreateDto,
  WebsiteContentUpdateDto,
  WebsiteContentPageQuery,
  WebsiteImageUploadDto,
  WebsiteImagePageQuery,
  WebsiteInquirySubmitDto,
  WebsiteInquiryPageQuery,
  WebsiteInquiryUpdateDto,
  WebsiteConfigDto,
  WebsiteContent
} from '../types/api';

// ========== 查询键定义 ==========
export const queryKeys = {
  // 前端展示相关
  homepage: ['website', 'homepage'] as const,
  contentByType: (type: WebsiteContentType) => ['website', 'content', type] as const,
  contentDetail: (id: number) => ['website', 'content', 'detail', id] as const,
  imagesByCategory: (category: WebsiteImageCategory) => ['website', 'images', category] as const,
  publicImages: (params?: any) => ['website', 'images', 'public', params] as const,
  services: ['website', 'services'] as const,
  cases: ['website', 'cases'] as const,
  contact: ['website', 'contact'] as const,
  about: ['website', 'about'] as const,
  gallery: (params?: any) => ['website', 'gallery', params] as const,
  team: ['website', 'team'] as const,
  seoMeta: (type: WebsiteContentType) => ['website', 'seo', type] as const,
  publicMenus: ['website', 'menus'] as const,

  // 管理后台相关
  contentPage: (query?: WebsiteContentPageQuery) => ['admin', 'content', 'page', query] as const,
  contentById: (id: number) => ['admin', 'content', id] as const,
  imagePage: (query?: WebsiteImagePageQuery) => ['admin', 'image', 'page', query] as const,
  imageById: (id: number) => ['admin', 'image', id] as const,
  inquiryPage: (query?: WebsiteInquiryPageQuery) => ['admin', 'inquiry', 'page', query] as const,
  inquiryById: (id: number) => ['admin', 'inquiry', id] as const,
  inquiryStatistics: ['admin', 'inquiry', 'statistics'] as const,
  allConfig: ['admin', 'config', 'all'] as const,
  publicConfig: ['website', 'config', 'public'] as const,
  configByKey: (key: string) => ['admin', 'config', 'key', key] as const,
  cosCredentials: ['admin', 'cos', 'credentials'] as const,
  tencentMapKey: ['website', 'config', 'tencentMapKey'] as const,
};

// ========== 前端展示页面 Hooks ==========

/**
 * 获取首页数据
 */
export function useHomepage() {
  return useQuery({
    queryKey: queryKeys.homepage,
    queryFn: async () => {
      try {
        const result = await websitePublicApi.getHomepage();
        return result || null;
      } catch (error) {
        console.warn('Homepage API调用失败:', error);
        return null;
      }
    },
    staleTime: 1000 * 60 * 5, // 5分钟
    gcTime: 1000 * 60 * 10, // 10分钟
    retry: 1,
  });
}

/**
 * 根据类型获取内容
 */
export function useContentByType(type: WebsiteContentType) {
  console.log(`🔍 useContentByType hook 被调用，类型: ${type}`);

  return useQuery({
    queryKey: queryKeys.contentByType(type),
    queryFn: async () => {
      console.log(`📡 开始执行 queryFn，类型: ${type}`);
      try {
        const result = await websitePublicApi.getContentByType(type);
        console.log(`✅ API调用成功，类型: ${type}，结果:`, result);
        return result || null;
      } catch (error) {
        console.error(`❌ ${type} API调用失败:`, error);
        throw error; // 重新抛出错误，让React Query处理
      }
    },
    staleTime: 1000 * 60 * 5,
    retry: 1,
  });
}

/**
 * 获取内容详情
 */
export function useContentDetail(id: number) {
  return useQuery({
    queryKey: queryKeys.contentDetail(id),
    queryFn: () => websitePublicApi.getContentDetail(id),
    enabled: !!id,
  });
}

/**
 * 根据分类获取图片
 */
export function useImagesByCategory(category: WebsiteImageCategory, params?: { current?: number; size?: number }) {
  return useQuery({
    queryKey: queryKeys.imagesByCategory(category),
    queryFn: () => websitePublicApi.getImagesByCategory(category, params),
    staleTime: 1000 * 60 * 3,
  });
}

/**
 * 获取服务页面数据
 */
export function useServices() {
  return useQuery({
    queryKey: queryKeys.services,
    queryFn: async () => {
      try {
        const result = await websitePublicApi.getServices();
        // 如果返回null，提供一个默认的空对象结构
        return result || null;
      } catch (error) {
        console.warn('Services API调用失败:', error);
        // 返回null而不是抛出错误，让页面可以正常显示
        return null;
      }
    },
    staleTime: 1000 * 60 * 5,
    retry: 1, // 减少重试次数
  });
}

/**
 * 获取案例页面数据
 */
export function useCases() {
  return useQuery({
    queryKey: queryKeys.cases,
    queryFn: async () => {
      try {
        const resp = await websitePublicApi.getCases();
        // 兼容 ApiResponse 包装
        const list = (resp as any)?.data ?? resp;
        return list || [];
      } catch (error) {
        console.warn('Cases API调用失败:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5,
    retry: 1,
  });
}

/**
 * 获取联系页面数据
 */
export function useContact() {
  return useQuery({
    queryKey: queryKeys.contact,
    queryFn: async () => {
      try {
        const result = await websitePublicApi.getContact();
        return result || null;
      } catch (error) {
        console.warn('Contact API调用失败:', error);
        return null;
      }
    },
    staleTime: 1000 * 60 * 5,
    retry: 1,
  });
}

/**
 * 获取关于页面数据
 */
export function useAbout() {
  return useQuery({
    queryKey: queryKeys.about,
    queryFn: async () => {
      try {
        const result = await websitePublicApi.getAbout();
        return result || null;
      } catch (error) {
        console.warn('About API调用失败:', error);
        return null;
      }
    },
    staleTime: 1000 * 60 * 5,
    retry: 1,
  });
}

/**
 * 获取公共配置的Hook
 */
export function usePublicConfig() {
  return useQuery({
    queryKey: queryKeys.publicConfig,
    queryFn: () => WebsiteConfigApi.getPublicConfig(),
    staleTime: 5 * 60 * 1000, // 5分钟
    gcTime: 30 * 60 * 1000, // 30分钟
    // 对返回的配置数据进行处理，确保空值安全
    select: (data: WebsiteConfigDto) => {
      // 检查是否是嵌套的 RestResponse 格式
      let configData = data;
      if (data && typeof data === 'object' && 'code' in data && 'data' in data) {
        console.log('usePublicConfig: 检测到嵌套的 RestResponse 格式', data);
        configData = (data as any).data;
      }
      
      return {
        // 基础配置的空值处理
        siteTitle: configData?.siteTitle?.trim() || '复印机维修服务',
        companyName: configData?.companyName?.trim() || '',
        contactPhone: configData?.contactPhone?.trim() || '',
        servicePhone: configData?.servicePhone?.trim() || '',
        contactEmail: configData?.contactEmail?.trim() || '',
        companyAddress: configData?.companyAddress?.trim() || '',
        businessHours: configData?.businessHours?.trim() || '',
        wechatNumber: configData?.wechatNumber?.trim() || '',
        qqNumber: configData?.qqNumber?.trim() || '',
        icpNumber: configData?.icpNumber?.trim() || '',
        companyProfile: configData?.companyProfile?.trim() || '',
        servicePhilosophy: configData?.servicePhilosophy?.trim() || '',
        coreAdvantages: configData?.coreAdvantages?.trim() || '',
        businessType: configData?.businessType?.trim() || '',
        headerLogoUrl: configData?.headerLogoUrl?.trim() || '',
        footerLogoUrl: configData?.footerLogoUrl?.trim() || '',
        copyrightNotice: configData?.copyrightNotice?.trim() || '',
        icpLink: configData?.icpLink?.trim() || '',
        policeNumber: configData?.policeNumber?.trim() || '',
        policeLink: configData?.policeLink?.trim() || '',
        wechatIcon: configData?.wechatIcon?.trim() || '',
        wechatQrCode: configData?.wechatQrCode?.trim() || '',
        qqIcon: configData?.qqIcon?.trim() || '',
        qqQrCode: configData?.qqQrCode?.trim() || '',
        tencentMapKey: configData?.tencentMapKey?.trim() || '',
      };
    }
  });
}

/**
 * 获取腾讯地图API Key
 * 安全的腾讯地图Key获取，从后端获取密文，在前端解密使用
 * 与react-modules项目保持完全一致的实现
 */
export function useTencentMapKey() {
  return useQuery({
    queryKey: queryKeys.tencentMapKey,
    queryFn: async () => {
      try {
        // 从安全接口获取密文
        const encryptedKey = await websitePublicApi.getSensitiveConfigEncrypted('tencentMapKey');

        if (encryptedKey) {
          // 在前端解密
          const { decryptConfigValue } = await import('../utils/encryption');
          const decryptedKey = decryptConfigValue(encryptedKey);
          return decryptedKey;
        } else {
          // 未能获取到腾讯地图Key密文
          return '';
        }
      } catch (error) {
        console.warn('腾讯地图Key获取失败:', error);
        return '';
      }
    },
    staleTime: 1000 * 60 * 10, // 10分钟
    retry: 1,
  });
}

// ========== 管理后台 Hooks ==========

/**
 * 获取内容分页数据
 */
export function useContentPage(query?: WebsiteContentPageQuery) {
  return useQuery({
    queryKey: queryKeys.contentPage(query),
    queryFn: () => WebsiteContentApi.getContentPage(query || {}),
  });
}

/**
 * 根据ID获取内容
 */
export function useContentById(id: number) {
  return useQuery({
    queryKey: queryKeys.contentById(id),
    queryFn: () => WebsiteContentApi.getContentById(id),
    enabled: !!id,
  });
}

/**
 * 获取图片分页数据
 */
export function useImagePage(query?: WebsiteImagePageQuery) {
  return useQuery({
    queryKey: queryKeys.imagePage(query),
    queryFn: () => WebsiteImageApi.getImagePage(query || {}),
  });
}

/**
 * 获取咨询分页数据
 */
export function useInquiryPage(query?: WebsiteInquiryPageQuery) {
  return useQuery({
    queryKey: queryKeys.inquiryPage(query),
    queryFn: () => WebsiteInquiryApi.getInquiryPage(query || {}),
  });
}

/**
 * 获取咨询统计数据
 */
export function useInquiryStatistics() {
  return useQuery({
    queryKey: queryKeys.inquiryStatistics,
    queryFn: () => WebsiteInquiryApi.getInquiryStatistics(),
    refetchInterval: 1000 * 60 * 5, // 5分钟自动刷新
    retry: 1, // 减少重试次数
  });
}

/**
 * 获取所有配置
 */
export function useAllConfig() {
  return useQuery({
    queryKey: queryKeys.allConfig,
    queryFn: () => WebsiteConfigApi.getAllConfig(),
  });
}

/**
 * 获取COS上传凭证
 */
export function useCosCredentials() {
  return useQuery({
    queryKey: queryKeys.cosCredentials,
    queryFn: () => WebsiteImageApi.getCosCredentials(),
    staleTime: 1000 * 60 * 50, // 50分钟（凭证1小时过期）
  });
}

// ========== 变更操作 Hooks ==========

/**
 * 创建内容
 */
export function useCreateContent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: WebsiteContentCreateDto) => WebsiteContentApi.createContent(data),
    onSuccess: () => {
      message.success('内容创建成功');
      queryClient.invalidateQueries({ queryKey: ['admin', 'content'] });
    },
    onError: (error: any) => {
      const errorMessage = extractErrorMessage(error);
      message.error(errorMessage);
      console.error('内容创建失败:', error);
    },
  });
}

/**
 * 更新内容
 */
export function useUpdateContent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: WebsiteContentUpdateDto) => WebsiteContentApi.updateContent(data),
    onSuccess: (_, variables) => {
      message.success('内容更新成功');
      queryClient.invalidateQueries({ queryKey: ['admin', 'content'] });
      queryClient.invalidateQueries({ queryKey: queryKeys.contentById(variables.id) });
      // 刷新前端展示数据
      queryClient.invalidateQueries({ queryKey: ['website'] });
    },
    onError: (error: any) => {
      const errorMessage = extractErrorMessage(error);
      message.error(errorMessage);
      console.error('内容更新失败:', error);
    },
  });
}

/**
 * 删除内容
 */
export function useDeleteContent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: number) => WebsiteContentApi.deleteContent(id),
    onSuccess: () => {
      message.success('内容删除成功');
      queryClient.invalidateQueries({ queryKey: ['admin', 'content'] });
      queryClient.invalidateQueries({ queryKey: ['website'] });
    },
    onError: (error: any) => {
      const errorMessage = extractErrorMessage(error);
      message.error(errorMessage);
      console.error('内容删除失败:', error);
    },
  });
}

/**
 * 发布内容
 */
export function usePublishContent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: number) => WebsiteContentApi.publishContent(id),
    onSuccess: () => {
      message.success('内容发布成功');
      queryClient.invalidateQueries({ queryKey: ['admin', 'content'] });
      queryClient.invalidateQueries({ queryKey: ['website'] });
    },
    onError: (error: any) => {
      const errorMessage = extractErrorMessage(error);
      message.error(errorMessage);
      console.error('内容发布失败:', error);
    },
  });
}

/**
 * 记录图片上传
 */
export function useRecordImageUpload() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: WebsiteImageUploadDto) => WebsiteImageApi.recordImageUpload(data),
    onSuccess: () => {
      message.success('图片记录保存成功');
      queryClient.invalidateQueries({ queryKey: ['admin', 'image'] });
    },
    onError: (error) => {
      console.error('图片记录保存失败:', error);
      message.error('图片记录保存失败');
    },
  });
}

/**
 * 删除图片
 */
export function useDeleteImage() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => WebsiteImageApi.deleteImage(id),
    onSuccess: () => {
      message.success('图片删除成功');
      queryClient.invalidateQueries({ queryKey: ['admin', 'image'] });
    },
    onError: (error) => {
      message.error(`图片删除失败: ${error.message}`);
    },
  });
}

/**
 * 提交在线咨询
 */
export function useAddInquiry() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: WebsiteInquirySubmitDto) =>
      WebsiteInquiryApi.submitInquiry(data),
    onSuccess: () => {
      // message.success('咨询已提交'); // 提交成功后由页面自行处理
      queryClient.invalidateQueries({ queryKey: queryKeys.inquiryPage() });
    },
    onError: (error) => {
      // 错误信息由调用处处理
      console.error('Inquiry submission failed:', error);
      throw error;
    },
  });
}

/**
 * 更新咨询
 */
export function useUpdateInquiry() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: WebsiteInquiryUpdateDto) => {
      // 前端兼容旧枚举，自动映射
      const converted = { ...data } as any;
      if ((converted.status as any) === 'REPLIED') {
        converted.status = 'COMPLETED';
      }
      return WebsiteInquiryApi.updateInquiry(converted);
    },
    onSuccess: () => {
      message.success('咨询更新成功');
      queryClient.invalidateQueries({ queryKey: ['admin', 'inquiry'] });
    },
    onError: (error: any) => {
      const errorMessage = extractErrorMessage(error);
      message.error(errorMessage);
      console.error('咨询更新失败:', error);
    },
  });
}

/**
 * 更新配置
 */
export function useUpdateConfig() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Partial<WebsiteConfigDto>) => WebsiteConfigApi.updateConfig(data),
    onSuccess: () => {
      message.success('配置更新成功');
      queryClient.invalidateQueries({ queryKey: ['admin', 'config'] });
      queryClient.invalidateQueries({ queryKey: ['website', 'config'] });
      queryClient.invalidateQueries({ queryKey: queryKeys.publicConfig });
    },
    onError: (error: any) => {
      const errorMessage = extractErrorMessage(error);
      message.error(errorMessage);
      console.error('配置更新失败:', error);
    },
  });
}

/**
 * 根据类型获取第一条已发布内容（管理员用）
 */
export function useContentByTypeForEdit(type: WebsiteContentType) {
  return useQuery({
    queryKey: ['admin', 'content', 'first-by-type', type],
    queryFn: async () => {
      try {
        const response = await WebsiteContentApi.getFirstByType(type);
        return response;
      } catch (error) {
        console.warn(`获取${type}内容失败:`, error);
        return null;
      }
    },
    staleTime: 1000 * 60 * 2,
    retry: 1,
  });
}

// ========== 统一保存 Hooks ==========

/**
 * 统一的内容保存 Hook
 * 根据页面类型自动处理保存逻辑、缓存失效和成功消息
 */
export function useSaveContent(pageType: WebsiteContentType) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: WebsiteContentUpdateDto) => WebsiteContentApi.updateContent(data),
    onMutate: async (newData) => {
      // 乐观更新：先取消相关查询
      await queryClient.cancelQueries({ queryKey: queryKeys.contentByType(pageType) });
      
      // 获取当前数据快照
      const previousData = queryClient.getQueryData(queryKeys.contentByType(pageType));
      
      // 乐观地更新缓存
      queryClient.setQueryData(queryKeys.contentByType(pageType), (old: any) => {
        if (!old) return old;
        return {
          ...old,
          content: newData.content,
          title: newData.title,
          summary: newData.summary,
          updatedAt: new Date().toISOString()
        };
      });
      
      // 返回回滚用的数据
      return { previousData };
    },
    onError: (err: any, newData, context) => {
      // 发生错误时回滚
      if (context?.previousData) {
        queryClient.setQueryData(queryKeys.contentByType(pageType), context.previousData);
      }
      const errorMessage = extractErrorMessage(err);
      message.error(errorMessage);
      console.error('内容保存失败:', err);
    },
    onSuccess: (data, variables) => {
      message.success('保存成功！');
      
      // 刷新前端展示/后台编辑缓存
      queryClient.invalidateQueries({ queryKey: queryKeys.contentByType(pageType) });
      queryClient.invalidateQueries({ queryKey: ['admin', 'content', 'first-by-type', pageType] });
      queryClient.invalidateQueries({ queryKey: queryKeys.publicMenus });
      queryClient.invalidateQueries({ queryKey: ['website'] });
    },
    onSettled: () => {
      // 无论成功还是失败，都重新获取数据确保一致性
      queryClient.invalidateQueries({ queryKey: queryKeys.contentByType(pageType) });
    },
  });
}

/**
 * 统一的配置保存 Hook
 * 处理全局配置的保存逻辑和缓存失效
 */
export function useSaveConfig() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Partial<WebsiteConfigDto>) => WebsiteConfigApi.updateConfig(data),
    onMutate: async (newData) => {
      // 乐观更新：先取消相关查询
      await queryClient.cancelQueries({ queryKey: queryKeys.publicConfig });
      await queryClient.cancelQueries({ queryKey: queryKeys.allConfig });
      
      // 获取当前数据快照
      const previousPublicConfig = queryClient.getQueryData(queryKeys.publicConfig);
      const previousAllConfig = queryClient.getQueryData(queryKeys.allConfig);
      
      // 乐观地更新缓存
      queryClient.setQueryData(queryKeys.publicConfig, (old: any) => {
        if (!old) return old;
        return { ...old, ...newData };
      });
      
      queryClient.setQueryData(queryKeys.allConfig, (old: any) => {
        if (!old) return old;
        return { ...old, ...newData };
      });
      
      // 返回回滚用的数据
      return { previousPublicConfig, previousAllConfig };
    },
    onError: (err: any, newData, context) => {
      // 发生错误时回滚
      if (context?.previousPublicConfig) {
        queryClient.setQueryData(queryKeys.publicConfig, context.previousPublicConfig);
      }
      if (context?.previousAllConfig) {
        queryClient.setQueryData(queryKeys.allConfig, context.previousAllConfig);
      }
      const errorMessage = extractErrorMessage(err);
      message.error(errorMessage);
      console.error('配置保存失败:', err);
    },
    onSuccess: () => {
      message.success('配置保存成功！');
      
      // 失效所有相关缓存
      const invalidatePromises = [
        queryClient.invalidateQueries({ queryKey: queryKeys.publicConfig }),
        queryClient.invalidateQueries({ queryKey: queryKeys.allConfig }),
        queryClient.invalidateQueries({ queryKey: ['admin', 'config'] }),
        queryClient.invalidateQueries({ queryKey: ['website', 'config'] }),
        // 配置更改可能影响所有前端页面，所以失效所有页面数据
        queryClient.invalidateQueries({ queryKey: queryKeys.homepage }),
        queryClient.invalidateQueries({ queryKey: queryKeys.services }),
        queryClient.invalidateQueries({ queryKey: queryKeys.about }),
        queryClient.invalidateQueries({ queryKey: queryKeys.contact }),
        queryClient.invalidateQueries({ queryKey: queryKeys.cases }),
        queryClient.invalidateQueries({ queryKey: queryKeys.publicMenus }),
      ];
      
      Promise.all(invalidatePromises).catch(console.warn);
    },
  });
} 