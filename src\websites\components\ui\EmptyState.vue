<template>
  <div class="empty-state" :class="className">
    <div class="empty-content">
      <div class="empty-icon">
        <i :class="icon"></i>
      </div>
      <div class="empty-text">
        <h3 class="empty-title">{{ title }}</h3>
        <p class="empty-description">{{ description }}</p>
      </div>
      <div v-if="$slots.action" class="empty-actions">
        <slot name="action"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmptyState',
  props: {
    title: {
      type: String,
      default: '暂无数据'
    },
    description: {
      type: String,
      default: '当前没有可显示的内容'
    },
    icon: {
      type: String,
      default: 'el-icon-box'
    },
    className: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  
  .empty-content {
    text-align: center;
    max-width: 400px;
    
    .empty-icon {
      margin-bottom: 16px;
      
      i {
        font-size: 48px;
        color: #cbd5e0;
      }
    }
    
    .empty-text {
      margin-bottom: 24px;
      
      .empty-title {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #4a5568;
      }
      
      .empty-description {
        margin: 0;
        font-size: 14px;
        color: #718096;
        line-height: 1.5;
      }
    }
    
    .empty-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }
}

// 紧凑模式
.empty-state.compact {
  padding: 20px;
  
  .empty-content {
    .empty-icon i {
      font-size: 32px;
    }
    
    .empty-text {
      margin-bottom: 16px;
      
      .empty-title {
        font-size: 14px;
      }
      
      .empty-description {
        font-size: 13px;
      }
    }
  }
}
</style>
