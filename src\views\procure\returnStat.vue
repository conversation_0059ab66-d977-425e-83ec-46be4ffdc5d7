<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane lazy label="按供应商" name="first">
        <!-- <RProvider /> -->
        <ProcureReturnStat type="provider" :columns="providerColumns" />
      </el-tab-pane>
      <el-tab-pane lazy label="按机型" name="second">
        <!-- <RMachine /> -->
        <ProcureReturnStat type="model" :columns="modelColumns" />
      </el-tab-pane>
      <!--<el-tab-pane lazy label="按零件编号" name="third">-->
      <!--  &lt;!&ndash; <RPart /> &ndash;&gt;-->
      <!--  &lt;!&ndash;<ProcureReturnStat type="part" :columns="partColumns" />&ndash;&gt;-->
      <!--  按零件编号-->
      <!--</el-tab-pane>-->
    </el-tabs>
  </div>
</template>

<script>
import ProcureReturnStat from "./cpns/procureReturnStat.vue";
export default {
  name: "ReturnStat",
  components: { ProcureReturnStat },
  data() {
    return {
      activeName: "first",
      providerColumns: [
        {
          title: "供应商编号",
          dataIndex: "manufacturerCode",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          title: "供应商名称",
          dataIndex: "manufacturerName",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 200,
        },
        {
          title: "采购时间",
          dataIndex: "month",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
        {
          title: "采购数量",
          dataIndex: "number",
          isTable: true,
        },
        {
          title: "退货数量",
          dataIndex: "refundNum",
          isTable: true,
        },
        {
          title: "采购金额",
          dataIndex: "amount",
          isTable: true,
        },
        {
          title: "退货金额",
          dataIndex: "refundAmount",
          isTable: true,
        },
      ],
      modelColumns: [
        {
          dataIndex: "series",
          title: "机型",
          isTable: true,
        },
        {
          dataIndex: "productIds",
          title: "机型",
          isSearch: true,
          clearable: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
        {
          title: "时间段",
          dataIndex: "month",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
        {
          title: "采购数量",
          dataIndex: "number",
          isTable: true,
        },
        {
          title: "退货数量",
          dataIndex: "refundNum",
          isTable: true,
        },
        {
          title: "实收数量",
          dataIndex: "receiveNum",
          isTable: true,
        },
        {
          title: "采购金额",
          dataIndex: "amount",
          isTable: true,
        },
        {
          title: "退货金额",
          dataIndex: "refundAmount",
          isTable: true,
        },
        // {
        //   title: "实付金额",
        //   dataIndex: "payAmount",
        //   isTable: true,
        // },
      ],
      partColumns: [],
    };
  },
};
</script>

<style scoped lang="scss"></style>
