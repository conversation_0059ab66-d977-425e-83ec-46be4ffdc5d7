<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <div v-if="type === 'day'" class="title-box-right">
          <div>客户数： {{ dayTotalData?.custCount || 0 }}</div>
          <div>访问次数： {{ dayTotalData?.viewCount || 0 }}</div>
        </div>
      </template>
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getCustomerVisitByPageApi,
  getCustomerVisitDayByPageApi,
  getCustomerVisitDayStatApi,
  getCustomerVisitStatApi,
} from "@/api/customer";
import { optionsGetRegionApi } from "@/api/operator";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";

export default {
  name: "VisitStat",
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "record",
    },
  },
  data() {
    return {
      tableData: [],
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      queryParam: {},
      dayTotalData: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startTime: null,
          endTime: null,
          data: parameter.currDate,
        },
        {
          startTime: null,
          endTime: null,
          data: parameter.finishedAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.currDate;
      delete requestParameters.finishedAt;
      // const editApi =
      //   this.type === "record"
      //     ? getCustomerVisitByPageApi
      //     : getCustomerVisitStatApi;
      const editApi = this.getEditApi(this.type);
      editApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      if (this.type === "day") {
        this.getDayVisitData(requestParameters);
      }
    },
    getEditApi(type) {
      switch (type) {
        case "day":
          return getCustomerVisitDayByPageApi;
        case "record":
          return getCustomerVisitByPageApi;
        case "stat":
          return getCustomerVisitStatApi;
      }
    },
    getDayVisitData(params) {
      getCustomerVisitDayStatApi(params).then((res) => {
        this.dayTotalData = res.data;
      });
    },
    /**
     * @description 获取省市区区域数据
     * @param node
     * @param {Function} resolve
     * @returns {Promise<void>}
     */
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 处理省市区数据
     * @param list
     * @returns {*}
     */
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
