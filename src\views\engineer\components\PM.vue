<!--
 * @Author: yangzhong
 * @Date: 2023-11-08 14:11:34
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 11:21:24
 * @Description: 
-->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :layout="{ labelWidth: '120px' }"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <!-- <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleEdit(null, 'add')"
          >新增</el-button
        >
      </template> -->
      <template #action="{ row }">
        <div class="fixed-width">
          <!-- <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'info')"
            >查看</el-button
          > -->
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleEdit(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </div>
      </template>
      <template #machine>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
    </ProTable>

    <!-- 修改弹窗 -->
    <ProDialog
      :value="showDialog"
      :title="dialogTitle"
      width="1000px"
      :no-footer="editType === 'info'"
      :confirm-loading="dialogLoading"
      top="50px"
      @ok="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="editForm"
        :form-param="editForm"
        :form-list="columns"
        :confirm-loading="dialogLoading"
        :layout="{ formWidth: '100%', labelWidth: '150px' }"
        :open-type="editType"
        @proSubmit="editFormSubmit"
      >
        <template #model>
          <el-cascader
            v-model="editForm.model"
            filterable
            style="width: 100%"
            :disabled="editType === 'info'"
            :options="productTreeOption"
            :props="{ label: 'name', value: 'id' }"
            @change="handleProductTree"
          >
          </el-cascader>
        </template>
        <template #position>
          <div>
            <el-tag
              v-for="(tag, ind) in editForm.position"
              :key="tag"
              closable
              :disable-transitions="false"
              @close="handleClose(index, ind)"
            >
              {{ tag }}
            </el-tag>

            <el-input
              v-if="inputVisible"
              ref="saveTagInput"
              v-model="inputValue"
              style="width: 100px"
              class="input-new-tag"
              size="small"
              @keyup.enter.native="handleInputConfirm()"
              @blur="handleInputConfirm()"
            >
            </el-input>
            <el-button
              v-else
              class="button-new-tag"
              size="small"
              @click="showTag()"
              >+ 标签</el-button
            >
          </div>
        </template>
      </ProForm>
      <ProTable
        v-if="editType === 'add'"
        ref="PartTable"
        :data="partData"
        :columns="partColumns"
        :local-pagination="partLocalPagination"
        :show-loading="false"
        :show-pagination="true"
        :show-search="false"
        :height="400"
      >
        <template #btn>
          <el-button
            type="success"
            class="add-btn"
            size="mini"
            icon="el-icon-plus"
            @click="handleOpenChooseDialog"
          >
            增加零件信息
          </el-button>
        </template>
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDeletePartList(row)"
            >
              移除
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDialog>
    <!-- 选择零件弹窗 -->
    <ProDialog
      :value="showPartDialog"
      title="选择零件"
      width="1200px"
      :confirm-loading="partDialogLoading"
      top="50px"
      @ok="handleChooseDialogConfirm"
      @cancel="handleChooseDialogCancel"
    >
      <OEMS @chooseOem="handleSelectionChange"></OEMS>
      <!-- <ProTable ref="ChoosePartTable" row-key="id" :data="choosePartData" :columns="partColumns"
        :query-param="choosePartQueryParam" :show-loading="false" :show-selection="true" show-search :height="400"
        :show-setting="false" @handleSelectionChange="handleSelectionChange" @loadData="load2"></ProTable> -->
    </ProDialog>
  </div>
</template>

<script>
import { Message, MessageBox } from "element-ui";
import OEMS from "@/views/engineer/components/oems.vue";
import { dictTreeByCodeApi } from "@/api/user";
import { isEmpty, cloneDeep } from "lodash";

import {
  partListApi,
  partPMPageApi,
  partPMDeleteApi,
  productListApi,
  getPartByModelApi,
  bomEditApi,
  productAllApi,
} from "@/api/dispose";
export default {
  name: "PM",
  components: { OEMS },
  data() {
    return {
      inputVisible: false,
      inputValue: "",
      showCheckBtn: false,
      options: [],
      columns: [
        {
          dataIndex: "oemNumber",
          title: "原厂零件编号",
          isTable: true,
        },
        {
          dataIndex: "name",
          title: "原厂编号/零件",
          isTable: false,
          clearable: true,
          placeholder: "原厂零件编号/零件名称",
          span: 4,
          isSearch: true,
          valueType: "input",
          // searchSpan: 6,
        },
        {
          dataIndex: "ch",
          title: "零件中文名称",
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "machine",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "lastIds",
          title: "机型",
          isSearch: true,
          searchSpan: 6,
          valueType: "product",
          // searchSlot: "machine",
          width: 100,
        },
        {
          dataIndex: "pmCycle",
          title: "厂商PM周期",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入厂商PM周期",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "repFrequency",
          title: "更换频次",
          isTable: true,
          width: 120,
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 12,
          option: [],
          optionMth: () => dictTreeByCodeApi(3300),
          formatter: (row) => row.repFrequency.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "spareLevel",
          title: "备件等级",
          isTable: true,
          // valueType: "select",
          // clearable: true,
          // formSpan: 12,
          // option: [],
          // optionMth: () => dictTreeByCodeApi(3400),
          formatter: (row) => row.spareLevel.label,
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
        },

        {
          dataIndex: "correctedLifespan",
          title: "运营修正生命周期",
          isForm: true,
          isTable: true,
          valueType: "input",
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入运营修正生命周期",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "spareLevel",
          title: "备件等级",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 12,
          option: [],
          optionMth: () => dictTreeByCodeApi(3400),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "position",
          title: "更换位置",
          isForm: true,
          formSpan: 24,
          formSlot: "position",
        },

        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 180,
        },
      ],
      tableData: [],
      localPagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      queryParam: {},
      editForm: {},
      showDialog: false,
      dialogTitle: "",
      dialogLoading: false,
      editType: "add",

      partData: [],
      partColumns: [
        // {
        //   title: '机型',
        //   dataIndex: 'id',
        //   isTable: true
        // },
        {
          title: "所属单元",
          dataIndex: "unit",
          isTable: true,
        },

        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "ch",
          title: "零件名称",
          isTable: true,
          clearable: true,
          span: 4,
          isSearch: true,
          valueType: "input",
        },
        {
          title: "零件英文名称",
          dataIndex: "en",
          isTable: true,
        },
        // {
        //   title: '操作',
        //   dataIndex: 'action',
        //   isTable: true,
        //   tableSlot: 'action',
        // },
      ],
      partLocalPagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },

      productTreeOption: [],
      showPartDialog: false,
      partDialogLoading: false,
      choosePartData: [],
      choosePartQueryParam: {},
      choosePartSelection: [],
    };
  },
  mounted() {
    this.getProductTree();
    this.refresh();
    productAllApi().then((res) => {
      console.log(res, "66666666");
      this.options = res.data;
      this.$refs.ProTable.refresh();
    });
  },
  methods: {
    async loadData(params) {
      try {
        const result = await partPMPageApi(params);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    async getProductTree() {
      try {
        const result = await productListApi({ pageNumber: 1, pageSize: 9999 });
        if (result.code === 200 && result.data) {
          this.productTreeOption = result.data;
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleSelect(item) {
      this.queryParam.lastIds = [];
      item.map((el) => {
        this.queryParam.lastIds.push(el[el.length - 1]);
      });
    },
    handleEdit(row, type) {
      console.log(row);
      this.showDialog = true;
      this.dialogTitle =
        type === "add"
          ? "新增零件PM信息"
          : type === "info"
          ? "零件PM信息"
          : "编辑零件PM信息";
      this.editType = type;
      if (row) {
        this.editForm = cloneDeep(row);
        this.editForm.repFrequency = this.editForm.repFrequency?.value;
        this.editForm.spareLevel = this.editForm.spareLevel?.value;
        this.partData = [{ id: row.id }];
        this.inputVisible = false;
        this.inputValue = "";
        this.editForm.position = this.editForm.position || [];
        // this.columns.find((item) => item.dataIndex === "model")?.isForm = false;
      } else {
        this.editForm = {};
        this.partData = [];
        // this.columns.find(item => item.dataIndex === 'model').isForm = true
      }
    },
    handleDelete(row) {
      MessageBox.confirm("移除后，该条数据将从列表中移除，确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const obj = {
          id: row.id,
          isPm: false,
        };
        const result = await bomEditApi(obj);
        if (result.code === 200) {
          Message.success("删除成功");
          this.refresh();
        }
      });
    },
    handleDialogConfirm() {
      this.$refs.editForm.handleSubmit();
    },
    handleDialogCancel() {
      this.showDialog = false;
    },
    async editFormSubmit(val) {
      try {
        if (this.partData.length === 0 && this.editType === "add") {
          throw new Error("请选择零件");
        }
        val.unit = val.unit.value;
        console.log("32423423532463575763524");
        const ids = this.partData.map((item) => item.id);
        const arr = [];
        arr.push({ ...val, ids });
        const result = await bomEditApi(arr);
        if (result.code === 200) {
          Message.success("操作成功");
          this.showDialog = false;
          this.refresh();
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    // 产品树变化 列表将清空
    handleProductTree() {
      this.partData = [];
    },
    async handleOpenChooseDialog(params) {
      // try {
      //   const { model } = this.editForm
      //   if (!model) throw new Error('请先选择机型')
      // } catch (error) {
      //   Message.error(error.message)
      // }
      this.showPartDialog = true;
      setTimeout(() => {
        this.$refs.ChoosePartTable.refresh();
      }, 300);
    },
    async load2(params) {
      // const { model } = this.editForm
      // const lastIds = [model[model.length - 1]]
      const result = await partListApi({
        ...params,
        // lastIds,
        pageNumber: 1,
        pageSize: 9999,
        // hasPm: false
      });
      this.choosePartData = result.data.rows;
      this.$refs.ChoosePartTable.listLoading = false;
    },
    handleDeletePartList(row) {
      this.partData = this.partData.filter((item) => item.id !== row.id);
      // this.partLocalPagination.total = this.partData.length;
    },
    handleChooseDialogConfirm() {
      this.partData.push(...this.choosePartSelection);
      const idMap = {};
      this.partData = this.partData.reduce((preVal, curVal) => {
        idMap[curVal.id] ? "" : (idMap[curVal.id] = preVal.push(curVal));
        return preVal;
      }, []);
      this.choosePartData = [];
      this.showPartDialog = false;
      // this.partLocalPagination.total = this.partData.length;
    },
    handleChooseDialogCancel() {
      this.showPartDialog = false;
    },
    handleSelectionChange(val) {
      this.choosePartSelection = val;
    },
    showTag() {
      this.inputVisible = true;
      console.log(this.editForm);
    },
    handleClose(index, ind) {
      this.editForm.position.splice(ind, 1);
    },
    handleInputConfirm() {
      if (this.inputValue) {
        this.editForm.position.push(this.inputValue);
        this.inputVisible = false;
        this.inputValue = "";
      }
    },
  },
};
</script>
