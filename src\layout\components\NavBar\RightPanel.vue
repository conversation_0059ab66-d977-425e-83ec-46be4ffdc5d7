<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-31 18:03:09
 * @Description: 
 -->
<template>
  <div class="right-panel">
    <!--<el-popover v-if="notice" placement="bottom" :width="320" trigger="hover">-->
    <!--  <template #reference>-->
    <!--    <i class="el-icon-bell icon-hover refresh"></i>-->
    <!--  </template>-->
    <!--  <div class="message-box">-->
    <!--    <div class="todo-header">-->
    <!--      <span class="title">待办事项</span>-->
    <!--      <span class="count">({{ tableData.length }})</span>-->
    <!--    </div>-->
    <!--    <div class="todo-list">-->
    <!--      <div-->
    <!--        v-for="(item, index) in displayedTodoList"-->
    <!--        :key="index"-->
    <!--        class="todo-item"-->
    <!--      >-->
    <!--        <div class="todo-content">-->
    <!--          <el-tag-->
    <!--            :type="-->
    <!--              item.priority === 'high'-->
    <!--                ? 'danger'-->
    <!--                : item.priority === 'medium'-->
    <!--                ? 'warning'-->
    <!--                : 'info'-->
    <!--            "-->
    <!--            size="small"-->
    <!--          >-->
    <!--            {{ item.type }}-->
    <!--          </el-tag>-->
    <!--          <span class="todo-title">{{ item.title }}</span>-->
    <!--        </div>-->
    <!--        <div class="todo-time">{{ item.createTime }}</div>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <div class="todo-footer">-->
    <!--      <el-button type="primary" size="small" link @click="showAllTodos">-->
    <!--        查看更多-->
    <!--        <i class="el-icon-arrow-right"></i>-->
    <!--      </el-button>-->
    <!--    </div>-->
    <!--  </div>-->
    <!--</el-popover>-->
    <i class="el-icon-refresh icon-hover refresh" @click="handleRefresh"></i>
    <Avatar :color="color" />

    <!-- 待办事项详情弹窗 -->
    <ProDialog
      :value="dialogVisible"
      title="待办事项清单"
      width="60%"
      top="2%"
      no-footer
      @cancel="dialogVisible = false"
    >
      <ProTable
        ref="ProTable"
        :query-param="queryParams"
        :local-pagination="localPagination"
        :show-loading="false"
        :show-setting="false"
        :show-search="false"
        :columns="columns"
        :data="tableData"
      >
        <template #status="{ row }">
          <el-tag :type="row.status === 'pending' ? 'warning' : 'success'">
            {{ row.status === "pending" ? "待处理" : "已完成" }}
          </el-tag>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import Avatar from "../Avatar/index.vue";
// import FullScreen from '@/components/FullScreen/index.vue';

export default {
  name: "RightPanel",
  components: {
    Avatar,
    // Cell,
    // FullScreen
  },
  mixins: [],
  props: {
    color: {
      type: String,
      default: "#666",
    },
  },
  data() {
    return {
      notice: true,
      activeName: "first",
      dialogVisible: false,
      queryParams: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "type",
          title: "类型",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "title",
          title: "标题",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "createTime",
          title: "创建时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          tableSlot: "status",
          isSearch: true,
          valueType: "select",
          option: [],
          width: 100,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 100,
        },
      ],
      tableData: [
        {
          type: "审批",
          title: "新入职员工张三的入职审批",
          createTime: "2025-03-31 10:00:00",
          priority: "high",
          status: "pending",
        },
        {
          type: "维修",
          title: "客户A公司打印机维修申请",
          createTime: "2025-03-31 09:30:00",
          priority: "medium",
          status: "pending",
        },
        {
          type: "合同",
          title: "B公司续约合同待审核",
          createTime: "2025-03-31 09:00:00",
          priority: "high",
          status: "pending",
        },
        {
          type: "报告",
          title: "上周工作总结提交",
          createTime: "2025-03-30 18:00:00",
          priority: "low",
          status: "pending",
        },
        {
          type: "会议",
          title: "部门周会",
          createTime: "2025-03-30 17:30:00",
          priority: "medium",
          status: "completed",
        },
      ],
    };
  },

  computed: {
    displayedTodoList() {
      return this.tableData.slice(0, 5);
    },
  },

  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleRefresh() {
      location.reload();
    },
    showAllTodos() {
      this.dialogVisible = true;
    },
    handleTodoComplete(todo) {
      todo.status = "completed";
      this.$message.success("已标记为完成");
    },
  },
};
</script>

<style lang="scss" scoped>
.right-panel {
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: flex-end;
  height: $base-nav-bar-height;

  .msg-badge {
    :deep(.el-badge__content.is-fixed) {
      right: calc(10px + var(--el-badge-size) / 2);
    }
  }

  .refresh,
  .theme {
    padding: $base-padding-20-10;
  }
}

.message-box {
  padding: $base-padding-5-15;

  .todo-header {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;

    .title {
      font-size: 16px;
      font-weight: bold;
    }

    .count {
      color: #999;
      margin-left: 5px;
    }
  }

  .todo-list {
    .todo-item {
      padding: 10px 0;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .todo-content {
        display: flex;
        align-items: center;
        margin-bottom: 5px;

        .todo-title {
          margin-left: 10px;
          font-size: 14px;
          color: #333;
        }
      }

      .todo-time {
        font-size: 12px;
        color: #999;
      }
    }
  }

  .todo-footer {
    text-align: center;
    padding: 10px 0;
    border-top: 1px solid #eee;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

/* .message-box {
  padding: $base-padding-5-15;

  :deep(.el-tabs__active-bar) {
    width: $base-tab-width_active !important;
  }
} */
</style>
