<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-09 18:12:14
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="应交增值税" name="应交增值税">应交增值税</el-tab-pane>
      <el-tab-pane label="未交增值税" name="未交增值税">未交增值税</el-tab-pane>
      <el-tab-pane label="待认证进项税额" name="待认证进项税额"
        >待认证进项税额</el-tab-pane
      >
      <el-tab-pane label="应交城市维护建设税" name="应交城市维护建设税"
        >应交城市维护建设税</el-tab-pane
      >
      <el-tab-pane label="应交教育费附加" name="应交教育费附加"
        >应交教育费附加</el-tab-pane
      >
      <el-tab-pane label="应交地方教育费附加" name="应交地方教育费附加"
        >应交地方教育费附加</el-tab-pane
      >
      <el-tab-pane label="应交印花税" name="应交印花税">应交印花税</el-tab-pane>
      <el-tab-pane label="应交个人所得税" name="应交个人所得税"
        >应交个人所得税</el-tab-pane
      >
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: "TaxPayable",
  data() {
    return {
      activeName: "应交增值税",
    };
  },
};
</script>

<style scoped lang="scss"></style>
