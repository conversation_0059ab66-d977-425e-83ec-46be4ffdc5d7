<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-16 09:53:46
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-17 20:53:15
 * @Description: 时效统计
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-rule
      :columns="columns"
      :data="tableData"
      @loadData="loadData">
      <template #rule>
        <div class="rules-tips">
          <h3 class="rule-title">工程师时效统计规则（按工单下单日期）</h3>

          <div class="rule-item">
            <span class="rule-number">统计周期：</span>
            <span class="rule-text">
              以工单下单日期为准，例如统计 1 月数据，即 1月1日 00:00:00 至
              1月31日 23:59:59
            </span>
          </div>

          <ol>
            <li>
              <div class="rule-item">
                <span class="rule-number">工单状态定义（共9种状态）：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  <span class="warning">待接单</span>
                  、
                  <span class="warning">工程师接单</span>
                  、
                  <span class="warning">工程师出发</span>
                  、
                  <span class="warning">工程师到达</span>
                  、
                  <span class="warning">待确认维修报告</span>
                  、
                  <span class="warning">已完成</span>
                  、
                  <span class="warning">待结算</span>
                  、
                  <span class="warning">待审核</span>
                  、
                  <span class="warning">关闭</span>
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">时间节点定义：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">发起报修：客户提交工单的时间</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  工程师接单：工程师点击"接单"按钮的时间
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  工程师出发：工程师点击"出发"按钮的时间
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  到店维修：工程师点击"到达"按钮客户现场的时间
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  提交维修报告：工程师上传维修报告的时间
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">报告确认：客户确认维修报告的时间</span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">统计指标（单位：分钟）：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  工单总数：所有工单数量减去
                  <span class="warning">关闭</span>
                  状态的工单数量
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  接单总次数：
                  <span class="warning">已完成</span>
                  状态的工单数量
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  接单总时长：所有
                  <span class="warning">已完成</span>
                  工单中，单个工单的（工程师接单时间 - 发起报修时间）累计值
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  平均接单时长：接单总时长 ÷ 接单总次数
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  领料总时长：所有
                  <span class="warning">已完成</span>
                  工单中，单个工单的（工程师出发时间 - 工程师接单时间）累计值
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  平均领料时长：领料总时长 ÷ 接单总次数
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  路途总耗时：所有
                  <span class="warning">已完成</span>
                  工单中，单个工单的（到店维修时间 - 工程师出发时间）累计值
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  平均路途耗时：路途总耗时 ÷ 接单总次数
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  维修总时长：所有
                  <span class="warning">已完成</span>
                  工单中，单个工单的（提交维修报告时间 - 到店维修时间）累计值
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  平均维修时长：维修总时长 ÷ 接单总次数
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  平均工单确认时长：所有
                  <span class="warning">已完成</span>
                  工单中，单个工单的（报告确认时间 - 提交维修报告时间）累计值 ÷
                  接单总次数
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  平均评价分：所有
                  <span class="warning">已完成</span>
                  工单中，每单的（专业能力评分 + 服务态度评分）之和 ÷ 接单总次数
                </span>
              </div>
            </li>
          </ol>
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 详情 -->
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="75%"
      :no-footer="true"
      @cancel="drawerVisible = false">
      <ProTable
        ref="detailTable"
        :query-param="detailQueryParam"
        :local-pagination="detailLocalPagination"
        :columns="detailColumns"
        :data="detailTableData"
        :show-setting="false"
        :use-infinite-scroll="true"
        :has-more="hasMore"
        @loadData="handleLoadData">
        <template #btn>
          <div class="title-box-right" style="font-size: 14px; gap: 10px">
            <div>总维修次数：{{ detailLocalPagination.total || 0 }}</div>
          </div>
        </template>
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button
              icon="el-icon-warning-outline"
              @click="handleDetail(row)">
              工单详情
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDrawer>
    <!-- 工程师接单明细 -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="60%"
      top="6%"
      :no-footer="true"
      @cancel="dialogVisible = false">
      <ProForm
        ref="ProFrom"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :form-param="formParam"
        :form-list="formColumns"
        :open-type="'info'"
        :confirm-loading="confirmLoading"></ProForm>
    </ProDialog>
    <!-- 维修工单详情 -->
    <WorkOrderDetail ref="workOrder" />
  </div>
</template>

<script>
import WorkOrderDetail from "@/views/financing/components/workOrderDetail.vue";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { engineerTimeStatApi, engineerTimeStatDetailApi } from "@/api/repair";

export default {
  name: "Aging",
  components: {
    WorkOrderDetail,
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "engineerName",
          title: "工程师名称",
          isTable: true,
          width: 90,
        },
        {
          dataIndex: "name",
          title: "工程师名称",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "monthly",
          title: "年月",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          valueFormat: "yyyy-MM",
          pickerFormat: "yyyy-MM",
          width: 80,
        },
        {
          dataIndex: "orderNums",
          title: "工单总数",
          isTable: true,
          minWidth: 65,
        },
        {
          dataIndex: "receiveNum",
          title: "接单总次数",
          isTable: true,
          minWidth: 75,
        },
        {
          dataIndex: "receiveTime",
          title: "接单总时长",
          isTable: true,
          minWidth: 75,
        },
        {
          dataIndex: "receiveTimeAvg",
          title: "平均接单时长",
          isTable: true,
          minWidth: 88,
        },
        {
          dataIndex: "prepareTime",
          title: "准备总时长",
          isTable: true,
          minWidth: 75,
        },
        {
          dataIndex: "prepareTimeAvg",
          title: "平均准备时长",
          isTable: true,
          minWidth: 88,
        },
        {
          dataIndex: "onRoadTime",
          title: "路途总耗时",
          isTable: true,
          minWidth: 75,
        },
        {
          dataIndex: "onRoadTimeAvg",
          title: "平均路途耗时",
          isTable: true,
          minWidth: 88,
        },
        {
          dataIndex: "repairTime",
          title: "维修总时长",
          isTable: true,
          minWidth: 75,
        },
        {
          dataIndex: "repairTimeAvg",
          title: "平均维修时长",
          isTable: true,
          minWidth: 88,
        },
        {
          dataIndex: "serviceEvaluateAvg",
          title: "平均服务评价分",
          isTable: true,
          minWidth: 95,
        },
        {
          dataIndex: "professionalEvaluateAvg",
          title: "平均技术评价分",
          isTable: true,
          minWidth: 95,
        },
        {
          dataIndex: "confirmTimeAvg",
          title: "平均工单确认时长",
          isTable: true,
          minWidth: 105,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 75,
        },
      ],
      tableData: [],
      editType: "info",
      drawerVisible: false,
      drawerTitle: "",
      // 明细数据
      detailQueryParam: {
        monthly: null,
        engineerId: null,
      },
      detailLocalPagination: {
        pageNumber: 1,
        pageSize: 15,
        total: 0,
      },
      detailColumns: [
        {
          dataIndex: "code",
          title: "工单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
          searchSpan: 6,
        },
        // {
        //   dataIndex: "code",
        //   title: "客户编号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          dataIndex: "name",
          title: "客户名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
          searchSpan: 6,
        },
        {
          dataIndex: "productInfo",
          title: "品牌型号",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "receiveTime",
          title: "接单时长",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          searchSpan: 6,
        },
        {
          dataIndex: "prepareTime",
          title: "准备时长",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          searchSpan: 6,
        },
        {
          dataIndex: "onRoadTime",
          title: "路途耗时",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          searchSpan: 6,
        },
        {
          dataIndex: "repairTime",
          title: "维修耗时",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          searchSpan: 6,
        },
        {
          dataIndex: "confirmTime",
          title: "工单确认耗时",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          searchSpan: 6,
        },
        {
          dataIndex: "professionalEvaluate",
          title: "技术评价分",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          searchSpan: 6,
        },
        {
          dataIndex: "serviceEvaluate",
          title: "服务评价分",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
          // searchSpan: 6,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 100,
        },
      ],
      detailTableData: [],
      hasMore: true,
      totalData: {},
      // 明细
      dialogVisible: false,
      dialogTitle: "张三接单明细",
      formParam: {},
      formColumns: [
        {
          dataIndex: "code",
          title: "工程师账号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "name",
          title: "工程师名称",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "orderNum",
          title: "接单次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        // {
        //   dataIndex: "overTimeOrderNum",
        //   title: "超时接单次数",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        //   searchSpan: 6,
        // },
        {
          dataIndex: "departNum",
          title: "出发次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "arriveNum",
          title: "到达次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "overTime",
          title: "超时到达次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "reportRepair",
          title: "提交报告次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "confirmReport",
          title: "客户确认次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "waitOrder",
          title: "未确认工单数量",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "avgOrderTime",
          title: "平均接单时长",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "avgHalfwayTime",
          title: "平均路上时长",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "avgRepairTime",
          title: "平均维修时长",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "avgReportTime",
          title: "平均报告确认时长",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "totalAmount",
          title: "抄表收入",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "blackWhite",
          title: "抄表黑白印量",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "colorPrint",
          title: "抄表彩色印量",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "num",
          title: "总维修次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "otherNum",
          title: "散客维修次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "otherAmount",
          title: "散客维修收入",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
      ],
      confirmLoading: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const inputRange = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.monthly,
        },
      ];
      filterParamRange(this, this.queryParam, inputRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.monthly;
      engineerTimeStatApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.detailQueryParam = {};
      this.editType = type;
      this.drawerTitle = `${row.engineerName} - ${row.monthly}时效统计详情`;
      this.detailQueryParam = {
        ...this.detailQueryParam,
        engineerId: row.engineerId,
        monthly: row.monthly,
      };
      this.detailLocalPagination = {
        pageNumber: 1,
        pageSize: 15,
        total: 0,
      };
      this.hasMore = true;
      this.drawerVisible = true;
      this.$nextTick(() => {
        this.$refs.detailTable.refresh();
      });
    },
    // 加载明细数据
    handleLoadData(parameter) {
      this.detailQueryParam = filterParam(
        Object.assign({}, this.detailQueryParam, parameter)
      );
      const searchRange = [
        {
          startReceiveTime: null,
          endReceiveTime: null,
          data: parameter.receiveTime,
        },
        {
          startPrepareTime: null,
          endPrepareTime: null,
          data: parameter.prepareTime,
        },
        {
          startOnRoadTime: null,
          endOnRoadTime: null,
          data: parameter.onRoadTime,
        },
        {
          startRepairTime: null,
          endRepairTime: null,
          data: parameter.repairTime,
        },
        {
          startConfirmTime: null,
          endConfirmTime: null,
          data: parameter.confirmTime,
        },
        {
          startEvaluate: null,
          endEvaluate: null,
          data: parameter.professionalEvaluate,
        },
        // {
        //   startMonth: null,
        //   endMonth: null,
        //   data: parameter.serviceEvaluate,
        // },
      ];
      filterParamRange(this, this.detailQueryParam, searchRange);
      const requestParameters = cloneDeep(this.detailQueryParam);
      [
        "receiveTime",
        "prepareTime",
        "onRoadTime",
        "repairTime",
        "confirmTime",
        "professionalEvaluate",
      ].forEach((key) => delete requestParameters[key]);
      engineerTimeStatDetailApi(requestParameters)
        .then((res) => {
          if (parameter.pageNumber === 1) {
            this.detailTableData = res.data.rows;
            this.detailLocalPagination.pageNumber = 1;
          } else {
            this.detailTableData = [...this.detailTableData, ...res.data.rows];
            this.detailLocalPagination.pageNumber = parameter.pageNumber;
          }
          this.detailLocalPagination.total = +res.data.total;
          // 判断是否还有更多数据
          this.hasMore =
            this.detailTableData.length < this.detailLocalPagination.total;
          this.$refs.detailTable && this.$refs.detailTable.resetScrolling();
        })
        .finally(() => {
          this.$refs.detailTable &&
            (this.$refs.detailTable.listLoading = false);
        });
    },
    handleDetail(row) {
      // this.dialogTitle = `${row.name} - 月度统计明细`;
      // this.dialogVisible = true;
      this.$refs.workOrder.show(row.code);
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
