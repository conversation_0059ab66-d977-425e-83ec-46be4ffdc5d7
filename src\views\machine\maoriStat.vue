<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-16 15:11:44
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-17 21:33:42
 * @Description: 机器 - 毛利统计
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :show-rule="true"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #rule>
        <div class="rules-tips">
          <h3 class="rule-title">机器毛利统计规则</h3>

          <div class="rule-item">
            <span class="rule-number">统计周期：</span>
            <span class="rule-text">
              例如统计 1 月数据，即 1月1日 00:00:00 至 1月31日 23:59:59
            </span>
          </div>

          <ol>
            <li>
              <div class="rule-item">
                <span class="rule-number">抄表收入统计：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  抄表应收：应付金额（数据来源：抄表对账-对账单）
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  抄表实收：实付金额（数据来源：抄表对账-对账单）
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">成本统计：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">零件成本：进价 × 数量（每个零件）</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">碳粉成本：进价 × 数量（每支碳粉）</span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">毛利统计：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  应收毛利：应付金额 - 零件成本 - 碳粉成本
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  实收毛利：实付金额 - 零件成本 - 碳粉成本
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">实收差额：应收毛利 - 实收毛利</span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">印量与维修统计：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  黑白印量：黑白印量（数据来源：抄表对账-抄表记录）
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  彩色印量：彩色印量（数据来源：抄表对账-抄表记录）
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  维修次数：状态为
                  <span class="warning">待确认维修报告</span>
                  、
                  <span class="warning">已完成</span>
                  、
                  <span class="warning">待结算</span>
                  、
                  <span class="warning">待审核</span>
                  的工单数量
                </span>
              </div>
            </li>
          </ol>
        </div>
      </template>
      <template #btn>
        <el-button type="success" icon="el-icon-download" size="mini">
          导出数据
        </el-button>
        <div
          v-if="statLoading"
          class="title-box-right"
          style="font-size: 15px; gap: 10px"
        >
          <div>抄表应收：{{ totalData?.readingReceivable || 0 }}</div>
          <div>抄表实收：{{ totalData?.actualReceipt || 0 }}</div>
          <div>零件成本：{{ totalData?.costPart || 0 }}</div>
          <div>碳粉成本：{{ totalData?.costConsumables || 0 }}</div>
          <div>应收毛利：{{ totalData?.readingProfit || 0 }}</div>
          <div>实收毛利：{{ totalData?.actualProfit || 0 }}</div>
          <div>实收差额：{{ totalData?.differenceProfit || 0 }}</div>
          <div>黑白印量：{{ totalData?.blackNums || 0 }}</div>
          <div>彩色印量：{{ totalData?.colorNums || 0 }}</div>
          <div>维修次数：{{ totalData?.repairTimes || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 详情 -->
    <ProDrawer
      :value="dialogVisible"
      :title="'月度毛利明细'"
      size="75%"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <ProForm
        ref="ProFrom"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :form-param="formParam"
        :form-list="formColumns"
        :open-type="editType"
        :confirm-loading="confirmLoading"
      >
        <template #deviceGroup>
          {{ formParam?.deviceGroup?.label }}
        </template>
        <template #line>
          <div class="title-box" style="margin-top: 0">成本收益</div>
        </template>
        <template #detail>
          <div class="title-box" style="margin-top: 0">成本收益明细</div>
          <ProTable
            ref="DetailTable"
            :show-loading="false"
            :show-search="false"
            :show-setting="false"
            height="52vh"
            :columns="detailColumns"
            :data="detailTableData"
          ></ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!--<el-tabs v-model="activeName">-->
    <!--  <el-tab-pane label="月度毛利" name="first" lazy>-->
    <!--    <MonthProfit />-->
    <!--  </el-tab-pane>-->
    <!--  <el-tab-pane label="毛利汇总" name="second" lazy>-->
    <!--    <ProfitSum />-->
    <!--  </el-tab-pane>-->
    <!--</el-tabs>-->
  </div>
</template>

<script>
// import MonthProfit from "@/views/machine/components/maori/monthProfit.vue";
// import ProfitSum from "@/views/machine/components/maori/profitSum.vue";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { getMachineProfitApi, getMachineProfitSummaryApi } from "@/api/machine";

export default {
  name: "MaoriStat",
  // components: { MonthProfit, ProfitSum },
  data() {
    return {
      activeName: "first",
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "deviceGroup",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "productInfo",
          title: "品牌/型号",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "productIds",
          title: "品牌/型号",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "name",
          title: "客户名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "seqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "monthly",
          title: "年月",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          valueFormat: "yyyy-MM",
          pickerFormat: "yyyy-MM",

          minWidth: 80,
        },
        {
          dataIndex: "readingReceivable",
          title: "抄表应收",
          isTable: true,
        },
        {
          dataIndex: "actualReceipt",
          title: "抄表实收",
          isTable: true,
        },
        // {
        //   dataIndex: "toner",
        //   title: "碳粉收入",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        // },
        {
          dataIndex: "costPart",
          title: "零件成本",
          isTable: true,
        },
        {
          dataIndex: "costConsumables",
          title: "碳粉成本",
          isTable: true,
        },
        {
          dataIndex: "readingProfit",
          title: "应收毛利",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "actualProfit",
          title: "实收毛利",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "differenceProfit",
          title: "实收差额",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "blackNums",
          title: "黑白印量",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "colorNums",
          title: "彩色印量",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "repairTimes",
          title: "维修次数",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        // {
        //   dataIndex: "customerCode",
        //   title: "客户编号",
        //   isSearch: true,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "deviceSeqId",
        //   title: "设备组编号",
        //   isSearch: true,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "deviceGroup",
        //   title: "设备组名称",
        //   isSearch: true,
        //   valueType: "input",
        //   placeholder: "1号机： 1",
        // },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   tableSlot: "action",
        //   width: 100,
        // },
      ],
      tableData: [],
      dialogVisible: false,
      editType: "info",
      confirmLoading: false,
      formParam: {},
      formColumns: [
        {
          dataIndex: "customerName",
          title: "客户名称",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "customerCode",
          title: "客户编号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "deviceSeqId",
          title: "设备组编号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isForm: true,
          formSlot: "deviceGroup",
          // valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "month",
          title: "年月",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "blackWhite",
          title: "黑白印量",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "colorPrint",
          title: "彩色印量",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "totalPrint",
          title: "总印量",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "repair",
          title: "维修次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        // {
        //   dataIndex: "profit",
        //   title: "毛利",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 6,
        // },
        {
          dataIndex: "line",
          title: "分割",
          isForm: true,
          formOtherSlot: "line",
          formSpan: 24,
        },
        {
          dataIndex: "totalAmount",
          title: "抄表应收",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "price",
          title: "抄表实收",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "partCost",
          title: "零件成本",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "carbonCost",
          title: "碳粉成本",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "expendAmount",
          title: "应收毛利",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "part",
          title: "实收毛利",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        // {
        //   dataIndex: "carbon1",
        //   title: "碳粉成本",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 6,
        // },
        // {
        //   dataIndex: "repair",
        //   title: "维修次数",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 6,
        // },
        // {
        //   dataIndex: "detail",
        //   title: "明细",
        //   isForm: true,
        //   formOtherSlot: "detail",
        //   formSpan: 24,
        // },
      ],
      detailColumns: [
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          formatter: (row) => row.deviceGroup?.label,
        },
        {
          dataIndex: "productInfo",
          title: "品牌机型",
          isTable: true,
        },
        {
          dataIndex: "blackWhite",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorPrint",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "totalPrint",
          title: "总印量",
          isTable: true,
        },
        {
          dataIndex: "repair",
          title: "维修次数",
          isTable: true,
        },
        {
          dataIndex: "meter",
          title: "抄表收入",
          isTable: true,
        },
        {
          dataIndex: "carbon",
          title: "碳粉收入",
          isTable: true,
        },
        {
          dataIndex: "carbon1",
          title: "碳粉成本",
          isTable: true,
        },
        {
          dataIndex: "part",
          title: "零件成本",
          isTable: true,
        },
        {
          dataIndex: "profit",
          title: "毛利",
          isTable: true,
        },
      ],
      detailTableData: [],
      totalData: {},
      statLoading: true,
      requestParameters: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.monthly,
        },
        {
          startReadingProfit: null,
          endReadingProfit: null,
          data: parameter.readingProfit,
        },
        {
          startActualProfit: null,
          endActualProfit: null,
          data: parameter.actualProfit,
        },
        {
          startDiffProfit: null,
          endDiffProfit: null,
          data: parameter.differenceProfit,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      this.requestParameters = cloneDeep(this.queryParam);
      ["monthly", "readingProfit", "actualProfit", "differenceProfit"].forEach(
        (key) => delete this.requestParameters[key]
      );
      getMachineProfitApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      getMachineProfitSummaryApi(this.requestParameters)
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    handleEdit(row, type) {
      this.editType = type;
      this.dialogVisible = true;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
