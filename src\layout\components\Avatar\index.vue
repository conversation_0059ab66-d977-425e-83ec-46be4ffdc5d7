<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-31 15:05:08
 * @Description: 
 -->
<template>
  <div class="container">
    <el-dropdown @command="handleCommand">
      <span class="avatar-dropdown" :style="{ color }">
        <!-- <img class="user-avatar" :src="avatar" alt="" /> -->
        <div class="user-name">
          {{ userName }}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </div>
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="password">修改密码</el-dropdown-item>
          <el-dropdown-item command="logout" divided>退出登陆</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <ReloadPassword ref="reloadPassword" />
  </div>
</template>

<script>
import ReloadPassword from "@/layout/components/Avatar/reloadPassword.vue";

export default {
  name: "Avatar",
  components: { ReloadPassword },
  mixins: [],
  props: {
    color: {
      type: String,
      default: "#666",
    },
  },
  data() {
    return {
      notice: true,
      userName: JSON.parse(localStorage.getItem("userInfo"))?.name || "",
      // dialog
    };
  },
  methods: {
    handleCommand(command) {
      switch (command) {
        case "logout":
          this.handleLogout();
          break;
        case "password":
          this.handleEditPassword();
          break;
        default:
          break;
      }
    },

    // 修改密码
    handleEditPassword() {
      this.$nextTick(() => {
        this.$refs.reloadPassword.show();
      });
      // this.$nextTick(() => {
      //   this.$refs["ProForm"].resetFormParam();
      // });
    },
    // 退出登陆
    handleLogout() {
      this.$confirm(`您确定要退出？`, "操作提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        dangerouslyUseHTMLString: true,
        type: "warning",
      }).then(async () => {
        await this.$store.dispatch("user/logout");
        this.$router.push(`/login?redirect=${this.$route.fullPath}`);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.avatar-dropdown {
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: center;
  justify-items: center;
  height: $base-avatar-dropdown-height;
  padding: $base-padding-10;

  .user-avatar {
    width: $base-avatar-widht;
    height: $base-avatar-height;
    cursor: pointer;
    border-radius: $base-border-radius-circle;
  }

  .user-name {
    position: relative;
    margin-left: $base-margin-5;
    margin-left: $base-margin-5;
    cursor: pointer;
  }
}
</style>
