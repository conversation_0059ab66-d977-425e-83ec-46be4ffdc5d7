<template>
  <div class="app-container">
    <!-- 选择机器/选配件 -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="70%"
      :top="'2%'"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProTable
        ref="ProTable"
        :row-key="(row) => row.machineNum"
        :query-param="queryParam"
        :local-pagination="localPagination"
        :columns="columns"
        :data="tableData"
        :show-selection="true"
        :height="400"
        @loadData="loadData"
        @handleSelectionChange="handleSelectionChange"
      >
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { getMachinePageApi } from "@/api/store";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "ChooseDispatchMachine",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    selectedData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 选择机器/选配件
      dialogTitle: "选择出库机器/选配件",
      queryParam: {},
      defaultQueryParam: {
        // status: ["ON_SALE"],
        // isSale: true,
      },
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 10,
      },
      columns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "originCode",
          title: "原机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          // multiple: true,
          optionMth: () => dictTreeByCodeApi(2000),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "productIds",
          title: "机器型号",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "tagName",
          title: "标签型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "placeOrigin",
          title: "产地版本",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "electric",
          title: "供电电压",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          minWidth: 80,
        },
        {
          dataIndex: "isSale",
          title: "是否上架",
          isTable: true,
          formatter: (row) => (row.isSale ? "已上架" : "未上架"),
          minWidth: 80,
        },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "colorCounter",
          title: "彩色计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "fiveColourCounter",
          title: "五色计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "source",
          title: "来源",
          isTable: true,
          formatter: (row) => row.source?.label,
          minWidth: 100,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          minWidth: 80,
        },
        {
          dataIndex: "createdAt",
          title: "入库时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
      ],
      tableData: [],
      selectionData: [],
      flag: false,
    };
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.ProTable.refresh();
          this.flag = true;
        });
      }
    },
  },
  mounted() {},
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      // requestParameters.status = ["ON_SALE"];
      // requestParameters.isSale = true;
      getMachinePageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          if (this.flag) {
            this.$refs.ProTable.$refs.ProElTable.clearSelection();
            if (this.selectedData.length) {
              this.selectedData.forEach((row) => {
                this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
                  row,
                  true
                );
              });
            }
          }
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
          this.flag = false;
        });
    },
    handleDialogOk() {
      // 将selectionData中的id值清除
      this.selectionData.forEach((item) => {
        Object.keys(item).forEach((key) => {
          item[key] = item[key].label ? item[key].value : item[key];
        });
        item.productInfo = item.productName || "";
        delete item.id;
      });
      this.$emit("confirmDispatch", this.selectionData);
    },
    handleDialogCancel() {
      this.$emit("update:dialogVisible", false);
    },
    handleSelectionChange(row) {
      this.selectionData = row;
    },
  },
};
</script>

<style scoped lang="scss"></style>
