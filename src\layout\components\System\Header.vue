<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-09-19 18:44:25
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-10-09 11:58:47
 * @FilePath: \benyin-web\src\layout\components\System\Header.vue
 * @Description: 
 * 
-->

<template>
  <!-- <div class="header1" style="color: #fff; text-align: left"></div> -->
  <div class="header1">
    <div class="center">
      <div>
        <div class="title">{{ proName }}</div>
      </div>
    </div>
    <div class="admin-box">
      <div class="user">
        <img src="../../../assets/images/lng/user.png" alt="" />
        <span>{{ name }}</span>
      </div>
      <img
        class="out"
        @click="logout"
        src="../../../assets/images/lng/quit-icon.png"
        alt=""
      />
    </div>
  </div>
</template>
<script>
import router from "@/router";
import { mapGetters } from "vuex";

import setting from "@/config/setting.js";
const { proName } = setting;
export default {
  name: "",
  components: {},
  props: {
    config: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      name: "",
      proName: proName,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.name = JSON.parse(localStorage.getItem("userInfo")).name;
  },
  mounted() {},
  beforeDestroy() {},
  destroyed() {},

  methods: {
    async logout() {
      await this.$store.dispatch("user/logout");
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    },
  },
};
</script>
<style lang="scss" scoped>
.header1 {
  background-image: url("@/assets/images/lng/header.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
  text-align: center;
  position: relative;

  .center {
    width: 550px;
    position: absolute;
    left: 52%;
    transform: translate(-50%, 0);
    display: flex;
    text-align: center;

    img {
      // height: 56px;
      width: 100%;
    }

    img.logo {
      width: 60px;
      display: inline-block;
    }

    .title {
      display: inline-block;
      font-size: 40px;
      font-weight: bold;
      color: #fff;
      font-size: 26px;

      font-family: Microsoft YaHei;
      letter-spacing: 5px;
      line-height: 50px;
      height: 35px;
      font-weight: bold;
      text-shadow: 0px 0px 10px rgba(123, 190, 191, 0.4);
      background: linear-gradient(180deg, #ffffff 16%, #99fce8 100%);
      -webkit-background-clip: text;
      -webkit-text-stroke: 1px #99fce8;
      -webkit-text-fill-color: transparent;
    }

    .tip {
      font-size: 5px;
      font-family: ShiShangZhongHeiJianTi;
      font-weight: 100;
      color: #aad8de;
    }
  }

  .admin-box {
    margin-left: auto;
    display: flex;
    align-items: center;
    width: 250px;
    padding-top: 10px;

    .user {
      display: flex;
      align-items: center;
      margin-right: 40px;

      span {
        font-size: 18px;
        font-weight: 800;
        color: #ffffff;
      }

      img {
        width: 30px;
        margin-right: 10px;
      }
    }
  }

  .out {
    width: 40px;
    margin-left: auto;
    margin-right: 20px;
    cursor: pointer;
  }
}
</style>
