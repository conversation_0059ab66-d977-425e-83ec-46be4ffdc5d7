<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-24 16:44:06
 * @Description: 崩溃统计图表组件
-->
<template>
  <div class="crash-charts">
    <el-row :gutter="20">
      <!-- 异常类型统计图 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>异常类型统计</span>
            <el-button type="text" size="small" @click="refreshCrashStats">
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div ref="exceptionChart" class="chart-container" v-loading="crashLoading"></div>
        </el-card>
      </el-col>

      <!-- 设备崩溃统计 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>设备崩溃统计</span>
            <el-button type="text" size="small" @click="refreshCrashStats">
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div ref="deviceCrashChart" class="chart-container" v-loading="crashLoading"></div>
        </el-card>
      </el-col>

      <!-- 应用版本崩溃统计 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>应用版本崩溃</span>
            <el-button type="text" size="small" @click="refreshCrashStats">
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div ref="appVersionChart" class="chart-container" v-loading="crashLoading"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { analysisApi } from '@/logcontrol/api/analysisApi'

export default {
  name: 'CrashCharts',
  data() {
    return {
      crashLoading: false,
      crashStats: null,
      exceptionChart: null,
      deviceCrashChart: null,
      appVersionChart: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.initCharts()
        this.loadCrashStats()
      }, 100)
    })
  },
  beforeDestroy() {
    this.destroyCharts()
  },
  methods: {
    // 初始化图表
    initCharts() {
      this.exceptionChart = echarts.init(this.$refs.exceptionChart)
      this.deviceCrashChart = echarts.init(this.$refs.deviceCrashChart)
      this.appVersionChart = echarts.init(this.$refs.appVersionChart)

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },

    // 销毁图表
    destroyCharts() {
      window.removeEventListener('resize', this.handleResize)
      
      if (this.exceptionChart) {
        this.exceptionChart.dispose()
      }
      if (this.deviceCrashChart) {
        this.deviceCrashChart.dispose()
      }
      if (this.appVersionChart) {
        this.appVersionChart.dispose()
      }
    },

    // 处理窗口大小变化
    handleResize() {
      this.$nextTick(() => {
        if (this.exceptionChart) this.exceptionChart.resize()
        if (this.deviceCrashChart) this.deviceCrashChart.resize()
        if (this.appVersionChart) this.appVersionChart.resize()
      })
    },

    // 加载崩溃统计
    async loadCrashStats() {
      this.crashLoading = true
      try {
        const response = await analysisApi.getCrashStats()
        this.crashStats = response.data
        this.updateExceptionChart()
        this.updateDeviceCrashChart()
        this.updateAppVersionChart()
      } catch (error) {
        console.error('加载崩溃统计失败:', error)
      } finally {
        this.crashLoading = false
      }
    },

    // 更新异常类型图表（水平柱状图）
    updateExceptionChart() {
      if (!this.exceptionChart || !this.crashStats?.exceptionTypeStats) return

      // 先处理所有数据并排序
      const allExceptionData = this.crashStats.exceptionTypeStats.map(item => ({
        name: this.getExceptionTypeLabel(item.exception_type),
        fullType: item.exception_type,
        count: parseInt(item.count) || 0
      }))

      // 按数量降序排列
      allExceptionData.sort((a, b) => b.count - a.count)

      // 计算总数
      const totalCount = allExceptionData.reduce((sum, item) => sum + item.count, 0)

      // 显示前9个异常类型，第10个及以后合并为"其它"
      let displayData = []

      if (allExceptionData.length <= 9) {
        // 如果总数不超过9个，直接显示所有
        displayData = allExceptionData
      } else {
        // 前9个正常显示
        const top9 = allExceptionData.slice(0, 9)

        // 第10个及以后合并为"其它"
        const othersCount = allExceptionData.slice(9).reduce((sum, item) => sum + item.count, 0)

        displayData = [...top9, {
          name: '其它',
          fullType: '其它异常类型',
          count: othersCount
        }]
      }

      const yAxisData = displayData.map(item => item.name)
      const seriesData = displayData.map(item => ({
        value: item.count,
        fullType: item.fullType
      }))

      const option = {
        title: {
          text: `异常总数: ${totalCount}`,
          left: 'center',
          top: '1%',
          textStyle: { fontSize: 14, color: '#666' }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            const param = params[0]
            const percentage = ((param.value / totalCount) * 100).toFixed(1)
            return `${param.data.fullType}<br/>数量: ${param.value} (${percentage}%)`
          }
        },
        grid: {
          left: '5%',
          right: '10%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          name: '崩溃次数',
          nameLocation: 'middle',
          nameGap: 25
        },
        yAxis: {
          type: 'category',
          data: yAxisData,
          axisLabel: {
            fontSize: 11,
            interval: 0,
            formatter: function(value) {
              // 如果标签太长，进行适当截断
              return value.length > 12 ? value.substring(0, 12) + '...' : value
            }
          },
          name: '异常类型',
          nameLocation: 'end',
          nameGap: 10,
          nameTextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        series: [{
          name: '异常数量',
          type: 'bar',
          data: seriesData,
          itemStyle: {
            color: function(params) {
              // 根据数值大小设置渐变色
              const colors = [
                '#ff4757', '#ff6b6b', '#ffa726', '#ffca28',
                '#66bb6a', '#42a5f5', '#ab47bc', '#26c6da', '#78909c'
              ]
              return colors[params.dataIndex % colors.length]
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            position: 'right',
            formatter: '{c}',
            fontSize: 11,
            color: '#333'
          }
        }]
      }

      this.exceptionChart.setOption(option)
    },

    // 更新设备崩溃图表（柱状图）
    updateDeviceCrashChart() {
      if (!this.deviceCrashChart || !this.crashStats?.deviceCrashStats) return

      const deviceData = this.crashStats.deviceCrashStats.map(item => ({
        deviceId: item.device_id,
        shortId: item.device_id.slice(-8), // 显示设备ID的后8位
        count: parseInt(item.count) || 0
      }))

      // 按崩溃次数降序排列
      deviceData.sort((a, b) => b.count - a.count)

      const xAxisData = deviceData.map(item => item.shortId)
      const seriesData = deviceData.map(item => ({
        value: item.count,
        fullDeviceId: item.deviceId
      }))

      const option = {
        title: {
          text: `设备总数: ${deviceData.length}`,
          left: 'center',
          top: '1%',
          textStyle: { fontSize: 14, color: '#666' }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            const param = params[0]
            return `设备ID: ${param.data.fullDeviceId}<br/>崩溃次数: ${param.value}`
          }
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '10%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            rotate: 45,
            fontSize: 12
          },
          name: '设备ID',
          nameLocation: 'middle',
          nameGap: 35
        },
        yAxis: {
          type: 'value',
          name: '崩溃次数',
          nameLocation: 'middle',
          nameGap: 40
        },
        series: [{
          name: '崩溃次数',
          type: 'bar',
          data: seriesData,
          itemStyle: {
            color: function(params) {
              // 根据数值大小设置不同颜色
              const colors = ['#ff6b6b', '#ffa726', '#66bb6a', '#42a5f5']
              return colors[params.dataIndex % colors.length]
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            position: 'top',
            formatter: '{c}',
            fontSize: 12,
            color: '#333'
          }
        }]
      }

      this.deviceCrashChart.setOption(option)
    },

    // 更新应用版本崩溃图表
    updateAppVersionChart() {
      if (!this.appVersionChart || !this.crashStats?.appVersionCrashStats) return

      const data = this.crashStats.appVersionCrashStats.map(item => ({
        name: item.app_version,
        value: parseInt(item.count) || 0
      }))

      const option = {
        title: {
          text: `崩溃总数: ${data.reduce((sum, item) => sum + item.value, 0)}`,
          left: 'center',
          top: '1%',
          textStyle: { fontSize: 14, color: '#666' }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: '10%',
          textStyle: { fontSize: 12 }
        },
        series: [{
          name: '应用版本',
          type: 'pie',
          radius: ['30%', '60%'],
          center: ['50%', '45%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            formatter: function(params) {
              return `${params.name}\n${params.value} (${params.percent}%)`
            },
            fontSize: 11,
            color: '#333'
          },
          labelLine: {
            show: true
          }
        }]
      }

      this.appVersionChart.setOption(option)
    },

    // 获取异常类型标签
    getExceptionTypeLabel(exceptionType) {
      // 特殊异常类型的友好显示名称
      const friendlyNames = {
        'java.io.IOException': 'IO异常',
        'java.lang.RuntimeException': '运行时异常',
        'java.lang.IllegalStateException': '非法状态异常',
        'java.lang.NullPointerException': '空指针异常',
        'android.database.sqlite.SQLiteConstraintException': 'SQLite约束异常',
        'com.google.gson.JsonSyntaxException': 'JSON语法异常',
        'android.database.sqlite.SQLiteException': 'SQLite异常',
        'java.net.ConnectException': '连接异常',
        'retrofit2.HttpException': 'HTTP异常'
      }
      
      // 如果有友好名称，使用友好名称，否则使用类名
      if (friendlyNames[exceptionType]) {
        return friendlyNames[exceptionType]
      }
      
      // 提取类名（最后一个点后的部分）
      const parts = exceptionType.split('.')
      return parts[parts.length - 1] || exceptionType
    },

    // 刷新崩溃统计
    async refreshCrashStats() {
      await this.loadCrashStats()
    }
  }
}
</script>

<style scoped>
.crash-charts {
  width: 100%;
}

.chart-card {
  height: 400px;
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 320px;
  width: 100%;
}
</style>
