<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-04 09:39:41
 * @Description: 商品管理
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :local-pagination="localPagination"
      :data="tableData"
      :height="500"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增商品
        </el-button>
      </template>
      <template #type="slotProps">
        {{ slotProps.row.type.label }}
      </template>
      <template #picsUrl="{ row }">
        <el-image
          v-if="row.picsUrl && row.picsUrl.length > 0"
          style="max-width: 100px; max-height: 100px"
          :src="getPicsUrlImg(row)"
          :preview-src-list="[getPicsUrlImg(row)]"
        ></el-image>
      </template>
      <template #saleStatus="slotProps">
        <el-switch
          v-model="slotProps.row.saleStatus"
          @change="(val) => handleSaleStatusChange(val, slotProps.row)"
        ></el-switch>
      </template>
      <template #actions="slotProps">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleCheck(slotProps.row)"
          >
            查看
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>

          <el-button
            v-if="slotProps.row.status !== 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="95%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <div class="dialog-content-box">
        <el-steps
          v-if="methodType !== 'info'"
          align-center
          :active="active"
          finish-status="success"
          class="steps-box"
        >
          <el-step title="商品基本信息"></el-step>
          <el-step title="商品规格库存"></el-step>
          <el-step title="商品详细信息"></el-step>
          <el-step title="商品其他信息"></el-step>
        </el-steps>
        <div :style="{ 'padding-top': methodType !== 'info' ? '85px' : '0px' }">
          <el-tabs
            v-if="methodType === 'edit'"
            v-model="activeName"
            type="card"
            @tab-click="handleClick"
          >
            <el-tab-pane label="商品基本信息" name="0"></el-tab-pane>
            <el-tab-pane label="商品规格库存" name="1"></el-tab-pane>
            <el-tab-pane label="商品详细信息" name="2"></el-tab-pane>
            <el-tab-pane label="商品其他信息" name="3"></el-tab-pane>
          </el-tabs>
          <div v-show="active === 0 || methodType === 'info'" class="boxa box0">
            <div class="tit-box">商品基本信息</div>
            <div class="sp-content jbxx-box">
              <el-form
                ref="proform_child1"
                :model="form"
                :disabled="methodType === 'info'"
                label-width="140px"
                class="demo-ruleForm"
              >
                <el-row>
                  <el-col :span="24">
                    <el-form-item
                      label="商品名称:"
                      :rules="[
                        {
                          required: true,
                          trigger: 'blur',
                          message: '请输入商品名称',
                        },
                      ]"
                      prop="name"
                    >
                      <el-input
                        v-model="form.name"
                        style="width: 300px"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="24">
                  <el-form-item label="商品品牌:" prop="brandId">
                    <el-select v-model="form.brandId" :multiple="false">
                      <el-option
                        v-for="(item, index) in formcolumns.brandList"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col> -->
                  <el-col :span="24">
                    <el-form-item
                      label="商品分类:"
                      :rules="[
                        {
                          required: true,
                          trigger: 'change',
                          message: '请选择商品分类',
                        },
                      ]"
                      prop="categoryId"
                    >
                      <el-select
                        v-model="form.categoryId"
                        style="width: 300px"
                        @change="handleCategoryChange"
                      >
                        <el-option
                          v-for="item in formcolumns.categoryList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col>
                    <el-form-item label="搜索字段">
                      <el-input
                        v-model="form.esKeyWord"
                        type="textarea"
                        :resize="'none'"
                        :autosize="{ minRows: 5, maxRows: 5 }"
                        placeholder="请输入内容"
                      ></el-input>
                      <span style="color: red">*关键词用逗号分隔( , )</span>
                    </el-form-item>
                  </el-col>
                  <el-col v-show="form.categoryId" :span="24">
                    <el-form-item label="分类属性:" prop="saleAttrVals">
                      <div
                        v-for="(item, index) in formcolumns.saleAttrValsList"
                        :key="index"
                      >
                        {{ item.name }}:
                        <el-radio-group
                          v-model="item.selectVal"
                          size="small"
                          style="margin-left: 20px"
                        >
                          <el-radio
                            v-for="(it, index) in item.children"
                            :key="index"
                            style="min-width: 120px; margin: 0 10px 10px 0"
                            :label="it.value"
                            border
                            >{{ it.label }}</el-radio
                          >
                        </el-radio-group>
                      </div>

                      <!-- <el-tree class="filter-tree" :data="formcolumns.saleAttrValsList" :props="{
                      children: 'children',
                      label: 'label',
                    }" default-expand-all ref="tree">
                      <span slot-scope="{ node, data }">
                       
                        <el-radio-group v-if="!data.isParent" v-model="node.parent.data.selectVal">
                          <el-radio :label="data.value" @click.native.prevent="
                            radioClick(data.value, node.parent.data.id)
                            ">
                            {{ data.label }}
                          </el-radio>
                        </el-radio-group>
                        <span v-else>{{ data.label }}</span>
                      </span>
                    </el-tree> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item
                      label="商品图:"
                      :rules="[
                        {
                          required: true,
                          trigger: 'change',
                          message: '请选择商品图',
                        },
                      ]"
                      prop="picsUrl"
                    >
                      <ProUpload
                        :file-list="form.picsUrl"
                        :type="methodType"
                        :limit="5"
                        :multiple="true"
                        draggable-sort
                        @uploadSuccess="handleUploadSuccess"
                        @uploadRemove="handleUploadRemove"
                      />
                      建议尺寸：800*800，默认首张图为主图，最多上传5张。
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="24">
                  <el-form-item label="商品状态:" :rules="[
                    {
                      required: true,
                      trigger: 'change',
                      message: '请选择商品状态',
                    },
                  ]" prop="saleStatus">
                    <el-radio-group v-model="form.saleStatus">
                      <el-radio v-for="(item, index) in formcolumns.saleStatusList" :label="item.value">{{ item.label
                      }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col> -->
                </el-row>
              </el-form>
            </div>
          </div>
          <div v-show="active === 1 || methodType === 'info'" class="boxa box1">
            <div class="tit-box">商品规格库存</div>
            <!-- <el-form
              ref="proform_child1"
              :model="form"
              :disabled="methodType == 'info'"
              label-width="140px"
              class="demo-ruleForm"
            >
              <el-row>
                <el-col :span="24">
                  <el-form-item
                    label="商品名称:"
                    :rules="[
                      {
                        required: true,
                        trigger: 'blur',
                        message: '请输入商品名称',
                      },
                    ]"
                    prop="name"
                  >
                    <el-input
                      v-model="form.name"
                      style="width: 300px"
                    ></el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="24">
                  <el-form-item
                    label="商品图:"
                    :rules="[
                      {
                        required: true,
                        trigger: 'change',
                        message: '请选择商品图',
                      },
                    ]"
                    prop="picsUrl"
                  >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form> -->
            <div class="sp-content jbxx-box">
              <productSpecifications
                ref="ProductSpecificationsRef"
                :item-id="itemId"
                :method-type="methodType"
                source-type="wares"
                @successProduct="handleSuccessProduct"
              ></productSpecifications>
            </div>
          </div>
          <div v-show="active === 2 || methodType === 'info'" class="boxa box2">
            <div class="tit-box">商品详细信息</div>
            <div class="sp-content editor-box">
              <div
                v-show="methodType === 'info'"
                v-html="proWangeEditorContent"
              ></div>
              <ProWangeEditor
                v-show="methodType !== 'info'"
                ref="ProWangeEditorRef"
              ></ProWangeEditor>
            </div>
          </div>
          <div v-show="active === 3 || methodType === 'info'" class="boxa box3">
            <div class="tit-box">推荐零件耗材</div>
            <div class="sp-content editor-box">
              <el-button
                type="primary"
                style="margin-bottom: 20px"
                @click="handleOpenChooseDialog"
                >选择商品</el-button
              >
              <el-table :data="recSkuList" border style="width: 100%">
                <el-table-column prop="itemName" label="商品名称">
                </el-table-column>
                <el-table-column prop="itemCode" label="商品编号">
                </el-table-column>
                <el-table-column prop="saleAttrVals" label="sku属性">
                  <template slot-scope="slotProps">
                    <span
                      v-for="(item, index) in slotProps.row.saleAttrVals"
                      :key="index"
                      style="border: 1px solid #ddd"
                      >{{ item.name }}: {{ item.val }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="picsUrl" label="商品主图">
                  <template slot-scope="slotProps">
                    <img
                      style="max-width: 100px; max-height: 100px"
                      :src="getPicsUrlImg(slotProps.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="oemNumber" label="OEM编号">
                </el-table-column>
                <el-table-column prop="articleCode" label="物品编号">
                </el-table-column>
                <el-table-column prop="categoryName" label="商品分类">
                </el-table-column>
                <el-table-column prop="saleStatus" label="商品状态">
                  <template slot-scope="slotProps">
                    {{ slotProps.row.saleStatus ? "已上架" : "未上架" }}
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="80">
                  <template slot-scope="scope">
                    <div class="fixed-width">
                      <el-button
                        type="danger"
                        size="small"
                        icon="el-icon-delete"
                        @click="handleDelOne(scope.row)"
                      >
                        移除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <div v-if="methodType !== 'info'" class="add-sp-box m-t-2">
        <el-button v-if="active > 0" type="primary" @click="back()">
          上一步
        </el-button>
        <el-button type="primary" @click="active === 3 ? save() : next()">
          {{ active === 3 ? "保存" : "下一步" }}
        </el-button>
      </div>
    </ProDrawer>
    <!-- 选择商品 -->
    <ProDialog
      :value="showPartDialog"
      title="选择商品"
      width="1500px"
      :confirm-loading="partDialogLoading"
      confirm-text="确认选择"
      top="0"
      @ok="handleChooseDialogConfirm"
      @cancel="showPartDialog = false"
    >
      <Goods
        ref="ChoosePartTable"
        :selected-data="choosePartSelection"
        @chooseOem="handleSelectionChange"
      ></Goods>
    </ProDialog>
  </div>
</template>
<script>
import {
  itemListApi,
  itemAddApi,
  itemUpdateDetailApi,
  itemUpdateSaleStatusApi,
  itemUpdateOtherInfoApi,
  itemDelApi,
  classifyListApi,
  classifyInfoApi,
  itemDetailByIdApi,
} from "@/api/goods";
import { brandListApi } from "@/api/brand";
import { isEmpty, cloneDeep } from "lodash";
import ProUpload from "@/components/ProUpload/index.vue";
import ProWangeEditor from "@/components/ProWangeEditor/index.vue";
import productSpecifications from "./productSpecifications.vue";
import { Message } from "element-ui";
import Goods from "./components/goods.vue";
import { filterParam, filterParamRange } from "@/utils";

export default {
  name: "Wares",
  components: { ProWangeEditor, productSpecifications, ProUpload, Goods },
  mixins: [],
  props: {},
  data() {
    return {
      itemId: "",
      active: 0,
      activeName: "0",
      // 列表
      spareiTypeList: [],
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      queryParam: {
        aduitState: null,
        name: null,
      },
      columns: [
        {
          dataIndex: "categoryId",
          title: "商品分类",
          isSearch: true,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          clearable: true,
          isSearch: true,
          formSpan: 8,
          valueType: "select",
          option: [
            { label: "上架", value: "ON_SALE" },
            { label: "下架", value: "NO_SALE" },
          ],
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          clearable: true,
          isSearch: true,
          formSpan: 16,
          valueType: "input",
        },

        {
          dataIndex: "code",
          title: "商品编号",
          isTable: true,
          isSearch: true,
          formSpan: 16,
          valueType: "input",
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 120,
        },
        {
          dataIndex: "name",
          title: "商品名称",
          isTable: true,
        },
        // {
        //   dataIndex: "商品类型",
        //   title: "商品类型",
        //   isTable: true,
        // },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
        },
        // {
        //   dataIndex: "oemNumber",
        //   title: "OEM编号",
        //   isSearch: true,
        // isTable: true,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "oemNumber",
        //   title: "物品编号",
        //   isTable: true,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "oemNumber",
        //   title: "售价",
        //   isTable: true,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "name",
        //   title: "上架人",
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          dataIndex: "launchTime",
          title: "上架时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isSearch: true,
          // isTable: true,
          valueType: "input",
        },
        // {
        //   dataIndex: "oemNumber",
        //   title: "物品编号",
        //   isTable: true,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "oemNumber",
        //   title: "售价",
        //   isTable: true,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "name",
        //   title: "上架人",
        //   isSearch: true,
        //   valueType: "input",
        // },

        {
          dataIndex: "soldOutNum",
          title: "已售卖数量",
          isTable: true,
        },
        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 240,
        },
      ],
      methodType: "add", //新增
      confirmLoading: false,
      dialogTitle: "",
      dialogVisible: false,
      cloneDeepPicsUrl: [],
      form: {
        name: null,
        brandId: null,
        esKeyWord: null,
        picsUrl: [],
        categoryId: null,
        saleAttrVals: [],
        saleStatus: "NO_SALE",
      },
      formcolumns: {
        brandList: [],
        categoryList: [],
        saleAttrValsList: [],
        saleStatusList: [
          { label: "上架", value: "ON_SALE" },
          { label: "下架", value: "NO_SALE" },
        ],
      },
      proWangeEditorContent: null,
      rowInfo: {},
      showPartDialog: false,
      choosePartSelection: [],

      dialogLoading: false,
      recSkuList: [],
      partDialogLoading: false,
    };
  },
  computed: {},
  watch: {
    active(val) {
      if (this.methodType == "edit") {
        if (val == 1) {
          this.$refs.ProductSpecificationsRef?.echo(this.rowInfo);
        } else if (val == 2) {
          this.$refs.ProWangeEditorRef?.echo(this.rowInfo.detailHtml);
        } else if (val == 3) {
          this.proWangeEditorContent = this.rowInfo.detailHtml;
        }
      }
      this.activeName = val.toString();
    },
  },
  created() {},
  mounted() {
    this.$refs.ProTable.refresh();
    this.init();
  },
  methods: {
    init() {
      brandListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.formcolumns.brandList = (res.data.rows || []).map((item) => ({
          label: item.brandName,
          value: item.id,
        }));
      });
      classifyListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.formcolumns.categoryList = this.columns[0].option = (
          res.data.rows || []
        ).map((item) => ({
          label: item.name,
          value: item.id,
        }));
      });
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    handleSaleStatusChange(val, row) {
      this.$refs.ProTable.listLoading = true;
      itemUpdateSaleStatusApi({
        itemId: row.id,
        saleStatusEnum: val ? "ON_SALE" : "NO_SALE",
      })
        .then((res) => {
          if (res.code === 200) {
            Message.success("操作成功");
            row.saleStatus = val;
            this.$set(row, "saleStatus", val);
          }
        })
        .catch(() => {
          this.$set(row, "saleStatus", !val);
        })
        .finally(() => {
          this.$refs.ProTable.listLoading = false;
        });
    },
    handleClick(tab, event) {
      console.log(tab);
      this.active = parseInt(this.activeName);
    },
    /**
     * 商品分类切换
     * @param {String} val 选中的商品分类
     * @param {Object} echoData 回显数据
     */
    handleCategoryChange(val, echoData) {
      if (val) {
        classifyInfoApi({
          id: val,
        }).then((res) => {
          this.formcolumns.saleAttrValsList = (res.data?.tagList || []).map(
            (item) => ({
              ...item,
              isParent: true,
              label: item.name,
              value: item.id,
              selectVal: echoData?.[item.id] || null,
              children: item.value.map((itemC) => ({
                isParent: false,
                label: itemC,
                value: itemC,
              })),
            })
          );
        });
      } else {
        this.formcolumns.saleAttrValsList = [];
      }
    },
    next() {
      if (this.active == 0) {
        this.firstStep();
      } else if (this.active == 1) {
        this.$refs.ProductSpecificationsRef.handleSubmit();
      } else if (this.active == 2) {
        localStorage.setItem(
          "detailHtml",
          this.$refs.ProWangeEditorRef?.getContent()
        );
        itemUpdateDetailApi({
          id: this.itemId,
          detailHtml: this.$refs.ProWangeEditorRef?.getContent() || "",
        }).finally((_) => {
          this.active++;
        });
      }
    },
    back() {
      this.active--;
    },
    /**
     * 第一步
     * @params {Boolean} isExit true为退出新增商品
     */
    firstStep(isExit) {
      this.$refs["proform_child1"].validate((valid) => {
        if (valid) {
          const saleAttrValsTemp = {};
          this.formcolumns.saleAttrValsList.map((item) => {
            if (item.selectVal) {
              saleAttrValsTemp[item.id] = item.selectVal;
            }
          });
          const args = {
            ...this.form,
            id: this.itemId || null,
            saleAttrVals: saleAttrValsTemp,
          };
          console.log(args);

          // if (this.methodType !== "add") {
          //   args["id"] = this.itemId;
          // }

          itemAddApi(args)
            .then((res) => {
              if (!this.itemId) {
                this.itemId = res.data;
              }
            })
            .finally((_) => {
              this.active++;
              this.$refs.ProTable.refresh();
              // if (this.active++ > 3) this.active = 0;
              // if (isExit) {
              //   this.dialogVisible = false;
              // }
            });
        } else {
          return false;
        }
      });
    },
    /**
     * 第二步 商品规格成功
     */
    handleSuccessProduct() {
      this.active++;
    },
    save() {
      if (this.active == 0) {
        this.firstStep(true);
      } else if (this.active == 3) {
        const recSkuList = [];
        this.recSkuList.map((ele) => {
          recSkuList.push(ele.saleSkuId);
        });

        itemUpdateOtherInfoApi({
          recSkuList: recSkuList,
          id: this.itemId,
        }).finally((_) => {
          this.$refs.ProTable.refresh();
          this.dialogVisible = false;
          this.active = 0;
          this.itemId = null;
          localStorage.removeItem("detailHtml");
        });
      }
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = filterParam(Object.assign({}, parameter));
      requestParameters.createdAtStartTime = requestParameters.launchTime
        ? requestParameters.launchTime[0]
        : null;
      requestParameters.createdAtEndTime = requestParameters.launchTime
        ? requestParameters.launchTime[1]
        : null;
      delete requestParameters.launchTime;
      itemListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows.map((item) => ({
            ...item,
            saleStatus: item.saleStatus === "ON_SALE",
          }));
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300); // 解决最后一页表格不能滚动问题
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.form);
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
    },
    //触发新增
    handleAdd() {
      this.recSkuList = [];
      this.dialogTitle = "新增商品";
      this.methodType = "add";
      this.active = 0;
      this.resetFrom();
      this.dialogVisible = true;
      this.form = this.$options.data().form;
      console.log(this.form);
      // 重置新增的id
      this.itemId = null;
      this.cloneDeepPicsUrl = [];
      localStorage.removeItem("detailHtml");
      this.$nextTick(() => {
        this.formcolumns.saleAttrValsList =
          this.formcolumns.saleAttrValsList.map((item) => ({
            ...item,
            selectVal: false,
          }));
        this.$refs["proform_child1"].clearValidate();

        console.log(this.form);
      });
    },
    //触发详情
    handleCheck(row) {
      this.dialogTitle = `查看 - ${row.name}`;
      this.methodType = "info";
      this.dialogVisible = true;
      this.form.picsUrl = [];
      this.recSkuList = [];
      this.getQueryDetail(row);
      localStorage.removeItem("detailHtml");
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = `编辑 - ${row.name}`;
      this.itemId = row.id;
      this.methodType = "edit";
      this.dialogVisible = true;
      this.recSkuList = [];
      this.active = 0;
      this.cloneDeepPicsUrl = [];
      this.form.picsUrl = [];
      this.getQueryDetail(row);
      localStorage.removeItem("detailHtml");
    },
    getQueryDetail(row) {
      itemDetailByIdApi(row.id).then((res) => {
        const data = res.data || {};
        this.rowInfo = data;
        this.cloneDeepPicsUrl = cloneDeep(data.picsUrl || []);
        this.form = {
          ...this.form,
          name: data.name,
          brandId: data.brandId,
          esKeyWord: data.esKeyWord,
          picsUrl: cloneDeep(data.picsUrl || []),
          categoryId: data.categoryId,
          saleAttrVals: data.saleAttrVals || [],
          saleStatus: data.saleStatus,
        };
        data.itemRecList?.map((ele) => {
          this.recSkuList.push({
            saleSkuId: ele.recSkuId,
            itemName: ele.recItem.name,
            itemCode: ele.recItem.code,
            saleAttrVals: ele.recSku.saleAttrVals,
            picsUrl: ele.recItem.picsUrl,
            oemNumber: ele.recSku.invSkuOem,
            articleCode: ele.categoryName,
            // categoryName:ele.categoryName,
            saleStatus: ele.recItem.saleStatus,
          });
        });

        this.handleCategoryChange(data.categoryId, data.saleAttrVals);
        this.$nextTick((_) => {
          if (this.methodType == "info") {
            this.$refs.ProductSpecificationsRef?.echo(data);
            this.$refs.ProWangeEditorRef?.echo(data.detailHtml);
            this.proWangeEditorContent = data.detailHtml;
          }
        });
      });
    },
    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          itemDelApi(data.id).then(() => {
            this.$message.success("删除成功");
            this.localPagination = {
              pageNumber: 1,
              pageSize: 10,
              total: 0,
            };
            this.$nextTick(() => {
              this.$refs.ProTable.refresh();
            });
          });
        })
        .catch((_) => {});
    },
    handleRemove(file) {
      console.log(file);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleDownload(file) {
      console.log(file);
    },
    // 图片处理
    handleUploadSuccess(result) {
      this.form.picsUrl.push(result);
    },
    handleUploadRemove(file) {
      const index = this.form.picsUrl.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      this.form.picsUrl.splice(index, 1);
    },
    /**
     * 可以去单选框的选中值
     */
    radioClick(ownVal, parentId) {
      const temp = this.formcolumns.saleAttrValsList.find(
        (item) => item.id == parentId
      );
      temp.selectVal == ownVal
        ? (temp.selectVal = "")
        : (temp.selectVal = ownVal);
    },
    // 打开模态框
    async handleOpenChooseDialog(params) {
      this.showPartDialog = true;
      // this.choosePartSelection = cloneDeep(this.recSkuList) || [];
      // this.$nextTick(() => {
      // this.$refs.ChoosePartTable.$refs.ProTable.refresh();
      // this.$refs.ChoosePartTable.$refs.ProTable.$refs.ProElTable.clearSelection();
      // if (this.choosePartSelection.length > 0) {
      //   this.choosePartSelection.map((row) => {
      //     this.$refs.ChoosePartTable.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
      //       row,
      //       true
      //     );
      //   });
      // }
      // });
    },
    // 勾选商品
    handleSelectionChange(val) {
      this.choosePartSelection = val;
    },
    // 点击模态框保存
    handleChooseDialogConfirm() {
      this.recSkuList = cloneDeep(this.choosePartSelection);
      // this.recSkuList.push(...this.choosePartSelection);
      this.showPartDialog = false;
      console.log(this.recSkuList);
    },
    handleDelOne(row) {
      this.recSkuList = this.recSkuList.filter(
        (item) => item.saleSkuId !== row.saleSkuId
      );
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-upload--picture-card,
::v-deep .el-upload-list__item {
  width: 120px;
  height: 120px;
}

.sp-content {
  padding: 10px;
  width: 90%;
  margin: auto;
}

.add-sp-box {
  z-index: 2;
  position: absolute;
  bottom: 0;
  text-align: center;
  width: 100%;
  background: #fff;
  padding: 10px 0;
  border-top: 1px solid #ebeaea;
}

.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-content-box {
  position: relative;
  height: 100%;
  overflow: scroll;
  padding-bottom: 80px;

  .steps-box {
    position: absolute;
    width: 80%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    z-index: 2;
  }

  .content-fix {
    height: calc(100vh - 110px);
    overflow: auto;
  }

  .tit-box {
    width: 90%;
    padding: 5px 10px;
    color: #409eff;
    position: relative;
    margin: 20px auto;
    font-size: 16px;
    font-weight: 800;

    &::before {
      content: "";
      width: 5px;
      height: 20px;
      background: #409eff;
      display: inline-block;
      position: absolute;
      left: -1px;
      top: 4px;
    }
  }
}

.boxa {
}
</style>
