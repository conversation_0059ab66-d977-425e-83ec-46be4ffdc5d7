# 日志配置管理接口使用说明

## 📋 **接口概览**

日志配置管理系统提供了完整的配置管理功能，支持配置的增删改查、模板管理、批量分配等功能。

**基础路径：** `/logcontrol/config`

---

## 🔍 **查询接口**

### 1. 获取激活的日志配置
```http
GET /logcontrol/config/get
```

**请求头：**
- `X-Device-Id` (可选): 设备ID
- `X-App-Version` (可选): 应用版本
- `X-User-Id` (可选): 用户ID

**优先级规则：** 用户配置 > 设备配置 > 默认配置

**响应示例：**
```json
{
  "id": 1,
  "configName": "default_config",
  "logLevel": "INFO",
  "enableLocationLog": true,
  "locationLogInterval": 30000,
  "logUploadInterval": 60000,
  "maxLogFiles": 10,
  "configVersion": "1.0.1",
  "isActive": true
}
```

### 2. 根据配置名称获取配置
```http
GET /logcontrol/config/get-by-name?configName={配置名称}
```

### 3. 根据配置版本获取配置
```http
GET /logcontrol/config/get-by-version?configVersion={配置版本}
```

### 4. 获取所有配置列表
```http
GET /logcontrol/config/list
```

**响应：** 返回所有配置的数组

---

## ✏️ **配置管理接口**

### 5. 更新日志配置
```http
POST /logcontrol/config/update
Content-Type: application/json
```

**请求体：**
```json
{
  "id": 1,
  "configName": "my_config",
  "logLevel": "DEBUG",
  "enableLocationLog": true,
  "locationLogInterval": 15000,
  "logUploadInterval": 30000,
  "maxLogFiles": 20,
  "configVersion": "1.0.2",
  "isActive": true
}
```

**字段说明：**
- `logLevel`: 日志级别 (DEBUG, INFO, WARN, ERROR)
- `enableLocationLog`: 是否启用位置日志
- `locationLogInterval`: 位置日志间隔(毫秒)
- `logUploadInterval`: 上传间隔(毫秒)
- `maxLogFiles`: 最大日志文件数量

### 6. 激活指定配置
```http
POST /logcontrol/config/activate/{id}
```

### 7. 删除配置
```http
DELETE /logcontrol/config/{id}
```

---

## 📝 **模板管理接口**

### 8. 获取配置模板列表
```http
GET /logcontrol/config/templates
```

**响应示例：**
```json
[
  {
    "templateName": "development",
    "displayName": "开发环境",
    "logLevel": "DEBUG",
    "enableLocationLog": true,
    "locationLogInterval": 10000,
    "logUploadInterval": 30000,
    "maxLogFiles": 50,
    "description": "开发环境配置模板"
  }
]
```

### 9. 从模板创建配置
```http
POST /logcontrol/config/create-from-template
Content-Type: application/json
```

**请求体：**
```json
{
  "templateName": "development",
  "configName": "dev_config_001",
  "customizations": {
    "logLevel": "INFO",
    "maxLogFiles": 30
  }
}
```

---

## 👥 **配置分配接口**

### 10. 为用户分配配置
```http
POST /logcontrol/config/assign-to-user?userId={用户ID}&configId={配置ID}
```

### 11. 为设备分配配置
```http
POST /logcontrol/config/assign-to-device?deviceId={设备ID}&configId={配置ID}
```

### 12. 批量分配配置
```http
POST /logcontrol/config/assign-batch
Content-Type: application/json
```

**请求体：**
```json
{
  "configSource": "123",
  "sourceType": "CONFIG_ID",
  "overrideExisting": false,
  "targets": [
    {
      "targetType": "USER",
      "targetId": "user001",
      "targetName": "张三"
    },
    {
      "targetType": "DEVICE", 
      "targetId": "device001",
      "targetName": "测试设备1"
    }
  ]
}
```

**字段说明：**
- `sourceType`: CONFIG_ID(配置ID) 或 TEMPLATE(模板)
- `targetType`: USER(用户), DEVICE(设备), GROUP(分组)

---

## 📊 **分配管理接口**

### 13. 获取配置分配情况
```http
GET /logcontrol/config/assignments?targetType={目标类型}&keyword={关键词}
```

**参数：**
- `targetType` (可选): USER, DEVICE, GROUP
- `keyword` (可选): 搜索关键词

### 14. 移除配置分配
```http
DELETE /logcontrol/config/assignment/{targetType}/{targetId}
```

---

## 🧪 **测试接口**

### 15. 测试接口
```http
GET /logcontrol/config/test
```

**响应：** `"LogControl模块正常工作"`

---

## 📱 **前端集成建议**

### 1. **配置管理页面**
```javascript
// 获取配置列表
const getConfigs = async () => {
  const response = await fetch('/logcontrol/config/list');
  return response.json();
};

// 更新配置
const updateConfig = async (config) => {
  const response = await fetch('/logcontrol/config/update', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(config)
  });
  return response.json();
};
```

### 2. **客户端配置获取**
```javascript
// 客户端获取配置（带设备信息）
const getClientConfig = async (deviceId, userId) => {
  const response = await fetch('/logcontrol/config/get', {
    headers: {
      'X-Device-Id': deviceId,
      'X-User-Id': userId
    }
  });
  return response.json();
};
```

### 3. **批量分配界面**
```javascript
// 批量分配配置
const batchAssign = async (configId, targets) => {
  const response = await fetch('/logcontrol/config/assign-batch', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      configSource: configId.toString(),
      sourceType: 'CONFIG_ID',
      targets: targets,
      overrideExisting: false
    })
  });
  return response.json();
};
```

## 🔧 **配置参数说明**

| 参数 | 类型 | 说明 | 示例值 |
|------|------|------|--------|
| `logLevel` | String | 日志级别 | DEBUG, INFO, WARN, ERROR |
| `enableLocationLog` | Boolean | 是否启用位置日志 | true/false |
| `locationLogInterval` | Integer | 位置日志间隔(毫秒) | 30000 (30秒) |
| `logUploadInterval` | Integer | 上传间隔(毫秒) | 60000 (1分钟) |
| `maxLogFiles` | Integer | 最大日志文件数 | 10 |

## ⚠️ **注意事项**

1. **配置优先级**：用户专属配置 > 设备专属配置 > 默认配置
2. **配置命名**：用户配置以 `user_` 开头，设备配置以 `device_` 开头
3. **版本管理**：每次更新配置都会生成新的版本号
4. **异步处理**：设备配置信息更新采用异步处理，不影响主流程
5. **错误处理**：所有接口都有完善的异常处理，失败时返回降级配置

这套接口提供了完整的日志配置管理功能，支持灵活的配置分发和管理策略。
