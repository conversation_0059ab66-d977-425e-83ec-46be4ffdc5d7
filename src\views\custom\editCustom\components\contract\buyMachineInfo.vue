<!--
 * @Author: wskg
 * @Date: 2025-01-15 11:59:00
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-26 15:28:27
 * @Description: 购机合同明细
 -->
<template>
  <div class="app-container">
    <ProDialog
      :value="dialogVisible"
      title="购机合约明细"
      width="75%"
      top="1%"
      :confirm-btn-loading="confirmLoading"
      :no-footer="editType === 'info'"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="ProForm"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :form-param="form"
        :form-list="formColumns"
        :open-type="editType"
        @proSubmit="formSubmit"
      >
        <template #hostType>
          <el-select
            v-model="form.hostType"
            placeholder="请选择主机类型"
            style="width: 100%"
            size="small"
            :disabled="true"
          >
            <el-option
              v-for="item in hostTypeListOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
        <template #productId>
          <el-cascader
            ref="ProductIds"
            v-model="form.productId"
            :options="options"
            style="width: 100%"
            :show-all-levels="false"
            :disabled="true"
            size="small"
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
              expandTrigger: 'click',
            }"
            leaf-only
          ></el-cascader>
        </template>
        <template #deviceOn>
          <el-select
            v-model="form.deviceOn"
            style="width: 100%"
            size="small"
            placeholder="请选择设备新旧"
            :disabled="true"
          >
            <el-option
              v-for="item in deviceOnOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
        <template #addToDeviceGroup>
          <el-checkbox
            v-model="form.addToDeviceGroup"
            border
            :disabled="editType === 'info'"
          >
            添加到设备组
          </el-checkbox>
        </template>
        <template #deviceGroupId>
          <el-select
            v-model="form.deviceGroupId"
            placeholder="请选择设备组"
            style="width: 100%"
            :disabled="editType === 'info'"
            clearable
          >
            <el-option
              v-for="item in deviceGroupOptions"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
        <template #hasGive>
          <el-radio-group
            v-model="form.hasGive"
            :disabled="editType === 'info'"
          >
            <el-radio :label="false"> 无赠送 </el-radio>
            <el-radio :label="true"> 有赠送 </el-radio>
          </el-radio-group>
        </template>
        <!-- 赠送服务 -->
        <template v-if="form.hasGive" #customerContractGives>
          <GiftService
            ref="giftServiceRef"
            v-model="form"
            :edit-type="editType"
          />
        </template>
        <template #serviceInfo>
          <!-- 分期付款 -->
          <InstallmentPayment
            v-if="form.settleMethod === 'INSTALLMENT'"
            v-model="form"
            :edit-type="editType"
            installment-field="tradeOrderInstallments"
          />
          <!-- 服务类型 -->
          <!-- 服务类型：质保 -->
          <WarrantyContract
            v-if="form.serType === 'WARRANTY'"
            ref="warrantyRef"
            v-model="form"
            :edit-type="editType"
          />
          <!-- 服务类型：全/半保 -->
          <FullHalfGuaranteed
            v-if="getServiceType(form.serType)"
            ref="serviceRef"
            v-model="form"
            :service-type="form.serType"
            :contract-type="contractType"
            :edit-type="editType"
          />
          <!-- 服务类型: 其它 -->
          <OtherService
            v-if="form.serType === 'OTHER'"
            ref="otherRef"
            v-model="form"
            service-type="buy"
            :edit-type="editType"
          />
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import InstallmentPayment from "@/views/custom/editCustom/components/contract/installmentPayment.vue";
import WarrantyContract from "@/views/custom/editCustom/components/contract/warrantyContract.vue";
import FullHalfGuaranteed from "@/views/custom/editCustom/components/contract/fullHalfGuaranteed.vue";
import OtherService from "@/views/custom/editCustom/components/contract/otherService.vue";
import { addAmount, filterParam, subtractAmount } from "@/utils";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
import { productAllApi } from "@/api/dispose";
import GiftService from "@/views/custom/editCustom/components/contract/giftService.vue";
import { getCustomerDeviceListApi } from "@/api/customer";

export default {
  name: "ByuMachineInfo",
  components: {
    GiftService,
    InstallmentPayment,
    WarrantyContract,
    FullHalfGuaranteed,
    OtherService,
  },
  props: {
    isSupplement: {
      type: Boolean,
      default: false,
    },
    contractType: {
      type: String,
      default: "",
    },
    customerId: {
      type: String,
      default: "",
    },
    // editType: {
    //   type: String,
    //   default: "add",
    // },
  },
  data() {
    return {
      dialogVisible: false,
      // form
      confirmLoading: false,
      form: {},
      defaultForm: {
        customerContractGives: [], // 赠送明细
      },
      editType: "add",
      formColumns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isForm: true,
          disabled: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isForm: true,
          disabled: true,
          formSlot: "hostType",
          formSpan: 6,
        },
        {
          dataIndex: "productId",
          title: "品牌/机型",
          isForm: false,
          // valueType: "text",
          formSlot: "productId",
          formSpan: 6,
        },
        {
          dataIndex: "productInfo",
          title: "选配件型号",
          isForm: false,
          disabled: true,
          valueType: "input",
          formSpan: 6,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isForm: true,
          formSlot: "deviceOn",
          formSpan: 6,
        },
        {
          dataIndex: "settleStatus",
          title: "结算状态",
          isForm: true,
          valueType: "select",
          option: [
            {
              label: "未结清",
              value: "0",
            },
            {
              label: "已结清",
              value: "1",
            },
          ],
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请选择结算状态",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "settleMethod",
          title: "结算方式",
          isForm: true,
          valueType: "select",
          effect: "light",
          tooltipContent:
            "全款：在合约确认后，需一次性完成购机全部款项的支付；\n定金+尾款：合约确认后，先支付定金，剩余尾款将显示在客户合约列表中，直至客户完成付款；\n分期付款：客户可通过分期方式支付尾款金额。",
          option: [
            {
              label: "全款",
              value: "FULL",
            },
            {
              label: "定金+尾款",
              value: "ADVANCE",
            },
            {
              label: "分期付款",
              value: "INSTALLMENT",
            },
          ],
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请选择结算方式",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "fullAmount",
          title: "机器总价",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请输入机器总价",
              trigger: "blur",
            },
            {
              validator(rule, value, callback) {
                if (value < 0) {
                  callback(new Error("机器总价不能为负数"));
                }
                callback();
              },
            },
          ],
          attrs: {
            suffix: "元",
          },
        },
        {
          dataIndex: "depositAmount",
          title: "定金",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请输入定金",
              trigger: "blur",
            },
            {
              validator(rule, value, callback) {
                if (value < 0) {
                  callback(new Error("定金不能为负数"));
                }
                callback();
              },
            },
          ],
          attrs: {
            suffix: "元",
          },
        },
        {
          dataIndex: "discountAmount",
          title: "折扣金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 6,
          attrs: {
            suffix: "元",
          },
          prop: [
            {
              validator(rule, value, callback) {
                if (value < 0) {
                  callback(new Error("折扣金额不能为负数"));
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "arrersAmount",
          title: "尾款",
          isForm: true,
          valueType: "input",
          inputType: "number",
          disabled: true,
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请输入总销售金额",
              trigger: "blur",
            },
          ],
          attrs: {
            suffix: "元",
          },
        },
        {
          dataIndex: "freeShipping",
          title: "是否包邮",
          isForm: true,
          valueType: "radio",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
          formSpan: 6,
        },
        {
          dataIndex: "installAmount",
          title: "安装费用",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 6,
          prop: [
            {
              validator(rule, value, callback) {
                if (value < 0) {
                  callback(new Error("安装费用不能为负数"));
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "installDate",
          title: "安装日期",
          isForm: true,
          valueType: "date-picker",
          pickerType: "date",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          formSpan: 6,
        },
        {
          title: "初始黑白计数器",
          dataIndex: "initBlackWhiteCounter",
          isForm: false,
          formSpan: 6,
          valueType: "input",
          inputType: "number",
          prop: [
            {
              validator(rule, value, callback) {
                if (value < 0) {
                  callback(new Error("计数器不能为负数"));
                }
                callback();
              },
            },
          ],
        },
        {
          title: "初始彩色计数器",
          dataIndex: "initColorCounter",
          isForm: false,
          formSpan: 6,
          valueType: "input",
          inputType: "number",
          prop: [
            {
              validator(rule, value, callback) {
                if (value < 0) {
                  callback(new Error("计数器不能为负数"));
                }
                callback();
              },
            },
          ],
        },
        {
          title: "初始五色计数器",
          dataIndex: "initFiveCounter",
          isForm: false,
          formSpan: 6,
          valueType: "input",
          inputType: "number",
          prop: [
            {
              validator(rule, value, callback) {
                if (value < 0) {
                  callback(new Error("计数器不能为负数"));
                }
                callback();
              },
            },
          ],
        },

        {
          dataIndex: "serType",
          title: "服务类型",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          // formSlot: true,
          effect: "light",
          tooltipContent:
            "服务类型的切换可能会清除一些影响程序运行的数据，因此请谨慎操作，避免随意更改服务类型。",
          option: [
            {
              label: "购机不保",
              value: "NO_WARRANTY",
            },
            {
              label: "购机质保",
              value: "WARRANTY",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "其它",
              value: "OTHER",
            },
          ],
          prop: [
            {
              required: true,
              message: "请选择服务类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "addToDeviceGroup",
          title: "添加机器信息",
          isForm: true,
          formSlot: "addToDeviceGroup",
          formSpan: 6,
        },
        {
          dataIndex: "deviceGroupId",
          title: "添加到现有设备组",
          isForm: true,
          formSlot: "deviceGroupId",
          formSpan: 6,
        },
        {
          dataIndex: "hasGive",
          title: "有无赠送",
          isForm: true,
          formSlot: "hasGive",
          formSpan: 6,
        },
        {
          dataIndex: "customerContractGives",
          title: "赠送物品",
          isForm: true,
          formOtherSlot: "customerContractGives",
          formSlot: 24,
        },
        {
          dataIndex: "serviceInfo",
          title: "服务信息",
          isForm: true,
          formOtherSlot: "serviceInfo",
          formSpan: 24,
        },
      ],
      hostTypeListOptions: [],
      deviceOnOptions: [],
      options: [],
      deviceGroupOptions: [],
    };
  },
  watch: {
    isSupplement: {
      handler(val) {
        if (val) {
          this.updateIsFormColumn(
            this.formColumns,
            ["addToDeviceGroup", "deviceGroupId"],
            true
          );
          // this.$set(this.form, "addToDeviceGroup", true);
        } else {
          this.updateIsFormColumn(
            this.formColumns,
            ["addToDeviceGroup", "deviceGroupId"],
            false
          );
        }
      },
      immediate: true,
    },
    // 结算方式
    "form.settleMethod": {
      handler(val) {
        if (val === "FULL") {
          this.updateIsFormColumn(
            this.formColumns,
            ["depositAmount", "arrersAmount"],
            false
          );
        } else {
          this.updateIsFormColumn(
            this.formColumns,
            ["depositAmount", "arrersAmount"],
            true
          );
        }
      },
    },
    // 总销售金额
    "form.fullAmount": {
      handler(val) {
        this.calculateArrearsAmount();
      },
    },
    // 定金
    "form.depositAmount": {
      handler(val) {
        this.calculateArrearsAmount();
      },
    },
    // 折扣金额
    "form.discountAmount": {
      handler(val) {
        this.calculateArrearsAmount();
      },
    },
  },
  methods: {
    /**
     * 显示弹窗
     * @param val 单行列表数据
     * @param type 编辑类型： info edit add
     */
    visible(val, type) {
      this.editType = type;
      this.resetFormParams().then(() => {
        const formParams = {
          ...this.defaultForm,
          ...val,
        };
        Object.keys(formParams).forEach((key) => {
          formParams[key] = formParams[key]?.label
            ? formParams[key].value
            : formParams[key];
        });
        this.dialogVisible = true;
        this.form = formParams;
        if (this.form.hasGive === undefined || this.form.hasGive === null) {
          this.$set(this.form, "hasGive", false);
        }
        if (this.form.hostType === "2008") {
          this.updateIsFormColumn(
            this.formColumns,
            [
              "productId",
              "initBlackWhiteCounter",
              "initColorCounter",
              "initFiveCounter",
            ],
            true
          );
        } else {
          this.updateIsFormColumn(this.formColumns, ["productInfo"], true);
          this.updateIsFormColumn(
            this.formColumns,
            ["initBlackWhiteCounter", "initColorCounter", "initFiveCounter"],
            false
          );
        }

        if (
          this.isSupplement &&
          this.editType === "edit" &&
          this.form.isSupplement !== undefined
        ) {
          this.$set(this.form, "addToDeviceGroup", true);
        }
        this.calculateArrearsAmount();
        this.getProductType();
        this.getDeviceOnOptions();
        this.getProductTreeOptions();
        this.getCustomerDeviceList();
      });
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    formSubmit(val) {
      const validationPromises = [];
      if (this.$refs.warrantyRef && this.$refs.warrantyRef.$refs.warrantyRef) {
        validationPromises.push(
          new Promise((resolve) => {
            this.$refs.warrantyRef.$refs.warrantyRef.validate((valid) => {
              resolve(valid);
            });
          })
        );
      }
      if (this.$refs.serviceRef && this.$refs.serviceRef.$refs.formRef) {
        validationPromises.push(
          new Promise((resolve) => {
            this.$refs.serviceRef.$refs.formRef.validate((valid) => {
              resolve(valid);
            });
          })
        );
      }
      if (this.$refs.otherRef && this.$refs.otherRef.$refs.formRef) {
        validationPromises.push(
          new Promise((resolve) => {
            this.$refs.otherRef.$refs.formRef.validate((valid) => {
              resolve(valid);
            });
          })
        );
      }
      // 等待所有验证完成
      Promise.all(validationPromises).then((results) => {
        if (results.every((result) => result)) {
          const result = cloneDeep(this.form);
          if (result.settleMethod === "INSTALLMENT") {
            if (!result.tradeOrderInstallments.length) {
              this.$message.error("请填写分期信息");
              return;
            }
            // 检查分期列表的总金额是否等于欠款金额
            const totalAmount = result.tradeOrderInstallments.reduce(
              (acc, cur) => {
                return addAmount(acc, cur.amount);
              },
              0
            );
            if (totalAmount !== result.arrersAmount) {
              this.$message.error("分期金额总和必须等于欠款金额");
              return;
            }
          }
          // if (result.priceType === "LADDER") {
          //   if (!result.repairMonthlyPrices.length) {
          //     return this.$message.error("请填写阶梯价格");
          //   }
          // }
          this.$emit("confirmContractInfo", filterParam(result));
          this.dialogVisible = false;
        } else {
          this.$message.error("请将合约信息填写完整");
        }
      });
    },
    async getProductTreeOptions() {
      try {
        const result = await productAllApi();
        if (result.code === 200) {
          this.options = result.data;
        }
      } catch (error) {
        this.options = [];
      }
    },
    async getProductType() {
      try {
        const result = await dictTreeByCodeApi(2000);
        if (result.code === 200) {
          this.hostTypeListOptions = result.data;
        }
      } catch (error) {
        this.hostTypeListOptions = [];
      }
    },
    async getDeviceOnOptions() {
      try {
        const result = await dictTreeByCodeApi(1100);
        if (result.code === 200) {
          this.deviceOnOptions = result.data;
        }
      } catch (error) {
        this.deviceOnOptions = [];
      }
    },
    getServiceType(type) {
      return ["BUY_HALF", "BUY_FULL", "PACKAGE_HALF", "PACKAGE_ALL"].includes(
        type
      );
    },
    handleDialogCancel() {
      this.dialogVisible = false;
      this.updateIsFormColumn(
        this.formColumns,
        ["productId", "productInfo"],
        false
      );
    },
    calculateArrearsAmount() {
      const fullAmount = this.form.fullAmount || 0;
      const depositAmount = this.form.depositAmount || 0;
      const discountAmount = this.form.discountAmount || 0;
      if (fullAmount) {
        let arrersAmount = fullAmount;

        if (depositAmount) {
          arrersAmount = subtractAmount(arrersAmount, depositAmount);
        }
        if (discountAmount) {
          arrersAmount = subtractAmount(arrersAmount, discountAmount);
        }
        this.form.arrersAmount = Math.max(arrersAmount, 0);
      } else {
        this.form.arrersAmount = 0;
      }
    },
    // 获取用户设备组列表
    async getCustomerDeviceList() {
      if (!this.customerId) {
        return;
      }
      try {
        const result = await getCustomerDeviceListApi(this.customerId);
        if (result.code === 200) {
          this.deviceGroupOptions = result.data.map((item) => {
            return {
              label: `${item.deviceGroup?.label}/${item.productInfo}`,
              value: item.id,
            };
          });
        }
      } catch (e) {
        this.deviceGroupOptions = [];
      }
    },
    updateIsFormColumn(columns, keys, isForm) {
      columns.forEach((item) => {
        if (keys.includes(item.dataIndex)) {
          item.isForm = isForm;
        }
      });
    },
    resetFormParams() {
      return new Promise((resolve) => {
        Object.keys(this.form).forEach((key) => {
          delete this.form[key];
        });
        resolve();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.billing-method {
  .mb-0 {
    margin-bottom: 0;
  }

  .installment-details {
    .el-input-number {
      width: 100%;
    }
  }
}
</style>
