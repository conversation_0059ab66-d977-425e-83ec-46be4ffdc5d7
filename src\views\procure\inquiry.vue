<!--
 * @Author: yangzhong
 * @Date: 2023-12-05 20:13:49
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:58:43
 * @Description: 询价单
-->
<template>
  <div class="inquiry view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增询价
        </el-button>
        <!--<el-button-->
        <!--  type="success"-->
        <!--  icon="el-icon-upload2"-->
        <!--  size="mini"-->
        <!--  @click="$refs.uploadExcel.show()"-->
        <!--&gt;-->
        <!--  导入数据-->
        <!--</el-button>-->
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleChange"
        ></el-cascader>
      </template>
      <!-- 物品大小类 -->
      <template #type>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.goodsType"
          filterable
          :options="goodsTypeOptions"
          style="width: 100%"
          :props="{
            label: 'label',
            value: 'value',
            children: 'children',
            expandTrigger: 'click',
          }"
          clearable
          leaf-only
        ></el-cascader>
      </template>

      <template #machine="slotProps">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in slotProps.row.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label">品牌/系列/机型</template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <el-button slot="reference" type="text" size="mini">
            适用机型
          </el-button>
        </el-popover>
      </template>
      <template #offerTime>
        <el-date-picker
          v-model="offerTime"
          type="daterange"
          style="width: 100%"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :format="'yyyy-MM-dd'"
          value-format="yyyy-MM-dd"
          @change="changeDate"
        ></el-date-picker>
      </template>
      <template #articleCode="slotProps">
        <span class="info-atext" @click="articleInfo(slotProps.row.articleId)">
          {{ slotProps.row.articleCode }}
        </span>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleEdit(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 新增弹框 -->
    <addInquiry ref="addInquiry" @refresh="refresh" />
    <!-- 编辑弹框 -->
    <ProDialog
      :value="showInquiryDialog"
      title="编辑询价单"
      width="800px"
      :confirm-loading="false"
      top="5%"
      :no-footer="false"
      @ok="handleEditConfirm"
      @cancel="handleEditCancel"
    >
      <ProForm
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        @proSubmit="proSubmit"
      >
        <template #priceList>
          <div class="price-list">
            <!--            <div v-for="(list, index) in priceLists" :key="index" class="item">-->
            <div class="item">
              <p>
                <span>数量:</span>
                <el-input
                  v-model="form.num"
                  style="width: 80%"
                  type="number"
                  placeholder="请输入数量"
                ></el-input>
              </p>
              <p>
                <span>价格:</span>
                <el-input
                  v-model="form.price"
                  style="width: 80%"
                  type="number"
                  step="0.1"
                  placeholder="请输入价格"
                ></el-input>
              </p>
            </div>
          </div>
          <!--          <el-button style="margin-top: 10px" size="mini" @click="addPriceList"-->
          <!--            >新增价格</el-button-->
          <!--          >-->
        </template>
      </ProForm>
    </ProDialog>
    <ProDrawer
      :value="dialogVisible1"
      :title="'物品详情'"
      size="80%"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible1 = false"
    >
      <ProForm
        v-if="dialogVisible1"
        ref="proform"
        :form-param="form1"
        :form-list="formcolumns1"
        :layout="{ formWidth: '100%', labelWidth: '180px' }"
        open-type="info"
      >
        <template #type>
          {{ form1.type.label }}
        </template>
        <template #manufacturerChannel>
          {{ form1.manufacturerChannel.label }}
        </template>
        <template #imageFiles>
          <div style="display: flex; width: 100%; flex-wrap: wrap">
            <el-image
              v-for="(item, index) in form1.imageFiles"
              :key="index"
              class="imgs"
              :src="item.url"
              alt=""
              :preview-src-list="[item.url]"
            ></el-image>
          </div>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 导入 -->
    <UploadExcel
      ref="uploadExcel"
      title="导入询价数据"
      :action-url="actionUrl"
      :download-template-fun="handleDownloadTemplate"
      @uploadSuccess="handleUploadExcelSuccess"
    />
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import addInquiry from "./cpns/addInquiry.vue";
import { MessageBox, Message } from "element-ui";
import { dictTreeByCodeApi } from "@/api/user";
import { productAllApi } from "@/api/dispose";
import {
  pageInquiryApi,
  updateInquiryApi,
  deleteInquiryApi,
  importInquiryApi,
  downTemplateApi,
} from "@/api/procure";
import { cloneDeep } from "lodash";
const { uploadURL } = window.config.api;
import { handleExcelExport } from "@/utils/exportExcel";
import UploadExcel from "@/components/ProUpload/excel.vue";

import { manufacturerListApi, articleGetDetailApi } from "@/api/store";
export default {
  name: "Inquiry",
  components: { ProTable, addInquiry, UploadExcel },
  data() {
    return {
      actionUrl: uploadURL + importInquiryApi,
      tableData: [],
      queryParam: {
        productTreeId: [],
        goodsType: [],
      },
      productIdName: "",
      options: [],
      columns: [
        {
          dataIndex: "productTreeId",
          isSearch: true,
          clearable: true,
          // searchSlot: "fullIdPath",
          title: "适用机型",
          valueType: "product",
        },
        // {
        //   dataIndex: "machine",
        //   isSearch: true,
        //   clearable: true,
        //   title: "适用机型",
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "unit",
        //   title: "所属单元",
        //   isSearch: true,
        //   valueType: "select",
        //   clearable: true,
        //   formSpan: 8,
        //   option: [],
        //   // formatter: (row) => row.type.label,
        //   optionMth: () => dictTreeByCodeApi(3200),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        {
          dataIndex: "type",
          isSearch: true,
          clearable: true,
          title: "物品大小类",
          valueType: "select",
          searchSlot: "type",
          formSpan: 8,
          // option: [],
          // // formatter: (row) => row.type.label,
          // optionMth: () => dictTreeByCodeApi(2100),
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
        },
        {
          dataIndex: "offerTime",
          title: "报价时间",
          isSearch: true,
          clearable: true,
          searchSlot: "offerTime",
        },
        {
          dataIndex: "articleName",
          isSearch: true,
          clearable: true,
          title: "物品名称",
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          width: 180,
          valueType: "input",
          tableSlot: "articleCode",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          width: 180,
          valueType: "input",
          formatter: (row) => row.storageArticle?.numberOem,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          width: 120,
          formatter: (row) => row.storageArticle?.name,
        },

        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          width: 150,
          isTable: true,
        },
        {
          dataIndex: "manufacturerId",
          title: "供应商名称",
          width: 150,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => manufacturerListApi(),
          optionskey: {
            label: "name",
            value: "id",
          },
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          // isTable: true,
          width: 150,
          formatter: (row) => row.storageArticle?.manufacturerGoodsName,
        },

        {
          dataIndex: "machine",
          title: "适用机型",
          isTable: true,
          width: 150,
          tableSlot: "machine",
        },
        {
          dataIndex: "type",
          title: "物品大小类",
          isTable: true,
          width: 120,
          formatter: (row) => row.storageArticle?.type?.label,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          clearable: true,
          isTable: true,
          isSearch: true,
          width: 120,
          valueType: "select",
          formatter: (row) => row.storageArticle?.manufacturerChannel?.label,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "colorBox",
          title: "颜色/纸盒",
          // isTable: true,
          width: 120,
          formatter: (row) => row.storageArticle?.colorBox,
        },
        // {
        //   dataIndex: "inCarrier",
        //   title: "含载体",
        //   isTable: true,
        //   width: 80,
        //   formatter: (row) => row.storageArticle?.inCarrier,
        // },
        // {
        //   dataIndex: "inChip",
        //   title: "含芯片",
        //   isTable: true,
        //   width: 80,
        //   formatter: (row) => row.storageArticle?.inChip,
        // },
        // {
        //   dataIndex: "suttle",
        //   title: "净重",
        //   isTable: true,
        //   width: 80,
        //   formatter: (row) => row.storageArticle?.suttle,
        // },
        {
          dataIndex: "unit",
          title: "单位",
          width: 80,
          isTable: true,
          formatter: (row) => row.storageArticle?.unit,
        },
        {
          dataIndex: "num",
          title: "需求数量",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "createdBy",
          title: "询价人",
          isTable: true,
          width: 80,
          formatter: (row) => row.createdBy?.name,
        },
        {
          dataIndex: "createdAt",
          title: "询价日期",
          isTable: true,
          width: 160,
        },
        {
          dataIndex: "expiresTime",
          title: "有效期截至",
          isTable: true,
          width: 160,
        },
        {
          dataIndex: "comment",
          title: "备注",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          fixed: "right",
          align: "left",
          tableSlot: "action",
          width: 180,
        },
      ],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      goodsTypeOptions: [],
      showInquiryDialog: false,
      form: {},
      formcolumns: [
        {
          dataIndex: "inCarrier",
          title: "含载体",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "inChip",
          title: "含芯片",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "suttle",
          title: "净重",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        // {
        //   dataIndex: "price",
        //   isForm: true,
        //   title: "单价",
        //   valueType: "input",
        //   inputType: "number",
        //   clearable: true,
        //   formSpan: 24,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请输入单价",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "price",
          title: "数量/价格",
          isForm: true,
          formSlot: "priceList",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入价格及数量",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "expiresTime",
          isForm: true,
          title: "有效期截至",
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          // pickerFormat: "yyyy-MM-dd",
          // valueFormat: "yyyy-MM-dd",
          // attrs: { "value-format": "yyyy-MM-dd" },
          // clearable: true,
          formSpan: 13,
          prop: [
            {
              required: true,
              message: "请选择有效期",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "comment",
          isForm: true,
          title: "备注",
          valueType: "input",
          inputType: "textarea",
          clearable: true,
          formSpan: 24,
          attrs: {
            rows: 5,
          },
        },
      ],
      offerTime: [],
      priceLists: [
        { quantity: 1, price: 10 },
        { quantity: 2, price: 8 },
      ],
      form1: {},
      dialogVisible1: false,
      formcolumns1: [
        {
          dataIndex: "name",
          isForm: true,
          title: "物品名称",
          valueType: "text",
        },
        {
          clearboth: true,
          dataIndex: "type",
          title: "物品大小类",
          isForm: true,
          formSpan: 6,
          valueType: "text",
          formSlot: "type",
        },

        {
          dataIndex: "numberOem",
          title: "原厂零件编号（OEM）",
          isForm: true,
          valueType: "text",
          formSpan: 6,
          // formSlot: "numberOem",
        },
        {
          dataIndex: "partName",
          isForm: true,
          title: "零件名称",
          valueType: "text",
          formSpan: 6,
        },

        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isForm: true,
          valueType: "text",
          formSpan: 6,
          formSlot: "manufacturerChannel",
        },
        {
          dataIndex: "manufacturerGoodsCode",
          title: "制造商物品编号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },

        {
          dataIndex: "manufacturerName",
          title: "制造商名称",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "manufacturerCode",
          title: "制造商编号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "partBrand",
          title: "零件品牌",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },

        {
          dataIndex: "minUnit",
          title: "单位（最小单位）",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "colorBox",
          title: "颜色/纸盒",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "expectLife",
          title: "预计寿命",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "suttle",
          title: "净重",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "packSize",
          title: "包装尺寸",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "inCarrier",
          title: "含载体",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "inChip",
          title: "含芯片",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "packWeight",
          title: "包装-重量",
          isForm: true,
          valueType: "text",
          formSpan: 6,
          unit: "g",
        },
        {
          dataIndex: "packLong",
          title: "包装-长",
          isForm: true,
          valueType: "text",
          formSpan: 6,
          unit: "mm",
        },
        {
          dataIndex: "packWide",
          title: "包装-宽",
          isForm: true,
          valueType: "text",
          formSpan: 6,
          unit: "mm",
        },
        {
          dataIndex: "packHigh",
          title: "包装-高",
          isForm: true,
          valueType: "text",
          formSpan: 6,
          unit: "mm",
        },
        {
          dataIndex: "saveWeek",
          title: "保存周期",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "saveTemp",
          title: "保存温度",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "saveHumidity",
          title: "保存湿度",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "isSqueeze",
          title: "能否挤压",
          isForm: true,
          clearboth: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "paperWeight",
          title: "纸张克重",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "paperLong",
          title: "纸张-长",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "paperWide",
          title: "纸张-宽",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "paperSize",
          title: "纸张尺寸规格",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "paperSeries",
          title: "纸张系列",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "totalNumber",
          title: "统计数量",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "avgMoney",
          title: "平均单价",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "sumLife",
          title: "修正寿命",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "avgLife",
          title: "平均寿命",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "packSpec",
          title: "包装规格",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },

        {
          dataIndex: "imageFiles",
          title: "物品图片",
          isForm: true,
          width: 150,
          formSlot: "imageFiles",
        },
      ],
    };
  },
  mounted() {
    this.refresh();
    this.init();
  },
  methods: {
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
      dictTreeByCodeApi(2100).then((res) => {
        this.goodsTypeOptions = res.data;
      });
    },
    loadData(params) {
      // console.log(this.productIdName);
      // console.log(this.queryParam.productTreeId);
      params.goodsType && delete params.goodsType;

      const data = {
        ...params,
        offerPriceStart:
          this.offerTime?.length > 0 ? this.offerTime?.[0] + " 00:00:00" : null,
        offerPriceEnd:
          this.offerTime?.length > 0 ? this.offerTime?.[1] + " 00:00:00" : null,
        productTreeId: this.queryParam.productTreeId,
        type: this.queryParam.goodsType[this.queryParam.goodsType.length - 1],
      };
      if (data.productTreeId.length === 0) delete data.productTreeId;
      this.tableData = [];
      pageInquiryApi(data).then((res) => {
        this.tableData = res.data.rows;
        this.localPagination.total = +res.data.total;
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    handleUploadExcelSuccess() {
      this.refresh();
    },
    handleDownloadTemplate() {
      handleExcelExport(downTemplateApi, {}, "询价单数据导入模板");
    },
    // 编辑
    handleEdit(row) {
      this.form = cloneDeep(row);
      this.form.inCarrier = this.form.storageArticle.inCarrier || "";
      this.form.inChip = this.form.storageArticle.inChip || "";
      this.form.suttle = this.form.storageArticle.suttle || "";
      this.showInquiryDialog = true;
    },
    // 新增
    handleAdd() {
      this.$refs.addInquiry.show();
    },
    addPriceList() {
      this.priceLists.push({ quantity: null, price: null });
    },
    // 弹框确认
    handleEditConfirm() {
      this.$refs.proform.handleSubmit();
    },
    handleEditCancel() {
      this.showInquiryDialog = false;
      this.form = {};
    },
    // 提交
    async proSubmit(data) {
      // console.log(data.expiresTime.indexOf("00:00:00"));
      // data.expiresTime.indexOf("00:00:00") < 0
      //   ? (data.expiresTime = data.expiresTime + " 00:00:00")
      //   : data.expiresTime;
      this.$confirm("此操作将修改询价单信息，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const { id, comment, expiresTime, price, num } = data;
          const result = await updateInquiryApi({
            id,
            comment,
            expiresTime,
            price,
            num,
          });
          if (result.code === 200) {
            Message.success("修改成功");
            this.showInquiryDialog = false;
            this.refresh();
          }
        } finally {
          console.log(1);
        }
      });
    },
    // 删除
    handleDelete(row) {
      MessageBox.confirm("确定删除该询价单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const result = await deleteInquiryApi(row.id);
          if (result.code === 200) {
            this.$message.success("删除成功");
            this.localPagination = {
              pageNumber: 1,
              pageSize: 10,
              total: 0,
            };
            this.$nextTick(() => {
              this.refresh();
            });
          }
        })
        .finally(() => {});
    },
    handleChange(item) {
      console.log(item);
      this.queryParam.productTreeId = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productTreeId.push(id);
      });
    },
    changeDate(time) {
      console.log(time);
      this.offerTime = time;
    },
    // 触发详情
    articleInfo(code) {
      articleGetDetailApi(code).then((res) => {
        this.form1 = cloneDeep(res.data);
        this.dialogVisible1 = true;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.price-list {
  display: flex;
  //flex-direction: column;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
  .item {
    display: flex;
    width: 100%;
    gap: 20px;
    p {
      display: flex;
      span {
        width: 20%;
      }
    }
  }
}
::v-deep .el-range-separator {
  width: 8% !important;
}
.imgs {
  height: 120px;
  width: 120px;
  float: left;
}
</style>
