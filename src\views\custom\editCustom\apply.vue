<template>
  <div class="store">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      :show-search="false"
      :show-settings="false"
      @loadData="loadData"
    >
      <template #btn>
        <div class="box">
          <div>抄表开始时间：{{ totalData?.meterDate }}</div>
          <div>总库存金额：{{ totalData?.amount }}</div>
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { receiveListApi } from "@/api/statisics";
import { getCustomerItemStoreApi } from "@/api/customer";

export default {
  name: "Apply",
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
  },
  data() {
    return {
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "itemNum",
          title: "数量",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "单价",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "payAmount",
          title: "金额",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "customerName",
          title: "领料人",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "createdAt",
          title: "领料时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "reviewers",
          title: "审核人",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "reviewersTime",
          title: "审核时间",
          isTable: true,
          width: 150,
        },
      ],
      tableData: [],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
    getCustomerItemStoreApi(this.id).then((res) => {
      this.totalData = res.data;
    });
  },
  methods: {
    loadData(params) {
      receiveListApi({ ...params, customerId: this.id })
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
          console.log(res);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  display: flex;
  gap: 50px;
  font-size: 16px;
  color: #6488cf;
}
</style>
