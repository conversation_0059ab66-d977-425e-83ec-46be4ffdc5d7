<!--
 * @Author: yangzhong
 * @Date: 2023-12-05 20:13:49
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:51
 * @Description: 询价单
-->
<template>
  <div class="supplySource view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #miniPriceFlag="slotProps">
        <span :style="{ color: slotProps.row.miniPriceFlag ? 'red' : '#000' }">
          {{ slotProps.row.miniPriceFlag ? "是" : "否" }}
        </span>
      </template>
      <!-- 机型 -->
      <template #machine="slotProps">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in slotProps.row.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label">品牌/系列/机型</template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-button slot="reference" type="text" size="mini">
            适用机型
          </el-button>
        </el-popover>
      </template>
      <!-- 物品大小类 -->
      <template #goodsType>
        <el-cascader
          ref="ProductIds"
          v-model="goodsType"
          filterable
          :options="goodsTypeOptions"
          style="width: 100%"
          :props="{
            label: 'label',
            value: 'value',
            children: 'children',
            expandTrigger: 'click',
          }"
          clearable
          leaf-only
          @change="handleChangeGoodsType"
        ></el-cascader>
      </template>
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleEdit(null, 'add')"
        >
          新增供应源
        </el-button>
      </template>
      <template #status="{ row, index }">
        <el-switch
          v-model="row.status"
          active-color="#13ce66"
          inactive-color="#ff4949"
          :active-value="1"
          :inactive-value="0"
          @change="handleChangeStatus(row, index)"
        ></el-switch>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            :disabled="!row.status ? false : true"
            icon="el-icon-edit-outline"
            @click="handleSelectClickFn(row.storageArticle.code)"
          >
            修改供应源
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-circle-check"
            @click="handleEdit(row, 'info')"
          >
            详情
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleEdit(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="95%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType == 'info'"
      confirm-text="确认新增"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <div class="add-box">
        <el-form ref="formRef" label-width="100px" label-position="right">
          <el-form-item
            label="选择询价单"
            prop="name"
            :rules="[
              {
                required: true,
                trigger: 'change',
              },
            ]"
          >
            <el-button
              type="success"
              class="add-btn"
              size="mini"
              icon="el-icon-plus"
              @click="handleSelectClick('')"
            >
              选择
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <ProTable
        ref="ProTableAdd"
        row-key="id"
        :data="tableDataAdd"
        :columns="columnsAdd"
        :query-param="addQueryParam"
        height="72vh"
        :show-setting="false"
        :show-loading="false"
        :show-search="false"
        @loadData="loadDataAdd"
      >
        <template #machine="slotProps">
          <el-popover placement="bottom" title="" width="700" trigger="click">
            <div style="margin: 20px; height: 400px; overflow-y: scroll">
              <el-descriptions
                class="margin-top"
                title="适用机型"
                :column="1"
                border
              >
                <el-descriptions-item
                  v-for="item in slotProps.row.productTreeDtoList"
                  :key="item.id"
                >
                  <template slot="label">品牌/系列/机型</template>
                  {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <el-button slot="reference" type="text" size="mini">
              适用机型
            </el-button>
          </el-popover>
        </template>
        <template #action="{ row, index }">
          <div class="fixed-width">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-warning-outline"
              @click="handleInfoClick(row, index)"
            >
              结算信息
            </el-button>
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              @click="handleRemove(row)"
            >
              移除
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDrawer>

    <!-- 选择询价单的弹框 -->
    <SupplaySearchInquiry
      ref="supplaySearchInquiryRef"
      :selected-data="tableDataAdd"
      @addData="handleAddData"
    ></SupplaySearchInquiry>

    <!-- 结算信息弹框 -->
    <SettleInfoDialog
      ref="settleInfoDialogRef"
      @sure="handleSettleDialogConfirm"
    ></SettleInfoDialog>
    <!-- 编辑弹框 -->
    <SettleEditDialog
      ref="settleEditDialogRef"
      @sure="handleSettleEditConfirm"
    ></SettleEditDialog>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import { Message, MessageBox } from "element-ui";
import SettleInfoDialog from "./cpns/settleInfoDialog.vue";
import SettleEditDialog from "./cpns/settleEditDialog.vue";
import SupplaySearchInquiry from "./cpns/supplaySearchInquiry.vue";

import { dictTreeByCodeApi } from "@/api/user";
import { manufacturerListApi } from "@/api/store";
import {
  addSupplierApi,
  pageSupplierApi,
  updateSupplierApi,
  deleteSupplierApi,
  updateSupplierStatusApi,
} from "@/api/procure";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";

export default {
  name: "SupplySource",
  components: {
    ProTable,
    SettleInfoDialog,
    SettleEditDialog,
    SupplaySearchInquiry,
  },
  data() {
    return {
      goodsType: "",
      tableData: [],
      queryParam: {},
      form: { parentId: "" },
      options: [],
      goodsTypeOptions: [],
      columns: [
        {
          dataIndex: "productTreeId",
          title: "适用机型",
          valueType: "product",
          isSearch: true,
          clearable: true,
          // searchSlot: "fullIdPath",
        },
        {
          dataIndex: "type",
          isSearch: true,
          clearable: true,
          title: "物品大小类",
          valueType: "select",
          searchSlot: "goodsType",
        },
        {
          dataIndex: "manufacturerId",
          title: "供应商名称",
          width: 150,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => manufacturerListApi(),
          optionskey: {
            label: "name",
            value: "id",
          },
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          valueType: "input",
          width: 180,
          formatter: (row) => row.storageArticle?.code,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "miniPriceFlag",
          title: "是否最低价",
          isTable: true,
          width: 100,
          tableSlot: "miniPriceFlag",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isSearch: true,
          isTable: true,
          valueType: "input",
          width: 180,
          formatter: (row) => row.storageArticle?.numberOem,
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          isTable: true,
          width: 180,
          formatter: (row) => row.storageArticle?.manufacturerGoodsName,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          width: 180,
          formatter: (row) => row.manufacturer?.name,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isSearch: true,
          clearable: true,
          isTable: true,
          width: 180,
          valueType: "input",
          formatter: (row) => row.storageArticle?.name,
        },
        // {
        //   dataIndex: "partBrand",
        //   title: "品牌",
        //   isTable: true,
        //   width: 80,
        //   formatter: (row) => row.storageArticle?.partBrand,
        // },
        {
          dataIndex: "machine",
          title: "适用机型",
          isTable: true,
          width: 120,
          tableSlot: "machine",
        },
        {
          dataIndex: "categLite",
          title: "物品大小类",
          isTable: true,
          minWidth: 100,
          formatter: (row) => row.storageArticle?.type?.label,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          minWidth: 100,
          formatter: (row) => row.storageArticle?.manufacturerChannel.label,
        },
        {
          dataIndex: "colorBox",
          title: "颜色/纸盒",
          isTable: true,
          minWidth: 100,
          formatter: (row) => row.storageArticle?.colorBox,
        },
        {
          dataIndex: "inCarrier",
          title: "含载体",
          isTable: true,
          minWidth: 80,
          formatter: (row) => row.storageArticle?.inCarrier,
        },
        {
          dataIndex: "inChip",
          title: "含芯片",
          isTable: true,
          minWidth: 80,
          formatter: (row) => row.storageArticle?.inChip,
        },
        {
          dataIndex: "suttle",
          title: "净重",
          isTable: true,
          minWidth: 80,
          formatter: (row) => row.storageArticle?.suttle,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          minWidth: 80,
          formatter: (row) => row.storageArticle?.unit,
        },
        // {
        //   dataIndex: "num",
        //   title: "需求数量",
        //   isTable: true,
        //   width: 120,
        // },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "applicantName",
          title: "申请人",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "status",
          title: "启用状态",
          isTable: true,
          tableSlot: "status",
          width: 100,
        },
        {
          dataIndex: "updatedByName",
          title: "编辑人",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "updateTime",
          title: "编辑日期",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          fixed: "right",
          width: 300,
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
        },
      ],

      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      productIdName: "",
      //新增
      methodType: "add",
      dialogVisible: false,
      dialogTitle: "",
      confirmLoading: false,
      addQueryParam: {},
      tableDataAdd: [],
      columnsAdd: [
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          minWidth: 160,
          formatter: (row) => row.storageArticle?.code,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          minWidth: 160,
          formatter: (row) => row.storageArticle?.numberOem,
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "物品名称",
          isTable: true,
          minWidth: 250,
          formatter: (row) => row.storageArticle?.manufacturerGoodsName,
        },
        // {
        //   dataIndex: "manufacturerName",
        //   title: "供应商名称",
        //   isTable: true,
        //   formatter: (row) => row.manufacturer.name,
        // },
        // {
        //   dataIndex: "partBrand",
        //   title: "品牌",
        //   isTable: true,
        //   width: 120,
        //   formatter: (row) => row.storageArticle?.partBrand,
        // },
        {
          dataIndex: "machine",
          title: "适用机型",
          isTable: true,
          minWidth: 120,
          tableSlot: "machine",
        },
        {
          dataIndex: "categLite",
          title: "物品大小类",
          isTable: true,
          minWidth: 100,
          formatter: (row) => row.storageArticle?.type?.label,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          minWidth: 100,
          formatter: (row) => row.storageArticle?.manufacturerChannel.label,
        },
        {
          dataIndex: "colorBox",
          title: "颜色/纸盒",
          isTable: true,
          minWidth: 80,
          formatter: (row) => row.storageArticle?.colorBox,
        },
        {
          dataIndex: "inCarrier",
          title: "含载体",
          isTable: true,
          minWidth: 80,
          formatter: (row) => row.storageArticle?.inCarrier,
        },
        {
          dataIndex: "inChip",
          title: "含芯片",
          isTable: true,
          minWidth: 80,
          formatter: (row) => row.storageArticle?.inChip,
        },
        {
          dataIndex: "suttle",
          title: "净重",
          isTable: true,
          minWidth: 80,
          formatter: (row) => row.storageArticle?.suttle,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          minWidth: 80,
          formatter: (row) => row.storageArticle?.unit,
        },
        // {
        //   dataIndex: "num",
        //   title: "需求数量",
        //   isTable: true,
        //   width: 80,
        // },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          minWidth: 120,
        },

        {
          dataIndex: "action",
          title: "操作",
          fixed: "right",
          width: 180,
          isTable: true,
          tooltip: false,
          tableSlot: "action",
        },
      ],
      currentTableIndex: 0,
    };
  },
  mounted() {
    this.init();
    this.refresh();
  },
  methods: {
    init() {
      // productAllApi().then((res) => {
      //   this.options = res.data;
      // });
      dictTreeByCodeApi(2100).then((res) => {
        this.goodsTypeOptions = res.data;
      });
    },
    handleSelect(item) {
      this.form.parentId = item[item.length - 1];
    },
    loadData(params) {
      this.queryParam = filterParam(Object.assign({}, this.queryParam, params));
      const searchRange = [
        {
          startDate: null,
          endDate: null,
          data: params.updateTime,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.updateTime;
      pageSupplierApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    handleEdit(row, type) {
      if (type === "add") {
        this.dialogTitle = "新增供应源";
        this.addQueryParam = {};
        this.dialogVisible = true;
        this.$nextTick(() => {
          this.$refs.ProTableAdd.refresh();
          this.tableDataAdd = [];
        });
      } else if (type === "edit") {
        this.dialogTitle = "编辑";
        this.$refs.settleEditDialogRef.show(row, "edit");
      } else if (type === "info") {
        this.dialogTitle = "详情";
        this.$refs.settleEditDialogRef.show(row, "info");
      }
    },
    handleDelete(row) {
      MessageBox.confirm("删除供应源信息。确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const result = await deleteSupplierApi(row.id);
        if (result.code === 200) {
          this.$message.success("删除成功");
          this.localPagination = {
            pageNumber: 1,
            pageSize: 10,
            total: 0,
          };
          this.$nextTick(() => {
            this.refresh();
          });
        }
      });
    },
    //触发表单提交
    handleDialogOk() {
      // 判断表格数据是否有时间信息
      if (
        !this.tableDataAdd.some(
          (item) => item.validityStartTime || item.validityEndTime
        )
      ) {
        Message.error("请完善结算信息");
        return;
      }
      this.$confirm("是否确认新增供应源?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const userInfo = JSON.parse(localStorage.getItem("userInfo"));
        const result = this.tableDataAdd.map((item) => {
          item.applicantId = userInfo.id;
          item.applicantName = userInfo.name;
          if (item.storageArticle) {
            delete item.storageArticle;
            delete item.id;
          }
          item.status = 1;
          return item;
        });
        console.log(result);
        addSupplierApi(result)
          .then((res) => {
            console.log(res);
          })
          .finally(() => {
            this.dialogVisible = false;
            this.refresh();
          });
      });
    },
    // 关闭弹框
    closedialog() {
      this.dialogVisible = false;
    },
    // 状态切换
    handleChangeStatus(row) {
      const params = {
        id: row.id,
        status: row.status,
      };
      updateSupplierStatusApi(params)
        .then((res) => {
          console.log(res);
        })
        .finally(() => {
          this.refresh();
        });
    },
    // 结算信息
    handleInfoClick(row, index) {
      this.currentTableIndex = index;
      const data = cloneDeep(row);
      this.$refs.settleInfoDialogRef.show(data, "edit");
    },
    // 移除
    handleRemove(row) {
      MessageBox.confirm("移除询价单信息。确认移除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const index = this.tableDataAdd.findIndex((item) => item.id === row.id);
        index > -1 && this.tableDataAdd.splice(index, 1);
      });
    },
    loadDataAdd() {
      this.$refs.ProTableAdd && (this.$refs.ProTableAdd.listLoading = false);
    },
    // 选择询价单
    handleSelectClick(id) {
      this.$refs.supplaySearchInquiryRef.show(this.tableDataAdd, id);
    },
    handleSelectClickFn(id) {
      this.handleEdit(null, "add");
      this.$nextTick(() => {
        this.handleSelectClick(id);
      });
    },
    handleAddData(data) {
      this.tableDataAdd = data;
    },
    //
    handleInquiryDialogConfirm() {},
    handleSettleDialogConfirm(data) {
      this.tableDataAdd[this.currentTableIndex] = {
        ...this.tableDataAdd[this.currentTableIndex],
        ...data,
      };
      this.tableDataAdd = [...this.tableDataAdd];
    },
    // 编辑确认
    handleSettleEditConfirm(data) {
      updateSupplierApi(data).then((res) => {
        if (res.code === 200) {
          // this.loadData({
          //   pageNumber: this.localPagination.page,
          //   pageSize: this.localPagination.pageSize,
          // });
          this.$refs.ProTable.refresh();
        }
      });
    },
    handleChange(item) {
      this.queryParam.productTreeId = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productTreeId.push(id);
      });
    },
    handleChangeGoodsType(item) {
      this.queryParam.type = item[item.length - 1];
    },
  },
};
</script>

<style lang="scss" scoped>
.supplySource {
}

.fixed-width {
  display: flex;
  flex-wrap: wrap;
}

.el-button--info {
  color: black;
}
</style>
