<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <div v-if="type === 'item'" class="title-box-right">
          <div>总销售数量:{{ totalData?.number || 0 }}</div>
          <div>总退货数量:{{ totalData?.returnNum || 0 }}</div>
          <div>总销售金额:{{ totalData?.totalAmount || 0 }}</div>
          <div>总退货金额:{{ totalData?.returnAmount || 0 }}</div>
          <div>总实收金额:{{ totalData?.actureAmount || 0 }}</div>
        </div>
      </template>
      <template #searchType>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.type"
          filterable
          :options="options1"
          style="width: 100%"
          :props="{
            label: 'label',
            value: 'value',
            children: 'children',
            expandTrigger: 'click',
          }"
          clearable
          leaf-only
          @change="handleChangeType"
        ></el-cascader>
      </template>
      <template #machine="{ row }">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in row.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-button slot="reference" size="mini">适用机型</el-button>
        </el-popover>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import {
  getAreaSalesStatApi,
  getItemSalesStatApi,
  getMachineSalesStatApi,
  getModelSalesStatApi,
} from "@/api/order";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";
import { productAllApi } from "@/api/dispose";

export default {
  name: "ItemsStat",
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "item",
    },
  },
  data() {
    return {
      productIdName: "",
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      options1: [],
      options: [],
      includeList: [],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
    dictTreeByCodeApi(2100).then((res) => {
      res.data.forEach((item) => {
        if (item.value === "2101") {
          this.includeList.push(item.value);
          item.children.forEach((i) => {
            this.includeList.push(i.value);
          });
        }
      });
      this.options1 = res.data;
    });
    this.getProductThird();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );

      const result = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.currMonth, // 月份
        },
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt, // 销售时间
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.currMonth;
      delete requestParameters.createdAt;
      const editApi = this.getMethodApi(this.type);
      editApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      if (this.type === "item") {
        this.getTotalData(requestParameters);
      }
    },
    handleChangeType(val) {
      this.$set(this.queryParam, "type", val[val.length - 1]);
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    handleSelect(arr) {
      this.queryParam.productIds = arr.map((item) => item[item.length - 1]);
    },
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    getTotalData(params) {
      getMachineSalesStatApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    getMethodApi(type) {
      switch (type) {
        case "item":
          return getItemSalesStatApi;
        case "machine":
          return getModelSalesStatApi;
        case "area":
          return getAreaSalesStatApi;
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
      // this.$refs.ProTable.listLoading = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
