<!--
 * @Author: wskg
 * @Date: 2024-08-15 19:47:18
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:20
 * @Description: 拜访记录
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="拜访查询" name="拜访查询" lazy>
        <CallOn :columns="detailColumns" type="detail" />
      </el-tab-pane>
      <el-tab-pane label="拜访统计" name="拜访统计" lazy>
        <CallOn :columns="startColumns" type="stat" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import CallOn from "@/views/engineer/components/callOn.vue";
import { dictTreeByCodeApi } from "@/api/user";
import { getCustomerUserListApi } from "@/api/customer";
export default {
  name: "Visiting",
  components: { CallOn },
  data() {
    return {
      activeName: "拜访查询",
      detailColumns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "seqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "reachShopName",
          title: "受访人名字",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "reachShopRole",
          title: "受访角色",
          isTable: true,
          formatter: (row) => row.reachShopRole?.label,
        },
        {
          dataIndex: "reachShopTel",
          title: "受访人电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "callTypes",
          title: "拜访方式",
          isTable: true,
          formatter: (row) => row.callType?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(3100),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "callGoals",
          title: "拜访目的",
          isTable: true,
          formatter: (row) => row.callGoal?.label,
          isSearch: true,
          // 多选
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(2400),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "remark",
          title: "拜访内容",
          isTable: true,
          minWidth: 250,
        },
        {
          dataIndex: "nextNoticeRemark",
          title: "下次注意事项",
          isTable: true,
          minWidth: 200,
        },
        {
          dataIndex: "callState",
          title: "跟进状态",
          isTable: true,
          formatter: (row) => row.callState?.label,
        },
        // {
        //   dataIndex: "company",
        //   title: "归属公司",
        //   isTable: true,
        // },
        {
          dataIndex: "optUserName",
          title: "拜访人",
          isTable: true,
        },
        {
          dataIndex: "operatIds",
          title: "拜访人",
          isTable: true,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
        },

        {
          dataIndex: "reachShopTime",
          title: "拜访时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          fixed: "right",
          width: 200,
        },
      ],
      startColumns: [
        // {
        //   dataIndex: "",
        //   title: "归属公司",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          dataIndex: "userName",
          title: "拜访人",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "yearMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "custCount",
          title: "拜访客户数",
          isTable: true,
        },
        {
          dataIndex: "signCount",
          title: "签约客户数",
          isTable: true,
        },
      ],
      userTureList: [],
      userList: [],
    };
  },
  mounted() {
    this.getUser();
  },
  methods: {
    getUser() {
      getCustomerUserListApi().then((res) => {
        this.userTureList = res.data;
        res.data.map((item) => {
          this.userList.push({
            value: item.id,
            label: item.name,
          });
        });
        // this.columns[8].option = this.userList;
        this.detailColumns.forEach((item) => {
          if (item.dataIndex === "operatIds") {
            console.log(this.userTureList);

            item.option = this.userList;
          }
        });
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
