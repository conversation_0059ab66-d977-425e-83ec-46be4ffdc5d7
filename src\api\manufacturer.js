/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-09 18:16:16
 * @Description:
 */

/**
 * 制造商管理模块
 */
import { get, post, put, del } from "@/utils/request";
//分页查询
export const manufacturerListApi = (data) => get(`/manufacturer/page`, data);
//新增
export const manufacturerAddApi = (data) => post(`/manufacturer`, data);
//修改
export const manufacturerEditApi = (data) => put(`/manufacturer`, data);
//删除
export const manufacturerDelApi = (id) => del(`/manufacturer/${id}`);
//详情
export const manufacturerInfoApi = (id) => get(`/manufacturer/detail/${id}`);

export const goodsAudit = (data) =>
  post(`/storage-in-warehouse/goodsAudit`, data);

export const goodsLook = (id) =>
  get(`/storage-in-warehouse/goodsInWareDetail/${id}`);

// ===================  订单  ====================
// 订单分页查询
export const pageProviderApi = (data) => post("/manufacterOrder/page", data);
// 详情
export const getPurchaseDetailApi = (id) => get(`/manufacterOrder/${id}`);
// 审核
export const auditPurchaseApi = (data) => put("/manufacterOrder/audit", data);
// 关闭订单
export const closePurchaseApi = (id) => del(`/manufacterOrder/${id}`);
// 根据采购单号查明细
export const getPurchaseDetailGoodsApi = (code) =>
  get(`/manufacterOrder/getByCode/${code}`);

// ==================  发货单 =====================
export const pageDeliveryApi = (data) => post("/manufacterDelivery/page", data);
// 详情
export const getDeliveryDetailApi = (id) => get(`/manufacterDelivery/${id}`);
// 发货记录表
export const getDeliveryRecordApi = (id) =>
  get(`/manufacterDelivery/deliveryRecord/${id}`);
// 提交
export const submitDeliveryApi = (data) =>
  post("/manufacterDelivery/delivery", data);

// 更新发货数量
export const updateDeliveryNumApi = (data) =>
  put("/manufacterDelivery/updateDeliveryRecord", data);

// ===========================  供应商收货单  ===========================
// 分页查询
export const pageReceiveApi = (data) => post("/manufacterReceive/page", data);
// 详情
export const getReceiveDetailApi = (id) => get(`/manufacterReceive/${id}`);
// 确认收货
export const confirmReceiveApi = (data) =>
  post("/manufacterReceive/receive", data);

// ===========================  供应商、采购退货单  ===========================
// 分页查询
export const pageReturnApi = (data) => post("/manufacterReturn/page", data);
// 申请退货
export const applyReturnApi = (data) => post("/manufacterReturn", data);
// 供应商获取退货详情
export const getReturnDetailApi = (id) => get(`/manufacterReturn/${id}`);

// 采购退货单列表
export const purchaseReturnListApi = (data) =>
  post("/manufacterReturn/page", data);
// 采购退货详情
export const getReturnDetailInfoApi = (id) =>
  get(`/manufacterReturn/returnInfo/${id}`);
// 登记退货
export const registerReturnApi = (data) =>
  post("/manufacterReturn/returnBack", data);
// 供应商退货单列表
export const supplierReturnListApi = (data) =>
  post("/manufacterReturn/manufacturer/page", data);
// 供应商确认退货
export const providerConfirmReturnApi = (data) =>
  post("/manufacterReturn/return", data);
// 供应商确认收货
export const providerConfirmReceiveApi = (id) =>
  post(`/manufacterReturn/receive/${id}`);
// 供应商退款
export const providerRefundApi = (data) =>
  post("/manufacterReturn/refund", data);
// 退货明细列表
export const returnDetailListApi = (data) =>
  post("/manufacterReturn/detailPage", data);
// 退货统计
export const returnDetailTotalApi = (data) =>
  post("/manufacterReturn/summary", data);

// ====================================  供应商销售统计  ==========================
// 按月统计
export const getSupplierMonthStatisticsApi = (data) =>
  post("/manufacterOrder/monthlyPage", data);
// 按机型统计
export const getSupplierModelStatisticsApi = (data) =>
  post("/manufacterOrder/mechinePage", data);
// 按物品统计
export const getSupplierSupplierStatisticsApi = (data) =>
  post("/manufacterOrder/articlePage", data);
// 按客户统计
export const getSupplierCustomerStatisticsApi = (data) =>
  post("/manufacterOrder/companyPage", data);
// 销售合计
export const getSupplierTotalStatisticsApi = (data) =>
  post("/manufacterOrder/summary", data);
