<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-19 15:42:53
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 11:07:21
 * @Description: 工程师位置
 -->

<template>
  <div class="map-container">
    <div v-if="showFullscreen" class="fullscreen-btn" @click="toggleFullScreen">
      <i :class="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"></i>
    </div>
    <div class="filter-container">
      <div
        ref="engineerFilterRef"
        class="filter-icon"
        @click="toggleDropdown('engineer')"
      >
        <svg
          t="1747712022135"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="7409"
          width="20"
          height="20"
        >
          <path
            d="M855.342766 151.46262c-6.056949-11.851932-18.984377-19.393699-33.219589-19.393699L101.903901 132.068921c-14.360056 0-27.412326 7.704472-33.396621 19.744693-5.988388 12.015661-3.845585 26.157753 5.520737 36.192294l255.896134 274.308483 0 309.339324c0 12.847609 7.895831 24.602328 20.389376 30.328749l189.116195 86.432535c5.154393 2.371 10.771321 3.515057 16.33913 3.515057 6.541997 0 13.090133-1.607614 18.926048-4.797259 10.718109-5.945409 17.427928-16.503882 17.809621-28.037567l12.957103-396.767536 245.078765-274.90507C859.543438 177.316451 861.425298 163.313529 855.342766 151.46262zM520.773827 804.275693l-117.384477-53.647851L403.38935 483.628836l127.858016 0L520.773827 804.275693zM550.774095 416.986019c-1.963725-0.299829-3.761674-1.090844-5.809309-1.090844L383.519814 415.895175 181.938726 199.803605l562.427506 0L550.774095 416.986019zM685.454494 524.008498l273.392624 0 0 59.759035-273.392624 0 0-59.759035ZM685.454494 654.104485l273.392624 0 0 59.759035-273.392624 0 0-59.759035ZM685.454494 773.618463l273.392624 0 0 59.759035-273.392624 0 0-59.759035Z"
            fill="currentColor"
            p-id="7410"
          ></path>
        </svg>
        <span>工程师（{{ selectedEngineers.length }}）</span>
      </div>
      <div
        ref="customerFilterRef"
        class="filter-icon"
        @click="toggleDropdown('customer')"
      >
        <svg
          t="1747712022135"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="7409"
          width="20"
          height="20"
        >
          <path
            d="M855.342766 151.46262c-6.056949-11.851932-18.984377-19.393699-33.219589-19.393699L101.903901 132.068921c-14.360056 0-27.412326 7.704472-33.396621 19.744693-5.988388 12.015661-3.845585 26.157753 5.520737 36.192294l255.896134 274.308483 0 309.339324c0 12.847609 7.895831 24.602328 20.389376 30.328749l189.116195 86.432535c5.154393 2.371 10.771321 3.515057 16.33913 3.515057 6.541997 0 13.090133-1.607614 18.926048-4.797259 10.718109-5.945409 17.427928-16.503882 17.809621-28.037567l12.957103-396.767536 245.078765-274.90507C859.543438 177.316451 861.425298 163.313529 855.342766 151.46262zM520.773827 804.275693l-117.384477-53.647851L403.38935 483.628836l127.858016 0L520.773827 804.275693zM550.774095 416.986019c-1.963725-0.299829-3.761674-1.090844-5.809309-1.090844L383.519814 415.895175 181.938726 199.803605l562.427506 0L550.774095 416.986019zM685.454494 524.008498l273.392624 0 0 59.759035-273.392624 0 0-59.759035ZM685.454494 654.104485l273.392624 0 0 59.759035-273.392624 0 0-59.759035ZM685.454494 773.618463l273.392624 0 0 59.759035-273.392624 0 0-59.759035Z"
            fill="currentColor"
            p-id="7410"
          ></path>
        </svg>
        <span>报修客户（{{ selectedCustomers.length }}）</span>
      </div>
    </div>
    <!-- 工程师下拉面板 -->
    <div
      v-if="engineerDropdownVisible"
      class="filter-dropdown"
      :style="engineerDropdownStyle"
      @mousedown.stop
    >
      <div class="select-all-container">
        <el-checkbox
          v-model="selectAllEngineers"
          class="select-all-checkbox"
          @change="handleEngineerSelectAll"
        >
          全选/取消全选
        </el-checkbox>
      </div>
      <el-divider class="divider"></el-divider>
      <el-checkbox
        v-for="engineer in engineerList"
        :key="engineer.id"
        v-model="selectedEngineers"
        :label="engineer.id"
        class="engineer-checkbox"
        :disabled="!engineer.isOnline"
        @change="onEngineerFilterChange"
      >
        <div class="checkbox-content">
          <span>{{ engineer.name }}</span>
          <span
            :class="
              engineer.isOnline
                ? engineer.busy
                  ? 'status busy'
                  : 'status idle'
                : 'status offline'
            "
          >
            {{ engineer.isOnline ? (engineer.busy ? "忙碌" : "空闲") : "掉线" }}
          </span>
        </div>
      </el-checkbox>
    </div>
    <div
      v-if="customerDropdownVisible"
      class="filter-dropdown"
      :style="customerDropdownStyle"
      @mousedown.stop
    >
      <!-- 添加全选/取消全选按钮 -->
      <div class="select-all-container">
        <el-checkbox
          v-model="selectAllCustomers"
          class="select-all-checkbox"
          @change="handleCustomerSelectAll"
        >
          全选/取消全选
        </el-checkbox>
      </div>
      <el-divider class="divider"></el-divider>
      <el-checkbox
        v-for="customer in customerList"
        :key="customer.id"
        v-model="selectedCustomers"
        :label="customer.id"
        class="engineer-checkbox"
        @change="onCustomerFilterChange"
      >
        <div class="checkbox-content">
          <span>{{ customer?.name }}</span>
          <div class="status-container">
            <!-- 状态标签 -->
            <!--    
             :class="{
                'status busy': customer.status?.value === 'pending_orders',
                'status idle': customer.status?.value !== 'pending_orders',
              }"-->
            <span class="status" :class="statusClass(customer)">
              {{ customer.status?.label }}
            </span>
            <!-- 超时提示 -->
            <div class="timeout-indicator">
              <span
                v-if="computeOverdueStatus(customer).repairOverdue"
                class="warning"
              >
                （⚠️维修超时）
              </span>
              <span
                v-else-if="computeOverdueStatus(customer).overdue"
                class="warning"
              >
                （⚠️接单超时）
              </span>
            </div>
          </div>
        </div>
      </el-checkbox>
    </div>
    <div id="map" ref="map" class="map-content"></div>
    <!-- 维修工单详情 -->
    <WorkOrderDetail
      ref="workOrderDetail"
      title="工单详情"
      @refreshWorkOrder="refreshWorkOrder"
    />
    <!-- 查看工程师维修工单列表 -->
    <WorkOrderInfo ref="workOrderInfo" @refreshWorkOrder="refreshWorkOrder" />
  </div>
</template>

<script>
import Vue from "vue";
import MapInfoPane from "@/views/engineer/components/mapInfoPane.vue";
import WorkOrderDetail from "@/views/engineer/components/workOrderDetail.vue";
// import EngineerOrderDetail from "@/views/engineer/components/engineerOrderDetail.vue";
import WorkOrderInfo from "@/views/engineer/components/workOrderInfo.vue";
import { getEngineerLocationApi } from "@/api/repair";
import { dictMemberApi } from "@/api/user";

export default {
  name: "EngineerMap",
  components: { WorkOrderDetail, WorkOrderInfo },
  props: {
    workOrder: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      isFullscreen: false,
      showFullscreen: false,
      map: null,
      markerLayer: null,
      marker: null,
      labelLayer: null,
      mapData: {
        latitude: "30.657349",
        longitude: "104.065837",
        max_distance: "5000",
        poiname: "四川省成都市青羊区人民南路一段86号",
      },
      mapArray: [],
      infoWindow: null,
      mapLoading: false,
      refreshTimer: null,
      zoom: null,
      debouncedInit: null,
      // 工程师筛选相关
      engineerDropdownVisible: false,
      engineerDropdownStyle: {},
      allEngineers: [],
      engineerList: [],
      selectedEngineers: [],
      selectAllEngineers: true,

      // 客户筛选相关
      customerDropdownVisible: false,
      customerDropdownStyle: {},
      customerList: [],
      selectAllCustomers: true,
      selectedCustomers: [],
      // 信息窗口
      infoWindows: [],
      mouseDownTime: 0,
      mouseDownX: 0,
      mouseDownY: 0,
      isMapInitialized: false,
    };
  },
  watch: {
    workOrder: {
      handler(val) {
        try {
          if (Array.isArray(val) && val.length > 0) {
            this.processCustomerData();
            if (this.isMapInitialized) {
              this.init();
            }
          } else {
            this.customerList = [];
            this.selectedCustomers = [];
            this.selectAllCustomers = false;
          }
        } catch (error) {
          console.error("WorkOrder数据处理异常:", error);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // 加载腾讯地图脚本
    if (!document.getElementById("qq-map")) {
      const script = document.createElement("script");
      script.id = "qq-map";
      script.type = "text/javascript";
      script.src = `https://map.qq.com/api/gljs?v=1.exp&libraries=service&key=SXEBZ-CKGCU-5HFVN-GHNV7-VTJ5J-ADBQB`;
      document.head.appendChild(script);
    }
    // 添加防抖处理
    this.debouncedInit = this.debounce(this.init, 200);
  },

  mounted() {
    document.addEventListener("mousedown", this.handleMouseDown);
    document.addEventListener("mouseup", this.handleMouseUp);
    document.addEventListener("fullscreenchange", this.handleFullscreenChange);
    this.loadMap().then(() => {
      this.startLocationRefresh();
    });
    this.getEngineers();
  },
  beforeDestroy() {
    document.removeEventListener("mousedown", this.handleMouseDown);
    document.removeEventListener("mouseup", this.handleMouseUp);
    document.removeEventListener(
      "fullscreenchange",
      this.handleFullscreenChange
    );
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    // 销毁地图和标记
    if (this.map) {
      this.map.destroy();
      this.map = null;
    }

    if (this.marker) {
      this.marker.setMap(null);
      this.marker = null;
    }

    if (this.infoWindow) {
      this.infoWindow.close();
      this.infoWindow = null;
    }
  },
  methods: {
    startLocationRefresh() {
      this.getEngineerLocations();
      this.refreshTimer = setInterval(() => {
        this.getEngineerLocations();
      }, 180000); // 3分钟刷新一次
    },
    async loadMap() {
      this.mapLoading = true;
      // 地址转坐标
      const MAP_KEY = "N3MBZ-KSTEN-AUQFA-S5G3F-TVLPE-YXBB6";
      return this.$jsonp(
        `https://map.qq.com/api/gljs?v=1.exp&key=${MAP_KEY}&address=${this.mapData.poiname}`
      )
        .then((res) => {
          if (this.map) {
            this.map.destroy(); // 销毁地图
          }

          // 设置地图的中心点坐标
          const center = new TMap.LatLng(
            this.mapData.latitude,
            this.mapData.longitude
          );
          //初始化地图
          this.map = new TMap.Map("map", {
            center: center,
            zoom: 11,
          });
          // 地图比例尺改变事件
          this.map.on("zoom_changed", () => {
            this.zoom = this.map.getZoom();
            this.debouncedInit();
          });
          this.isMapInitialized = true;
          this.showFullscreen = true;
          this.init();
        })
        .finally(() => {
          this.mapLoading = false;
        });
    },

    // 初始化地图
    init() {
      if (this.marker) {
        this.marker.setMap(null);
        this.marker = null;
      }
      if (this.infoWindows) {
        this.infoWindows.forEach((window) => {
          if (window.windowData?.component) {
            window.windowData.component.$destroy();
          }
          window.close();
        });
        this.infoWindows = [];
      }

      // 当没有选中任何工程师时，不显示任何标记
      const filterEngineerMapArray = this.selectedEngineers.length
        ? this.mapArray.filter((item) =>
            this.selectedEngineers.includes(item.id)
          )
        : [];
      const filterCustomerMapArray = this.selectedCustomers.length
        ? this.workOrder.filter((item) =>
            this.selectedCustomers.includes(item.id)
          )
        : [];
      // 更新地图点
      const engineerMarkers = filterEngineerMapArray.map((item) => ({
        styleId: "marker",
        position: new TMap.LatLng(item.latitude, item.longitude),
        latitude: Number(item.latitude),
        longitude: Number(item.longitude),
        id: item.id,
        name: item.name,
        reportTime: item.reportTime,
        waitingWorkNum: item.workVo.waitingWorkNum, // 剩余工单数
        completedWorkNum: item.workVo.completedWorkNum, // 今日完成工单数
        // deliveryWorkNum: item.deliveryWorkNum, // 指派工单数
        todayWorkNum: item.workVo.todayWorkNum, // 总工单数
        type: "engineer",
      }));
      const customerMarkers = filterCustomerMapArray.map((item) => ({
        styleId: "marker",
        position: new TMap.LatLng(
          item?.location?.latitude,
          item?.location?.longitude
        ),
        latitude: Number(item?.location?.latitude),
        longitude: Number(item?.location?.longitude),
        id: item.id,
        code: item.code,
        name: item.customerName,
        status: item.status,
        createdAt: item.createdAt, // 下单时间
        orderReceiveTime: item.orderReceiveTime, // 工程师接单时间
        departureTime: item.departureTime, // 工程师出发时间
        actualArriveTime: item.actualArriveTime, // 工程师到达时间
        sendReportTime: item.sendReportTime, // 提交维修报告时间
        completedAt: item.completedAt, // 工单完成时间
        productInfo: item.productInfo,
        productId: item.productId,
        engineerId: item.engineerId,
        type: "customer",
      }));
      this.mapPointArray = [...engineerMarkers, ...customerMarkers];
      // 如果有选中的工程师，创建新的标记
      if (this.mapPointArray.length > 0) {
        // 创建标记层，使用原来的图标
        this.marker = new TMap.MultiMarker({
          map: this.map,
          styles: {
            marker: new TMap.MarkerStyle({
              width: 20,
              height: 30,
              anchor: { x: 10, y: 30 },
            }),
          },
          enableCollision: true,
          geometries: this.mapPointArray,
        });

        // 创建信息窗口
        this.infoWindows = [];
        this.mapPointArray.forEach((item) => {
          const infoWindow = this.createInfoWindow(item, this.map, this.zoom);
          this.infoWindows.push(infoWindow);
          infoWindow.open();
          infoWindow.setPosition(item.position);
        });
      }
    },

    /**
     * @description 创建位置信息窗口
     * @param data 信息数据
     * @param map 地图实例
     * @param zoom 地图缩放级别
     * @returns {TMap.InfoWindow|VNode|{mapArray, zoom, windowId: string}}
     */
    createInfoWindow(data, map, zoom) {
      const infoWindowId = `info-window-${data.id}`;

      // 创建唯一标识的容器
      const container = document.createElement("div");
      container.id = infoWindowId;
      container.className = "custom-info-window";
      container.dataset.windowId = infoWindowId; // 添加数据属性

      // 创建Vue组件
      const ComponentClass = Vue.extend({
        data() {
          return {
            mapArray: data,
            zoom: zoom,
            windowId: infoWindowId,
          };
        },
        render(h) {
          return h(MapInfoPane, {
            props: {
              infoData: this.mapArray,
              zoom: this.zoom,
            },
            attrs: {
              "data-window-id": this.windowId,
              "data-item-id": this.mapArray.id,
            },
          });
        },
      });

      // 挂载组件
      const component = new ComponentClass().$mount();
      container.appendChild(component.$el);

      // 创建信息窗口
      const infoWindow = new TMap.InfoWindow({
        map: map,
        position: data.position,
        offset: { x: 0, y: -32 },
        content: container.outerHTML,
        enableCustom: true,
      });

      // 保存数据引用
      infoWindow.windowData = {
        id: infoWindowId,
        component: component,
        originalData: data,
      };

      return infoWindow;
    },
    /**
     * @description 鼠标按下事件
     * @param e
     */
    handleMouseDown(e) {
      this.mouseDownTime = Date.now();
      this.mouseDownX = e.clientX;
      this.mouseDownY = e.clientY;
    },

    /**
     * @description 鼠标抬起事件
     * @param e
     */
    handleMouseUp(e) {
      const timeDiff = Date.now() - this.mouseDownTime;
      const distance = Math.sqrt(
        Math.pow(e.clientX - this.mouseDownX, 2) +
          Math.pow(e.clientY - this.mouseDownY, 2)
      );

      // 如果时间小于200ms且移动距离小于5px，则认为是点击而不是拖动
      if (timeDiff < 200 && distance < 5) {
        this.handleGlobalClick(e);
      }
    },
    /**
     * @description 全局点击事件,处理点击信息窗口事件
     * @param e
     */
    handleGlobalClick(e) {
      // 检查点击是否来自信息窗口
      const infoWindowElement = e.target.closest(".custom-info-window");
      if (!infoWindowElement) return;

      // 获取窗口ID和数据
      const windowId = infoWindowElement.dataset.windowId;
      if (!windowId) return;

      // 找到对应的infoWindow实例
      const infoWindow = this.infoWindows.find(
        (w) => w.windowData && w.windowData.id === windowId
      );
      if (!infoWindow) return;

      // 检查点击的具体元素
      const target = e.target;
      let clickType = "other";

      if (target.classList.contains("name")) {
        clickType = "name";
      } else if (target.classList.contains("status-tag")) {
        clickType = "status";
      }

      // 处理点击事件
      this.handleInfoWindowClick({
        clickType: clickType,
        data: infoWindow.windowData.originalData,
        originalEvent: e,
      });
    },
    /**
     * 点击信息窗口
     * @param clickType 类型id：用于区分点击的是名称还是状态
     * @param data 数据
     */
    handleInfoWindowClick({ clickType, data }) {
      const { type, code } = data;
      // type: customer  客户； engineer 工程师
      if (type === "customer") {
        this.$refs.workOrderDetail.show(code);
      } else if (type === "engineer") {
        // 查看工程师工单列表
        this.$refs.workOrderInfo.show(data);
      }
    },
    // 工程师全选
    handleEngineerSelectAll(val) {
      const onlineEngineerIds = this.engineerList
        .filter((e) => e.isOnline)
        .map((e) => e.id);

      this.selectedEngineers = val ? [...onlineEngineerIds] : [];
      this.selectAllEngineers = val;
      this.onEngineerFilterChange();
      // 恢复默认视图
      this.setDefaultMapCenter();
    },
    // 客户全选
    handleCustomerSelectAll(val) {
      this.selectedCustomers = val ? this.customerList.map((c) => c.id) : [];
      this.selectAllCustomers = val;
      this.onCustomerFilterChange();
      // 恢复默认视图
      this.setDefaultMapCenter();
    },
    // 选择工程师
    onEngineerFilterChange() {
      // 更新全选状态
      const onlineEngineerIds = this.engineerList
        .filter((e) => e.isOnline)
        .map((e) => e.id);

      this.selectAllEngineers =
        this.selectedEngineers.length === onlineEngineerIds.length;

      // 重新初始化地图
      this.init();

      // 获取最新选中的工程师（数组中的最后一个）
      const lastSelectedEngineer =
        this.selectedEngineers.length > 0
          ? this.mapArray.find(
              (item) =>
                item.id ===
                this.selectedEngineers[this.selectedEngineers.length - 1]
            )
          : null;

      if (lastSelectedEngineer) {
        // 将地图中心移动到最新选中的工程师位置
        const center = new TMap.LatLng(
          lastSelectedEngineer.latitude,
          lastSelectedEngineer.longitude
        );
        this.map.setCenter(center);
        this.map.setZoom(14);
      } else {
        // 恢复默认视图
        this.setDefaultMapCenter();
      }
    },
    // 选择客户
    onCustomerFilterChange() {
      this.selectAllCustomers =
        this.selectedCustomers.length === this.customerList.length;
      this.init();
      // 获取最新选中的工程师（数组中的最后一个）
      const lastSelectedEngineer =
        this.selectedCustomers.length > 0
          ? this.workOrder.find(
              (item) =>
                item.id ===
                this.selectedCustomers[this.selectedCustomers.length - 1]
            )
          : null;

      if (lastSelectedEngineer) {
        // 将地图中心移动到最新选中的工程师位置
        const center = new TMap.LatLng(
          lastSelectedEngineer.location.latitude,
          lastSelectedEngineer.location.longitude
        );
        this.map.setCenter(center);
        this.map.setZoom(14);
      } else {
        // 恢复默认视图
        this.setDefaultMapCenter();
      }
    },
    toggleDropdown(type) {
      if (type === "engineer") {
        this.engineerDropdownVisible = !this.engineerDropdownVisible;
        this.positionDropdown("engineer");
        this.setupOutsideClickListener("engineer");
      } else {
        this.customerDropdownVisible = !this.customerDropdownVisible;
        this.positionDropdown("customer");
        this.setupOutsideClickListener("customer");
      }
    },
    positionDropdown(type) {
      this.$nextTick(() => {
        const refName =
          type === "engineer" ? "engineerFilterRef" : "customerFilterRef";
        const dropdownStyle =
          type === "engineer"
            ? "engineerDropdownStyle"
            : "customerDropdownStyle";

        const icon = this.$refs[refName]?.$el || this.$refs[refName];
        if (icon) {
          this[dropdownStyle] = {
            position: "absolute",
            top: "45px",
            left: "0",
            zIndex: 9999,
            minWidth: "150px",
            maxHeight: "460px",
            overflowY: "auto",
          };
        }
      });
    },
    setupOutsideClickListener(type) {
      const handler = (e) => {
        const refName =
          type === "engineer" ? "engineerFilterRef" : "customerFilterRef";
        const visibleProp =
          type === "engineer"
            ? "engineerDropdownVisible"
            : "customerDropdownVisible";

        const element = this.$refs[refName]?.$el || this.$refs[refName];
        if (
          !element.contains(e.target) &&
          !e.target.closest(`.${type}-filter-dropdown`)
        ) {
          this[visibleProp] = false;
          document.removeEventListener("mousedown", handler);
        }
      };

      document.addEventListener("mousedown", handler);
    },
    processCustomerData() {
      if (!Array.isArray(this.workOrder)) return;

      //  生成客户列表
      const customers = this.workOrder.reduce((acc, order) => {
        if (order.customerName) {
          acc.push({
            id: order.id,
            name: order.customerName,
            status: order.status || {},
            productId: order.productId,
            engineerId: order.engineerId,
            createdAt: order.createdAt,
            orderReceiveTime: order.orderReceiveTime, // 工程师接单时间
            departureTime: order.departureTime, // 工程师出发时间
            actualArriveTime: order.actualArriveTime, // 工程师到达时间
            sendReportTime: order.sendReportTime, // 提交维修报告时间
            completedAt: order.completedAt, // 工单完成时间
          });
        }
        return acc;
      }, []);

      // 首次加载时默认全选
      const oldSelected = [...this.selectedCustomers];

      this.customerList = customers;
      if (oldSelected.length === 0) {
        this.selectedCustomers = this.customerList.map((c) => c.id);
      } else {
        // 保留仍然存在的客户ID
        this.selectedCustomers = oldSelected.filter((id) =>
          this.customerList.some((c) => c.id === id)
        );
      }

      // 更新全选状态
      this.selectAllCustomers =
        this.selectedCustomers.length === this.customerList.length;
      if (this.isMapInitialized) {
        this.init();
      }
    },
    refreshWorkOrder() {
      this.$emit("refreshWorkOrder");
    },
    // 获取所有工程师列表
    getEngineers() {
      dictMemberApi("1002", {
        pageNumber: 1,
        pageSize: 1000,
      }).then((res) => {
        this.allEngineers = res.data.rows.map((item) => ({
          name: item.name,
          id: item.id,
          isOnline: false, // 初始为掉线
          busy: 0,
        }));
        this.engineerList = [...this.allEngineers];
      });
    },
    /**
     * @description 获取所有工程师的位置信息；筛选在线工程师；更新地图数据
     */
    getEngineerLocations() {
      getEngineerLocationApi()
        .then((res) => {
          if (!(res.data && res.data.length)) {
            this.selectAllEngineers = false;
            return;
          }
          this.mapArray = res.data;
          // 生成工程师列表（带在线状态）
          this.engineerList = this.allEngineers.map((engineer) => {
            const location = this.mapArray.find(
              (item) => item.name === engineer.name
            );
            return {
              ...engineer,
              isOnline: !!location,
              busy: location?.workVo?.waitingWorkNum
                ? location?.workVo?.waitingWorkNum
                : 0,
            };
          });

          // 在线工程师排在最前
          this.engineerList.sort((a, b) =>
            a.isOnline && !b.isOnline ? -1 : 1
          );

          // 首次加载时默认全选在线工程师
          if (this.selectedEngineers.length === 0) {
            this.selectedEngineers = this.engineerList
              .filter((e) => e.isOnline)
              .map((e) => e.id);
          }

          // 保留仍然在线的工程师ID
          this.selectedEngineers = this.selectedEngineers.filter((id) =>
            this.engineerList.some((e) => e.id === id && e.isOnline)
          );
          // 更新全选状态
          const onlineEngineerIds = this.engineerList
            .filter((e) => e.isOnline)
            .map((e) => e.id);

          this.selectAllEngineers =
            this.selectedEngineers.length === onlineEngineerIds.length;
          this.init();
        })
        .catch(() => {
          this.$message.error("获取工程师位置信息失败");
          this.selectedEngineers = [];
          // this.engineerList = []
          this.selectAllEngineers = false;
          this.init();
        });
    },
    debounce(func, wait) {
      let timeout;
      return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
          func.apply(context, args);
        }, wait);
      };
    },
    computeOverdueStatus(item) {
      if (!item || typeof item !== "object") {
        return { overdue: false, repairOverdue: false };
      }

      return {
        overdue: this.isOverdue(item),
        repairOverdue: this.isRepairOverdue(item),
      };
    },
    statusClass(item) {
      const value = item?.status?.value;
      switch (value) {
        case "pending_orders":
          return "status-orange";
        case "engineer_arrive":
          return "status-green";
        case "engineer_receive":
          return "status-blue";
        case "completed":
          return "status-gray";
        case "wait_confirmed_report":
          return "status-orange";
        case "engineer_departure":
          return "status-purple";
        default:
          return "status-green";
      }
    },
    // 报修超时：报修 -> 工程师到达超过4小时算超时
    isOverdue(item) {
      if (!item || typeof item !== "object" || !("createdAt" in item)) {
        return false;
      }

      const parseDate = (str) => {
        if (!str) return null;
        return new Date(
          str.replace("年", "-").replace("月", "-").replace("日", "")
        );
      };

      const start = parseDate(item.createdAt);
      const end = item.actualArriveTime
        ? parseDate(item.actualArriveTime)
        : new Date();

      // 验证日期有效性
      if (!(start instanceof Date) || isNaN(start.getTime())) return false;
      if (!(end instanceof Date) || isNaN(end.getTime())) return false;

      return this.getEffectiveHours(start, end) > 4;
    },
    // 维修超时：到达 -> 提交报告时间超过4小时算超时
    isRepairOverdue(item) {
      if (!item || typeof item !== "object" || !("actualArriveTime" in item)) {
        return false;
      }
      const parseDate = (str) => {
        if (!str) return null;
        return new Date(
          str.replace("年", "-").replace("月", "-").replace("日", "")
        );
      };

      const start = parseDate(item.actualArriveTime);
      const end = item.sendReportTime
        ? parseDate(item.sendReportTime)
        : new Date();

      // 验证日期有效性
      if (!(start instanceof Date) || isNaN(start.getTime())) return false;
      if (!(end instanceof Date) || isNaN(end.getTime())) return false;
      return this.getEffectiveHours(start, end) > 4;
    },
    // 计算有效工作时间（仅工作日 8:30~18:00）
    getEffectiveHours(start, end) {
      if (
        !(start instanceof Date) ||
        !(end instanceof Date) ||
        isNaN(start) ||
        isNaN(end)
      ) {
        console.error("无效时间参数");
        return 0;
      }

      const WORK_START_HOUR = 8;
      const WORK_START_MIN = 30;
      const WORK_END_HOUR = 18;
      let totalHours = 0;

      const current = new Date(start);

      while (current < end) {
        const day = current.getDay();

        // 排除周日（可加条件 day !== 6 排除周六）
        if (day !== 0) {
          const workStart = new Date(current);
          workStart.setHours(WORK_START_HOUR, WORK_START_MIN, 0, 0);

          const workEnd = new Date(current);
          workEnd.setHours(WORK_END_HOUR, 0, 0, 0);

          const effectiveStart = Math.max(
            current.getTime(),
            workStart.getTime()
          );
          const effectiveEnd = Math.min(end.getTime(), workEnd.getTime());

          if (effectiveStart < effectiveEnd) {
            totalHours += (effectiveEnd - effectiveStart) / 36e5; // 1000*60*60
          }
        }
        // 下一天 00:00
        current.setDate(current.getDate() + 1);
        current.setHours(0, 0, 0, 0);
      }
      return totalHours;
    },
    // 恢复地图默认视图
    setDefaultMapCenter() {
      const defaultCenter = new TMap.LatLng(
        this.mapData.latitude,
        this.mapData.longitude
      );
      this.map.setCenter(defaultCenter);
      this.map.setZoom(11);
    },
    handleFullscreenChange() {
      this.isFullscreen = !!document.fullscreenElement;
    },
    toggleFullScreen() {
      const container = document.querySelector(".map-container");
      if (!document.fullscreenElement) {
        container.requestFullscreen().catch((err) => {
          console.error(
            `Error attempting to enable fullscreen: ${err.message}`
          );
        });
        this.isFullscreen = true;
        document.body.classList.add("fullscreen");
      } else {
        document.exitFullscreen();
        this.isFullscreen = false;
        document.body.classList.remove("fullscreen");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  .fullscreen-btn {
    position: absolute;
    top: 175px;
    right: 30px;
    z-index: 10000;
    cursor: pointer;
    padding: 9px;
    background: rgba(255, 255, 255, 1);
    border-top: 1px solid #dee2e8;
    border-radius: 0 0 4px 4px;
    transition: all 0.3s ease;
    i {
      font-size: 22px;
      color: #000;
    }

    //&:hover {
    //  background: rgba(255, 255, 255, 0.3);
    //}
  }
  .filter-container {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 99999;
    display: flex;
    gap: 10px;
    .filter-icon {
      cursor: pointer;
      display: inline-flex;
      background-color: rgba(255, 255, 255, 0.8);
      align-items: center;
      justify-content: center;
      padding: 8px;
      border-radius: 4px;
      transition: background-color 0.3s;
      color: #515151;
      svg {
        fill: currentColor;
        transition: color 0.3s ease;
      }

      span {
        font-size: 14px;
        color: #333;
        margin-left: 5px;
        transition: color 0.3s ease;
      }
    }
    .filter-icon:hover {
      background-color: #f0f0f0;
      span,
      svg {
        color: #1890ff;
      }
    }
  }

  .filter-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 4px;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 6px;

    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-width: 160px;
    max-height: 460px;
    overflow-y: auto;
    z-index: 99999;
  }
  .select-all-container {
    padding: 14px 18px;
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 99;
  }

  .select-all-checkbox {
    width: 100%;
  }

  .divider {
    margin-top: 0;
    margin-bottom: 8px;
  }
}

.map-content {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  pointer-events: auto;

  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center center;
  user-select: none;
}

::v-deep.engineer-checkbox {
  border-radius: 12px;
  padding: 7px 18px;
  margin: 4px 0;
  display: flex;
  align-items: center;
}
::v-deep.engineer-checkbox .el-checkbox__label {
  flex: 1 !important;
}
.checkbox-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.status {
  //margin-left: 32px;
  font-weight: bold;
}

.status.busy {
  color: #fa8c16;
}

.status.idle {
  color: #52c41a;
}
.status.offline {
  color: #f56c6c;
}

.status-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
  margin-left: 32px;
}
// 状态颜色
.status-green {
  background-color: #f6ffed;
  color: #52c41a;
}
.status-blue {
  background-color: #e6f7ff;
  color: #1890ff;
}
.status-gray {
  background-color: #f5f5f5;
  color: #8c8c8c;
}
.status-orange {
  background-color: #fff7e6;
  color: #fa8c16;
}
.status-purple {
  background-color: #f9f0ff;
  color: #722ed1;
}
.bgc {
  background-color: transparent;
}

.timeout-indicator {
  margin-top: 4px;
  font-size: 12px;
  line-height: 1;
  span {
    color: #fa8c16;
  }
}
.engineer-checkbox:hover {
  background-color: #f5f7fa;
  cursor: pointer;
  border-radius: 5px;
}
</style>
