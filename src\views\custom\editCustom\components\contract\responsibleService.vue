<!--
 * @Author: wskg
 * @Date: 2025-01-22 09:53:19
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-30 16:42:47
 * @Description: 包量服务
 -->
<template>
  <div class="responsible-contract">
    <el-form
      ref="formRef"
      :model="infoData"
      label-width="140px"
      :rules="formRules"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-col :span="6">
            <el-form-item label="包量类型：" prop="packageType">
              <el-select
                v-model="infoData.packageType"
                :disabled="editType === 'info'"
                clearable
              >
                <el-option
                  v-for="item in [
                    {
                      label: '黑白印量',
                      value: 'BW',
                    },
                    {
                      label: '彩色印量',
                      value: 'COLOR',
                    },
                    {
                      label: '总印量',
                      value: 'TOTAL',
                    },
                  ]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.packageType === 'TOTAL'" :span="6">
            <el-form-item label="包量总印量：" prop="packageNumber">
              <el-input
                v-model="infoData.packageNumber"
                type="number"
                :min="0"
                placeholder="请输入包量总印量"
                :disabled="editType === 'info'"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.packageType === 'BW'" :span="6">
            <el-form-item label="黑白包量印量：" prop="packageBwNumber">
              <el-input
                v-model="infoData.packageBwNumber"
                type="number"
                :min="0"
                placeholder="请输入黑白包量印量"
                :disabled="editType === 'info'"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.packageType === 'COLOR'" :span="6">
            <el-form-item label="彩色包量印量：" prop="packageColorNumber">
              <el-input
                v-model="infoData.packageColorNumber"
                type="number"
                :min="0"
                placeholder="请输入包量总印量"
                :disabled="editType === 'info'"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col
            v-if="
              infoData.packageType === 'BW' || infoData.packageType === 'TOTAL'
            "
            :span="6"
          >
            <el-form-item label="黑色包量单价：" prop="packageBwPrice">
              <el-input
                v-model="infoData.packageBwPrice"
                type="number"
                :min="0"
                placeholder="请输入包量总印量"
                :disabled="editType === 'info'"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col
            v-if="
              infoData.packageType === 'COLOR' ||
              infoData.packageType === 'TOTAL'
            "
            :span="6"
          >
            <el-form-item label="彩色包量单价：" prop="packageColorPrice">
              <el-input
                v-model="infoData.packageColorPrice"
                type="number"
                :min="0"
                placeholder="请输入彩色包量单价"
                :disabled="editType === 'info'"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="已使用印量：" prop="packageUse">
              <el-input
                v-model="infoData.packageUse"
                type="number"
                :min="0"
                placeholder="请输入已使用印量"
                :disabled="editType === 'info'"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="包量截止日期：" prop="packageExpireDate">
              <el-date-picker
                v-model="infoData.packageExpireDate"
                style="width: 100%"
                type="date"
                placeholder="选择包量截止日期"
                value-format="yyyy-MM-dd"
                :disabled="editType === 'info'"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结算状态：" prop="serveSettleStatus">
              <el-select
                v-model="infoData.serveSettleStatus"
                :disabled="editType === 'info'"
              >
                <el-option
                  v-for="item in [
                    {
                      label: '未结清',
                      value: '0',
                    },
                    {
                      label: '已结清',
                      value: '1',
                    },
                  ]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结算方式：" prop="serveSettleMethod">
              <el-select
                v-model="infoData.serveSettleMethod"
                :disabled="editType === 'info'"
              >
                <el-option
                  v-for="item in [
                    {
                      label: '全款',
                      value: 'FULL',
                    },
                    {
                      label: '预付+尾款',
                      value: 'ADVANCE',
                    },
                    {
                      label: '分期付款',
                      value: 'INSTALLMENT',
                    },
                  ]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="包量合同金额：" prop="packageAmount">
              <el-input
                v-model="infoData.packageAmount"
                type="number"
                :min="0"
                placeholder="请输入包量合同金额"
                :disabled="editType === 'info'"
                @change="handlePackageAmountChange"
              >
                <template #suffix> 元 </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.serveSettleMethod !== 'FULL'" :span="6">
            <el-form-item label="包量预付金额：" prop="packagePreAmount">
              <el-input
                v-model="infoData.packagePreAmount"
                type="number"
                :min="0"
                placeholder="请输入包量预付金额"
                :disabled="editType === 'info'"
                @change="handlePackagePreAmountChange"
              >
                <template #suffix> 元 </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="infoData.serveSettleMethod !== 'FULL'" :span="6">
            <el-form-item label="包量尾款：" prop="serveArrersAmount">
              <el-input
                v-model="infoData.serveArrersAmount"
                type="number"
                :min="0"
                placeholder="请输入包量尾款"
                :disabled="true"
              >
                <template #suffix> 元 </template>
              </el-input>
            </el-form-item>
          </el-col>
          <!--<el-col :span="6">-->
          <!--  <el-form-item label="预付印量：" prop="packagePrint">-->
          <!--    <el-input-->
          <!--      v-model="infoData.packagePrint"-->
          <!--      type="number"-->
          <!--      :min="0"-->
          <!--      placeholder="请输入预付印量"-->
          <!--      :disabled="editType === 'info'"-->
          <!--    >-->
          <!--    </el-input>-->
          <!--  </el-form-item>-->
          <!--</el-col>-->

          <el-col :span="24">
            <!-- 分期付款 -->
            <InstallmentPayment
              v-if="infoData.serveSettleMethod === 'INSTALLMENT'"
              v-model="infoData"
              :edit-type="editType"
              installment-field="serverInstallments"
              force-stop-buy-field="forceStopServer"
            />
          </el-col>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import InstallmentPayment from "@/views/custom/editCustom/components/contract/installmentPayment.vue";
import { subtractAmount } from "@/utils";
import { validatePositiveNumber } from "@/utils/validate";
export default {
  name: "ResponsibleService",
  components: { InstallmentPayment },
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    editType: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      dialogVisible: false,
      formRules: {
        packageType: [
          {
            required: true,
            message: "请选择包类型",
            trigger: "change",
          },
        ],
        packageBwNumber: [
          {
            required: true,
            message: "请输入黑白包量印量",
            trigger: "blur",
          },
          {
            validator: validatePositiveNumber,
            message: "印量不能小于0",
            trigger: "blur",
          },
        ],
        packageNumber: [
          {
            required: true,
            message: "请输入黑白包量印量",
            trigger: "blur",
          },
          {
            validator: validatePositiveNumber,
            message: "印量不能小于0",
            trigger: "blur",
          },
        ],
        packageColorNumber: [
          {
            required: true,
            message: "请输入彩色包量印量",
            trigger: "blur",
          },
          {
            validator: validatePositiveNumber,
            message: "印量不能小于0",
            trigger: "blur",
          },
        ],
        packageBwPrice: [
          {
            required: true,
            message: "请输入黑白印量单价",
            trigger: "blur",
          },
          {
            validator: validatePositiveNumber,
            message: "单价不能小于0",
            trigger: "blur",
          },
        ],
        packageColorPrice: [
          {
            required: true,
            message: "请输入彩色印量单价",
            trigger: "blur",
          },
          {
            validator: validatePositiveNumber,
            message: "单价不能小于0",
            trigger: "blur",
          },
        ],
        packageAmount: [
          {
            required: true,
            message: "请输入合同金额",
            trigger: "blur",
          },
        ],
        packagePreAmount: [
          {
            required: true,
            message: "请输入预付金额",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (+value > +this.infoData.packageAmount) {
                callback(new Error("预付金额不能大于合同金额"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        // packagePrint: [
        //   {
        //     required: true,
        //     message: "请输入预付印量",
        //     trigger: "blur",
        //   },
        // ],
        packageUse: [
          {
            required: true,
            message: "请输入已使用印量",
            trigger: "blur",
          },
          {
            validator: validatePositiveNumber,
            message: "印量不能小于0",
            trigger: "blur",
          },
        ],
        serveSettleStatus: [
          {
            required: true,
            message: "请选择结算状态",
            trigger: "change",
          },
        ],
        serveSettleMethod: [
          {
            required: true,
            message: "请选择结算方式",
            trigger: "change",
          },
        ],
      },
    };
  },
  computed: {
    infoData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    "infoData.packageAmount": {
      handler(val) {
        this.calculateArrearsAmount();
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    const defaultValues = {
      signBlackWhiteCounter: null,
      signColoursCounter: null,
      signFiveColoursCounter: null,
      wasteType: "ACTUAL", // 废张计算方式
      bwWasteNumber: null,
      colorWasteNumber: null,
      bwWasteScale: null,
      colorWasteScale: null,
      blackWhitePrice: null,
      colorPrice: null,
      fiveWasteNumber: null,
      fiveWasteScale: null,
      fiveColourPrice: null,
      cycleType: "MONTH", // 付款周期
      statementDate: 26, // 每月账单生成时间
      payExpireDate: 7, // 每月付款截止时间
      forceStopServer: false, // 是否强制停保
      autoRenewal: false, // 是否自动续约
      guaranteeType: "NONE", // 保底类型
      hasGive: false, // 是否赠送
      priceType: "FIXED", // 价格类型
      mergeType: "NONE",
      paperType: "A4",
      packageAmount: null,
      packageUse: 0,
      packagePreAmount: null,
      serveArrersAmount: null,
      packageType: "TOTAL", // 包量类型
      packageBwNumber: null, // 黑白包量印量
      packageColorNumber: null, // 彩色包量印量
      packageNumber: null, // 包量印量
      packageBwPrice: null, // 黑白包量价格
      packageColorPrice: null, // 彩色包量价格
      packageExpireDate: null, // 包量截止日期
    };
    this.$emit("input", { ...defaultValues, ...this.value });
  },
  mounted() {},
  methods: {
    handlePackageAmountChange() {
      this.calculateArrearsAmount();
    },
    handlePackagePreAmountChange() {
      this.calculateArrearsAmount();
    },
    calculateArrearsAmount() {
      const fullAmount = this.infoData.packageAmount || 0;
      const depositAmount = this.infoData.packagePreAmount || 0;
      if (fullAmount) {
        let serveArrersAmount = fullAmount;
        if (depositAmount) {
          serveArrersAmount = subtractAmount(serveArrersAmount, depositAmount);
        }
        this.infoData.serveArrersAmount = Math.max(serveArrersAmount, 0);
      } else {
        this.infoData.serveArrersAmount = 0;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.responsible-contract {
  width: 100%;
  display: inline-block;
  //margin-bottom: 16px;
}
</style>
