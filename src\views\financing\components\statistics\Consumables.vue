<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-09 13:59:17
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-30 15:15:41
 * @Description: 耗材应收汇总
 -->
<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      show-selection
      @handleSelectionChange="handleSelectionChange"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="primary"
          icon="el-icon-edit"
          size="mini"
          @click="handleBulkEdit"
        >
          账款批量核销
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-check"
          @click="clearSelection"
        >
          取消全选
        </el-button>
        <el-button
          v-auth="['@ums:manage:finance:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>应收总金额：{{ totalData?.totalAmount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'info')"
          >
            详情
          </el-button>
          <el-button icon="el-icon-edit" @click="handleEdit(row, 'audit')">
            账款核销
          </el-button>
        </div>
      </template>
    </ProTable>

    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      :confirm-text="'确认核销'"
      :confirm-btn-loading="formLoading"
      width="30%"
      top="5%"
      @ok="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParams"
        :form-list="formColumns"
        :open-type="'edit'"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        @proSubmit="proSubmit"
      >
        <template #voucherImg>
          <ProUpload
            :file-list="formParams.voucherImg"
            :type="'edit'"
            :limit="3"
            style="padding-left: 0"
            @uploadSuccess="handleUploadSuccess"
            @uploadRemove="handleUploadRemove"
          />
        </template>
      </ProForm>
    </ProDialog>
    <SaleOrderDetail ref="saleOrderDetail" />
    <WorkOrderDetail ref="workOrderDetail" />
    <BulkEdit
      ref="bulkEdit"
      :columns="bulkEditColumns"
      type="consumable"
      @refresh="refresh(), clearSelection()"
    />
  </div>
</template>

<script>
import SaleOrderDetail from "@/views/financing/components/saleOrderDetail.vue";
import WorkOrderDetail from "@/views/financing/components/workOrderDetail.vue";
import ProUpload from "@/components/ProUpload/index.vue";
import BulkEdit from "@/views/financing/components/BulkEdit.vue";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { handleExcelExport } from "@/utils/exportExcel";
import {
  receivableConsumableSummaryListApi,
  receivableBaseSummaryStatisticsApi,
  receivableConsumableSummaryExportApi,
  receivableWriteOffApi,
} from "@/api/finance";

export default {
  name: "Consumables",
  components: { SaleOrderDetail, ProUpload, BulkEdit, WorkOrderDetail },
  data() {
    const _this = this;
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "type",
          title: "订单类型",
          isTable: true,
          formatter: (row) => (row.type ? "维修工单" : "耗材销售"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "耗材销售",
              value: 0,
            },
            {
              label: "维修工单",
              value: 1,
            },
          ],
          minWidth: 100,
        },

        {
          dataIndex: "code",
          title: "订单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        // {
        //   dataIndex: "tradeOrderNum",
        //   title: "关联订单号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 150,
        // },
        {
          dataIndex: "status",
          title: "订单状态",
          isTable: true,
          isSearch: true,
          // multiple: true,
          valueType: "select",
          // formatter: (row) => {
          //   switch (row.status) {
          //     case "WAIT_PAY":
          //       return "待支付";
          //     case "WAIT_DELIVER":
          //       return "待发货";
          //     case "WAIT_RECEIVE":
          //       return "待收货";
          //     case "WAIT_AUDIT":
          //       return "待审核";
          //     case "SUCCESS":
          //       return "已完成";
          //     // case "CLOSED":
          //     //   return "已取消";
          //   }
          // },
          option: [
            // { label: "待支付", value: "WAIT_PAY" },
            { label: "待发货", value: "WAIT_DELIVER" },
            { label: "待收货", value: "WAIT_RECEIVE" },
            // { label: "待审核", value: "WAIT_AUDIT" },
            { label: "已完成", value: "SUCCESS" },
            { label: "待确认维修报告", value: "wait_confirmed_report" },
            { label: "待结算", value: "to_be_settled" },
            { label: "待审核", value: "wait_audit" },
            // { label: "已取消", value: "CLOSED" },
          ],
          minWidth: 80,
        },
        // {
        //   dataIndex: "orderType",
        //   title: "订单类型",
        //   isTable: true,
        //   formatter: (row) => row.orderType?.label,
        //   minWidth: 80,
        // },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "customerCode",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "license",
          title: "营业执照名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        // {
        //   dataIndex: "articleName",
        //   title: "物品名称",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 160,
        // },
        // {
        //   dataIndex: "articleCode",
        //   title: "物品编码",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 160,
        // },
        // {
        //   dataIndex: "manufacturerChannel",
        //   title: "制造商渠道",
        //   isTable: true,
        //   formatter: (row) => row.manufacturerChannel?.label,
        //   minWidth: 90,
        // },
        {
          dataIndex: "logisticsProvider",
          title: "配送方式",
          isTable: true,
          formatter: (row) => row.logisticsProvider?.label,
          minWidth: 80,
        },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          isTable: true,
          width: 150,
        },
        // {
        //   dataIndex: "unit",
        //   title: "单位",
        //   isTable: true,
        //   minWidth: 60,
        // },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          minWidth: 60,
        },
        {
          dataIndex: "amount",
          title: "商品金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "shippingFee",
          title: "运费",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "orderAmount",
          title: "订单金额",
          isTable: true,
          minWidth: 100,
        },

        // {
        //   dataIndex: "payTime",
        //   title: "支付时间",
        //   isTable: true,
        //   formatter: (row) => (row.payTime ? row.payTime : "/"),
        //   width: 150,
        // },
        // {
        //   dataIndex: "totalAmount",
        //   title: "收款总金额",
        //   isTable: true,
        //   minWidth: 100,
        // },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 160,
        },
      ],
      tableData: [],
      totalData: {},
      requestParameters: {},
      statLoading: false,
      exportLoading: false,
      // 账款核销
      dialogVisible: false,
      dialogTitle: "账款核销",
      formParams: {},
      formColumns: [
        {
          dataIndex: "license",
          title: "营业执照名称",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "code",
          title: "订单编号",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "orderAmount",
          title: "订单金额",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "verifyReceiveAmount",
          title: "应收金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入应收金额",
              trigger: "change",
              validator: (rule, value, callback) => {
                if (!value) {
                  callback(new Error("请输入应收金额"));
                } else if (+value < 0) {
                  callback(new Error("应收金额不能为负数"));
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "verifyDiscountAmount",
          title: "折扣金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入折扣金额",
          //     trigger: "change",
          //     validator: (rule, value, callback) => {
          //       if (+value < 0) {
          //         callback(new Error("折扣金额不能小于0"));
          //       }
          //       callback();
          //     },
          //   },
          // ],
        },
        {
          dataIndex: "verifyActualAmount",
          title: "实收金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          prop: [
            {
              required: true,
              trigger: "change",
              validator: (rule, value, callback) => {
                if (!value) {
                  callback(new Error("请输入实收金额"));
                } else if (+value < 0) {
                  callback(new Error("实收金额不能小于0"));
                } else {
                  const deposit = parseFloat(
                    _this.formParams.verifyReceiveAmount
                  );
                  const discount = parseFloat(
                    _this.formParams.verifyDiscountAmount
                  );
                  const payable = deposit - (isNaN(discount) ? 0 : discount);

                  if (+value > payable) {
                    callback(new Error("实收金额不能大于应收金额减去折扣金额"));
                  }
                  if (+value < payable) {
                    callback(new Error("实收金额不能小于应收金额减去折扣金额"));
                  }
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "voucherImg",
          title: "上传凭证",
          isForm: true,
          formSlot: "voucherImg",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请上传凭证",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "remark",
          title: "备注",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          wordlimit: 255,
          formSpan: 24,
        },
      ],
      formLoading: false,
      bulkEditColumns: [
        {
          dataIndex: "code",
          title: "订单号",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "status",
          title: "订单状态",
          isTable: true,
          isSearch: true,
          // multiple: true,
          valueType: "select",
          // formatter: (row) => {
          //   switch (row.status) {
          //     case "WAIT_PAY":
          //       return "待支付";
          //     case "WAIT_DELIVER":
          //       return "待发货";
          //     case "WAIT_RECEIVE":
          //       return "待收货";
          //     case "WAIT_AUDIT":
          //       return "待审核";
          //     case "SUCCESS":
          //       return "已完成";
          //   }
          // },
          option: [
            { label: "待发货", value: "WAIT_DELIVER" },
            { label: "待收货", value: "WAIT_RECEIVE" },
            { label: "已完成", value: "SUCCESS" },
          ],
          minWidth: 80,
        },
        // {
        //   dataIndex: "customerName",
        //   title: "店铺名称",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 150,
        // },
        // {
        //   dataIndex: "customerCode",
        //   title: "客户编号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 140,
        // },
        // {
        //   dataIndex: "license",
        //   title: "营业执照名称",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 160,
        // },
        {
          dataIndex: "logisticsProvider",
          title: "配送方式",
          isTable: true,
          formatter: (row) => row.logisticsProvider?.label,
          minWidth: 80,
        },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          // fixed: "right",
          minWidth: 60,
        },
        {
          dataIndex: "amount",
          title: "商品金额",
          isTable: true,
          // fixed: "right",
          minWidth: 100,
        },
        {
          dataIndex: "shippingFee",
          title: "运费",
          isTable: true,
          // fixed: "right",
          minWidth: 100,
        },
        {
          dataIndex: "orderAmount",
          title: "订单金额",
          isTable: true,
          // fixed: "right",
          minWidth: 100,
        },
        {
          dataIndex: "verifyReceiveAmount",
          title: "应收金额",
          isTable: true,
          // fixed: "right",
          tableSlot: "verifyReceiveAmount",
          width: 150,
        },
        {
          dataIndex: "verifyDiscountAmount",
          title: "折扣金额",
          isTable: true,
          // fixed: "right",
          tableSlot: "verifyDiscountAmount",
          width: 150,
        },
        {
          dataIndex: "verifyActualAmount",
          title: "实收金额",
          isTable: true,
          // fixed: "right",
          tableSlot: "verifyActualAmount",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          // fixed: "right",
          tooltip: false,
          tableSlot: "actions",
          width: 100,
        },
      ],
      selectionData: [],
      tipLock: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startDate: null,
          endData: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      this.requestParameters = requestParameters;
      receivableConsumableSummaryListApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      receivableBaseSummaryStatisticsApi({
        ...this.requestParameters,
        collectionType: 2,
      })
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    handleBulkEdit() {
      if (!this.selectionData.length) {
        this.$message.warning("请先选择要核销的订单");
        return;
      }
      this.$refs.bulkEdit.show(this.selectionData);
    },
    clearSelection() {
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },
    handleSelectionChange(rows) {
      this.$nextTick(() => {
        const firstRow = rows[0];
        const validRows = rows.filter((row) => {
          if (
            row.customerCode !== firstRow.customerCode ||
            row.type !== firstRow.type
          ) {
            this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(row, false);
            return true;
          }
          return false;
        });
        if (validRows.length > 0 && !this.tipLock) {
          this.$message.warning("请选择同一客户/同一订单类型的收款单");
          this.tipLock = true;
          setTimeout(() => {
            this.tipLock = false;
          }, 2000);
        }
        this.selectionData = cloneDeep(rows);
      });
    },
    handleEdit(row, type) {
      if (type === "info") {
        if (!row.code) {
          return;
        }
        if (row.type) {
          this.$refs.workOrderDetail.show(row.code);
        } else {
          this.$refs.saleOrderDetail.show(row.code);
        }
      } else if (type === "audit") {
        this.formParams = cloneDeep(row);
        console.log(this.formParams);
        this.dialogVisible = true;
        this.dialogTitle = `核销 【${row.customerName}】 账款`;
      }
    },
    handleDialogConfirm() {
      this.$refs.ProForm.handleSubmit();
    },
    proSubmit(val) {
      this.$confirm("此操作将核销当前客户的账单, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.formLoading = true;
        const args = {
          customerId: this.formParams.customerId,
          tradeOrderOrigin: this.formParams.type
            ? "REPAIR_ORDER"
            : "SALES_ORDER",
          tradeOrderNumber: this.formParams.code,
          verifyReceiveAmount: this.formParams.verifyReceiveAmount, // 应收金额
          verifyDiscountAmount: this.formParams.verifyDiscountAmount, // 折扣金额
          verifyActualAmount: this.formParams.verifyActualAmount, // 实收金额
          voucherImg: this.formParams.voucherImg, // 实收金额
          remark: this.formParams.remark,
        };
        receivableWriteOffApi(filterParam(args))
          .then((res) => {
            this.$message.success("核销成功");
            this.handleDialogCancel();
            this.refresh();
          })
          .finally(() => {
            this.formLoading = false;
          });
      });
    },
    handleDialogCancel() {
      this.dialogVisible = false;
      this.formParams = {};
    },
    handleUploadSuccess(result) {
      if (!this.formParams.voucherImg) {
        this.$set(this.formParams, "voucherImg", []);
      }
      this.formParams.voucherImg.push(result);
    },
    handleUploadRemove(file) {
      const index = this.formParams.voucherImg.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParams.voucherImg.splice(index, 1);
    },
    handleExport() {
      this.$confirm("此操作将导出耗材应收款明细, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          receivableConsumableSummaryExportApi,
          this.requestParameters,
          "耗材应收款明细",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
