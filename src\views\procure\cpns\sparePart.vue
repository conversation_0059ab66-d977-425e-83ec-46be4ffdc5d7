<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-01 13:17:35
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-03 16:17:54
 * @Description: 
 -->
<template>
  <div class="app-container">
    <ProDialog
      :value="dialogVisible"
      top="2%"
      title="选配件"
      width="70%"
      @cancel="handleCancel"
      @ok="handleOk"
    >
      <ProTable
        ref="ProTable"
        :query-param="queryParam"
        :local-pagination="localPagination"
        :height="350"
        :columns="columns"
        :data="tableData"
        @loadData="loadData"
      >
        <template #brand="slotProps">
          <el-popover
            placement="bottom"
            title="当前设备关联产品树"
            width="400"
            trigger="click"
          >
            <div style="margin: 20px">
              <el-tree
                :data="deviceProductTree"
                :props="{
                  children: 'children',
                  label: 'name',
                }"
                default-expand-all
              ></el-tree>
            </div>

            <el-button slot="reference" @click="getBrand(slotProps.row.id)">
              查看品牌
            </el-button>
          </el-popover>
        </template>
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button icon="el-icon-circle-check" @click="handleSelect(row)">
              确定
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { accessoryListApi, accessoryProductTreeApi } from "@/api/dispose";
import { cloneDeep } from "lodash";

export default {
  name: "SparePart",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    spareType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "lastIds",
          isSearch: true,
          clearable: true,
          valueType: "product",
          title: "品牌名称",
        },

        {
          dataIndex: "brand",
          title: "产品树",
          isTable: true,
          tableSlot: "brand",
        },
        {
          dataIndex: "type",
          title: "选配件类型",
          isTable: true,
          formatter: (row) => row.type?.label,
          // isSearch: true,
          // clearable: true,
          // valueType: "select",
          // formSpan: 8,
          // option: [],
          // optionMth: () => dictTreeByCodeApi(2000),
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
        },
        {
          dataIndex: "modeType",
          title: "配件型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "machineNumber",
          title: "机器编号",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 100,
        },
      ],
      tableData: [],
      deviceProductTree: [],
    };
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.refresh();
        });
      }
    },
  },
  methods: {
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      if (this.spareType) {
        this.queryParam.type = this.spareType;
      }
      const requestParameters = cloneDeep(this.queryParam);
      accessoryListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    handleOk() {
      this.$emit("update:dialogVisible", false);
    },
    handleCancel() {
      this.$emit("update:dialogVisible", false);
    },
    handleSelect(row) {
      this.$emit("confirm", row);
    },
    getBrand(id) {
      accessoryProductTreeApi(id).then((res) => {
        this.deviceProductTree = res.data;
      });
    },
    refresh() {
      if (this.dialogVisible) {
        this.$refs.ProTable.refresh();
      }
    },
  },
};
</script>

<style scoped lang="scss"></style>
