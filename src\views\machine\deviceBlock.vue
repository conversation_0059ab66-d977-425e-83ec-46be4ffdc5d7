<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-14 15:28:32
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="机器数据" name="机器数据" lazy>
        <MachineData />
      </el-tab-pane>
      <el-tab-pane label="设备运行查询" name="设备运行查询" lazy>
        <MonthAccount />
      </el-tab-pane>
      <!--<el-tab-pane label="选配件数据" name="选配件数据" lazy>-->
      <!--  选配件数据-->
      <!--</el-tab-pane>-->
      <el-tab-pane label="印量查询" name="印量查询" lazy>
        <DayAccount />
      </el-tab-pane>
      <!--<el-tab-pane label="抄表查询" name="抄表查询" lazy>抄表查询</el-tab-pane>-->
      <el-tab-pane label="维修查询" name="维修查询" lazy>
        <RepairRecord />
      </el-tab-pane>
      <!--<el-tab-pane label="时间查询" name="时间查询" lazy>时间查询</el-tab-pane>-->
      <el-tab-pane label="换件查询" name="换件查询" lazy>
        <PartRecord />
      </el-tab-pane>
      <!--<el-tab-pane label="耗材寿命" name="耗材寿命" lazy>耗材寿命</el-tab-pane>-->
      <!--<el-tab-pane label="机器寿命" name="机器寿命" lazy>机器寿命</el-tab-pane>-->
      <!--<el-tab-pane label="成本查询" name="成本查询" lazy>成本查询</el-tab-pane>-->
    </el-tabs>
  </div>
</template>

<script>
//设备数据
import MachineData from "@/views/machine/components/machineData.vue";
import RepairRecord from "@/views/machine/components/repairRecord.vue";
// 统计-日印量统计
import DayAccount from "@/views/machine/components/dayAccount.vue";
// 统计-设备运行统计
import MonthAccount from "@/views/machine/components/monthAccount.vue";
// 换件记录
import PartRecord from "@/views/machine/components/partRecord.vue";

export default {
  name: "DeviceBlock",
  components: {
    MachineData,
    PartRecord,
    RepairRecord,
    DayAccount,
    MonthAccount,
  },
  data() {
    return {
      activeName: "机器数据",
    };
  },
};
</script>

<style scoped lang="scss"></style>
