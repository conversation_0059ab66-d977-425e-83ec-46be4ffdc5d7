<template>
  <div class="edit-base-info">
    <ProForm ref="ProForm" :form-param="formParam" :form-list="columns" :confirm-loading="formLoading"
      :layout="{ formWidth: '100%', labelWidth: '140px' }" :open-type="type" @proSubmit="formSubmit">
    </ProForm>

    <div v-if="type !== 'info'" class="dialog-footer1">
      <div class="btn-box">
        <el-button type="primary" @click="handleOk">保存</el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import ProForm from "@/components/ProForm/index.vue";
import {
  iotTimeConfigApi,
  iotTimeConfigEditApi,
  iotTimeConfigAddApi,
} from "@/api/customer";
import { cloneDeep } from "lodash";

export default {
  name: "EditBaseInfo",
  components: { ProForm },
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
    // baseInfo: {
    //   type: Object,
    //   default: () => {
    //     return {};
    //   },
    // },
  },
  data() {
    return {
      formParam: {},
      columns: [
        {
          dataIndex: "serVersion",
          title: "服务端版本",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "cliVersion",
          title: "客户端版本",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "regDate",
          title: "安装日期",
          isForm: true,
          valueType: "date-picker",
          clearable: true,
          prop: [],
          formSpan: 12
        },
        {
          dataIndex: "upDate",
          title: "更新日期",
          isForm: true,
          valueType: "date-picker",
          clearable: true,
          prop: [],
          formSpan: 12
        },
        {
          dataIndex: "uploadCounterTime",
          title: "上报计数器时间",
          isForm: true,
          valueType: "input-number",
          // step: 1,
          // isnumtop: true,
          clearable: true,
          unit: "分钟",
          formSpan: 12,
        },
        {
          // clearboth: true,
          dataIndex: "uploadExceptionTime",
          title: "上报异常时间",
          isForm: true,
          valueType: "input-number",
          // step: 1,
          // isnumtop: true,
          clearable: true,
          unit: "分钟",
          formSpan: 12,
        },
        {
          // clearboth: true,
          dataIndex: "uploadPowerTime",
          title: "上报粉量时间",
          isForm: true,
          valueType: "input-number",
          // step: 1,
          // isnumtop: true,
          clearable: true,
          unit: "分钟",
          formSpan: 12,
        },

        {
          // clearboth: true,
          dataIndex: "uploadMachineMsgTime",
          title: "查询基本信息时间",
          isForm: true,
          valueType: "input-number",
          // step: 1,
          // isnumtop: true,
          clearable: true,
          unit: "分钟",
          formSpan: 12,
        },
        {
          // clearboth: true,
          dataIndex: "checkServiceTime",
          title: "检查更新",
          isForm: true,
          valueType: "input-number",
          // step: 1,
          // isnumtop: true,
          clearable: true,
          unit: "分钟",
          // span:8,
          formSpan: 12,
        },
      ],
      formLoading: false,
    };
  },
  async created() {
    await this.loadData();
  },
  methods: {
    formSubmit(val) {
      this.formParam.customerId = this.id;
      if (this.formParam.id) {
        iotTimeConfigEditApi(this.formParam).then(() => {
          this.$message.success("保存成功");
          this.$emit("refresh");
        });
      } else {
        iotTimeConfigAddApi(this.formParam).then(() => {
          this.$message.success("保存成功");
          this.$emit("refresh");
        });
      }
    },
    
    loadData() {
      iotTimeConfigApi(this.id).then((res) => {
        const needTry = ['uploadCounterTime', 'uploadExceptionTime', 'uploadMachineMsgTime', 'uploadPowerTime', 'checkServiceTime']
        const keys = Object.keys(res.data).filter((item)=>!needTry.includes(item))
        needTry.forEach((item) => {
          if (typeof res.data[item] === "string") {
            this.formParam[item] = parseFloat(res.data[item]);
          } else {
            this.formParam[item] = res.data[item]
          }
        })
        keys.forEach((item) => {
          this.formParam[item] = res.data[item]
        })
        // res.data.checkServiceTime=Number(res.data.checkServiceTime);
        this.formParam = cloneDeep(this.formParam);
        console.log(this.formParam)
      });
    },
    handleOk() {
      if (this.formParam.regDate) {
        this.formParam.regDate = this.$moment(this.formParam.regDate).format('YYYY-MM-DD HH:MM:SS');
      };
      if (this.formParam.upDate) {
        this.formParam.upDate = this.$moment(this.formParam.upDate).format('YYYY-MM-DD HH:MM:SS');
      };
      console.log(typeof (this.formParam.uploadCounterTime))
      // this.formParam = cloneDeep(this.formParam);
      this.$refs.ProForm.handleSubmit();
    },
    handleClose() {
      this.$emit("closeDrawer");
    },
  },
};
</script>

<style lang="scss" scoped>
.edit-base-info {
  height: 100%;
  overflow: auto;
  padding-bottom: 50px;

  .location {
    display: flex;

    &>span {
      display: block;
      margin: 0 10px;
    }
  }
}
</style>
