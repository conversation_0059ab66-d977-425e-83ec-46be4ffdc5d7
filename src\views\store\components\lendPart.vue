<template>
  <div class="container">
    <ProDrawer
      :value="showDrawer"
      size="80%"
      title="借件记录"
      :confirm-loading="false"
      @cancel="closeDrawer"
    ></ProDrawer>
  </div>
</template>

<script>
export default {
  name: "LendPart",
  data() {
    return {
      showDrawer: false,
      formColumns: [],
    };
  },
  mounted() {},
  methods: {
    show(row) {
      this.showDrawer = true;
    },
    closeDrawer() {
      this.showDrawer = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
