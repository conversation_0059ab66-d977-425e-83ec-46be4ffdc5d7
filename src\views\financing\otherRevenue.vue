<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-17 13:55:43
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-09 17:28:26
 * @Description: 财务 - 其他收入
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :layout="{ labelWidth: '80px' }"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleEdit(null, 'add')"
        >
          收入登记开单
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>总数量：{{ totalData?.num || 0 }}</div>
          <div>总金额：{{ totalData?.totalAmount || 0 }}</div>
          <div>实收总金额：{{ totalData?.actualAmount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 新增登记 -->
    <AddOtherAccount ref="addOtherAccount" type="revenue" @refresh="refresh" />
    <!-- 登记明细 -->
    <OtherAccountDetail
      ref="otherAccountDetail"
      type="revenue"
      @refresh="refresh"
    ></OtherAccountDetail>
  </div>
</template>

<script>
import AddOtherAccount from "@/views/financing/components/addOtherAccount.vue";
import OtherAccountDetail from "@/views/financing/components/otherAccountDetail.vue";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { otherIncomePageApi, otherIncomeStatApi } from "@/api/operator";

export default {
  name: "OtherRevenue",
  components: { AddOtherAccount, OtherAccountDetail },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "monthly",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          valueFormat: "yyyy-MM",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "totalAmount",
          title: "金额",
          isTable: true,
        },
        {
          dataIndex: "actualAmount",
          title: "实收金额",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          tooltip: false,
          width: 100,
        },
      ],
      tableData: [],
      editType: "info",
      drawerVisible: false,
      statLoading: true,
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startCreateTime: null,
          endCreateTime: null,
          data: parameter.monthly,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.monthly;
      requestParameters.businessType = 1;
      otherIncomePageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData(requestParameters);
    },
    handleEdit(row, type) {
      this.editType = type;
      if (type === "add") {
        this.$refs.addOtherAccount.show();
      } else {
        this.$refs.otherAccountDetail.show(row);
      }
      this.drawerVisible = true;
    },
    getTotalData(params) {
      this.statLoading = false;
      otherIncomeStatApi(params)
        .then((res) => {
          this.totalData = res.data;
        })
        .catch(() => {
          this.totalData = {};
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
