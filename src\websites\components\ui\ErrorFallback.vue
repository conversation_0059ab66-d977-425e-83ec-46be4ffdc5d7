<template>
  <div class="error-fallback" :class="className">
    <div class="error-content">
      <div class="error-icon">
        <i class="el-icon-warning-outline"></i>
      </div>
      <div class="error-text">
        <h3 class="error-title">{{ title }}</h3>
        <p class="error-description">{{ description }}</p>
      </div>
      <div v-if="showReload" class="error-actions">
        <el-button 
          type="primary" 
          size="small" 
          @click="handleReload"
          :loading="reloading"
        >
          重新加载
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorFallback',
  props: {
    error: {
      type: [Error, String],
      default: null
    },
    title: {
      type: String,
      default: '出现错误'
    },
    description: {
      type: String,
      default: '抱歉，出现了一些问题，请稍后重试'
    },
    showReload: {
      type: Boolean,
      default: false
    },
    resetError: {
      type: Function,
      default: null
    },
    className: {
      type: String,
      default: ''
    }
  },
  
  data() {
    return {
      reloading: false
    }
  },
  
  methods: {
    async handleReload() {
      if (this.resetError) {
        this.reloading = true
        try {
          await this.resetError()
        } catch (error) {
          console.error('重新加载失败:', error)
          this.$message.error('重新加载失败，请稍后重试')
        } finally {
          this.reloading = false
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.error-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  
  .error-content {
    text-align: center;
    max-width: 400px;
    
    .error-icon {
      margin-bottom: 16px;
      
      i {
        font-size: 48px;
        color: #f56565;
      }
    }
    
    .error-text {
      margin-bottom: 24px;
      
      .error-title {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #1a202c;
      }
      
      .error-description {
        margin: 0;
        font-size: 14px;
        color: #718096;
        line-height: 1.5;
      }
    }
    
    .error-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }
}

// 紧凑模式
.error-fallback.compact {
  padding: 20px;
  
  .error-content {
    .error-icon i {
      font-size: 32px;
    }
    
    .error-text {
      margin-bottom: 16px;
      
      .error-title {
        font-size: 16px;
      }
      
      .error-description {
        font-size: 13px;
      }
    }
  }
}
</style>
