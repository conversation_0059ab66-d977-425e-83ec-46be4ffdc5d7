<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:58:44
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>客户数量：{{ totalData.totalCustomerNum || 0 }}</div>
          <div>机器数量：{{ totalData.totalDeviceNum || 0 }}</div>
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <el-button icon="el-icon-edit" @click="handleEdit(row, 'edit')">
            编辑
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="showDrawer"
      size="80%"
      :title="drawerTitle"
      :no-footer="methodType === 'info'"
      :confirm-text="'确认修改'"
      @ok="handleSubmit"
      @cancel="handleClose"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="columns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
      >
        <template #purchaseChannel>
          <el-select
            v-model="tempPurchaseChannel"
            multiple
            placeholder="请选择主要购买渠道"
            :disabled="methodType === 'info'"
          >
            <el-option
              v-for="item in purchaseChannelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <template #serviceType>
          <el-select
            v-model="tempServiceType"
            multiple
            placeholder="请选择当前服务方式"
            :disabled="methodType === 'info'"
          >
            <el-option
              v-for="item in serviceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <template #shopRecruitmentImg>
          <ProUpload
            :file-list="form.shopRecruitmentImg"
            :type="methodType"
            :limit="2"
            :multiple="true"
            @uploadSuccess="
              (e) => handlePhotoUploadSuccess(e, 'shopRecruitmentImg')
            "
            @uploadRemove="
              (e) => handlePhotoUploadRemove(e, 'shopRecruitmentImg')
            "
          />
        </template>
        <template #shopOutsideImg>
          <ProUpload
            :file-list="form.shopOutsideImg"
            :type="methodType"
            :limit="4"
            :multiple="true"
            @uploadSuccess="
              (e) => handlePhotoUploadSuccess(e, 'shopOutsideImg')
            "
            @uploadRemove="(e) => handlePhotoUploadRemove(e, 'shopOutsideImg')"
          />
        </template>
        <template #shopInsideImg>
          <ProUpload
            :file-list="form.shopInsideImg"
            :type="methodType"
            :limit="5"
            :multiple="true"
            @uploadSuccess="(e) => handlePhotoUploadSuccess(e, 'shopInsideImg')"
            @uploadRemove="(e) => handlePhotoUploadRemove(e, 'shopInsideImg')"
          />
        </template>
      </ProForm>
    </ProDrawer>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { filterParam } from "@/utils";
import { dictTreeByCodeApi } from "@/api/user";
import {
  getCustomerTagByPageApi,
  getCustomerTagDataStatApi,
  getCustomerTagDetailApi,
  updateCustomerTagDetailApi,
} from "@/api/customer";
import ProUpload from "@/components/ProUpload/index.vue";
import { Message } from "element-ui";

export default {
  name: "Tag",
  components: { ProUpload },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "settleMethod",
          title: "结算方式",
          isSearch: "true",
          valueType: "select",
          option: [
            {
              value: 1,
              label: "现结",
            },
            {
              value: 2,
              label: "月付",
            },
            {
              value: 3,
              label: "第三方支付",
            },
            {
              value: 4,
              label: "预充抵扣",
            },
          ],
        },

        {
          dataIndex: "financeAccount",
          title: "财务核算",
          isSearch: true,
          valueType: "select",
          option: [
            {
              value: 1,
              label: "单店私账支付",
            },
            {
              value: 2,
              label: "多店私账支付",
            },
            {
              value: 3,
              label: "单店对公支付",
            },
            {
              value: 4,
              label: "多店对公支付",
            },
          ],
        },
        {
          dataIndex: "openBillType",
          title: "开票类型",
          isSearch: true,
          valueType: "select",
          option: [
            {
              value: 1,
              label: "增值税普通发票",
            },
            {
              value: 2,
              label: "增值税专用发票",
            },
          ],
        },
        {
          dataIndex: "cliVersion",
          title: "客户端版本",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "nativePlace",
          title: "老板籍贯",
          isTable: true,
          width: 180,
          isForm: true,
          formSpan: 6,
          formatter: (row) => row.nativePlace?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5900),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "siteArea",
          title: "店面大小",
          isTable: true,
          formatter: (row) => row.siteArea?.label,
          isForm: true,
          formSpan: 6,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(4900),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "personnelNum",
          title: "店面人数",
          isTable: true,
          formatter: (row) => row.personnelNum?.label,
          isForm: true,
          formSpan: 6,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5100),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "customerCost",
          title: "客户形态",
          isTable: true,
          formatter: (row) => row.customerCost?.label,
          isForm: true,
          formSpan: 6,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(3800),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "monthBlackWhiteNum",
          title: "月黑白印量",
          isForm: true,
          formSpan: 6,
          isTable: true,
          isSearch: true,
          valueType: "select",
          formatter: (row) => row.monthBlackWhiteNum?.label,
          option: [],
          optionMth: () => dictTreeByCodeApi(4800),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "monthColoursNum",
          title: "月彩色印量",
          isForm: true,
          formSpan: 6,
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5800),
          formatter: (row) => row.monthColoursNum?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "turnover",
          title: "营业额",
          isTable: true,
          formatter: (row) => row.turnover?.label,
          isForm: true,
          formSpan: 6,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "pictureRatio",
          title: "图文占比",
          isForm: true,
          formSpan: 6,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5300),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "peerProcessNum",
          title: "同行加工",
          isForm: true,
          formSpan: 6,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "realMachineNum",
          title: "实际机器数",
          isForm: true,
          formSpan: 6,
          isTable: true,
          formatter: (row) => row.realMachineNum?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6300),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "num",
        //   title: "生产型机器数量",
        //   isTable: true,
        //   width: 180,
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          dataIndex: "hasNew",
          title: "是否有新机",
          isForm: true,
          formSpan: 6,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi("isTrue"),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "deviceNum",
          title: "后道设备数",
          isForm: true,
          formSpan: 6,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6400),
          optionskey: {
            label: "label",
            value: "value",
          },
        },

        {
          dataIndex: "purchaseChannel",
          title: "主要购买渠道",
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(4600),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "purchaseChannel",
          title: "主要购买渠道",
          isForm: true,
          formSpan: 6,
          formSlot: "purchaseChannel",
        },
        {
          dataIndex: "consumableAdvantage",
          title: "耗材价格优势",
          isForm: true,
          formSpan: 6,
          isSearch: true,
          valueType: "select",
          option: [],
          // multiple: true,
          optionMth: () => dictTreeByCodeApi(5400),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "consumableCompetitorMoney",
        //   title: "耗材价格反馈",
        //   isSearch: true,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        {
          dataIndex: "selfStudy",
          title: "自修能力",
          isForm: true,
          isSearch: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "serviceType",
          title: "当前服务方式",
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5600),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "serviceType",
          title: "当前服务方式",
          isForm: true,
          formSpan: 6,
          formSlot: "serviceType",
        },
        {
          dataIndex: "normalMaster",
          title: "以直客为主",
          isForm: true,
          formSpan: 6,
          isSearch: true,
          placeholder: "是否以直客为主",
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi("isTrue"),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "demandDegree",
          title: "印品要求程度",
          isForm: true,
          formSpan: 6,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(5700),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "largScaleCustomer",
          title: "有大型客户",
          isForm: true,
          formSpan: 6,
          isSearch: true,
          placeholder: "是否有大型客户",
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi("isTrue"),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "consumableCompetitor",
          title: "耗材竞争对手",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "serCompetitor",
          title: "服务竞争对手",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "consumableCompetitor",
          title: "耗材竞争对手",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
          placeholder: "请输入耗材竞争对手，如有多个请用“/”隔开",
        },
        {
          dataIndex: "serCompetitor",
          title: "服务竞争对手",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
          placeholder: "请输入服务竞争对手，如有多个请用”/“隔开",
        },
        {
          dataIndex: "shopRecruitmentImg",
          title: "店招照片",
          isForm: true,
          formSpan: 24,
          formSlot: "shopRecruitmentImg",
        },
        {
          dataIndex: "shopOutsideImg",
          title: "店铺周围照片",
          isForm: true,
          formSpan: 24,
          formSlot: "shopOutsideImg",
        },
        {
          dataIndex: "shopInsideImg",
          title: "店内照片",
          isForm: true,
          formSpan: 24,
          formSlot: "shopInsideImg",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 160,
        },
      ],
      tableData: [],
      totalData: {},
      showDrawer: false,
      drawerTitle: "1",
      form: {},
      formLoading: false,
      methodType: "info",
      tempPurchaseChannel: [],
      purchaseChannelOptions: [],
      tempServiceType: [],
      serviceOptions: [],
    };
  },
  mounted() {
    this.refresh();
    dictTreeByCodeApi(4600).then((res) => {
      this.purchaseChannelOptions = res.data;
    });
    dictTreeByCodeApi(5600).then((res) => {
      this.serviceOptions = res.data;
    });
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      getCustomerTagByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData(requestParameters);
    },
    async handleEdit(row, type) {
      this.methodType = type;
      this.drawerTitle =
        type === "info"
          ? `查看 - ${row.customerName}`
          : `编辑 - ${row.customerName}`;
      await getCustomerTagDetailApi(row.customerId).then((res) => {
        const formParam = JSON.parse(JSON.stringify(res.data));
        Object.keys(formParam).forEach((key) => {
          if (
            typeof formParam[key] === "object" &&
            formParam[key] !== null &&
            Object.keys(formParam[key]).length === 0
          ) {
            delete formParam[key];
          } else {
            formParam[key] = formParam[key].label
              ? formParam[key].value
              : formParam[key];
          }
        });
        this.form = formParam;
        this.tempPurchaseChannel = this.form.purchaseChannel;
        this.tempServiceType = this.form.serviceType;
        this.showDrawer = true;
        this.$nextTick((e) => {
          this.$refs["ProForm"].resetFormParam();
        });
      });
    },
    handleSubmit() {
      const arg = {
        ...this.form,
        purchaseChannel: this.tempPurchaseChannel,
        serviceType: this.tempServiceType,
      };
      updateCustomerTagDetailApi(arg).then((res) => {
        this.refresh();
        this.showDrawer = false;
        Message.success("编辑成功");
      });
    },
    handleClose() {
      // this.form = {};
      this.drawerTitle = "";
      this.showDrawer = false;
    },
    getTotalData(params) {
      getCustomerTagDataStatApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    handlePhotoUploadSuccess(result, type) {
      if (!this.form[type]) {
        this.$set(this.form, type, []);
      }
      this.form[type].push(result);
    },
    handlePhotoUploadRemove(file, type) {
      const index = this.form[type].findIndex((val) => val.key === file.key);
      if (index === -1) return;
      this.form[type].splice(index, 1);
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
