<!--
 * @Author: wskg
 * @Date: 2024-08-30 10:27:26
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:58:44
 * @Description: 客户 - 转换分析
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>客户总数：{{ totalData?.customerNum || 0 }}</div>
          <div>登记地址：{{ totalData?.hasAddrNum || 0 }}</div>
          <div>登记机器：{{ totalData?.hasDeviceNum || 0 }}</div>
          <!--          <div>数据完整：{{ totalData?.totalAmount || 0 }}</div>-->
          <div>客户端：{{ totalData?.hasCliMechineNum || 0 }}</div>
          <div>交易：{{ totalData?.hasTradingNum || 0 }}</div>
          <div>机器：{{ totalData?.hasMachineNum || 0 }}</div>
          <div>耗材：{{ totalData?.hasOrderNum || 0 }}</div>
          <div>散修：{{ totalData?.hasRetailNum || 0 }}</div>
          <div>包月：{{ totalData?.hasMonthNum || 0 }}</div>
          <div>半包全包：{{ totalData?.hasHalfAllNum || 0 }}</div>
          <div>租赁：{{ totalData?.hasRentNum || 0 }}</div>
        </div>
      </template>
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getCustomerConversionByPageApi,
  getCustomerConversionStatApi,
} from "@/api/customer";
import { optionsGetRegionApi } from "@/api/operator";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";

export default {
  name: "Convert",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      columns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "hasAddr",
          title: "登记地址",
          isTable: true,
          align: "center",
          formatter: (row) => (row.hasAddr ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "hasDevice",
          title: "登记机器",
          isTable: true,
          align: "center",
          formatter: (row) => (row.hasDevice ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "hasCliMechine",
          title: "客户端",
          isTable: true,
          align: "center",
          formatter: (row) => (row.hasCliMechine ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "hasTrading",
          title: "交易",
          isTable: true,
          align: "center",
          formatter: (row) => (row.hasTrading ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "hasMechine",
          title: "机器",
          isTable: true,
          align: "center",
          formatter: (row) => (row.hasMechine ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "hasOrder",
          title: "耗材",
          isTable: true,
          align: "center",
          formatter: (row) => (row.hasOrder ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "hasRetail",
          title: "散修",
          isTable: true,
          align: "center",
          formatter: (row) => (row.hasRetail ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "hasMonth",
          title: "包月",
          isTable: true,
          align: "center",
          formatter: (row) => (row.hasMonth ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "hasHalfAll",
          title: "半保全保",
          isTable: true,
          align: "center",
          formatter: (row) => (row.hasHalfAll ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "hasRent",
          title: "租赁",
          isTable: true,
          align: "center",
          formatter: (row) => (row.hasRent ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "enterDate",
          title: "入驻时间",
          isTable: true,
          align: "center",
          formatter: (row) => this.formatDate(row.enterDate),
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
          width: 100,
        },
        {
          dataIndex: "recentlyTrading",
          title: "最近交易时间",
          isTable: true,
          align: "center",
          formatter: (row) => this.formatDate(row.recentlyTrading),
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 120,
        },
        {
          dataIndex: "recentlyInterview",
          title: "最近访问时间",
          isTable: true,
          align: "center",
          formatter: (row) => this.formatDate(row.recentlyInterview),
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 120,
        },
        {
          dataIndex: "recentlySearch",
          title: "最近搜索时间",
          isTable: true,
          align: "center",
          formatter: (row) => this.formatDate(row.recentlySearch),
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 120,
        },
        {
          dataIndex: "recentlyVisit",
          title: "最近拜访时间",
          isTable: true,
          align: "center",
          formatter: (row) => this.formatDate(row.recentlyVisit),
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 120,
        },
      ],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
    this.optionsGetRegionAFn();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );

      const result = [
        {
          enterStartDate: null,
          enterEndDate: null,
          data: parameter.enterDate,
        },
        {
          recentlyTradingStart: null,
          recentlyTradingEnd: null,
          data: parameter.recentlyTrading,
        },
        {
          recentlyInterviewStart: null,
          recentlyInterviewEnd: null,
          data: parameter.recentlyInterview,
        },
        {
          recentlySearchStart: null,
          recentlySearchEnd: null,
          data: parameter.recentlySearch,
        },
        {
          recentlyVisitStart: null,
          recentlyVisitEnd: null,
          data: parameter.recentlyVisit,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.enterDate;
      delete requestParameters.recentlyTrading;
      delete requestParameters.recentlyInterview;
      delete requestParameters.recentlySearch;
      delete requestParameters.recentlyVisit;
      getCustomerConversionByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData(requestParameters);
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    getTotalData(params) {
      getCustomerConversionStatApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    formatDate(date) {
      return date ? date.slice(0, 10) : "/";
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  display: flex;
  justify-content: space-between;
  flex: 1;
}
</style>
