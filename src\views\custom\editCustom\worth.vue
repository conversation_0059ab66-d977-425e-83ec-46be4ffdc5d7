<template>
  <div class="worth">
    <div class="workBox">
      <div>
        机器数量: {{ formParam.deviceCount ? formParam.deviceCount : 0 }}
      </div>
      <div>
        客户端安装数量:
        {{ formParam.regClientCount ? formParam.regClientCount : 0 }}
      </div>
      <div>面积: {{ formParam.siteArea ? formParam.siteArea?.label : 0 }}</div>
      <div>
        人员: {{ formParam.personnelNum ? formParam.personnelNum?.label : 0 }}
      </div>
      <div>
        总充值金额: {{ formParam.topupAmount ? formParam.topupAmount : 0 }}
      </div>
      <div>入住天数: {{ formParam.enterDays ? formParam.enterDays : 0 }}</div>
    </div>
    <el-divider></el-divider>
    <ProForm
      ref="ProForm"
      :form-param="formParam"
      :form-list="formColumns"
      :confirm-loading="formLoading"
      :layout="{ formWidth: '100%', labelWidth: '140px' }"
      :open-type="type"
    ></ProForm>
  </div>
</template>

<script>
import { getCustomerValueApi } from "@/api/customer";

export default {
  name: "Worth",
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
  },
  data() {
    return {
      formParam: {},
      formLoading: false,
      formColumns: [
        {
          dataIndex: "totalPrintNum",
          title: "总印量",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "monthAvgPrint",
          title: "月均印量",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "totalBlackPrint",
          title: "黑白总印量",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "totalColorPrint",
          title: "彩色总印量",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "interviewCount",
          title: "总访问次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "monthAvgInterview",
          title: "月均访问次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "searchCount",
          title: "总搜索次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "monthAvgSearch",
          title: "月均搜索次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "orderCount",
          title: "订单次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "orderAmount",
          title: "订单金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "avgOrderAmount",
          title: "单笔平均采购金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "monthAvgOrder",
          title: "月均订单次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "workCount",
          title: "工单次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "workAmount",
          title: "工单金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "avgWorkAmount",
          title: "单笔平均工单金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "monthAvgWork",
          title: "月均工单次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "totalFault",
          title: "总故障次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "monthAvgFault",
          title: "月均故障次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "totalRepair",
          title: "维修次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "monthAvgRepair",
          title: "月均维修次数",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "",
          title: "应采购金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "",
          title: "月均应采购金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "consumeAmount",
          title: "总消费金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "monthAvgConsume",
          title: "月消费金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "",
          title: "总成本",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "",
          title: "月均成本",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "",
          title: "总利润",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "",
          title: "月均利润",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
      ],
    };
  },
  mounted() {
    getCustomerValueApi(this.id).then((res) => {
      this.formParam = res.data;
      console.log(res);
    });
  },
};
</script>

<style scoped lang="scss">
.workBox {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-top: 9px;
  gap: 20px;
  div {
    text-wrap: wrap;
    font-size: 16px;
    color: #6488cf;
  }
}
</style>
