<!--
 * @Author: AI Assistant
 * @Date: 2025-08-03
 * @Description: 分配关系列表组件
-->
<template>
  <div class="distribution-list-container">
    <!-- 统计信息 -->
    <div class="statistics-row mb-3">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total }}</div>
              <div class="stat-label">总分配关系</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number active">{{ statistics.active }}</div>
              <div class="stat-label">激活状态</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number user">{{ statistics.userCount }}</div>
              <div class="stat-label">用户分配</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number device">{{ statistics.deviceCount }}</div>
              <div class="stat-label">设备分配</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 分配关系表格 -->
    <el-table
      v-loading="loading"
      :data="distributionList"
      stripe
      border
      style="width: 100%"
      :empty-text="loading ? '加载中...' : '暂无分配关系数据'"
    >
      <!-- 序号列 -->
      <el-table-column label="序号" width="60" type="index" :index="getIndex" />

      <el-table-column label="版本名称" width="120">
        <template slot-scope="{ row }">
          <div class="version-info">
            <div class="version-name">{{ row.versionName || '-' }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="版本号" width="80">
        <template slot-scope="{ row }">
          <div class="version-code">
            V{{ row.versionCode || '-' }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="发布类型" width="120">
        <template slot-scope="{ row }">
          <el-tag 
            :type="getReleaseTypeColor(row.releaseType)"
            size="small"
          >
            {{ getReleaseTypeText(row.releaseType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="目标类型" width="100">
        <template slot-scope="{ row }">
          <el-tag 
            :type="getTargetTypeColor(row.targetType)"
            size="small"
          >
            {{ getTargetTypeText(row.targetType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="目标名称" prop="targetName" min-width="180" />

      <el-table-column label="状态" width="140">
        <template slot-scope="{ row }">
          <el-tag 
            :type="row.isActive ? 'success' : 'info'"
            size="small"
          >
            {{ row.isActive ? '激活' : '未激活' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="分配时间" width="180">
        <template slot-scope="{ row }">
          {{ formatDateTime(row.assignTime) }}
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="180">
        <template slot-scope="{ row }">
          {{ formatDateTime(row.createdAt) }}
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="140" fixed="right">
        <template slot-scope="{ row }">
          <div class="fixed-width">
            <!-- 激活/取消激活按钮 -->
            <el-button
              v-if="row.isActive"
              size="mini"
              type="primary"
              @click="handleDeactivateDistribution(row)"
              :loading="row.loading"
            >
              取消激活
            </el-button>
            <el-button
              v-else
              size="mini"
              type="primary"
              @click="handleActivateDistribution(row)"
              :loading="row.loading"
            >
              重新激活
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 空状态 -->
    <div v-if="!loading && distributionList.length === 0" class="empty-state">
      <i class="el-icon-info"></i>
      <p>暂无分配关系数据</p>
    </div>
  </div>
</template>

<script>
import {
  getDistributionsDetail,
  deactivateDistribution,
  activateDistribution
} from '@/appupdate/api/appVersion';

export default {
  name: 'DistributionList',
  props: {
    showActiveOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      distributionList: [],
      statistics: {
        total: 0,
        active: 0,
        userCount: 0,
        deviceCount: 0
      }
    };
  },
  watch: {
    showActiveOnly: {
      handler(newVal, oldVal) {
        // 只有在值真正改变时才重新加载，避免初始化时的重复请求
        if (newVal !== oldVal) {
          this.loadDistributions();
        }
      },
      immediate: false
    }
  },
  mounted() {
    this.loadDistributions();
  },
  methods: {
    /**
     * 加载分配关系列表
     */
    async loadDistributions() {
      this.loading = true;
      try {
        const response = await getDistributionsDetail(this.showActiveOnly);
        this.distributionList = response.data || [];
        this.calculateStatistics();
      } catch (error) {
        this.$message.error('加载分配关系列表失败');
        this.distributionList = [];
        this.resetStatistics();
      } finally {
        this.loading = false;
      }
    },

    /**
     * 计算统计信息
     */
    calculateStatistics() {
      const list = this.distributionList;
      this.statistics = {
        total: list.length,
        active: list.filter(item => item.isActive).length,
        userCount: list.filter(item => item.targetType === 'USER').length,
        deviceCount: list.filter(item => item.targetType === 'DEVICE').length
      };
    },

    /**
     * 重置统计信息
     */
    resetStatistics() {
      this.statistics = {
        total: 0,
        active: 0,
        userCount: 0,
        deviceCount: 0
      };
    },

    /**
     * 刷新列表
     */
    refresh() {
      this.loadDistributions();
    },

    /**
     * 获取序号
     */
    getIndex(index) {
      return index + 1;
    },

    /**
     * 获取发布类型文本
     */
    getReleaseTypeText(type) {
      const typeMap = {
        'GLOBAL': '全局发布',
        'TARGETED': '定向发布'
      };
      return typeMap[type] || type;
    },

    /**
     * 获取发布类型颜色
     */
    getReleaseTypeColor(type) {
      const colorMap = {
        'GLOBAL': 'success',
        'TARGETED': 'warning'
      };
      return colorMap[type] || 'info';
    },

    /**
     * 获取目标类型文本
     */
    getTargetTypeText(type) {
      const typeMap = {
        'USER': '用户',
        'DEVICE': '设备',
        'GROUP': '用户组'
      };
      return typeMap[type] || type;
    },

    /**
     * 获取目标类型颜色
     */
    getTargetTypeColor(type) {
      const colorMap = {
        'USER': 'primary',
        'DEVICE': 'success',
        'GROUP': 'warning'
      };
      return colorMap[type] || 'info';
    },

    /**
     * 格式化日期时间
     */
    formatDateTime(dateTime) {
      if (!dateTime) return '-';
      return new Date(dateTime).toLocaleString('zh-CN');
    },

    /**
     * 取消激活分发关系
     */
    async handleDeactivateDistribution(distribution) {
      try {
        await this.$confirm(
          `确定要取消激活 ${distribution.targetName || distribution.targetId} 的分发关系吗？\n取消后该用户/设备暂时无法获取定向版本，但可以重新激活。`,
          '确认取消激活',
          { type: 'warning' }
        );

        // 设置加载状态
        this.$set(distribution, 'loading', true);

        // 调用取消激活分发关系的API
        await deactivateDistribution(distribution.versionId, distribution.targetType, distribution.targetId);
        console.log('取消激活分发关系成功:', distribution);
        this.$message.success('取消激活成功');

        // 重新加载分发关系列表
        this.loadDistributions();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消激活分发关系失败:', error);
          this.$message.error('取消激活失败：' + (error.message || '未知错误'));
        }
      } finally {
        // 清除加载状态
        this.$set(distribution, 'loading', false);
      }
    },

    /**
     * 重新激活分发关系
     */
    async handleActivateDistribution(distribution) {
      try {
        await this.$confirm(
          `确定要重新激活 ${distribution.targetName || distribution.targetId} 的分发关系吗？\n激活后该用户/设备将重新可以获取定向版本。`,
          '确认重新激活',
          { type: 'info' }
        );

        // 设置加载状态
        this.$set(distribution, 'loading', true);

        // 调用重新激活分发关系的API
        await activateDistribution(distribution.versionId, distribution.targetType, distribution.targetId);
        console.log('重新激活分发关系成功:', distribution);
        this.$message.success('重新激活成功');

        // 重新加载分发关系列表
        this.loadDistributions();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重新激活分发关系失败:', error);
          this.$message.error('重新激活失败：' + (error.message || '未知错误'));
        }
      } finally {
        // 清除加载状态
        this.$set(distribution, 'loading', false);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.distribution-list-container {
  max-height: none;
  overflow: visible;
  .statistics-row {
    .stat-card {
      border: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .stat-content {
        text-align: center;
        padding: 6px 0;

        .stat-number {
          font-size: 18px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 2px;
          
          &.active {
            color: #67C23A;
          }
          
          &.user {
            color: #409EFF;
          }
          
          &.device {
            color: #E6A23C;
          }
        }
        
        .stat-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .version-info {
    display: flex;
    align-items: center;

    .version-name {
      font-weight: 600;
      color: #303133;
    }

    .version-code {
      font-weight: 600;
      color: #909399;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #909399;
    
    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }
    
    p {
      font-size: 16px;
      margin: 0;
    }
  }
}

.mb-3 {
  margin-bottom: 12px;
}

// 操作按钮样式
.fixed-width {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
