<!--
 * @Description: 
 * @version: 
 * @Author: <PERSON>le
 * @Date: 2025-02-18 16:16:40
 * @LastEditors: <PERSON>le
 * @LastEditTime: 2025-02-19 15:56:42
-->
<template>
   <el-form ref="defaultForm" :model="defaultData" label-width="120px">
        <el-row ref="defaultRowRef" :gutter="20">
            <el-col v-for="item in column" :key="item.label" :span="item.span||actSpan">
                <el-form-item v-if="item.formType" :label="item.label">
                    <el-select v-if="item.formType=='select'" v-model="defaultData[item.key]" placeholder="">
                        <el-option v-for="option in item.options()" :key="option.value" :label="option.label" :value="option.value">
                        </el-option>
                    </el-select>
                    <div v-else-if="item.formType=='image'" class="form-image">
                        <el-image v-for="src in defaultData[item.key]" :key="src" fit="fit" :src="src"></el-image>
                    </div>
                    <el-input v-else-if="item.formType=='input'" v-model="defaultData[item.key]" placeholder=""></el-input>
                    <el-date-picker v-else-if="item.formType=='date'" v-model="defaultData[item.key]" value-format="yyyy-MM-dd" placeholder=""></el-date-picker>
                    <el-checkbox v-else-if="item.formType=='checkbox'" v-model="defaultData[item.key]" ></el-checkbox>
                    <el-input v-else-if="item.formType=='textarea'" v-model="defaultData[item.key]" type="textarea" placeholder=""></el-input>
                    <el-input v-else-if="item.formType=='number'" v-model="defaultData[item.key]" type="number" placeholder=""></el-input>
                    <div v-else-if="item.formType=='devicePicker'">
                        {{ defaultData.machineNum }}
                        <el-button type="primary" size="small" @click="dialogVisible=true">选择</el-button>
                    </div>
                </el-form-item>
                
                <el-form-item v-else :label="item.label">
                    <span v-if="item.formatter">{{ item.formatter(defaultData) }}</span>
                    <span v-else>{{ defaultData[item.key] }}</span>
                </el-form-item>
            </el-col>
        </el-row>
        <choose-device v-model="dialogVisible" host-type="2008" @confirmDispatch="setDevice"></choose-device>
        </el-form>
</template>
<script>
import {cloneDeep} from 'lodash'
import ChooseDevice from './chooseDevices.vue'
export default {
  name: "TradeForm",
  components: {ChooseDevice},
  model: {
    prop: "modelValue",
    event: "update:modelValue"
  },
  props: {
    modelValue: {
      type: Object,
      default: () => {},
    },
    column: {
        type: Array,
        default: () => []
    }
  },
  data() {
    return {
        defaultData: {},
        containerWidth: 0,
        actSpan: 8,
        dialogVisible: false
    };
  },
  computed: {},
  watch: {
    defaultData: {
        handler(){
        this.$emit("update:modelValue", this.defaultData)
    },
    deep: true
    }
  },
  created() {
    this.defaultData = cloneDeep(this.modelValue)
  },
   mounted() {
    this.getDefaultWidth();
    window.addEventListener("resize", this.getDefaultWidth);
  },
  methods: {
setDevice(data){
    if (data && data.length > 0){
        const device = data[0];
        console.log(device);
        this.defaultData.machineNum = device.machineNum
        this.defaultData.productName = device.productName
        this.defaultData.blackWhiteCounter = device.blackWhiteCounter
        this.defaultData.colorCounter = device.colorCounter
        this.defaultData.fiveColourCounter2 = device.fiveColourCounter
        this.defaultData.electric = device.electric
    }
},
    getSearchSpan() {
      if (this.containerWidth > 2616) {
        return 3; // 8
      } else if (this.containerWidth > 1962) {
        return 4; // 6
      } else if (this.containerWidth > 1308) {
        return 6; // 4
      } else if (this.containerWidth > 981) {
        return 8; // 3
      } else {
        return 12;
      }
    },
    getDefaultWidth(){
        const _this = this;
        this.$nextTick(() => {
            const el = _this.$refs.defaultRowRef;
            if (el){
                _this.containerWidth = el.$el.clientWidth;
                console.log(_this.containerWidth);
                _this.actSpan = _this.getSearchSpan()
                console.log(_this.actSpan);
            }
        })
    },
  }
};
</script>
<style lang="scss" scoped>
    .form-image{
        .el-image{
            width:120px;height:120px;margin-right:15px;
            &:last-of-type{
                margin-right: 0;
            }
        }
    }
    :deep(.el-input){
    input[type="number"]::-webkit-outer-spin-button,  
input[type="number"]::-webkit-inner-spin-button {  
  -webkit-appearance: none;  
  margin: 0;  
}  
  
/* Firefox */  
input[type="number"] {  
  -moz-appearance: textfield;  
}
}
:deep(.el-form-item__content){
        min-height:43px;
}
</style>
