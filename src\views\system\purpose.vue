<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-08 15:32:56
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-18 16:44:05
 * @Description: 意向客户
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <el-button icon="el-icon-edit" @click="handleEdit(row, 'edit')">
            编辑
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="60%"
      :confirm-button-disabled="confirmLoading"
      confirm-text="确认修改"
      @ok="handleDrawerOk"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :form-param="formParam"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :open-type="editType"
        @proSubmit="formSubmit"
      ></ProForm>
    </ProDrawer>
  </div>
</template>
<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { intentionEditApi, intentionListApi } from "@/api/user";

export default {
  name: "Purpose",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "telphone",
          title: "电话号码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入电话号码",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "userName",
          title: "姓名",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "type",
          title: "客户类型",
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "未识别",
              value: 0,
            },
            {
              label: "老客户",
              value: 1,
            },
            {
              label: "新客户",
              value: 2,
            },
          ],
          formatter: (row) => {
            switch (row.type) {
              case 0:
                return "未识别";
              case 1:
                return "老客户";
              case 2:
                return "新客户";
              default:
                return "";
            }
          },
        },
        {
          dataIndex: "trendType",
          title: "意向类型",
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "待跟进",
              value: 0,
            },
            {
              label: "有意向",
              value: 1,
            },
            {
              label: "未接通",
              value: 2,
            },
            {
              label: "无意向",
              value: 3,
            },
            {
              label: "考虑中",
              value: 4,
            },
          ],
          formatter: (row) => {
            switch (row.trendType) {
              case 0:
                return "待跟进";
              case 1:
                return "有意向";
              case 2:
                return "未接通";
              case 3:
                return "无意向";
              case 4:
                return "考虑中";
              default:
                return "";
            }
          },
        },
        {
          dataIndex: "comments",
          title: "意向说明",
          isTable: true,
          minWidth: 200,
        },
        {
          dataIndex: "manageLoginAt",
          title: "后台登录时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "clientLoginAt",
          title: "客户端登录时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "engineLoginAt",
          title: "工程师端登录时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          // fixed: "right",
          width: 160,
        },
      ],
      tableData: [],
      editType: "info",
      drawerVisible: false,
      drawerTitle: "明细",
      confirmLoading: false,
      formParam: {},
      formColumns: [
        {
          dataIndex: "telphone",
          title: "电话号码",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "userName",
          title: "姓名",
          isForm: true,
          valueType: "input",
          formSpan: 8,
        },
        {
          dataIndex: "type",
          title: "客户类型",
          isForm: true,
          valueType: "select",
          option: [
            {
              label: "未识别",
              value: 0,
            },
            {
              label: "老客户",
              value: 1,
            },
            {
              label: "新客户",
              value: 2,
            },
          ],
          prop: [
            {
              required: true,
              message: "请选择客户类型",
              trigger: "change",
            },
          ],
          formSpan: 8,
        },
        {
          dataIndex: "trendType",
          title: "意向类型",
          isForm: true,
          valueType: "select",
          option: [
            {
              label: "待跟进",
              value: 0,
            },
            {
              label: "有意向",
              value: 1,
            },
            {
              label: "未接通",
              value: 2,
            },
            {
              label: "无意向",
              value: 3,
            },
            {
              label: "考虑中",
              value: 4,
            },
          ],
          prop: [
            {
              required: true,
              message: "请选择客户类型",
              trigger: "change",
            },
          ],
          formSpan: 8,
        },
        {
          dataIndex: "comments",
          title: "意向说明",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          autosize: {
            minRows: 10,
          },
          wordlimit: 1000,
          formSpan: 24,
        },
        {
          dataIndex: "manageLoginAt",
          title: "后台登录时间",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "manageLoginTimes",
          title: "后台登录次数",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "other1",
          title: "后台登录次数",
          isForm: true,
          // valueType: "text",
          formOtherSlot: "other1",
          formSpan: 12,
        },
        {
          dataIndex: "clientLoginAt",
          title: "客户端登录时间",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "clientLoginTimes",
          title: "客户端登录次数",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "other2",
          title: "后台登录次数",
          isForm: true,
          // valueType: "text",
          formOtherSlot: "other2",
          formSpan: 12,
        },
        {
          dataIndex: "engineLoginAt",
          title: "工程师端登录时间",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "engineLoginTimes",
          title: "工程师端登录次数",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
      ],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      intentionListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleDrawerOk() {
      this.$refs.ProForm.handleSubmit();
    },
    formSubmit(val) {
      this.$confirm(
        "是否确认将该客户的意向信息更新为最新内容？此操作将影响后续跟进状态。",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.confirmLoading = true;
        const args = {
          id: val.id,
          type: val.type,
          userName: val.userName,
          trendType: val.trendType,
          comments: val.comments,
        };
        intentionEditApi(args)
          .then((res) => {
            this.$message.success("操作成功");
            this.drawerVisible = false;
            this.refresh();
          })
          .finally(() => {
            this.confirmLoading = false;
          });
      });
    },
    closeDrawer() {
      this.drawerVisible = false;
    },
    handleEdit(row, type) {
      this.editType = type;
      this.formParam = cloneDeep(row);
      this.drawerVisible = true;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
