import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Spin } from 'antd';
import { useCases } from '../../../hooks/useWebsiteApi';

export default function CasesPage() {
  const { data: casesData, isLoading: isCasesLoading, error: casesError } = useCases();
  
  const isLoading = isCasesLoading;
  const error = casesError;

  const [selectedCategory, setSelectedCategory] = useState('全部');

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" spinning={true}>
          <div className="p-8">案例信息加载中...</div>
        </Spin>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
          <p className="text-gray-600">请刷新页面重试</p>
        </div>
      </div>
    );
  }

  // 修复：API返回的是数组，取第一个元素
  const content = Array.isArray(casesData) ? casesData[0] : casesData;
  const seoMeta = content?.seoMeta;





  if (!content) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无案例数据</h3>
          <p className="text-gray-600">请联系管理员添加案例内容</p>
        </div>
      </div>
    );
  }



  // 解析内容数据
  let contentData: any = {};
  try {
    contentData = JSON.parse(content.content || '{}');
  } catch (error) {
    // 解析失败时使用空对象
  }

  // 获取页面配置
  const pageConfig = contentData.config || {
    heroTitle: '成功案例',
    heroSubtitle: '真实案例展示我们的专业能力',
    filterTitle: '分类',
    advantageTitle: '为什么选择我们'
  };

  // 获取案例列表
  const caseItems = contentData.cases || [];

  // 获取业务类型 - 从config中获取
  const businessTypes = pageConfig.businessTypes || ['复印机维修', '打印机维修', '设备保养', '紧急维修', '设备升级'];
  const categories = ['全部', ...businessTypes];



  // 获取优势数据
  const advantageItems = contentData.config?.advantages || [
    { icon: "⚡", title: "快速响应", description: "24小时内响应，紧急情况优先处理" },
    { icon: "🔧", title: "专业技术", description: "资深技师团队，丰富维修经验" },
    { icon: "✅", title: "质量保证", description: "维修质量保证，售后服务完善" }
  ];

  // 获取CTA配置
  const ctaConfig = {
    title: contentData.config?.ctaTitle || '需要维修服务？',
    subtitle: contentData.config?.ctaSubtitle || '立即联系我们，获得专业的设备维修解决方案',
    buttonText: contentData.config?.ctaButtonText || '立即联系我们'
  };

  // 公司信息 - 从案例内容中推断
  const companyInfo = {
    name: content.title || '复印机维修服务'
  };

  // 过滤案例
  const filteredCases = selectedCategory === '全部' 
    ? caseItems 
    : caseItems.filter((item: any) => item.category === selectedCategory);

  return (
    <>
      <Helmet>
        <title>{seoMeta?.title || `案例展示 - ${companyInfo.name}`}</title>
        <meta name="description" content={seoMeta?.description || `查看我们的${companyInfo.name}案例，了解专业的解决方案`} />
        {seoMeta?.keywords && <meta name="keywords" content={seoMeta.keywords} />}
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* 页面头部 - 全宽渐变与服务页一致 */}
        <div className="bg-gray-800 text-white py-16 text-center min-h-[276px]">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {pageConfig.heroTitle}
            </h1>
            <p className="text-xl mb-0 max-w-2xl mx-auto">
              {pageConfig.heroSubtitle}
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-6 md:py-12">
          {/* 案例分类筛选 */}
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-center mb-6">{pageConfig.filterTitle}</h2>
            <div className="flex flex-wrap justify-center gap-4">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-6 py-2 rounded-full border transition-colors ${
                    selectedCategory === category
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 hover:border-blue-600 hover:text-blue-600'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </section>

          {/* 案例列表 */}
          <section className="mb-16">
            {filteredCases.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">暂无 {selectedCategory} 相关案例</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredCases.map((caseItem: any, index: number) => (
                  <div key={caseItem.id || (caseItem.title + '-' + index)} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <img 
                      src={caseItem.image} 
                      alt={caseItem.title}
                      className="w-full h-48 object-cover"
                      onError={(e) => {
                        // 设置备用图片 - 使用更可靠的占位符服务
                        e.currentTarget.src = 'https://picsum.photos/400/300?grayscale&blur=1';
                      }}
                    />
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-2">
                        <span className="inline-block bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded" style={{ backgroundColor: '#dbeafe', color: '#1e40af' }}>
                          {caseItem.category}
                        </span>
                        {caseItem.date && (
                          <span className="text-gray-500 text-sm">{caseItem.date}</span>
                        )}
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {caseItem.title}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {caseItem.description}
                      </p>
                      {/* 标签渲染加类型保护 */}
                      {Array.isArray(caseItem.tags) && caseItem.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {caseItem.tags.map((tag: string, index: number) => (
                            <span key={tag + '-' + index} className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded" style={{ backgroundColor: '#f3f4f6', color: '#374151' }}>
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </section>

          {/* 服务优势 */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center mb-12">{pageConfig.advantageTitle}</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {advantageItems.map((advantage: any, index: number) => (
                <div key={index} className="text-center p-6 bg-white rounded-lg shadow-md">
                  <div className="text-4xl mb-4">{advantage.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{advantage.title}</h3>
                  <p className="text-gray-600">{advantage.description}</p>
                </div>
              ))}
            </div>
          </section>

          {/* 联系咨询 */}
          <section className="mt-16 text-center bg-blue-600 text-white rounded-lg p-12">
            <h2 className="text-3xl font-bold mb-4">{ctaConfig.title}</h2>
            <p className="text-xl mb-8">{ctaConfig.subtitle}</p>
            <a href="/contact" className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              {ctaConfig.buttonText}
            </a>
          </section>
        </div>
      </div>
    </>
  );
} 