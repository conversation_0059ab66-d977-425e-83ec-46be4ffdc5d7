<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:54
 * @Description: 
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleEdit(null, 'add')"
        >
          新增问题商品
        </el-button>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          collapse-tags
          filterable
          clearable
          :options="options"
          style="width: 500px"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #searchType>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.type"
          filterable
          :options="options1"
          style="width: 100%"
          :props="{
            label: 'label',
            value: 'value',
            children: 'children',
            expandTrigger: 'click',
          }"
          clearable
          leaf-only
          @change="handleChangeType"
        ></el-cascader>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <!--          <el-button icon="el-icon-edit">编辑</el-button>-->
          <el-button
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row.id)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="showDrawer"
      :title="drawerTitle"
      :confirm-button-disabled="formLoading"
      size="85%"
      confirm-text="确认新增"
      @ok="handleDrawerOk"
      @cancel="handleCloseDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="columns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
      >
        <template #goodsList>
          <ProTable
            ref="goodsProTable"
            :columns="goodsColumns"
            :data="goodsTableData"
            :show-pagination="false"
            :show-search="false"
            :show-setting="false"
            :show-loading="false"
            height="75vh"
          >
            <template #btn>
              <el-button
                type="success"
                size="mini"
                icon="el-icon-plus"
                @click="handleChooseGoods"
              >
                选择物品
              </el-button>
            </template>
            <template #number="{ row }">
              <el-input-number
                v-model="row.number"
                :min="1"
                size="mini"
                controls-position="right"
                style="width: 100%"
              ></el-input-number>
            </template>
            <template #description="{ row }">
              <el-input
                v-model="row.description"
                placeholder="请输入问题描述"
                type="textarea"
                autosize
                clearable
                maxlength="500"
                show-word-limit
                size="mini"
              ></el-input>
            </template>
            <template #actions="{ index }">
              <div class="fixed-width">
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-delete"
                  @click="handleDeleteGoods(index)"
                  >移除</el-button
                >
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 选择物品弹窗 -->
    <ProDialog
      :value="showDialog"
      title="选择物品"
      width="1000px"
      :confirm-loading="dialogLoading"
      confirm-text="确认选择"
      top="50px"
      @ok="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <ProTable
        ref="AddGoodsTable"
        row-key="id"
        :data="addGoodsTableData"
        :columns="addGoodsColumns"
        :height="400"
        :query-param="addGoodsQueryParam"
        :local-pagination="addGoodsLocalPagination"
        show-index
        show-selection
        show-search
        show-loading
        show-pagination
        @loadData="loadGoodsData"
        @handleSelectionChange="handleSelectionChange"
      >
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { dictTreeByCodeApi } from "@/api/user";
import { productThirdApi } from "@/api/dispose";
import {
  addProblemGoodsApi,
  deleteProblemGoodsApi,
  getProblemGoodsPageApi,
} from "@/api/goods";
import { cloneDeep } from "lodash";
import { Message } from "element-ui";
import { articlePageApi } from "@/api/store";

export default {
  name: "CheckIn",
  data() {
    return {
      productIdName: "",
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 1,
      },
      formLoading: false,
      columns: [
        {
          dataIndex: "productIds",
          isSearch: true,
          clearable: true,
          valueType: "product",
          title: "适用机型",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },

        {
          dataIndex: "type",
          title: "物品大小类",
          isTable: true,
          formatter: (row) => row.type?.label,
          isSearch: true,
          valueType: "select",
          searchSlot: "searchType",
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.manufacturerChannel.label,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "manufacturerCode",
          title: "供应商编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "goodsList",
          title: "选择问题商品",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "goodsList",
        },
        {
          dataIndex: "number",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "source",
          title: "问题来源",
          isTable: true,
          formatter: (row) => row.source?.label,
        },
        {
          dataIndex: "description",
          title: "问题描述",
          isTable: true,
          minWidth: 250,
        },
        {
          dataIndex: "createdBy",
          title: "登记人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "createdAt",
          title: "登记时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "description",
          title: "问题描述",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 150,
          tableSlot: "actions",
        },
      ],
      tableData: [],
      options: [],
      options1: [],
      includeList: [],
      methodType: "add",
      showDrawer: false,
      drawerTitle: "新增问题商品",
      form: {},
      goodsColumns: [
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "数量",
          isTable: true,
          tableSlot: "number",
          width: 100,
        },
        {
          dataIndex: "description",
          title: "问题描述",
          isTable: true,
          tableSlot: "description",
          minWidth: 250,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      goodsTableData: [],
      showDialog: false,
      dialogLoading: false,
      addGoodsQueryParam: {},
      addGoodsLocalPagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      addGoodsTableData: [],
      addGoodsColumns: [
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          width: 180,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          span: 4,
          width: 180,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel.label,
        },
      ],
      choosedata: [],
    };
  },
  mounted() {
    this.refresh();
    dictTreeByCodeApi(2100).then((res) => {
      res.data.forEach((item) => {
        if (item.value === "2101") {
          this.includeList.push(item.value);
          item.children.forEach((i) => {
            this.includeList.push(i.value);
          });
        }
      });
      this.options1 = res.data;
    });
    productThirdApi().then((res) => {
      this.options = res.data;
    });
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      getProblemGoodsPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
    },
    async loadGoodsData(params) {
      try {
        const result = await articlePageApi({
          ...params,
          warehouseId: 1731282648590000130,
        });
        if (result.code === 200 && result.data) {
          this.addGoodsTableData = result.data.rows;
          this.addGoodsLocalPagination = {
            pageNumber: params.pageNumber,
            pageSize: params.pageSize,
            total: +result.data.total,
          };
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.AddGoodsTable &&
          (this.$refs.AddGoodsTable.listLoading = false);
      }
    },
    handleDeleteGoods(index) {
      this.goodsTableData.splice(index, 1);
    },
    refreshAddGoodsTable() {
      this.$refs.AddGoodsTable.refresh();
    },
    handleDialogConfirm() {
      this.goodsTableData = [];
      this.choosedata.map((ele) => {
        this.goodsTableData.push({
          ...ele,
          batchCode: "",
          inWarehouseNumber: null,
        });
      });
      this.handleDialogCancel();
      this.dialogLoading = false;
    },
    handleDialogCancel() {
      this.showDialog = false;
    },
    async handleSelectionChange(row) {
      this.choosedata = row;
    },
    handleChooseGoods() {
      this.showDialog = true;
      this.addGoodsQueryParam = {};
      this.addGoodsLocalPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.$nextTick(() => {
        this.refreshAddGoodsTable();
        this.$refs.AddGoodsTable.$refs.ProElTable.clearSelection();
        this.choosedata = cloneDeep(this.goodsTableData);
        this.choosedata.map((row) => {
          this.$refs.AddGoodsTable.$refs.ProElTable.toggleRowSelection(row);
        });
      });
    },
    // 新增
    handleEdit(row, type) {
      this.methodType = type;
      this.showDrawer = true;
    },
    handleDrawerOk() {
      if (!this.goodsTableData.length) {
        this.$message.error("请选择物品");
        return;
      }
      const sign = this.goodsTableData.some(
        (item) => !item.number || !item.description
      );
      if (sign) {
        Message.error("请填写商品数量及其问题描述");
        return;
      }
      this.$confirm("是否确认新增?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.formLoading = true;
        const params = [];
        this.goodsTableData.map((item) => {
          params.push({
            articleCode: item.code,
            number: item.number,
            description: item.description,
          });
        });
        addProblemGoodsApi(params)
          .then((res) => {
            Message.success("新增成功");
            this.handleCloseDrawer();
            this.refresh();
          })
          .finally(() => {
            this.formLoading = false;
          });
      });
    },
    handleCloseDrawer() {
      this.choosedata = [];
      this.goodsTableData = [];
      this.showDrawer = false;
    },
    handleDelete(id) {
      this.$confirm("此操作将会删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteProblemGoodsApi(id).then((res) => {
          if (res.code === 200) {
            this.refresh();
            Message.success("删除成功");
          } else {
            Message.error(res.message);
          }
        });
      });
    },
    handleChangeType(val) {
      this.$set(this.queryParam, "type", val[val.length - 1]);
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        this.queryParam.productIds.push(el[el.length - 1]);
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
