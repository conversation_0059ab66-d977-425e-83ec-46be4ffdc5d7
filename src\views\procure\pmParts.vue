<!--
 * @Author: yangzhong
 * @Date: 2023-11-08 14:11:34
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-10-22 16:57:38
 * @Description: 
-->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :layout="{ labelWidth: '100px' }"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-index
      show-search
      show-loading
      isrules
      :show-pagination="false"
      @loadData="loadData"
    >
      <template #btn>
        <el-badge
          :value="selectionData.length"
          class="item"
          :hidden="selectionData.length === 0"
        >
          <el-button
            type="success"
            class="add-btn"
            size="mini"
            icon="el-icon-plus"
            @click="handlePurchaseOrder"
            >采购计划</el-button
          >
        </el-badge>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handlePart(row, 'info')"
            >查看物品</el-button
          >
        </div>
      </template>
      <template #machine>
        <el-cascader
          ref="ProductIds"
          v-model="machine"
          filterable
          clearable
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #stockNum>
        <el-input
          v-model="stockNumMini"
          :min="0"
          :controls="false"
          :max="1000000"
          label="库存数量"
          placeholder="最小库存"
          style="width: 100px"
        ></el-input>
        <span> - </span>
        <el-input
          v-model="stockNumMax"
          :min="0"
          :controls="false"
          :max="1000000"
          label="库存数量"
          clearable
          placeholder="最大库存"
          style="width: 100px"
        ></el-input>
      </template>
    </ProTable>
    <!--  物品弹窗 -->
    <ProDialog
      :value="showPartDialog"
      title="物品信息"
      width="1800px"
      :confirm-loading="partDialogLoading"
      top="150px"
      :no-footer="true"
      @cancel="showPartDialog = false"
    >
      <ProTable
        ref="ChoosePartTable"
        row-key="id"
        :data="choosePartData"
        :columns="partColumns"
        :show-loading="false"
        :show-pagination="false"
        :show-search="false"
        :height="500"
        :show-setting="false"
        @loadData="load2"
      >
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-edit-outline"
              @click="handleGoods(row)"
              >查看商品</el-button
            >
            <el-button
              v-if="
                !selectionData.find((item) => item.articleId === row.articleId)
              "
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="addSelection(row)"
              >加入采购计划</el-button
            >
            <el-button
              v-if="
                selectionData.find((item) => item.articleId === row.articleId)
              "
              type="danger"
              size="mini"
              icon="el-icon-delete"
              @click="removeSelection(row)"
              >移出采购计划</el-button
            >
          </div>
        </template>
      </ProTable>
    </ProDialog>
    <!-- 商品信息 -->
    <ProDialog
      :value="showGoodsDialog"
      title="商品信息"
      width="1200px"
      :confirm-loading="partDialogLoading"
      top="50px"
      :no-footer="true"
      @cancel="showGoodsDialog = false"
    >
      <ProTable
        ref="ChooseGoodsTable"
        row-key="id"
        :data="chooseGoodsData"
        :columns="chooseGoodsColumns"
        :show-pagination="false"
        :show-search="false"
        :height="400"
        @loadData="load3"
      >
        <template #btn>
          <div class="field">
            <div class="item">
              <div class="label">物品编号：</div>
              <div class="value">
                {{ chooseGoodsData[0] && chooseGoodsData[0].articleCode }}
              </div>
            </div>
            <div class="item">
              <div class="label">库品名称：</div>
              <div class="value">
                {{ chooseGoodsData[0] && chooseGoodsData[0].inventoryName }}
              </div>
            </div>
            <div class="item">
              <div class="label">原厂零件编号（OEM）：</div>
              <div class="value">
                {{ chooseGoodsData[0] && chooseGoodsData[0].numberOem }}
              </div>
            </div>
          </div>
        </template>
      </ProTable>
    </ProDialog>

    <!-- 新增采购单 -->
    <ProDialog
      :value="showAddDialog"
      title="新增采购单"
      width="80%"
      :confirm-loading="addLoading"
      top="50px"
      :no-footer="false"
      @cancel="showAddDialog = false"
      @ok="handleConfirmDialog"
    >
      <ProForm
        ref="addForm"
        :form-param="addForm"
        :form-list="addFormColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="'add'"
        @proSubmit="formSubmit"
      >
        <template #purchaseGoods>
          <ProTable
            ref="ChooseGoodsTable"
            row-key="id"
            :data="addData"
            :columns="addColumns"
            :show-pagination="false"
            :show-search="false"
            :show-loading="false"
            :show-setting="false"
            :height="400"
          >
            <template #manufacturerName="{ row }">
              <el-select
                v-if="row.manufacturer.length !== 0"
                v-model="row.manufacturerId"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in row.manufacturer"
                  :key="item.manufacturerId"
                  :label="item.manufacturer.name"
                  :value="item.manufacturerId"
                >
                </el-option>
              </el-select>
              <div v-else>/</div>
            </template>
            <template #number="{ row }">
              <el-input-number
                v-model="row.planNum"
                style="width: 100px"
                :controls="false"
              ></el-input-number>
            </template>
            <template #machine="slotProps">
              <div v-if="slotProps.row.manufacturer.length !== 0">
                <el-popover
                  placement="bottom"
                  title=""
                  width="700"
                  trigger="click"
                >
                  <div style="margin: 20px; height: 400px; overflow-y: scroll">
                    <el-descriptions
                      class="margin-top"
                      title="适用机型"
                      :column="1"
                      border
                    >
                      <el-descriptions-item
                        v-for="item in slotProps.row.manufacturer[0]
                          .productTreeDtoList"
                        :key="item.id"
                      >
                        <template slot="label"> 品牌/系列/机型 </template>
                        {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>

                  <el-button slot="reference" size="mini">适用机型</el-button>
                </el-popover>
              </div>
              <div v-else>/</div>
            </template>
            <template #price="{ row }">
              <div v-if="!row.manufacturerId">/</div>
              <div v-else>
                {{
                  row.manufacturer.find(
                    (item) => item.manufacturerId === row.manufacturerId
                  ).price
                }}
              </div>
            </template>
            <template #sumPrice="{ row }">
              <div v-if="!row.manufacturerId || !row.number">/</div>
              <div v-else>
                {{
                  +row.manufacturer.find(
                    (item) => item.manufacturerId === row.manufacturerId
                  ).price * row.number
                }}
              </div>
            </template>
            <template #action="{ row }">
              <div class="fixed-width">
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleRemoveAddData(row)"
                  >移除</el-button
                >
              </div>
            </template>
          </ProTable>
        </template>

        <template #deliveryTime>
          <el-date-picker
            v-model="addForm.deliveryTime"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择期望发货时间"
          >
          </el-date-picker>
        </template>

        <template #arrivalTime>
          <el-date-picker
            v-model="addForm.arrivalTime"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择期望到货时间"
          >
          </el-date-picker>
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import { Message, MessageBox } from "element-ui";
import OEMS from "@/views/engineer/components/oems.vue";
import { dictTreeByCodeApi, dictTreeByCodeApi2 } from "@/api/user";
import { isEmpty, cloneDeep } from "lodash";
import { warehouseListApi } from "@/api/store";

import {
  pmInvListApi,
  productAllApi,
  pmPartListApi,
  pmSkuListApi,
} from "@/api/dispose";
import { queryByArticleIdsApi, addPurchaseApi } from "@/api/procure";
export default {
  name: "PmParts",
  components: {},
  data() {
    return {
      options: [],
      columns: [
        {
          dataIndex: "warehouseName",
          title: "所属仓库",
          isTable: true,
          formatter: (row) => (row.warehouseName ? row.warehouseName : "/"),
          width: 150,
        },
        {
          dataIndex: "warehouseId",
          title: "所属仓库",
          isSearch: true,
          clearable: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择所属仓库",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "machine",
          isTable: false,
          isSearch: true,
          searchSlot: "machine",
          title: "机型",
          width: 100,
          prop: [
            {
              required: true,
              message: "请选择机型",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "spareLevel",
          title: "备件等级",
          isTable: false,
          width: 120,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          multiple: true,
          collapseTags: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(3400),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "repFrequency",
          title: "更换频次",
          isTable: false,
          width: 120,
          isSearch: true,
          collapseTags: true,
          valueType: "select",
          clearable: true,
          multiple: true,
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(3300),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: false,
          isSearch: false,
          width: 150,
          span: 5,
          valueType: "select",
          clearable: true,
          formatter: (row) => row.manufacturerChannel.label,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },

        {
          dataIndex: "articleCode",
          title: "物品编码",
          isTable: false,
          clearable: true,
          width: 150,
          span: 4,
          isSearch: false,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "制造商名称",
          isTable: false,
          width: 150,
        },
        {
          dataIndex: "oemNumber",
          title: "原厂零件编号",
          isTable: true,
          width: 150,
        },

        {
          dataIndex: "ch",
          title: "零件中文名称",
          width: 150,
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "machine",
          isTable: true,
          isSearch: false,
          searchSlot: "machine",
          title: "机型",
          width: 100,
          prop: [
            {
              required: true,
              message: "请选择机型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "unit",
          title: "所属单元",
          width: 150,
          isTable: true,
          isSearch: false,
          clearable: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(3200),
          formatter: (row) => row.unit.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "productPartType",
          title: "物品小类",
          width: 150,
          isTable: true,
          isSearch: false,
          clearable: true,
          valueType: "select",
          formatter: (row) => row.productPartType.label,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "spareLevel",
          title: "备件等级",
          isTable: true,
          width: 120,
          isSearch: false,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(3400),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "repFrequency",
          title: "更换频次",
          isTable: true,
          // width: 120,
          // isSearch: true,
          // valueType: "select",
          // clearable: true,
          // formSpan: 8,
          // option: [],
          // optionMth: () => dictTreeByCodeApi(3300),
          formatter: (row) => row.repFrequency.label,
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
        },
        {
          dataIndex: "correctedLifespan",
          title: "运营修正生命周期",
          isForm: true,
          isTable: true,
          valueType: "input",
          width: 180,
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入运营修正生命周期",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "stockNum",
          title: "库存量",
          width: 150,
          isTable: true,
          isSearch: true,
          formatter: (row) => (row.stockNum !== undefined ? row.stockNum : "/"),
          searchSlot: "stockNum",
        },
        {
          dataIndex: "unitList",
          title: "所属单元",
          width: 150,
          isTable: false,
          isSearch: true,
          clearable: true,
          valueType: "select",
          collapseTags: true,
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(3200),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "productPartTypeList",
          title: "物品小类",
          width: 150,
          isTable: false,
          isSearch: true,
          clearable: true,
          valueType: "select",
          collapseTags: true,
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "name",
          title: "原厂编号/零件",
          isTable: false,
          clearable: true,
          placeholder: "原厂零件编号/零件名称",
          span: 4,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 120,
        },
      ],
      tableData: [],
      localPagination: {
        page: 1,
        pageSize: 99999,
        total: 0,
      },
      data: {},
      data1: {},
      queryParam: {},
      showPartDialog: false,
      showGoodsDialog: false,
      partDialogLoading: false,
      partColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
        },
        {
          dataIndex: "inventoryName",
          title: "库品名称",
          isTable: true,
        },

        {
          dataIndex: "numberOem",
          title: "原厂零件编号（OEM）",
          isTable: true,
          width: 200,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          formatter: (row) =>
            row.manufacturerChannel ? row.manufacturerChannel.label : "-",
          width: 200,
        },
        {
          dataIndex: "name",
          title: "制造商名称",
          isTable: true,
        },
        {
          dataIndex: "stockNum",
          title: "库存",
          isTable: true,
        },
        {
          dataIndex: "name",
          title: "原厂零件编号/零件名称",
          isTable: false,
          clearable: true,
          span: 4,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 240,
        },
      ],
      choosePartData: [],
      goodsColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号	",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "inventoryName",
          title: "库品名称",
          isTable: true,
        },
        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
        },
        {
          dataIndex: "numberOem",
          title: "原厂零件编号（OEM）",
          isTable: true,
          width: 200,
        },
        {
          dataIndex: "stockNum",
          title: "库存",
          isTable: true,
        },
        {
          dataIndex: "skuId",
          title: "skuID",
          isTable: true,
        },
        {
          dataIndex: "warehouseName",
          title: "仓库名称",
          isTable: true,
        },
      ],
      chooseGoodsData: [],
      chooseGoodsColumns: [
        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
        },
        {
          dataIndex: "stockNum",
          title: "库存",
          isTable: true,
        },
        {
          dataIndex: "skuId",
          title: "skuID",
          isTable: true,
        },
        {
          dataIndex: "warehouseName",
          title: "仓库名称",
          isTable: true,
        },
      ],
      selectionData: [],
      showAddDialog: false,
      addLoading: false,
      addData: [],
      addColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "machine",
          title: "适用机型",
          isTable: true,
          width: "150",
          tableSlot: "machine",
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          tableSlot: "manufacturerName",
        },
        {
          dataIndex: "warehouseNumber",
          title: "库存量",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          tableSlot: "price",
        },
        {
          dataIndex: "planNum",
          title: "采购数量",
          isTable: true,
          tableSlot: "number",
        },
        {
          dataIndex: "sumPrice",
          title: "采购金额",
          isTable: true,
          tableSlot: "sumPrice",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
        },
      ],
      addForm: {},
      addFormColumns: [
        {
          dataIndex: "warehouseId",
          title: "选择仓库",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择仓库",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "purchaseGoods",
          title: "选择供应源",
          isForm: true,
          formSlot: "purchaseGoods",
        },
        {
          dataIndex: "deliveryTime",
          title: "期望发货时间",
          isForm: true,
          formSlot: "deliveryTime",
          // valueType: "date-picker",
          // valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请输入期望发货时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "arrivalTime",
          title: "期望到货时间",
          isForm: true,
          formSlot: "arrivalTime",
          // valueType: "date-picker",
          // valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 8,
          prop: [
            {
              required: true,
              message: "请输入期望发货时间",
              trigger: "change",
            },
          ],
        },
      ],
      formLoading: false,
      machine: [],
      lastIds: "",
      stockNumMini: "",
      stockNumMax: "",
    };
  },
  mounted() {
    productAllApi().then((res) => {
      this.options = res.data;
      this.$refs.ProTable.listLoading = false;
      // this.$refs.ProTable.refresh();
    });
  },
  methods: {
    handleSelect(item) {
      this.machine = item;
      // this.queryParam["machine"] = item;
      this.$set(this.queryParam, "machine", item);
      this.queryParam.lastIds = [item[item.length - 1]];
      this.lastIds = [item[item.length - 1]];
    },
    async loadData(params) {
      if (!this.lastIds || this.lastIds.length === 0) {
        this.$message.warning("请选择机型再查询！");
        this.$refs.ProTable.listLoading = false;
        return;
      }
      if (!params.warehouseId) {
        this.$message.warning("请选择仓库再查询！");
        this.$refs.ProTable.listLoading = false;
        return;
      }
      this.queryParam = {
        ...this.queryParam,
        ...params,
        lastIds: this.lastIds,
        stockNumMini: this.stockNumMini,
        stockNumMax: this.stockNumMax,
        pageNumber: 1,
        pageSize: 99999,
      };
      try {
        const result = await pmInvListApi(this.queryParam);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    handlePart(row) {
      this.data = cloneDeep(row);
      this.showPartDialog = true;
      setTimeout(() => {
        this.$refs.ChoosePartTable.refresh();
      }, 300);
    },
    handleGoods(row) {
      this.data1 = cloneDeep(row);
      this.showGoodsDialog = true;
      setTimeout(() => {
        this.$refs.ChooseGoodsTable.refresh();
      }, 300);
    },
    async load2(params) {
      try {
        const result = await pmPartListApi({
          partId: this.data.partId,
          warehouseId: this.queryParam.warehouseId,
        });
        if (result.code === 200 && result.data) {
          this.choosePartData = result.data;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ChoosePartTable &&
          (this.$refs.ChoosePartTable.listLoading = false);
      }
    },
    async load3(params) {
      try {
        const result = await pmSkuListApi({
          articleCode: this.data1.articleCode,
          warehouseId: this.queryParam.warehouseId,
        });
        if (result.code === 200 && result.data) {
          this.chooseGoodsData = result.data;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ChooseGoodsTable &&
          (this.$refs.ChooseGoodsTable.listLoading = false);
      }
    },
    async handlePurchaseOrder() {
      try {
        this.addLoading = true;
        if (!this.selectionData.length) {
          throw new Error("请选择商品");
        }
        console.log(this.queryParam);
        const result = await queryByArticleIdsApi(
          this.selectionData.map((item) => item.articleId)
        );
        this.addData = cloneDeep(this.selectionData).map((item) => ({
          ...item,
          oemNumber: item.numberOem,
          warehouseNumber: item.stockNum,
          manufacturer: result.data[item.articleId],
        }));
        console.log(this.addData);
        this.addForm.warehouseId = this.queryParam.warehouseId;
        this.showAddDialog = true;
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.addLoading = false;
      }
    },
    addSelection(row) {
      this.selectionData.push(row);
      Message.success("添加成功");
    },
    removeSelection(row) {
      const index = this.selectionData.findIndex(
        (item) => item.articleId === row.articleId
      );
      this.selectionData.splice(index, 1);
      Message.success("删除成功");
    },
    handleRemoveAddData(row) {
      this.removeSelection(row);
      const index = this.addData.findIndex(
        (item) => item.articleId === row.articleId
      );
      this.addData.splice(index, 1);
    },
    async formSubmit(val) {
      try {
        const userInfo = JSON.parse(localStorage.getItem("userInfo"));
        const list = this.addData.map((item) => {
          if (!item.manufacturerId) {
            throw new Error("请选择供应商");
          }
          if (!item.planNum) {
            throw new Error("请输入采购数量");
          }
          const temp = { ...item };
          delete item.manufacturer;
          return {
            ...item,
            price: temp.manufacturer.find(
              (item) => item.manufacturerId === temp.manufacturerId
            ).price,
          };
        });
        const args = {
          ...this.addForm,
          purchaseGoods: list,
          initiatorId: userInfo.id,
          initiatorName: userInfo.name,
        };
        const result = await addPurchaseApi(args);
        if (result.code === 200) {
          Message.success("添加成功");
          this.selectionData = [];
          this.addData = [];
          this.showAddDialog = false;
          this.addForm = {};
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleConfirmDialog() {
      this.$refs.addForm.handleSubmit();
    },
  },
};
</script>

<style scoped>
.field {
  display: flex;
}
.item {
  display: flex;
}
.item + .item {
  margin-left: 15px;
}
</style>
