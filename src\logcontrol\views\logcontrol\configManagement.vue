<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 配置管理主页面
-->
<template>
  <div class="config-management app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">配置管理</h1>
        <p class="page-description">管理日志配置和分配情况</p>
      </div>
      <div class="header-actions">

        <el-button type="primary" @click="showBatchAssignDialog">
          <i class="el-icon-setting"></i>
          批量分配
        </el-button>
        <el-button @click="showCreateConfigDialog">
          <i class="el-icon-plus"></i>
          创建配置
        </el-button>
        <el-button @click="refreshData" :loading="loading">
          <i class="el-icon-refresh"></i>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 配置列表区域 -->
    <el-card class="config-section">
      <div slot="header" class="section-header">
        <span>配置列表</span>
        <div class="header-actions">
          <el-tag type="success">{{ configs.length }} 个配置</el-tag>
          <!-- <el-button size="small" type="primary" @click="createNewConfig">
            <i class="el-icon-plus"></i>
            新建配置
          </el-button> -->
        </div>
      </div>

      <el-table :data="configs" v-loading="configsLoading" stripe style="width: 100%">
        <el-table-column prop="configName" label="配置名称" min-width="120" show-overflow-tooltip />
        <el-table-column prop="logLevel" label="日志级别" width="110">
          <template slot-scope="scope">
            <el-tag :type="getLogLevelType(scope.row.logLevel)" size="small">
              {{ scope.row.logLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enableLocationLog" label="位置日志" width="110">
          <template slot-scope="scope">
            <el-tag :type="scope.row.enableLocationLog ? 'success' : 'info'" size="mini">
              {{ scope.row.enableLocationLog ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="locationLogInterval" label="位置间隔" min-width="110">
          <template slot-scope="scope">
            {{ formatInterval(scope.row.locationLogInterval) }}
          </template>
        </el-table-column>
        <el-table-column prop="logUploadInterval" label="上传间隔" min-width="110">
          <template slot-scope="scope">
            {{ formatInterval(scope.row.logUploadInterval) }}
          </template>
        </el-table-column>
        <el-table-column prop="maxLogFiles" label="最大文件" width="110">
          <template slot-scope="scope">
            {{ scope.row.maxLogFiles }} 个
          </template>
        </el-table-column>
        <el-table-column prop="configVersion" label="版本" min-width="110" show-overflow-tooltip />
        <el-table-column prop="isActive" label="状态" width="110">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'info'" size="mini">
              {{ scope.row.isActive ? '激活' : '未激活' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <div class="fixed-width">
              <el-button size="mini" type="primary" @click="editConfig(scope.row)">编辑</el-button>
              <el-button
                size="mini"
                type="primary"
                @click="activateConfig(scope.row)"
                :disabled="scope.row.isActive"
              >
                激活
              </el-button>
              <el-button size="mini" type="danger" @click="deleteConfig(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>



    <!-- 配置分配区域 -->
    <el-card class="assignment-section">
      <div slot="header" class="section-header">
        <span>配置分配情况</span>
        <div class="search-bar">
          <el-select v-model="searchForm.targetType" placeholder="类型" clearable @change="loadAssignments">
            <el-option label="用户" value="USER" />
            <el-option label="设备" value="DEVICE" />
          </el-select>
          <el-select v-model="searchForm.distributionStatus" placeholder="分发状态" clearable @change="loadAssignments">
            <el-option label="待应用" value="PENDING" />
            <el-option label="已分配" value="ASSIGNED" />
            <el-option label="已应用" value="APPLIED" />
          </el-select>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索关键词"
            clearable
            @keyup.enter.native="loadAssignments"
            @clear="loadAssignments"
          >
            <el-button slot="append" icon="el-icon-search" @click="loadAssignments" />
          </el-input>
        </div>
      </div>

      <!-- 分配列表表格 -->
      <el-table :data="assignments" v-loading="loading" stripe border style="width: 100%">
        <el-table-column prop="targetType" label="类型" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.targetType === 'USER' ? 'primary' : 'warning'" size="small">
              {{ scope.row.targetType === 'USER' ? '用户' : '设备' }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="targetId" label="目标ID" min-width="100" show-overflow-tooltip /> -->
        <el-table-column prop="targetName" label="目标名称" min-width="220" show-overflow-tooltip />
        <el-table-column prop="configName" label="配置名称" min-width="90" show-overflow-tooltip />
        <el-table-column prop="logLevel" label="日志级别" width="100">
          <template slot-scope="scope">
            <el-tag :type="getLogLevelType(scope.row.logLevel)" size="small">
              {{ scope.row.logLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="distributionStatus" label="分发状态" width="110">
          <template slot-scope="scope">
            <el-tag :type="getDistributionStatusType(scope.row.distributionStatus)" size="small">
              {{ getDistributionStatusText(scope.row.distributionStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isActive" label="激活状态" width="110">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'info'" size="small">
              {{ scope.row.isActive ? '已激活' : '未激活' }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="distributionId" label="分发ID" width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="distribution-id">{{ scope.row.distributionId }}</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="assignTime" label="分配时间" min-width="140" show-overflow-tooltip />
        <el-table-column label="操作" width="180" fixed="right">
          <template slot-scope="scope">
            <div class="fixed-width">


              <el-button size="small" type="primary" @click="handleEdit(scope.row)">
                <i class="el-icon-edit"></i>
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="handleRemove(scope.row)">
                <i class="el-icon-delete"></i>
                移除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="pagination.current"
          :page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量分配对话框 -->
    <batch-assign-dialog
      :visible.sync="batchAssignDialog"
      :configs="configs"
      @confirm="handleBatchAssign"
    />





    <!-- 配置表单对话框 -->
    <config-form-dialog
      :visible.sync="configFormDialog"
      :config="selectedConfig"
      @confirm="handleConfigSave"
    />

    <!-- 分发关系编辑对话框 -->
    <distribution-edit-dialog
      :visible.sync="distributionEditDialog"
      :distribution="editingDistribution"
      @success="handleDistributionEdited"
    />
  </div>
</template>

<script>
import BatchAssignDialog from '@/logcontrol/components/ConfigManagement/BatchAssignDialog.vue'
import ConfigFormDialog from '@/logcontrol/components/ConfigManagement/ConfigFormDialog.vue'
import DistributionEditDialog from '@/logcontrol/components/ConfigManagement/DistributionEditDialog.vue'
import { configApi, DISTRIBUTION_STATUS_TEXT, DISTRIBUTION_STATUS_TYPE } from '@/logcontrol/api/configApi'

export default {
  name: 'ConfigManagement',
  components: {
    BatchAssignDialog,
    ConfigFormDialog,
    DistributionEditDialog
  },
  data() {
    return {
      loading: false,
      configsLoading: false,

      // 配置数据
      configs: [],
      selectedConfig: null,

      // 分配数据
      assignments: [],
      searchForm: {
        targetType: '',
        distributionStatus: '',
        keyword: ''
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },

      // 对话框状态
      batchAssignDialog: false,
      configFormDialog: false,
      distributionEditDialog: false,
      editingConfig: null,
      editingDistribution: null,


    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      await Promise.all([
        this.loadConfigs(),
        this.loadAssignments()
      ])
    },

    // 加载配置列表
    async loadConfigs() {
      this.configsLoading = true
      try {
        const response = await configApi.getConfigList()
        this.configs = response.data || []
      } catch (error) {
        console.error('加载配置列表失败:', error)
        this.$message.error('加载配置列表失败')
      } finally {
        this.configsLoading = false
      }
    },



    // 加载配置分配情况
    async loadAssignments() {
      this.loading = true
      try {
        const params = {
          targetType: this.searchForm.targetType,
          distributionStatus: this.searchForm.distributionStatus,
          keyword: this.searchForm.keyword,
          page: this.pagination.current,
          size: this.pagination.size
        }

        const response = await configApi.getAssignments(params)
        this.assignments = response.data.records || response.data || []
        this.pagination.total = response.data.total || 0
      } catch (error) {
        console.error('加载分配情况失败:', error)
        this.$message.error('加载分配情况失败')
      } finally {
        this.loading = false
      }
    },



    // 显示批量分配对话框
    showBatchAssignDialog() {
      this.batchAssignDialog = true
    },

    // 显示创建配置对话框
    showCreateConfigDialog() {
      this.editingConfig = null
      this.configFormDialog = true
    },

    // 处理批量分配
    async handleBatchAssign(assignData) {
      try {
        const response = await configApi.batchAssign(assignData)
        const result = response.data

        this.$message.success(
          `批量分配完成：成功 ${result.success}，失败 ${result.failed}`
        )

        this.batchAssignDialog = false
        this.loadAssignments()
      } catch (error) {
        console.error('批量分配失败:', error)
        this.$message.error('批量分配失败')
      }
    },



    // 编辑分发关系
    handleEdit(assignment) {
      this.editingDistribution = assignment
      this.distributionEditDialog = true
    },

    // 处理分发关系编辑成功
    handleDistributionEdited() {
      this.loadAssignments()
      this.distributionEditDialog = false
    },



    // 移除配置分配
    async handleRemove(assignment) {
      try {
        await this.$confirm('确定要移除此配置分配吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 优先使用新的分发ID删除接口
        if (assignment.distributionId) {
          await configApi.deleteDistributionById(assignment.distributionId)
        } else {
          // 兼容旧的删除方式
          await configApi.removeAssignment(assignment.targetType, assignment.targetId)
        }

        this.$message.success('移除成功')
        this.loadAssignments()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('移除失败:', error)
          this.$message.error('移除失败')
        }
      }
    },

    // 保存配置
    async handleConfigSave(configData) {
      try {
        if (configData.id) {
          await configApi.updateConfig(configData)
          this.$message.success('更新成功')
        } else {
          await configApi.createConfig(configData)
          this.$message.success('创建成功')
        }

        this.configFormDialog = false
        this.loadAssignments()
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      }
    },

    // 分页处理
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadAssignments()
    },

    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadAssignments()
    },

    // 刷新数据
    async refreshData() {
      try {
        await this.initData()
        this.$message.success('数据刷新成功')
      } catch (error) {
        console.error('刷新数据失败:', error)
        this.$message.error('数据刷新失败')
      }
    },

    // 配置管理方法
    createNewConfig() {
      this.selectedConfig = {
        id: null,  // 新建时设为null
        configName: '',
        logLevel: 'INFO',
        enableLocationLog: true,
        locationLogInterval: 3000,
        logUploadInterval: 3600,
        maxLogFiles: 5,
        configVersion: '1.0.0',
        isActive: false
      }
      this.configFormDialog = true
    },



    // 编辑配置
    editConfig(config) {
      // 直接使用列表中的配置数据，避免额外的API调用和CORS问题
      // 配置列表数据已包含编辑所需的所有字段
      this.selectedConfig = { ...config }
      this.configFormDialog = true
    },

    async activateConfig(config) {
      try {
        await this.$confirm(
          `确定要激活配置 "${config.configName}" 吗？\n\n注意：激活此配置将自动停用其他所有配置，每个系统只能有一个激活的配置。`,
          '激活配置确认',
          {
            confirmButtonText: '确定激活',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: false
          }
        )

        const loadingMessage = this.$loading({
          lock: true,
          text: '正在激活配置...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        try {
          await configApi.activateConfig(config.id)
          loadingMessage.close()
          this.$message.success('配置激活成功，其他配置已自动停用')
          this.loadConfigs()
        } catch (apiError) {
          loadingMessage.close()
          throw apiError
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('激活配置失败:', error)
          if (error.response && error.response.data && error.response.data.message) {
            this.$message.error(`激活失败：${error.response.data.message}`)
          } else {
            this.$message.error('激活配置失败，请检查网络连接')
          }
        }
      }
    },

    async deleteConfig(config) {
      try {
        await this.$confirm(`确定要删除配置 "${config.configName}" 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await configApi.deleteConfig(config.id)
        this.$message.success('配置删除成功')
        this.loadConfigs()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除配置失败:', error)
          this.$message.error('删除配置失败')
        }
      }
    },



    // 工具方法
    getLogLevelType(level) {
      const types = {
        'DEBUG': 'info',
        'INFO': 'success',
        'WARN': 'warning',
        'ERROR': 'danger'
      }
      return types[level] || ''
    },

    // 分发状态相关方法
    getDistributionStatusType(status) {
      return DISTRIBUTION_STATUS_TYPE[status] || 'info'
    },

    getDistributionStatusText(status) {
      return DISTRIBUTION_STATUS_TEXT[status] || '未知'
    },

    formatInterval(interval) {
      if (!interval) return '未设置'

      // 后端已改为秒，直接处理
      const seconds = interval

      if (seconds < 60) return `${seconds}秒`

      const minutes = Math.floor(seconds / 60)
      if (minutes < 60) return `${minutes}分钟`

      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60

      if (remainingMinutes > 0) {
        return `${hours}小时${remainingMinutes}分钟`
      }
      return `${hours}小时`
    },

    // 处理配置保存
    async handleConfigSave(configData) {
      try {
        // 验证必填字段
        if (!configData.configName) {
          this.$message.error('配置名称不能为空')
          return
        }
        if (!configData.logLevel) {
          this.$message.error('日志级别不能为空')
          return
        }
        if (configData.enableLocationLog === undefined || configData.enableLocationLog === null) {
          this.$message.error('位置日志设置不能为空')
          return
        }
        if (!configData.locationLogInterval || configData.locationLogInterval <= 0) {
          this.$message.error('位置日志间隔必须大于0')
          return
        }
        if (!configData.logUploadInterval || configData.logUploadInterval <= 0) {
          this.$message.error('上传间隔必须大于0')
          return
        }
        if (!configData.maxLogFiles || configData.maxLogFiles <= 0) {
          this.$message.error('最大文件数量必须大于0')
          return
        }


        await configApi.updateConfig(configData)
        this.$message.success(configData.id ? '配置更新成功' : '配置创建成功')
        this.configFormDialog = false
        this.loadConfigs()
      } catch (error) {


        // 处理限流错误
        if (error.response && error.response.status === 429) {
          this.$message.error('操作过于频繁，请稍后再试（每分钟最多10次）')
        } else if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(`保存失败：${error.response.data.message}`)
        } else {
          this.$message.error('保存配置失败，请检查网络连接')
        }
      }
    },



  }
}
</script>

<style lang="scss" scoped>
.config-management {
  padding: 20px;
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      .page-title {
        margin: 0 0 5px 0;
        font-size: 24px;
        font-weight: 500;
        color: #303133;
      }

      .page-description {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }

    .header-actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }

  .config-section {
    margin-bottom: 20px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }

    // 表格容器
    .el-table {
      width: 100%;

      // 确保表格在小屏幕上可以横向滚动
      .el-table__body-wrapper {
        overflow-x: auto;
      }
    }
  }

  .assignment-section {
    margin-bottom: 20px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .search-bar {
        display: flex;
        align-items: center;
        gap: 10px;

        .el-select {
          width: 120px;
        }

        .el-input {
          width: 200px;
        }

        .el-select {
          margin-right: 10px;
        }
      }
    }

    // 表格容器
    .el-table {
      width: 100%;

      // 确保表格在小屏幕上可以横向滚动
      .el-table__body-wrapper {
        overflow-x: auto;
      }
    }
  }



  .assignment-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .search-bar {
        display: flex;
        gap: 10px;

        .el-select {
          width: 120px;
        }

        .el-input {
          width: 200px;
        }
      }
    }

    .pagination-wrapper {
      margin-top: 20px;
      text-align: right;
    }
  }

  // 分发ID样式
  .distribution-id {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #606266;
    background-color: #f5f7fa;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #e4e7ed;
  }



}
</style>
