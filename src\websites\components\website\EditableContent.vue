<template>
  <div class="editable-content" :class="{ 'editing': isEditing }">
    <!-- 编辑模式 -->
    <div v-if="isEditing" class="edit-mode">
      <!-- 多行文本编辑 -->
      <el-input
        v-if="multiline"
        type="textarea"
        v-model="editValue"
        :placeholder="placeholder"
        :rows="rows"
        :autosize="autosize"
        @keydown.ctrl.enter="handleSave"
        @keydown.meta.enter="handleSave"
        ref="editInput"
      />
      <!-- 单行文本编辑 -->
      <el-input
        v-else
        v-model="editValue"
        :placeholder="placeholder"
        @keydown.enter="handleSave"
        ref="editInput"
      />
      
      <!-- 编辑操作按钮 -->
      <div class="edit-actions">
        <el-button size="mini" type="primary" @click="handleSave">
          保存
        </el-button>
        <el-button size="mini" @click="handleCancel">
          取消
        </el-button>
      </div>
    </div>
    
    <!-- 显示模式 -->
    <div 
      v-else 
      class="display-mode"
      :class="displayClass"
      :style="displayStyle"
      @click="startEdit"
      @mouseenter="showEditHint = true"
      @mouseleave="showEditHint = false"
    >
      <!-- 内容显示 -->
      <span v-if="displayValue" v-html="formattedValue"></span>
      <span v-else class="placeholder-text">{{ placeholder }}</span>
      
      <!-- 编辑提示 -->
      <div v-if="showEditHint" class="edit-hint">
        <i class="el-icon-edit"></i>
        点击编辑
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EditableContent',
  props: {
    // 内容值
    value: {
      type: String,
      default: ''
    },
    // 是否多行
    multiline: {
      type: Boolean,
      default: false
    },
    // 占位符
    placeholder: {
      type: String,
      default: '点击编辑'
    },
    // 显示样式类名
    displayClass: {
      type: String,
      default: ''
    },
    // 显示样式
    displayStyle: {
      type: Object,
      default: () => ({})
    },
    // 文本域行数
    rows: {
      type: Number,
      default: 3
    },
    // 自动调整高度
    autosize: {
      type: [Boolean, Object],
      default: () => ({ minRows: 2, maxRows: 6 })
    },
    // 是否支持HTML格式化
    allowHtml: {
      type: Boolean,
      default: false
    },
    // 是否禁用编辑
    disabled: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      isEditing: false,
      editValue: '',
      showEditHint: false
    }
  },
  
  computed: {
    displayValue() {
      return this.value || ''
    },
    
    formattedValue() {
      if (!this.displayValue) return ''
      
      if (this.allowHtml) {
        return this.displayValue
      } else {
        // 转义HTML并保留换行
        return this.displayValue
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/\n/g, '<br>')
      }
    }
  },
  
  watch: {
    value: {
      handler(newVal) {
        this.editValue = newVal || ''
      },
      immediate: true
    }
  },
  
  methods: {
    // 开始编辑
    startEdit() {
      if (this.disabled) return
      
      this.editValue = this.value || ''
      this.isEditing = true
      this.showEditHint = false
      
      // 下一帧聚焦输入框
      this.$nextTick(() => {
        if (this.$refs.editInput) {
          this.$refs.editInput.focus()
          // 选中所有文本
          if (this.$refs.editInput.$refs.input) {
            this.$refs.editInput.$refs.input.select()
          } else if (this.$refs.editInput.$refs.textarea) {
            this.$refs.editInput.$refs.textarea.select()
          }
        }
      })
    },
    
    // 保存编辑
    handleSave() {
      const newValue = this.editValue.trim()
      
      // 如果值有变化，触发更新
      if (newValue !== this.value) {
        this.$emit('input', newValue)
        this.$emit('change', newValue)
      }
      
      this.isEditing = false
    },
    
    // 取消编辑
    handleCancel() {
      this.editValue = this.value || ''
      this.isEditing = false
    },
    
    // 处理点击外部
    handleClickOutside(event) {
      if (this.isEditing && !this.$el.contains(event.target)) {
        this.handleSave()
      }
    }
  },
  
  mounted() {
    // 监听全局点击事件
    document.addEventListener('click', this.handleClickOutside)
  },
  
  beforeDestroy() {
    // 清理事件监听
    document.removeEventListener('click', this.handleClickOutside)
  }
}
</script>

<style lang="scss" scoped>
.editable-content {
  position: relative;
  
  .edit-mode {
    .edit-actions {
      margin-top: 8px;
      display: flex;
      gap: 8px;
    }
  }
  
  .display-mode {
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    border-radius: 4px;
    padding: 4px 8px;
    min-height: 24px;
    display: inline-block;
    width: 100%;

    &:hover {
      border-color: #409eff;
      background-color: rgba(64, 158, 255, 0.05);
    }

    // 确保内容区域可以正确传递点击事件
    span {
      pointer-events: none;
      display: inline-block;
      width: 100%;
    }

    .placeholder-text {
      color: #c0c4cc;
      font-style: italic;
    }
    
    .edit-hint {
      position: absolute;
      top: calc(100% + 8px);
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      z-index: 1000;

      &::before {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-bottom-color: rgba(0, 0, 0, 0.8);
      }
    }
  }
  
  &.editing .display-mode {
    display: none;
  }
}

// 特殊样式类
.editable-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  margin-bottom: 16px;
}

// 英雄区样式已移至全局样式块

.editable-section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.editable-description {
  color: #6b7280;
  line-height: 1.6;
}

.editable-list-item {
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
  
  &:last-child {
    border-bottom: none;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .editable-hero-title {
    font-size: 24px;
  }
  
  .editable-hero-subtitle {
    font-size: 16px;
  }
  
  .editable-title {
    font-size: 20px;
  }
}
</style>

<!-- 全局样式 - 英雄区统一样式 -->
<style lang="scss">
.editable-hero-title {
  font-size: 32px !important;
  font-weight: 700 !important;
  color: white !important;
  text-align: center !important;
  margin-bottom: 16px !important;
}

.editable-hero-subtitle {
  font-size: 18px !important;
  color: white !important;
  text-align: center !important;
  opacity: 0.9 !important;
}

.hero-title-unified {
  font-size: 32px !important;
  font-weight: 700 !important;
  color: white !important;
  text-align: center !important;
  margin-bottom: 16px !important;
}

.hero-subtitle-unified {
  font-size: 18px !important;
  color: white !important;
  text-align: center !important;
  opacity: 0.9 !important;
}

.hero-title {
  font-size: 32px !important;
  font-weight: 700 !important;
  color: white !important;
  text-align: center !important;
  margin-bottom: 16px !important;
}

.hero-subtitle {
  font-size: 18px !important;
  color: white !important;
  text-align: center !important;
  opacity: 0.9 !important;
}

.hero-title-preview {
  font-size: 32px !important;
  font-weight: 700 !important;
  color: white !important;
  text-align: center !important;
  margin-bottom: 16px !important;
}

.hero-subtitle-preview {
  font-size: 18px !important;
  color: white !important;
  text-align: center !important;
  opacity: 0.9 !important;
}

// 页面标题统一样式
.editable-page-title {
  font-size: 24px !important;
  font-weight: 600 !important;
  color: #303133 !important;
  text-align: center !important;
  margin-bottom: 16px !important;
}

.text-3xl {
  font-size: 30px !important;
  line-height: 36px !important;
}

.font-bold {
  font-weight: 700 !important;
}

.text-center {
  text-align: center !important;
}

.mb-6 {
  margin-bottom: 24px !important;
}

.block {
  display: block !important;
}
</style>
