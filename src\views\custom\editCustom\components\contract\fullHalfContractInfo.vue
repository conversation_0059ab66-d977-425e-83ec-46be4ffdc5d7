<!--
 * @Author: wskg
 * @Date: 2025-01-15 11:59:00
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 购机合同明细
 -->
<template>
  <div class="app-container">
    <ProDialog
      :value="dialogVisible"
      title="抄表合约明细"
      width="75%"
      top="1%"
      :confirm-btn-loading="confirmLoading"
      :no-footer="editType === 'info'"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="ProForm"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :form-param="form"
        :form-list="formColumns"
        :open-type="editType"
        @proSubmit="formSubmit"
      >
        <template #deviceGroup>
          {{ form.deviceGroup?.label }}
        </template>
        <template #hasGive>
          <el-radio-group
            v-model="form.hasGive"
            :disabled="editType === 'info'"
          >
            <el-radio :label="false"> 无赠送 </el-radio>
            <el-radio :label="true"> 有赠送 </el-radio>
          </el-radio-group>
        </template>
        <!-- 赠送服务 -->
        <template v-if="form.hasGive" #customerContractGives>
          <GiftService
            ref="giftServiceRef"
            v-model="form"
            :edit-type="editType"
          />
        </template>
        <template #serviceInfo>
          <!-- 分期付款 -->
          <!--<InstallmentPayment-->
          <!--  v-if="form.settleMethod === 'INSTALLMENT'"-->
          <!--  v-model="form"-->
          <!--/>-->
          <!-- 服务类型 -->
          <!-- 服务类型：质保 -->
          <!--<WarrantyContract v-if="form.serType === 'WARRANTY'" v-model="form" />-->
          <!-- 服务类型：全/半保 -->
          <FullHalfGuaranteed
            v-if="getServiceType(form.serType)"
            ref="serviceRef"
            v-model="form"
            :service-type="form.serType"
            :contract-type="contractType"
            :edit-type="editType"
          />
          <!-- 服务类型: 其它 -->
          <OtherService
            v-if="form.serType === 'OTHER'"
            ref="otherRef"
            v-model="form"
            :edit-type="editType"
          />
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import FullHalfGuaranteed from "@/views/custom/editCustom/components/contract/fullHalfGuaranteed.vue";
import OtherService from "@/views/custom/editCustom/components/contract/otherService.vue";
import GiftService from "@/views/custom/editCustom/components/contract/giftService.vue";
import { addAmount, filterParam, subtractAmount } from "@/utils";
import { cloneDeep } from "lodash";

export default {
  name: "FullHalfContractInfo",
  components: { GiftService, FullHalfGuaranteed, OtherService },
  props: {
    isSupplement: {
      type: Boolean,
      default: false,
    },
    contractType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      dialogVisible: false,
      // form
      confirmLoading: false,
      form: {},
      defaultForm: {
        contractBuyGive: {}, // 购机明细
        contractServeGive: {}, // 服务明细
      },
      editType: "add",
      formColumns: [
        {
          dataIndex: "deviceSeqId",
          title: "设备组编号",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isForm: true,
          formSlot: "deviceGroup",
          formSpan: 8,
        },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          tooltipContent:
            "服务类型的切换可能会清除一些影响程序运行的数据，因此请谨慎操作，避免随意更改服务类型。",
          option: [
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "其它",
              value: "OTHER",
            },
          ],
          prop: [
            {
              required: true,
              message: "请选择服务类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "hasGive",
          title: "有无赠送",
          isForm: true,
          formSlot: "hasGive",
          formSpan: 6,
        },
        {
          dataIndex: "customerContractGives",
          title: "赠送物品",
          isForm: true,
          formOtherSlot: "customerContractGives",
          formSlot: 24,
        },
        {
          dataIndex: "serviceInfo",
          title: "服务信息",
          isForm: true,
          formOtherSlot: "serviceInfo",
          formSpan: 24,
        },
      ],
    };
  },
  watch: {
    // isSupplement: {
    //   handler(val) {
    //     if (val) {
    //       this.updateIsFormColumn(this.formColumns, ["addMachin"], false);
    //     } else {
    //       this.updateIsFormColumn(this.formColumns, ["addMachin"], true);
    //     }
    //   },
    //   immediate: true,
    // },
    // 总销售金额
    "form.fullAmount": {
      handler(val) {
        this.calculateArrearsAmount();
      },
    },
    // 定金
    "form.depositAmount": {
      handler(val) {
        this.calculateArrearsAmount();
      },
    },
    // 折扣金额
    "form.discountAmount": {
      handler(val) {
        this.calculateArrearsAmount();
      },
    },
  },
  methods: {
    visible(val, type) {
      this.editType = type;
      this.resetForm().then(() => {
        const formParams = {
          ...this.defaultForm,
          ...val,
        };
        Object.keys(formParams).forEach((key) => {
          if (key === "deviceGroup") {
            return;
          }
          formParams[key] = formParams[key]?.label
            ? formParams[key].value
            : formParams[key];
        });
        this.form = formParams;
        if (this.form.hasGive === undefined || this.form.hasGive === null) {
          this.$set(this.form, "hasGive", false);
        }
        this.dialogVisible = true;
      });
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    formSubmit(val) {
      const validationPromises = [];
      if (this.$refs.serviceRef && this.$refs.serviceRef.$refs.formRef) {
        validationPromises.push(
          new Promise((resolve) => {
            this.$refs.serviceRef.$refs.formRef.validate((valid) => {
              resolve(valid);
            });
          })
        );
      }
      if (this.$refs.otherRef && this.$refs.otherRef.$refs.formRef) {
        validationPromises.push(
          new Promise((resolve) => {
            this.$refs.otherRef.$refs.formRef.validate((valid) => {
              resolve(valid);
            });
          })
        );
      }
      // 等待所有表单验证完成
      Promise.all(validationPromises).then((results) => {
        if (results.every((result) => result)) {
          const result = cloneDeep(this.form);
          if (result.settleMethod === "INSTALLMENT") {
            if (!result.tradeOrderInstallments.length) {
              this.$message.error("请填写分期信息");
              return;
            }
            // 检查分期列表的总金额是否等于欠款金额
            const totalAmount = result.tradeOrderInstallments.reduce(
              (acc, cur) => {
                return addAmount(acc, cur.amount);
              },
              0
            );
            if (totalAmount !== result.arrearsAmount) {
              this.$message.error("分期金额总和必须等于欠款金额");
              return;
            }
          }
          // if (result.priceType === "LADDER") {
          //   if (!result.repairMonthlyPrices.length) {
          //     return this.$message.error("请填写阶梯价格");
          //   }
          // }
          this.$emit("confirmContractInfo", filterParam(result));
          this.dialogVisible = false;
        } else {
          this.$message.error("请将合约信息填写完整");
        }
      });
    },
    submitForm() {
      const result = cloneDeep(this.form);
      if (result.settleMethod === "INSTALLMENT") {
        if (!result.tradeOrderInstallments.length) {
          this.$message.error("请填写分期信息");
          return;
        }
        // 检查分期列表的总金额是否等于欠款金额
        const totalAmount = result.tradeOrderInstallments.reduce((acc, cur) => {
          return addAmount(acc, cur.amount);
        }, 0);
        if (totalAmount !== result.arrearsAmount) {
          this.$message.error("分期金额总和必须等于欠款金额");
          return;
        }
      }
      this.$emit("confirmContractInfo", result);
      this.dialogVisible = false;
    },
    handleDialogCancel() {
      this.dialogVisible = false;
    },
    getServiceType(type) {
      return ["HALF", "ALL", "PACKAGE_HALF", "PACKAGE_ALL"].includes(type);
    },
    calculateArrearsAmount() {
      const fullAmount = this.form.fullAmount || 0;
      const depositAmount = this.form.depositAmount || 0;
      const discountAmount = this.form.discountAmount || 0;
      if (fullAmount) {
        let arrearsAmount = fullAmount;

        if (depositAmount) {
          arrearsAmount = subtractAmount(arrearsAmount, depositAmount);
        }
        if (discountAmount) {
          arrearsAmount = subtractAmount(arrearsAmount, discountAmount);
        }
        this.form.arrearsAmount = Math.max(arrearsAmount, 0);
      } else {
        this.form.arrearsAmount = 0;
      }
    },
    updateIsFormColumn(columns, keys, isForm) {
      columns.forEach((item) => {
        if (keys.includes(item.dataIndex)) {
          item.isForm = isForm;
        }
      });
    },
    resetForm() {
      return new Promise((resolve) => {
        Object.keys(this.form).forEach((key) => {
          delete this.form[key];
        });
        resolve();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.billing-method {
  .mb-0 {
    margin-bottom: 0;
  }

  .installment-details {
    .el-input-number {
      width: 100%;
    }
  }
}
</style>
