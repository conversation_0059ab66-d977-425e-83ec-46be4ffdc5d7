/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 10:15:04
 * @Description:
 */
import { get, post, put, del, down } from "@/utils/request";

// ================== 分类管理 ====================
export const classifyListApi = (data) => get(`/commodity-classify/page`, data);
export const classifyAddApi = (data) => post(`/commodity-classify`, data);
export const classifyEditApi = (data) => put(`/commodity-classify`, data);
export const classifyDelApi = (id) => del(`/commodity-classify/${id}`);
export const classifyInfoApi = (params) =>
  get(`/commodity-classify/getOne`, params);
// 启用分类
export const classifyEnableApi = (id, status) =>
  put(`/commodity-classify/${id}/${status}`);
// ================== 商品管理 ===================
export const itemListApi = (params) => post(`/item/page`, params); // 商品列表
export const itemAddApi = (params) => post(`/item/saveOrUpdate`, params); // 添加
export const itemUpdateItemSkuApi = (params) =>
  put(`/item/updateItemSku`, params); // 更新商品下的sku
export const itemUpdateDetailApi = (params) =>
  put(`/item/updateDetail`, params); // 更新商品详情
export const itemUpdateOtherInfoApi = (params) =>
  put(`/item/updateOtherInfo`, params); // 更新设置其他信息
export const itemDelApi = (id) => del(`/item/delete/${id}`); // 删除
export const itemUpdateSaleStatusApi = (params) =>
  put(`/item/updateSaleStatus`, params); // 上下架状态
export const itemDetailByIdApi = (id) => get(`/item/detail/${id}`); // 商品详情
export const storageInventoryListApi = (params) =>
  get(`/storage-inventory/page`, params); // 库品列表查询

// ================== 商品管理 ===================
export const itemSummaryListApi = (params) => post(`/itemSummary/page`, params); // 商品列表

// 报价单
export const addOfferPriceApi = (data) => post(`/offerPrice/add`, data);
export const getOfferPriceDetailsApi = (id) => get(`/offerPrice/detail/${id}`);
export const removeOfferPriceApi = (id) => get(`/offerPrice/remove/${id}`);
export const getOfferPriceListApi = (params) =>
  post(`/offerPrice/page`, params);
export const getCustomerStaffPageApi = (params) =>
  post(`/customer/customerStaffPage`, params);
export const getPreviewPageApi = (params) =>
  post(`/operator/trade-order/preview`, params);
export const getCreatePageApi = (params) =>
  post(`/operator/trade-order/create`, params);
// 获取分期价格
export const getStagePriceApi = (params) =>
  put(`/operator/trade-order/calculate`, params);

// =============================  问题商品  ===============================
// 分页查询
export const getProblemGoodsPageApi = (params) =>
  post(`/problems/page`, params);
// 删除
export const deleteProblemGoodsApi = (id) => del(`/problems/${id}`);
// 添加问题商品
export const addProblemGoodsApi = (data) => post(`/problems/batch`, data);
