<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:20
 * @Description: 设备运行查询
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      :show-index="false"
      :height="550"
      @loadData="loadData"
    >
      <template #deviceTree>
        <el-cascader
          v-model="productIdName"
          filterable
          clearable
          style="width: 250px"
          :options="productTreeOption"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          @change="handleProductTree"
        >
        </el-cascader>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-edit-outline"
            @click="monthCount(slotProps.row)"
          >
            详情
          </el-button>
        </span>
      </template>
    </ProTable>
    <!--  m每日统计明细  -->
    <ProDrawer
      class="margin-top"
      :value="unfoldDrawer"
      size="85%"
      :title="drawerTitle"
      :top="'10%'"
      :no-footer="true"
      @cancel="closeDrawer"
    >
      <div class="order-border-box">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="客户编号">{{
            dayDescriptionsData.customerSeq
          }}</el-descriptions-item>
          <el-descriptions-item label="店铺名称">{{
            dayDescriptionsData.customerName
          }}</el-descriptions-item>
          <el-descriptions-item label="品牌">{{
            dayDescriptionsData.brand
          }}</el-descriptions-item>
          <el-descriptions-item label="机型">{{
            dayDescriptionsData.machine
          }}</el-descriptions-item>
          <el-descriptions-item label="设备组名称">{{
            dayDescriptionsData.deviceGroup?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="合约类型">{{
            dayDescriptionsData.treatyType?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="是否安装客户端">{{
            dayDescriptionsData.regCliState == 0 ? "否" : "是"
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <el-divider></el-divider>
      <ProTable
        ref="dayProTable"
        :columns="dayColumns"
        show-pagination
        row-key="label"
        :local-pagination="localDayPagination"
        :data="dayTableData"
        sticky
        :height="550"
        :query-param="dayQueryParam"
        @loadData="loadDayData"
      >
        <template #dayDeviceTree>
          <el-cascader
            v-model="dayQueryParam.deviceGroupName"
            filterable
            clearable
            style="width: 100%"
            :options="productTreeOption"
            :props="{
              label: 'name',
              value: 'fullIdPath',
              children: 'children',
              expandTrigger: 'click',
              multiple: true,
            }"
            @change="handleProductTree"
          >
          </el-cascader>
        </template>
      </ProTable>
    </ProDrawer>
  </div>
</template>
<script>
import { printMonthListApi, printDailyListApi } from "@/api/statisics";
import { cloneDeep } from "lodash";
import { filterParam, filterParamRange, divideAmount } from "@/utils";
import { productListApi } from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "MonthCount",
  mixins: [],
  props: {},
  data() {
    return {
      productIdName: "",
      tableData: [],
      dayTableData: [], // 每日统计
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      localDayPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      }, // 每日统计pagination
      productTreeOption: [],
      queryParam: {},
      dayQueryParam: {},
      columns: [
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          valueType: "product",
          isSearch: true,
          // searchSlot: "deviceTree",
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          width: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
          fixed: "left",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          width: 200,
          valueType: "input",
          isSearch: true,
          isTable: true,
          fixed: "left",
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          valueType: "input",
          isTable: true,
          fixed: "left",
          isSearch: true,
          placeholder: "1号机： 1",
          formatter: (row) => row.deviceGroup.label,
          width: 100,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          valueType: "select",
          isTable: true,
          width: 70,
        },
        {
          dataIndex: "machine",
          title: "机型",
          isTable: true,
        },

        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => row.serType?.label,
        },
        {
          dataIndex: "serTypes",
          title: "服务类型",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "散修",
              value: "SCATTERED",
            },
            {
              label: "购机不保",
              value: "NO_WARRANTY",
            },
            {
              label: "购机质保",
              value: "WARRANTY",
            },
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "质保服务",
              value: "QA",
            },
            {
              label: "质保含部件",
              value: "QA_COMPONENT",
            },
            {
              label: "维保服务",
              value: "MAINTENANCE",
            },
            {
              label: "其它",
              value: "OTHER",
            },
          ],
        },
        {
          dataIndex: "regCliState",
          title: "安装客户端",
          valueType: "select",
          isSearch: true,
          isTable: true,
          clearable: true,
          option: [
            { value: 1, label: "是" },
            { value: 0, label: "否" },
          ],
          formatter: (row) => (row.regCliState == 1 ? "是" : "否"),
          width: 100,
        },
        {
          dataIndex: "blackRange",
          title: "黑白印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "colorRange",
          title: "彩色印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "totalRange",
          title: "总印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
          attrs: { "value-format": "yyyy-MM" },
          // tableSlot: "month",
        },
        {
          dataIndex: "workNum",
          title: "工单数",
          isTable: true,
        },
        {
          dataIndex: "repairNum",
          title: "自修次数",
          isTable: true,
        },
        {
          dataIndex: "partNum",
          title: "零件更换数",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "partAmount",
          title: "零件耗材费",
          isTable: true,
          formatter: (row) => divideAmount(row.partAmount, 100),
          width: 100,
        },
        {
          dataIndex: "mallAmount",
          title: "商城购买金额",
          isTable: true,
          formatter: (row) => divideAmount(row.mallAmount, 100),
          width: 110,
        },
        {
          dataIndex: "laborAmount",
          title: "工单人工费",
          isTable: true,
          formatter: (row) => divideAmount(row.laborAmount, 100),
          width: 100,
        },
        {
          dataIndex: "totalAmount",
          title: "总费用",
          isTable: true,
          formatter: (row) => divideAmount(row.totalAmount, 100),
        },
        // {
        //   dataIndex: "faultNum",
        //   title: "上报故障数",
        //   isTable: true,
        // },
        {
          dataIndex: "blackWhiteCount",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorCount",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "totalCount",
          title: "总印量",
          width: 150,
          isTable: true,
        },
        {
          dataIndex: "Actions",
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tableSlot: "actions",
        },
      ],
      dayColumns: [
        // {
        //   dataIndex: "deviceGroupName",
        //   title: "品牌/机型",
        //   valueType: "select",
        //   isSearch: true,
        //   clearable: true,
        //   searchSlot: "dayDeviceTree",
        // },
        // {
        //   dataIndex: "blackRange",
        //   title: "黑白印量",
        //   isSearch: true,
        //   valueType: "inputRange",
        //   // searchSlot: "blackRange",
        // },
        // {
        //   dataIndex: "colorRange",
        //   title: "彩色印量",
        //   isSearch: true,
        //   searchSlot: "colorRange",
        // },
        // {
        //   dataIndex: "totalRange",
        //   title: "总印量",
        //   isSearch: true,
        //   searchSlot: "totalRange",
        // },
        // {
        //   dataIndex: "invtervalDays",
        //   title: "间隔天数",
        //   isTable: true,
        // },
        {
          dataIndex: "dataSource",
          title: "统计类型",
          isTable: true,
          formatter: (row) => row.dataSource?.label,
        },
        {
          dataIndex: "blackWhiteInception",
          title: "初始黑白打印数",
          isTable: true,
        },
        {
          dataIndex: "blackWhiteCutoff",
          title: "截止黑白打印数",
          isTable: true,
        },
        {
          dataIndex: "blackWhiteCount",
          title: "黑白印量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "colorInception",
          title: "起始彩色打印数",
          isTable: true,
        },
        {
          dataIndex: "colorCutoff",
          title: "截止彩色打印数",
          isTable: true,
        },
        {
          dataIndex: "colorCount",
          title: "彩色印量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "totalCount",
          title: "总印量",
          width: 150,
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        // {
        //   dataIndex: "firstWorkCode",
        //   title: "开始工单号",
        //   width: 150,
        //   isTable: true,
        // },
        // {
        //   dataIndex: "lastWorkCode",
        //   title: "结束工单号",
        //   width: 150,
        //   isTable: true,
        // },
        // {
        //   dataIndex: "startYearMonth",
        //   title: "开始年月",
        //   isSearch: true,
        //   valueType: "date-picker",
        //   pickerType: "monthrange",
        //   pickerFormat: "yyyy-MM",
        //   attrs: { "value-format": "yyyy-MM" },
        // },
        {
          dataIndex: "currDate",
          title: "日期",
          width: 150,
          isTable: true,
        },
        // {
        //   dataIndex: "Actions",
        //   fixed: "right",
        //   title: "操作",
        //   align: "left",
        //   isTable: true,
        //   tableSlot: "actions",
        // },
      ], // 每日统计列表标题
      unfoldDrawer: false,
      drawerTitle: "",
      dayPrintCount: {},
      monthPrintCount: {},
      month: "", // 用于查询每月的详情
      deviceGroupId: "", // 设备组ID
      dayDescriptionsData: {},
    };
  },

  computed: {},

  watch: {},
  created() {},

  mounted() {
    this.$refs.ProTable.refresh();
    this.getProductTree();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );

      const result = [
        {
          beginYearMonth: null,
          endYearMonth: null,
          data: parameter.currMonth,
        },
        {
          beginBlackWhite: null,
          endBlackWhite: null,
          data: parameter.blackRange,
        },
        {
          beginColor: null,
          endColor: null,
          data: parameter.colorRange,
        },
        {
          beginCount: null,
          endCount: null,
          data: parameter.totalRange,
        },
      ];
      filterParamRange(this, this.queryParam, result);

      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.currDate;
      delete requestParameters.blackRange;
      delete requestParameters.colorRange;
      delete requestParameters.totalRange;
      printMonthListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    // 加载每日统计明细
    loadDayData(parameter) {
      parameter.cycle = this.month; // 需查询日期
      parameter.deviceGroupId = this.deviceGroupId;
      console.log(parameter, "dayQueryParam.parameter ");
      this.dayQueryParam = filterParam(
        Object.assign({}, this.dayQueryParam, parameter)
      );
      const result = [
        {
          beginBlackWhite: null,
          endBlackWhite: null,
          data: parameter.blackWhiteCount,
        },
        {
          beginColor: null,
          endColor: null,
          data: parameter.colorCount,
        },
        {
          beginCount: null,
          endCount: null,
          data: parameter.totalCount,
        },
      ];
      filterParamRange(this, this.dayQueryParam, result);
      const requestParameters = cloneDeep(this.dayQueryParam);
      delete requestParameters.blackWhiteCount;
      delete requestParameters.colorCount;
      delete requestParameters.totalCount;

      printDailyListApi(requestParameters)
        .then((res) => {
          console.log(res, "日统计");
          this.dayTableData = res.data.rows;
          this.localDayPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.dayProTable
            ? (this.$refs.dayProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    // 计算日印量和月印量
    monthCount(row, type) {
      this.drawerTitle = row.currMonth + " - 每日印量详情";
      this.dayDescriptionsData = row;
      this.month = row.currMonth;
      this.deviceGroupId = row.deviceGroupId;
      this.$nextTick(() => {
        this.$refs.dayProTable.refresh();
      });
      this.unfoldDrawer = true;
    },
    closeDrawer() {
      this.unfoldDrawer = false;
    },
    async getProductTree() {
      try {
        const result = await productListApi({ pageNumber: 1, pageSize: 9999 });
        if (result.code === 200 && result.data) {
          this.productTreeOption = result.data;
        }
      } catch (error) {
        console.log(error);
      }
    },
    formSubmit(val) {
      console.log(val);
    },
    handleProductTree(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.gap {
  margin: 20px 0;
}
.order-border-box {
  border: dashed 1px #ccc;
  padding: 10px;
}
</style>
