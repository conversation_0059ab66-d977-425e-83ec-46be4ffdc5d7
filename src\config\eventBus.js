/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-16 16:53:58
 * @Description:
 */
/*
 * @Description:
 * @Autor: sr
 * @Date: 2022-07-25 17:43:47
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-16 16:53:58
 */
import { filter, Subject } from "rxjs";

const subjects = {};

export function listen(type, callback = (data) => {}) {
  if (!subjects[type]) {
    subjects[type] = new Subject();
  }

  const eventSubject = subjects[type];

  const subscription = eventSubject
    .pipe(filter((value) => !!value.type))
    .pipe(filter((value) => value.type === type))
    .subscribe((value) => {
      callback(value.data);
    });
  return () => {
    subscription.unsubscribe();
  };
}

export function dispatch(type, data = {}) {
  if (!type) {
    return;
  }
  if (!subjects[type]) {
    subjects[type] = new Subject();
  }
  const eventSubject = subjects[type];

  eventSubject.next({ type, data });
}
