<!--
 * @Author: wskg
 * @Date: 2024-08-28 14:15:11
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 客户领料
 -->
<template>
  <div class="store">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      :query-param="queryParam"
      :show-settings="false"
      @loadData="loadData"
    >
    </ProTable>
  </div>
</template>

<script>
import { receiveListApi } from "@/api/statisics";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";

export default {
  name: "Apply",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "itemNum",
          title: "数量",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "单价",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "payAmount",
          title: "金额",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "customerName",
          title: "领料人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "createdAt",
          title: "领料时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "reviewers",
          title: "审核人",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "reviewersTime",
          title: "审核时间",
          isTable: true,
          width: 150,
        },
      ],
      tableData: [],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(params) {
      this.queryParam = filterParam(Object.assign({}, this.queryParam, params));
      const result = [
        {
          startDate: null,
          endDate: null,
          data: params.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      receiveListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  display: flex;
  gap: 50px;
  font-size: 16px;
  color: #6488cf;
}
</style>
