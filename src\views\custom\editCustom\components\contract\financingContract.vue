<!--
 * @Author: wskg
 * @Date: 2025-01-15 10:27:13
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:48
 * @Description: 购机合约
 -->
<template>
  <div class="app-container">
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="65%"
      :confirm-button-disabled="confirmLoading"
      :method-type="methodType"
      :no-footer="methodType === 'info'"
      confirm-text="确认提交"
      @ok="handleSubmit"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="formSubmit"
      >
        <template #isSupplement>
          <el-radio-group
            v-model="form.isSupplement"
            :disabled="methodType === 'info'"
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </template>
        <!-- 客户地址信息 -->
        <template #customerInfo>
          <el-col :span="8" style="padding-left: 0; padding-right: 13px">
            <el-form-item label="收货地址：" prop="addressId">
              <el-select
                v-model="form.addressId"
                style="width: 100%"
                placeholder="请选择收货地址"
                clearable
                :disabled="methodType === 'info'"
                @change="handleAddressChange"
              >
                <el-option
                  v-for="item in addressList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系人：" prop="consignee">
              <el-input
                v-model="form.consignee"
                placeholder="请输入联系人"
                :disabled="methodType === 'info'"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话：" prop="consigneePhone">
              <el-input
                v-model="form.consigneePhone"
                placeholder="请输入联系电话"
                :disabled="methodType === 'info'"
              ></el-input>
            </el-form-item>
          </el-col>
        </template>
        <template #signId>
          <el-select
            v-model="form.signId"
            style="width: 100%"
            :disabled="methodType === 'info'"
          >
            <el-option
              v-for="item in userList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <template #agentId>
          <el-select
            v-model="form.agentId"
            style="width: 100%"
            :disabled="true"
          >
            <el-option
              v-for="item in userList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <!-- 合约附件 -->
        <template #attachments>
          <div>
            <PDFFiles
              :file-list="form.attachments"
              :limit="5"
              is-auth
              :disabled="methodType === 'info'"
              @uploadSuccess="handleLogFileUploadSuccess"
            />
            <!--<div v-else>-->
            <!--  <p v-for="item in form.attachments" :key="item.key">-->
            <!--    {{ item.name }}-->
            <!--  </p>-->
            <!--</div>-->
          </div>
        </template>
        <!-- 签约机器信息 -->
        <template #signMachineInfo>
          <ProTable
            ref="ProTable"
            :show-search="false"
            :show-pagination="false"
            :show-setting="false"
            :show-loading="false"
            :height="350"
            :columns="columns"
            :data="tableData"
          >
            <template #btn>
              <el-button
                v-if="methodType === 'add'"
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="handleDispatch"
              >
                选择出库机器
              </el-button>
            </template>
            <template #action="{ row }">
              <div class="fixed-width">
                <el-button
                  icon="el-icon-view"
                  @click="handleViewOrEdit(row, 'info')"
                >
                  查看
                </el-button>
                <el-button
                  v-if="methodType !== 'info'"
                  icon="el-icon-edit"
                  @click="handleViewOrEdit(row, 'edit')"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="methodType !== 'info'"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDelete(row)"
                >
                  移除
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 机器合约信息 -->
    <FinancingContractInfo
      ref="financingContractInfo"
      :is-supplement="form.isSupplement"
      :edit-type="methodType"
      @confirmContractInfo="confirmContractInfo"
    />
    <!-- 选择出库机器 -->
    <ChooseDispatchMachine
      :dialog-visible.sync="dispatchDialogVisible"
      :selected-data="tableData"
      @confirmDispatch="confirmDispatch"
    />
  </div>
</template>

<script>
import PDFFiles from "@/components/ProUpload/pdfFiles.vue";
import FinancingContractInfo from "@/views/custom/editCustom/components/contract/financingContractInfo.vue";
import ChooseDispatchMachine from "@/views/custom/editCustom/components/contract/chooseDispatchMachine.vue";
import { cloneDeep } from "lodash";
import { addCustomerContractApi, getCustomerUserListApi } from "@/api/customer";
import { getAddressListApi } from "@/api/operator";
import { Message } from "element-ui";
import { transformFormParams } from "@/utils";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "RentContract",
  components: { FinancingContractInfo, PDFFiles, ChooseDispatchMachine },
  data() {
    const that = this;
    return {
      drawerVisible: false,
      drawerTitle: "",
      confirmLoading: false,
      methodType: "add", // form类型 add/edit/info
      form: {
        isSupplement: false,
      },
      formColumns: [
        // {
        //   dataIndex: "isSupplement",
        //   title: "是否为补录合同",
        //   isForm: true,
        //   formSpan: 8,
        //   formSlot: "isSupplement",
        // },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "customerInfo",
          title: "客户信息",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "customerInfo",
        },
        {
          dataIndex: "code",
          title: "合同编号",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入合同编号",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入合同名称",
              trigger: "change",
            },
          ],
        },

        // {
        //   dataIndex: "addToDeviceGroup",
        //   title: "添加机器信息",
        //   isForm: true,
        //   formSlot: "addToDeviceGroup",
        //   formSpan: 8,
        // },
        {
          dataIndex: "remark",
          title: "摘要",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
        },
        {
          dataIndex: "signTime",
          title: "签约时间",
          isForm: true,
          formSpan: 8,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          prop: [
            {
              required: true,
              message: "请选择签约时间",
              trigger: "change",
            },
          ],
        },
        // {
        //   dataIndex: "startTime",
        //   title: "合约开始时间",
        //   isForm: true,
        //   formSpan: 8,
        //   valueType: "date-picker",
        //   pickerType: "date",
        //   pickerFormat: "yyyy-MM-dd",
        //   valueFormat: "yyyy-MM-dd",
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择合约开始时间",
        //       trigger: "change",
        //     },
        //   ],
        // },
        // {
        //   dataIndex: "endTime",
        //   title: "合约截止时间",
        //   isForm: true,
        //   formSpan: 8,
        //   valueType: "date-picker",
        //   pickerType: "date",
        //   pickerFormat: "yyyy-MM-dd",
        //   valueFormat: "yyyy-MM-dd",
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择合约截止时间",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "signId",
          title: "销售",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          formSlot: "signId",
          option: [],
        },
        {
          dataIndex: "agentId",
          title: "商务",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          formSlot: "agentId",
          option: [],
        },
        {
          dataIndex: "attachments",
          title: "合约附件",
          isForm: true,
          formSpan: 24,
          formSlot: "attachments",
        },
        {
          dataIndex: "signMachineInfo",
          title: "签约机器信息",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "signMachineInfo",
        },
      ],
      userList: [],
      ladderList: [],
      // 签约机器信息
      columns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => {
            return that.dictMap[row.hostType] || "未知类型";
            // switch (row.hostType) {
            //   case "2002":
            //     return "A4大容量纸盒";
            //   case "2003":
            //     return "A3大容量纸盒";
            //   case "2004":
            //     return "分页器-不带骑马钉";
            //   case "2005":
            //     return "分页器-带骑马钉";
            //   case "2006":
            //     return "真空纸盒";
            //   case "2007":
            //     return "服务器";
            //   case "2008":
            //     return "主机";
            // }
          },
        },
        {
          dataIndex: "productInfo",
          title: "品牌/型号",
          isTable: true,
        },
        // {
        //   dataIndex: "brand",
        //   title: "",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "status",
        //   title: "合同状态",
        //   isTable: true,
        //   formatter: (row) => row.status?.label,
        // },
        // {
        //   dataIndex: "settleStatus",
        //   title: "结算状态",
        //   isTable: true,
        //   formatter: (row) => {
        //     if (row.settleStatus?.label) {
        //       return row.settleStatus?.label;
        //     } else {
        //       switch (row.settleStatus) {
        //         case "0":
        //           return "未结算";
        //         case "1":
        //           return "已结算";
        //         default:
        //           return "";
        //       }
        //     }
        //   },
        // },
        // {
        //   dataIndex: "settleMethod",
        //   title: "结算方式",
        //   isTable: true,
        //   formatter: (row) => {
        //     if (row.settleMethod?.label) {
        //       return row.settleMethod?.label;
        //     } else {
        //       switch (row.settleMethod) {
        //         case "FULL":
        //           return "全款";
        //         case "INSTALLMENT":
        //           return "分期付款";
        //         default:
        //           return "";
        //       }
        //     }
        //   },
        // },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => {
            if (row.serType?.label) {
              return row.serType?.label;
            } else {
              switch (row.serType) {
                case "FINANCING_HALF":
                  return "融资半保";
                case "FINANCING_FULL":
                  return "融资全保";
                case "PACKAGE_HALF":
                  return "包量半保";
                case "PACKAGE_ALL":
                  return "包量全保";
                case "OTHER":
                  return "其它";
                default:
                  return "";
              }
            }
          },
        },
        {
          dataIndex: "action",
          title: "操作",
          tooltip: false,
          isTable: true,
          tableSlot: "action",
          width: 200,
        },
      ],
      tableData: [],
      editType: "edit", // 列表合同明细类型 edit/info
      dispatchDialogVisible: false,
      currentRow: null,
      addressList: [],
      fullAddressList: [],
      dictMap: {},
    };
  },
  mounted() {
    this.loadDictMap();
  },
  methods: {
    visible(val, type) {
      this.addressList = [];
      this.fullAddressList = [];
      this.tableData = [];
      this.userList = [];
      this.methodType = type;
      this.drawerTitle = this.titleMap(type);
      this.form = cloneDeep(val);
      this.$set(this.form, "isSupplement", true);
      if (type === "add") {
        this.$set(
          this.form,
          "agentId",
          JSON.parse(localStorage.getItem("userInfo")).id
        );
      }
      if (
        this.form.customerContractItems &&
        this.form.customerContractItems.length >= 1
      ) {
        this.tableData = this.form.customerContractItems.map((item) => {
          return transformFormParams(item);
        });
      }
      this.operatList();
      this.getCustomerAddressList();
      this.drawerVisible = true;
    },
    loadDictMap() {
      dictTreeByCodeApi(2000).then((res) => {
        this.dictMap = res.data.reduce((acc, item) => {
          acc[item.value] = item.label;
          return acc;
        }, {});
      });
    },
    handleSubmit() {
      this.$refs.ProForm.handleSubmit();
    },
    async formSubmit(val) {
      try {
        // if (!val.isSupplement && !val.addressId) {
        //   this.$message.warning("请选择收货地址");
        //   return;
        // }
        // if (val.addressId && (!val.consignee || !val.consigneePhone)) {
        //   this.$message.warning("请填写联系人或联系方式");
        //   return;
        // }
        // if (val.addressId && !val.consigneePhone) {
        //   this.$message.warning("请填写联系人或联系方式");
        //   return;
        // }
        if (!this.tableData.length) {
          this.$message.warning("请选择机器信息");
          return;
        }
        this.confirmLoading = true;
        const args = {
          ...val,
          customerContractItems: this.tableData,
          contractType: "1230",
        };
        console.log(args, "args");
        const result = await addCustomerContractApi(args);
        if (result.code === 200) {
          this.$message.success("操作成功");
          this.$emit("refresh");
          this.drawerVisible = false;
        }
      } finally {
        this.confirmLoading = false;
      }
    },
    // 列表明细
    handleViewOrEdit(row, type) {
      this.currentRow = cloneDeep(row);
      this.$refs.financingContractInfo.visible(this.currentRow, type);
    },
    // 移除
    handleDelete(row) {
      this.tableData = this.tableData.filter(
        (item) => item.machineNum !== row.machineNum
      );
    },
    // 确认机器合约信息
    confirmContractInfo(info) {
      const index = this.tableData.findIndex(
        (item) => item.machineNum === this.currentRow.machineNum
      );
      if (index !== -1) {
        this.$set(this.tableData, index, cloneDeep(info));
      }
    },
    handleLogFileUploadSuccess(result) {
      if (!this.form.attachments) {
        this.$set(this.form, "attachments", []);
      }
      this.form.attachments.push(result);
    },
    handleDispatch() {
      this.dispatchDialogVisible = true;
    },
    confirmDispatch(row) {
      this.tableData = cloneDeep(row);
      this.dispatchDialogVisible = false;
    },
    // 员工列表处理
    operatList() {
      this.userList = [];
      getCustomerUserListApi().then((res) => {
        res.data.map((item) => {
          this.userList.push({
            value: item.id,
            label: item.name,
          });
        });
      });
    },
    async getCustomerAddressList() {
      // 获取用户可用地址
      try {
        if (this.form.customerId) {
          const res = await getAddressListApi(this.form.customerId);
          this.fullAddressList = res.data;
          res.data.map((item) => {
            this.addressList.push({
              value: item.id,
              label: item.address,
            });
          });
        }
      } catch (e) {
        Message.error(e.message);
      }
    },
    handleAddressChange(val) {
      const addressInfo = this.fullAddressList.find((item) => item.id === val);
      if (addressInfo) {
        this.$set(this.form, "consignee", addressInfo.contact);
        this.$set(this.form, "consigneePhone", addressInfo.phone);
      }
    },
    titleMap(type) {
      const data = {
        add: "新增补录融资合约",
        edit: "编辑补录融资合约",
        info: "查看补录融资合约",
      };
      return data[type];
    },
    closeDrawer() {
      this.drawerVisible = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
