<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-15 14:44:15
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-30 18:02:49
 * @FilePath: src/views/dispose/components/privacyProtocol.vue
 * @Description: 隐私协议
 * 
-->
<template>
  <div class="app-container">
    <div class="edit">
      <WangeEditor ref="WangeEditorRef" :height="600" />
    </div>
    <div>
      <el-button
        type="primary"
        size="small"
        style="margin-top: 20px"
        @click="handleSubmit"
      >
        保存隐私协议
      </el-button>
    </div>
  </div>
</template>

<script>
import WangeEditor from "@/components/ProWangeEditor/index.vue";
import { setBaseCompanyInfoApi } from "@/api";
export default {
  name: "PrivacyProtocol",
  components: {
    WangeEditor,
  },
  props: {
    privacy: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      content: "",
      privacyProtocol: "",
    };
  },
  watch: {
    privacy: {
      handler(val) {
        this.$refs.WangeEditorRef?.echo(val);
      },
    },
    // this.$nextTick(() => {
    //
    // });
  },
  methods: {
    handleSubmit() {
      const result = this.$refs.WangeEditorRef.getContent();
      if (!this.id) {
        this.$message.error("请先保存收款配置信息");
        return;
      }
      this.$emit("save", { privacyProtocol: result });
      // setBaseCompanyInfoApi({ id: this.id, privacyProtocol: result }).then(
      //   (res) => {
      //     console.log(res);
      //     this.$message.success("配置保存成功");
      //   }
      // );
    },
  },
};
</script>

<style scoped lang="scss">
.edit {
  text-align: left;
}
</style>
