<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 11:19:20
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="使用帮助" name="使用帮助" lazy>
        <Help />
      </el-tab-pane>
      <el-tab-pane label="热词管理" name="热词管理" lazy>
        <Words />
      </el-tab-pane>
      <el-tab-pane label="知识库" name="知识库" lazy>
        <Knowledge />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Help from "./components/help.vue";
import Words from "./components/words.vue";
import Knowledge from "./components/knowledge.vue";
export default {
  name: "Repository",
  components: { Help, Words, Knowledge },
  data() {
    return {
      activeName: "使用帮助",
    };
  },
};
</script>

<style scoped lang="scss"></style>
