@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 自定义CSS变量 */
:root {
  --primary-color: #3b82f6;
  --primary-color-hover: #2563eb;
  --text-color: #374151;
  --border-color: #e5e7eb;
  --background-color: #f9fafb;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --transition: all 0.3s ease;
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-color);
  line-height: 1.6;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 通用工具类 */
.transition-smooth {
  transition: var(--transition);
}

.shadow-custom {
  box-shadow: var(--shadow-md);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-color) 0%, #6366f1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 响应式文字大小 */
.responsive-text {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
}

.responsive-title {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  body {
    font-size: 16px; /* 防止iOS自动缩放 */
  }
  
  /* 移动端触摸优化 */
  button,
  [role="button"],
  input[type="submit"],
  input[type="button"] {
    touch-action: manipulation;
  }
}

/* 打印样式 */
@media print {
  body {
    font-size: 12pt;
    line-height: 1.4;
    color: black;
  }
  
  .no-print {
    display: none !important;
  }
}

/* 调整 Ant Design 顶部菜单选中项下划线高度 */
.ant-menu-horizontal > .ant-menu-item-selected::after,
.ant-menu-horizontal > .ant-menu-submenu-selected::after {
  border-bottom-width: 4px !important; /* 默认2px，增至3px */
}

/* 管理后台页签样式 - 苹果风格设计 */
.admin-tabs .ant-tabs-nav {
  margin-bottom: 0;
  border-bottom: 0.5px solid #d1d1d6;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.admin-tabs .ant-tabs-nav-wrap {
  padding: 0;
}

.admin-tabs .ant-tabs-tab {
  padding: 12px 20px;
  margin-right: 0;
  border-radius: 0;
  background: transparent;
  border: none;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  min-width: auto;
  color: #86868b;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.42857;
  letter-spacing: -0.01em;
}

.admin-tabs .ant-tabs-tab:hover {
  background: transparent;
  color: #1d1d1f;
}

.admin-tabs .ant-tabs-tab-active {
  background: transparent;
  color: #007aff !important;
  font-weight: 590;
}

.admin-tabs .ant-tabs-tab-active::after {
  content: '';
  position: absolute;
  bottom: -0.5px;
  left: 0;
  transform: none;
  width: 100%;
  height: 2px;
  background: #007aff;
  border-radius: 1px;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.admin-tabs .ant-tabs-ink-bar {
  display: none;
}

.admin-tabs .ant-tabs-content-holder {
  display: none;
}

/* 页签图标样式 */
.admin-tabs .ant-tabs-tab .anticon {
  font-size: 16px;
  margin-right: 0;
  vertical-align: -1px;
  opacity: 0.85;
}

/* 页签文字样式优化 */
.admin-tabs .ant-tabs-tab .ant-tabs-tab-btn {
  display: flex;
  align-items: center;
  gap: 2px;
}

.admin-tabs .ant-tabs-tab .ant-tabs-tab-btn > * + * {
  margin-left: 1px !important;
}

.admin-tabs .ant-tabs-tab-active .anticon {
  opacity: 1;
}

/* 苹果风格的Header样式 */
.ant-layout-header {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 0.5px solid #d1d1d6 !important;
}

/* 移动端页签响应式 - 苹果风格 */
@media (max-width: 768px) {
  .admin-tabs .ant-tabs-tab {
    padding: 10px 16px;
    font-size: 13px;
  }
  
  .admin-tabs .ant-tabs-tab .ant-tabs-tab-btn span:last-child {
    display: none;
  }
  
  .admin-tabs .ant-tabs-tab .anticon {
    font-size: 18px;
    margin-right: 0;
    opacity: 0.85;
  }
  
  .admin-tabs .ant-tabs-tab-active .anticon {
    opacity: 1;
  }
  
  .admin-tabs .ant-tabs-tab-active::after {
    width: 100%;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .admin-tabs .ant-tabs-nav {
    border-bottom-color: #424245;
    background: rgba(28, 28, 30, 0.8);
  }
  
  .ant-layout-header {
    background: rgba(28, 28, 30, 0.8) !important;
    border-bottom-color: #424245 !important;
  }
  
  .admin-tabs .ant-tabs-tab {
    color: #98989d;
  }
  
  .admin-tabs .ant-tabs-tab:hover {
    color: #f2f2f7;
  }
  
  .admin-tabs .ant-tabs-tab-active {
    color: #007aff !important;
  }
} 