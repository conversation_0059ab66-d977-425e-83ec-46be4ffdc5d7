<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-07-03 14:08:48
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 14:45:57
 * @Description: 耗材库存
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :layout="{ labelWidth: '80px' }"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-warning-outline" @click="handleEdit(row)">
            详情
          </el-button>
          <el-button
            v-auth="['@ums:manage:repertory:download']"
            :loading="exportLoading"
            icon="el-icon-download"
            @click="handleExport(row, false)"
          >
            导出库存数据
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="90%"
      no-footer
      @cancel="closeDrawer"
    >
      <ProTable
        ref="detailTable"
        :query-param="detailQueryParam"
        :local-pagination="detailLocalPagination"
        :columns="detailColumns"
        :data="detailData"
        @loadData="loadDetailData"
      >
        <template #btn>
          <el-button
            v-auth="['@ums:manage:repertory:download']"
            type="success"
            icon="el-icon-download"
            size="mini"
            @click="handleExport(null, true)"
          >
            导出库存数据
          </el-button>
          <div v-if="statLoading" class="title-box-right">
            <div>库存数量： {{ totalData?.sumWarehouseNumber || 0 }}</div>
            <div>工程师数量： {{ totalData?.engineerNum || 0 }}</div>
            <div>总数量： {{ totalData?.total || 0 }}</div>
            <div>不含税总金额： {{ totalData?.noTaxAmount || 0 }}</div>
            <div>含税总金额： {{ totalData?.taxAmount || 0 }}</div>
          </div>
          <div v-else class="title-box-right" style="gap: 5px">
            <i class="el-icon-loading"></i>
            正在加载统计数据
          </div>
        </template>
      </ProTable>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  exportConsumableEndOfMonthApi,
  getConsumableEndOfMonthApi,
  getConsumableEndOfMonthDetailApi,
  getConsumableEndOfMonthTotalApi,
  warehouseListApi,
} from "@/api/store";
import { handleExcelExport } from "@/utils/exportExcel";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "ConsumableStock",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "monthly",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        // {
        //   dataIndex: "warehouseId",
        //   title: "归属仓库",
        //   isTable: true,
        //   formatter: (row) => row?.warehouseName,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [],
        //   optionMth: () => warehouseListApi({ status: 1401 }),
        //   optionskey: {
        //     label: "name",
        //     value: "id",
        //   },
        // },
        {
          dataIndex: "sumWarehouseNumber",
          title: "库存数量",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "engineerNum",
          title: "工程师数量",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "total",
          title: "总数量",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "noTaxPrice",
          title: "不含税均价",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "noTaxAmount",
          title: "不含税金额",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "taxPrice",
          title: "含税均价",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "taxAmount",
          title: "含税金额",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 210,
        },
      ],
      tableData: [],
      exportLoading: false,
      // drawer
      drawerVisible: false,
      drawerTitle: "月末库存",
      detailQueryParam: {},
      detailLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      detailColumns: [
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "partBrand",
          title: "品牌",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          isSearch: true,
          valueType: "select",
          formatter: (row) => row.manufacturerChannel?.label,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 90,
        },
        // {
        //   dataIndex: "imageFiles",
        //   title: "物品图片",
        //   width: 150,
        //   isTable: true,
        //   tableSlot: "imageFiles",
        //   clearable: true,
        //   align: "center",
        // },
        {
          dataIndex: "warehouseId",
          title: "归属仓库",
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [],
          formatter: (row) => row?.warehouseName,
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          minWidth: 120,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          minWidth: 60,
        },
        {
          dataIndex: "sumWarehouseNumber",
          title: "库存数量",
          isTable: true,
          valueType: "input",
          align: "center",
          minWidth: 80,
        },
        {
          dataIndex: "engineerNum",
          title: "工程师数量",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
          align: "center",
          minWidth: 90,
        },
        // {
        //   dataIndex: "applyNum",
        //   title: "客户仓数量",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        //   width: 90,
        //   align: "center",
        // },
        {
          dataIndex: "total",
          title: "总数量",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
          align: "center",
          minWidth: 80,
        },
        {
          dataIndex: "noTaxPrice",
          title: "不含税均价",
          isTable: true,
          align: "center",
          minWidth: 90,
        },
        {
          dataIndex: "noTaxAmount",
          title: "不含税金额",
          isTable: true,
          align: "center",
          minWidth: 90,
        },
        {
          dataIndex: "taxPrice",
          title: "含税均价",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "taxAmount",
          title: "含税金额",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "alarmNumber",
          title: "预警值",
          isTable: true,
          align: "center",
          minWidth: 80,
        },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 80,
        },
      ],
      detailData: [],
      detailRequestParameters: {},
      totalData: {},
      statLoading: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startMonthly: null,
          endMonthly: null,
          data: parameter.monthly,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.monthly;
      getConsumableEndOfMonthApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    loadDetailData(parameter) {
      this.detailQueryParam = filterParam(
        Object.assign({}, this.detailQueryParam, parameter)
      );
      // const requestParameters = cloneDeep(this.detailQueryParam);

      this.detailRequestParameters = cloneDeep(this.detailQueryParam);
      getConsumableEndOfMonthDetailApi(this.detailRequestParameters)
        .then((res) => {
          this.detailData = res.data.rows;
          this.detailLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.detailTable &&
            (this.$refs.detailTable.listLoading = false);
        });
      this.getTotalData();
    },
    handleEdit(row) {
      this.detailQueryParam = {};
      this.detailLocalPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.drawerTitle = `${row.monthly} - 库存数据`;
      this.detailQueryParam.monthly = row.monthly;
      this.drawerVisible = true;
      this.$nextTick(() => {
        this.$refs.detailTable.refresh();
      });
    },
    closeDrawer() {
      this.drawerVisible = false;
    },
    getTotalData() {
      this.statLoading = false;
      getConsumableEndOfMonthTotalApi(this.detailRequestParameters)
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    handleExport(row, isParams) {
      this.$confirm("此操作将导出当月库存数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          exportConsumableEndOfMonthApi,
          isParams ? this.detailRequestParameters : { monthly: row.monthly },
          `${
            isParams ? this.detailRequestParameters.monthly : row.monthly
          }月库存数据`,
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
