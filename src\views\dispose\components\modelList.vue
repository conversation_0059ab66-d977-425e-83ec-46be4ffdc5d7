<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-07 18:27:17
 * @Description: 选择机型
 -->

<template>
  <div class=" ">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :layout="{ labelWidth: '120px' }"
      :columns="columns"
      show-selection
      :local-pagination="localPagination"
      :data="tableData"
      default-expand-all
      :query-param="queryParam"
      :height="400"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <!-- <template #fullIdPath>

        <el-autocomplete clearable class="inline-input" v-model="queryParam.fullIdPathName"
          @input="queryParam.fullIdPath = queryParam.fullIdPathName" :fetch-suggestions="querySearch" placeholder="请输入内容"
          :trigger-on-focus="false" @select="handleSelect" value-key=name></el-autocomplete>
      </template> -->
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            checkStrictly: true,
          }"
          leaf-only
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>
<script>
import {
  bomModelListApi,
  modelListApi,
  productAddApi,
  productDelApi,
  productEditApi,
  productThirdApi,
} from "@/api/dispose";

import { isEmpty, cloneDeep } from "lodash";

import { filterName, getAllParentArr } from "@/utils";

export default {
  name: "ModelList",
  components: {},
  mixins: [],
  props: {
    choosePartSelection: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  data() {
    return {
      productIdName: [],
      fullIdPathIdName: [],
      options: [],
      active: 9,
      // 列表
      spareiTypeList: [],
      tableData: [],
      localPagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      queryParam: {
        fullIdPath: null,
        name: null,
      },
      columns: [
        {
          dataIndex: "fullIdPath",
          isSearch: true,
          clearable: true,
          searchSlot: "fullIdPath",
          title: "品牌/产品树/系列",
          valueType: "select",
          searchSpan: 8,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "tree",
          title: "产品树",
          isTable: true,
        },
        {
          dataIndex: "serial",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "name",
          title: "机型",
          isTable: true,
          isSearch: true,
          valueType: "input",
          searchSpan: 8,
        },
        {
          dataIndex: "fullName",
          title: "全称",
          isTable: true,
        },
      ],
    };
  },

  computed: {},
  watch: {},

  created() {
    this.init();
  },

  mounted() {},
  methods: {
    setData(data) {
      data.map((row) => {
        this;
      });
    },
    init() {
      productThirdApi().then((res) => {
        this.options = res.data;
        this.$refs.ProTable.refresh();
      });
    },
    handleSelect(item) {
      this.form.parentId = item[item.length - 1];
      // this.fullIdPathIdName = item
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      if (this.productIdName) {
        requestParameters.fullIdPath =
          this.productIdName[this.productIdName.length - 1];
      } else {
        requestParameters.fullIdPath = null;
      }
      bomModelListApi({
        ...requestParameters,
        partId: this.choosePartSelection.id || null,
      })
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    handleSelectionChange(row) {
      this.$emit("choose", row);
    },
  },
};
</script>
