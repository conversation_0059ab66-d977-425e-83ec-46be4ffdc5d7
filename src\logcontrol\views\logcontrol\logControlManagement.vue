<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 日志控制管理主页面 - 使用标签页模式
-->
<template>
  <div class="view app-container">


    <el-tabs v-model="activeTab" style="padding: 0 10px" @tab-click="handleTabClick">
      <el-tab-pane label="仪表板" name="dashboard" lazy>
        <Dashboard ref="dashboard" />
      </el-tab-pane>
      <el-tab-pane label="配置管理" name="configManagement" lazy>
        <ConfigManagement ref="configManagement" />
      </el-tab-pane>
      <el-tab-pane label="设备管理" name="deviceManagement" lazy>
        <DeviceManagement ref="deviceManagement" />
      </el-tab-pane>

      <el-tab-pane label="日志查看" name="logAnalysis" lazy>
        <LogAnalysis ref="logAnalysis" />
      </el-tab-pane>
      <el-tab-pane label="崩溃分析" name="crashAnalysis" lazy>
        <CrashAnalysis ref="crashAnalysis" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Dashboard from './dashboard.vue'
import ConfigManagement from './configManagement.vue'
import DeviceManagement from './deviceManagement.vue'

import LogAnalysis from './logAnalysis.vue'
import CrashAnalysis from './crashAnalysis.vue'

export default {
  name: 'LogControlManagement',
  components: {
    Dashboard,
    ConfigManagement,
    DeviceManagement,
    LogAnalysis,
    CrashAnalysis
  },
  data() {
    return {
      activeTab: 'dashboard'
    }
  },
  mounted() {
    // 根据URL参数设置默认tab
    const tab = this.$route.query.tab
    const validTabs = ['dashboard', 'configManagement', 'deviceManagement', 'logAnalysis', 'crashAnalysis']
    
    if (tab && validTabs.includes(tab)) {
      this.activeTab = tab
    }
  },
  watch: {
    // 监听路由变化，更新活跃标签
    '$route.query.tab'(newTab) {
      const validTabs = ['dashboard', 'configManagement', 'deviceManagement', 'logAnalysis', 'crashAnalysis']
      if (newTab && validTabs.includes(newTab)) {
        this.activeTab = newTab
      }
    }
  },
  methods: {
    // 处理标签点击
    handleTabClick(tab) {
      // 更新URL参数，但不刷新页面
      this.$router.replace({
        path: this.$route.path,
        query: {
          ...this.$route.query,
          tab: tab.name
        }
      })
    },
    
    // 切换到指定标签页（供外部调用）
    switchToTab(tabName) {
      const validTabs = ['dashboard', 'configManagement', 'deviceManagement', 'logAnalysis', 'crashAnalysis']
      if (validTabs.includes(tabName)) {
        this.activeTab = tabName
        this.handleTabClick({ name: tabName })
      }
    },



    // 获取当前活跃的组件实例
    getCurrentComponent() {
      // 由于使用了 lazy 加载，我们需要通过事件总线或其他方式来触发刷新
      // 这里我们使用一个简化的方法：直接调用子组件的刷新方法
      const componentMap = {
        dashboard: this.$refs.dashboard,
        configManagement: this.$refs.configManagement,
        deviceManagement: this.$refs.deviceManagement,
        logAnalysis: this.$refs.logAnalysis,
        crashAnalysis: this.$refs.crashAnalysis
      }

      return componentMap[this.activeTab]
    }
  }
}
</script>

<style lang="scss" scoped>
.view {


  .el-tabs {
    ::v-deep .el-tabs__header {
      margin: 0 0 20px 0;
      
      .el-tabs__nav-wrap {
        &::after {
          height: 1px;
          background-color: #e4e7ed;
        }
      }
      
      .el-tabs__item {
        padding: 0 20px;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        color: #606266;
        
        &.is-active {
          color: #409EFF;
          font-weight: 500;
        }
        
        &:hover {
          color: #409EFF;
        }
      }
    }
    
    ::v-deep .el-tabs__content {
      .el-tab-pane {
        min-height: 500px;
      }
    }
  }
}
</style>
