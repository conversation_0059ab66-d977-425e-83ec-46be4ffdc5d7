<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-24 11:35:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-01 13:54:52
 * @Description: 维修工单详情
 -->

<template>
  <div v-show="dialogVisible" class="container">
    <ProDrawer
      :value="dialogVisible"
      :title="title"
      size="75%"
      :confirm-loading="confirmLoading"
      :no-confirm-footer="true"
      @cancel="closeDrawer"
    >
      <div class="dialog-content-box">
        <div>
          <div class="boxa box0">
            <div class="tit-box">维修进度跟踪</div>
            <div class="sp-content jbxx-box steps-container">
              <el-steps :active="stepActive">
                <el-step
                  index="CREATE"
                  title="发起报修"
                  :description="formData.createdAt"
                ></el-step>
                <el-step
                  index="ENGINEER_RECEIVE"
                  title="工程师接单"
                  :description="formData.orderReceiveTime"
                ></el-step>
                <el-step
                  index="ENGINEER_DEPARTURE"
                  title="工程师出发"
                  :description="formData.departureTime"
                ></el-step>

                <el-step
                  index="ENGINEER_ARRIVE"
                  title="到店维修"
                  :description="formData.actualArriveTime"
                ></el-step>
                <el-step
                  index="WAIT_CONFIRM"
                  :description="formData.sendReportTime"
                  title="提交维修报告"
                ></el-step>
                <el-step
                  index="DONE"
                  :description="formData.completedAt"
                  title="已完成"
                ></el-step>
              </el-steps>
              <!-- 时间差显示 -->
              <div class="duration-labels">
                <div v-if="formData.orderReceiveTime" class="duration-item">
                  {{
                    getTimeDiff(formData.createdAt, formData.orderReceiveTime)
                  }}
                </div>
                <div v-if="formData.departureTime" class="duration-item">
                  {{
                    getTimeDiff(
                      formData.orderReceiveTime,
                      formData.departureTime
                    )
                  }}
                </div>
                <div v-if="formData.actualArriveTime" class="duration-item">
                  {{
                    getTimeDiff(
                      formData.departureTime,
                      formData.actualArriveTime
                    )
                  }}
                </div>
                <div v-if="formData.sendReportTime" class="duration-item">
                  {{
                    getTimeDiff(
                      formData.actualArriveTime,
                      formData.sendReportTime
                    )
                  }}
                </div>
                <div v-if="formData.completedAt" class="duration-item">
                  {{
                    getTimeDiff(formData.sendReportTime, formData.completedAt)
                  }}
                </div>
              </div>
            </div>
          </div>
          <div class="boxa box1">
            <div class="tit-box">工单信息</div>
            <div class="card-box">
              <el-descriptions :column="2">
                <el-descriptions-item label="工单编号">
                  {{ formData.code }}
                </el-descriptions-item>
                <el-descriptions-item label="客户编号">
                  {{ formData.customer?.seqId }}
                </el-descriptions-item>
                <el-descriptions-item label="工单状态">
                  {{ formData.status?.label }}
                </el-descriptions-item>
                <el-descriptions-item label="店铺名称">
                  {{ formData.customer?.shopRecruitment }}
                </el-descriptions-item>
                <el-descriptions-item label="店铺地址">
                  {{ formData.customer?.address }}
                </el-descriptions-item>
                <el-descriptions-item label="报修人电话"></el-descriptions-item>
                <el-descriptions-item label="设备组图" :span="2">
                  <div>
                    <el-image
                      class="imgs1"
                      :src="
                        formData.deviceGroupImg && formData.deviceGroupImg.url
                          ? formData.deviceGroupImg.url
                          : require('../../../assets/images/top.png')
                      "
                      alt=""
                      :preview-src-list="[formData.deviceGroupImg?.url]"
                    ></el-image>
                    <div
                      style="float: left; margin-left: 10px; font-weight: bold"
                    >
                      <div>{{ formData.deviceGroup?.label }}</div>
                      <div>{{ formData.productInfo }}</div>
                    </div>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="故障描述" :span="2">
                  {{ formData.excDesc }}
                </el-descriptions-item>
                <el-descriptions-item label="故障照片" :span="2">
                  <div style="display: flex; width: 100%; flex-wrap: wrap">
                    <el-image
                      v-for="(item, index) in formData.excPics"
                      :key="index"
                      class="imgs"
                      :src="item.url"
                      alt=""
                      :preview-src-list="[item.url]"
                    ></el-image>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <div class="card-box">
              <el-descriptions :column="2">
                <!-- <el-descriptions-item label="报修发起时间">{{
                  form.createdAt
                }}</el-descriptions-item> -->
                <el-descriptions-item label="期望到店时间">
                  {{ formData.expectArriveTime }}
                </el-descriptions-item>
                <el-descriptions-item label="预计到店时间">
                  {{ formData.prospectArriveTime }}
                </el-descriptions-item>
                <el-descriptions-item label="接单工程师">
                  {{ formData.engineerId?.name }}
                </el-descriptions-item>
                <el-descriptions-item label="接单时长">
                  {{ formData.orderReceiveTime }}
                </el-descriptions-item>
                <el-descriptions-item label="路途时长">
                  {{ formData.travelTime }}
                </el-descriptions-item>
                <el-descriptions-item label="维修时长">
                  {{ formData.fixTime }}
                </el-descriptions-item>
                <!--<el-descriptions-item label="付款时长"></el-descriptions-item>-->
                <el-descriptions-item
                  v-if="formData.repairReport"
                  label="维修报告"
                >
                  <el-button
                    type="primary"
                    size="small"
                    style="padding: 4px 8px"
                    @click="handleReport(formData.id)"
                  >
                    查看
                  </el-button>
                </el-descriptions-item>
                <!--<el-descriptions-item v-if="formData.isAppeal" label="申诉次数">-->
                <!--  {{ formData.appealCount }} 次-->
                <!--</el-descriptions-item>-->
              </el-descriptions>
            </div>
            <div class="card-box">
              <el-descriptions :column="2">
                <!--<el-descriptions-item label="上门费">-->
                <!--  {{ form.visitPay }} 元-->
                <!--</el-descriptions-item>-->
                <!--<el-descriptions-item label="远程误工费">-->
                <!--  {{ form.longWayVisitPay || 0 }} 元-->
                <!--</el-descriptions-item>-->
                <!--<el-descriptions-item label="零件更换费">-->
                <!--  {{ form.actualReplacePay || 0 }} 元-->
                <!--</el-descriptions-item>-->
                <el-descriptions-item label="维修诊断费" :span="2">
                  <span style="color: #ff541e">
                    ¥ {{ formData.repairPay || "0.00" }} 元
                  </span>
                </el-descriptions-item>
                <!--<el-descriptions-item label="工程师减免费用">-->
                <!--  -{{ form.derateAmount || 0 }} 元-->
                <!--</el-descriptions-item>-->
                <!--<el-descriptions-item label="工程师加价">-->
                <!--  {{ form.engineerAdditionalPay || 0 }}元-->
                <!--</el-descriptions-item>-->

                <!--<el-descriptions-item label="客户追加报酬">-->
                <!--  {{ form.additionalPay || 0 }} 元-->
                <!--</el-descriptions-item>-->
                <!--<el-descriptions-item label="会员减免" :span="2">-->
                <!--  -{{ form.discountAmount || 0 }} 元-->
                <!--</el-descriptions-item>-->
                <!--<el-descriptions-item label="技术咨询费用合计" :span="2">-->
                <!--  <span style="color: #ff541e">-->
                <!--    ¥ {{ form.laborCost || "0.00" }} 元-->
                <!--  </span>-->
                <!--</el-descriptions-item>-->
                <el-descriptions-item label="耗材费用合计" :span="2">
                  <span style="color: #ff541e">
                    ¥ {{ formData.itemPay || "0.00" }} 元
                  </span>
                </el-descriptions-item>

                <el-descriptions-item label="总费用" :span="2">
                  <span style="color: #ff541e">
                    ¥ {{ formData?.totalAmount }} 元
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="实付费用" :span="2">
                  <span style="color: #ff541e">
                    ¥ {{ formData?.totalPay || "0.00" }} 元
                  </span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
          <div
            v-if="formData.isEvaluated"
            class="card-box evaluation-container"
          >
            <div class="evaluate-content">
              <div class="evaluate-header">
                <i class="el-icon-star-on"></i>
                <span>服务评价</span>
              </div>

              <div class="evaluate-body">
                <div class="evaluate-item">
                  <span class="label">专业能力:</span>
                  <el-rate
                    v-model="formData.workEvaluate.professional"
                    show-text
                    :texts="['非常差', '差', '一般', '好', '非常好']"
                    disabled
                  ></el-rate>
                </div>

                <div class="evaluate-item">
                  <span class="label">服务态度:</span>
                  <el-rate
                    v-model="formData.workEvaluate.service"
                    show-text
                    :texts="['非常差', '差', '一般', '好', '非常好']"
                    disabled
                  ></el-rate>
                </div>

                <div class="evaluate-content-box">
                  <div class="content-header">
                    <span class="label">评价内容:</span>
                  </div>
                  <div class="content-body">
                    {{ formData.workEvaluate.content || "暂无评价内容" }}
                  </div>
                </div>

                <div class="evaluate-item time-item">
                  <span class="label">评价时间:</span>
                  <span class="content">
                    {{ formData.workEvaluate.createdAt || "暂无评价时间" }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button
          v-if="
            formData.status?.value !== 'close' &&
            formData.status?.value !== 'completed'
          "
          type="danger"
          @click="handleDelete(formData.id)"
        >
          关闭工单
        </el-button>
        <el-button
          v-if="formData.status?.value === 'pending_orders'"
          type="primary"
          @click="
            handleCustomerWorkOrderStatus(
              formData.id,
              formData.productId,
              'allot'
            )
          "
        >
          指派工程师
        </el-button>
        <el-button
          v-if="
            formData.status?.value === 'engineer_receive' ||
            formData.status?.value === 'engineer_departure' ||
            formData.status?.value === 'engineer_arrive'
          "
          type="primary"
          @click="
            handleCustomerWorkOrderStatus(
              formData.id,
              formData.productId,
              'transfer'
            )
          "
        >
          转派给其他工程师
        </el-button>
      </template>
    </ProDrawer>
    <ProDialog
      :value="dialogVisible2"
      :title="'维修报告'"
      width="60%"
      :top="'0px'"
      :no-footer="true"
      @cancel="dialogVisible2 = false"
    >
      <div class="dialog-content-box">
        <div style="height: 700px; overflow: hidden; overflow-y: scroll">
          <div class="boxa box0">
            <div class="tit-box">故障描述</div>
            <div style="line-height: 20px; width: 80%">
              {{ formReport?.excDesc }}
            </div>
            <div style="display: flex; width: 100%; flex-wrap: wrap">
              <el-image
                v-for="(item, index) in formReport?.excDescPics"
                :key="index"
                class="imgs"
                :src="item.url"
                alt=""
                :preview-src-list="[item.url]"
              ></el-image>
            </div>
          </div>
          <div class="boxa box1">
            <div class="tit-box">解决措施</div>
            <div>
              <div style="line-height: 20px; width: 80%">
                {{ formReport?.resolveDesc }}
              </div>

              <div style="display: flex; width: 100%; flex-wrap: wrap">
                <el-image
                  v-for="(item, index) in formReport?.resolveDescPics"
                  :key="index"
                  class="imgs"
                  :src="item.url"
                  alt=""
                  :preview-src-list="[item.url]"
                ></el-image>
              </div>
            </div>
            <div class="tit-box">下次注意事项</div>
            <div>
              <div style="line-height: 20px; width: 80%">
                {{
                  formReport?.announcements ? formReport?.announcements : "无"
                }}
              </div>
            </div>
            <div class="tit-box">其他</div>
            <div style="line-height: 30px">
              现象分类：{{ formReport?.excType?.label }}
            </div>
            <div style="line-height: 30px">
              原因分类：{{ formReport?.reasonType?.label }}
            </div>
            <div style="line-height: 30px">
              处理类型：{{ formReport?.resolveType?.label }}
            </div>
            <div style="line-height: 30px">
              故障组件：{{ formReport?.excUnit?.label }}
            </div>
            <div style="line-height: 30px">
              黑白计数器：{{ formReport?.blackWhiteCount }}
            </div>
            <div style="line-height: 30px">
              彩色计数器：{{ formReport?.colorCount }}
            </div>
            <div style="line-height: 30px">
              五色计数器：{{ formReport?.fiveColorCount }}
            </div>
            <div style="line-height: 30px">
              黑白废张：{{ formReport?.blackWhiteExclude }}
            </div>
            <div style="line-height: 30px">
              彩色废张：{{ formReport?.colorExclude }}
            </div>
            <div style="line-height: 30px">
              五色废张：{{ formReport?.fiveColorExclude }}
            </div>
            <div style="line-height: 30px">
              上次维修后到目前的印量：{{ formReport?.printCount }}
            </div>
            <div style="line-height: 30px">
              维修费用：{{ formReport?.repairPay }}
            </div>

            <div class="tit-box">更换耗材零件</div>
            <div>
              <DataTable
                ref="ProTable1"
                :columns="columns1"
                :show-setting="false"
                :show-pagination="false"
                :show-search="false"
                row-key="index"
                :data="tableData1"
                sticky
                :height="350"
                style="width: 100%; margin-top: 20px"
                :show-table-operator="false"
              >
                <template #imageFiles="{ row }">
                  <el-image
                    :preview-src-list="[row?.imageFiles[0]?.url]"
                    style="width: 100px; height: 100px"
                    :src="row?.imageFiles[0]?.url"
                  ></el-image>
                </template>
              </DataTable>
            </div>
          </div>
        </div>
      </div>
    </ProDialog>
    <!-- 指派、转让工单 -->
    <ProDialog
      :value="workOrderDialogVisible"
      :title="allotTitle"
      width="600px"
      :confirm-loading="confirmLoading"
      confirm-text="确认选择"
      :top="'10%'"
      @ok="handleDialogOk"
      @cancel="workOrderDialogVisible = false"
    >
      <ProForm
        v-if="workOrderDialogVisible"
        ref="ProForm"
        :form-param="engineerForm"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        open-type="edit"
        @proSubmit="proSubmit"
      ></ProForm>
    </ProDialog>
  </div>
</template>

<script>
import {
  WorkOrderDetailInfoApi,
  getReportApi,
  closeOrderApi,
  engineerListApi,
  changeEngineerApi,
  changeOrderApi,
} from "@/api/repair";
import { cloneDeep } from "lodash";
import { getTimeDiff } from "@/utils";
export default {
  name: "WorkOrderDetail",
  props: {
    title: {
      type: String,
      default: "工单详情",
    },
  },
  data() {
    return {
      stepActive: 0,
      stepList: [
        "CREATE",
        "ENGINEER_RECEIVE",
        "ENGINEER_DEPARTURE",
        "ENGINEER_ARRIVE",
        "WAIT_CONFIRM",
        "DONE",
      ],
      dialogVisible: false,
      confirmLoading: false,
      formData: {},
      formReport: {},
      dialogVisible2: false,
      tableData1: [],
      columns1: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          minWidth: 130,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          minWidth: 130,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "imageFiles",
          title: "物品图片",
          isTable: true,
          tableSlot: "imageFiles",
          width: 150,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "单价",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "num",
          title: "更换数量",
          minWidth: 80,
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "location",
          title: "更换位置",
          width: 120,
          isTable: true,
          formatter: (row) =>
            Array.isArray(row.location) ? row.location.join("、") : "",
        },
        {
          dataIndex: "itemStore",
          title: "更换类型",
          isTable: true,
          minWidth: 100,
          formatter: (row) => {
            switch (row.skuSource) {
              case "ENGINEER_APPLY":
                return "工程师外带";
              default:
                return "客户自配";
            }
          },
        },
      ],
      workOrderDialogVisible: false,
      allotTitle: "指派工单",
      engineerForm: {},
      formColumns: [],
    };
  },
  methods: {
    getTimeDiff,
    show(tradeOrderNumber) {
      this.formData = {};
      this.confirmLoading = true;
      WorkOrderDetailInfoApi(tradeOrderNumber)
        .then((res) => {
          this.formData = cloneDeep(res.data);
          this.stepActive = this.stepList.indexOf(this.formData.currentProcess);
        })
        .finally(() => {
          this.confirmLoading = false;
        });
      this.$nextTick(() => {
        this.dialogVisible = true;
      });
    },
    handleReport(id) {
      this.formReport = {};
      getReportApi(id).then((res) => {
        this.dialogVisible2 = true;
        this.formReport = cloneDeep(res.data.repairReport);
        this.tableData1 = res.data?.replaceOrder?.replaceDetailList;
      });
    },
    handleDelete(id) {
      this.$confirm("是否确认关闭当前工单", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const args = {
          needPay: false,
          id: id,
        };
        closeOrderApi(args).then(() => {
          this.$message.success("关闭成功");
          this.$emit("refreshWorkOrder");
          this.dialogVisible = false;
        });
      });
    },
    handleCustomerWorkOrderStatus(id, productId, type) {
      this.workOrderDialogVisible = true;
      this.allotTitle = type === "allot" ? "指派工单" : "转派工单";
      this.formColumns = [
        {
          clearboth: true,
          dataIndex: "engineerId",
          title: "工程师",
          isForm: true,
          formSpan: 24,
          valueType: "select",
          option: [],
          optionMth: () => engineerListApi(productId),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择工程师",
              trigger: "change",
            },
          ],
        },
      ];
      this.engineerForm = {
        ...this.engineerForm,
        workOrderId: id,
        productId: productId,
        type: type,
      };
    },
    // 确认指派、分配工单
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    // 确认提交工单分配
    proSubmit(val) {
      const confirmText = val.type === "allot" ? "指派工单" : "转派工单";
      this.$confirm(`是否确认${confirmText}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const args = {
          engineerId: val.engineerId,
          id: val.workOrderId,
        };
        const editApi =
          val.type === "allot" ? changeEngineerApi : changeOrderApi;
        editApi(args).then(() => {
          this.$message.success("操作成功");
          this.workOrderDialogVisible = false;
          this.engineerForm = {};
          this.show(this.formData.code);
          this.$emit("refreshWorkOrder");
        });
      });
    },
    handleUpdate(id, productId, type) {
      this.form1 = {};
      this.form1.id = id;
      this.allotType = type;
      this.allotTitle =
        type === "transfer" ? "转派给其他工程师" : "分配给其他工程师";
      this.methodType = "edit";
      this.dialogVisible1 = true;
      this.formcolumns = [
        {
          clearboth: true,
          dataIndex: "engineerId",
          title: "工程师",
          isForm: true,
          formSpan: 24,
          valueType: "select",
          option: [],
          optionMth: () => engineerListApi(productId),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择工程师",
              trigger: "change",
            },
          ],
        },
      ];
    },
    closeAppealDialog() {
      this.appealValue = false;
    },
    closeDrawer() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style scoped lang="scss">
.imgs {
  height: 120px;
  margin: 10px;
  max-width: calc((100% - 80px) / 4);
}
.imgs1 {
  height: 120px;
  width: 120px;
  float: left;
}
.card-box {
  border-bottom: 5px solid #f1eeee;
  margin: 10px 0;
  padding: 0 20px;
}
.dialog-content-box {
  position: relative;
  height: 100%;
  overflow: scroll;
  /* padding-bottom: 80px; */
  .steps-box {
    position: absolute;
    width: 80%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    z-index: 2;
  }

  .content-fix {
    height: calc(100vh - 110px);
    overflow: auto;
  }

  .tit-box {
    width: 100%;
    padding: 5px 10px;
    color: #409eff;
    position: relative;
    margin: 20px auto;
    font-size: 16px;
    font-weight: 800;

    &::before {
      content: "";
      width: 5px;
      height: 20px;
      background: #409eff;
      display: inline-block;
      position: absolute;
      left: -1px;
      top: 4px;
    }
  }
}
.steps-container {
  position: relative;
  .duration-labels {
    position: absolute;
    top: -10px;
    bottom: 0;
    display: flex;
    width: calc(100% - 112px);
    pointer-events: none;
  }

  .duration-item {
    width: 20%;
    font-size: 12px;
    color: #409eff;
    text-align: center;
  }
}
.evaluation-container {
  background: #fff;
  border-radius: 8px;
  //box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  .evaluate-content {
    padding: 20px 0;

    .evaluate-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;

      i {
        color: #ffc107;
        margin-right: 8px;
        font-size: 20px;
      }

      span {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .evaluate-body {
      .evaluate-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .label {
          width: 80px;
          color: #606266;
          font-size: 14px;
        }

        &.time-item {
          color: #909399;
          font-size: 13px;
        }
      }

      .evaluate-content-box {
        background: #f8f9fa;
        border-radius: 4px;
        margin: 15px 0;

        .content-header {
          padding: 10px 15px;
          border-bottom: 1px solid #ebeef5;

          .label {
            color: #606266;
            font-size: 14px;
          }
        }

        .content-body {
          padding: 15px;
          min-height: 60px;
          color: #606266;
          font-size: 14px;
          line-height: 1.6;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }
  }
}
</style>
