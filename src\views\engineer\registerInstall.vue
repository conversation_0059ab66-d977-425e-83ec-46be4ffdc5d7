<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-10-12 10:04:18
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-10-12 10:45:57
 * @FilePath: \benyin-web\src\views\engineer\registerInstall.vue
 * @Description: 工程师 - 登记安装
 * 
-->

<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="完整度统计" name="first">
        <RegisterInstallStat type="full" :columns="fullColumns" />
      </el-tab-pane>
      <el-tab-pane label="安装明细" name="second">
        <RegisterInstallStat type="detail" :columns="detailColumns" />
      </el-tab-pane>
      <el-tab-pane label="安装统计" name="third">
        <RegisterInstallStat type="stat" :columns="statColumns" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import RegisterInstallStat from "./components/registerInstallStat.vue";
export default {
  name: "RegisterInstall",
  components: { RegisterInstallStat },
  data() {
    return {
      activeName: "first",
      fullColumns: [
        {
          dataIndex: "companyName",
          title: "归属公司",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "工程师",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "month",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
      ],
      detailColumns: [],
      statColumns: [],
    };
  },
};
</script>

<style scoped lang="scss"></style>
