<template>
  <div class="services-page-editor">
      <!-- 页面标题 -->
      <div class="page-title-section">
        <EditableContent
          v-model="content.title"
          display-class="editable-page-title"
          placeholder="点击编辑页面标题"
          @change="handleContentChange"
        />
      </div>

      <!-- Hero区域 -->
      <section class="hero-section">
        <div class="hero-content">
          <EditableContent
            v-model="content.config.servicesHeroTitle"
            display-class="editable-hero-title"
            placeholder="点击编辑主标题"
            @change="handleContentChange"
          />
          <EditableContent
            v-model="content.config.servicesHeroSubtitle"
            display-class="editable-hero-subtitle"
            placeholder="点击编辑副标题"
            multiline
            :rows="2"
            @change="handleContentChange"
          />
        </div>
      </section>

      <!-- 主要描述区域 -->
      <section class="main-description-section">
        <div class="container">
          <EditableContent
            v-model="content.config.servicesMainDescription"
            display-class="editable-main-description"
            placeholder="点击编辑主要服务描述"
            multiline
            :rows="3"
            @change="handleContentChange"
          />
        </div>
      </section>

        <!-- 服务项目区域 -->
        <section class="services-section">
          <div class="container">
            <!-- 标题和添加按钮 -->
            <div class="section-header">
              <h3 class="section-title">服务项目</h3>
              <el-button
                type="dashed"
                icon="el-icon-plus"
                @click="openServiceModal(-1)"
                class="add-service-header-btn"
              >
                添加服务项目
              </el-button>
            </div>

            <div class="services-grid">
              <div
                v-for="(service, index) in content.config.serviceItems"
                :key="`service-${index}-${service.title}`"
                class="service-item"
              >
                <div class="service-icon">
                  <span v-if="service.icon" class="icon-emoji">{{ service.icon }}</span>
                  <i v-else class="el-icon-picture icon-placeholder"></i>
                </div>
                <div class="service-content">
                  <h4 class="service-title">{{ service.title }}</h4>
                  <p class="service-description">{{ service.description }}</p>
                </div>
              <div class="service-actions">
                <el-button size="mini" type="primary" icon="el-icon-edit" @click="editService(index)">
                  编辑
                </el-button>
                <el-button size="mini" type="danger" @click="removeService(index)">
                  删除
                </el-button>
              </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 服务流程区域 -->
        <section class="process-section">
          <div class="container">
          <div class="text-center">
            <EditableContent
              v-model="content.config.servicesProcessTitle"
              display-class="editable-section-title"
              placeholder="点击编辑流程标题"
              @change="handleContentChange"
            />
            <p class="edit-hint">流程步骤（用逗号分隔）：</p>
            <EditableContent
              v-model="content.config.servicesProcessSteps"
              display-class="editable-process-steps"
              placeholder="故障诊断,维修方案制定,专业维修,质量检测,售后保障"
              @change="handleContentChange"
            />
          </div>

            <!-- 流程步骤显示 -->
            <div v-if="processSteps.length > 0" class="process-steps-display">
              <div
                v-for="(step, index) in processSteps"
                :key="index"
                class="process-step-item"
              >
                <div class="step-number">{{ index + 1 }}</div>
                <h3 class="step-title">{{ step }}</h3>
              </div>
            </div>
          </div>
        </section>

        <!-- 服务优势区域 -->
        <section class="advantages-section">
          <div class="container">
          <div class="text-center">
            <EditableContent
              v-model="content.config.servicesAdvantageTitle"
              display-class="editable-section-title"
              placeholder="点击编辑优势标题"
              @change="handleContentChange"
            />
            <p class="edit-hint">优势列表（用逗号分隔）：</p>
            <EditableContent
              v-model="content.config.servicesAdvantageList"
              display-class="editable-advantage-list"
              placeholder="专业技术团队,快速响应服务,质量保障承诺,合理收费标准,完善售后服务"
              @change="handleContentChange"
            />
          </div>

            <!-- 优势列表显示 -->
            <div v-if="advantageItems.length > 0" class="advantages-display">
              <div
                v-for="(advantage, index) in advantageItems"
                :key="index"
                class="advantage-item"
              >
                <div class="advantage-icon">✓</div>
                <span class="advantage-text">{{ advantage }}</span>
              </div>
            </div>
          </div>
        </section>

        <!-- CTA区域 -->
        <section class="cta-section">
          <div class="container">
            <EditableContent
              v-model="content.config.servicesCtaTitle"
              display-class="editable-cta-title"
              placeholder="点击编辑CTA标题"
              @change="handleContentChange"
            />
            <EditableContent
              v-model="content.config.servicesCtaSubtitle"
              display-class="editable-cta-subtitle"
              placeholder="点击编辑CTA副标题"
              @change="handleContentChange"
            />
            <a
              href="/contact"
              @click.prevent
              class="cta-button"
            >
              <EditableContent
                v-model="content.config.servicesCtaButtonText"
                placeholder="按钮文字"
                @change="handleContentChange"
              />
            </a>
          </div>
        </section>

        <!-- 服务项目编辑模态框 -->
        <el-dialog
          :title="editingServiceIndex >= 0 ? '编辑服务项目' : '添加服务项目'"
          :visible.sync="serviceModalVisible"
          width="500px"
          :close-on-click-modal="false"
          append-to-body
          modal-append-to-body
          destroy-on-close
          @close="resetServiceForm"
        >
          <el-form
            ref="serviceForm"
            :model="serviceFormData"
            :rules="serviceFormRules"
            label-width="80px"
            size="small"
          >
        <el-form-item label="图标" prop="icon" extra="建议使用emoji图标，如：🔧 ✅ 🕐 🚚">
          <div class="icon-input-group">
            <el-input
              v-model="serviceFormData.icon"
              placeholder="🔧"
              style="width: 200px;"
              @input="handleIconInput"
            />
            <div class="icon-preview">
              <span v-if="serviceFormData.icon" class="icon-display">{{ serviceFormData.icon }}</span>
              <span v-else class="icon-placeholder">预览</span>
            </div>
          </div>
          <div class="icon-suggestions">
            <span>常用图标：</span>
            <el-button
              v-for="icon in commonIcons"
              :key="icon"
              size="mini"
              type="text"
              @click="serviceFormData.icon = icon"
            >
              {{ icon }}
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="服务标题" prop="title">
          <el-input
            v-model="serviceFormData.title"
            placeholder="如：设备维修"
          />
        </el-form-item>

        <el-form-item label="服务描述" prop="description">
          <el-input
            type="textarea"
            v-model="serviceFormData.description"
            :rows="3"
            placeholder="详细描述这个服务..."
          />
        </el-form-item>
          </el-form>

          <div slot="footer" class="dialog-footer">
            <el-button @click="serviceModalVisible = false">取消</el-button>
            <el-button type="primary" @click="saveService" :loading="saving">
              {{ editingServiceIndex >= 0 ? '更新' : '添加' }}
            </el-button>
          </div>
        </el-dialog>
  </div>
</template>

<script>
import EditableContent from '@/websites/components/website/EditableContent.vue'

export default {
  name: 'ServicesPageEditor',
  components: {
    EditableContent
  },
  
  props: {
    content: {
      type: Object,
      required: true
    }
  },
  
  data() {
    return {
      // 服务编辑模态框相关
      serviceModalVisible: false,
      editingServiceIndex: -1,
      saving: false,

      // 服务表单数据
      serviceFormData: {
        icon: '',
        title: '',
        description: ''
      },

      // 表单验证规则
      serviceFormRules: {
        icon: [
          { required: true, message: '请输入图标', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '请输入服务标题', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入服务描述', trigger: 'blur' }
        ]
      },

      // 常用图标
      commonIcons: ['🔧', '🛠️', '⚙️', '🖨️', '💻', '📱', '🔌', '⚡', '🚚', '✅', '🕐', '💡']
    }
  },

  computed: {
    // 处理优势列表
    advantageItems() {
      const list = this.content.config?.servicesAdvantageList || ''
      return list.split(',').map(item => item.trim()).filter(item => item !== '')
    },

    // 处理流程步骤
    processSteps() {
      const steps = this.content.config?.servicesProcessSteps || ''
      return steps.split(',').map(item => item.trim()).filter(item => item !== '')
    }
  },

  mounted() {
    this.initializeContent()
  },
  
  methods: {
    // 初始化内容
    initializeContent() {
      if (!this.content.config) {
        this.$set(this.content, 'config', {})
      }
      
      // 设置默认值
      const defaults = {
        servicesHeroTitle: '专业可靠的设备维修服务',
        servicesHeroSubtitle: '我们提供全方位的设备维修解决方案，让您的设备重获新生',
        servicesMainDescription: '我们拥有专业的技术团队和先进的维修设备，为您提供高质量、快速、可靠的维修服务。',
        serviceItems: [
          { icon: "🖨️", title: "复印机维修", description: "专业维修各品牌复印机，快速诊断问题" },
          { icon: "🖨️", title: "打印机维修", description: "修复各种打印机故障" },
          { icon: "🔧", title: "设备保养", description: "定期保养维护，延长设备使用寿命" },
          { icon: "🛠️", title: "耗材更换", description: "提供原装和兼容耗材" }
        ],
        servicesProcessTitle: '我们的服务流程',
        servicesProcessSteps: '故障诊断,维修方案制定,专业维修,质量检测,售后保障',
        servicesAdvantageTitle: '选择我们的优势',
        servicesAdvantageList: '专业技术团队,快速响应服务,质量保障承诺,合理收费标准,完善售后服务',
        servicesCtaTitle: '需要专业维修服务？',
        servicesCtaSubtitle: '立即联系我们，获得快速专业的解决方案',
        servicesCtaButtonText: '立即咨询'
      }
      
      Object.keys(defaults).forEach(key => {
        if (!this.content.config[key]) {
          this.$set(this.content.config, key, defaults[key])
        }
      })
    },

    // 处理内容变化
    handleContentChange() {
      this.hasChanges = true
      this.$emit('change')
      this.$emit('update', this.content)
    },

    // 打开服务编辑模态框
    openServiceModal(index) {
      this.editingServiceIndex = index

      if (index >= 0) {
        // 编辑模式：填充现有数据
        const service = this.content.config.serviceItems[index]
        this.serviceFormData = {
          icon: service.icon || '',
          title: service.title || '',
          description: service.description || ''
        }
      } else {
        // 添加模式：重置表单
        this.resetServiceForm()
      }

      this.serviceModalVisible = true

      // 等待DOM更新后清除验证状态
      this.$nextTick(() => {
        if (this.$refs.serviceForm) {
          this.$refs.serviceForm.clearValidate()
        }
      })
    },

    // 重置服务表单
    resetServiceForm() {
      this.serviceFormData = {
        icon: '',
        title: '',
        description: ''
      }
      this.editingServiceIndex = -1

      if (this.$refs.serviceForm) {
        this.$refs.serviceForm.clearValidate()
      }
    },

    // 编辑服务
    editService(index) {
      this.openServiceModal(index)
    },

    // 添加服务（保持向后兼容）
    addService() {
      this.openServiceModal(-1)
    },

    // 删除服务
    removeService(index) {
      const service = this.content.config.serviceItems[index]
      const serviceName = service ? service.title || '未命名服务' : '服务'

      this.$confirm(`确定要删除服务项目"${serviceName}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        try {
          this.content.config.serviceItems.splice(index, 1)
          this.handleContentChange()
          this.$message.success(`服务项目"${serviceName}"已删除`)
        } catch (error) {
          console.error('删除服务失败:', error)
          this.$message.error('删除失败，请重试')
        }
      }).catch(() => {
        // 用户取消删除，不需要处理
      })
    },



    // 保存服务
    saveService() {
      this.$refs.serviceForm.validate((valid) => {
        if (valid) {
          this.saving = true

          try {
            const serviceData = {
              icon: this.serviceFormData.icon.trim(),
              title: this.serviceFormData.title.trim(),
              description: this.serviceFormData.description.trim()
            }

            if (this.editingServiceIndex >= 0) {
              // 更新现有服务
              this.$set(this.content.config.serviceItems, this.editingServiceIndex, serviceData)
              this.$message.success(`服务项目"${serviceData.title}"更新成功`)
            } else {
              // 添加新服务
              this.content.config.serviceItems.push(serviceData)
              this.$message.success(`服务项目"${serviceData.title}"添加成功`)
            }

            this.handleContentChange()
            this.serviceModalVisible = false
            this.resetServiceForm()

          } catch (error) {
            console.error('保存服务失败:', error)
            this.$message.error('保存失败，请重试')
          } finally {
            this.saving = false
          }
        } else {
          this.$message.error('请检查表单填写是否正确')
        }
      })
    },

    // 处理图标输入
    handleIconInput(value) {
      // 限制图标长度，避免输入过长内容
      if (value && value.length > 10) {
        this.serviceFormData.icon = value.substring(0, 10)
        this.$message.warning('图标内容过长，已自动截取')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.services-page-editor {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 16px 20px;

  // 页面标题样式
  .page-title-section {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 2rem;

    .editable-page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }

    // Hero区域样式
    .hero-section {
      background: #1f2937;
      color: white;
      padding: 64px 16px;
      border-radius: 8px;
      text-align: center;
      margin-bottom: 2rem;

      .hero-content {
        max-width: 800px;
        margin: 0 auto;
      }

      .editable-hero-title {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 16px;
      }

      .editable-hero-subtitle {
        font-size: 18px;
        opacity: 0.9;
      }
    }

    // 通用容器
    .container {
      width: 100%;
      padding: 0;
    }

    // 添加间距工具类
    .space-y-8 > * + * {
      margin-top: 2rem;
    }

    // 区域样式
    .main-description-section,
    .services-section,
    .process-section,
    .advantages-section,
    .cta-section {
      background: white;
      border-radius: 8px;
      padding: 32px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;

      .section-title {
        text-align: center;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 32px;
      }

      // 区域头部样式（标题+按钮）
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32px;

        .section-title {
          margin-bottom: 0;
          text-align: left;
        }

        .add-service-header-btn {
          border-style: dashed;
          border-color: #52c41a;
          color: #52c41a;

          &:hover {
            border-color: #73d13d;
            color: #73d13d;
          }
        }
      }
    }

      // 主要描述区域
      .main-description-section {
        .editable-main-description {
          font-size: 16px;
          color: #6b7280;
          line-height: 1.6;
          text-align: center;
          max-width: 800px;
          margin: 0 auto;
        }
      }

      // 服务网格
      .services-grid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        gap: 32px;
        margin-top: 40px;

        // 响应式布局
        @media (min-width: 768px) {
          grid-template-columns: repeat(2, 1fr);
        }

        .service-item {
          position: relative;
          background: #f8f9fa;
          padding: 24px;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          border: 1px solid #e5e7eb;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            background: white;
          }

          .service-icon {
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .icon-emoji {
              font-size: 48px; // 增大图标尺寸，对应React的text-4xl
            }

            .icon-placeholder {
              color: #9ca3af;
              font-size: 32px;
            }
          }

          .service-title {
            font-size: 20px; // 增大标题字体，对应React的text-xl
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
          }

          .service-description {
            color: #6b7280;
            line-height: 1.6;
            font-size: 16px; // 增大描述字体
          }

          .service-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            flex-direction: row;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;
            background: rgba(255, 255, 255, 0.9);
            padding: 4px;
            border-radius: 6px;
            backdrop-filter: blur(4px);
          }

          &:hover .service-actions {
            opacity: 1;
          }
        }


      }

      // 流程区域样式
      .process-section {
        background: #f8f9fa;

        .text-center {
          text-align: center;
          margin-bottom: 40px;
        }

        .editable-section-title {
          font-size: 28px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 16px;
        }

        .edit-hint {
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 8px;
        }

        .editable-process-steps {
          border: 2px dashed #d1d5db;
          border-radius: 8px;
          padding: 16px;
          background: white;
          font-size: 14px;
          color: #374151;
        }

        .process-steps-display {
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          gap: 16px;
          margin-top: 32px;

          // 响应式布局，对应React的grid-cols-1 md:grid-cols-5
          @media (min-width: 768px) {
            grid-template-columns: repeat(5, 1fr);
          }

          .process-step-item {
            text-align: center;

            .step-number {
              width: 48px;
              height: 48px;
              background: #dbeafe;
              color: #3b82f6;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: 600;
              margin: 0 auto 16px;
              font-size: 18px;
            }

            .step-title {
              font-size: 16px;
              font-weight: 600;
              color: #1f2937;
              margin: 0;
            }
          }
        }
      }

      // 优势区域样式
      .advantages-section {
        background: white;

        .text-center {
          text-align: center;
          margin-bottom: 40px;
        }

        .editable-section-title {
          font-size: 28px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 16px;
        }

        .edit-hint {
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 8px;
        }

        .editable-advantage-list {
          border: 2px dashed #d1d5db;
          border-radius: 8px;
          padding: 16px;
          background: white;
          font-size: 14px;
          color: #374151;
        }

        .advantages-display {
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          gap: 24px;
          margin-top: 32px;

          // 响应式布局，对应React的grid-cols-1 md:grid-cols-3 lg:grid-cols-5
          @media (min-width: 768px) {
            grid-template-columns: repeat(3, 1fr);
          }

          @media (min-width: 1024px) {
            grid-template-columns: repeat(5, 1fr);
          }

          .advantage-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 24px;
            background: #f8f9fa;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
              background: white;
            }

            .advantage-icon {
              width: auto;
              height: auto;
              background: transparent;
              color: #3b82f6; // 蓝色，对应React的text-blue-600
              border-radius: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: 600;
              font-size: 48px; // 对应React的text-3xl
              margin-bottom: 16px;
              flex-shrink: 0;
            }

            .advantage-text {
              color: #1f2937;
              font-size: 16px;
              font-weight: 600; // 对应React的font-semibold
            }
          }
        }
      }

      // CTA区域样式
      .cta-section {
        background: #3b82f6;
        color: white;
        text-align: center;
        padding: 48px 32px;

        .editable-cta-title {
          font-size: 32px;
          font-weight: 700;
          color: white;
          margin-bottom: 16px;
        }

        .editable-cta-subtitle {
          font-size: 20px;
          color: white;
          margin-bottom: 32px;
          opacity: 0.9;
        }

        .cta-button {
          background: white;
          color: #3b82f6;
          padding: 12px 32px;
          border-radius: 8px;
          font-weight: 600;
          text-decoration: none;
          display: inline-block;
          transition: all 0.3s ease;

          &:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
  

// 服务编辑模态框样式
.icon-input-group {
  display: flex;
  align-items: center;
  gap: 12px;

  .icon-preview {
    width: 40px;
    height: 32px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;

    .icon-display {
      font-size: 18px;
    }

    .icon-placeholder {
      font-size: 12px;
      color: #c0c4cc;
    }
  }
}

.icon-suggestions {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;

  span {
    margin-right: 8px;
  }

  .el-button {
    padding: 2px 4px;
    margin: 0 2px;
    font-size: 14px;
    min-height: auto;
  }
}



.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 1200px) {
  .services-page-editor {
    .container {
      padding: 0 16px;
    }
  }
}

@media (max-width: 768px) {
  .icon-input-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .icon-suggestions {
    .el-button {
      margin: 2px;
    }
  }
}
</style>
