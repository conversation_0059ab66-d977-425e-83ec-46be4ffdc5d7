<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 16:49:57
 * @Description: 
 -->
<!--
 * @Description: 
 * @Autor: shh
 * @Date: 2023-10-16 15:17:39
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 16:49:57
-->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-search
      show-loading
      show-pagination
      :tree-props="{ children: 'customers', hasChildren: 'hasChildren' }"
      @loadData="loadData"
    >
      <template #eventSource="{ row }">
        {{
          row.eventSource === "MALL_HOME"
            ? "商城"
            : row.eventSource === "KNOWLEDGE_HOME"
            ? "知识库"
            : ""
        }}
      </template>
      <template #eventType="{ row }">
        {{
          row.eventType === "NO_LOGIN"
            ? "未登录"
            : row.eventType === "Key_word"
            ? "关键字搜索"
            : row.eventType === "Key_params"
            ? "关键字+参数搜索"
            : ""
        }}
      </template>
      <template #total>
        <el-input
          v-model="queryParam.minTotal"
          :min="0"
          :controls="false"
          :max="1000000"
          label="库存数量"
          placeholder="最小结果"
          style="width: 120px"
        ></el-input>
        <span> - </span>
        <el-input
          v-model="queryParam.maxTotal"
          :min="0"
          :controls="false"
          :max="1000000"
          label="库存数量"
          clearable
          placeholder="最大结果"
          style="width: 120px"
        ></el-input>
      </template>
    </ProTable>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import { getCustomerSearchInfoApi } from "@/api/customer";
import { Message } from "element-ui";
export default {
  name: "SearchInfo",
  components: { ProTable },
  data() {
    const self = this;
    return {
      tableData: [],
      columns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerName",
          title: "店铺名",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "eventSource",
          title: "来源页面",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "select",
          tableSlot: "eventSource",
          option: [
            {
              label: "商城",
              value: "MALL_HOME",
            },
            {
              label: "知识库",
              value: "KNOWLEDGE_HOME",
            },
          ],
        },
        // {
        //   dataIndex: "sourcePhone",
        //   title: "来源电话",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "sourceRole",
        //   title: "账号角色",
        //   isTable: true,
        // },
        {
          dataIndex: "eventType",
          title: "事件类型",
          isTable: true,
          tableSlot: "eventType",
        },
        {
          dataIndex: "originParam",
          title: "搜索关键字",
          isTable: true,
          minWidth: 300,
        },
        {
          dataIndex: "total",
          title: "返回结果数",
          isTable: true,
          isSearch: true,
          formatter: (row) => (row.total !== undefined ? row.total : "/"),
          searchSlot: "total",
          valueType: "input",
        },
        {
          dataIndex: "createTime",
          title: "创建时间",
          isTable: true,
          isSearch: true,
          isExport: false,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          attrs: {
            change(val) {
              if (!val) {
                self.queryParam.createStartTime = null;
                self.queryParam.createEndTime = null;
                return;
              }
              self.queryParam.createStartTime = val[0];
              self.queryParam.createEndTime = val[1];
              console.log(self.queryParam);
            },
          },
          clearable: true,
        },
      ],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {},
      minTotal: "",
      maxTotal: "",
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    async loadData(params) {
      const requestParameters = Object.assign(this.queryParam, params);
      // this.queryParam = {
      //     ...this.queryParam,
      //     ...params,
      //     minTotal: this.minTotal,
      //     maxTotal: this.maxTotal,
      //     page: 1,
      //     pageSize: 10,
      // };
      // delete this.queryParam.createTime;
      try {
        const result = await getCustomerSearchInfoApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style lang="scss" scoped>
.group {
}
</style>
