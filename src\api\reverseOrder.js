/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-07 15:39:48
 * @Description:
 */

import { get, post, put, del, down } from "@/utils/request";

// ================== 分类管理 ====================
export const reverseOrderPcPageApi = (data) =>
  get(`/reverseManager/page`, data); // pc端-分页查询
export const reverseOrderAuditApi = (data) =>
  put(`/reverseManager/audit`, data); // pc端-审核接口

export const reverseOrderOneApi = (id) => get(`/reverseManager/detail/${id}`); // pc端-详情接口
// 根据单号查耗材退货明细
export const reverseOrderDetailApi = (tradeOrderNum) =>
  get(`/reverseManager/getByTradeOrderNum/${tradeOrderNum}`);

// 退货统计
export const reverseOrderStatisticsApi = (data) =>
  get(`/reverseManager/total`, data);
