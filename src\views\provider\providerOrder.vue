<!--
 * @Author: wskg
 * @Date: 2024-08-09 11:52:32
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-01 11:52:20
 * @Description: 供应商 - 订单
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :layout="{ labelWidth: '100px' }"
      show-selection
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #btn>
        <el-button type="success" size="mini" @click="handleBatchPayment">
          生成付款单
        </el-button>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
          <el-button
            v-if="row.status?.value === 'WAIT_AUDIT'"
            size="mini"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'audit')"
          >
            去审核
          </el-button>
          <el-button
            v-if="row.status?.value === 'WAIT_DELIVERY'"
            size="mini"
            icon="el-icon-discount"
            @click="handleDelivery(row.code)"
          >
            去发货
          </el-button>
          <!--<el-button-->
          <!--  v-if="row.status?.value === 'SUCCESS'"-->
          <!--  size="mini"-->
          <!--  icon="el-icon-edit-outline"-->
          <!--&gt;-->
          <!--  去开票-->
          <!--</el-button>-->
          <el-button
            v-if="
              row.status?.value === 'WAIT_DELIVERY' &&
              row.settleStatus?.value === 'NO_SETTLE'
            "
            type="danger"
            icon="el-icon-circle-close"
            @click="handleCloseOrder(row.id)"
          >
            作废
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="providerDrawer"
      :title="drawerTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :method-type="methodType"
      :no-confirm-footer="true"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '110px' }"
        :open-type="methodType"
      >
        <template #purchaseItems>
          <div class="title-box" style="margin-top: 0">采购物品</div>
          <ProTable
            ref="itemsProTable"
            :row-key="(row) => row.id"
            :columns="itemsColumns"
            :data="itemsTableData"
            :local-pagination="itemsLocalPagination"
            height="55vh"
            :show-setting="false"
            :show-search="false"
            :show-loading="false"
            sticky
          >
            <template #applicableModels="{ row }">
              <el-popover
                placement="bottom"
                title=""
                width="700"
                trigger="click"
              >
                <div style="margin: 20px; height: 400px; overflow-y: scroll">
                  <el-descriptions
                    class="margin-top"
                    title="适用机型"
                    :column="1"
                    border
                  >
                    <el-descriptions-item
                      v-for="item in row.productTreeDtoList"
                      :key="item.id"
                    >
                      <template slot="label">品牌/系列/机型</template>
                      {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <el-button slot="reference" type="text" size="mini">
                  适用机型
                </el-button>
              </el-popover>
            </template>
            <!-- 可采购数量 -->
            <template #number="{ row }">
              <el-input-number
                v-if="methodType === 'audit'"
                v-model="row.number"
                style="width: 100%"
                :min="0"
                size="small"
                controls-position="right"
                :disabled="methodType === 'info'"
                @change="handleChangeNumber"
              />
              <div v-else>{{ row?.number || 0 }}</div>
            </template>
          </ProTable>
        </template>
      </ProForm>
      <template #footer>
        <div v-if="methodType === 'audit'" class="footer">
          <el-button type="danger" @click="handleAudit('REJECT')">
            驳回
          </el-button>
          <el-button type="primary" @click="handleAudit('WAIT_PAY')">
            审核通过
          </el-button>
          <el-button plain @click="closeDrawer">取消</el-button>
        </div>
      </template>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam, filterParamRange, mulAmount } from "@/utils";
import { cloneDeep } from "lodash";
import {
  auditPurchaseApi,
  closePurchaseApi,
  getPurchaseDetailApi,
  pageProviderApi,
} from "@/api/manufacturer";
import { Message } from "element-ui";
import { mergePurchasePaymentApi } from "@/api/procure";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "ProviderOrder",
  data() {
    return {
      methodType: "add",
      queryParam: {},
      columns: [
        {
          dataIndex: "code",
          title: "供应商订单编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "code",
          title: "订单编号",
          isSearch: true,
          placeholder: "供应商订单编号",
          valueType: "input",
        },
        {
          dataIndex: "purchaseCode",
          title: "采购申请单编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "purchaseCode",
          title: "申请单编号",
          isSearch: true,
          placeholder: "采购申请单编号",
          valueType: "input",
        },
        {
          dataIndex: "receiveCompany",
          title: "公司",
          isTable: true,
          isSearch: false,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "deliveryTime",
          title: "期望发货时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "initiatorPhone",
          title: "电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        // {
        //   dataIndex: "num",
        //   title: "采购数量",
        //   isTable: true,
        //   width: 120,
        // },
        {
          dataIndex: "amount",
          title: "采购金额",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "status",
          title: "订单状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            {
              label: "已完成",
              value: "SUCCESS",
            },
            {
              label: "待审核",
              value: "WAIT_AUDIT",
            },
            {
              label: "待付款",
              value: "WAIT_PAY",
            },
            {
              label: "待发货",
              value: "WAIT_DELIVERY",
            },
            {
              label: "待收货",
              value: "WAIT_RECEIVE",
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "refundStatus",
          title: "退货状态",
          isTable: true,
          formatter: (row) => row.refundStatus?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "无",
              value: "NO",
            },
            {
              label: "待确认",
              value: "WAIT_CONFIRM",
            },
            {
              label: "退货中",
              value: "WAIT_RETURN",
            },
            {
              label: "待收货",
              value: "WAIT_RECEIVE",
            },
            {
              label: "待退款",
              value: "WAIT_REFUND",
            },
            {
              label: "部分退款",
              value: "PART_REFUND",
            },
            {
              label: "完成退款",
              value: "SUCCESS",
            },
            {
              label: "关闭",
              value: "CLOSED",
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "invoiceStatus",
          title: "开票状态",
          isTable: true,
          isSearch: false,
          valueType: "select",
          clearable: true,
          formatter: (row) => (row.invoiceStatus === 1 ? "已开票" : "未开票"),
          option: [
            {
              label: "未开票",
              value: 0,
            },
            {
              label: "已开票",
              value: 1,
            },
          ],
        },
        {
          dataIndex: "settleMethod",
          title: "结算方式",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formatter: (row) => row.settleMethod?.label,
          option: [],
          optionMth: () => dictTreeByCodeApi(4200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "settleStatus",
          title: "结算状态",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formatter: (row) => row.settleStatus?.label,
          option: [
            {
              label: "未结算",
              value: "NO_SETTLE",
            },
            {
              label: "付款中",
              value: "PAYING",
            },
            {
              label: "已结算",
              value: "SETTLED",
            },
          ],
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 280,
          fixed: "right",
          tableSlot: "actions",
        },
      ],
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      providerDrawer: false,
      drawerTitle: "查看 - ",
      confirmLoading: false,
      formLoading: false,
      form: {},
      formColumns: [
        // {
        //   dataIndex: "code",
        //   title: "订单编号",
        //   isForm: true,
        //   formSpan: 24,
        //   valueType: "text",
        // },
        {
          dataIndex: "manufacturerName",
          title: "供应商",
          isForm: true,
          formSpan: 24,
          valueType: "text",
        },
        {
          dataIndex: "receiveCompany",
          title: "下单公司",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "createdAt",
          title: "提交时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "deliveryTime",
          title: "期待发货时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "status",
          title: "审核状态",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "refundStatus",
          title: "退货状态",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "invoiceStatus",
          title: "开票状态",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "amount",
          title: "总金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "purchaseItems",
          title: "采购物品",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "purchaseItems",
        },
      ],
      itemsColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "applicableModels",
          title: "适用机型",
          isTable: true,
          width: 110,
          tableSlot: "applicableModels",
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          align: "center",
          minWidth: 80,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          align: "center",
          minWidth: 80,
        },
        {
          dataIndex: "approveNum",
          title: "计划采购数量",
          isTable: true,
          align: "center",
          minWidth: 100,
        },
        {
          dataIndex: "refundNum",
          title: "已退数量",
          isTable: true,
          align: "center",
          minWidth: 80,
        },
        {
          dataIndex: "number",
          title: "可采购数量",
          isTable: true,
          align: "center",
          tableSlot: "number",
          width: 140,
        },
        {
          dataIndex: "amount",
          title: "订单金额",
          isTable: true,
          align: "center",
          minWidth: 100,
        },
      ],
      itemsTableData: [],
      itemsLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      selectedPayment: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          deliveryTimeStart: null,
          deliveryTimeEnd: null,
          data: parameter.deliveryTime,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.deliveryTime;
      pageProviderApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    // 批量付款
    handleBatchPayment() {
      if (this.hasDifferentIds(this.selectedPayment)) {
        return Message.error("请选择同一供应商进行付款");
      }
      const valid = this.selectedPayment.every(
        (item) => item.settleStatus.value === "NO_SETTLE"
      );
      if (!valid) {
        return Message.error("请选择未结算的订单");
      }
      const ids = this.selectedPayment.map((item) => item.id);
      mergePurchasePaymentApi({ ids }).then((res) => {
        Message.success("生成付款单成功");
        this.refresh();
        this.$refs.ProTable.$refs.ProElTable.clearSelection();
      });
    },

    hasDifferentIds(array) {
      const ids = new Set(array.map((item) => item.manufacturerId));
      return ids.size > 1;
    },
    handleEdit(row, type) {
      this.methodType = type;
      this.drawerTitle =
        type === "info" ? `查看 - ${row.code}` : `审核 - ${row.code}`;
      getPurchaseDetailApi(row.id).then((res) => {
        const data = res.data;
        this.form = {
          ...data,
          status: data.status?.label,
          refundStatus: data.refundStatus?.label,
          invoiceStatus: data.invoiceStatus === 0 ? "未开票" : "已开票",
        };
        this.itemsTableData = res.data.manufacturerOrderGoodsList;
      });
      this.providerDrawer = true;
    },
    getRefundStatusLabel(status) {
      switch (status) {
        case 0:
          return "无";
        case 1:
          return "已退货";
        case 2:
          return "部分退货";
        default:
          return "未知状态";
      }
    },
    handleAudit(type) {
      const confirmTitle = type === "WAIT_PAY" ? "通过" : "驳回";
      this.$confirm(`确认${confirmTitle}该订单的审核吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const params = {
          id: this.form.id,
          manufacturerOrderGoodsList: this.itemsTableData,
          status: type,
        };
        auditPurchaseApi(params).then((res) => {
          this.refresh();
          this.closeDrawer();
          Message.success(`${confirmTitle}成功`);
        });
      });
    },
    handleChangeNumber(value) {
      let totalPrice = 0;
      this.itemsTableData.forEach((item) => {
        item.amount = mulAmount(item.price, item.number).toFixed(2);
        totalPrice += mulAmount(item.price, item.number);
      });
      this.form.amount = totalPrice.toFixed(2);
    },
    handleSelectionChange(val) {
      console.log(val);
      this.selectedPayment = val;
    },
    handleDelivery(code) {
      this.$router.push({ path: "/consignment", query: { code } });
    },
    // 关闭订单
    handleCloseOrder(id) {
      this.$confirm("此操作将会该订单作废, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        closePurchaseApi(id).then(() => {
          Message.success("操作成功");
          this.refresh();
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    closeDrawer() {
      this.form = {};
      this.providerDrawer = false;
    },
  },
};
</script>

<style scoped lang="scss">
.footer {
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style>
