<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-07 15:34:21
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:22
 * @Description: 销售退货入库
 -->
<template>
  <ProDrawer
    :value="dialogVisible"
    :title="dialogTitle"
    size="95%"
    :confirm-loading="confirmLoading"
    :top="'10%'"
    :no-footer="true"
    @cancel="handleDrawerCancel"
  >
    <div v-if="orderInfo" class="order-fix">
      <div class="order-border-box">
        <el-descriptions class="margin-top" title="售后信息" :column="3" border>
          <el-descriptions-item label="退货状态">{{
            statusList.filter((ele) => {
              return ele.value == orderInfo.processStatus;
            })[0]?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="退货单编号">{{
            orderInfo?.code
          }}</el-descriptions-item>
          <el-descriptions-item label="关联订单编号">{{
            orderInfo?.tradeOrderNum
          }}</el-descriptions-item>
          <el-descriptions-item label="支付金额（元）">{{
            orderInfo.tradeOrder?.actualAmount
          }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{
            orderInfo.applyTime
          }}</el-descriptions-item>
          <el-descriptions-item label="申请退款金额">{{
            orderInfo?.refundAmount
          }}</el-descriptions-item>
          <el-descriptions-item label="关联物流单号">{{
            orderInfo.reverseReturnOrder?.logisticsCode
          }}</el-descriptions-item>
          <el-descriptions-item label="申请原因">{{
            orderInfo?.reason
          }}</el-descriptions-item>
          <el-descriptions-item label="下单时间">{{
            orderInfo.tradeOrder?.createdAt
          }}</el-descriptions-item>
          <el-descriptions-item label="下单手机号">{{
            orderInfo.tradeOrder?.consigneePhone
          }}</el-descriptions-item>
          <el-descriptions-item label="店铺名称">{{
            orderInfo.tradeOrder?.consigneeName
          }}</el-descriptions-item>
          <el-descriptions-item label="订单金额（元）">{{
            orderInfo.tradeOrder?.paidAmount
          }}</el-descriptions-item>
          <el-descriptions-item label="收货详细地址">{{
            orderInfo.tradeOrder?.consigneeAddress
          }}</el-descriptions-item>
        </el-descriptions>
        <p v-if="orderInfo?.evidenceFiles?.length > 0" class="text-p">
          <span class="p-label">凭证：</span>
          <el-image
            v-for="(item, index) in orderInfo?.evidenceFiles?.split(',')"
            :key="index"
            style="width: 100px; height: 100px; margin-left: 5px"
            :src="item"
          ></el-image>
        </p>
      </div>
      <!-- 商品信息 -->
      <div class="m-t-8">
        <p class="title-p m-b-12">订单详情</p>
        <DataTable
          :columns="spxxColumns"
          :show-setting="false"
          :show-pagination="false"
          :show-search="false"
          row-key="id"
          :data="orderInfo.reverseOrderDetailList"
          sticky
          :height="250"
        >
        </DataTable>
      </div>
      <!-- 物流信息 -->
      <div class="m-t-8">
        <p class="title-p m-b-12">物流信息</p>
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in orderInfo.reverseOrderRecordList"
            :key="index"
            :timestamp="activity.createdAt"
          >
            {{ activity.title }}{{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
  </ProDrawer>
</template>

<script>
import { reverseOrderDetailApi } from "@/api/reverseOrder";

export default {
  name: "ReturnWaybill",
  data() {
    return {
      dialogVisible: false,
      dialogTitle: "",
      confirmLoading: false,
      orderInfo: {},
      spxxColumns: [
        {
          dataIndex: "itemName",
          isTable: true,
          title: "商品名称",
        },
        {
          dataIndex: "itemId",
          isTable: true,
          title: "商品编号",
          width: 180,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
        },
        {
          dataIndex: "saleUnitPrice",
          isTable: true,
          title: "商品单价（元）",
          formatter: (row) => row.tradeOrderDetail.saleUnitPrice,
        },

        {
          dataIndex: "itemNum",
          isTable: true,
          title: "购买数量",
        },
        {
          dataIndex: "reverseItemNum",
          isTable: true,
          title: "退货数量",
        },
        {
          dataIndex: "discountAmount",
          isTable: true,
          title: "优惠金额（元）",
          formatter: (row) => "-" + row.discountAmount,
        },
        {
          dataIndex: "refundAmount",
          isTable: true,
          title: "退款金额（元）",
        },
        {
          dataIndex: "actualPayAmount",
          isTable: true,
          title: "实付金额（元）",
        },
      ],
      statusList: [
        {
          label: "待审核",
          value: "PENDING_AUDIT",
        },
        { label: "平台驳回", value: "REJECT" },
        { label: "待用户寄回", value: "WAIT_SEND_BACK" },
        { label: "待卖家入库", value: "WAIT_SELLER_RECEIVED" },
        { label: "待买家签收", value: "WAIT_BUYER_SIGNED" },
        { label: "退款中", value: "REFUNDING" },
        { label: "售后成功", value: "SUCCESS" },
        { label: "售后关闭", value: "CLOSED" },
      ],
    };
  },
  mounted() {},
  methods: {
    show(shopWaybill) {
      this.dialogTitle = `${shopWaybill} - 退货单详情`;
      reverseOrderDetailApi(shopWaybill).then((res) => {
        this.orderInfo = res.data;
      });
      this.dialogVisible = true;
    },
    handleDrawerCancel() {
      this.dialogVisible = false;
      this.orderInfo = {};
    },
  },
};
</script>

<style scoped lang="scss">
.order-fix {
  margin-left: 20px;
  font-size: 14px;

  .red {
    color: #d14b50;
  }

  .text-p {
    &.right {
      text-align: right;
    }

    color: #606266;

    .p-label {
      color: #606266;
      font-weight: 700;
    }

    margin-top: 15px;
  }

  .content-fixed {
    display: flex;
    justify-content: space-between;
  }

  .btn-p {
    margin-top: 15px;

    .el-button {
      padding: 8px 29px;
    }
  }

  .order-border-box {
    border: dashed 1px #ccc;
    padding: 10px;
  }

  .title-p {
    width: 100%;
    padding: 5px 10px;
    color: #409eff;
    position: relative;
    margin: 20px auto;
    font-size: 16px;
    font-weight: 800;
    border-bottom: 1px solid #dcdfe6;

    &::before {
      content: "";
      width: 5px;
      height: 20px;
      background: #409eff;
      display: inline-block;
      position: absolute;
      left: -1px;
      top: 4px;
    }
  }
}
</style>
