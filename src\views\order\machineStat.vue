<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="按机型分类" name="first" lazy>
        <MachinesStat type="model" :columns="modelStatColumns" />
      </el-tab-pane>
      <el-tab-pane label="按地区分类" name="second" lazy>
        <MachinesStat type="area" :columns="areaStatColumns" />
      </el-tab-pane>
      <el-tab-pane label="按销售分布" name="third" lazy>
        <MachinesStat type="sale" :columns="saleStatColumns" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MachinesStat from "@/views/order/components/machinesStat.vue";
export default {
  name: "MachineStat",
  components: { MachinesStat },
  data() {
    return {
      activeName: "first",
      modelStatColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "productIds",
          isSearch: true,
          clearable: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
          title: "系列",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "series",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "销售额",
          isTable: true,
        },
        {
          dataIndex: "avgAmount",
          title: "台均销售额",
          isTable: true,
        },
      ],
      areaStatColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "number",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "销售额",
          isTable: true,
        },
        {
          dataIndex: "avgAmount",
          title: "台均销售额",
          isTable: true,
        },
      ],
      saleStatColumns: [
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "productIds",
          title: "系列",
          isSearch: true,
          clearable: true,
          valueType: "product",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "series",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "数量",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
        },
        {
          dataIndex: "amount",
          title: "销售额",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss"></style>
