<!--
 * @Author: AI Assistant
 * @Date: 2025-01-29
 * @Description: 版本列表表格组件
-->
<template>
  <div class="version-list-container">
    <ProTable
    ref="ProTable"
    :columns="columns"
    :data="tableData"
    :local-pagination="localPagination"
    :query-param="queryParam"
    show-pagination
    :show-table-operator="false"
    :show-setting="false"
    @loadData="loadData"
  >
    <!-- 搜索表单插槽 -->
    <template #form>
      <el-form :model="queryParam" inline>
        <el-form-item label="版本名称">
          <el-input
            v-model="queryParam.versionName"
            placeholder="请输入版本名称"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleSearch"
          />
        </el-form-item>
        <el-form-item label="更新类型">
          <el-select
            v-model="queryParam.updateType"
            placeholder="请选择更新类型"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="可选更新" value="optional" />
            <el-option label="强制更新" value="force" />
            <el-option label="管理员强制" value="admin_force" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryParam.isActive"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>

    <!-- 版本名称插槽 -->
    <template #versionName="{ row }">
      <div class="version-info">
        <span class="version-name">{{ row.versionName }}</span>
        <el-tag 
          v-if="isCurrentVersion(row)" 
          type="success" 
          size="mini" 
          class="ml-2"
        >
          当前使用
        </el-tag>
      </div>
    </template>

    <!-- 版本号插槽 -->
    <template #versionCode="{ row }">
      <div class="version-code">
        V{{ row.versionCode || '-' }}
      </div>
    </template>

    <!-- 文件信息插槽 -->
    <template #fileInfo="{ row }">
      <div class="file-info">
        <div class="file-name">{{ row.apkFileName }}</div>
        <div class="file-size">{{ formatFileSize(row.fileSize) }}</div>
      </div>
    </template>

    <!-- 更新类型插槽 -->
    <template #updateType="{ row }">
      <el-tag :type="getUpdateTypeColor(row)">
        {{ getUpdateTypeText(row) }}
      </el-tag>
    </template>

    <!-- 发布类型插槽 -->
    <template #releaseType="{ row }">
      <el-tag :type="getReleaseTypeColor(row.releaseType)">
        {{ getReleaseTypeText(row.releaseType) }}
      </el-tag>
    </template>
    
    <!-- 状态插槽 -->
    <template #status="{ row }">
      <el-tag :type="row.isActive ? 'success' : 'info'">
        {{ row.isActive ? '启用' : '禁用' }}
      </el-tag>
    </template>

    <!-- 更新说明插槽 -->
    <template #updateLog="{ row }">
      <div class="update-log">
        <div v-if="row.updateLog" class="update-log-content">
          <div class="update-log-preview" v-html="getUpdateLogPreview(row.updateLog)"></div>
          <el-button
            v-if="hasMoreContent(row.updateLog)"
            type="text"
            size="mini"
            @click="showUpdateLogDetail(row)"
            class="view-more-btn"
          >
            查看详情
          </el-button>
        </div>
        <span v-else class="no-log">暂无说明</span>
      </div>
    </template>
    
    <!-- 操作按钮插槽 -->
    <template #actions="{ row }">
      <div class="fixed-width">
        <el-button size="mini" type="primary" @click="$emit('edit', row)">
          编辑
        </el-button>
        <el-button
          size="mini"
          type="primary"
          @click="$emit('toggle-force', row)"
        >
          {{ row.adminForce ? '取消强制' : '设为强制' }}
        </el-button>

        <!-- 发布类型操作按钮 -->
        <el-button
          v-if="row.releaseType === 'GLOBAL'"
          size="mini"
          type="primary"
          @click="$emit('set-targeted', row)"
        >
          设定向
        </el-button>
        <el-button
          v-if="row.releaseType === 'TARGETED'"
          size="mini"
          type="primary"
          @click="$emit('set-global', row)"
        >
          转全局
        </el-button>
        <el-button
          v-if="row.releaseType === 'TARGETED'"
          size="mini"
          type="primary"
          @click="$emit('view-distributions', row)"
        >
          分发情况
        </el-button>

        <el-button
          size="mini"
          type="primary"
          @click="handleViewDetail(row)"
        >
          详情
        </el-button>
        <el-button
          size="mini"
          type="danger"
          @click="$emit('delete', row)"
        >
          删除
        </el-button>
      </div>
    </template>
  </ProTable>

  <!-- 更新说明详情对话框 -->
  <el-dialog
    title="更新说明详情"
    :visible.sync="updateLogDialogVisible"
    width="45%"
    :close-on-click-modal="false"
    :modal="false"
    :modal-append-to-body="false"
    custom-class="update-log-dialog-center"
  >
    <div class="update-log-dialog">
      <div class="version-info">
        <el-tag type="primary">{{ currentUpdateLogVersion.versionName }}</el-tag>
        <span class="version-time">{{ formatDateTime(currentUpdateLogVersion.createdAt) }}</span>
      </div>
      <div class="update-log-content" v-html="currentUpdateLogVersion.updateLog"></div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="updateLogDialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>

  <!-- 版本详情对话框 -->
  <el-dialog
    title="版本详情"
    :visible.sync="detailDialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    destroy-on-close
  >
    <div v-if="currentDetail" class="version-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="版本名称">
          {{ currentDetail.versionName }}
        </el-descriptions-item>
        <el-descriptions-item label="版本号">
          V{{ currentDetail.versionCode }}
        </el-descriptions-item>
        <el-descriptions-item label="文件名">
          {{ currentDetail.apkFileName }}
        </el-descriptions-item>
        <el-descriptions-item label="文件大小">
          {{ formatFileSize(currentDetail.fileSize) }}
        </el-descriptions-item>
        <el-descriptions-item label="发布时间" :span="2">
          {{ formatDateTime(currentDetail.createdAt) }}
        </el-descriptions-item>
        <el-descriptions-item label="MD5校验" :span="2">
          <el-input
            :value="currentDetail.fileMd5"
            readonly
            size="mini"
            style="font-family: monospace;"
          />
        </el-descriptions-item>
        <el-descriptions-item label="更新说明" :span="2">
          <div class="update-log-detail">
            <div v-if="currentDetail.updateLog" v-html="currentDetail.updateLog" class="update-log-html"></div>
            <span v-else class="no-log">暂无说明</span>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      
      <div class="download-link mt-3">
        <el-button 
          type="primary" 
          icon="el-icon-download"
          @click="handleDownload(currentDetail)"
        >
          下载APK
        </el-button>
        <el-button 
          type="success" 
          icon="el-icon-copy-document"
          @click="copyDownloadLink(currentDetail)"
        >
          复制下载链接
        </el-button>
      </div>
    </div>
  </el-dialog>
  </div>
</template>

<script>
import { getVersionPage } from '@/appupdate/api/appVersion';
import {
  formatFileSize,
  getUpdateTypeText,
  getUpdateTypeColor,
  getReleaseTypeText,
  getReleaseTypeColor
} from '@/appupdate/api/appVersion';

export default {
  name: 'VersionList',
  data() {
    return {
      // 表格数据
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      
      // 查询参数
      queryParam: {
        versionName: '',
        updateType: '',
        isActive: '',
      },
      
      // 详情对话框
      detailDialogVisible: false,
      currentDetail: null,

      // 更新说明对话框
      updateLogDialogVisible: false,
      currentUpdateLogVersion: {},
      
      // 表格列配置
      columns: [
        {
          title: "版本名称",
          dataIndex: "versionName",
          width: 110,
          isTable: true,
          tableSlot: "versionName",
        },
        {
          title: "版本号",
          dataIndex: "versionCode",
          width: 70,
          isTable: true,
          tableSlot: "versionCode"
        },
        {
          title: "文件信息",
          dataIndex: "fileInfo",
          width: 160,
          isTable: true,
          tableSlot: "fileInfo",
        },
        {
          title: "发布时间",
          dataIndex: "createdAt",
          width: 140,
          isTable: true,
          formatter: (row) => this.formatDateTime(row.createdAt),
        },
        {
          title: "更新说明",
          dataIndex: "updateLog",
          minWidth: 300,
          isTable: true,
          tableSlot: "updateLog",
        },
        {
          title: "更新类型",
          dataIndex: "updateType",
          width: 120,
          isTable: true,
          tableSlot: "updateType",
        },
        {
          title: "发布类型",
          dataIndex: "releaseType",
          width: 100,
          isTable: true,
          tableSlot: "releaseType",
        },
        {
          title: "状态",
          dataIndex: "status",
          width: 80,
          isTable: true,
          tableSlot: "status",
        },
        {
          title: "操作",
          dataIndex: "actions",
          width: 350,
          isTable: true,
          tableSlot: "actions",
          fixed: "right",
        },
      ],
    };
  },
  mounted() {
    // 组件挂载后自动加载数据
    this.loadData();
  },
  methods: {
    /**
     * 加载表格数据
     */
    async loadData(params = {}) {
      try {
        // 合并查询参数
        const queryData = {
          pageNumber: params.pageNumber || this.localPagination.pageNumber,
          pageSize: params.pageSize || this.localPagination.pageSize,
          ...this.queryParam,
        };

        const response = await getVersionPage(queryData);
        const { rows, total } = response.data;

        // 处理数据格式，确保fileSize为数字类型
        const processedData = (rows || []).map(item => ({
          ...item,
          fileSize: parseInt(item.fileSize) || 0,
        }));

        this.tableData = processedData;
        this.localPagination = {
          pageNumber: params.pageNumber || this.localPagination.pageNumber,
          pageSize: params.pageSize || this.localPagination.pageSize,
          total: parseInt(total) || 0,
        };

        // 触发版本列表更新事件，通知父组件更新版本数量
        this.$emit('version-list-updated', processedData);
      } catch (error) {
        console.error('加载版本列表失败:', error);
        this.$message.error('加载版本列表失败');
        this.tableData = [];
        this.localPagination.total = 0;

        // 即使加载失败也要触发更新事件，确保版本数量显示为0
        this.$emit('version-list-updated', []);
      } finally {
        // 确保关闭加载状态
        this.$nextTick(() => {
          if (this.$refs.ProTable) {
            this.$refs.ProTable.listLoading = false;
          }
        });
      }
    },

    /**
     * 搜索
     */
    handleSearch() {
      this.localPagination.pageNumber = 1;
      this.$refs.ProTable.refresh(this.queryParam, true);
    },

    /**
     * 重置搜索
     */
    handleReset() {
      this.queryParam = {
        versionName: '',
        updateType: '',
        isActive: '',
      };
      this.handleSearch();
    },

    /**
     * 刷新表格
     */
    refresh() {
      this.$refs.ProTable.refresh();
    },

    /**
     * 获取版本列表（供父组件使用）
     */
    getVersionList() {
      return this.tableData;
    },

    /**
     * 查看版本详情
     */
    handleViewDetail(version) {
      this.currentDetail = version;
      this.detailDialogVisible = true;
    },

    /**
     * 显示更新说明详情
     */
    showUpdateLogDetail(row) {
      this.currentUpdateLogVersion = { ...row };
      this.updateLogDialogVisible = true;
    },

    /**
     * 获取更新说明预览内容
     */
    getUpdateLogPreview(updateLog) {
      if (!updateLog) return '';

      // 移除HTML标签，只保留纯文本
      const textContent = updateLog.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ');

      // 限制显示长度
      if (textContent.length > 80) {
        return textContent.substring(0, 80) + '...';
      }

      return textContent;
    },

    /**
     * 判断是否有更多内容
     */
    hasMoreContent(updateLog) {
      if (!updateLog) return false;

      const textContent = updateLog.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ');
      return textContent.length > 80 || updateLog.includes('<img') || updateLog.includes('<p>');
    },

    /**
     * 下载APK文件
     */
    handleDownload(version) {
      if (version.cosUrl) {
        window.open(version.cosUrl, '_blank');
      } else {
        this.$message.warning('下载链接不可用');
      }
    },

    /**
     * 复制下载链接
     */
    async copyDownloadLink(version) {
      try {
        await navigator.clipboard.writeText(version.cosUrl);
        this.$message.success('下载链接已复制到剪贴板');
      } catch (error) {
        this.$message.error('复制失败，请手动复制');
      }
    },

    /**
     * 判断是否为当前版本
     */
    isCurrentVersion(version) {
      // 这里可以根据实际业务逻辑判断当前版本
      return version.adminForce && version.isActive;
    },

    /**
     * 格式化文件大小
     */
    formatFileSize,

    /**
     * 获取更新类型文本
     */
    getUpdateTypeText,

    /**
     * 获取更新类型颜色
     */
    getUpdateTypeColor,

    /**
     * 获取发布类型文本
     */
    getReleaseTypeText,

    /**
     * 获取发布类型颜色
     */
    getReleaseTypeColor,

    /**
     * 格式化日期时间
     */
    formatDateTime(dateTime) {
      if (!dateTime) return '';
      return this.$moment(dateTime).format('YYYY-MM-DD HH:mm:ss');
    },

    /**
     * 导出版本列表
     */
    async handleExport() {
      try {
        this.$message.info('正在导出数据...');

        // 获取所有数据（不分页）
        const response = await getVersionPage({
          pageNumber: 1,
          pageSize: 10000,
          ...this.queryParam,
        });

        const data = response.data.rows || [];

        if (data.length === 0) {
          this.$message.warning('没有数据可导出');
          return;
        }

        // 构建导出数据
        const exportData = data.map(item => ({
          '版本名称': item.versionName,
          '版本号': item.versionCode,
          '文件名': item.apkFileName,
          '文件大小': this.formatFileSize(parseInt(item.fileSize) || 0),
          '更新类型': this.getUpdateTypeText(item),
          '状态': item.isActive ? '启用' : '禁用',
          '下载次数': item.downloadCount || 0,
          '发布时间': this.formatDateTime(item.createdAt),
          '更新说明': item.updateLog || '',
        }));

        // 使用现有的导出功能（如果项目中有的话）
        // 这里简化为下载JSON文件
        this.downloadAsJson(exportData, `版本列表_${this.$moment().format('YYYY-MM-DD_HH-mm-ss')}.json`);

        this.$message.success('导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      }
    },

    /**
     * 下载为JSON文件
     */
    downloadAsJson(data, filename) {
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.version-info {
  display: flex;
  align-items: center;

  .version-name {
    font-weight: 600;
    color: #303133;
  }
}

.version-code {
  font-weight: 600;
  color: #909399;
}

.file-info {
  .file-name {
    font-size: 13px;
    color: #303133;
    margin-bottom: 2px;
  }
  
  .file-size {
    font-size: 12px;
    color: #909399;
  }
}

.update-log {
  max-width: 200px;
  word-break: break-all;
}

.update-log-detail {
  white-space: pre-wrap;
  max-height: 400px;
  overflow-y: auto;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.6;
}

.version-detail {
  .download-link {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
  }
}

.ml-2 {
  margin-left: 8px;
}

.mt-3 {
  margin-top: 12px;
}

// 更新说明样式
.update-log {
  .update-log-content {
    .update-log-preview {
      line-height: 1.0;
      color: #606266;
      margin-bottom: 2px;
    }

    .view-more-btn {
      color: #409EFF;
      padding: 0;
      font-size: 12px;
    }
  }

  .no-log {
    color: #C0C4CC;
    font-style: italic;
  }
}

// 更新说明详情对话框样式
.update-log-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;

  .version-info {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #EBEEF5;
    width: 80%;

    .version-time {
      margin-left: 12px;
      color: #909399;
      font-size: 14px;
    }
  }

  .update-log-content {
    line-height: 1.0;
    color: #606266;
    min-height: 200px;
    max-height: 500px;
    overflow-y: auto;
    text-align: left;
    width: 80%;
    margin: 0 auto;

    // HTML内容样式
    ::v-deep {
      p {
        margin: 8px 0;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      strong {
        font-weight: 600;
        color: #303133;
      }

      img {
        max-width: 100%;
        height: auto;
        margin: 12px 0;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      ul, ol {
        margin: 8px 0;
        padding-left: 20px;
      }

      li {
        margin: 4px 0;
      }
    }
  }
}

// 详情对话框中的更新说明样式
.update-log-detail {
  .update-log-html {
    line-height: 1.6;
    color: #606266;

    ::v-deep {
      p {
        margin: 8px 0;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      strong {
        font-weight: 600;
        color: #303133;
      }

      img {
        max-width: 100%;
        height: auto;
        margin: 12px 0;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .no-log {
    color: #C0C4CC;
    font-style: italic;
  }
}

/* 修复对话框蒙版问题 */
::v-deep .el-dialog__wrapper {
  z-index: 9999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

::v-deep .el-overlay {
  z-index: 9998 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

::v-deep .el-dialog {
  z-index: 9999 !important;
  position: relative !important;
}

/* 更新说明对话框居中样式 */
::v-deep .update-log-dialog-center {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}
</style>
