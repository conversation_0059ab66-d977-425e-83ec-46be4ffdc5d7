<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-04 09:34:31
 * @Description: 商品列表
 -->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      show-selection
      :row-key="'saleSkuId'"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="380"
      :query-param="queryParam"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #type="slotProps">
        {{ slotProps.row.type.label }}
      </template>
      <template #picsUrl="slotProps">
        <img
          style="max-width: 100px; max-height: 100px"
          :src="getPicsUrlImg(slotProps.row)"
        />
      </template>
      <template #saleAttrVals="slotProps">
        <span
          v-for="(item, index) in slotProps.row.saleAttrVals"
          :key="index"
          style="border: 1px solid #ddd"
          >{{ item.name }}: {{ item.val }}
        </span>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleChange"
        ></el-cascader>
      </template>
      <template #saleStatus="slotProps">
        {{ slotProps.row.saleStatus ? "已上架" : "未上架" }}
      </template>
    </ProTable>
  </div>
</template>
<script>
import { itemSummaryListApi, classifyListApi } from "@/api/goods";
import { isEmpty, cloneDeep } from "lodash";
import { brandListApi } from "@/api/brand";
import { dictTreeByCodeApi, dictTreeByCodeApi2 } from "@/api/user";
import { productAllApi } from "@/api/dispose";

export default {
  name: "GoodsManage",
  components: {},
  mixins: [],
  props: {
    selectedData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      productIdName: [],
      options: [],
      // 列表
      deviceProductTree: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        lastIds: [],
      },
      columns: [
        {
          dataIndex: "fullIdPath",
          isSearch: true,
          searchSlot: "fullIdPath",
          title: "适用机型",
          valueType: "select",
        },
        {
          dataIndex: "categoryId",
          title: "商品分类",
          isSearch: true,
          formSpan: 8,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          clearable: true,
          isSearch: true,
          formSpan: 8,
          valueType: "select",
          option: [
            { label: "上架", value: "ON_SALE" },
            { label: "下架", value: "NO_SALE" },
          ],
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          isSearch: true,
          formSpan: 16,
          valueType: "input",
          minWidth: 120,
        },

        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
          clearable: true,
          isSearch: true,
          formSpan: 16,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          width: 249,
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          width: 120,
          isTable: true,
          tableSlot: "picsUrl",
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          clearable: true,
          isSearch: true,
          formSpan: 16,
          width: 150,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          clearable: true,
          isSearch: true,
          formSpan: 16,
          width: 170,
          valueType: "input",
        },

        // {
        //   dataIndex: "商品类型",
        //   title: "商品类型",
        //   isTable: true,
        // },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
        },
        {
          dataIndex: "soldOutNum",
          title: "已售卖数量",
          isTable: true,
        },
        {
          dataIndex: "spareLevel",
          title: "备件等级",
          isTable: false,
          width: 120,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(3400),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "repFrequency",
          title: "更换频次",
          isTable: false,
          width: 120,
          isSearch: true,
          valueType: "select",
          clearable: true,
          multiple: true,
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(3300),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "unitList",
          title: "所属单元",
          width: 150,
          isTable: false,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(3200),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "productPartTypeList",
          title: "物品小类",
          width: 150,
          isTable: false,
          isSearch: true,
          clearable: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
      ],
      formcolumns: {
        brandList: [],
        categoryList: [],
        saleAttrValsList: [],
        saleStatusList: [
          { label: "上架", value: "ON_SALE" },
          { label: "下架", value: "NO_SALE" },
        ],
      },
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    this.init();
    this.$refs.ProTable.refresh();
  },
  methods: {
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });

      classifyListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.columns[1].option =
          res.data.rows?.map((item) => ({
            label: item.name,
            value: item.id,
          })) || [];
        // this.$nextTick(() => {
        //   this.$refs.ProTable.refresh();
        // });
      });
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    handleSelect(item) {
      this.queryParam.lastIds = [];
      item.map((el) => {
        this.queryParam.lastIds.push(el[el.length - 1]);
      });
    },
    handleChange(item) {
      this.queryParam.lastIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.lastIds.push(id);
      });
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign({}, parameter);
      itemSummaryListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows.map((item) => ({
            ...item,
            saleStatus: item.saleStatus === "ON_SALE",
          }));
          this.localPagination.total = parseInt(res.data.total);
          // this.$refs.ProTable.$refs.ProElTable.clearSelection();
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);

          this.$nextTick(() => {
            this.tableData.forEach((row) => {
              if (
                this.$props.selectedData.some(
                  (item) => item.saleSkuId === row.saleSkuId
                )
              ) {
                this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
                  row,
                  true
                );
              }
            });
          });
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },

    handleSelectionChange(row) {
      console.log(row, "xuanzhong ");
      this.$emit("chooseOem", row);
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
