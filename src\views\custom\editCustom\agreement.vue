<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-04 17:49:28
 * @Description: 
 -->
<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :height="500"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-if="type !== 'info'"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleEdit(null, 'add', 'buyMachine')"
        >
          购机合约
        </el-button>
        <el-button
          v-if="type !== 'info'"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleEdit(null, 'add', 'insure')"
        >
          抄表合约
        </el-button>
        <el-button
          v-if="type !== 'info'"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleEdit(null, 'add', 'rent')"
        >
          租赁合约
        </el-button>
        <el-button
          v-if="type !== 'info'"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleEdit(null, 'add', 'financing')"
        >
          融资合约
        </el-button>
        <el-button
          v-if="type !== 'info'"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleEdit(null, 'add', 'safeguard')"
        >
          维保合约
        </el-button>
      </template>
      <template #setting>
        <el-popover placement="bottom" width="200" trigger="click">
          <div class="status-filter">
            <div class="filter-title">合约状态筛选</div>
            <el-checkbox-group
              v-model="selectedStatuses"
              @change="handleStatusFilterChange"
            >
              <el-checkbox
                v-for="status in statusOptions"
                :key="status.value"
                :label="status.value"
              >
                {{ status.label }}
              </el-checkbox>
            </el-checkbox-group>
            <div class="filter-actions">
              <el-button
                type="primary"
                size="mini"
                @click="confirmStatusFilter"
              >
                确认
              </el-button>
              <el-button size="mini" @click="resetStatusFilter">重置</el-button>
            </div>
          </div>

          <i
            slot="reference"
            class="el-icon-setting filter-icon"
            :class="{ 'filter-active': selectedStatuses.length > 0 }"
          ></i>
        </el-popover>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info', null)">
            查看
          </el-button>
          <el-button
            v-if="
              row.status?.value === 'EFFECTED' &&
              type !== 'info' &&
              hasDownloadPermission
            "
            @click="handlePatch(row)"
          >
            补录合约附件
          </el-button>
          <el-button
            v-if="
              type !== 'info' &&
              row.isSupplement &&
              row.status?.value === 'EFFECTED'
            "
            @click="handleResetStatus(row)"
          >
            状态回退
          </el-button>
          <!--&&
              row.contractType?.value !== '1201'-->
          <el-button
            v-if="
              type !== 'info' &&
              (row.status?.value === 'EFFECTED' ||
                row.status?.value === 'UNEFFECT')
            "
            @click="handleEdit(row, 'edit', '', true)"
          >
            续约
          </el-button>
          <el-button
            v-if="!(type === 'info' || row.status?.value !== 'WAIT_CONFIRM')"
            icon="el-icon-edit"
            @click="handleEdit(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            v-if="
              type !== 'info' &&
              row.status?.value !== 'RETURN' &&
              row.deliveryStatus !== 0 &&
              (row.contractType?.value === '1265' ||
                row.contractType?.value === '1230' ||
                row.contractType?.value === '1201') &&
              !row.isReturn
            "
            @click="handleReturnMachine(row)"
          >
            退机
          </el-button>
          <el-button
            v-if="
              type !== 'info' &&
              (row.status?.value !== 'CANCEL' ||
                row.status?.value === 'EFFECTED')
            "
            type="danger"
            @click="handleDelete(row)"
          >
            作废
          </el-button>
        </div>
      </template>
      <!-- 合约文件列表 -->
      <template #attachments="{ row }">
        <el-popover
          v-if="row.attachments?.length > 0"
          placement="bottom"
          title="合约文件"
          width="400"
          trigger="hover"
        >
          <div class="attachments">
            <el-link
              v-for="item in row.attachments"
              :key="item.key"
              :disabled="!hasDownloadPermission"
              :href="item.url"
              icon="el-icon-folder-opened"
            >
              {{ item.name }}
            </el-link>
          </div>

          <el-button slot="reference" type="text" size="mini"> 查看 </el-button>
        </el-popover>
        <!--<div class="attachments">-->
        <!--  <el-link-->
        <!--    v-for="item in row.attachments"-->
        <!--    :key="item.key"-->
        <!--    :href="item.url"-->
        <!--    icon="el-icon-folder-opened"-->
        <!--  >-->
        <!--    {{ item.name }}-->
        <!--  </el-link>-->
        <!--</div>-->
      </template>
    </ProTable>
    <!-- 补录合同附件 -->
    <PatchAttachment ref="patchAttachment" @refresh="refresh" />
    <!--购机、租赁、融资合约-->
    <BuyRentFinanceContract
      ref="buyRentFinanceContract"
      :contract-type="contractType"
      @refresh="refresh"
    />
    <!-- 抄表合约 -->
    <InsureContract
      ref="insureContract"
      :contract-type="contractType"
      @refresh="refresh"
    />
    <!-- 维保合约 -->
    <SafeguardContract
      ref="safeguardContract"
      :contract-type="contractType"
      @refresh="refresh"
    />
  </div>
</template>

<script>
import PatchAttachment from "@/views/custom/editCustom/components/patchAttachment.vue";
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import {
  customerContractRenewApi,
  customerMachineContractBackApi,
  customerMachineContractReturnMachineApi,
  deleteCustomerContractApi,
  getCustomerContractApi,
  getCustomerContractByPageApi,
} from "@/api/customer";
import { Message } from "element-ui";
import { mapGetters } from "vuex";

export default {
  name: "Agreement",
  components: {
    InsureContract: () => import("./components/contract/insureContract.vue"),
    SafeguardContract: () =>
      import("./components/contract/safeguardContract.vue"),
    BuyRentFinanceContract: () =>
      import("./components/contract/buyRentFinanceContract.vue"),
    PatchAttachment,
  },
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    shopName: {
      type: [String, null],
      default: null,
    },
    seqId: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "合同编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isTable: true,
          minWidth: 150,
        },
        // {
        //   dataIndex: "remark",
        //   title: "摘要",
        //   isTable: true,
        // },
        {
          dataIndex: "contractType",
          title: "合约类型",
          isTable: true,
          formatter: (row) => row.contractType?.label,
          minWidth: 100,
        },
        {
          dataIndex: "attachments",
          title: "附件",
          isTable: true,
          tableSlot: "attachments",
          minWidth: 120,
        },
        {
          dataIndex: "signName",
          title: "签约人",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "agentName",
          title: "经办人",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "status",
          title: "合约状态",
          isTable: true,
          minWidth: 100,
          // sortable: true,
          // sortMethod: (a, b) => {
          //   return a.status?.label - b.status?.label;
          // },
          formatter: (row) => row.status?.label,
        },
        {
          dataIndex: "signTime",
          title: "签约时间",
          isTable: true,
          width: 160,
        },
        // {
        //   dataIndex: "startTime",
        //   title: "合约开始时间",
        //   isTable: true,
        //   width: 120,
        // },
        // {
        //   dataIndex: "endTime",
        //   title: "合约截至时间",
        //   isTable: true,
        //   width: 120,
        // },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          fixed: "right",
          tableSlot: "actions",
          minWidth: 340,
        },
      ],
      tableData: [],
      methodType: "add",
      contractType: "1201", // 合约类型
      statusOptions: [
        {
          label: "待确认",
          value: "WAIT_CONFIRM",
        },
        {
          label: "待支付",
          value: "WAIT_PAY",
        },
        {
          label: "支付待审核",
          value: "WAIT_AUDIT",
        },
        {
          label: "支付未通过",
          value: "REJECT",
        },
        {
          label: "待生效",
          value: "WAIT_EFFECT",
        },
        {
          label: "已生效",
          value: "EFFECTED",
        },
        {
          label: "退机中",
          value: "RETURN",
        },
        {
          label: "已失效",
          value: "UNEFFECT",
        },
        {
          label: "已作废",
          value: "CANCEL",
        },
      ],
      selectedStatuses: [],
    };
  },
  computed: {
    ...mapGetters({ permits: "permits" }),
    hasDownloadPermission() {
      return this.permits.some(
        (item) => item.permit === "@ums:manage:contract:download"
      );
    },
  },
  mounted() {
    this.refresh();
    // this.form.attachments ||= this.$set(this.form, "attachments", []);
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      if (this.selectedStatuses.length > 0) {
        requestParameters.statusList = this.selectedStatuses;
      } else {
        delete requestParameters.statusList;
      }
      getCustomerContractByPageApi({
        ...requestParameters,
        customerId: this.id,
      })
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    confirmStatusFilter() {
      this.refresh();
    },

    handleStatusFilterChange(selected) {
      console.log(selected);
      this.selectedStatuses = selected;
      // this.refresh();
    },

    resetStatusFilter() {
      this.selectedStatuses = [];
      this.refresh();
    },

    handleEdit(row, type, contractType, isContinue = false) {
      this.methodType = type;
      if (row) {
        const editApi = isContinue
          ? customerContractRenewApi
          : getCustomerContractApi;
        editApi(row.id).then((res) => {
          const formParam = {
            ...res.data,
            customerName: this.shopName,
            customerSeqId: this.seqId,
            customerId: this.id,
          };
          // 购机合约续约
          if (isContinue && row.contractType.value === "1201") {
            this.contractType = "1202";
            this.$refs.insureContract.visible(formParam, type);
            return;
          }
          if (row.contractType.value === "1201") {
            this.contractType = "1201";
            this.$refs.buyRentFinanceContract.visible(formParam, type);
          }
          if (row.contractType.value === "1202") {
            this.contractType = "1202";
            this.$refs.insureContract.visible(formParam, type);
          }
          if (row.contractType.value === "1265") {
            this.contractType = "1265";
            this.$refs.buyRentFinanceContract.visible(formParam, type);
          }
          if (row.contractType.value === "1230") {
            this.contractType = "1230";
            this.$refs.buyRentFinanceContract.visible(formParam, type);
          }
          if (row.contractType.value === "1220") {
            this.contractType = "1220";
            this.$refs.safeguardContract.visible(formParam, type);
          }
        });
      } else {
        this.form = {
          customerSeqId: this.seqId,
          customerName: this.shopName,
          customerId: this.id,
        };
        if (contractType === "buyMachine") {
          this.contractType = "1201";
          this.$refs.buyRentFinanceContract.visible(this.form, type);
        } else if (contractType === "insure") {
          this.contractType = "1202";
          this.$refs.insureContract.visible(this.form, type);
        } else if (contractType === "rent") {
          this.contractType = "1265";
          this.$refs.buyRentFinanceContract.visible(this.form, type);
        } else if (contractType === "financing") {
          this.contractType = "1230";
          this.$refs.buyRentFinanceContract.visible(this.form, type);
        } else if (contractType === "safeguard") {
          this.contractType = "1220";
          this.$refs.safeguardContract.visible(this.form, type);
        }
      }
    },
    // 补录合约附件
    handlePatch(row) {
      this.$refs.patchAttachment.show(row);
    },
    // 状态回退
    handleResetStatus(row) {
      this.$confirm("此操作将会将该合同状态修改为待确认, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        customerMachineContractBackApi(row.id).then((res) => {
          this.refresh();
        });
      });
    },
    handleDelete(row) {
      this.$confirm("此操作将会作废该合约, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteCustomerContractApi(row.id).then((res) => {
          this.refresh();
          Message.success("操作成功");
        });
      });
    },
    // 退机
    handleReturnMachine(row) {
      this.$prompt("请输入退机原因", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputType: "textarea",
        inputPlaceholder: "请输入退机原因",
        inputValidator: (value) => {
          if (!value) {
            return "请输入退机原因";
          }
          if (value.length > 255) {
            return "退机原因不能超过255个字符";
          }
          return true;
        },
        inputErrorMessage: "输入无效",
      })
        .then(({ value }) => {
          const args = {
            id: row.id,
            returnReason: value,
          };
          customerMachineContractReturnMachineApi(args).then((res) => {
            this.$message.success("操作成功");
            this.refresh();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消输入",
          });
        });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .filter-icon {
  font-size: 22px;
  cursor: pointer;
  color: #606266;
  transition: color 0.3s;

  &.filter-active {
    color: #409eff;
  }

  &:hover {
    color: #409eff;
  }
}

.status-filter {
  .filter-title {
    font-weight: bold;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
  }

  .el-checkbox {
    display: block;
    margin-bottom: 10px;
  }

  .filter-actions {
    margin-top: 15px;
    //text-align: right;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
::v-deep.attachments {
  width: 100%;
  white-space: wrap !important;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  a {
    color: #409eff;
    display: flex;
    align-items: center;
    .el-icon-folder-opened {
      font-size: 24px !important;
    }
  }
}
</style>
