<!--
 * @Author: wskg
 * @Date: 2024-08-30 16:01:19
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:18:43
 * @Description: 客户 - 客户仓库
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      row-key="label"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="550"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:customer-store:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div class="title-box-right">
          <div>总金额：{{ totalData || 0 }}</div>
        </div>
      </template>
      <template #imageFiles="slotProps">
        <img
          style="max-width: 100px; max-height: 100px"
          :src="getPicsUrlImg(slotProps.row)"
          @click="searchBigImage(slotProps.row)"
        />
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #machine="{ row }">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in row.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-button slot="reference" type="text" size="mini">
            适用机型
          </el-button>
        </el-popover>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            size="mini"
            icon="el-icon-info"
            @click="handleLogInfo(row)"
          >
            操作日志
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="drawerVisible"
      title="操作日志"
      size="65%"
      :no-footer="true"
      :confirm-loading="false"
      @cancel="drawerVisible = false"
    >
      <ProTable
        ref="logProTable"
        :columns="logColumns"
        :data="logTableData"
        :local-pagination="logLocalPagination"
        :show-setting="false"
        :show-search="false"
        height="80vh"
      ></ProTable>
    </ProDrawer>
  </div>
</template>

<script>
import {
  customConsumableDetailListApi,
  customConsumableExportApi,
  customConsumableListApi,
  engineerLogListApi,
} from "@/api/statisics";
import { cloneDeep } from "lodash";
import { filterParam, filterParamRange } from "@/utils";
import { productAllApi } from "@/api/dispose";
import { dictTreeByCodeApi2 } from "@/api/user";
import { handleExcelExport } from "@/utils/exportExcel";

export default {
  name: "ConsumableStore",

  mixins: [],
  props: {},
  data() {
    return {
      productIdName: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      productTreeOption: [],
      queryParam: { userType: "CUSTOMER" },
      columns: [
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          width: 180,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          width: 180,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          width: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          width: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "imageFiles",
          title: "物品图片",
          width: 150,
          isTable: true,
          tableSlot: "imageFiles",
          clearable: true,
        },
        {
          dataIndex: "machine",
          title: "品牌/机型",
          isTable: true,
          tableSlot: "machine",
          width: 120,
        },
        {
          dataIndex: "productIdList",
          title: "品牌/机型",
          isSearch: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
        // {
        //   dataIndex: "type",
        //   title: "物品大小类",
        //   width: 150,
        //   isTable: true,
        //   isSearch: true,
        //   clearable: true,
        //   valueType: "select",
        //   searchSlot: "searchType",
        // },
        {
          dataIndex: "type",
          title: "物品小类",
          width: 100,
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          formatter: (row) => row.type?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "saleUnitPrice",
          title: "单价",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "金额",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 100,
          tableSlot: "action",
        },
      ],
      options: [],
      includeList: [],
      totalData: "",
      drawerVisible: false,
      logLocalPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 20,
      },
      logColumns: [
        {
          dataIndex: "operateCode",
          title: "操作单号",
          isTable: true,
        },
        {
          dataIndex: "operateType",
          title: "操作类型",
          isTable: true,
          formatter: (row) => {
            switch (row.operateType) {
              case "APPLY":
                return "客户领料单";
              case "PURCHASE":
                return "客户自购";
              case "MALL":
                return "商城下单";
              case "RETURN":
                return "退料单退回";
              case "REPAIR_USE":
                return "维修工单使用";
              case "REVERSE":
                return "售后单退货";
              case "SELF_USE":
                return "客户自修使用";
              case "ENGINEER_APPLY":
                return "工程师领料";
              default:
                return "";
            }
          },
        },
        // {
        //   dataIndex: "userType",
        //   title: "操作人",
        //   isTable: true,
        //   formatter: (row) => {
        //     if (row.userType === "CUSTOMER") {
        //       return "客户";
        //     } else if (row.userType === "ENGINEER") {
        //       return "工程师";
        //     }
        //   },
        // },
        {
          dataIndex: "alterBefore",
          title: "变更前数量",
          isTable: true,
        },
        {
          dataIndex: "num",
          title: "操作数量",
          isTable: true,
          formatter: (row) => {
            switch (row.operateType) {
              case "APPLY":
                return row.num;
              case "PURCHASE":
                return row.num;
              case "MALL":
                return row.num;
              case "RETURN":
                return "-" + row.num;
              case "REPAIR_USE":
                return "-" + row.num;
              case "REVERSE":
                return row.num;
              case "SELF_USE":
                return "-" + row.num;
              default:
                return row.num;
            }
          },
        },
        {
          dataIndex: "alterEnd",
          title: "变更后数量",
          isTable: true,
        },
        {
          dataIndex: "createdAt",
          title: "操作时间",
          isTable: true,
        },
      ],
      logTableData: [],
      exportLoading: false,
      requestParameters: {},
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
    this.getProductThird();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          minNum: null,
          maxNum: null,
          data: parameter.num,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.num;
      if (requestParameters.oemNumber) {
        requestParameters.oem = requestParameters.oemNumber;
        delete requestParameters.oemNumber;
      }
      this.requestParameters = requestParameters;
      customConsumableListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getTotalData(requestParameters);
    },
    // 查看操作日志
    handleLogInfo(row) {
      const args = {
        ...this.logLocalPagination,
        itemStoreId: row.id,
      };
      engineerLogListApi(args)
        .then((res) => {
          this.logTableData = res.data.rows;
          this.logLocalPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.logProTable
            ? (this.$refs.logProTable.listLoading = false)
            : null;
        });
      this.drawerVisible = true;
    },
    getTotalData(params) {
      customConsumableDetailListApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    // handleChangeType(val) {
    //   this.$set(this.queryParam, "type", val[val.length - 1]);
    // },
    handleExport() {
      this.$confirm("此操作将导出客户仓库数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          customConsumableExportApi,
          this.requestParameters,
          "客户仓库数据",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    getPicsUrlImg(row) {
      return row?.skuInfo.picUrl?.[0]?.url;
    },
    searchBigImage(row) {},
    handleChange(row) {
      console.log(row);
    },
    handleSelect(arr) {
      this.queryParam.productIdList = arr.map((item) => item[item.length - 1]);
    },
  },
};
</script>
<style lang="scss" scoped>
.gap {
  margin: 20px 0;
}

.order-border-box {
  border: dashed 1px #ccc;
  padding: 10px;
}
</style>
