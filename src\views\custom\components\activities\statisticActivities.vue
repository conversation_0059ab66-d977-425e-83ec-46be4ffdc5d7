<!--
 * @Description: 活动统计
 * @version: 
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-11 18:05:32
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-02-12 13:44:55
-->
<template>
  <div class="container">
    <ProDrawer
      :value="showDrawer"
      :title="title"
      size="80%"
      :no-footer="true"
      :destroy-on-close="true"
      @cancel="cancel"
    >
      <el-form
        inline
        size="small"
        class="search-form detail-info"
        label-width="110px"
      >
        <el-form-item label="活动名称：">
          <span> {{ activityData.activityName }}</span>
        </el-form-item>
        <el-form-item label="活动类型：">
          <span> {{ activityData.activityType?.label }}</span>
        </el-form-item>
        <el-form-item label="活动状态：">
          <span> {{ activityData.status?.label }}</span>
        </el-form-item>
        <el-form-item label="活动开始时间：">
          <span> {{ activityData.startTime }}</span>
        </el-form-item>
        <el-form-item label="活动结束时间：">
          <span> {{ activityData.endTime }}</span>
        </el-form-item>
      </el-form>
      <ProTable
        ref="ProTable1"
        class="statistic-activity"
        row-key="id"
        :data="tableData"
        :columns="columns"
        :query-param="queryParam"
        :show-pagination="true"
        :local-pagination="localPagination"
        :height="400"
        @loadData="loadData"
      >
        <template #btn>
          <div class="title-box-right">
            <div>参与客户数：{{ tableData.length || 0 }}</div>
            <div>分享次数：{{ statData.shareNum || 0 }}</div>
            <div>触达店铺数：{{ statData.clickNum || 0 }}</div>
            <div>注册店铺数：{{ statData.registerNum || 0 }}</div>
            <div>消费店铺数：{{ statData.orderCustNum || 0 }}</div>
            <div>消费订单数：{{ statData.orderNum || 0 }}</div>
            <div>消费金额：{{ statData.orderAmount || 0 }}</div>
          </div>
        </template>
        <template #orderNum="{ row }">
          <el-button type="text" @click="showOrderDetail(row)">
            {{ row.orderNum }}
          </el-button>
        </template>
        <template #registerNum="{ row }">
          <el-button type="text" @click="showRegister(row)">
            {{ row.registerNum }}
          </el-button>
        </template>
        <template #clickNum="{ row }">
          <el-button type="text" @click="showClickNum(row)">
            {{ row.clickNum }}
          </el-button>
          <!--{{ row.clickNum }}-->
        </template>
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-connection"
              @click="sendPrize(row, 'send')"
            >
              奖品发放
            </el-button>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-s-order"
              @click="sendPrize(row, 'record')"
            >
              奖品清单
            </el-button>
          </div>
        </template>
      </ProTable>
      <el-dialog
        custom-class="center-dialog"
        width="80vw"
        :visible.sync="prizeVisible"
        :close-on-click-modal="false"
        append-to-body
        title="奖品发放"
      >
        <div>
          <el-form
            size="small"
            inline
            class="search-form detail-info"
            label-width="120px"
          >
            <el-form-item label="店铺名称：">
              <span> {{ activeCustomer.customerName }}</span>
            </el-form-item>
            <el-form-item label="点击次数：">
              <span> {{ activeCustomer.clickNum }}</span>
            </el-form-item>
            <el-form-item label="收藏次数：">
              <span> {{ activeCustomer.collectNum }}</span>
            </el-form-item>
            <el-form-item label="消费金额：">
              <span> {{ activeCustomer.orderAmount }}</span>
            </el-form-item>
            <el-form-item label="消费订单数：">
              <span> {{ activeCustomer.orderNum }}</span>
            </el-form-item>
            <el-form-item label="注册客户数：">
              <span> {{ activeCustomer.registerNum }}</span>
            </el-form-item>
            <el-form-item label="分享次数：">
              <span> {{ activeCustomer.shareNum }}</span>
            </el-form-item>
          </el-form>
          <div class="title-box">奖品列表</div>
          <ProTable
            ref="ProTable"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="dialogTableColumns"
            :show-pagination="false"
            :data="prizeList"
            sticky
            :height="400"
            row-key="saleSkuId"
            class="dialog-proTable"
            :query-param="{}"
            @loadData="refresh"
          >
            <template #btn>
              <el-button
                v-if="grandType === 'send'"
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="addSaleRow"
              >
                新增
              </el-button>
            </template>
            <template #awardType="{ row }">
              <div v-if="row.flag">
                {{ row.awardType?.label }}
              </div>
              <div v-else>
                <el-select v-model="row.awardType" @change="changeType(row)">
                  <el-option label="零件耗材" value="PART"></el-option>
                  <el-option label="积分券" value="POINTS"></el-option>
                  <el-option label="维修券" value="REPAIR"></el-option>
                  <el-option label="购机券" value="MACHINE"></el-option>
                  <el-option label="代金券" value="TICKET"></el-option>
                  <el-option label="其他" value="OTHER"></el-option>
                </el-select>
              </div>
            </template>
            <template #awardName="{ row, index }">
              <div
                v-if="row.awardType == 'PART' && !row.flag"
                @click="checkSKU(row, index)"
              >
                <el-button v-if="!row.awardName" type="text">选择</el-button>
                <el-button v-else type="text">{{ row.awardName }}</el-button>
              </div>
              <el-input
                v-else-if="!row.flag"
                v-model="row.awardName"
              ></el-input>
              <div v-else>{{ row.awardName }}</div>
            </template>
            <template #price="{ row }">
              <div v-if="row.flag">{{ row.price }}</div>
              <div v-else>
                <el-input v-model.number="row.price" placeholder="0">
                </el-input>
              </div>
            </template>
            <template #deliveryStatus="slotProps">
              {{ slotProps.row.deliveryStatus?.label }}
            </template>
            <template #limit="{ row, index }">
              <div
                v-if="
                  (row.flag &&
                    (row.awardType.value === 'REPAIR' ||
                      row.awardType.value === 'MACHINE' ||
                      row.awardType.value === 'TICKET')) ||
                  (!row.flag &&
                    (row.awardType === 'REPAIR' ||
                      row.awardType === 'MACHINE' ||
                      row.awardType === 'TICKET'))
                "
              >
                <el-button type="text" @click="showLimitDialog(row, index)">
                  限制条件
                </el-button>
              </div>
              <div v-else></div>
            </template>
            <template v-if="grandType == 'send'" #num="slotProps">
              <el-input v-model.number="slotProps.row.num" placeholder="0">
              </el-input>
            </template>
            <template #action="{ row, index }">
              <div v-if="!row.flag" class="fixed-width">
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDel(row, index)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </ProTable>
        </div>
        <span v-if="grandType == 'send'" slot="footer" class="dialog-footer">
          <el-button @click="prizeVisible = false">取 消</el-button>
          <el-button type="primary" @click="sendAward()">发 放</el-button>
        </span>
      </el-dialog>
    </ProDrawer>
    <el-dialog
      custom-class="center-dialog"
      width="80vw"
      :visible.sync="skuVisible"
      append-to-body
      title="关联耗材零件"
    >
      <div>
        <ProTable
          ref="skuProTable"
          :columns="SKUcolumns"
          :local-pagination="skuLocalPagination"
          :data="skuTableData"
          sticky
          :height="400"
          :show-selection="true"
          row-key="saleSkuId"
          :query-param="skuQueryParam"
          @loadData="loadSkuData"
          @handleSelected="selectStu"
        >
          <template #btn> 商品列表 </template>
          <template #type="slotProps">
            {{ slotProps.row.type.label }}
          </template>
          <template #picsUrl="{ row }">
            <el-image
              v-if="row.picsUrl && row.picsUrl.length > 0"
              style="max-width: 100px; height: 100px"
              :src="getPicsUrlImg(row)"
              :preview-src-list="[getPicsUrlImg(row)]"
            >
            </el-image>
          </template>
          <template #saleAttrVals="slotProps">
            <span
              v-for="(item, index) in slotProps.row.saleAttrVals"
              :key="index"
              style="border: 1px solid #ddd"
              >{{ item.name }}: {{ item.val }}
            </span>
          </template>

          <template #saleStatus="slotProps">
            {{ slotProps.row.saleStatus ? "已上架" : "未上架" }}
          </template>
        </ProTable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="skuVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveStuData">确 定</el-button>
      </span>
    </el-dialog>
    <StatisticCustomer ref="statisticCustomer" />
    <StatisticOrder ref="statisticOrder" />
    <!-- 优惠券使用限制条件 -->
    <el-dialog
      title="限制条件"
      :visible.sync="limitVisible"
      :close-on-click-modal="false"
      width="500px"
      append-to-body
    >
      <div
        v-if="
          selectRow.awardType === 'REPAIR' || selectRow.awardType === 'MACHINE'
        "
      >
        <el-radio-group
          v-model="selectRow.limitType"
          :disabled="selectRow.flag"
          @change="handleLimitType"
        >
          <el-radio label="GENERAL">通用</el-radio>
          <el-radio label="MODEL">机型限制</el-radio>
        </el-radio-group>
        <div
          v-if="selectRow.limitType === 'MODEL'"
          style="
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            gap: 10px;
            align-items: center;
          "
        >
          <span>机器型号：</span>
          <el-cascader
            v-model="selectRow.fullIdPath"
            :options="modelOptions"
            placeholder="请选择可使用该券机型"
            filterable
            clearable
            :disabled="selectRow.flag"
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
              expandTrigger: 'click',
              multiple: true,
            }"
            leaf-only
            style="flex: 1"
            @change="handleProductChange"
          ></el-cascader>
        </div>
      </div>
      <div v-if="selectRow.awardType === 'TICKET'">
        <el-radio-group
          v-model="selectRow.limitType"
          :disabled="selectRow.flag"
        >
          <el-radio label="GENERAL">通用</el-radio>
          <el-radio label="AMOUNT">最低消费限制</el-radio>
        </el-radio-group>
        <div
          v-if="selectRow.limitType === 'AMOUNT'"
          style="
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            gap: 10px;
            align-items: center;
          "
        >
          <span>最低消费金额：</span>
          <el-input
            v-model="selectRow.minAmount"
            style="flex: 1"
            type="number"
            placeholder="请输入最低消费金额"
            :disabled="selectRow.flag"
          >
            <template slot="append">元</template>
          </el-input>
        </div>
      </div>
      <div
        style="
          margin-top: 20px;
          display: flex;
          justify-content: space-between;
          gap: 10px;
          align-items: center;
        "
      >
        <span>过期时间：</span>
        <el-date-picker
          v-if="
            selectRow.awardType === 'REPAIR' ||
            selectRow.awardType === 'MACHINE' ||
            selectRow.awardType === 'TICKET'
          "
          v-model="selectRow.expireDate"
          style="flex: 1"
          type="date"
          :disabled="selectRow.flag"
          placeholder="选择过期时间"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
        <div v-else></div>
      </div>
      <span v-if="!selectRow.flag" slot="footer" class="dialog-footer">
        <el-button @click="limitVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveLimitData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import StatisticCustomer from "@/views/custom/components/activities/statisticCustomer.vue";
import StatisticOrder from "@/views/custom/components/activities/statisticOrder.vue";
import {
  getActivitiesSituation,
  getActivitiesPrizeApi,
  putActivitiesGrandApi,
  getGrandRecordApi,
  getActivityShareStatApi,
} from "@/api/customer";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep, merge } from "lodash";
import { dictTreeByCodeApi2 } from "@/api/user";
import { itemSummaryListApi } from "@/api/goods";
import { productAllApi } from "@/api/dispose";

export default {
  name: "StatisticActivities",
  components: { StatisticCustomer, StatisticOrder },
  props: {},
  data() {
    return {
      title: "活动统计",
      showDrawer: false,
      queryParam: {},
      activityData: { activityType: {}, status: {} },
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 20,
        total: 0,
      },
      columns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "shareNum",
          title: "分享次数",
          isTable: true,
          minWidth: 100,
          isSearch: true,
          valueType: "inputRange",
        },

        {
          dataIndex: "clickNum",
          title: "触达店铺数",
          isTable: true,
          tableSlot: "clickNum",
          // isSearch: true,
          // valueType: "inputRange",
          minWidth: 100,
        },
        {
          dataIndex: "registerNum",
          title: "注册店铺数",
          isTable: true,
          minWidth: 100,
          tableSlot: "registerNum",
          isSearch: true,
          valueType: "inputRange",
        },

        // {
        //   dataIndex: "collectNum",
        //   title: "收藏次数",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        //   minWidth: 100,
        // },
        {
          dataIndex: "orderCustNum",
          title: "消费店铺数",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
          // tableSlot: "customerNum",
          minWidth: 100,
        },
        {
          dataIndex: "orderNum",
          title: "消费订单数",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          tableSlot: "orderNum",
          minWidth: 100,
        },
        {
          dataIndex: "orderAmount",
          title: "消费金额",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          minWidth: 100,
        },
        {
          dataIndex: "grant",
          title: "奖品发放",
          isTable: true,
          formatter: (row) => (row.grant ? "已发放" : "未发放"),
        },
        {
          dataIndex: "action",
          tableSlot: "action",
          width: 180,
          title: "操作",
          isTable: true,
          isSearch: false,
          fixed: "right",
          tooltip: false,
        },
      ],
      prizeVisible: false,
      activeCustomer: {},
      prizeList: [],
      prizeColumns: [
        {
          dataIndex: "awardType",
          tableSlot: "awardType",
          title: "奖品类型",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "awardName",
          title: "奖品名称",
          isTable: true,
          tableSlot: "awardName",
          minWidth: 150,
        },
        {
          dataIndex: "articleCode",
          title: "奖品编码",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "limit",
          title: "限制条件",
          isTable: true,
          tableSlot: "limit",
          minWidth: 120,
        },
        {
          dataIndex: "price",
          title: "单价/券面额",
          isTable: true,
          tableSlot: "price",
          minWidth: 100,
        },
        {
          dataIndex: "maxBuy",
          title: "单个客户最多数量",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "quantity",
          title: "总数量",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "num",
          title: "发放数量",
          isTable: true,
          tableSlot: "num",
          minWidth: 150,
        },

        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 100,
        },
      ],
      recordColumns: [
        {
          dataIndex: "awardType",
          title: "奖品类型",
          isTable: true,
          formatter: (row) => row.awardType?.label,
          minWidth: 100,
        },
        {
          dataIndex: "awardName",
          title: "奖品名称",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "limit",
          title: "限制条件",
          isTable: true,
          tableSlot: "limit",
          minWidth: 120,
        },
        {
          dataIndex: "status",
          title: "发放状态",
          formatter: (row) => row.status?.label,
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "createdAt",
          title: "发放时间",
          isTable: true,
          width: 160,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "quantity",
          title: "发放数量",
          isTable: true,
          minWidth: 100,
        },

        {
          dataIndex: "createdBy",
          title: "发放人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
          minWidth: 100,
        },
      ],
      grandType: "send",
      dialogTableColumns: [],
      awardGrants: [],
      actRowIndex: null,
      selectRow: {},
      skuVisible: false,
      limitVisible: false,
      modelOptions: [],
      SKUcolumns: [
        {
          dataIndex: "lastIds",
          title: "适用机型",
          isSearch: true,
          clearable: true,
          valueType: "product",
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isSearch: true,
          isTable: true,
          valueType: "input",
          clearable: true,
          minWidth: 150,
          width: 180,
        },

        {
          dataIndex: "itemCode",
          title: "商品编号",
          isSearch: true,
          valueType: "input",
          clearable: true,
          minWidth: 150,
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          width: 150,
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          width: 120,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          formSpan: 16,
          width: 180,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
        },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
          formatter: (row) => row.type?.label,
        },
        {
          dataIndex: "productPartTypeList",
          title: "物品小类",
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
      ],
      skuLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      skuTableData: [],
      skuQueryParam: {},
      statData: {},
      requestParameters: {},
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.getProductThird();
  },
  methods: {
    open(data) {
      this.showDrawer = true;
      this.queryParam = {
        activityId: data.id,
        customerName: "",
        pageNumber: 1,
        pageSize: 20,
      };
      this.activityData = data;
      this.$nextTick(() => {
        this.$refs.ProTable1.refresh();
      });
    },
    async loadData(params) {
      this.queryParam = filterParam(Object.assign({}, this.queryParam, params));
      const rangeData = [
        {
          startShareNum: null,
          endShareNum: null,
          data: params.shareNum,
        },
        {
          startRegisterNum: null,
          endRegisterNum: null,
          data: params.registerNum,
        },
        {
          startOrderNum: null,
          endOrderNum: null,
          data: params.orderNum,
        },
        {
          startOrderAmount: null,
          endOrderAmount: null,
          data: params.orderAmount,
        },
      ];
      filterParamRange(this, this.queryParam, rangeData);
      this.requestParameters = cloneDeep(this.queryParam);
      delete this.requestParameters.shareNum;
      delete this.requestParameters.registerNum;
      delete this.requestParameters.orderNum;
      delete this.requestParameters.orderAmount;
      const result = await getActivitiesSituation(this.requestParameters);
      if (result.code === 200 && result.data) {
        this.tableData = result.data.rows || [];
        this.localPagination.total = +result.data.total;
      }
      this.$refs.ProTable1 && (this.$refs.ProTable1.listLoading = false);
      this.getStatData();
    },
    loadSkuData(parameter) {
      this.skuQueryParam = filterParam(
        Object.assign({}, this.skuQueryParam, parameter)
      );
      const requestParameters = cloneDeep(this.skuQueryParam);
      itemSummaryListApi(requestParameters)
        .then((res) => {
          this.skuTableData = res.data.rows;
          this.skuLocalPagination.total = parseInt(res.data.total);
          this.$nextTick(() => {
            this.selectRow.skuId &&
              this.$refs.ProTable.setSelection([this.selectRow.skuId]);
            !this.selectRow.skuId && this.$refs.ProTable.setSelection([]);
          });
        })
        .finally(() => {
          this.$refs.skuProTable
            ? (this.$refs.skuProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    cancel() {
      this.showDrawer = false;
    },
    // 添加促销商品
    addSaleRow() {
      const resultList = cloneDeep(this.prizeList);
      resultList.push({
        activityId: this.activityId || 0,
        articleCode: "",
        awardName: "",
        awardType: "",
        maxBuy: "",
        price: "",
        quantity: "",
        skuId: "",
        flag: false,
      });
      this.prizeList = resultList;
    },
    // 券使用限制条件
    showLimitDialog(row, index) {
      this.limitVisible = true;
      this.actRowIndex = index;
      // 初始化当前行数据
      if (!row.limitType) {
        this.selectRow = {
          ...row,
          limitType: "GENERAL",
          limitInfo: [],
          minAmount: null,
          expireDate: "",
        };
      } else if (row.limitType.label) {
        this.selectRow = cloneDeep(row);
        this.selectRow.limitType = this.selectRow.limitType?.value;
        this.selectRow.awardType = this.selectRow.awardType?.value;
        if (
          Array.isArray(this.selectRow.fullIdPath) &&
          this.selectRow.fullIdPath.length > 0
        ) {
          this.selectRow.fullIdPath = this.selectRow.fullIdPath.map((path) => {
            const trimmedString = path.replace(/^\/|\/$/g, "");
            const parts = trimmedString.split("/");
            return parts.map((part) => part); // 直接返回每个部分
          });
        }
      } else {
        this.selectRow = cloneDeep(row);
      }
    },
    saveLimitData() {
      const resultList = cloneDeep(this.prizeList);
      const row = resultList[this.actRowIndex];
      resultList[this.actRowIndex] = {
        ...row,
        limitType: this.selectRow.limitType,
        limitInfo: this.selectRow.limitInfo || [],
        fullIdPath: this.selectRow.fullIdPath || [],
        expireDate: this.selectRow.expireDate || "",
        minAmount:
          this.selectRow.limitType === "AMOUNT"
            ? this.selectRow.minAmount
            : null,
      };
      this.prizeList = resultList;
      // this.$emit("change", resultList);
      this.limitVisible = false;
    },
    //礼品类型切换，商品数据
    changeType(row) {
      //耗材，清空
      row.skuId = "";
      row.articleCode = "";
      if (row.awardType == "PART") {
        row.awardName = "";
        row.price = "";
      } else if (row.awardType == "REPAIR") {
        row.price = 1;
      }
    },
    checkSKU(row, index) {
      this.skuVisible = true;
      this.actRowIndex = index;
      this.selectRow = row;
      this.$nextTick(() => {
        this.skuLocalPagination = {
          pageNumber: 1,
          pageSize: 10,
          total: 0,
        };
        this.$refs.skuProTable.refresh();
      });
      // this.loadData({ pageNumber: 1, pageSize: 10 });
    },
    saveStuData() {
      const resultList = cloneDeep(this.prizeList);
      const row = resultList[this.actRowIndex];
      resultList[this.actRowIndex] = merge({}, row, this.selectRow);
      console.log(resultList);
      this.prizeList = resultList;
      this.skuVisible = false;
    },
    selectStu(selectedRows, row) {
      this.selectRow.articleCode = row.articleCode;
      this.selectRow.awardName = row.itemName;
      this.selectRow.price = row.saleUnitPrice;
      this.selectRow.skuId = row.saleSkuId;
      this.$refs.skuProTable.setSelection([this.selectRow.skuId]);
    },
    handleDel(row, index) {
      const resultList = cloneDeep(this.prizeList);
      resultList.splice(index, 1);
      this.prizeList = resultList;
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    //奖品发放
    sendPrize(row, type) {
      this.grandType = type;
      this.activeCustomer = row;
      this.prizeVisible = true;
      if (type === "send") {
        this.dialogTableColumns = this.prizeColumns;
        this.prizeLoadData();
      } else if (type === "record") {
        this.dialogTableColumns = this.recordColumns;
        this.prizeRecordData();
      }
    },
    refresh() {
      if (this.grandType === "send") {
        this.prizeLoadData();
      } else if (this.grandType === "record") {
        this.prizeRecordData();
      }
    },
    //发放记录
    prizeRecordData() {
      this.prizeList = [];
      getGrandRecordApi({
        activityId: this.activityData.id,
        customerId: this.activeCustomer.customerId,
        pageNumber: 1,
        pageSize: 10000,
      })
        .then((result) => {
          if (result.code === 200 && result.data.rows) {
            this.prizeList = result.data.rows.map((item) => {
              return {
                ...item,
                flag: true,
              };
            });
            this.localPagination.total = +result.data.total;
          }
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    //奖品列表
    prizeLoadData() {
      getActivitiesPrizeApi(this.activityData.id)
        .then((result) => {
          if (result.code === 200 && result.data) {
            this.prizeList = (result.data || []).map((item) => {
              return {
                ...item,
                flag: true,
              };
            });
            this.localPagination.total = +result.data.length;
          }
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    // 查看注册客户详情
    showRegister(row) {
      this.$refs.statisticCustomer.show(row, "add");
    },
    // 查看订单消费明细
    showOrderDetail(row) {
      this.$refs.statisticOrder.show(row);
    },
    // 查看点击客户明细
    showClickNum(row) {
      this.$refs.statisticCustomer.show(row, "info");
    },
    // 获取统计数据
    getStatData() {
      const args = {
        ...this.requestParameters,
        activityId: this.activityData.id,
      };
      getActivityShareStatApi(args).then((res) => {
        if (res.code === 200) {
          this.statData = res.data;
        }
      });
    },
    handleLimitType() {
      this.selectRow.limitInfo = [];
      this.selectRow.fullIdPath = [];
      this.selectRow.minAmount = null;
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.modelOptions = res.data;
      });
    },
    handleProductChange(arr) {
      this.selectRow.limitInfo = arr.map((item) => item[item.length - 1]);
    },
    sendAward() {
      let data = this.prizeList.filter((o) => {
        return o.num && o.num > 0;
      });
      if (data.length === 0) {
        this.$message({
          type: "warning",
          message: "请选择发放奖品数量",
        });
        return;
      } else {
        data = data.map((o) => {
          return {
            awardId: o.id,
            quantity: o.num, // 数量
            awardType: o.awardType,
            skuId: o.skuId,
            articleCode: o.articleCode,
            price: o.price,
            awardName: o.awardName,
            limitInfo: o.limitInfo,
            limitType: o.limitType,
            expireDate: o.expireDate,
            minAmount: o.minAmount,
          };
        });
        putActivitiesGrandApi({
          awardGrants: data,
          customerId: this.activeCustomer.customerId,
          id: this.activityData.id,
        }).then((res) => {
          if (res.code === 200) {
            this.$message({
              type: "success",
              message: res.message || "发放成功",
            });
            this.prizeVisible = false;
          } else {
            this.$message({
              type: "error",
              message: res.message || "发放失败",
            });
          }
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.statistic-activity {
  font-family: Avenir, Helvetica, Arial, sans-serif;
}
.detail-info {
  :deep(.el-form-item__label) {
    font-family: Avenir, Helvetica, Arial, sans-serif;
  }
  .el-form-item__content span {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    display: inline-block;
    width: 200px;
  }
  .el-table th.el-table__cell > .cell {
    font-family: Avenir, Helvetica, Arial, sans-serif;
  }
}
:deep(.center-dialog) {
  .el-dialog__body {
    padding-top: 10px;
    .el-table__body-wrapper {
      overflow: auto;
    }
  }
  &.el-dialog {
    top: 50%;
    transform: translate(0, -50%);
    margin-top: 0 !important;
  }
  .has-gutter .el-checkbox {
    display: none;
  }
}
</style>
