<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-29 12:02:17
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 18:15:42
 * @Description: 耗材领料
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="createdOrder"
        >
          创建领料单
        </el-button>
        <div class="box">
          <div class="title-box-right">
            <div>总订单数量：{{ totalData?.orderNum || 0 }}</div>
            <div>总商品金额：{{ totalData?.itemAmount || 0 }}</div>
            <div>总配送费：{{ totalData?.shippingFeeAmount || 0 }}</div>
            <div>总订单金额：{{ totalData?.totalAmount || 0 }}</div>
          </div>
        </div>
      </template>
      <template #type="slotProps">
        {{ slotProps.row.type.label }}
      </template>
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleInfo(row, 'info')"
          >
            查看
          </el-button>
          <!-- <el-button icon="el-icon-edit" @click="handleInfo(row, 'edit')">
            编辑
          </el-button> -->
          <!-- @click="showAuditDialog(row)" -->
          <el-button
            v-if="
              (row.orderType.value === 'APPLY' ||
                (row.orderType.value === 'SALE' &&
                  row.payMode.value === 'CYCLE') ||
                row.orderType.value === 'GIFT') &&
              row.orderStatus === 'WAIT_AUDIT'
            "
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(row, 'audit')"
          >
            审核
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      v-loading="infoLoading"
      :value="dialogVisible"
      :title="dialogTitle"
      size="90%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="editType === 'info'"
      :no-confirm-footer="true"
      @cancel="dialogVisible = false"
    >
      <el-steps :active="active" finish-status="success" class="steps-box">
        <el-step title="买家下单"></el-step>
        <el-step title="买家付款"></el-step>
        <el-step title="发货"></el-step>
        <el-step title="买家确认收货"></el-step>
      </el-steps>
      <!-- 买家下单 -->
      <div v-if="orderInfo" ref="dialogContent" class="order-fix">
        <!-- <el-button type="primary" class="image-btn" @click="toImage"
        >生成图片</el-button
      > -->
        <!-- <p class="text-p m-b-8">买家还有<span>10分00秒</span>支付订单。</p> -->
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="订单信息"
            :column="3"
            border
          >
            <el-descriptions-item label="订单状态">
              {{ getOrderStatusChinese(orderInfo.tradeOrder.orderStatus) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单编号">
              {{ orderInfo.tradeOrder.orderNum }}
            </el-descriptions-item>
            <el-descriptions-item label="配送方式">
              {{ orderInfo?.tradeOrder?.logisticsProvider?.label }}
            </el-descriptions-item>

            <el-descriptions-item label="关联客户">
              {{ orderInfo.companyName }}
            </el-descriptions-item>
            <el-descriptions-item label="下单用户">
              {{ orderInfo.buyerName }}
            </el-descriptions-item>
            <el-descriptions-item label="客户等级">
              {{ orderInfo?.tradeOrder?.customerLevel?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="下单手机号">
              {{ orderInfo?.tradeOrder?.consigneePhone }}
            </el-descriptions-item>
            <el-descriptions-item label="支付方式">
              {{ orderInfo?.tradeOrder?.payMode?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="总成本（元）">
              {{ orderInfo.tradeOrder?.totalCostAmount }}
            </el-descriptions-item>
            <el-descriptions-item label="收货地址" :span="3">
              {{ orderInfo?.tradeOrder?.consigneeFullAddress }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 商品信息 -->
        <div class="m-t-8">
          <p class="tit-box m-b-12">商品信息</p>
          <ProTable
            ref="ProSPXXTable"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="goodsColumns"
            :show-pagination="false"
            :show-loading="false"
            :data="orderInfo.tradeOrder.tradeOrderDetailList || []"
            :show-setting="false"
            :show-search="false"
            :show-table-operator="false"
            sticky
            :height="300"
          >
            <template #storageArticle="{ row }">
              {{ row.storageArticle?.minUnit }}
            </template>
            <!-- 领料单修改领取数量 -->
            <template #itemNum="{ row }">
              <div
                v-if="
                  orderInfo.tradeOrder.orderType?.value === 'APPLY' &&
                  editType === 'audit'
                "
              >
                <el-input
                  v-model="row.itemNum"
                  type="number"
                  placeholder="购买数量"
                  size="small"
                  @change="(e) => handleItemChange(e, row)"
                ></el-input>
              </div>
              <div v-else>{{ row.itemNum }}</div>
            </template>
            <template #actualUnitPrice="{ row }">
              <span
                v-if="
                  editType === 'info' ||
                  orderInfo.tradeOrder?.orderType?.value === 'APPLY'
                "
              >
                {{ row.actualUnitPrice }}
              </span>

              <el-input
                v-if="
                  isCycleAudit(
                    orderInfo.tradeOrder?.orderType?.value,
                    orderInfo.tradeOrder?.payMode?.value
                  )
                "
                v-model="row.actualUnitPrice"
                type="number"
                placeholder="请输入商品单价"
                @change="(e) => handleInputChange(e, row)"
              ></el-input>
            </template>
          </ProTable>
          <div class="text-content">
            <p class="text-p">
              <label class="p-label">订单商品金额（元）：</label>
              <span class="p-content">
                {{ orderInfo.tradeOrder.actualGoodsAmount }}
              </span>
            </p>

            <p class="text-p">
              <label class="p-label">成本价（元）：</label>
              <span class="p-content">
                {{ orderInfo.tradeOrder.costAmount }}
              </span>
            </p>

            <p class="text-p">
              <label class="p-label">订单运费（元）：</label>
              <span
                v-if="
                  editType === 'info' ||
                  orderInfo.tradeOrder?.orderType?.value === 'APPLY'
                "
                class="p-content"
              >
                {{ orderInfo.tradeOrder.shippingFee }}
              </span>
              <el-input
                v-if="
                  isCycleAudit(
                    orderInfo.tradeOrder?.orderType?.value,
                    orderInfo.tradeOrder?.payMode?.value
                  )
                "
                v-model="orderInfo.tradeOrder.shippingFee"
                style="width: 50%"
                type="number"
                placeholder="订单运费"
                min="0"
                @input="
                  (e) => handleShippingFeeInputChange(e, orderInfo.tradeOrder)
                "
              ></el-input>
            </p>
            <!--<p class="text-p">-->
            <!--  <label class="p-label">实收款（元）：</label>-->
            <!--  <span class="p-content">-->
            <!--    {{ orderInfo.tradeOrder.actualAmount }}-->
            <!--  </span>-->
            <!--</p>-->
            <p v-if="orderInfo.tradeOrder.buyerRemark" class="text-p m-b-8">
              <label class="p-label">订单备注：</label>
              <span class="p-content">
                {{ orderInfo.tradeOrder.buyerRemark }}
              </span>
            </p>
          </div>
        </div>

        <!-- 交易明细 -->
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="交易明细"
            :column="3"
            border
          >
            <el-descriptions-item label="订单来源">
              {{ getOrderStatusText(orderInfo.tradeOrder.orderSource) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单创建时间">
              {{ orderInfo.tradeOrder.createdAt }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderInfo.tradeOrder.payTime"
              label="订单支付时间"
            >
              {{ orderInfo.tradeOrder.payTime }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderInfo.deliveryTime"
              label="订单发货时间"
            >
              {{ orderInfo.deliveryTime }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderInfo.tradeOrder.finishTime"
              label="订单确认收货时间"
            >
              {{ orderInfo.tradeOrder.finishTime }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 物流信息 -->
        <div
          v-if="tradeInfo?.length > 0"
          class="m-t-8 box-box"
          style="clear: both"
        >
          <p class="tit-box m-b-12">物流信息</p>
          <div style="overflow: hidden">
            <div
              v-for="(item, index) in tradeInfo"
              :key="index"
              style="float: left; margin-right: 20px"
              @click="changeTrade(item)"
            >
              <div
                :class="
                  tradeInfoDetail.waybillNumber == item.waybillNumber
                    ? 'trade active'
                    : 'trade'
                "
              >
                <text class="iconfont iconbaoguo"></text>
                <div class="info">
                  <div>{{ item.packageName }}</div>
                  <div>共{{ item.expectedNumber }}件</div>
                </div>
              </div>
            </div>
          </div>
          <div class="tradedetail">
            <div>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'jdl'"
                class="iconfont iconsr_jingdong"
                style="color: red; font-size: 50rpx"
              ></text>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'iss'"
                class="iconfont iconshansonghuise"
                style="color: #ee822f; font-size: 50rpx"
              ></text>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'self'"
                class="iconfont iconziti"
                style="color: #ee822f; font-size: 50rpx"
              ></text>
              <text style="margin-left: 20rpx">
                {{ tradeInfoDetail?.logisticsProvider?.label }}:{{
                  tradeInfoDetail?.waybillNumber
                }}
              </text>
            </div>
            <div style="margin-top: 30px">
              <el-timeline>
                <el-timeline-item
                  v-for="item in tradeInfoDetail.traces"
                  :key="item.id"
                  :timestamp="item.providerStatus + '    ' + item.operatedAt"
                >
                  {{ item.operationRemark }}
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div v-if="editType === 'audit'" class="footer-btn">
          <el-button
            type="danger"
            @click="handleAudit(orderInfo.tradeOrder, 'REFUSE')"
          >
            驳回
          </el-button>
          <el-button
            type="primary"
            @click="handleAudit(orderInfo.tradeOrder, 'APPROVE')"
          >
            审核通过
          </el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
        </div>
      </template>
    </ProDrawer>
    <CreateApplyOrder ref="createOrder" @refresh="refresh" />
  </div>
</template>
<script>
import CreateApplyOrder from "@/views/store/components/createApplyOrder.vue";
import {
  operatorTradeOrderPageApi,
  operatorTradeOrderDetailApi,
  OrderTracesApi,
  operatorTradeOrderAuditApi,
  operatorTradeOrderSaleAuditApi,
  getOrderStatisticsApi,
} from "@/api/operator";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";

import { cloneDeep } from "lodash";
import { Message } from "element-ui";
import { filterParamRange, mulAmount } from "@/utils";
export default {
  name: "ApplyOrder",
  components: {
    CreateApplyOrder,
  },
  props: {},
  data() {
    return {
      queryParam2: {
        lastIds: null,
      },
      active: 0,
      // 列表
      tableData: [],
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      queryParam: {
        orderNum: "",
        orderTypes: ["APPLY", "GIFT"],
      },
      columns: [
        {
          dataIndex: "orderStatusList",
          title: "订单状态",
          isTable: true,
          formSpan: 8,
          isSearch: true,
          multiple: true,
          valueType: "select",
          formatter: (row) => {
            switch (row.orderStatus) {
              case "WAIT_PAY":
                return "待支付";
              case "WAIT_DELIVER":
                return "待发货";
              case "WAIT_RECEIVE":
                return "待收货";
              case "WAIT_AUDIT":
                return "待审核";
              case "SUCCESS":
                return "已完成";
              case "CLOSED":
                return "已取消";
            }
          },
          option: [
            { label: "待支付", value: "WAIT_PAY" },
            { label: "待发货", value: "WAIT_DELIVER" },
            { label: "待收货", value: "WAIT_RECEIVE" },
            { label: "待审核", value: "WAIT_AUDIT" },
            { label: "已完成", value: "SUCCESS" },
            { label: "已取消", value: "CLOSED" },
          ],
        },
        {
          dataIndex: "orderType",
          title: "订单类型",
          isTable: true,
          formatter: (row) => row.orderType?.label,
        },
        {
          dataIndex: "orderType",
          title: "订单类型",
          isSearch: true,
          valueType: "select",
          option: [
            { label: "领料单", value: "APPLY" },
            { label: "赠品订单", value: "GIFT" },
          ],
        },
        {
          dataIndex: "payTime",
          title: "支付时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          clearable: true,
        },
        {
          dataIndex: "logisticsProviders",
          title: "配送方式",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            { label: "京东快递", value: "jdl" },
            { label: "闪送", value: "iss" },
            { label: "自提", value: "self" },
            { label: "工程师带", value: "passing" },
          ],
        },
        {
          dataIndex: "orderNum",
          title: "订单号",
          width: 180,
          isTable: true,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
        },
        {
          dataIndex: "consigneeFullAddress",
          title: "收货地址",
          isTable: true,
          minWidth: 210,
        },
        // {
        //   dataIndex: "payModes",
        //   title: "支付方式",
        //   isSearch: true,
        //   valueType: "select",
        //   multiple: true,
        //   // optionMth: () => dictTreeByCodeApi(4200),
        //   option: [
        //     { label: "微信支付", value: "WECHART" },
        //     { label: "线下支付", value: "OFFLINE" },
        //     { label: "期结", value: "CYCLE" },
        //   ],
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },

        {
          dataIndex: "consigneePhone",
          title: "手机号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "logisticsProvider",
          title: "配送方式",
          isTable: true,
          formatter: (row) => row.logisticsProvider?.label,
        },
        // {
        //   dataIndex: "payMode",
        //   title: "支付方式",
        //   isTable: true,
        //   formatter: (row) => row.payMode?.label,
        // },
        {
          dataIndex: "actualGoodsAmount",
          title: "商品金额",
          isTable: true,
        },
        {
          dataIndex: "shippingFee",
          title: "配送费",
          isTable: true,
        },
        {
          dataIndex: "actualAmount",
          title: "订单金额",
          isTable: true,
        },
        {
          dataIndex: "costAmount",
          title: "成本金额",
          isTable: true,
        },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          isTable: true,
          width: 150,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
        },
        {
          dataIndex: "payTime",
          title: "支付时间",
          isTable: true,
          width: 150,
          formatter: (row) => (row.payTime ? row.payTime : "/"),
        },
        {
          dataIndex: "orderNum",
          title: "订单号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "Actions",
          width: 160,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      //新增
      editType: "info",
      confirmLoading: false,
      dialogTitle: "",
      dialogVisible: false,
      goodsColumns: [
        {
          dataIndex: "itemName",
          isTable: true,
          title: "商品名称",
          minWidth: 120,
        },
        {
          dataIndex: "code",
          isTable: true,
          title: "物品编号",
          formatter: (row) => row.storageArticle?.code,
          minWidth: 150,
        },
        {
          dataIndex: "name",
          isTable: true,
          title: "物品名称",
          formatter: (row) => row.storageArticle?.name,
          minWidth: 140,
        },
        {
          dataIndex: "numberOem",
          isTable: true,
          title: "OEM编号",
          formatter: (row) => row.storageArticle?.numberOem,
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerChannel",
          isTable: true,
          title: "制造渠道",
          formatter: (row) => row.storageArticle?.manufacturerChannel.label,
        },
        {
          dataIndex: "saleUnitPrice",
          isTable: true,
          title: "商品单价（元）",
          minWidth: 120,
        },
        {
          dataIndex: "actualUnitPrice",
          isTable: true,
          title: "成交价（元）",
          // tableSlot: "actualUnitPrice",
          minWidth: 120,
        },
        {
          dataIndex: "costPrice",
          isTable: true,
          title: "成本价（元）",
          minWidth: 120,
        },
        {
          dataIndex: "itemNum",
          isTable: true,
          title: "购买数量",
          tableSlot: "itemNum",
          minWidth: 100,
        },
        {
          dataIndex: "deliveryNum",
          isTable: true,
          title: "已发数量",
        },
        {
          dataIndex: "receiveNum",
          isTable: true,
          title: "已收数量",
        },
        {
          dataIndex: "reverseNum",
          isTable: true,
          title: "已退数量",
        },
        {
          dataIndex: "storageArticle",
          isTable: true,
          title: "单位",
          tableSlot: "storageArticle",
        },
        // {
        //   dataIndex: "discountAmount",
        //   isTable: true,
        //   title: "会员减免（元）",
        //   formatter: (row) => "-" + row.discountAmount,
        //   minWidth: 100,
        // },
        {
          dataIndex: "costAmount",
          isTable: true,
          title: "小计（元）",
          minWidth: 100,
        },
      ],
      tradeInfo: [],
      tradeInfoDetail: {},
      orderInfo: null,
      infoLoading: false,
      dataInfo: {},
      minUnit: "个",
      totalData: {},
    };
  },
  computed: {},
  watch: {},
  mounted() {
    if (this.$route.query.id) {
      this.queryParam.orderNum = this.$route.query.id;
    }
    this.$refs.ProTable?.refresh();
  },
  methods: {
    createdOrder() {
      this.$refs.createOrder.show();
    },
    isCycleAudit(orderType, payMode) {
      return (
        this.editType === "audit" && orderType === "SALE" && payMode === "CYCLE"
      );
    },
    handleItemChange(e, row) {
      // 输入数量不能小于0
      if (e < 1) {
        Message.error("购买数量不能小于1");
        row.itemNum = 1;
      }
    },
    handleInputChange(val, row) {
      row.payAmount = mulAmount(val, row.itemNum) - Number(row.discountAmount);

      const { tradeOrderDetailList, shippingFee, discountAmount } =
        this.orderInfo.tradeOrder;

      const totalPayAmount = tradeOrderDetailList.reduce(
        (total, item) => total + Number(item.payAmount),
        0
      );

      this.orderInfo.tradeOrder.actualAmount = (
        parseFloat(totalPayAmount) +
        parseFloat(shippingFee) -
        parseFloat(discountAmount)
      ).toFixed(2);
    },
    handleShippingFeeInputChange(e, tradeOrder) {
      // 确保输入值 e 是有效的数字
      const shippingFee = parseFloat(e);
      if (isNaN(shippingFee)) {
        Message.error("请输入有效的数字");
        return;
      }

      // 获取相关数据
      const { tradeOrderDetailList, discountAmount } = tradeOrder;

      // 计算总支付金额
      const totalPayAmount = tradeOrderDetailList.reduce(
        (total, item) => total + Number(item.payAmount),
        0
      );
      // 更新实际金额
      this.orderInfo.tradeOrder.actualAmount = (
        parseFloat(totalPayAmount) +
        shippingFee -
        parseFloat(discountAmount)
      ).toFixed(2);
    },
    // 审核订单
    async handleAudit(row, status) {
      const confirmText = status === "APPROVE" ? "通过" : "驳回";
      const confirmType = status === "APPROVE" ? "success" : "warning";

      const result = await this.$confirm(
        `此操作将${confirmText}该订单的审核, 是否继续?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: confirmType,
        }
      ).catch(() => {});

      if (!result) return;

      const apiMap = {
        APPLY: operatorTradeOrderAuditApi,
        SALE: operatorTradeOrderSaleAuditApi,
        GIFT: operatorTradeOrderAuditApi,
      };

      const api = apiMap[row.orderType.value];
      if (!api) {
        Message.error("不支持该订单类型的审核");
        return;
      }

      const params = {
        ...(row.orderType.value === "APPLY" || row.orderType.value === "GIFT"
          ? { id: row.id }
          : { tradeOrder: row }),
        auditStatus: status,
        remark: row.remark,
        ...(row.orderType.value === "APPLY"
          ? { tradeOrderDetails: row.tradeOrderDetailList }
          : null),
      };
      try {
        const response = await api(params);
        if (response.code === 200) {
          this.handleSuccess(status);
          this.$refs.ProTable.refresh();
        } else {
          Message.error("审核失败");
        }
      } catch (e) {
        Message.error("系统错误");
      }
    },

    handleSuccess(status) {
      const message = status === "APPROVE" ? "审核通过" : "已驳回";
      Message.success(message);
      this.dialogVisible = false;
    },
    // 获取统计数据
    getOrderStatisticsFn(params) {
      getOrderStatisticsApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    //加载表格
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const result = [
        {
          orderTimeStart: null,
          orderTimeEnd: null,
          data: parameter.createdAt,
        },
        {
          payTimeStart: null,
          payTimeEnd: null,
          data: parameter.payTime,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      delete requestParameters.payTime;
      operatorTradeOrderPageApi(requestParameters)
        .then((res) => {
          const result = [];
          this.localPagination.total = parseInt(res.data.total);
          if (res.data.rows.length === 0) {
            this.tableData = [];
            return;
          }

          res.data.rows.forEach(async (item, index) => {
            const code = item.consigneeRegionCode;
            result[index] = item;
            if (result.length === res.data.rows.length) {
              this.tableData = result;
            }
          });
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getOrderStatisticsFn(requestParameters);
    },

    // 触发详情
    handleInfo(row, type) {
      this.confirmLoading = true;
      this.dialogTitle =
        type === "info" ? "查看 - " + row.orderNum : "编辑 - " + row.orderNum;
      this.editType = type;
      this.dialogVisible = true;
      this.infoLoading = true;
      operatorTradeOrderDetailApi(row.orderNum)
        .then((res) => {
          this.orderInfo = res.data;
        })
        .finally((_) => {
          this.infoLoading = false;
          this.confirmLoading = false;
        });
      OrderTracesApi(row.orderNum).then((res) => {
        this.tradeInfo = res.data;
        this.tradeInfoDetail = this.tradeInfo[0];
      });
    },
    changeTrade(data) {
      this.tradeInfoDetail = data;
    },
    /**
     * @description 获取省市区区域数据
     * @param node
     * @param {Function} resolve
     * @returns {Promise<void>}
     */
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 处理省市区数据
     * @param list
     * @returns {*}
     */
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    /**
     * 获取订单状态
     */
    getOrderStatusChinese(orderStatus) {
      let value = "";
      switch (orderStatus) {
        case "CLOSED":
          value = "订单关闭";
          this.active = null;
          break;
        case "PAID":
          value = "已支付";
          this.active = 2;
          break;
        case "SUCCESS":
          value = "交易成功";
          this.active = 4;
          break;
        case "WAIT_DELIVER":
          value = "待发货";
          this.active = 2;
          break;
        case "WAIT_PAY":
          value = "待支付";
          this.active = 1;
          break;
        case "WAIT_AUDIT":
          value = "待审核";
          this.active = 1;
          break;
        case "WAIT_RECEIVE":
          value = "待收货";
          this.active = 3;
          break;
      }
      return value;
    },

    getOrderStatusText(orderStatus) {
      let value = "";
      switch (orderStatus) {
        case "ITEM":
          value = "商品直接下单";
          break;
        case "CART":
          value = "购物车下单";
          break;
        case "SELLER_CREATE":
          value = "卖家代客下单";
          break;
        case "CONTRACT_GIFT":
          value = "签约赠送";
          break;
        case "ACTIVYTY_GIFT":
          value = "活动奖励";
          break;
      }
      return value;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>
<style lang="scss" scoped>
.box {
  display: flex;
  justify-content: space-between;
  flex: 1;
}
.footer-btn {
  display: flex;
  justify-content: center;
  //gap: 10px;
  margin-top: 20px;
}
.box-box {
  .tradedetail {
    width: 100%;
    height: 100%;
    clear: both;
    margin-top: 20px;
  }

  .trade {
    width: 140px;
    padding: 10px 0;
    text-align: center;
    border: 1px solid #555555;
    border-radius: 20px;
    cursor: pointer;
  }

  .info {
    display: inline-block;

    text {
      display: block;
      text-align: left;
    }
  }

  .iconbaoguo {
    font-size: 40px;
  }

  .trade.active {
    border: 1px solid #ee822f;
    color: #ee822f;
  }
}

::v-deep .el-upload--picture-card,
::v-deep .el-upload-list__item {
  width: 120px;
  height: 120px;
}

.steps-box {
  position: relative;
  width: 80%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  z-index: 2;
}

.order-fix {
  margin-left: 20px;
  font-size: 14px;

  .red {
    color: #d14b50;
  }

  .text-content {
    display: flex;
    justify-content: space-between;
    .text-p {
      color: #606266;

      .p-label {
        color: #606266;
        font-weight: 700;
      }
      margin: 30px 0;
    }
  }

  .content-fixed {
    display: flex;
    justify-content: space-between;

    .text-p {
      flex: 1;
      display: flex;
    }
  }

  .btn-p {
    margin-top: 15px;

    .el-button {
      padding: 8px 29px;
    }
  }

  .order-border-box {
    border: dashed 1px #ccc;
    padding: 10px;
  }

  .title-p {
    background: #d9d9d9;
    color: #232323;
    padding: 5px;
  }
}

.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
</style>
