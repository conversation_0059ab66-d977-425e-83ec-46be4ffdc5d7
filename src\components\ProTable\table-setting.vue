<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-09-19 18:44:25
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-04 17:31:36
 * @FilePath: src/components/ProTable/table-setting.vue
 * @Description:  
 * 
-->
<!-- table-setting -->
<template>
  <div class="table-setting">
    <i class="el-icon-refresh" @click="refresh" />
    <slot></slot>
  </div>
</template>

<script>
import { getScrollBarWidth } from "@/utils";
import { toggleClass, hasClass } from "@/utils";
const scrollBarWidth = getScrollBarWidth();
export default {
  name: "TableSetting",
  props: {
    refresh: {
      type: Function,
      default: () => {},
    },
    showSettingBtn: {
      type: Boolean,
      default: false,
    },
    columns: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {};
  },
  computed: {
    dragOptions() {
      return {
        animation: 0,
        group: "description",
        disabled: false,
        ghostClass: "ghost",
      };
    },
    // draggableList() {
    //   get: () => prop.columns,
    //         set: (val) => {
    //           emit('update:columns', val)
    //           emit('reRender')
    //         },
    // }
  },
  methods: {
    handleSetting() {
      this.$emit("handleSetting");
    },
    fullscreen() {
      const el = document.querySelector("#app .app-container");
      toggleClass(el, "content-screenfull");
      if (hasClass(el, "content-screenfull")) {
        document.documentElement.style.overflow = "hidden";
        document.body.style.borderRight =
          scrollBarWidth + "px solid transparent";
      } else {
        document.documentElement.style.overflow = "";
        document.body.style.borderRight = "";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.list-group-item {
  max-width: 80%;
  min-width: 40%;
  margin: 5px auto;
  padding: 10px 20px;
  cursor: pointer;
  box-shadow: 0px 1px 3px 0 rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: space-between;

  span {
    vertical-align: middle;
  }

  .btnWrap {
    display: flex;
    align-items: center;
  }
}
</style>
