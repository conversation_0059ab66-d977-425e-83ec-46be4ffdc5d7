<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 11:17:40
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="active" style="padding: 10px">
      <el-tab-pane label="软件更新记录" name="first" lazy>
        <Software />
      </el-tab-pane>
      <el-tab-pane label="OID配置表（新）" name="second" lazy>
        <OidSet />
      </el-tab-pane>
      <el-tab-pane label="OID配置表（旧）" name="third" lazy>
        <Oid />
      </el-tab-pane>
      <el-tab-pane label="取值配置接口" name="fourth" lazy>
        <Allocation />
      </el-tab-pane>
      <el-tab-pane label="数字代码记录" name="fifth" lazy>
        <Code />
      </el-tab-pane>
      <el-tab-pane label="异常日志" name="sixth" lazy>
        <Abnormal />
      </el-tab-pane>
      <el-tab-pane label="绑定日志" name="seventh" lazy>
        <BindLog />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Software from "@/views/dispose/components/iot/software.vue";
import Oid from "@/views/dispose/components/iot/oid.vue";
import OidSet from "@/views/dispose/components/iot/oidSet.vue";
import Allocation from "@/views/dispose/components/iot/allocation.vue";
import Code from "@/views/dispose/components/iot/code.vue";
import Abnormal from "@/views/dispose/components/iot/abnormal.vue";
import BindLog from "@/views/dispose/components/iot/bindLog.vue";
export default {
  name: "IOT",
  components: { Software, Oid, Allocation, Code, Abnormal, OidSet, BindLog },
  data() {
    return {
      active: "first",
    };
  },
};
</script>

<style scoped lang="scss"></style>
