<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 15:31:04
 * @Description: 
 -->
<!-- 头部搜索 -->
<template>
  <div
    v-show="searchListAll && searchListAll.length > 0"
    class="search-container"
  >
    <div
      id="search-form-wrap"
      ref="searchWrapper"
      class="table-page-search-wrapper"
    >
      <el-form
        ref="SearchQueryForm"
        :model="queryParamVal"
        class="search-form"
        :label-position="labelPosition"
        :rules="isrules ? rules : {}"
        :label-width="layout.labelWidth || '100px'"
        :inline="true"
        @submit.native.prevent
      >
        <div style="display: inline-block">
          <slot name="form"> </slot>
        </div>

        <div style="display: flex; flex-direction: column; width: 100%">
          <el-row style="width: 100%; display: flex; flex-wrap: wrap">
            <el-col
              v-for="(item, index) in searchListAll"
              :key="index"
              :span="getSearchSpan(item)"
              :xs="item.form_xs || item.searchSpan"
              :sm="item.form_sm || item.searchSpan"
              :md="item.form_md || item.searchSpan"
              :lg="item.form_lg || item.searchSpan"
              :xl="item.form_xl || item.searchSpan"
            >
              <div>
                <!-- Custom Slot Rendering -->
                <template v-if="item.searchOtherSlot">
                  <slot :name="item.searchOtherSlot" :item="item"></slot>
                </template>
                <template v-else-if="item.searchSlot">
                  <el-form-item
                    :prop="item.dataIndex"
                    :label="item.title + ' : '"
                  >
                    <slot :name="item.searchSlot" :item="item"></slot>
                  </el-form-item>
                </template>
                <el-form-item
                  v-else
                  :prop="item.dataIndex"
                  :label="item.title + ' : '"
                >
                  <el-input
                    v-if="item.valueType === 'input'"
                    v-model.trim="queryParamVal[item.dataIndex]"
                    v-bind="item.attrs"
                    :type="item.inputType || 'text'"
                    minlength="20"
                    :maxlength="item.wordlimit || 500"
                    :autosize="item.autosize"
                    :placeholder="item.placeholder || item.title"
                    clearable
                    :disabled="item.disabled"
                    style="width: 100%"
                  />
                  <el-select
                    v-else-if="item.valueType === 'select'"
                    v-model="queryParamVal[item.dataIndex]"
                    :placeholder="item.placeholder || item.title"
                    :clearable="item.clearable || true"
                    filterable
                    :multiple="item.multiple"
                    :collapse-tags="item.collapseTags"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(s, i) in item.option"
                      :key="i"
                      :label="s.label"
                      :value="s.value"
                      :disabled="s.disabled"
                    >
                      {{ s.label }}
                    </el-option>
                  </el-select>

                  <el-date-picker
                    v-else-if="item.valueType === 'date-picker'"
                    v-model="queryParamVal[item.dataIndex]"
                    style="width: 100%"
                    :type="item.pickerType"
                    :placeholder="item.title"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :format="item.pickerFormat"
                    :value-format="item.pickerFormat"
                    v-on="item.attrs"
                  />
                  <InputRange
                    v-else-if="item.valueType === 'inputRange'"
                    v-model="queryParamVal[item.dataIndex]"
                    style="width: 100%"
                    :start-placeholder="item.startPlaceholder || item.title"
                    :end-placeholder="item.endPlaceholder || item.title"
                    @update:values="(e) => updateRange(e, item.dataIndex)"
                  />
                  <ProductTree
                    v-else-if="item.valueType === 'product'"
                    v-model="queryParamVal[item.dataIndex]"
                    style="width: 100%"
                    :placeholder="item.placeholder || item.title"
                    :multiple="item.multiple"
                    :collapse-tags="item.collapseTags"
                    :clearable="item.clearable || true"
                    @change="(e) => handleProductChange(e, item.dataIndex)"
                  />
                  <AreaTree
                    v-else-if="item.valueType === 'area'"
                    v-model="queryParamVal[item.dataIndex]"
                    style="width: 100%"
                    :placeholder="item.placeholder || item.title"
                    :multiple="item.multiple"
                    :collapse-tags="item.collapseTags"
                    :clearable="item.clearable || true"
                    @change="(e) => handleProductChange(e, item.dataIndex)"
                  />
                </el-form-item>
              </div>
            </el-col>
          </el-row>
          <slot name="header"> </slot>
          <slot name="footer"> </slot>
        </div>
      </el-form>
    </div>
    <div class="table-page-search-submitButtons">
      <el-button
        class="search-btn"
        type="primary"
        icon="el-icon-search"
        size="small"
        :loading="loading"
        @click="search"
      >
        查询
      </el-button>
      <!--<a-->
      <!--  v-if="searchListAll.filter((i) => i.isSearch).length > 12"-->
      <!--  style="margin-left: 8px; font-size: 14px"-->
      <!--  @click="toggleAdvanced"-->
      <!--&gt;-->
      <!--  {{ advanced ? "收起" : "展开" }}-->
      <!--  <i :class="advanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />-->
      <!--</a>-->
    </div>
  </div>
</template>

<script>
import InputRange from "@/components/InputRange/index.vue";
import ProductTree from "@/components/ProductTree/index.vue";
import AreaTree from "@/components/AreaTree/index.vue";
import { cloneDeep } from "lodash";
export default {
  name: "SearchForm",
  components: { InputRange, ProductTree, AreaTree },
  props: {
    isrules: {
      type: Boolean,
      default: false,
    },
    queryParam: {
      type: Object,
      default: () => {
        return {};
      },
    },
    searchList: {
      type: Array,
      default: () => {},
    },
    loading: {
      type: Boolean,
      default: false,
    },
    layout: {
      type: Object,
      default: () => {
        return {};
      },
    },
    labelPosition: {
      type: String,
      default: "right",
    },
  },
  data() {
    return {
      rules: {},
      advanced: false,
      queryParamVal: {},
      searchListAll: [],
      containerWidth: 0,
    };
  },
  computed: {
    menuBgColor() {
      return "";
    },
    containerHeightClass() {
      if (this.searchListAll.filter((i) => i.isSearch).length > 12) {
        return "search-container-height-130";
      } else if (this.advanced) {
        return "search-container-height-auto";
      } else {
        return "";
      }
    },
  },
  watch: {
    searchList: {
      handler(val) {
        this.searchListAll = cloneDeep(val);
        this.init();
      },
    },
    queryParam: {
      handler(val) {
        this.queryParamVal = cloneDeep(val);
      },
      immediate: true,
      deep: true,
    },
    queryParamVal: {
      handler(val) {
        this.$emit("searchChange", val);
      },
      immediate: true,
      deep: true,
    },
  },
  beforeMount() {},
  created() {
    this.searchListAll = cloneDeep(this.searchList);
    this.init();
  },
  mounted() {
    this.updateContainerWidth();
    window.addEventListener("resize", this.updateContainerWidth);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.updateContainerWidth);
  },
  methods: {
    init() {
      for (let index = 0; index < this.searchListAll.length; index++) {
        const element = this.searchListAll[index];
        if (element.valueType && element.valueType === "select") {
          if (element.optionMth) {
            if (element.option.length === 0) {
              this.initMthOption(element);
            }
          }
          if (!element.optionMth && element.optionskey) {
            if (element.option.length > 0) {
              this.initOption(element);
            }
          }
        }
      }
      this.initRules();
    },
    initRules() {
      this.rules = {};
      // 遍历表单列表
      this.searchListAll.forEach((i) => {
        // 如果该表单项有prop属性，则将其加入到验证规则中
        if (i.prop) {
          this.rules[i.dataIndex] = i.prop;
        }
      });
    },
    initMthOption(element) {
      element.optionMth().then((res) => {
        if (!res) return;

        const rows = res.data?.rows ?? res.data;
        const { optionskey, filterOption } = element;

        if (!rows || !Array.isArray(rows)) {
          element.option = [];
          return;
        }

        if (optionskey) {
          const { label, value } = optionskey;
          const result = [];

          rows.forEach((item) => {
            // 根据不同类型的 filterOption 进行筛选
            let shouldPush = true;
            if (filterOption) {
              if (
                Object.prototype.toString.call(filterOption) ===
                "[object Object]"
              ) {
                shouldPush = item[filterOption.key] === filterOption.value;
              } else if (Array.isArray(filterOption)) {
                shouldPush = !filterOption.includes(item.value);
              }
            }
            if (shouldPush) {
              result.push({
                label: item[label],
                value: item[value],
              });
            }
          });

          element.option = result;
        } else {
          element.option = rows.map((i) => ({
            label: i,
            value: i,
          }));
        }
      });
    },
    // initMthOption(element) {
    //   element.optionMth().then((res) => {
    //     if (!res) return;
    //     let data;
    //     if (res.data.rows) {
    //       data = res.data.rows;
    //     } else {
    //       data = res.data;
    //     }
    //     if (element.optionskey) {
    //       element.option = [];
    //       data.forEach((i) => {
    //         const obj = {};
    //         if (
    //           element.filterOption &&
    //           Object.prototype.toString.call(element.filterOption) ===
    //             "[object Object]"
    //         ) {
    //           if (i[element.filterOption.key] === element.filterOption.value) {
    //             obj.label = i[element.optionskey.label];
    //             obj.value = i[element.optionskey.value];
    //             element.option.push(obj);
    //           }
    //         } else if (
    //           element.filterOption &&
    //           Array.isArray(element.filterOption)
    //         ) {
    //           if (element.filterOption.includes(i.value)) {
    //             return false;
    //           } else {
    //             obj.label = i[element.optionskey.label];
    //             obj.value = i[element.optionskey.value];
    //             element.option.push(obj);
    //           }
    //         } else {
    //           obj.label = i[element.optionskey.label];
    //           obj.value = i[element.optionskey.value];
    //           element.option.push(obj);
    //         }
    //       });
    //     } else {
    //       element.option = data.map((i) => {
    //         const obj = {};
    //         obj.label = i;
    //         obj.value = i;
    //         return obj;
    //       });
    //     }
    //   });
    // },
    initOption(element) {
      if (!element.option) {
        console.error("element.option is empty");
        return;
      }
      const data = element.option;

      if (element.optionskey) {
        element.option = data.map((i) => {
          const obj = {};
          obj.label = i[element.optionskey.label];
          obj.value = i[element.optionskey.value];
          return obj;
        });
      } else {
        element.option = data.map((i) => {
          const obj = {};
          obj.label = i;
          obj.value = i;
          return obj;
        });
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    updateContainerWidth() {
      this.$nextTick(() => {
        try {
          const searchFormRef = this.$parent.$refs.SearchForm;
          if (!searchFormRef) {
            console.warn("SearchForm ref not found");
            return;
          }
          const container = searchFormRef.$refs.searchWrapper;
          if (!container) {
            console.warn("searchWrapper ref not found");
            return;
          }
          this.containerWidth = container.offsetWidth || 1635; // 默认值
        } catch (error) {
          console.error("Error updating container width:", error);
        }
      });
      // this.$nextTick(() => {
      //   const container = this.$parent.$refs.SearchForm.$refs.searchWrapper;
      //   if (container) {
      //     this.containerWidth = container.offsetWidth || 0;
      //   }
      // });
    },
    getSearchSpan(item) {
      if (this.containerWidth > 2616) {
        return 3; // 8
      } else if (this.containerWidth > 1962) {
        return 4; // 6
      } else if (this.containerWidth < 1308) {
        return 6; // 4
      } else if (this.containerWidth < 981) {
        return 8; // 3
      } else {
        return item.searchSpan || 5;
      }
    },
    updateRange(val, dataIndex) {
      this.$set(this.queryParamVal, dataIndex, val);
    },
    handleProductChange(val, dataIndex) {
      this.$set(this.queryParamVal, dataIndex, val);
    },
    resetQueryParam() {
      for (const key in this.queryParamVal) {
        if (this.queryParamVal[key] instanceof Array) {
          this.queryParamVal[key] = [];
        } else {
          this.queryParamVal[key] = "";
        }
      }
    },
    search() {
      this.$refs.SearchQueryForm.validate((valid) => {
        if (valid) {
          this.$emit("search", this.queryParamVal, true);
        }
      });
    },
  },
};
</script>
<style scoped>
#search-form-wrap {
  width: 100%;
  overflow: hidden;
}

.el-form-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.el-form-item .el-form-item__label {
  flex: 0 0 auto;
}

.el-form-item .el-form-item__content {
  flex: 1 !important;
}
@media (max-width: 768px) {
  .el-col {
    flex: 0 0 100% !important;
  }
}
</style>
<style lang="scss" scoped>
// 数据列表 搜索条件
.search-container {
  background: #ffffff;
  border-bottom: 10px solid #f6f8f9;
}
.search-container-height-130 {
  height: 130px;
  overflow: hidden;
}
.search-container-height-auto {
  max-height: none;
  //height: calc-size(auto);
}
.table-page-search-wrapper {
  overflow-x: hidden;
  text-align: left;
  background: #ffffff;
  //border-bottom: 10px solid #f6f8f9;
  //padding-top: 10px;
  transition: all $base-transition-time-4;
  interpolate-size: allow-keywords;
}
.table-page-search-submitButtons {
  width: 100%;
  height: 36px;
  margin-bottom: 18px;
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .el-button {
    margin-left: 10px;
  }
  a {
    margin-right: 10px;
  }
}
</style>
