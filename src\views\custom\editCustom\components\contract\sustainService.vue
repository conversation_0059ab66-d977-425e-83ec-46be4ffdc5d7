<!--
 * @Author: wskg
 * @Date: 2025-01-15 15:30:19
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 维保合约
 -->
<template>
  <div class="warranty-contract">
    <el-form
      ref="formRef"
      :model="infoData"
      label-width="140px"
      :rules="formRules"
    >
      <el-row :gutter="20">
        <!--<el-col :span="6">-->
        <!--  <el-form-item label="维保类型：" prop="warrantyType">-->
        <!--    <el-select-->
        <!--      v-model="infoData.warrantyType"-->
        <!--      placeholder="请选择维保类型"-->
        <!--      style="width: 100%"-->
        <!--      clearable-->
        <!--      :disabled="editType === 'info'"-->
        <!--    >-->
        <!--      <el-option-->
        <!--        v-for="item in warrantyTypeOptions"-->
        <!--        :key="item.value"-->
        <!--        :label="item.label"-->
        <!--        :value="item.value"-->
        <!--      ></el-option>-->
        <!--    </el-select>-->
        <!--  </el-form-item>-->
        <!--</el-col>-->
        <!-- 维保截止日期 -->
        <el-col :span="6">
          <el-form-item label="维保截止日期：" prop="warrantyExpireDate">
            <el-date-picker
              v-model="infoData.warrantyExpireDate"
              type="date"
              placeholder="选择维保截止日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              :disabled="editType === 'info'"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <!-- 维保总印量 -->
        <el-col :span="6">
          <el-form-item label="维保总印量：" prop="warrantyCount">
            <el-input
              v-model="infoData.warrantyCount"
              placeholder="请输入维保总印量"
              type="number"
              style="width: 100%"
              :disabled="editType === 'info'"
            >
              <template #suffix> 张 </template>
            </el-input>
          </el-form-item>
        </el-col>
        <!-- 维保维修次数 -->
        <el-col v-if="infoData.serType === 'MAINTENANCE'" :span="6">
          <el-form-item label="维保维修次数：" prop="warrantyRepairCount">
            <el-input
              v-model="infoData.warrantyRepairCount"
              placeholder="请输入维保维修次数"
              type="number"
              style="width: 100%"
              :disabled="editType === 'info'"
            >
              <template #suffix> 次 </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col v-if="infoData.serType === 'QA_COMPONENT'" :span="6">
          <el-form-item label="维保零件：" prop="warrantyPartTypes">
            <el-select
              v-model="infoData.warrantyPartTypes"
              placeholder="请选择维保类型"
              style="width: 100%"
              clearable
              filterable
              multiple
              :disabled="editType === 'info'"
            >
              <el-option
                v-for="item in warrantyTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 维保: 维保含零件 -->
        <!--<el-col :span="24">-->
        <!--  <el-form-item label="维保含零件：">-->
        <!--    <div class="parts-container">-->
        <!--      <el-button-->
        <!--        type="primary"-->
        <!--        size="small"-->
        <!--        :disabled="editType === 'info'"-->
        <!--        @click="handleSelectParts"-->
        <!--      >-->
        <!--        选择零件-->
        <!--      </el-button>-->

        <!--      <el-table-->
        <!--        v-if="-->
        <!--          infoData.warrantyPartTypes &&-->
        <!--          infoData.warrantyPartTypes.length-->
        <!--        "-->
        <!--        :data="infoData.warrantyPartTypes"-->
        <!--        style="width: 100%; margin-top: 10px"-->
        <!--        border-->
        <!--      >-->
        <!--        &lt;!&ndash;序号&ndash;&gt;-->
        <!--        <el-table-column type="index" label="序号" width="80" />-->
        <!--        <el-table-column prop="oemNumber" label="原厂零件编号" />-->
        <!--        <el-table-column prop="ch" label="零件中文名称" />-->
        <!--        <el-table-column prop="en" label="零件英文名称" />-->
        <!--        <el-table-column prop="type" label="物品小类" width="150">-->
        <!--          <template #default="{ row }">-->
        <!--            {{ row.type?.label }}-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column-->
        <!--          v-if="editType !== 'info'"-->
        <!--          label="操作"-->
        <!--          width="100"-->
        <!--        >-->
        <!--          <template #default="{ $index }">-->
        <!--            <el-button type="text" @click="handleDeletePart($index)">-->
        <!--              删除-->
        <!--            </el-button>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--      </el-table>-->
        <!--    </div>-->
        <!--  </el-form-item>-->
        <!--</el-col>-->
      </el-row>
    </el-form>
    <!--<ChooseSustainParts-->
    <!--  ref="chooseParts"-->
    <!--  :dialog-visible.sync="dialogVisible"-->
    <!--  :selected-data="infoData.warrantyPartTypes"-->
    <!--  @confirm="confirmChoose"-->
    <!--/>-->
  </div>
</template>

<script>
// import ChooseSustainParts from "@/views/custom/editCustom/components/contract/chooseSustainParts.vue";
import { dictTreeByCodeApi2 } from "@/api/user";

export default {
  name: "SustainService",
  components: {},
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    editType: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      dialogVisible: false,
      formRules: {
        // warrantyType: [
        //   {
        //     required: true,
        //     message: "请选择维保类型",
        //     trigger: "change",
        //   },
        // ],
        warrantyExpireDate: [
          {
            required: true,
            message: "请选择维保截止日期",
            trigger: "blur",
          },
        ],
        warrantyCount: [
          {
            required: true,
            message: "请输入维保总印量",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (value < 0) {
                callback(new Error("维保总印量不能小于0"));
              } else {
                callback();
              }
            },
          },
        ],
        warrantyRepairCount: [
          {
            required: true,
            message: "请输入维保维修次数",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (value < 0) {
                callback(new Error("维保维修次数不能小于0"));
              } else {
                callback();
              }
            },
          },
        ],
        warrantyPartTypes: [
          {
            required: true,
            message: "请选择维保零件",
            trigger: "blur",
          },
        ],
      },
      warrantyTypeOptions: [],
    };
  },
  computed: {
    infoData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  created() {
    const defaultValues = {
      warrantyType: "REPAIR",
      warrantyExpireDate: "", // 维保截止日期
      warrantyCount: 0, // 维保总印量
      warrantyRepairCount: 0,
      warrantyPartTypes: [], // 维保含零件列表
    };
    this.$emit("input", { ...defaultValues, ...this.value });
  },
  mounted() {
    this.getWarrantyTypeOptions();
  },
  methods: {
    getWarrantyTypeOptions() {
      dictTreeByCodeApi2(2100, 2101).then((res) => {
        this.warrantyTypeOptions = res.data;
      });
    },
    handleSelectParts() {
      this.dialogVisible = true;
    },
    confirmChoose(val) {
      console.log(val);
      this.infoData.warrantyPartTypes = val;
      this.dialogVisible = false;
    },
    handleDeletePart(index) {
      this.infoData.warrantyPartTypes.splice(index, 1);
    },
  },
};
</script>

<style scoped lang="scss"></style>
