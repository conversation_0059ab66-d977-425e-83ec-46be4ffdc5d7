/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-01-02 16:06:14
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-03 17:04:16
 * @FilePath: src/api/repair.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { get, post, put, del, postDown } from "@/utils/request";
// 维修工单管理
export const getWorkOrderByPageApi = (data) =>
  post(`/work-order-pc/page`, data);
export const WorkOrderInfoApi = (id) => get(`/work-order-pc/pcDetail/${id}`);
// 收款单查看维修工单详情
export const WorkOrderDetailInfoApi = (code) =>
  get(`/work-order-pc/detailByCode/${code}`);
export const closeOrderApi = (data) => post(`/work-order-pc/closeOrder`, data);
export const changeEngineerApi = (data) =>
  post(`/work-order-pc/changeEngineer`, data);
// 转派工单
export const changeOrderApi = (data) => post(`/work-order/transfer`, data);
export const engineerListApi = (data) =>
  get(`/engineer-info/enablePcList/${data}`);

export const getReportApi = (data) => get(`/work-order-pc/getReport/${data}`);

export const calcPay = (data) => post(`/work-order-pc/calcPay`, data);
// 维修工单数据汇总
export const getWorkOrderSummary = (data) => post(`/work-order-pc/total`, data);

//  =======================================  安装工单管理  =======================================
// 列表
export const getInstallOrderByPageApi = (data) =>
  post("/install-order/page", data);
// 工单详情
export const getInstallOrderDetailApi = (id) => get(`/install-order/${id}`);
// 获取收货机器明细
export const getReceiveMachineApi = (installCode) =>
  get(`/install-order/exchangeMachines/${installCode}`);
// 获取安装机器明细
export const getInstallMachineApi = (installCode) =>
  get(`/install-order/exchangeNewMachines/${installCode}`);
// 换机收货
export const receiveMachineApi = (data) => post("/install-order/receive", data);
// 工单分配工程师
export const assignEngineerApi = (data) =>
  put("/install-order/distribution", data);
//  =======================================  程师管理管理  =======================================
export const getEngineerInfoApi = (id) => get(`/engineer-info/detail/${id}`);
export const getEngineerByPageApi = (data) => get("/engineer-info/page", data);
export const updateEngineerApi = (params) =>
  post("/engineer-info/extendInfoAdd", params);

// 机型维修报价
export const repairPriceByPageApi = (data) => post("/repairPrice/page", data);
export const repairPriceAddApi = (params) => post("/repairPrice/add", params);
export const repairPriceEditApi = (params) => post("/repairPrice/edit", params);
export const repairPriceInfoApi = (id) => get(`/repairPrice/detail/${id}`);
export const repairPriceDelApi = (id) => del("/repairPrice/" + id);
export const repairPriceAuditApi = (data) => put("/repairPrice/audit", data);

// 远程维修报价
export const visitPriceByPageApi = (data) => post("/visitPrice/page", data);
export const visitPriceAddApi = (params) => post("/visitPrice/add", params);
export const visitPriceEditApi = (params) => post("/visitPrice/edit", params);
export const visitPriceInfoApi = (id) => get(`/visitPrice/detail/${id}`);
export const visitPriceDelApi = (id) => del("/visitPrice/" + id);
export const visitPriceAuditApi = (data) => put("/visitPrice/audit", data);

// 申诉单
export const appealByPageApi = (data) => post("/appeal-pc/page", data);

export const cancelAppealApi = (id) => post(`/appeal-applet/cancel`, id);

//领料单
export const pcListPageApi = (data) => post("/applyOrder/pcListPage", data);
export const detailInfoApi = (id) => get(`/applyOrder/detail/${id}`);
export const cancelDetailApi = (data) => post(`/applyOrder/cancelDetail`, data);
export const cancelOrderApi = (id) => post(`/applyOrder/cancelOrder`, id);
//退料单
export const applyReturnPageApi = (data) => post("/applyReturn/page", data);
export const applyReturnInfoApi = (id) => get(`/applyReturn/detail/${id}`);
// 工程师退料审核
export const applyReturnAuditApi = (data) => put("/applyReturn/approve", data);

// 客户自修登记 分页
export const selfRepairApi = (data) => post("/self/repair-report/page", data);
export const selfRepairDetailApi = (id) =>
  get(`/self/repair-report/pcPageDetail/${id}`);

// ======================  拜访数据  ==================
export const visitDataApi = (data) =>
  post("/customer-call-record/pageList", data);
// 列表数据汇总
export const visitDataStatApi = (data) =>
  post("/customer-call-record/statisticsSummart", data);
// 拜访明细
export const visitDetailApi = (id) => get(`/customer-call-record/${id}`);
// 更新拜访记录
export const updateVisitDetailApi = (data) =>
  put("/customer-call-record", data);
// 删除拜访明细
export const delVisitDetailApi = (id) => del(`/customer-call-record/${id}`);
// 拜访统计
export const visitStatApi = (data) =>
  post("/customer-call-record/statistics", data);

// ========================================  毛机维修 =======================================
// 毛机维修列表
export const machineRepairListApi = (data) =>
  post("/machine-repair/page", data);
// 获取机器维修详情
export const machineRepairDetailApi = (id) => get(`/machine-repair/${id}`);
// 确认维修报告
export const machineRepairConfirmApi = (data) =>
  put(`/machine-repair/audit`, data);
// 导出毛机维修记录
export const machineRepairExportApi = (data) =>
  postDown("/machine-repair/export", data);

// ========================================  拆机记录 =======================================
// 列表
export const machineSplitListApi = (data) =>
  post("/machine-disassemble/page", data);
// 详情
export const machineSplitDetailApi = (id) => get(`/machine-disassemble/${id}`);
// 根据单号查详情
export const machineSplitDetailByCodeApi = (code) =>
  get(`/machine-disassemble/getByCode/${code}`);
// 审核
export const machineSplitAuditApi = (data) => put("/machine-disassemble", data);

//审核换机单
export const passInstallOrderApi = (data) =>
  put("/install-order/auditExchange", data);
//换机确认收货
export const receiveInstallOrderApi = (code) =>
  put("/install-order/receive/" + code);

// ========================================  翻新组件 =======================================
// 列表
export const componentsRepairListApi = (data) =>
  post("/component-repair/page", data);
// 维修明细
export const componentsRepairDetailApi = (id) => get(`/component-repair/${id}`);
// 根据单号查维修明细
export const componentsRepairDetailByCodeApi = (code) =>
  get(`/component-repair/getByCode/${code}`);
// 审核
export const componentsRepairAuditApi = (data) =>
  put("/component-repair/audit", data);

// ========================================  工单管理 =======================================
// 获取工程师位置信息
export const getEngineerLocationApi = (data) =>
  get("/engineer-info/getEngineerLocation", data);

// ========================================  工程师月度统计 =======================================
// 分页查询
export const engineerMonthStatApi = (data) =>
  post("/statistics/queryEngineerMonthStatistics", data);
// 月份统计明细
export const engineerMonthStatDetailApi = (data) =>
  post("/statistics/queryEngineerMonthGrossProfitList", data);
// 汇总数据
export const engineerMonthStatSummaryApi = (data) =>
  post("/statistics/queryEngineerMonthGrossProfit", data);

// ========================================  工程师时效统计 =======================================
// 分页查询
export const engineerTimeStatApi = (data) =>
  post("/statistics/queryEngineerMonthTimelinessList", data);
// 明细
export const engineerTimeStatDetailApi = (data) =>
  post("/statistics/queryEngineerMonthTimelinessDetailsList", data);

// ========================================  工程师毛利统计 =======================================
// 分页查询
export const engineerGrossProfitApi = (data) =>
  post("/statistics/queryEngineerMonthGross", data);
// 服务客户列表
export const engineerGrossProfitDetailApi = (data) =>
  post("/statistics/queryEngineerMonthGrossProfitCustomerList", data);
// 汇总数据
export const engineerGrossProfitSummaryApi = (data) =>
  post("/statistics/queryEngineerMonthGrossProfitCustomer", data);
