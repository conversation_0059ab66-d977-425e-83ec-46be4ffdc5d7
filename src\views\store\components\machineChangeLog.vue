<!--
 * @Author: wskg
 * @Date: 2025-03-22 16:44:54
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-08 11:16:49
 * @Description: 机器出入库流水
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :show-search="false"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      :height="350"
      @loadData="loadData"
    ></ProTable>
  </div>
</template>

<script>
import { getMachineRecordApi } from "@/api/store";

export default {
  name: "MachineChangeLog",
  props: {
    machineNum: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "flowCode",
          title: "出入库单号",
          isTable: true,
        },
        {
          dataIndex: "sourceCode",
          title: "关联单号",
          isTable: true,
        },
        {
          dataIndex: "sourceType",
          title: "出入库类型",
          isTable: true,
          formatter: (row) => row.sourceType?.label,
        },
        // {
        //   dataIndex: "inOutType",
        //   title: "类型",
        //   isTable: true,
        //   formatter: (row) => row.sourceType?.label,
        // },
        {
          dataIndex: "time",
          title: "出入库时间",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "价格",
          isTable: true,
        },
      ],
      tableData: [],
    };
  },

  mounted() {
    if (this.machineNum) {
      this.$refs.ProTable.refresh();
    } else {
      this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
    }
  },
  methods: {
    loadData(parameter) {
      const requestParameters = {
        ...parameter,
        machineNum: this.machineNum,
      };
      getMachineRecordApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
  },
};
</script>

<style scoped lang="scss"></style>
