<!--
 * @Author: wskg
 * @Date: 2025-01-17 17:30:48
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:48
 * @Description: 全/半保合约
 -->
<template>
  <div class="app-container">
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="65%"
      :confirm-button-disabled="confirmLoading"
      :method-type="methodType"
      :no-footer="methodType === 'info'"
      confirm-text="确认提交"
      @ok="handleSubmit"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="formSubmit"
      >
        <template #isSupplement>
          <el-radio-group v-model="form.isSupplement">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </template>
        <template #deviceGroupId>
          <el-select
            v-model="form.deviceGroupId"
            style="width: 100%"
            placeholder="请选择设备组名称"
            :disabled="methodType !== 'add'"
          >
            <el-option
              v-for="item in deviceGroupOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <template #signId>
          <el-select
            v-model="form.signId"
            style="width: 100%"
            :disabled="methodType === 'info'"
            filterable
          >
            <el-option
              v-for="item in userList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <template #agentId>
          <el-select
            v-model="form.agentId"
            style="width: 100%"
            :disabled="true"
            filterable
          >
            <el-option
              v-for="item in userList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <!-- 合约附件 -->
        <template #attachments>
          <div>
            <PDFFiles
              :file-list="form.attachments"
              :limit="5"
              :disabled="methodType === 'info'"
              is-auth
              permit-code="@ums:manage:contract:download"
              @uploadSuccess="handleLogFileUploadSuccess"
            />
          </div>
        </template>
        <!-- 签约机器信息 -->
        <template #signMachineInfo>
          <ProTable
            ref="ProTable"
            :show-search="false"
            :show-pagination="false"
            :show-setting="false"
            :show-loading="false"
            :columns="columns"
            :data="tableData"
          >
            <template #btn>
              <el-button
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="handleDispatch"
              >
                选择签约设备组
              </el-button>
            </template>
            <template #action="{ row, index }">
              <div class="fixed-width">
                <!--&& row.hostType === '2008'-->
                <!--  v-if="row.hostType === '2008'"-->
                <el-button
                  icon="el-icon-view"
                  @click="handleViewOrEdit(row, 'info', index)"
                >
                  查看
                </el-button>
                <el-button
                  v-if="methodType !== 'info'"
                  icon="el-icon-edit"
                  @click="handleViewOrEdit(row, 'edit', index)"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="methodType !== 'info'"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDelete(row)"
                >
                  移除
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 机器合约信息 -->
    <FullHalfContractInfo
      ref="buyMachineInfo"
      :contract-type="contractType"
      @confirmContractInfo="confirmContractInfo"
    />
    <!-- 选择出库机器 -->
    <ChooseDeviceGroup
      :dialog-visible.sync="dispatchDialogVisible"
      :customer-id="form.customerId"
      :selected-data="tableData"
      @confirmDispatch="confirmDispatch"
    />
  </div>
</template>

<script>
import PDFFiles from "@/components/ProUpload/pdfFiles.vue";
import FullHalfContractInfo from "@/views/custom/editCustom/components/contract/fullHalfContractInfo.vue";
import ChooseDeviceGroup from "@/views/custom/editCustom/components/contract/chooseDeviceGroup.vue";
import { cloneDeep } from "lodash";
import { addCustomerContractApi, getCustomerUserListApi } from "@/api/customer";
import { transformFormParams } from "@/utils";

export default {
  name: "InsureContract",
  components: { FullHalfContractInfo, PDFFiles, ChooseDeviceGroup },
  props: {
    contractType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      drawerVisible: false,
      drawerTitle: "",
      confirmLoading: false,
      methodType: "add", // form类型 add/edit/info
      form: {
        isSupplement: true,
      },
      formColumns: [
        {
          dataIndex: "isSupplement",
          title: "是否为补录合同",
          isForm: true,
          formSpan: 8,
          formSlot: "isSupplement",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "code",
          title: "合同编号",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入合同编号",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入合同名称",
              trigger: "change",
            },
          ],
        },

        // {
        //   dataIndex: "addToDeviceGroup",
        //   title: "添加机器信息",
        //   isForm: true,
        //   formSlot: "addToDeviceGroup",
        //   formSpan: 8,
        // },
        {
          dataIndex: "remark",
          title: "摘要",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
        },
        // {
        //   dataIndex: "contractType",
        //   title: "合约类型",
        //   isForm: true,
        //   valueType: "select",
        //   option: [
        //     {
        //       label: "半保",
        //       value: "1202",
        //     },
        //     {
        //       label: "全保",
        //       value: "1230",
        //     },
        //   ],
        //   formSpan: 8,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择合约类型",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "signTime",
          title: "签约时间",
          isForm: true,
          formSpan: 8,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          prop: [
            {
              required: true,
              message: "请选择签约时间",
              trigger: "change",
            },
          ],
        },
        // {
        //   dataIndex: "startTime",
        //   title: "合约开始时间",
        //   isForm: true,
        //   formSpan: 8,
        //   valueType: "date-picker",
        //   pickerType: "date",
        //   pickerFormat: "yyyy-MM-dd",
        //   valueFormat: "yyyy-MM-dd",
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择合约开始时间",
        //       trigger: "change",
        //     },
        //   ],
        // },
        // {
        //   dataIndex: "endTime",
        //   title: "合约截止时间",
        //   isForm: true,
        //   formSpan: 8,
        //   valueType: "date-picker",
        //   pickerType: "date",
        //   pickerFormat: "yyyy-MM-dd",
        //   valueFormat: "yyyy-MM-dd",
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择合约截止时间",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "signId",
          title: "销售",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          formSlot: "signId",
          option: [],
        },
        {
          dataIndex: "agentId",
          title: "商务",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          formSlot: "agentId",
          option: [],
        },
        {
          dataIndex: "attachments",
          title: "合约附件",
          isForm: true,
          formSpan: 24,
          formSlot: "attachments",
        },
        {
          dataIndex: "signMachineInfo",
          title: "签约机器信息",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "signMachineInfo",
        },
      ],
      deviceGroupOptions: [],
      userList: [],
      ladderList: [],
      // 签约机器信息
      columns: [
        {
          title: "设备组编号",
          dataIndex: "deviceSeqId",
          isTable: true,
          width: 150,
        },
        {
          title: "设备组名称",
          dataIndex: "deviceGroup",
          isTable: true,
          formatter: (row) => row.deviceGroup?.label,
        },
        {
          title: "品牌/机型",
          dataIndex: "productInfo",
          isTable: true,
        },
        // {
        //   dataIndex: "settleStatus",
        //   title: "结算状态",
        //   isTable: true,
        //   formatter: (row) => {
        //     if (row.settleStatus?.label) {
        //       return row.settleStatus?.label;
        //     } else {
        //       switch (row.settleStatus) {
        //         case "0":
        //           return "未结算";
        //         case "1":
        //           return "已结算";
        //         default:
        //           return "";
        //       }
        //     }
        //   },
        // },
        // {
        //   dataIndex: "settleMethod",
        //   title: "结算方式",
        //   isTable: true,
        //   formatter: (row) => {
        //     if (row.settleMethod?.label) {
        //       return row.settleMethod?.label;
        //     } else {
        //       switch (row.settleMethod) {
        //         case "FULL":
        //           return "全款";
        //         case "INSTALLMENT":
        //           return "分期付款";
        //         default:
        //           return "";
        //       }
        //     }
        //   },
        // },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => {
            if (row.serType?.label) {
              return row.serType?.label;
            } else {
              switch (row.serType) {
                case "HALF":
                  return "普通半保";
                case "ALL":
                  return "普通全保";
                case "PACKAGE_HALF":
                  return "包量半保";
                case "PACKAGE_ALL":
                  return "包量全保";
                case "OTHER":
                  return "其它";
                default:
                  return "";
              }
            }
          },
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 200,
        },
      ],
      tableData: [],
      editType: "edit", // 列表合同明细类型 edit/info
      dispatchDialogVisible: false,
      currentRow: null,
      currentIndex: 0,
    };
  },
  watch: {
    "form.isSupplement": {
      handler(val) {
        if (val) {
          this.formColumns.forEach((item) => {
            if (item.dataIndex === "code") {
              item.isForm = true;
            }
          });
        } else {
          this.formColumns.forEach((item) => {
            if (item.dataIndex === "code") {
              item.isForm = false;
            }
          });
        }
        if (!this.form.isSupplement && this.methodType === "info") {
          this.formColumns.forEach((item) => {
            if (item.dataIndex === "code") {
              item.isForm = true;
            }
          });
        }
      },
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    visible(val, type) {
      this.tableData = [];
      this.userList = [];
      this.methodType = type;
      this.drawerTitle = this.titleMap(type);
      this.form = cloneDeep(val);
      if (
        this.form.isSupplement === undefined ||
        this.form.isSupplement === null
      ) {
        this.$set(this.form, "isSupplement", true);
      }
      if (type === "add") {
        this.$set(
          this.form,
          "agentId",
          JSON.parse(localStorage.getItem("userInfo")).id
        );
      }
      if (
        this.form.customerContractItems &&
        this.form.customerContractItems.length >= 1
      ) {
        this.tableData = this.form.customerContractItems.map((item) => {
          return transformFormParams(item, ["deviceGroup"]);
        });
      }
      this.operatList();
      this.drawerVisible = true;
    },
    handleSubmit() {
      this.$refs.ProForm.handleSubmit();
    },
    async formSubmit(val) {
      try {
        if (!this.tableData.length) {
          this.$message.warning("请选择签约设备组");
          return;
        }
        this.confirmLoading = true;
        const args = {
          ...val,
          customerContractItems: this.tableData,
          contractType: "1202",
        };
        if (!args.isSupplement && this.methodType === "add") {
          delete args.code;
        }
        const result = await addCustomerContractApi(args);
        if (result.code === 200) {
          this.$message.success("操作成功");
          this.$emit("refresh");
          this.drawerVisible = false;
        }
      } finally {
        this.confirmLoading = false;
      }
    },
    // 列表明细
    handleViewOrEdit(row, type, index) {
      this.currentRow = cloneDeep(row);
      this.currentIndex = index;
      this.$refs.buyMachineInfo.visible(this.currentRow, type);
    },
    // 移除
    handleDelete(row) {
      this.tableData = this.tableData.filter(
        (item) => item.machineNum !== row.machineNum
      );
    },
    // 确认机器合约信息
    confirmContractInfo(info) {
      console.log(info, "info");
      if (this.currentRow.machineNum) {
        const index = this.tableData.findIndex(
          (item) => item.machineNum === this.currentRow.machineNum
        );
        console.log(index, "this.index");
        if (index !== -1) {
          this.$set(this.tableData, index, cloneDeep(info));
        }
      } else {
        console.log(this.currentIndex, "this.currentIndex");
        this.$set(this.tableData, this.currentIndex, cloneDeep(info));
      }
      // const index = this.tableData.findIndex(
      //   (item) => item.machineNum === this.currentRow.machineNum
      // );
      // if (index !== -1) {
      //   this.$set(this.tableData, index, cloneDeep(info));
      // }
    },
    handleLogFileUploadSuccess(result) {
      if (!this.form.attachments) {
        this.$set(this.form, "attachments", []);
      }
      this.form.attachments.push(result);
    },
    handleDispatch() {
      this.dispatchDialogVisible = true;
    },
    confirmDispatch(row) {
      this.tableData = cloneDeep(row);
      this.dispatchDialogVisible = false;
    },
    // 员工列表处理
    operatList() {
      this.userList = [];
      getCustomerUserListApi().then((res) => {
        res.data.map((item) => {
          this.userList.push({
            value: item.id,
            label: item.name,
          });
        });
      });
    },
    titleMap(type) {
      const data = {
        add: "新增抄表合约",
        edit: "编辑抄表合约",
        info: "查看抄表合约",
      };
      return data[type];
    },
    closeDrawer() {
      this.drawerVisible = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
