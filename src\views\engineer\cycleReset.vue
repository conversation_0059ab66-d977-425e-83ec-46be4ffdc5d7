<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 11:21:24
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <PM />
    <!--<el-tabs v-model="activeName">-->
    <!--  <el-tab-pane label="PM周期" name="PM周期">-->
    <!--    <PM />-->
    <!--  </el-tab-pane>-->
    <!--  <el-tab-pane label="保养周期" name="保养周期">保养周期</el-tab-pane>-->
    <!--</el-tabs>-->
  </div>
</template>

<script>
import PM from "@/views/engineer/components/PM.vue";
export default {
  name: "CycleReset",
  components: { PM },
  data() {
    return {
      activeName: "PM周期",
    };
  },
};
</script>

<style scoped lang="scss"></style>
