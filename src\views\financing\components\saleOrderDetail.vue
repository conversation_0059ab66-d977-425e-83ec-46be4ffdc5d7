<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-10-10 15:32:55
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-30 16:10:05
 * @FilePath: src/views/financing/components/saleOrderDetail.vue
 * @Description: 销售订单详情
 * 
-->
<template>
  <div class="container">
    <ProDrawer
      :value="dialogVisible"
      :title="title"
      size="85%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      class="drawer_b"
      @cancel="handleClose"
    >
      <el-steps :active="active" finish-status="success" class="steps-box">
        <el-step title="买家下单"></el-step>
        <el-step title="买家付款"></el-step>
        <el-step title="发货"></el-step>
        <el-step title="买家确认收货"></el-step>
      </el-steps>
      <!-- 买家下单 -->
      <div v-if="orderInfo" ref="dialogContent" class="order-fix">
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="订单信息"
            :column="3"
            border
          >
            <el-descriptions-item label="订单状态">
              {{ getOrderStatusText(orderInfo?.tradeOrder?.orderStatus) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单编号">
              {{ orderInfo.tradeOrder?.orderNum }}
            </el-descriptions-item>
            <el-descriptions-item label="支付金额（元）">
              {{ orderInfo.tradeOrder?.actualAmount }}
            </el-descriptions-item>
            <el-descriptions-item label="关联客户">
              {{ orderInfo.companyName }}
            </el-descriptions-item>
            <el-descriptions-item label="下单用户">
              {{ orderInfo.buyerName }}
            </el-descriptions-item>
            <el-descriptions-item label="客户等级">
              {{ orderInfo?.tradeOrder?.customerLevel?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="下单手机号">
              {{ orderInfo?.tradeOrder?.consigneePhone }}
            </el-descriptions-item>
            <el-descriptions-item label="支付方式"> 微信 </el-descriptions-item>
            <el-descriptions-item label="配送方式">
              {{ orderInfo?.tradeOrder?.logisticsProvider?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="收货地址">
              {{ orderInfo?.tradeOrder?.consigneeFullAddress }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 商品信息 -->
        <div class="m-t-8">
          <p class="tit-box m-b-12">商品信息</p>
          <ProTable
            ref="ProSPXXTable"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="orderColumns"
            :show-pagination="false"
            :show-loading="false"
            :data="orderInfo.tradeOrder?.tradeOrderDetailList || []"
            :show-setting="false"
            :show-search="false"
            :show-table-operator="false"
            sticky
            :height="300"
          >
            <template #storageArticle="{ row }">
              {{ row.storageArticle?.minUnit }}
            </template>
          </ProTable>
          <div class="text-content">
            <p class="text-p">
              <label class="p-label">订单商品金额（元）：</label>
              <span class="p-content">{{
                orderInfo.tradeOrder?.actualGoodsAmount
              }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">差异金额（元）：</label>
              <span class="p-content">
                {{
                  orderInfo.tradeOrder.discountAmount
                    ? mulAmount(
                        orderInfo.tradeOrder.discountAmount,
                        -1
                      ).toFixed(2)
                    : "0.00"
                }}
              </span>
            </p>
            <p class="text-p">
              <label class="p-label">订单运费（元）：</label>
              <span class="p-content">{{
                orderInfo.tradeOrder?.shippingFee
              }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">实收款（元）：</label>
              <span class="p-content">{{
                orderInfo.tradeOrder?.actualAmount
              }}</span>
            </p>
            <p v-if="orderInfo.tradeOrder?.buyerRemark" class="text-p m-b-8">
              <label class="p-label">订单备注：</label>
              <span class="p-content">{{
                orderInfo.tradeOrder?.buyerRemark
              }}</span>
            </p>
          </div>
        </div>

        <!-- 交易明细 -->
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="交易明细"
            :column="3"
            border
          >
            <el-descriptions-item label="订单来源">
              {{ getOrderSourceText(orderInfo.tradeOrder?.orderSource) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单创建时间">
              {{ orderInfo.tradeOrder?.createdAt }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderInfo.tradeOrder?.payTime"
              label="订单支付时间"
            >
              {{ orderInfo.tradeOrder?.payTime }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderInfo.deliveryTime"
              label="订单发货时间"
            >
              {{ orderInfo.deliveryTime }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderInfo.tradeOrder?.finishTime"
              label="订单确认收货时间"
            >
              {{ orderInfo.tradeOrder?.finishTime }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 物流信息 -->
        <div
          v-if="tradeInfo?.length > 0"
          class="m-t-8 box-box"
          style="clear: both"
        >
          <p class="tit-box m-b-12">物流信息</p>
          <div style="overflow: hidden">
            <div
              v-for="(item, index) in tradeInfo"
              :key="index"
              style="float: left; margin-right: 20px"
            >
              <div
                :class="
                  tradeInfoDetail.waybillNumber == item.waybillNumber
                    ? 'trade active'
                    : 'trade'
                "
              >
                <text class="iconfont iconbaoguo"></text>
                <div class="info">
                  <div>{{ item.packageName }}</div>
                  <div>共{{ item.expectedNumber }}件</div>
                </div>
              </div>
            </div>
          </div>
          <div class="tradedetail">
            <div>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'jdl'"
                class="iconfont iconsr_jingdong"
                style="color: red; font-size: 50rpx"
              ></text>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'iss'"
                class="iconfont iconshansonghuise"
                style="color: #ee822f; font-size: 50rpx"
              ></text>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'self'"
                class="iconfont iconziti"
                style="color: #ee822f; font-size: 50rpx"
              ></text>

              <text style="margin-left: 20rpx">
                {{ tradeInfoDetail?.logisticsProvider?.label }}:{{
                  tradeInfoDetail?.waybillNumber
                }}
              </text>
            </div>
            <div style="margin-top: 30px">
              <el-timeline>
                <el-timeline-item
                  v-for="item in tradeInfoDetail.traces"
                  :key="item.id"
                  :timestamp="item.providerStatus + '    ' + item.operatedAt"
                >
                  {{ item.operationRemark }}
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </div>
      </div>
    </ProDrawer>
  </div>
</template>

<script>
import { operatorTradeOrderDetailApi, OrderTracesApi } from "@/api/operator";
import { mulAmount } from "@/utils";
export default {
  name: "SaleOrderDetail",
  props: {
    title: {
      type: String,
      default: "订单详情",
    },
  },
  data() {
    return {
      active: 0,
      dialogVisible: false,
      confirmLoading: false,
      orderInfo: {},
      tradeInfo: {},
      tradeInfoDetail: {},
      orderColumns: [
        {
          dataIndex: "itemName",
          isTable: true,
          title: "商品名称",
        },
        {
          dataIndex: "itemId",
          isTable: true,
          title: "商品编号",
        },
        {
          dataIndex: "code",
          isTable: true,
          title: "物品编号",
          formatter: (row) => row.storageArticle?.code,
        },
        {
          dataIndex: "name",
          isTable: true,
          title: "物品名称",
          formatter: (row) => row.storageArticle?.name,
        },
        {
          dataIndex: "numberOem",
          isTable: true,
          title: "OEM编号",
          formatter: (row) => row.storageArticle?.numberOem,
        },
        {
          dataIndex: "manufacturerChannel",
          isTable: true,
          title: "制造渠道",
          formatter: (row) => row.storageArticle?.manufacturerChannel.label,
        },
        {
          dataIndex: "saleUnitPrice",
          isTable: true,
          title: "商品单价（元）",
        },

        {
          dataIndex: "itemNum",
          isTable: true,
          title: "购买数量",
        },
        {
          dataIndex: "storageArticle",
          isTable: true,
          title: "单位",
          tableSlot: "storageArticle",
        },
        {
          dataIndex: "discountAmount",
          isTable: true,
          title: "差异金额（元）",
          // formatter: (row) => "-" + row.discountAmount,
          formatter: (row) =>
            row.discountAmount
              ? mulAmount(row.discountAmount, -1).toFixed(2)
              : "0.00",
        },
        {
          dataIndex: "actualPayAmount",
          isTable: true,
          title: "小计（元）",
        },
      ],
    };
  },
  methods: {
    mulAmount,

    show(tradeOrderNumber) {
      this.orderInfo = {};
      this.tradeInfo = {};
      this.tradeInfoDetail = {};
      this.confirmLoading = true;
      operatorTradeOrderDetailApi(tradeOrderNumber)
        .then((res) => {
          this.orderInfo = res.data;
        })
        .finally((_) => {
          this.confirmLoading = false;
        });

      OrderTracesApi(tradeOrderNumber).then((res) => {
        this.tradeInfo = res.data;
        this.tradeInfoDetail = this.tradeInfo[0];
      });
      this.$nextTick(() => {
        this.dialogVisible = true;
      });
    },
    getOrderStatusText(orderStatus) {
      let value = "";
      switch (orderStatus) {
        case "CLOSED":
          value = "订单关闭";
          this.active = null;
          break;
        case "PAID":
          value = "已支付";
          this.active = 2;
          break;
        case "SUCCESS":
          value = "交易成功";
          this.active = 4;
          break;
        case "WAIT_DELIVER":
          value = "待发货";
          this.active = 2;
          break;
        case "WAIT_PAY":
          value = "待支付";
          this.active = 1;
          break;
        case "WAIT_RECEIVE":
          value = "待收货";
          this.active = 3;
          break;
      }
      return value;
    },
    getOrderSourceText(orderStatus) {
      let value = "";
      switch (orderStatus) {
        case "ITEM":
          value = "商品直接下单";
          break;
        case "CART":
          value = "购物车下单";
          break;
        case "SELLER_CREATE":
          value = "卖家代客下单";
          break;
      }
      return value;
    },
    handleClose() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style scoped lang="scss">
.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
.text-content {
  display: flex;
  justify-content: space-between;
  .text-p {
    //&.right {
    //  position: relative;
    //  left: 85%;
    //  top: 0;
    //}

    color: #606266;

    .p-label {
      color: #606266;
      font-weight: 700;
    }

    //margin-top: 15px;
    margin: 30px 0;
  }
}
.steps-box {
  position: relative;
  width: 80%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  z-index: 2;
}
.order-border-box {
  border: dashed 1px #ccc;
  padding: 10px;
}
</style>
