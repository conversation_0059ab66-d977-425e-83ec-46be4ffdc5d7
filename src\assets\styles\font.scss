@font-face {
  font-family: DINOT;
  src: url(@/assets/fonts/DINOT.OTF) format('truetype');
}

@font-face {
  font-family: DIN-BLACK;
  src: url(@/assets/fonts/DIN-BLACK.OTF) format('truetype');
}

@font-face {
  font-family: DIN-BOLD;
  src: url(@/assets/fonts/DIN-BOLD.OTF) format('truetype');
}

@font-face {
  font-family: DIN-LIGHT;
  src: url(@/assets/fonts/DIN-LIGHT.OTF) format('truetype');
}

@font-face {
  font-family: DIN-MEDIUM;
  src: url(@/assets/fonts/DIN-MEDIUM.OTF) format('truetype');
}

@font-face {
  font-family: PingFangMedium;
  src: url(@/assets/fonts/PINGFANG_MEDIUM.TTF) format('truetype');
}

@font-face {
  font-family: PingFang SC;
  src: url('@/assets/fonts/方正正中黑简体.TTF');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: DFPZongYiW7-GB;
  src: url(@/assets/fonts/DFPZongYiW7-GB.TTF) format('truetype');
}

@font-face {
  font-family: ShiShangZhongHeiJianTi;
  src: url(@/assets/fonts/ShiShangZhongHeiJianTi.TTF) format('truetype');
}

@font-face {
  font-family: YouSheBiaoTiHei;
  src: url(@/assets/fonts/YouSheBiaoTiHei.ttf) format('truetype');
}

@font-face {
  font-family: MFLiHeiNoncommercialRegular;
  src: url(@/assets/fonts/MFLiHei_Noncommercial-Regular.otf) format('truetype');
}