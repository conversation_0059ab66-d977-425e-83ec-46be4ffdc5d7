<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-28 18:03:32
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-29 16:24:46
 * @Description: 财务 - 月度收入
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-rule
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #rule>
        <div class="rules-tips">
          <h3 class="rule-title">月度收入统计规则</h3>
          <div class="rule-item">
            <span class="rule-number">统计周期：</span>
            <span class="rule-text">
              例如统计 1 月数据，即 1月1日 00:00:00 至 1月31日 23:59:59
            </span>
          </div>
          <ol>
            <li>
              <div class="rule-item">
                <span class="rule-number">抄表收入：</span>
                <span class="rule-text">抄表对账中的应收金额之和</span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">散修人工收入：</span>
                <span class="rule-text">工单中的人工费之和</span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">机器销售收入：</span>
                <span class="rule-text">合约查询中的机器销售金额之和</span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">碳粉销售收入：</span>
                <span class="rule-text">
                  耗材销售中的碳粉销售金额之和 + 工单中的碳粉销售金额之和
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">零件销售收入：</span>
                <span class="rule-text">
                  耗材销售中的零件销售金额之和 + 工单中的零件销售金额之和
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">其他收入：</span>
                <span class="rule-text">其他收入中的总金额之和</span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">总收入：</span>
                <span class="rule-text">以上所有收入之和</span>
              </div>
            </li>
          </ol>
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      :no-footer="true"
      size="75%"
      @cancel="drawerVisible = false"
    >
      <ProTable
        ref="detailTable"
        :query-param="detailQueryParam"
        :columns="detailColumns"
        :data="detailTableData"
        @loadData="LoadDetailData"
      ></ProTable>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { monthIncomePageApi } from "@/api/operator";

export default {
  name: "MonthlyRevenue",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "monthly",
          title: "年月",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          valueFormat: "yyyy-MM",
          pickerFormat: "yyyy-MM",
          width: 100,
        },
        {
          dataIndex: "actualReceipt",
          title: "抄表实收",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "repairPay",
          title: "散修人工收入",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "mechanicalPlaceOrderIncomeAmount",
          title: "机器销售收入",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "tonerIncome",
          title: "碳粉销售收入",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "costIncome",
          title: "零件销售收入",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "otherAmount",
          title: "其他收入",
          isTable: true,
          isSearch: true,

          valueType: "inputRange",
        },
        {
          dataIndex: "saleTotalAmount",
          title: "总收入",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   tableSlot: "action",
        //   width: 120,
        // },
      ],
      tableData: [
        {
          monthly: "2025-05",
          actualReceipt: 100,
          repairPay: 100,
          machineSale: 100,
          carbon: 100,
          part: 100,
          totalAmount: 100,
        },
      ],
      editType: "info",
      drawerVisible: false,
      drawerTitle: "",
      // 明细
      detailQueryParam: {},
      detailColumns: [
        {
          dataIndex: "type",
          title: "收入类型",
          isTable: true,
          formatter: (row) => row.type?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "耗材",
              value: "consumables",
            },
            {
              label: "机器",
              value: "machine",
            },
            {
              label: "抄表",
              value: "meter",
            },
            {
              label: "维修",
              value: "repair",
            },
          ],
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "remark",
          title: "摘要",
          isTable: true,
        },
        {
          dataIndex: "createdAt",
          title: "订单时间",
          isTable: true,
        },
      ],
      detailTableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startMonthly: null,
          endMonthly: null,
          data: parameter.monthly,
        },
        {
          startActualReceipt: null,
          endActualReceipt: null,
          data: parameter.actualReceipt,
        },
        {
          startRepairPay: null,
          endRepairPay: null,
          data: parameter.repairPay,
        },
        {
          startMechanicalSaleAmount: null,
          endMechanicalSaleAmount: null,
          data: parameter.mechanicalPlaceOrderIncomeAmount,
        },
        {
          startTonerSaleAmount: null,
          endTonerSaleAmount: null,
          data: parameter.tonerIncome,
        },
        {
          startCostSaleAmount: null,
          endCostSaleAmount: null,
          data: parameter.costIncome,
        },
        {
          startOtherAmount: null,
          endOtherAmount: null,
          data: parameter.otherAmount,
        },
        {
          startTotalSaleAmount: null,
          endTotalSaleAmount: null,
          data: parameter.saleTotalAmount,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      [
        "monthly",
        "actualReceipt",
        "repairPay",
        "mechanicalPlaceOrderIncomeAmount",
        "tonerIncome",
        "costIncome",
        "otherIncome",
        "saleTotalAmount",
      ].forEach((key) => delete requestParameters[key]);
      monthIncomePageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    LoadDetailData(parameter) {
      this.detailQueryParam = filterParam(
        Object.assign({}, this.detailQueryParam, parameter)
      );
      const requestParameters = cloneDeep(this.detailQueryParam);
      this.$refs.detailTable && (this.$refs.detailTable.listLoading = false);
    },
    handleEdit(row, type) {
      this.editType = type;
      this.drawerVisible = true;
      this.drawerTitle = `${row.monthly} - 详情`;
      this.$nextTick(() => {
        this.$refs.detailTable.refresh();
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
