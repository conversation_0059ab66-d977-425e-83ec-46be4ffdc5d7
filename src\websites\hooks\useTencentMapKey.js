/**
 * 安全的腾讯地图Key获取Hook
 * 从后端获取密文，在前端解密使用，避免明文暴露在开发者工具中
 */

import { ref, onMounted } from 'vue'
import { websiteConfigApi } from '@/websites/api/website'
import { decryptConfigValue } from '@/websites/untils/encryption'

/**
 * 获取腾讯地图Key的Hook
 * 从后端获取加密的腾讯地图Key，在前端解密后使用
 */
export function useTencentMapKey() {
  const mapKey = ref('')
  const loading = ref(true)
  const error = ref(null)

  // 获取腾讯地图Key - 与React项目完全一致的实现
  const fetchMapKey = async () => {
    try {
      loading.value = true
      error.value = null

      // 从安全接口获取密文 - 与React项目API完全一致
      const encryptedKey = await websiteConfigApi.getSensitiveConfigEncrypted('tencentMapKey')

      if (encryptedKey) {
        // 在前端解密 - 与React项目逻辑完全一致
        const decryptedKey = decryptConfigValue(encryptedKey)
        mapKey.value = decryptedKey
      } else {
        // 未能获取到腾讯地图Key密文
        mapKey.value = ''
      }
    } catch (err) {
      error.value = err.message || '获取地图Key失败'
      mapKey.value = ''
    } finally {
      loading.value = false
    }
  }

  // 重新获取腾讯地图Key - 与React项目完全一致的实现
  const refetch = () => {
    const fetchMapKey = async () => {
      try {
        loading.value = true
        error.value = null

        const encryptedKey = await websiteConfigApi.getSensitiveConfigEncrypted('tencentMapKey')

        if (encryptedKey) {
          const decryptedKey = decryptConfigValue(encryptedKey)
          mapKey.value = decryptedKey
        } else {
          mapKey.value = ''
        }
      } catch (err) {
        error.value = err.message || '获取地图Key失败'
        mapKey.value = ''
      } finally {
        loading.value = false
      }
    }

    fetchMapKey()
  }

  // 组件挂载时自动获取
  onMounted(() => {
    fetchMapKey()
  })

  return {
    mapKey,
    loading,
    error,
    refetch
  }
}

/**
 * 兼容React项目的Hook导出方式
 */
export default useTencentMapKey
