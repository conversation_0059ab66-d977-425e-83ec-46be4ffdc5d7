<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-29 12:02:17
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-14 15:13:37
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <!--style="font-size: 26px; color: #ddd; line-height: 200px"-->
    <!--欢 迎 进 入 四 川 至 简 智 印 业 务 管 理 平 台-->
    <big-data-view />
  </div>
</template>
<script>
import BigDataView from "@/views/home/<USER>/bigDataView.vue";
export default {
  name: "Home",
  components: { BigDataView },
  data() {
    return {};
  },
  mounted() {},
  beforeDestroy() {},
  methods: {},
};
</script>
