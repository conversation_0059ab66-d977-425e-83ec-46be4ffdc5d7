{"name": "benyin-website-frontend", "version": "1.0.0", "description": "复印机维修服务企业官网前端项目", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@tanstack/react-query": "^5.14.2", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "ahooks": "^3.7.8", "antd": "^5.12.8", "antd-mobile": "^5.34.0", "axios": "^1.6.2", "classnames": "^2.3.2", "cos-js-sdk-v5": "^1.4.20", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "js-cookie": "^3.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.4", "react-router-dom": "^6.8.1", "styled-components": "^6.1.19", "tailwindcss": "^3.3.6", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "^3.1.1", "typescript": "^5.2.2", "vite": "^5.0.8"}}