<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-04 09:26:14
 * @Description: 商品规格
 -->

<template>
  <div class="app-container">
    <div style="width: 50%">
      <ProForm
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
      </ProForm>
      <div v-if="methodType != 'info'" style="text-align: left">
        <div>
          <div
            v-for="(item, index) in tagList"
            :key="index"
            style="padding: 20px; border: 1px solid #f5f5f5; margin: 20px"
          >
            <div style="margin-bottom: 20px">
              <el-input
                v-model="item.label"
                style="width: 200px"
                placeholder="请输入内容"
              ></el-input
              ><i class="el-icon-error" @click="delTag(index)"></i>
            </div>
            <div>
              <el-tag
                v-for="(tag, ind) in item.children"
                :key="tag.value"
                closable
                :disable-transitions="false"
                @close="handleClose(index, ind)"
              >
                {{ tag.value }}
              </el-tag>
              <el-input
                v-if="item.inputVisible"
                ref="saveTagInput"
                v-model="item.inputValue"
                class="input-new-tag"
                size="small"
                @keyup.enter.native="handleInputConfirm(index)"
                @blur="handleInputConfirm(index)"
              >
              </el-input>
              <el-button
                v-else
                class="button-new-tag"
                size="small"
                @click="showInput(index)"
                >+ 标签</el-button
              >
            </div>
          </div>
        </div>
        <div style="padding: 20px; margin-left: 140px">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-circle-plus"
            @click="addTag"
          >
            添加新规格
          </el-button>
          <el-button
            type="success"
            size="mini"
            icon="el-icon-circle-plus"
            @click="create"
          >
            立即生成
          </el-button>
        </div>
      </div>
    </div>
    <div v-if="methodType != 'info'" class="tit-box" style="width: 100%">
      商品属性
    </div>
    <DataTable
      ref="ProTable"
      :columns="columns"
      :show-setting="false"
      :show-pagination="false"
      :show-search="false"
      row-key="index"
      :data="tableData"
      sticky
      :height="350"
      style="width: 100%"
      :show-table-operator="false"
    >
      <template #skuPicUrl="slotProps">
        <div style="height: 120px; width: 150px; overflow: hidden">
          <ProUpload
            :file-list="slotProps.row.skuPicUrl"
            :type="methodType"
            :limit="1"
            @uploadSuccess="
              (res) => handleUploadSuccess(res, slotProps.row.skuPicUrl)
            "
            @uploadRemove="
              (res) => handleUploadRemove(res, slotProps.row.skuPicUrl)
            "
          />
        </div>
      </template>
      <template #invSkuId="slotProps">
        {{ slotProps.row.invSkuName }}
        <!-- <span v-if="slotProps.row.invSkuOem">（{{ slotProps.row.invSkuOem }}）</span>-->
        <el-button
          v-if="methodType != 'info'"
          type="primary"
          plain
          size="mini"
          @click="showOemDialog(slotProps.index)"
        >
          选择
        </el-button>
      </template>
      <template #saleUnitPrice="slotProps">
        <el-input
          v-model="slotProps.row.saleUnitPrice"
          :disabled="methodType == 'info'"
          type="number"
          :min="0"
          placeholder="请输入售价"
        ></el-input>
      </template>
      <template #discountPrice="slotProps">
        <el-input
          v-model="slotProps.row.discountPrice"
          :disabled="methodType == 'info'"
          placeholder="客户端折扣"
          type="number"
          :min="0"
        ></el-input>
      </template>
      <template #maxCostUnitPrice="slotProps">
        <el-input
          v-model="slotProps.row.maxCostUnitPrice"
          :disabled="true"
          placeholder="最高采购价"
          type="number"
          :min="0"
        ></el-input>
      </template>
      <template #minCostUnitPrice="slotProps">
        <el-input
          v-model="slotProps.row.minCostUnitPrice"
          :disabled="true"
          placeholder="最低采购价"
          type="number"
          :min="0"
        ></el-input>
      </template>
      <template #registerPrice="slotProps">
        <el-input
          v-model="slotProps.row.registerPrice"
          :disabled="methodType == 'info'"
          placeholder="登记客户折扣"
          type="number"
          :min="0"
        ></el-input>
      </template>
      <template #signingPrice="slotProps">
        <el-input
          v-model="slotProps.row.signingPrice"
          :disabled="methodType == 'info'"
          placeholder="签约客户折扣"
          type="number"
          :min="0"
        ></el-input>
      </template>
      <template #vipDiscountPrice="slotProps">
        <el-input
          v-model="slotProps.row.vipDiscountPrice"
          :disabled="methodType == 'info'"
          placeholder="VIP客户折扣"
          type="number"
          :min="0"
        ></el-input>
      </template>
      <template #highestCostPrice="slotProps">
        <el-input
          v-model="slotProps.row.highestCostPrice"
          :disabled="true"
          placeholder="最高成本价"
          type="number"
          :min="0"
        ></el-input>
      </template>
      <template #lowestCostPrice="slotProps">
        <el-input
          v-model="slotProps.row.lowestCostPrice"
          :disabled="true"
          placeholder="最低成本价"
          type="number"
          :min="0"
        ></el-input>
      </template>
      <template #recentCostPrice="slotProps">
        <el-input
          v-model="slotProps.row.recentCostPrice"
          :disabled="true"
          placeholder="最近成本价"
          type="number"
          :min="0"
        ></el-input>
      </template>
      <template #availableNum="slotProps">
        <el-input
          v-model="slotProps.row.availableNum"
          style="width: 90%"
          :disabled="methodType == 'info'"
          placeholder="请输入可售卖库存量"
          type="number"
          :min="0"
        ></el-input>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </DataTable>
    <ProDrawer
      :value="dialogVisibleOem"
      title="选择库品"
      size="80%"
      :confirm-loading="false"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisibleOem = false"
    >
      <OEM :warehouse-id="warehouseId" @chooseOem="setOem"></OEM>
    </ProDrawer>
  </div>
</template>
<script>
import { warehouseListApi } from "@/api/store";
import { itemUpdateItemSkuApi, storageInventoryListApi } from "@/api/goods";
import ProUpload from "@/components/ProUpload/index.vue";
import { isEmpty, cloneDeep, uniqBy } from "lodash";
import { filterName, getAllParentArr } from "@/utils";
import OEM from "@/views/goods/oem.vue";
export default {
  name: "ProductSpecifications",
  components: {
    ProUpload,
    OEM,
  },
  mixins: [],
  props: {
    itemId: {
      type: String,
      default: () => {
        return null;
      },
    },
    methodType: {
      type: String,
      default: () => {
        return "add";
      },
    },
    sourceType: {
      type: String,
      default: () => {
        return "wares";
      },
    },
  },
  data() {
    return {
      itemList: [],
      columns: [],
      defcolumns: [
        {
          dataIndex: "skuPicUrl",
          title: "图片",
          isTable: true,
          tableSlot: "skuPicUrl",
          width: "150px",
        },
        {
          dataIndex: "invSkuName",
          title: "库品",
          isTable: true,
          width: "250px",
          tableSlot: "invSkuId",
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          width: "120px",
          isTable: true,
        },
        {
          dataIndex: "invSkuOem",
          title: "OEM编号",
          width: "120px",
          isTable: true,
        },
        {
          dataIndex: "maxCostUnitPrice",
          title: "最高采购价",
          width: "120px",
          isTable: this.sourceType === "wares",
          tableSlot: "maxCostUnitPrice",
        },
        {
          dataIndex: "minCostUnitPrice",
          title: "最低采购价",
          width: "120px",
          isTable: this.sourceType === "wares",
          tableSlot: "minCostUnitPrice",
        },
        {
          dataIndex: "saleUnitPrice",
          title: "售价",
          width: "120px",
          isTable: true,
          tableSlot: "saleUnitPrice",
        },
        {
          dataIndex: "discountPrice",
          title: "客户端折扣",
          width: "120px",
          isTable: this.$props?.sourceType === "wares",
          tableSlot: "discountPrice",
        },
        {
          dataIndex: "registerPrice",
          title: "登记客户折扣",
          width: "120px",
          isTable: this.$props?.sourceType === "wares",
          tableSlot: "registerPrice",
        },
        {
          dataIndex: "signingPrice",
          title: "签约客户折扣",
          width: "120px",
          isTable: this.$props?.sourceType === "wares",
          tableSlot: "signingPrice",
        },
        {
          dataIndex: "vipDiscountPrice",
          title: "VIP客户折扣",
          width: "120px",
          isTable: this.$props?.sourceType === "wares",
          tableSlot: "vipDiscountPrice",
        },
        // {
        //   dataIndex: "highestCostPrice",
        //   title: "最高成本价",
        //   width: "140px",
        //   isTable: true,
        //   tableSlot: "highestCostPrice",
        // },
        // {
        //   dataIndex: "lowestCostPrice",
        //   title: "最低成本价",
        //   width: "140px",
        //   isTable: true,
        //   tableSlot: "lowestCostPrice",
        // },
        // {
        //   dataIndex: "recentCostPrice",
        //   title: "最近成本价",
        //   width: "140px",
        //   isTable: true,
        //   tableSlot: "recentCostPrice",
        // },
        {
          dataIndex: "availableNum",
          title: "可售卖库存量",
          width: "180px",
          isTable: true,
          tableSlot: "availableNum",
        },
        {
          dataIndex: "sumWarehouseNumber",
          title: "剩余库存",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "Actions",
          width: 80,
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      // 列表
      tableData: [],
      form: {},
      formcolumns: [
        {
          dataIndex: "warehouseId",
          title: "出库仓库",
          isForm: true,
          valueType: "select",
          formSpan: 24,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择出库仓库",
              trigger: "change",
            },
          ],
          fun: {
            change(val) {},
          },
        },
      ],
      tagList: [],
      skuArray: [],
      skuList: [],
      dialogVisibleOem: false, // oem弹窗
      //当前表格索引
      currentTableIndex: 0,
      warehouseId: "", // 仓库id
    };
  },
  computed: {},
  watch: {},
  created() {
    this.resetFrom();
    this.$nextTick((e) => {
      this.$refs["proform"].resetFormParam();
      this.$refs.ProTable.doLayout();
    });
  },
  mounted() {},
  methods: {
    checkPrice(item) {
      // discountPrice 客户端
      //saleUnitPrice  售价
      //vipDiscountPrice VIP折扣

      // 售价>VIP折扣>客户端
      if (item.vipDiscountPrice * 1000 >= item.saleUnitPrice * 1000) {
        this.$message.warning("VIP折扣不能大于售价");
        item.vipDiscountPrice = 0;

        return;
      }
      if (item.discountPrice * 1000 >= item.saleUnitPrice * 1000) {
        this.$message.warning("客户端折扣不能大于售价");
        item.discountPrice = 0;
        return;
      }
      if (item.discountPrice * 1000 >= item.vipDiscountPrice * 1000) {
        this.$message.warning("客户端不能大于VIP折扣");
        item.discountPrice = 0;
        return;
      }
    },
    echo(data) {
      console.log(data);
      setTimeout(() => {
        this.form = {
          warehouseId: data.warehouse?.id,
        };
        if (data.saleSkuVos) {
          const columns = cloneDeep(this.defcolumns);
          this.columns = cloneDeep([]);
          const tagList = [];
          this.tagList = [];
          this.tableData = data.saleSkuVos.map((item, index) => {
            if (item.storageArticle) {
              item.manufacturerChannel =
                item.storageArticle?.manufacturerChannel.label;
            }

            delete item.storageArticle;
            item.saleAttrVals.map((itemS, indexS) => {
              if (index == 0) {
                columns.unshift({
                  // dataIndex: `saleAttrVals${indexS + 1}`,
                  dataIndex: `saleAttrVals${indexS}`,
                  isTable: true,
                  title: itemS.name,
                  width: 120,
                });
                tagList.push({
                  index: `saleAttrVals${indexS}`,
                  label: itemS.name,
                  inputVisible: false,
                  inputValue: "",
                  children: [],
                });
              }
              if (tagList[indexS]) {
                tagList[indexS].children.push({ value: itemS.val });
              }
              // item[`saleAttrVals${indexS + 1}`] = itemS.val;
              item[`saleAttrVals${indexS}`] = itemS.val;
            });
            item.invSkuId = item.inventorySku?.id;
            return item;
          });
          console.log(this.tableData);
          this.tagList = tagList.map((item) => ({
            ...item,
            children: uniqBy(item.children, "value"),
          }));
          this.columns = columns;
        }
        if (this.methodType === "info") {
          this.columns.splice(this.columns.length - 1, 1);
        }
        console.log(this.columns);
      });
    },
    // 图片处理
    handleUploadSuccess(result, item) {
      item.push(result);
    },
    handleUploadRemove(file, item) {
      const index = item.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      item.splice(index, 1);
    },
    init() {
      // storageInventoryListApi().then((res) => {
      //   this.itemList = res.data;
      // });
    },
    addTag() {
      this.tagList.push({
        index: "saleAttrVals" + this.tagList.length,
        label: "",
        inputVisible: false,
        inputValue: "",
        children: [],
      });
    },
    delTag(index) {
      this.tagList.splice(index, 1);
    },
    showInput(index) {
      this.tagList[index].inputVisible = true;
    },

    handleClose(index, ind) {
      this.tagList[index].children.splice(ind, 1);
    },
    handleInputConfirm(index) {
      const inputValue = this.tagList[index].inputValue;
      if (inputValue) {
        this.tagList[index].children.push({
          value: inputValue,
        });
        this.tagList[index].inputVisible = false;
        this.tagList[index].inputValue = "";
      }
    },
    //初始化表单
    resetFrom() {
      // this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    async proSubmit(val) {
      this.form = cloneDeep(val);
      // console.log(this.tableData)
      await itemUpdateItemSkuApi({
        ...val,
        skuVoList: this.tableData.map((item) => ({
          ...item,
          itemId: this.itemId,
          saleUnitPrice: Number(item.saleUnitPrice || 0),
          vipDiscountPrice: Number(item.vipDiscountPrice || 0),
          signingPrice: Number(item.signingPrice || 0),
          registerPrice: Number(item.registerPrice || 0),
          discountPrice: Number(item.discountPrice || 0),

          // highestCostPrice: Number(item.highestCostPrice || 0),
          // lowestCostPrice: Number(item.lowestCostPrice || 0),
          // recentCostPrice: Number(item.recentCostPrice || 0),
          availableNum: Number(item.availableNum || 0),
        })),
        itemId: this.itemId,
      }).then((res) => {
        console.log(res);
        if (res.code === 200) {
          this.$emit("successProduct");
        }
      });
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //响应新增
    create() {
      this.columns = [...this.$options.data().columns, ...this.defcolumns];
      this.skuList = [];
      // this.tableData = [];
      this.skuArray = [];
      this.tagList.map((item) => {
        this.columns.unshift({
          dataIndex: item.index,
          isTable: true,
          title: item.label,
        });
      });
      this.$refs.ProTable.initColumns();
      const arr = [];
      this.tagList.map((item, index) => {
        arr.push({
          name: item.label,
          list: [],
        });
        item.children.map((child) => {
          arr[index].list.push(child.value);
        });
      });
      arr.forEach((element) => {
        element.list.length > 0 ? this.skuArray.push(element.list) : "";
      });
      if (this.skuArray.length > 0) {
        this.getSkuData([], 0, this.skuArray);
        setTimeout(() => {
          this.skuList.map((ele, index) => {
            const k = { saleAttrVals: [] };
            ele.map((el, i) => {
              k[this.tagList[i].index] = el;
              k.saleAttrVals.push({
                name: this.tagList[i].label,
                val: el,
              });
            });
            const isPush = !this.tableData.find((item) => {
              let flag = true;
              for (const key in k) {
                if (key === "saleAttrVals") continue;
                if (!item[key]) return false;
                if (item[key] !== k[key]) flag = false;
              }
              return flag;
            });
            isPush &&
              this.tableData.push({
                ...k,
                skuPicUrl: [],
                saleUnitPrice: null,
                highestCostPrice: null,
                lowestCostPrice: null,
                recentCostPrice: null,
                availableNum: null,
              });
          });
          console.log("tableData", this.tableData);
          this.$nextTick(() => {
            this.$refs.ProTable.doLayout();
          });
        });
      } else {
        this.$message.error("请先完善规格信息！");
      }
    },
    getSkuData(skuArr = [], i, list) {
      for (let j = 0; j < list[i].length; j++) {
        if (i < list.length - 1) {
          skuArr[i] = list[i][j];
          this.getSkuData(skuArr, i + 1, list); // 递归循环
        } else {
          this.skuList.push([...skuArr, list[i][j]]); // 扩展运算符，连接两个数组
        }
      }
    },
    //响应删除
    handleDelete(index) {
      this.tableData.splice(index, 1);
    },
    handleSubmit() {
      this.$refs["proform"].handleSubmit();
    },
    showOemDialog(index) {
      this.warehouseId = this.form.warehouseId;
      this.dialogVisibleOem = true;
      console.log(index);
      this.currentTableIndex = index;
    },
    // 选择oem
    setOem(data) {
      this.$set(this.tableData[this.currentTableIndex], "invSkuId", data.id);
      this.$set(
        this.tableData[this.currentTableIndex],
        "invSkuOem",
        data.article.numberOem
      );
      this.$set(
        this.tableData[this.currentTableIndex],
        "partId",
        data.article.partId
      );

      this.$set(
        this.tableData[this.currentTableIndex],
        "invSkuName",
        data.name
      );
      this.$set(
        this.tableData[this.currentTableIndex],
        "manufacturerChannel",
        data.article?.manufacturerChannel?.label
      );
      this.$set(
        this.tableData[this.currentTableIndex],
        "sumWarehouseNumber",
        data.sumWarehouseNumber
      );
      console.log(this.tableData);
      this.dialogVisibleOem = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.tit-box {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}

.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

// ::v-deep .el-upload--picture-card,
// ::v-deep .el-upload-list__item {
//   width: 80px !important;
//   height: 80px !important;
//   line-height: 80px !important;
// }

::v-deep .el-table__body-wrapper {
  overflow-x: auto;
}

.upload {
  display: inline-block !important;
}
</style>
