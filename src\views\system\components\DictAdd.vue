<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:54
 * @Description: 部门/角色 人员管理
 -->
<template>
  <!-- 人员管理  -->
  <ProDrawer
    :value="dialogVisible"
    :title="'字典项'"
    size="70%"
    :no-footer="true"
    @cancel="handleCancel"
  >
    <ProTable
      ref="ProTable"
      :columns="columns"
      :show-pagination="false"
      :query-param="queryParam"
      :data="tableData"
      sticky
      :show-search="false"
      height="80vh"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </template>
      <template #isEnable="slotProps">
        {{ slotProps.row.isEnable }}
      </template>

      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>

          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button></span
        >
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDialog
      :modal="false"
      :value="dialogVisibleK"
      :title="dialogTitleK"
      width="600px"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType === 'info'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisibleK"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
      </ProForm>
    </ProDialog>
  </ProDrawer>
</template>
<script>
import {
  dictoptListApi,
  dictoptAddApi,
  dictoptEditApi,
  dictoptDelApi,
} from "@/api/user";
import { isEmpty, cloneDeep } from "lodash";
import { filterName, getAllParentArr } from "@/utils";
export default {
  name: "DictAdd",
  components: {},
  mixins: [],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },

    roleId: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      tableData: [],
      selectDataall: [],
      selectData: [],
      queryParam: {
        isFilter: true,
        spareName: null,
      },
      columns: [
        {
          dataIndex: "label",
          title: "字典项名称",
          isTable: true,
          // isSearch: true,
          // span: 4,
          // valueType: 'input',
        },
        {
          dataIndex: "value",
          title: "编码",
          isTable: true,
        },
        {
          dataIndex: "sort",
          title: "排序",
          isTable: true,
        },
        // {
        //   dataIndex: "isEnable",
        //   tableSlot: "isEnable",
        //   title: "是否启用",
        //   isTable: true,
        // },

        {
          dataIndex: "Actions",
          width: 180,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      //新增
      methodType: "add",
      confirmLoading: false,
      form: { dictId: "" },
      dialogTitleK: "",
      dialogVisibleK: false,
      defaultFormParams: { dictId: "" },
      formcolumns: [
        {
          dataIndex: "label",
          isForm: true,
          title: "名称",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入名称",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "value",
          isForm: true,
          title: "字典码",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入字典码",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "sort",
          isForm: true,
          title: "排序",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入排序",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "description",
          isForm: true,
          title: "描述",
          valueType: "input",
          inputType: "textarea",
          clearable: true,
          formSpan: 24,
        },
      ],
    };
  },

  computed: {},

  watch: {},
  created() {},

  mounted() {},
  methods: {
    loadData(parameter) {
      this.tableData = [];
      dictoptListApi(this.roleId, parameter)
        .then((res) => {
          this.tableData = res.data;
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisibleK = false;
    },
    //触发新增
    handleAdd(data) {
      this.dialogTitle = "新增字典项目";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisibleK = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应新增
    create() {
      this.form.dictId = this.roleId;
      dictoptAddApi(this.form)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisibleK = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑 - " + row.label;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.methodType = "edit";
      this.dialogVisibleK = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应编辑
    update() {
      dictoptEditApi(this.form)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisibleK = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.label;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.methodType = "info";
      this.dialogVisibleK = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        dictoptDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },

    handleCancel() {
      this.$emit("cancel");
    },
    // handleAdd() {
    //   this.$emit("add");
    // },
  },
};
</script>
<style lang="scss" scoped></style>
