<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-09-19 18:44:25
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-10-10 13:31:41
 * @FilePath: \benyin-web\src\layout\components\Breadcrumb\index.vue
 * @Description: 
 * 
-->
<template>
  <el-breadcrumb class="breadcrumb-container" separator=">">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="item in list" :key="item.path">
        {{ item.meta.title }}
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>
<script>
import { isEmpty, cloneDeep } from "lodash";
import { mapActions, mapGetters } from "vuex";

export default {
  name: "Breadcrumb",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      list: this.getBreadcrumb(),
    };
  },

  computed: {},

  watch: {
    $route() {
      this.list = this.getBreadcrumb();
    },
  },
  created() {},
  mounted() {},
  methods: {
    getBreadcrumb() {
      return this.$route.matched.filter((item) => item.name && item.meta.title);
    },
  },
};
</script>

<style lang="scss" scoped>
.breadcrumb-container {
  display: flex;
  align-items: center;

  :deep(.el-breadcrumb__inner, .el-breadcrumb__item) {
    display: inline-flex;
    align-items: center;
    margin: 1px;
  }

  .menu-icon {
    padding-right: 5px;
  }
}
</style>
