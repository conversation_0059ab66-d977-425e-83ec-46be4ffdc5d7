<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 14:14:58
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="耗材" name="耗材">
        <Consumable />
      </el-tab-pane>
      <el-tab-pane label="机器" name="机器">
        <Machine />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Consumable from "@/views/store/components/consumable.vue";
import Machine from "@/views/store/components/machine.vue";

export default {
  name: "Losses",
  components: { Machine, Consumable },
  data() {
    return {
      activeName: this.$route.params.tabName || "耗材",
    };
  },
};
</script>

<style scoped lang="scss"></style>
