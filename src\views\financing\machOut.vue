<!--
 * @Author: wskg
 * @Date: 2025-03-22 11:29:42
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 17:53:32
 * @Description: 机器出库明细
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:finance:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>销售含税总额：{{ totalData?.totalAmount || 0 }}</div>
          <div>销售不含税总额：{{ totalData?.totalNoTaxAmount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import {
  machineOutboundDetailExportApi,
  machineOutboundDetailListApi,
  machineOutboundDetailStatisticsApi,
} from "@/api/finance";
import { handleExcelExport } from "@/utils/exportExcel";
import { filterParam, filterParamRange } from "@/utils";

export default {
  name: "MachOutDetails",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      columns: [
        {
          dataIndex: "createDate",
          title: "销售日期",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 100,
        },
        {
          dataIndex: "code",
          title: "订单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "type",
          title: "类型",
          isTable: true,
          formatter: (row) =>
            row.type == 1
              ? "销售出货"
              : row.type == 2
              ? "租赁出货"
              : row.type == 3
              ? "采购退货"
              : "",
          isSearch: true,
          valueType: "select",
          // multiple: true,
          option: [
            {
              label: "销售出货",
              value: 1,
            },
            {
              label: "租赁出货",
              value: 2,
            },
            {
              label: "采购退货",
              value: 3,
            },
          ],
          width: 80,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "customerCode",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "license",
          title: "营业执照名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "productName",
          title: "型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 100,
        },
        {
          dataIndex: "num",
          title: "销售数量",
          isTable: true,
          minWidth: 80,
        },

        {
          dataIndex: "price",
          title: "成本单价",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "taxAmount",
          title: "税额",
          isTable: true,
          minWidth: 80,
        },

        // {
        //   dataIndex: "tax",
        //   title: "税率(%)",
        //   isTable: true,
        //   formatter: (row) => (row.tax ? row.tax + "%" : ""),
        //   minWidth: 80,
        // },

        {
          dataIndex: "amount",
          title: "成本含税金额",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "noTaxAmount",
          title: "成本不含税金额",
          isTable: true,
          minWidth: 120,
        },
        // {
        //   dataIndex: "invoiceStatus",
        //   title: "开票状态",
        //   isTable: true,
        //   formatter: (row) => (row.invoiceStatus === 1 ? "已开票" : "未开票"),
        //   minWidth: 80,
        // },
      ],
      requestParameters: {},
      exportLoading: false,
      totalData: {},
      statLoading: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const paramsRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createDate,
        },
      ];
      filterParamRange(this, this.queryParam, paramsRange);
      this.requestParameters = cloneDeep(this.queryParam);
      delete this.requestParameters.createDate;
      machineOutboundDetailListApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      machineOutboundDetailStatisticsApi(this.requestParameters).then((res) => {
        this.totalData = res.data;
        this.statLoading = true;
      });
    },
    handleExport() {
      this.$confirm("此操作将导出机器出库明细, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          machineOutboundDetailExportApi,
          this.requestParameters,
          "机器出库明细",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
