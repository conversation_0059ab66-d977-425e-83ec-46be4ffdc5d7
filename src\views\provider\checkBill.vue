<!--
 * @Author: wskg
 * @Date: 2024-08-06 18:12:47
 * @LastEditors: wskg
 * @LastEditTime: 2024-08-09 11:50:48
 * @Description: 供应商 - 对账单
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'edit')"
            >开票</el-button
          >
          <el-button size="mini" icon="el-icon-discount">红冲</el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="billDrawer"
      :title="drawerTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :method-type="methodType"
      :no-footer="methodType === 'info'"
      :confirm-text="'提交'"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
      >
        <template #items>
          <ProTable
            ref="itemsProTable"
            :row-key="(row) => row.id"
            :columns="itemsColumns"
            :data="itemsTableData"
            :height="400"
            :show-setting="false"
            :show-search="false"
            :show-loading="false"
            sticky
          >
            <template #billList>
              <el-select v-model="value" placeholder="请选择">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </template>
          </ProTable>
        </template>
        <template #billList>
          <ProTable
            ref="courierProTable"
            :columns="billListColumns"
            :data="billListTableData"
            :height="400"
            :show-search="false"
            :show-setting="false"
            :show-loading="false"
            sticky
          >
            <template #btn>
              <el-button
                v-if="methodType !== 'info'"
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="handleAddBill"
              >
                新增发票</el-button
              >
            </template>
            <template #billNumber="{ row }">
              <el-input
                v-model="row.bill"
                placeholder="请输入发票编号"
              ></el-input>
            </template>
            <template #billPic="{ row }">
              <ProUpload
                :file-list="row.billPic"
                :type="methodType"
                :limit="2"
                :drag="true"
                @uploadSuccess="(e) => handleLicenseImgUploadSuccess(e, row)"
                @uploadRemove="(e) => handleLicenseImgUploadRemove(e, row)"
              />
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import ProUpload from "@/components/ProUpload/index.vue";

export default {
  name: "AccountCheck",
  components: { ProUpload },
  data() {
    return {
      value: "",
      options: [
        {
          value: "选项1",
          label: "黄金糕",
        },
        {
          value: "选项2",
          label: "双皮奶",
        },
        {
          value: "选项3",
          label: "蚵仔煎",
        },
        {
          value: "选项4",
          label: "龙须面",
        },
        {
          value: "选项5",
          label: "北京烤鸭",
        },
      ],
      methodType: "add",
      queryParam: {},
      columns: [
        {
          dataIndex: "code",
          title: "订单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "company",
          title: "公司",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "placeASinglePerson",
          title: "下单人",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "placeASinglePhone",
          title: "下单手机号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },

        {
          dataIndex: "orderAmount",
          title: "订单金额",
          isTable: true,
        },
        {
          dataIndex: "theAmountOfTheReturn",
          title: "退货金额",
          isTable: true,
        },
        {
          dataIndex: "paidInAmount",
          title: "实收金额",
          isTable: true,
        },
        {
          dataIndex: "status",
          title: "审核状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            {
              label: "待确认",
              value: 0,
            },
            {
              label: "已确认",
              value: 1,
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "refundStatus",
          title: "退货状态",
          isTable: true,
          formatter: (row) => {
            switch (row.refundStatus) {
              case 0:
                return "无";
              case 1:
                return "已退货";
              case 2:
                return "部分退货";
              default:
                return "无";
            }
          },
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            {
              label: "无",
              value: 0,
            },
            {
              label: "已退货",
              value: 1,
            },
            {
              label: "部分退货",
              value: 2,
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "invoiceStatus",
          title: "开票状态",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formatter: (row) => (row.invoiceStatus === 1 ? "已开票" : "未开票"),
          option: [
            {
              label: "未开票",
              value: 0,
            },
            {
              label: "已开票",
              value: 1,
            },
          ],
        },
        {
          dataIndex: "orderTime",
          title: "订单时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          width: 280,
          fixed: "right",
          tableSlot: "actions",
        },
      ],
      tableData: [
        {
          code: "20221000001",
          company: "北京XXXXXX有限公司",
          deliveryTime: "2022-10-10 10:10:10",
          initiatorName: "张三",
          phone: "13800000000",
          status: {
            label: "待确认",
            value: 0,
          },
          refundStatus: 0,
          invoiceStatus: 0,
        },
      ],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      billDrawer: false,
      drawerTitle: "查看 - ",
      confirmLoading: false,
      formLoading: false,
      form: {},
      // 物品清单
      formColumns: [
        {
          dataIndex: "code",
          title: "订单编号",
          isForm: true,
          formSpan: 24,
          valueType: "text",
        },
        {
          dataIndex: "company",
          title: "公司",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorName",
          title: "下单人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "submitTime",
          title: "提交时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "deliveryTime",
          title: "订单金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "license",
          title: "退货金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "bank",
          title: "实付金额",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "items",
          title: "物品清单",
          isForm: true,
          formSpan: 24,
          formSlot: "items",
        },
        {
          dataIndex: "billList",
          title: "发票列表",
          isForm: true,
          formSpan: 24,
          formSlot: "billList",
        },
      ],
      itemsColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
        {
          dataIndex: "number",
          title: "采购数量",
          isTable: true,
        },
        {
          dataIndex: "approveNum",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "sumPrice",
          title: "订单金额",
          isTable: true,
        },
        {
          dataIndex: "returnPrice",
          title: "退货金额",
          isTable: true,
        },
        {
          dataIndex: "realPrice",
          title: "实际金额",
          isTable: true,
        },
        {
          dataIndex: "ballCode",
          title: "发票编号",
          isTable: true,
          tableSlot: "billList",
        },
      ],
      itemsTableData: [
        {
          articleCode: "20221000001",
          articleName: "gongosn",
          oemNumber: "sadkjnl655",
          applicableModels: "",
          unit: "个",
          price: "152522",
          planNum: "20",
          approveNum: "20",
          sumPrice: "232152",
        },
      ],
      //   发票清单
      billListColumns: [
        {
          dataIndex: "billNumber",
          title: "发票编号",
          isTable: true,
          width: 220,
          tableSlot: "billNumber",
        },
        {
          dataIndex: "billPic",
          title: "发票附件",
          isTable: true,
          tableSlot: "billPic",
        },
      ],
      billListTableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      // pagePurchaseApi(requestParameters)
      //     .then((res) => {
      //       // this.tableData = res.data.rows;
      //       this.localPagination.total = Number(res.data.total);
      //     })
      //     .finally(() => {
      //       this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      //     });
    },
    handleEdit(row, type) {
      this.methodType = type;
      // getPurchaseDetailApi(row.id).then((res) => {
      //   this.form = res.data;
      // });
      this.billDrawer = true;
    },
    // 新增快递单号
    handleAddBill() {
      this.billListTableData.push({
        billNumber: "",
        billPic: [],
      });
    },
    handleLicenseImgUploadSuccess(result, row) {
      row.billPic.push(result);
    },
    handleLicenseImgUploadRemove(file, row) {
      const index = row.billPic.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      row.billPic.splice(index, 1);
    },
    refresh() {
      // this.$refs.ProTable.refresh();
      this.$refs.ProTable.listLoading = false;
    },
    closeDrawer() {
      this.billDrawer = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
