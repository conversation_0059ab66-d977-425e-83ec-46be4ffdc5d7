<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-01 13:58:11
 * @Description: 库品管理
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAddDialog"
        >
          新增库存
        </el-button>
        <el-button
          v-auth="['@ums:manage:stock:upload']"
          type="success"
          icon="el-icon-upload2"
          size="mini"
          @click="$refs.uploadExcel.show()"
        >
          导入数据
        </el-button>
        <el-button
          v-auth="['@ums:manage:stock:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>库存总量：{{ totalData.inventoryNum || 0 }}</div>
          <div>工程师仓数量：{{ totalData.engineerNum || 0 }}</div>
          <div>客户仓总数量：{{ totalData.applyNum || 0 }}</div>
          <div>库存总数量：{{ totalData.totalNum || 0 }}</div>
          <div>预估总金额：{{ totalData.amount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
      <template #type="slotProps">
        {{ slotProps.row?.type?.label }}
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            详情
          </el-button>
          <el-button
            size="mini"
            icon="el-icon-plus"
            @click="handleAddPurchase(slotProps.row)"
          >
            添加到采购单
          </el-button>
          <!-- <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button> -->
        </span>
      </template>
      <template #imageFiles="{ row }">
        <el-image
          v-if="row.article.imageFiles && row.article.imageFiles.length > 0"
          style="max-width: 100px; max-height: 100px"
          :src="getPicsUrlImg(row)"
          :preview-src-list="[getPicsUrlImg(row)]"
        ></el-image>
        <!--<div v-else>暂无</div>-->
      </template>
      <template #location="slotProps">
        <el-input
          v-model="slotProps.row.location"
          placeholder="请输入储位"
          type="text"
          :disabled="!slotProps.row.isedit"
          style="width: 130px"
          @click="slotProps.row.isedit = true"
        ></el-input>

        <el-link
          v-if="!slotProps.row.isedit"
          style="margin-left: 10px"
          icon="el-icon-edit"
          :underline="false"
          @click="clicka(slotProps.row)"
        >
          编辑
        </el-link>
        <el-link
          v-if="slotProps.row.isedit"
          style="margin-left: 10px"
          :underline="false"
          @click="changekwFn(slotProps.row)"
        >
          保存
        </el-link>
        <el-link
          v-if="slotProps.row.isedit"
          style="margin-left: 10px"
          :underline="false"
          type="info"
          @click="changekwFn()"
        >
          取消
        </el-link>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType == 'info'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
        <template #baseInfo>
          <div class="title-box" style="margin-top: 0">基础信息</div>
        </template>
        <template #productTree>
          <el-popover placement="bottom" title="" width="400" trigger="click">
            <div style="margin: 20px; height: 400px; overflow-y: auto">
              <el-descriptions
                class="margin-top"
                title="所属品牌/产品树"
                :column="1"
                border
              >
                <el-descriptions-item
                  v-for="item in form.productTreeDtoList"
                  :key="item.id"
                >
                  <template slot="label">品牌/系列/机型</template>
                  {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <el-button slot="reference" type="text" size="mini">查看</el-button>
          </el-popover>
        </template>
        <template #numberOem>
          <span>
            {{ form.article?.numberOem }}
          </span>
        </template>
        <template #supplierCode>
          <span>{{ form.manufacturer?.code }}</span>
        </template>
        <template #unit>
          <span>{{ form.article?.unit }}</span>
        </template>
        <template #supplierName>
          <span>{{ form.manufacturer?.name }}</span>
        </template>
        <template #manufacturerGoodsCode>
          <span>{{ form.article?.manufacturerGoodsCode }}</span>
        </template>
        <template #manufacturerName>
          <span>{{ form.manufacturer?.name }}</span>
        </template>
        <template #manufacturerChannel>
          <span>{{ form.article?.manufacturerChannel.label }}</span>
        </template>
        <template #quality>
          <span>{{ form.article?.quality }}</span>
        </template>

        <template #warehouseType>
          <span>{{ form?.warehouseType }}</span>
        </template>
        <template #warehouseName>
          <el-button type="text" @click="openLink">
            {{ form.warehouseName }}
          </el-button>
        </template>
        <template #alarmNumber>
          <el-input
            v-model="form.alarmNumber"
            style="width: 60%"
            size="small"
            :disabled="!isedit"
            placeholder="请输入内容"
          ></el-input>
          <el-link
            v-if="!isedit"
            style="margin-left: 10px"
            icon="el-icon-edit"
            :underline="false"
            @click="isedit = true"
          >
            编辑
          </el-link>
          <el-link
            v-if="isedit"
            style="margin-left: 10px"
            :underline="false"
            @click="update"
          >
            保存
          </el-link>
          <el-link
            v-if="isedit"
            style="margin-left: 10px"
            :underline="false"
            type="info"
            @click="isedit = false"
          >
            取消
          </el-link>
        </template>
        <template #safeNumber>
          <el-input
            v-model="form.safeNumber"
            style="width: 60%"
            size="small"
            :disabled="!isEdit"
            placeholder="请输入内容"
          ></el-input>
          <el-link
            v-if="!isEdit"
            style="margin-left: 10px"
            icon="el-icon-edit"
            :underline="false"
            @click="isEdit = true"
          >
            编辑
          </el-link>
          <el-link
            v-if="isEdit"
            style="margin-left: 10px"
            :underline="false"
            @click="update"
          >
            保存
          </el-link>
          <el-link
            v-if="isEdit"
            style="margin-left: 10px"
            :underline="false"
            type="info"
            @click="isEdit = false"
          >
            取消
          </el-link>
        </template>
        <template #location>
          <el-input
            v-model="form.location"
            class="kuwei"
            size="small"
            :disabled="!isedits"
            placeholder="请输入库位"
            type="text"
          ></el-input>

          <el-link
            v-if="!isedits"
            style="margin-left: 10px"
            icon="el-icon-edit"
            :underline="false"
            @click="isedits = true"
          >
            编辑
          </el-link>
          <el-link
            v-if="isedits"
            style="margin-left: 10px"
            :underline="false"
            @click="update"
          >
            保存
          </el-link>
          <el-link
            v-if="isedits"
            style="margin-left: 10px"
            :underline="false"
            type="info"
            @click="isedits = false"
          >
            取消
          </el-link>
        </template>
      </ProForm>
      <el-tabs
        v-model="activeName"
        @change="loadData1(), loadData3(), loadData2()"
      >
        <el-tab-pane label="入库批次" name="inkBatch">
          <ProTable
            ref="ProTable1"
            :columns="columns1"
            show-pagination
            :show-search="false"
            row-key="id"
            :local-pagination="localPagination1"
            :data="inWarehouseList"
            out-warehouse-list
            sticky
            :height="350"
            @loadData="loadData1"
          ></ProTable>
        </el-tab-pane>
        <el-tab-pane label="入库记录" name="ink">
          <ProTable
            ref="ProTable3"
            :columns="columns3"
            :show-search="false"
            show-pagination
            row-key="id"
            :local-pagination="localPagination3"
            :data="WarehouseRecordList"
            sticky
            :height="350"
            @loadData="loadData3"
          ></ProTable>
        </el-tab-pane>
        <el-tab-pane label="出库记录" name="outk">
          <ProTable
            ref="ProTable2"
            :columns="columns2"
            :show-search="false"
            show-pagination
            row-key="id"
            :local-pagination="localPagination2"
            :data="outWarehouseList"
            sticky
            :height="350"
            @loadData="loadData2"
          ></ProTable>
        </el-tab-pane>
      </el-tabs>
    </ProDrawer>
    <!-- 导入 -->
    <ProDialog
      :value="dialogUpload"
      :title="'导入文件'"
      :confirm-text="'导入'"
      width="400px"
      :top="'10%'"
      @ok="UploadOk"
      @cancel="dialogUpload = false"
    >
      <el-select
        v-model="warehouse"
        style="margin-bottom: 20px"
        placeholder="请选择归属仓库"
      >
        <el-option
          v-for="item in warehouseList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        ></el-option>
      </el-select>
      <el-upload
        ref="upload"
        class="upload-demo"
        :action="actionUrl"
        name="file"
        :headers="{ 'X-AUTH-TOKEN': token }"
        accept=".xls,.xlsx,.XLSX,.XLS"
        :multiple="false"
        :limit="1"
        :file-list="fileList"
        :on-success="handleSuccess"
        :on-change="handleChange"
        :auto-upload="false"
        :data="{ ckid: warehouse }"
      >
        <el-button size="small" type="primary">选择文件</el-button>
        <div slot="tip" class="el-upload__tip">
          提示：仅支持上传“xls”或“xlsx”格式文件。
        </div>
      </el-upload>
    </ProDialog>

    <!-- 新增弹窗 -->
    <ProDialog
      :value="addDialog"
      :title="'新增库存'"
      :confirm-text="'确认新增'"
      width="60%"
      :top="'50px'"
      @ok="handleAddDialogOk"
      @cancel="addDialog = false"
    >
      <ProForm
        ref="addForm"
        :form-param="addForm"
        :form-list="addColumns"
        :confirm-loading="addFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :open-type="'add'"
        @proSubmit="addFormSubmit"
      >
        <template #articleList>
          <ProTable
            ref="ProTable1"
            row-key="id"
            :data="selectedAddArticle"
            :columns="selectedAddArticleColumns"
            show-index
            :show-search="false"
            :show-setting="false"
            :height="400"
            :show-loading="false"
          >
            <template #btn>
              <el-button
                type="success"
                class="add-btn"
                size="mini"
                icon="el-icon-plus"
                @click="handleSelectArticle"
              >
                选择物品
              </el-button>
            </template>
            <template #location="{ row }">
              <el-input
                v-model="row.location"
                placeholder="请输入储位"
                size="medium"
                clearable
              ></el-input>
            </template>
            <template #action="{ row }">
              <div class="fixed-width">
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDeleteAddArticle(row)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDialog>

    <!--  选择物品弹窗  -->
    <ProDialog
      :value="articleDialog"
      title="选择物品"
      width="1400px"
      :confirm-loading="articleDialogLoading"
      confirm-text="确认选择"
      top="50px"
      @ok="articleDialogConfirm"
      @cancel="articleDialogCancel"
    >
      <ProTable
        ref="articleTable"
        row-key="id"
        :data="articleData"
        :columns="articleColumns"
        :height="350"
        :query-param="articleQueryParam"
        :local-pagination="articleLocalPagination"
        show-index
        show-selection
        show-search
        show-loading
        show-pagination
        @loadData="loadArticleData"
        @handleSelected="handleArticleSelected"
        @handleSelectionChange="handleArticleSelectionChange"
      ></ProTable>
    </ProDialog>
    <!-- 导入 -->
    <UploadExcel
      ref="uploadExcel"
      title="导入耗材仓库数据"
      :action-url="articleActionUrl"
      :download-template-fun="handleDownloadTemplate"
      @uploadSuccess="handleUploadExcelSuccess"
    />
  </div>
</template>
<script>
import { Message } from "element-ui";

const { uploadURL } = window.config.api;
import {
  storagePageApi,
  storageAddApi,
  storageDelApi,
  storageEditApi,
  storageInfoApi,
  warehouseListApi,
  storageUpApi,
  articlePageApi,
  addStorageInventoryApi,
  inFlowPageApi,
  outFlowPageApi,
  addToPurchasePlanApi,
  storageImportApi,
  storageDownTemplateApi,
  storageExportApi,
  storageStatisticsApi,
} from "@/api/store";
import { dictTreeByCodeApi } from "@/api/user";

import { cloneDeep } from "lodash";
import { filterParam, filterParamRange, mulAmount } from "@/utils";
import UploadExcel from "@/components/ProUpload/excel.vue";
import { handleExcelExport } from "@/utils/exportExcel";

export default {
  name: "Stock",
  components: { UploadExcel },
  mixins: [],
  props: {},
  data() {
    return {
      minSumWarehouseNumber: "",
      maxSumWarehouseNumber: "",
      activeName: "inkBatch",
      articleActionUrl: uploadURL + storageImportApi,
      isedit: false,
      isedits: false,
      isEdit: false,
      warehouse: "",
      warehouseList: [],
      token: localStorage.getItem("token"),
      active: 9,
      // 列表
      spareiTypeList: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        aduitState: null,
        name: null,
      },
      columns: [
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          minWidth: 160,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          span: 4,
          minWidth: 150,
          valueType: "input",
        },
        {
          dataIndex: "partBrand",
          title: "品牌",
          isTable: true,
          isSearch: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          width: 150,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          isSearch: true,
          span: 5,
          width: 90,
          valueType: "select",
          clearable: true,
          formatter: (row) => row.article.manufacturerChannel.label,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "imageFiles",
          title: "物品图片",
          width: 150,
          isTable: true,
          tableSlot: "imageFiles",
          clearable: true,
          align: "center",
        },
        {
          dataIndex: "warehouseId",
          title: "归属仓库",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          formatter: (row) => row.warehouseName,
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
          width: 120,
        },
        {
          dataIndex: "outWarehouseNumber",
          title: "出库量",
          isTable: true,
          width: 80,
          align: "center",
        },
        {
          dataIndex: "saleStatus",
          title: "销售状态",
          isSearch: true,
          isTable: true,
          clearable: true,
          span: 4,
          valueType: "select",
          formatter: (row) =>
            row.saleStatus == "0"
              ? "未售"
              : row.saleStatus == "1"
              ? "在售"
              : "",
          option: [
            {
              label: "未售",
              value: "0",
            },
            {
              label: "在售",
              value: "1",
            },
          ],
          width: 80,
          align: "center",
        },
        {
          dataIndex: "runWarehouseNumber",
          title: "在途量",
          isTable: true,
          width: 80,
          align: "center",
        },
        {
          dataIndex: "sumWarehouseNumber",
          title: "库存量",
          isSearch: true,
          valueType: "inputRange",
          // searchSlot: "searchNumbers",
          span: 4,
        },
        {
          dataIndex: "sumWarehouseNumber",
          title: "库存量",
          isTable: true,
          span: 4,
          valueType: "input",
          width: 80,
          align: "center",
        },
        {
          dataIndex: "engineerNum",
          title: "工程师数量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          width: 90,
          align: "center",
        },
        {
          dataIndex: "applyNum",
          title: "客户仓数量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          width: 90,
          align: "center",
        },
        {
          dataIndex: "totalNum",
          title: "总数量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          width: 80,
          align: "center",
        },
        {
          dataIndex: "alarmNumber",
          title: "预警值",
          isTable: true,
          width: 80,
          align: "center",
        },
        {
          dataIndex: "alarmNumber",
          title: "库存预警值",
          isSearch: true,
          valueType: "inputRange",
          width: 80,
          align: "center",
        },
        {
          dataIndex: "location",
          title: "储位",
          isSearch: true,
          isTable: true,
          width: 240,
          tableSlot: "location",
          valueType: "input",
          clearable: true,
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(3500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "isAlarm",
          title: "库存警告",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      columns1: [
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
        },
        {
          dataIndex: "inWarehouseId",
          title: "关联入库单号",
          isTable: true,
          formatter: (row) => row.inWarehouseId,
        },

        {
          dataIndex: "price",
          title: "采购单价",
          isTable: true,
          formatter: (row) => (row.price ? row.price + "元" : "0元"),
        },
        {
          dataIndex: "sumWarehouseNumber",
          title: "入库数量",
          isTable: true,
          align: "center",
          formatter: (row) =>
            row.sumWarehouseNumber ? row.sumWarehouseNumber + "件" : "0件",
        },
        {
          dataIndex: "remWarehouseNumber",
          title: "库存余量",
          isTable: true,
          align: "center",
          formatter: (row) =>
            row.remWarehouseNumber ? row.remWarehouseNumber + "件" : "0件",
        },
        {
          dataIndex: "id",
          title: "库存金额",
          isTable: true,
          formatter: (row) =>
            row.remWarehouseNumber && row.price
              ? mulAmount(row.remWarehouseNumber, row.price) + "元"
              : "0元",
        },
        {
          dataIndex: "inWarehouseList",
          title: "入库方式",
          isTable: true,
          formatter: (row) => row.inWarehouseType.label,
        },
        {
          dataIndex: "inWarehouseTime",
          title: "入库时间",
          isTable: true,
        },
      ],
      columns2: [
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
        },
        {
          dataIndex: "flowId",
          title: "关联出库单号",
          isTable: true,
        },

        {
          dataIndex: "number",
          title: "出库数量",
          isTable: true,
        },

        {
          dataIndex: "outType",
          title: "出库方式",
          isTable: true,
          formatter: (row) => row.type?.label,
        },
        {
          dataIndex: "time",
          title: "出库时间",
          isTable: true,
        },
      ],
      columns3: [
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
        },
        {
          dataIndex: "flowId",
          title: "关联出库单号",
          isTable: true,
        },

        {
          dataIndex: "number",
          title: "入库数量",
          isTable: true,
        },

        {
          dataIndex: "outType",
          title: "入库方式",
          isTable: true,
          formatter: (row) => row.type?.label,
        },
        {
          dataIndex: "time",
          title: "入库时间",
          isTable: true,
        },
      ],
      localPagination1: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      localPagination2: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      localPagination3: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      inWarehouseList: [],
      WarehouseRecordList: [],
      outWarehouseList: [],
      //新增
      methodType: "add",
      confirmLoading: false,
      form: { parentId: "" },
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: { parentId: "" },
      formcolumns: [
        {
          dataIndex: "baseInfo",
          title: "基础信息",
          isForm: true,
          formOtherSlot: "baseInfo",
          formSpan: 24,
        },
        // {
        //   isForm: true,
        //   isCut: true,
        //   title: "基础信息",
        // },
        {
          dataIndex: "code",
          title: "物品编号",
          isForm: true,
          valueType: "text",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "name",
          title: "物品名称",
          isForm: true,
          valueType: "text",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "numberOem",
          title: "原厂零件编号",
          isForm: true,
          valueType: "text",
          clearable: true,
          formSpan: 6,
          formSlot: "numberOem",
        },
        {
          dataIndex: "productTree",
          title: "所属品牌/产品树",
          isForm: true,
          valueType: "text",
          clearable: true,
          formSpan: 6,
          formSlot: "productTree",
        },
        {
          dataIndex: "supplierCode",
          title: "供应商编号",
          isForm: true,
          valueType: "text",
          clearable: true,
          formSpan: 6,
          formSlot: "supplierCode",
        },
        {
          dataIndex: "supplierName",
          title: "供应商名称",
          isForm: true,
          valueType: "text",
          clearable: true,
          formSpan: 6,
          formSlot: "supplierName",
        },
        {
          dataIndex: "manufacturerGoodsCode",
          title: "制造商物品编号",
          isForm: true,
          valueType: "text",
          clearable: true,
          formSpan: 6,
          formSlot: "manufacturerGoodsCode",
        },
        {
          dataIndex: "manufacturerName",
          title: "制造商名称",
          isForm: true,
          valueType: "text",
          clearable: true,
          formSpan: 6,
          formSlot: "manufacturerName",
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isForm: true,
          valueType: "text",
          clearable: true,
          formSpan: 6,
          formSlot: "manufacturerChannel",
        },
        // {
        //   dataIndex: "quality",
        //   title: "成色",
        //   isForm: true,
        //   valueType: "text",
        //   clearable: true,
        //   formSpan: 6,
        //   formSlot: "quality",
        // },
        {
          dataIndex: "unit",
          title: "单位",
          isForm: true,
          valueType: "text",
          clearable: true,
          formSpan: 6,
          formSlot: "unit",
        },
        {
          dataIndex: "sumWarehouseNumber",
          title: "库存总数",
          isForm: true,
          valueType: "text",

          formSpan: 6,
        },
        {
          dataIndex: "alarmNumber",
          title: "库存预警值",
          formSlot: "alarmNumber",
          isForm: true,
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "safeNumber",
          title: "安全库存",
          formSlot: "safeNumber",
          isForm: true,
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "warehouseType",
          title: "仓库类型",
          isForm: true,
          valueType: "text",
          clearable: true,
          formSpan: 6,
          formSlot: "warehouseType",
        },
        {
          dataIndex: "warehouseName",
          title: "仓库名称",
          isForm: true,
          formSlot: "warehouseName",
          clearable: true,
          formSpan: 6,
        },
        {
          dataIndex: "location",
          title: "储位",
          formSlot: "location",
          isForm: true,
          width: 40,
          clearable: true,
          formSpan: 6,
          formatter: (row) => row.location.value,
        },
      ],
      fileList: [],
      actionUrl: uploadURL + storageUpApi,
      dialogUpload: false,
      url: "",
      srcList: [],
      locationList: [],
      addDialog: false,
      addColumns: [
        {
          dataIndex: "warehouseId",
          title: "仓库名称",
          width: 100,
          isForm: true,
          formatter: (row) => row.warehouseName,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          formSpan: 8,
          optionskey: {
            label: "name",
            value: "id",
          },
          prop: [
            {
              required: true,
              message: "请选择仓库名称",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "articleList",
          title: "物品列表",
          isForm: true,
          formOtherSlot: "articleList",
          formSpan: 24,
        },
      ],
      addForm: {},
      addFormLoading: false,
      selectedAddArticle: [],
      selectedAddArticleColumns: [
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          minWidth: 100,
          tableSlot: "location",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 100,
        },
      ],
      articleDialog: false,
      articleDialogLoading: false,
      articleQueryParam: {},
      articleLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      articleData: [],
      articleSelection: [],
      articleColumns: [
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
          searchSpan: 6,
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          minWidth: 130,
          valueType: "input",
          searchSpan: 6,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          minWidth: 120,
          valueType: "input",
          searchSpan: 6,
        },
        {
          dataIndex: "partBrand",
          title: "品牌",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isSearch: true,
          valueType: "input",
          searchSpan: 6,
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          isTable: true,
          // isSearch: true,
          minWidth: 150,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerGoodsCode",
          title: "制造商物品编号",
          isTable: true,
          minWidth: 130,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel.label,
          width: 100,
        },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
          formatter: (row) => row.type?.label,
          width: 100,
        },
      ],
      exportLoading: false,
      statLoading: true,
      totalData: {},
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
    warehouseListApi().then((res) => {
      this.warehouseList = res.data;
    });
  },
  methods: {
    // 添加到采购单
    handleAddPurchase(row) {
      const params = {
        articleCode: row.code,
        number: 1,
        warehouseId: row.warehouseId,
        requireSource: "MANUAL",
      };
      addToPurchasePlanApi(params).then((res) => {
        Message.success("添加成功");
      });
    },
    clicka(row) {
      this.editId = row.id;
      this.$refs.ProTable.refresh();
    },
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          minAlarmNumber: null,
          maxAlarmNumber: null,
          data: parameter.alarmNumber,
        },
        {
          minEngineerNum: null,
          maxEngineerNum: null,
          data: parameter.engineerNum, // 工程师数量
        },
        {
          minApplyNum: null,
          maxApplyNum: null,
          data: parameter.applyNum, // 客户仓数量
        },
        {
          minTotalNum: null,
          maxTotalNum: null,
          data: parameter.totalNum, // 总数量
        },
        {
          minSumWarehouseNumber: null,
          maxSumWarehouseNumber: null,
          data: parameter.sumWarehouseNumber, // 库存量
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      [
        "alarmNumber",
        "engineerNum",
        "applyNum",
        "totalNum",
        "sumWarehouseNumber",
      ].forEach((key) => delete requestParameters[key]);
      this.requestParameters = requestParameters;
      storagePageApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.tableData.map((ele) => {
            if (ele.id === this.editId) {
              ele.isedit = true;
            } else {
              ele.isedit = false;
            }
          });
          this.localPagination.total = parseInt(res.data.total);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      storageStatisticsApi(this.requestParameters)
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    //加载表格
    loadData1(parameter) {
      const requestParameters = Object.assign({ id: this.form.id }, parameter);
      inFlowPageApi(requestParameters)
        .then((res) => {
          this.inWarehouseList = res.data.rows;

          this.localPagination1.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable1
            ? (this.$refs.ProTable1.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    // 出库记录
    loadData2(parameter) {
      const requestParameters = Object.assign(
        { id: this.form.id, inOutType: 2 },
        parameter
      );
      outFlowPageApi(requestParameters)
        .then((res) => {
          this.outWarehouseList = res.data.rows;

          this.localPagination2.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable2
            ? (this.$refs.ProTable2.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    // 入库记录
    loadData3(parameter) {
      const requestParameters = Object.assign(
        { id: this.form.id, inOutType: 1 },
        parameter
      );
      outFlowPageApi(requestParameters)
        .then((res) => {
          this.WarehouseRecordList = res.data.rows;
          this.localPagination3.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable3
            ? (this.$refs.ProTable3.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    openLink() {
      localStorage.setItem("nextActive", "/warehouse");
      this.$router.push({
        path: "/warehouse",
        query: { name: this.form.warehouseName },
      });
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.dialogTitle = "新增字典";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;

      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应新增
    create() {
      storageAddApi(this.form)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑 - " + row.label;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.methodType = "edit";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    getPicsUrlImg(row) {
      return row?.article.imageFiles?.[0]?.url;
    },
    //响应编辑
    update() {
      const obj = {
        id: this.form.id,
        alarmNumber: this.form.alarmNumber,
        safeNumber: this.form.safeNumber,
        location: this.form.location,
      };
      storageEditApi(obj)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.isedit = false;
          this.isedits = false;
          this.isEdit = false;
          // this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    changekwFn(row) {
      if (row) {
        storageEditApi({ id: row.id, location: row.location })
          .then(() => {
            this.$message.success("修改成功");
          })
          .finally(() => {
            this.confirmLoading = false;
            this.editId = null;
            this.$refs.ProTable.refresh();
            // this.dialogVisible = false;
            // this.$refs.ProTable.refresh();
          });
      } else {
        this.editId = null;
        this.$refs.ProTable.refresh();
      }
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.name;
      this.resetFrom();
      this.inWarehouseList = [];
      this.WarehouseRecordList = [];
      this.outWarehouseList = [];
      storageInfoApi({ id: row.id }).then((res) => {
        this.form = cloneDeep(res.data);
        this.$refs.ProTable1.refresh();
        this.$refs.ProTable2.refresh();
        this.$refs.ProTable3.refresh();
      });

      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        storageDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
    handleUpload() {
      this.warehouse = "";
      this.fileList = [];
      this.dialogUpload = true;
    },
    UploadOk() {
      this.$refs.upload.submit();
    },
    handleSuccess(res) {
      if (res.code == 200) {
        this.$message.success("导入成功");
        this.dialogUpload = false;
        this.$refs.ProTable.refresh();
      } else {
        this.$message.error("导入失败，请检查文件");
      }
    },
    handleChange(files, fileList) {
      if (files.status == "ready") {
        if (!/\.(xlsx|xls|XLSX|XLS)$/.test(files.raw.name)) {
          this.$message.error("仅支持上传“xls”或“xlsx”格式文件。");
          fileList.splice(-1, 1);
          return;
        }
        if (files.raw.size / 1024 / 1024 > 2) {
          this.$message.error();
          fileList.splice(-1, 1);
          return;
        }
      }
      const newFileList = fileList.slice(-1);
      this.fileList = newFileList;
    },
    handleAddDialog() {
      this.addForm = {};
      this.articleSelection = [];
      this.selectedAddArticle = [];
      this.addDialog = true;
    },
    handleAddDialogOk() {
      this.$refs.addForm.handleSubmit();
    },
    async addFormSubmit(val) {
      this.$confirm("此操作将添加库存数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          this.addFormLoading = true;
          const articleDtoList = this.selectedAddArticle.map((item) => {
            return {
              code: item.code,
              location: item.location,
            };
          });
          const result = await addStorageInventoryApi({
            ...val,
            articleDtoList,
          });
          if (result.code === 200) {
            Message.success("添加成功");
            this.addDialog = false;
            this.$refs.ProTable.refresh();
          }
        } catch (err) {
          Message.error(err.message);
        } finally {
          this.addFormLoading = false;
        }
      });
    },

    handleSelectArticle() {
      if (!this.addForm.warehouseId) {
        Message.error("请先选择仓库");
        return;
      }
      this.articleDialog = true;
      this.articleQueryParam = {};
      this.articleLocalPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.$nextTick(() => {
        this.$refs.articleTable.refresh();
        this.$refs.articleTable.$refs.ProElTable.clearSelection();
        this.articleSelection = cloneDeep(this.selectedAddArticle);
        this.articleSelection.map((row) => {
          this.$refs.articleTable.$refs.ProElTable.toggleRowSelection(row);
        });
      });
    },
    articleDialogConfirm() {
      // this.selectedAddArticle = [...this.articleSelection]
      this.selectedAddArticle = this.articleSelection.map((row) => {
        const target = this.selectedAddArticle.find(
          (item) => item.id === row.id
        );
        return target || row;
      });
      this.articleDialogCancel();
    },
    articleDialogCancel() {
      this.articleDialog = false;
    },
    async loadArticleData(params) {
      try {
        const result = await articlePageApi({
          ...params,
          warehouseId: this.addForm.warehouseId,
        });
        if (result.code === 200 && result.data) {
          this.articleData = result.data.rows;
          this.articleLocalPagination = {
            pageNumber: params.pageNumber,
            pageSize: params.pageSize,
            total: +result.data.total,
          };
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.articleTable &&
          (this.$refs.articleTable.listLoading = false);
      }
    },
    handleArticleSelected(selection, row) {
      const index = this.articleSelection.findIndex(
        (item) => item.id === row.id
      );
      if (index === -1) {
        this.articleSelection.push(row);
      } else {
        this.articleSelection.splice(index, 1);
      }
    },
    handleUploadExcelSuccess() {
      this.$refs.ProTable.refresh();
    },
    handleDownloadTemplate() {
      handleExcelExport(storageDownTemplateApi, {}, "耗材仓库数据导入模板");
    },
    handleArticleSelectionChange(val) {
      this.articleSelection = [...val];
    },
    handleDeleteAddArticle(row) {
      const index = this.selectedAddArticle.findIndex(
        (item) => item.id === row.id
      );
      if (index === -1) return;
      this.selectedAddArticle.splice(index, 1);
      this.articleSelection.splice(index, 1);
    },
    handleExport() {
      this.$confirm("此操作将导出所有耗材仓库数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          storageExportApi,
          this.requestParameters,
          "耗材仓库数据",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.searchNumbers {
  width: 240px;
  height: 40px;
  display: flex;
  align-items: center;
  color: #dcdfe6;
}

.kuwei {
  width: 130px !important;

  ::v-deep .el-input__inner {
  }
}
</style>
