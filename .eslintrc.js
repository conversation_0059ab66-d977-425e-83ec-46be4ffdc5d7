/*
 * @Description:
 * @Autor: shh
 * @Date: 2022-11-10 17:20:39
 * @LastEditors: shh
 * @LastEditTime: 2022-11-15 10:58:11
 */
module.exports = {
  root: true,
  //此项是用来指定javaScript语言类型和风格，sourceType用来指定js导入的方式，默认是script，此处设置为module，指某块导入方式
  parserOptions: {
    parser: "@babel/eslint-parser",
    sourceType: "module",
  },
  //此项指定环境的全局变量，下面的配置指定为浏览器环境
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  extends: ["plugin:vue/recommended", "eslint:recommended"],
  /**
   * Eslint 检查规则（A）
   * "off" 或者 0 -- 关闭规则
   * "warn" 或者 1 -- 将规则打开为警告（不影响退出代码）
   * "error" 或者 2 -- 将规则打开为错误（触发时退出代码为 1）
   */
  rules: {
    "vue/max-attributes-per-line": 0,
    "vue/html-closing-bracket-newline": 0,
    "vue/html-closing-bracket-spacing": 0,
    "no-console": "off",
    "no-debugger": "off",
    "vue/multi-word-component-names": 0, // 不校验组件名
    "vue/singleline-html-element-content-newline": 0, // 禁用单行标签内容需换行的校验
    "vue/multiline-html-element-content-newline": 0, // 禁用多行标签内容需换行的校验
    "vue/name-property-casing": [1, "PascalCase"], // 组件 name 属性值必须使用帕斯卡命名法（单词首字母大写）
    "vue/no-v-html": 0, // 禁用禁止使用 v-html 的校验
    "accessor-pairs": 2, // 对象必须使用 getter / setter 对，即：每个已定义 setter 的属性都需有一个 getter
    "arrow-spacing": [
      // 箭头函数 => 前后必须有空格
      1,
      {
        before: true,
        after: true,
      },
    ],

    // 单行代码块花括号打开块之后和关闭块之前，至少一个空格
    "block-spacing": 0,

    // 强制执行标准大括号风格样式（关键字要与花括号保持在同一行）
    "brace-style": [
      1,
      "1tbs",
      {
        allowSingleLine: true,
      },
    ],
    // 关闭驼峰命名规则校验（变量命名时，名称之间不允许下划线，常量（全部大写）除外）
    camelcase: [
      0,
      {
        properties: "always",
      },
    ],
    // 数组和对象键值对最后一个逗号， never参数：不能带末尾的逗号, always参数：必须带末尾的逗号
    // always-multiline：多行模式必须带逗号，单行模式不能带逗号
    // only-multiline：都行
    "comma-dangle": 0, // 在对象或数组中不允许尾随逗号
    "comma-spacing": 0,
    // [1, { // 变量声明，数组文字，对象文字，函数参数和序列中的逗号前后加上一致的间距
    //   'before': false,
    //   'after': true
    // }],
    "comma-style": [1, "last"], // 使用标准逗号样式，逗号位于当前行的末尾。在数组元素，对象属性或变量声明在同一行之后和同一行需要逗号
    "constructor-super": 1, // 子类构造函数必须使用 super()调用父类构造函数。非子类的构造函数不得调用
    curly: [1, "multi-line"], // 当一个块只包含一条语句时，if，else if，else，for，while，或 do 允许省略花括号
    "dot-location": [1, "property"], // 表达式中点"."要求与属性位于同一行
    "eol-last": 0, // 强制文件以换行符结束（文件以一空行结束）
    eqeqeq: [1, "always", { null: "ignore" }], // 强制使用 === 和 !== 但不将此规则应用于 null
    "generator-star-spacing": [
      1,
      {
        // 使用生成器时关键字 * 前后均须有空格
        before: true,
        after: true,
      },
    ],
    "handle-callback-err": [1, "^(err|error)$"], // 回调模式需处理 err 或 error，报告所有未处理的错误
    indent: 0,
    "vue/html-self-closing": 0,
    "vue/html-indent": 0,
    "jsx-quotes": [1, "prefer-single"], // 在 JSX 属性中强制使用单引号
    "key-spacing": [
      1,
      {
        // 对象字面量属性中强制在冒号后放置空格，冒号前不允许空格
        beforeColon: false, // 不允许在对象文字中的键和冒号之间使用空格
        afterColon: true, // 需要在冒号和对象文字中的值之间至少有一个空格
      },
    ],
    "keyword-spacing": [
      1,
      {
        // 关键字前后至少有一个空格
        before: true,
        after: true,
      },
    ],
    // 构造函数名需以大写字母开头，以下内置标识符可免除此规则：Array、Boolean、 Date、Error、Function、Number、Object、RegExp、String、Symbol
    "new-cap": [
      1,
      {
        newIsCap: true, // new 方式调用的函数需以大写开头
        capIsNew: false, // 允许直接调用大写开头的函数，无需使用 new
      },
    ],
    "new-parens": 1, // 使用 new 关键字调用无参构造函数，函数名后强制使用括号
    "no-array-constructor": 1, // 禁止使用数组构造函数
    "no-caller": 1, // 禁止使用 arguments.caller 和 arguments.callee
    "no-console": "off", // 关闭 console 检验规则
    "no-class-assign": 1, // 禁止给类赋值
    "no-cond-assign": 1, // 禁止在条件语句中使用赋值运算符
    "no-const-assign": 1, // 禁止修改 const 关键字声明的变量
    "no-control-regex": 0, // 关闭正则表达式中的控制字符校验规则
    "no-delete-var": 1, // 不允许对变量使用 delete 操作符
    "no-dupe-args": 1, // 不允许在函数声明或表达式中使用重复的参数名称
    "no-dupe-class-members": 1, // 不允许在类的声明中出现重复名称
    "no-dupe-keys": 1, // 不允许在对象中使用重复键
    "no-duplicate-case": 1, // 不允许在 switch 语句的 case 子句中使用重复的 case 分支
    "no-empty-character-class": 1, // 不允许在正则表达式中使用空字符
    "no-empty-pattern": 1, // 对象和数组使用解构时，不允许空模式
    "no-eval": 1, // 禁止使用 eval()函数
    "no-ex-assign": 1, // 禁止给 catch 语句中的异常参数赋值
    "no-extend-native": 2, // 不允许直接修改内置原生对象
    "no-extra-bind": 2, // 避免不必要的函数绑定 bind()
    "no-extra-boolean-cast": 2, // 禁止不必要的布尔转换
    "no-extra-parens": [1, "functions"], // 在使用函数表达式时，禁止使用不必要的括号
    "no-fallthrough": 1, // 禁止 switch 穿透，需使用 break 中断
    "no-floating-decimal": 1, // 禁止省略浮点数中的 0
    "no-func-assign": 1, // 不允许重新分配 function 声明
    "no-implied-eval": 1, // 禁止使用隐式 eval()，即：始终使用函数作为 setTimeout()、setInterval()和execScript()的第一个参数
    "no-inner-declarations": [1, "functions"], // 禁止在块中声明 function
    "no-invalid-regexp": 1, // 不允许 RegExp 构造函数中使用无效的正则表达式字符串
    "no-irregular-whitespace": 1, // 禁止使用无效空格（不是正常的制表符和空格），但允许在字符串中使用任何空格字符
    "no-iterator": 1, // 禁止使用该__iterator__属性
    "no-label-var": 1, // label 标签名不能与变量名相同
    "no-labels": [
      1,
      {
        // 禁止使用标签语句
        allowLoop: false,
        allowSwitch: false,
      },
    ],
    "no-lone-blocks": 1, // 禁止使用不必要的嵌套块
    "no-mixed-spaces-and-tabs": 0, // 不允许使用混合空格和制表符进行缩进
    "no-multi-spaces": 0, // 禁止在逻辑表达式，条件表达式，声明，数组元素，对象属性，序列和函数参数周围使用多个空格
    "no-multi-str": 1, // 禁止使用斜线（\）进行换行
    "no-multiple-empty-lines": 0,
    // [0, {// 禁止多个连续空行，最大连续空行数为 1
    //   'max': 1
    // }],
    "no-native-reassign": 1, // 不允许修改只读全局变量
    "no-negated-in-lhs": 1, // 不允许否定 in 表达式中的左操作数
    "no-new-object": 1, // 不允许使用 Object 构造函数
    "no-new-require": 1, // 不允许使用 new require
    "no-new-symbol": 1, // 禁止使用 Symbol 构造器
    "no-new-wrappers": 1, // 禁止使用原始包装器，new String，new Number 或 new Boolean
    "no-obj-calls": 1, // 禁止将全局对象作为函数调用，即不允许调用 Math，JSON 和 Reflect 对象作为函数
    "no-octal": 1, // 不允许使用八进制
    "no-octal-escape": 1, // 不允许字符串中的八进制转义序列
    "no-path-concat": 1, // node 中避免使用__dirname 和__filename 全局变量进行路径字符串拼接
    "no-proto": 2, // 使用方法 getPrototypeOf 替换__proto__属性
    "no-redeclare": 1, // 不允许同一作用域内声明相同变量名称
    "no-regex-spaces": 1, // 正则表达式文字中不允许有多个空格
    "no-return-assign": [1, "except-parens"], // return 语句中的赋值必需有括号包裹
    "no-self-assign": 2, // 不允许将变量自我赋值
    "no-self-compare": 1, // 禁止变量与自己进行比较操作
    "no-sequences": 1, // 禁止使用逗号运算符，除非在 for 语句的初始化或更新部分，表达式序列显式包含在圆括号中
    "no-shadow-restricted-names": 1, // 禁止对全局对象赋值
    "no-spaced-func": 1, // 功能标识符与其应用程序之间不允许有间距
    "no-sparse-arrays": 1, // 禁止使用稀疏数组，不适用于最后一个元素之后的尾随逗号
    "no-this-before-super": 1, // 使用 this 前请确保 super() 已调用
    "no-throw-literal": 1, // 用 throw 抛错时，抛出 Error 对象而不是字符串
    "no-trailing-spaces": 0, // 禁止在行尾添加尾随空白（空格，制表符和其他 Unicode 空白字符）
    "no-undef": 1, // 禁止引用未声明的变量
    "no-undef-init": 1, // 禁止使用 undefined 来初始化变量
    "no-unexpected-multiline": 1, // 禁止混淆多行表达式
    "no-unmodified-loop-condition": 1, // 检查循环条件内的引用是否在循环中被修改
    "no-unneeded-ternary": [
      1,
      {
        // 当存在更简单的选择时，此规则不允许三元运算符
        defaultAssignment: false, // 不允许将条件表达式作为默认的分配模式
      },
    ],
    "no-unreachable": 1, // return，throw，continue，和 break 语句后不允许跟代码
    "no-unsafe-finally": 1, // 不允许 return，throw，break，和 continue 语句使用 finally 块，即：finally 代码块中不要再改变程序执行流程，但它允许间接使用
    "no-unused-vars": [
      1,
      {
        // 不允许存在声明但未使用的变量，函数和函数的参数
        vars: "all", // 检查全局范围内的变量
        args: "none", // 不检查参数
      },
    ],
    "no-useless-call": 1, // 禁止使用.call()和.apply()
    "no-useless-computed-key": 1, // 禁止不必要的计算属性键作对象属性
    "no-useless-constructor": 1, // 禁止不必要的构造函数
    "no-useless-escape": 0, // 关闭不必要的转义检测规则
    "no-whitespace-before-property": 0, // 对象的属性在同一行上时禁止在属性前使用空格，当对象和属性位于单独的行上时，此规则允许使用空格，因为通常在较长的属性链中添加换行符
    "no-with": 1, // 禁止使用 with
    "one-var": [
      1,
      {
        // 一个变量关键字（var，let 或 const）只声明一个变量
        initialized: "never", // 每个作用域要求多个变量声明用于初始化变量
      },
    ],
    "operator-linebreak": [
      1,
      "after",
      {
        // 使用一致的换行符样式，将换行符放置在运算符之后
        overrides: {
          "?": "before", // 三元运算换行符置于 ? 前
          ":": "before", // 三元运算换行符置于 : 前
        },
      },
    ],
    "padded-blocks": 0, // 在 block 语句和类的开头和结尾处不允许出现空行
    quotes: [
      0,
      "single",
      {
        // 字符串使用单引号
        avoidEscape: true, // 允许字符串使用单引号或双引号，只要字符串包含必须被转义的引号即可
        allowTemplateLiterals: true, // 允许字符串使用反引号
      },
    ],
    semi: [0, "never"], // 禁止使用分号结尾
    "semi-spacing": 0,
    "space-before-blocks": 0, // 代码块前至少有一个空格
    "space-before-function-paren": 0, // 函数名称或 function 关键字与开头括号之间不允许有空格
    "space-in-parens": 0, // 禁止在括号内使用空格
    "space-infix-ops": 1, // 中缀运算符周围需有空格
    "space-unary-ops": [
      1,
      {
        // 一元运算词后须有空格，一元运算符后不允许有空格
        words: true,
        nonwords: false,
      },
    ],
    "spaced-comment": [
      0,
      "always",
      {
        // 注释//和/*后必须跟一个空格
        markers: [
          "global",
          "globals",
          "eslint",
          "eslint-disable",
          "*package",
          "!",
          ",",
        ],
      },
    ],
    "template-curly-spacing": [1, "never"], // 模板字符串${}中不允许有空格
    "use-isnan": 1, // 不允许 NaN 值得直接比较，需使用 isNaN()
    "valid-typeof": 1, // typeof 比较需使用有效字符串（"undefined"，"object"，"boolean"，"number"，"string"，"function"，"symbol"，和"bigint"）
    "wrap-iife": [1, "any"], // 自调用匿名函数 (IIFEs) 使用括号包裹
    "yield-star-spacing": [0, "both"], // yield 表达式前后都要有空格
    yoda: [1, "never"], // 禁止 Yoda 条件（条件的字面值排在第一，而变量排在第二）
    "prefer-const": 1, // 使用 const 声明初始赋值后永远不重新赋值的变量
    "no-debugger": process.env.NODE_ENV === "production" ? 1 : 0, // 生产环境中禁止使用 debugger 调试器
    "object-curly-spacing": 0,
    // [0, 'always', {// 花括号内需要空格（{}除外）
    //   objectsInObjects: false// 以对象元素开始或结束的花括号间不允许有空格
    // }],
    "array-bracket-spacing": 0, // 数组方括号内不允许使用空格
  },
};
