<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-15 16:16:41
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="维修工单" name="付款维修单" lazy>
        <WorkPool />
      </el-tab-pane>
      <el-tab-pane label="维修申诉单" name="维修申诉单" lazy>
        <Appeal />
      </el-tab-pane>
      <el-tab-pane label="安装工单" name="安装工单" lazy>
        <InstallOrder type="INSTALL" />
      </el-tab-pane>
      <el-tab-pane label="换机工单" name="换机工单" lazy>
        <InstallOrder type="EXCHANGE" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import WorkPool from "@/views/order/components/workPool.vue";
import Appeal from "@/views/order/components/appeal.vue";
import InstallOrder from "@/views/order/installOrder.vue";
export default {
  name: "WorkOrder",
  components: { WorkPool, Appeal, InstallOrder },
  data() {
    return {
      activeName: "付款维修单",
    };
  },
};
</script>

<style scoped lang="scss"></style>
