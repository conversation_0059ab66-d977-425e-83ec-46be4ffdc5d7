<template>
  <ProDrawer
    :value="value"
    :title="drawerTitle"
    :top="'10%'"
    size="70%"
    :confirm-loading="confirmLoading"
    :no-footer="true"
    @cancel="closeAppealDrawer"
  >
    <div class="dialog-content-box">
      <div>
        <div class="boxa box0">
          <div class="tit-box">维修进度跟踪</div>
          <div class="sp-content jbxx-box">
            <el-steps :active="stepActive">
              <el-step
                index="REPAIR_COMPLETED"
                title="维修完成时间"
                :description="form.createdAt"
              >
              </el-step>
              <el-step
                index="LAUNCH_APPEAL"
                title="发起申诉时间"
                :description="form.orderReceiveTime"
              ></el-step>
              <el-step
                index="REPLAY_TIME"
                title="回复时间"
                :description="form.departureTime"
              ></el-step>
              <el-step
                index="START_TIME"
                title="出发时间"
                :description="form.actualArriveTime"
              >
              </el-step>
              <el-step
                index="REACH_TIME"
                :description="form.sendReportTime"
                title="到店时间"
              ></el-step>
              <el-step
                index="SUB_REPORT_TIME"
                :description="form.completedAt"
                title="提交报告时间"
              ></el-step
              ><el-step
                index="CONFIRM_REPORT_TIME"
                :description="form.completedAt"
                title="确定报告时间"
              ></el-step
              ><el-step
                index="DONE"
                :description="form.completedAt"
                title="已完成"
              ></el-step>
            </el-steps>
          </div>
        </div>
      </div>
    </div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #actons="{ row }">
        <el-button type="text" size="small" @click="handleDetail(row.id)"
          >查看</el-button
        >
      </template>
    </ProTable>
  </ProDrawer>
</template>

<script>
import { cloneDeep } from "lodash";

export default {
  name: "AppealStep",
  props: {
    value: {
      type: Boolean,
      default: () => false,
    },
    title: {
      type: String,
      default: () => "",
    },
  },
  data() {
    return {
      stepActive: 0,
      drawerTitle: `查看 - ${this.$props.title}`,
      confirmLoading: false,
      form: {},
      stepList: [
        "REPAIR_COMPLETED",
        "LAUNCH_APPEAL",
        "REPLAY_TIME",
        "START_TIME",
        "REACH_TIME",
        "SUB_REPORT_TIME",
        "CONFIRM_REPORT_TIME",
        "DONE",
      ],
      columns: [
        {
          title: "工单编号",
          dataIndex: "code",
          isTable: true,
        },
        {
          title: "品牌/机型",
          dataIndex: "brand",
          isTable: true,
        },
        {
          title: "申诉理由",
          dataIndex: "reason",
          isTable: true,
        },
        {
          title: "相关照片",
          dataIndex: "imgUrl",
          isTable: true,
        },
        {
          title: "上次处理工程师",
          dataIndex: "engineer",
          isTable: true,
        },
        {
          title: "申诉时间",
          dataIndex: "appealTime",
          isTable: true,
        },
        {
          title: "当次工程师",
          dataIndex: "currEngineer",
          isTable: true,
        },
        {
          title: "处理时间",
          dataIndex: "createdAt",
          isTable: true,
        },
        {
          title: "上门处理时间",
          dataIndex: "createdAtm",
          isTable: true,
        },
        {
          title: "操作",
          dataIndex: "action",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {},
    };
  },
  methods: {
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const requestParameters = cloneDeep(this.queryParam);
      this.$refs.ProTable ? (this.$refs.ProTable.listLoading = false) : null;
      // getWorkOrderByPageApi(requestParameters)
      //     .then((res) => {
      //       this.tableData = res.data.rows;
      //       this.localPagination.total = parseInt(res.data.total);
      //     })
      //     .finally(() => {
      //       this.$refs.ProTable
      //           ? (this.$refs.ProTable.listLoading = false)
      //           : null;
      //     })
      //     .catch(() => {});
    },
    handleDetail(id) {
      this.$emit("handleRowDetail", id);
    },
    closeAppealDrawer() {
      this.$emit("cancel", false);
    },
  },
};
</script>

<style scoped lang="scss">
.dialog-content-box {
  position: relative;
  //height: 100%;
  overflow: scroll;
  padding-bottom: 80px;
  .steps-box {
    position: absolute;
    width: 80%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    z-index: 2;
  }

  .content-fix {
    height: calc(100vh - 110px);
    overflow: auto;
  }

  .tit-box {
    width: 100%;
    padding: 5px 10px;
    color: #409eff;
    position: relative;
    margin: 20px auto;
    font-size: 16px;
    font-weight: 800;

    &::before {
      content: "";
      width: 5px;
      height: 20px;
      background: #409eff;
      display: inline-block;
      position: absolute;
      left: -1px;
      top: 4px;
    }
  }
}
</style>
