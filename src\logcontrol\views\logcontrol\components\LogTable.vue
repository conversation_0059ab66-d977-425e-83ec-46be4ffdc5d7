<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 日志表格组件
-->
<template>
  <div class="log-table">
    <el-table 
      :data="data" 
      v-loading="loading"
      @row-click="handleRowClick"
      stripe
      border
    >
      <el-table-column prop="deviceId" label="设备ID" width="140" show-overflow-tooltip />
      <el-table-column prop="userCode" label="用户编码" width="100" />
      <el-table-column prop="userName" label="用户姓名" width="100" show-overflow-tooltip />
      <el-table-column prop="level" label="级别" width="80">
        <template slot-scope="scope">
          <el-tag :type="getLogLevelType(scope.row.level)" size="small">
            {{ scope.row.level }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="logType" label="类型" width="100">
        <template slot-scope="scope">
          <el-tag :type="getLogTypeType(scope.row.logType)" size="mini">
            {{ scope.row.logType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="tag" label="标签" width="180" show-overflow-tooltip />
      <el-table-column prop="message" label="日志内容" min-width="200" show-overflow-tooltip />
      <el-table-column prop="timestamp" label="时间戳" width="160" />
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <div class="fixed-width">
            <el-button size="small" @click="handleViewDetail(scope.row)">查看详情</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        :current-page="pagination.current"
        :page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'LogTable',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    pagination: {
      type: Object,
      default: () => ({
        current: 1,
        size: 10,
        total: 0
      })
    }
  },
  methods: {
    getLogLevelType(level) {
      const types = {
        'DEBUG': 'info',
        'INFO': 'success',
        'WARN': 'warning',
        'ERROR': 'danger'
      }
      return types[level] || ''
    },

    getLogTypeType(logType) {
      const types = {
        'LOCATION': 'primary',
        'BUSINESS': 'success',
        'CRASH': 'danger',
        'PERFORMANCE': 'warning'
      }
      return types[logType] || ''
    },
    
    handleRowClick(row) {
      this.$emit('row-click', row)
    },
    
    handleViewDetail(row) {
      this.$emit('row-click', row)
    },
    
    handleSizeChange(size) {
      this.$emit('size-change', size)
    },
    
    handleCurrentChange(current) {
      this.$emit('page-change', current)
    }
  }
}
</script>

<style lang="scss" scoped>
.log-table {
  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
