<!--
 * @Description: 选择人员
 * @Autor: shh
 * @Date: 2022-11-16 16:42:14
 * @LastEditors: shh
 * @LastEditTime: 2023-12-01 11:00:00
-->
<template>
  <ProDialog
    :value="dialogVisible"
    title="选择人员"
    width="50%"
    :top="'30px'"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-selection
      :row-key="getRowKeys"
      :show-pagination="true"
      :reserve-selection="true"
      :query-param="queryParam"
      :data="tableData"
      :local-pagination="localPagination"
      sticky
      :height="400"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #sex="slotProps">
        {{ slotProps.row.sex.label }}
      </template>
    </ProTable>
  </ProDialog>
</template>
<script>
import { departMemberPageApi } from "@/api/user";
import { isEmpty, cloneDeep } from "lodash";
import { filterName, getAllParentArr } from "@/utils";
export default {
  name: "StaffList",
  components: {},
  mixins: [],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    roleId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      tableData: [],
      selectDataall: [],
      selectData: [],
      queryParam: {
        isFilter: true,
        spareName: null,
      },
      columns: [
        {
          dataIndex: "code",
          title: "账号",
          isTable: true,
        },
        {
          dataIndex: "name",
          title: "姓名",
          isTable: true,
          isSearch: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "mobileNumber",
          title: "电话",
          isTable: true,
        },
        {
          dataIndex: "sex",
          tableSlot: "sex",
          title: "性别",
          isTable: true,
        },
      ],
    };
  },

  computed: {},

  watch: {},
  created() {},

  mounted() {},
  methods: {
    getRowKeys(row) {
      return row.id;
    },
    loadData(parameter) {
      this.tableData = [];
      // this.localPagination.total = 0;
      this.queryParam.spareName = parameter.spareName;
      const requestParameters = Object.assign({}, parameter, this.queryParam);
      departMemberPageApi(this.roleId, requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);

          this.$nextTick(function () {
            if (this.selectDataall?.length > 0) {
              this.selectDataall.forEach((row) => {
                if (
                  this.tableData.find((item) => {
                    return item?.id === row?.spareId;
                  })
                ) {
                  this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
                    this.tableData.find((item) => {
                      return item?.id === row?.spareId;
                    }),
                    true
                  );
                }
              });
            }
          });
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    handleSelectionChange(data) {
      if (this.selectDataall.length > 0) {
        this.selectDataall.forEach((item) => {
          data.forEach((it) => {
            if (item.spareId == it.id) {
              it.expectNum = item.expectNum;
            }
          });
        });
      }

      setTimeout(() => {
        this.selectData = cloneDeep(data);
      }, 500);
    },
    handleOk() {
      const arr = [];
      this.selectData.forEach((el) => {
        arr.push(el.id);
      });
      this.$emit("ok", arr);
    },
    handleCancel() {
      this.$emit("cancel");
    },
  },
};
</script>
<style lang="scss" scoped></style>
