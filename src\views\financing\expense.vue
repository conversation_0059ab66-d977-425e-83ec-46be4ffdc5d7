<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:22
 * @Description: 抄表价格
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <!--<el-button-->
        <!--  type="success"-->
        <!--  icon="el-icon-plus"-->
        <!--  size="mini"-->
        <!--  @click="handleEdit(null, 'add')"-->
        <!--&gt;-->
        <!--  新增-->
        <!--</el-button>-->
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <el-button icon="el-icon-edit" @click="handleEdit(row, 'edit')">
            编辑
          </el-button>
          <el-button
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row.id)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDialog
      :value="dialogVisible"
      :confirm-btn-loading="confirmBtnLoading"
      width="500px"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="editForm"
        :form-list="formColumns"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :confirm-loading="formConfirmLoading"
        :open-type="editType"
        @proSubmit="proSubmit"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam, filterParamRange, transformFormParams } from "@/utils";
import { cloneDeep } from "lodash";
import {
  meterRepairPriceAddApi,
  meterRepairPriceDelApi,
  meterRepairPriceEditApi,
  meterRepairPricePageApi,
} from "@/api/finance";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "MeterExpense",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          formatter: (row) => row.deviceGroup?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(700),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "mechine",
          title: "机型",
          isTable: true,
        },
        {
          dataIndex: "productIds",
          title: "机型",
          isSearch: true,
          valueType: "product",
        },
        // {
        //   dataIndex: "printType",
        //   title: "色彩类型",
        //   isTable: true,
        //   formatter: (row) => row.printType?.label,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [
        //     {
        //       label: "彩色",
        //       value: "1701",
        //     },
        //     {
        //       label: "黑色",
        //       value: "1702",
        //     },
        //   ],
        // },
        {
          dataIndex: "printInterval",
          title: "印量区间",
          isTable: true,
          formatter: (row) => `${row.startCount} ~ ${row.endCount}`,
        },
        {
          dataIndex: "blackWhitePrice",
          title: "黑白价格",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
        },
        {
          dataIndex: "colorPrice",
          title: "彩色价格",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
        },
        {
          dataIndex: "fiveColourPrice",
          title: "五彩价格",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
        },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   tableSlot: "action",
        //   width: 200,
        // },
      ],
      tableData: [],
      // Dialog
      dialogVisible: false,
      confirmBtnLoading: false,
      // From
      editForm: {},
      formColumns: [
        {
          dataIndex: "printType",
          title: "色彩类型",
          isForm: true,
          valueType: "select",
          disabled: false,
          option: [
            {
              label: "彩色",
              value: "1701",
            },
            {
              label: "黑色",
              value: "1702",
            },
          ],
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请选择色彩类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "printInterval",
          title: "印量区间",
          isForm: true,
          valueType: "inputRange",
          disabled: false,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入印量区间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "price",
          title: "价格",
          isForm: true,
          valueType: "input-number",
          disabled: false,
          step: 0.001,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入费用",
              trigger: "blur",
            },
          ],
        },
      ],
      formConfirmLoading: false,
      editType: "add",
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          priceStart: null,
          priceEnd: null,
          data: parameter.price,
        },
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.price;
      delete requestParameters.createdAt;
      meterRepairPricePageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.editForm = {}; // 初始化为空对象
      this.editType = type;
      const rowData = cloneDeep(row);
      this.setFormColumns(type);
      if (!row && type === "add") {
        this.dialogVisible = true;
        return;
      }
      if (row) {
        this.editForm = transformFormParams(rowData);
        this.editForm.printInterval = [row?.startCount, row?.endCount];
      }
      this.dialogVisible = true;
    },
    setFormColumns(type) {
      this.formColumns.forEach((item) => {
        if (type === "edit") {
          item.disabled = item.dataIndex !== "price";
        } else if (type === "add") {
          item.disabled = false;
        }
      });
    },
    handleDelete(id) {
      this.$confirm("此操作将删除该条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        meterRepairPriceDelApi(id).then((res) => {
          this.$message.success("操作成功");
          this.refresh();
        });
      });
    },
    async proSubmit(val) {
      try {
        this.confirmBtnLoading = true;
        this.formConfirmLoading = true;
        if (val.printInterval && Array.isArray(val.printInterval)) {
          val.startCount = val.printInterval[0];
          val.endCount = val.printInterval[1];
          // delete val.printInterval;
        }
        const editApi =
          this.editType === "add"
            ? meterRepairPriceAddApi
            : meterRepairPriceEditApi;
        const result = await editApi(val);
        if (result.code === 200) {
          this.$message.success("操作成功");
          this.handleDialogCancel();
          this.refresh();
        }
      } finally {
        this.confirmBtnLoading = false;
        this.formConfirmLoading = false;
      }
    },
    resetEditForm() {
      Object.keys(this.editForm).forEach((key) => {
        // this.editForm[key] = "";
        delete this.editForm[key];
      });
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    handleDialogCancel() {
      this.dialogVisible = false;
      this.$nextTick(() => {
        this.resetEditForm();
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
