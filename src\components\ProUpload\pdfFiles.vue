<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-29 16:55:56
 * @Description: 
 -->
<template>
  <div class="upload" :class="objClass">
    <el-upload
      ref="upload"
      action=""
      :http-request="uploadFile"
      :on-preview="handlePreview"
      :file-list="fileList"
      :limit="limit"
      :accept="accept"
      :multiple="multiple"
      :disabled="disabled"
      :on-exceed="handleExceed"
      :before-remove="handleRemove"
      :before-upload="checkFileType"
      :class="{ 'disable-upload': disabled }"
    >
      <el-button
        v-if="!disabled"
        size="small"
        type="primary"
        :disabled="disabled"
      >
        点击上传
      </el-button>
      <div v-if="!disabled" slot="tip" class="el-upload__tip">
        仅支持PDF和图片格式(jpg、jpeg、png)，文件大小不超过{{ fileSize }}M
      </div>
    </el-upload>

    <!-- 图片预览弹窗 -->
    <el-dialog
      :visible.sync="showPreview"
      top="5vh"
      :modal="false"
      :append-to-body="true"
    >
      <el-image
        v-if="isImage"
        class="preview-img"
        width="80%"
        :src="previewUrl"
        alt=""
      />
    </el-dialog>
  </div>
</template>

<script>
import { uploadFile } from "@/api/upload";
import { Message } from "element-ui";
import { mapGetters } from "vuex";

export default {
  name: "FileUpload",
  props: {
    fileList: {
      type: Array,
      default: () => [],
    },
    limit: {
      type: Number,
      default: 5,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    fileSize: {
      type: Number,
      default: 10,
    },
    isAuth: {
      type: Boolean,
      default: false,
    },
    permitCode: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      showPreview: false,
      previewUrl: "",
      isImage: false,
      accept: ".pdf,image/jpg,image/jpeg,image/png",
      fileType: ["application/pdf", "image/jpeg", "image/png", "image/jpg"],
      uploadedFiles: [],
      objClass: {
        upLoadShow: true,
        upLoadHide: false,
      },
    };
  },
  computed: {
    ...mapGetters({ permits: "permits" }),
  },
  methods: {
    async uploadFile(val) {
      try {
        const { file } = val;
        const result = await uploadFile(file);
        this.uploadedFiles.push(result);
        if (this.uploadedFiles.length === this.limit) {
          this.objClass.upLoadHide = true;
          this.objClass.upLoadShow = false;
        }

        this.$emit("uploadSuccess", result);
        // Message.success("上传成功");
      } catch (error) {
        Message.error(error.message);
      }
    },
    handlePreview(file) {
      const fileName = file.name.toLowerCase();
      const isPdf = fileName.endsWith(".pdf");
      const isImage =
        fileName.endsWith(".jpg") ||
        fileName.endsWith(".jpeg") ||
        fileName.endsWith(".png");
      this.isImage = isImage;

      if (isPdf) {
        if (
          this.isAuth &&
          !this.permits.some((item) => item.permit === this.permitCode)
        ) {
          this.$message.error("您没有权限下载文件");
          return;
        }
        // PDF文件直接下载
        const a = document.createElement("a");
        a.href = file.url;
        a.setAttribute("download", file.name);
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      } else if (isImage) {
        // 图片文件预览
        this.showPreview = true;
        this.previewUrl = file.url;
      } else {
        // 其他文件类型处理
        this.$message.warning("不支持的文件类型");
      }
    },
    handleExceed() {
      Message.warning(`最多只能上传${this.limit}个文件`);
    },
    handleRemove(file) {
      this.uploadedFiles = this.uploadedFiles.filter(
        (item) => item.uid !== file.uid
      );
      this.objClass.upLoadShow = true;
      this.objClass.upLoadHide = false;
      this.$emit("uploadRemove", file);
    },
    checkFileType(file) {
      const { type, size } = file;
      let errorMessage = "";

      if (!this.fileType.includes(type)) {
        errorMessage = "仅支持上传PDF和图片格式(jpg、jpeg、png)的文件";
      }

      if (size > this.fileSize * 1024 * 1024) {
        if (!errorMessage) {
          errorMessage = `文件大小不能超过${this.fileSize}M`;
        } else {
          errorMessage += `，且文件大小不能超过${this.fileSize}M`;
        }
      }
      if (errorMessage) {
        Message.error(errorMessage);
        return false;
      }
      return true;
    },
  },
};
</script>

<style lang="scss" scoped>
.disable-upload {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
  ::v-deep .el-upload--text {
    display: none;
  }
}

.preview-img {
  display: block;
  margin: 0 auto;
  z-index: 10002;
}

.upload {
  display: flex;
  padding-left: 15px;
}

.upLoadHide .el-upload {
  display: none;
}
</style>
