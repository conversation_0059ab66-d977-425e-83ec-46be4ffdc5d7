<template>
  <div class="website-images">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div>
          <h1 class="page-title">
            <i class="el-icon-picture-outline" style="margin-right: 8px;"></i>
            图片管理
          </h1>
          <p class="page-description">管理网站图片，查看使用情况，清理未使用的图片</p>
        </div>
        <div class="header-actions">
          <el-button type="default" icon="el-icon-refresh" @click="refreshData" :loading="loading">
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <el-row :gutter="16" class="stats-row" v-if="statistics">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background-color: #1890ff20; color: #1890ff;">
              <i class="el-icon-picture"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.totalCount }}</div>
              <div class="stat-label">总图片数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background-color: #52c41a20; color: #52c41a;">
              <i class="el-icon-check"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.usedImages }}</div>
              <div class="stat-label">使用中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background-color: #faad1420; color: #faad14;">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.unusedImages }}</div>
              <div class="stat-label">未使用</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" style="background-color: #722ed120; color: #722ed1;">
              <i class="el-icon-info"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value" :style="{ color: statistics.usageRate > 80 ? '#3f8600' : '#cf1322' }">
                {{ statistics.usageRate ? statistics.usageRate.toFixed(1) : '0.0' }}%
              </div>
              <div class="stat-label">使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作提示 -->
    <el-alert
      title="图片管理说明"
      description="系统会自动扫描所有图片的使用情况，包括配置页面和内容页面。未使用的图片可以安全删除以节省存储空间。切换到'未使用'标签页可以进行批量删除操作。"
      type="info"
      show-icon
      :closable="false"
      style="margin-bottom: 16px;"
    />

    <!-- 错误信息显示 -->
    <el-alert
      v-if="errorMessage"
      title="数据加载错误"
      :description="errorMessage"
      type="error"
      show-icon
      :closable="false"
      style="margin-bottom: 16px;"
    />

    <!-- 图片列表 -->
    <el-card class="images-card">
      <div class="card-header">
        <el-tabs v-model="activeTab" @tab-click="handleTabChange" type="card">
          <el-tab-pane :label="`全部 (${allImages.length})`" name="all">
            <!-- 内容在下方统一显示 -->
          </el-tab-pane>
          <el-tab-pane :label="`使用中 (${statistics?.usedImages || 0})`" name="used">
            <!-- 内容在下方统一显示 -->
          </el-tab-pane>
          <el-tab-pane name="unused">
            <span slot="label">
              未使用 ({{ statistics?.unusedImages || 0 }})
              <el-tag v-if="statistics?.unusedImages > 0" size="mini" type="warning" style="margin-left: 4px;">可批量删除</el-tag>
            </span>
            <!-- 内容在下方统一显示 -->
          </el-tab-pane>
        </el-tabs>

        <div class="card-actions" v-if="selectedImages.length > 0 && activeTab === 'unused'">
          <el-button type="danger" @click="batchDeleteImages" :loading="deleting">
            批量删除 ({{ selectedImages.length }})
          </el-button>
        </div>
      </div>

      <el-table
        :data="paginatedImages"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column
          type="selection"
          width="55"
          :selectable="row => activeTab === 'unused' && row.canDelete"
        />
        
        <el-table-column label="图片预览" width="80">
          <template #default="{ row }">
            <el-image
              :src="row.cosUrl"
              :alt="row.imageName"
              style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;"
              :preview-src-list="[row.cosUrl]"
              fit="cover"
            />
          </template>
        </el-table-column>

        <el-table-column label="图片信息" min-width="200">
          <template #default="{ row }">
            <div>
              <div style="font-weight: 500; margin-bottom: 4px;">{{ row.imageName }}</div>
              <div style="font-size: 12px; color: #8c8c8c; margin-bottom: 2px;">
                分类：{{ getCategoryText(row.category) }} | 大小：{{ formatFileSize(row.fileSize) }}
              </div>
              <div style="font-size: 11px; color: #bfbfbf;">
                创建时间：{{ formatDate(row.createdAt) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="使用状态" width="160">
          <template #default="{ row }">
            <div>
              <div v-if="row.inUse">
                <el-tag type="success" size="small">
                  <i class="el-icon-check" style="margin-right: 4px;"></i>
                  使用中
                </el-tag>
                <div style="font-size: 11px; color: #8c8c8c; margin-top: 4px;">
                  {{ row.usageCount }} 处引用
                </div>
              </div>
              <div v-else>
                <el-tag type="danger" size="small">
                  <i class="el-icon-close" style="margin-right: 4px;"></i>
                  未使用
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="mini" icon="el-icon-view" @click="viewImage(row)">详情</el-button>
              <el-popconfirm
                v-if="row.canDelete"
                :title="row.riskDescription || '确定要删除这张图片吗？'"
                @confirm="deleteImage(row)"
              >
                <template #reference>
                  <el-button
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                  >
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredImages.length"
          :show-total="showTotal"
        />
      </div>
    </el-card>



    <!-- 查看图片对话框 -->
    <el-dialog
      title="图片详情"
      :visible.sync="viewDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal-append-to-body="true"
      custom-class="image-detail-dialog"
    >
      <div v-if="selectedImage" class="image-detail">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-image
              :src="selectedImage.cosUrl"
              :alt="selectedImage.imageName"
              style="width: 100%; max-height: 200px; object-fit: cover;"
            />
          </el-col>
          <el-col :span="16">
            <div class="image-info">
              <div class="info-item">
                <strong>图片名称：</strong>{{ selectedImage.imageName }}
              </div>
              <div class="info-item">
                <strong>分类：</strong>{{ getCategoryText(selectedImage.category) }}
              </div>
              <div class="info-item">
                <strong>文件大小：</strong>{{ formatFileSize(selectedImage.fileSize) }}
              </div>
              <div class="info-item">
                <strong>创建时间：</strong>{{ formatDate(selectedImage.createdAt) }}
              </div>
              <div class="info-item">
                <strong>使用状态：</strong>
                <el-tag :type="selectedImage.inUse ? 'success' : 'danger'">
                  {{ selectedImage.inUse ? `使用中 (${selectedImage.usageCount} 处)` : '未使用' }}
                </el-tag>
              </div>
              <div class="info-item" v-if="selectedImage.inUse && selectedImage.usageDetails && selectedImage.usageDetails.length > 0">
                <strong>使用位置详情：</strong>
                <div class="usage-details" style="margin-top: 8px;">
                  <div
                    v-for="detail in selectedImage.usageDetails"
                    :key="detail.id || detail.locationDescription"
                    class="usage-detail-item"
                    style="margin-bottom: 12px; padding: 8px; border: 1px solid #e8e8e8; border-radius: 4px;"
                  >
                    <div style="margin-bottom: 4px;">
                      <el-tag
                        :type="detail.locationType === 'CONFIG' ? 'primary' : 'success'"
                        size="small"
                        style="margin-right: 8px;"
                      >
                        {{ detail.locationType === 'CONFIG' ? '配置' : '内容' }}
                      </el-tag>
                      <span style="font-weight: 500;">{{ detail.pageName }} - {{ detail.contentTitle }}</span>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-bottom: 2px;">
                      {{ detail.locationDescription }}
                    </div>
                    <div style="font-size: 11px; color: #999;">
                      最后更新：{{ formatDate(detail.lastUpdated) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { get, del } from '@/utils/request'

export default {
  name: 'WebsiteImages',
  data() {
    return {
      loading: false,
      deleting: false,
      errorMessage: '',

      // 图片数据
      allImages: [],
      statistics: null,
      selectedImages: [],
      selectedImage: null,

      // 分页
      pagination: {
        current: 1,
        size: 10
      },

      // 选项卡
      activeTab: 'all',

      // 对话框状态
      viewDialogVisible: false
    }
  },
  
  computed: {
    // 过滤后的图片
    filteredImages() {
      switch (this.activeTab) {
        case 'used':
          return this.allImages.filter(img => img.inUse)
        case 'unused':
          return this.allImages.filter(img => !img.inUse)
        default:
          return this.allImages
      }
    },

    // 分页后的图片数据
    paginatedImages() {
      const start = (this.pagination.current - 1) * this.pagination.size
      const end = start + this.pagination.size
      return this.filteredImages.slice(start, end)
    }
  },

  watch: {
    // 监听过滤后的数据变化，重置分页
    filteredImages() {
      this.pagination.current = 1
    }
  },

  mounted() {
    this.loadData()
  },
  
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      this.errorMessage = ''

      try {
        // 使用与React项目相同的API端点
        const [imagesRes, statsRes] = await Promise.all([
          get('/website/image-management/analyze-all'),
          get('/website/image-management/statistics')
        ])

        // 处理API响应结构，与React项目保持一致
        const images = Array.isArray(imagesRes.data.data) ? imagesRes.data.data :
                      Array.isArray(imagesRes.data) ? imagesRes.data : []
        this.allImages = images

        // 处理统计数据
        const statsData = statsRes.data.data || statsRes.data
        const totalImages = typeof statsData.totalImages === 'string' ? parseInt(statsData.totalImages) : (statsData.totalImages || 0)
        const usedImages = typeof statsData.usedImages === 'string' ? parseInt(statsData.usedImages) : (statsData.usedImages || 0)
        const unusedImages = typeof statsData.unusedImages === 'string' ? parseInt(statsData.unusedImages) : (statsData.unusedImages || 0)

        this.statistics = {
          totalCount: totalImages,
          usedImages: usedImages,
          unusedImages: unusedImages,
          usageRate: totalImages > 0 ? (usedImages / totalImages) * 100 : 0
        }

      } catch (error) {
        console.error('加载数据失败:', error)
        this.errorMessage = `图片数据加载失败: ${error.message || '未知错误'}`
        this.$message.error('加载数据失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    
    // 刷新数据
    refreshData() {
      this.loadData()
    },
    
    // 切换选项卡
    handleTabChange() {
      this.selectedImages = []
      // 重置分页到第一页
      this.pagination.current = 1
    },
    
    // 选择变化
    handleSelectionChange(selection) {
      // 只在"未使用"标签页时允许选择
      if (this.activeTab === 'unused') {
        this.selectedImages = selection
      } else {
        this.selectedImages = []
      }
    },
    
    // 分页变化
    handleSizeChange(size) {
      this.pagination.size = size
    },
    
    handleCurrentChange(current) {
      this.pagination.current = current
    },
    

    
    // 查看图片
    viewImage(image) {
      this.selectedImage = image
      this.viewDialogVisible = true
    },
    

    
    // 删除图片
    async deleteImage(image) {
      try {
        // 保持原始的imageId格式，避免精度丢失
        const imageId = image.imageId
        // 直接传递数组作为请求体，与Vue项目的del方法实现保持一致
        const response = await del('/website/image-management/unused', [imageId])

        const data = response.data
        this.$message.success(`成功删除 ${data.deletedCount} 张图片`)

        // 显示错误信息（如果有）
        if (data.errors && data.errors.length > 0) {
          data.errors.forEach(error => this.$message.warning(error))
        }

        this.loadData()
      } catch (error) {
        console.error('删除失败:', error)
        this.$message.error('删除失败：' + (error.message || '未知错误'))
      }
    },
    
    // 批量删除图片
    batchDeleteImages() {
      // 过滤出可删除的图片
      const deletableImages = this.selectedImages.filter(img => img.canDelete)
      // 保持原始的imageId格式，避免精度丢失
      const deletableIds = deletableImages.map(img => img.imageId)

      if (deletableIds.length === 0) {
        this.$message.warning('请选择可删除的图片')
        return
      }

      this.$confirm(`确定要删除选中的 ${deletableIds.length} 张未使用图片吗？`, '批量删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.deleting = true
          // 直接传递数组作为请求体，与Vue项目的del方法实现保持一致
          const response = await del('/website/image-management/unused', deletableIds)

          const data = response.data
          this.$message.success(`成功删除 ${data.deletedCount} 张图片`)

          // 显示错误信息（如果有）
          if (data.errors && data.errors.length > 0) {
            data.errors.forEach(error => this.$message.warning(error))
          }

          this.selectedImages = []
          this.loadData()
        } catch (error) {
          console.error('批量删除失败:', error)
          this.$message.error('删除失败：' + (error.message || '未知错误'))
        } finally {
          this.deleting = false
        }
      })
    },
    
    // 显示总数信息
    showTotal(total, range) {
      return `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
    },
    
    // 获取分类文本
    getCategoryText(category) {
      const categoryMap = {
        'banner': '横幅图片',
        'service': '服务图片',
        'team': '团队图片',
        'case': '案例图片',
        'other': '其他图片'
      }
      return categoryMap[category] || category
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    // 格式化日期
    formatDate(dateString) {
      return new Date(dateString).toLocaleString()
    }
  }
}
</script>

<style scoped>
.website-images {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.page-description {
  margin: 8px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
  margin-top: 4px;
}

.images-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  margin-top: 16px;
  text-align: right;
}

.image-detail {
  padding: 16px 0;
}

.image-info {
  padding-left: 16px;
}

.info-item {
  margin-bottom: 12px;
  line-height: 1.5;
}

.info-item strong {
  color: #262626;
  margin-right: 8px;
}

.usage-locations {
  margin-top: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  min-width: 160px;
}

.action-buttons .el-button {
  flex-shrink: 0;
}

/* 减少tab-pane的空间占用 */
.images-card .el-tabs__content {
  padding: 0;
  margin: 0;
  min-height: 0;
}

.images-card .el-tab-pane {
  padding: 0;
  margin: 0;
  min-height: 0;
}

.images-card .el-tabs {
  margin-bottom: 16px;
}

/* 表格优化 - 减少切换标签页时的抖动 */
::v-deep .el-table {
  transition: all 0.2s ease-in-out;
}

::v-deep .el-table .el-table__header-wrapper,
::v-deep .el-table .el-table__body-wrapper {
  transition: all 0.2s ease-in-out;
}

/* 选择框列的样式优化 */
::v-deep .el-table .el-table-column--selection .el-checkbox {
  transition: opacity 0.2s ease-in-out;
}

/* 修复对话框蒙版问题 - 确保图片详情对话框在最上层 */
::v-deep .image-detail-dialog {
  z-index: 9999 !important;
}

::v-deep .image-detail-dialog .el-dialog__wrapper {
  z-index: 9999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

::v-deep .image-detail-dialog .el-overlay {
  z-index: 9998 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* 确保对话框内容可以正常交互 */
::v-deep .image-detail-dialog .el-dialog__body,
::v-deep .image-detail-dialog .el-dialog__footer {
  position: relative;
  z-index: 10001 !important;
  pointer-events: auto !important;
}

/* 确保按钮可以正常点击 */
::v-deep .image-detail-dialog .el-button {
  pointer-events: auto !important;
  z-index: 10002 !important;
  position: relative;
}
</style>
