<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-15 14:42:04
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-30 18:02:44
 * @FilePath: src/views/dispose/components/registrationProtocol.vue
 * @Description: 注册协议
 * 
-->
<template>
  <div class="app-container">
    <div class="edit">
      <WangeEditor ref="WangeEditorRef" :content="register" :height="600" />
    </div>
    <div>
      <el-button
        type="primary"
        size="small"
        style="margin-top: 20px"
        @click="handleSubmit"
      >
        保存注册协议
      </el-button>
    </div>
  </div>
</template>

<script>
import WangeEditor from "@/components/ProWangeEditor/index.vue";
import { setBaseCompanyInfoApi } from "@/api";
export default {
  name: "RegistrationProtocol",
  components: {
    WangeEditor,
  },
  props: {
    register: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      content: "",
      registerProtocol: "",
    };
  },
  register: {
    handler(val) {
      this.$refs.WangeEditorRef?.echo(val);
    },
  },
  // created() {
  //   this.$refs.WangeEditorRef.echo(this.register);
  // },
  methods: {
    handleSubmit() {
      const result = this.$refs.WangeEditorRef.getContent();
      if (!this.id) {
        this.$message.error("请先保存收款配置信息");
        return;
      }
      this.$emit("save", { registerProtocol: result });
      // setBaseCompanyInfoApi({ id: this.id, registerProtocol: result }).then(
      //   (res) => {
      //     console.log(res);
      //     this.$message.success("配置保存成功");
      //   }
      // );
    },
  },
};
</script>

<style scoped lang="scss">
.edit {
  text-align: left;
}
</style>
