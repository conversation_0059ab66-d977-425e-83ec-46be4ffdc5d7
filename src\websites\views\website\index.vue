<template>
  <div id="website-frontend-container" style="width: 100%; height: 100vh;">
    <!-- 使用iframe来加载React前端 -->
    <iframe
      ref="reactFrame"
      src="/website/index.html"
      style="width: 100%; height: 100%; border: none;"
      @load="onFrameLoad"
      @error="onFrameError"
    ></iframe>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载企业网站前端...</div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <div class="error-message">{{ error }}</div>
      <button @click="reload" class="retry-button">重新加载</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WebsiteFrontend',
  data() {
    return {
      loading: true,
      error: null
    }
  },
  mounted() {
    // 设置一个短暂的加载状态
    setTimeout(() => {
      this.loading = false;
    }, 1000);
  },
  methods: {
    onFrameLoad() {
      this.loading = false;
      this.error = null;
      console.log('React前端加载成功');
    },

    onFrameError() {
      this.loading = false;
      this.error = '无法加载企业网站前端，请检查网络连接或联系管理员';
      console.error('React前端加载失败');
    },

    reload() {
      this.loading = true;
      this.error = null;
      this.$refs.reactFrame.src = this.$refs.reactFrame.src;
    }
  }
}
</script>

<style scoped>
#website-frontend-container {
  position: relative;
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 16px;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.error-message {
  color: #e74c3c;
  font-size: 16px;
  margin-bottom: 20px;
  text-align: center;
  max-width: 400px;
}

.retry-button {
  padding: 10px 20px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.retry-button:hover {
  background-color: #2980b9;
}
</style>
