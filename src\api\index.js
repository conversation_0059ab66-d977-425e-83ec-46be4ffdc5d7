/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 11:20:17
 * @Description:
 */
import axios from "axios";
import { get, post, put, del, down } from "@/utils/request";

export const uploadApi = `/api/file/upload`; // 文件上传

export const ExportApi = (id) => down(`/report/download/${id}`); // 详情
// 获取实时统计数据
export const getBigViewRealTimeData = (params) => get(`/index`, params);
// 获取工单数量统计
export const getBigViewWorkOrderData = (type) =>
  get(`/statisics/queryWorkStatistics?type=${type}`);
// 获取异常工单数据
export const getBigViewExceptionData = (day) =>
  get(`/statisics/workOrderException?num=${day}`);
// 获取客户评价数据
export const getBigViewEvaluationData = (day) =>
  get(`/statisics/customerEvaluate?num=${day}`);
// 根据选择获取销售统计金额
export const getBigViewSalesData = (type) => get(`/index/sales/${type}`);
// 根据区域编码获取机器分布数量
export const getBigViewMachineData = (code) => get(`/index/machine/${code}`);
// 获取基础配置信息
export const getBaseCompanyInfoApi = (data) => get("/company", data);
// 配置基础信息
export const setBaseCompanyInfoApi = (data) => post("/company", data);
