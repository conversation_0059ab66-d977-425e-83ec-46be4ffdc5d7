<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      row-key="label"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="550"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-refresh"
          @click="reload"
        >
          重新计算
        </el-button>
        <!--<el-button-->
        <!--  type="success"-->
        <!--  size="mini"-->
        <!--  icon="el-icon-plus"-->
        <!--  @click="handleAddMeter"-->
        <!--  >-->
        <!--  新增对账单-->
        <!--</el-button>-->
      </template>
      <template #deviceTree>
        <el-cascader
          v-model="productIdName"
          filterable
          clearable
          collapse-tags
          style="width: 250px"
          :options="productTreeOption"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          @change="handleProductTree"
        >
        </el-cascader>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-circle-check"
            @click="showDetail(slotProps.row)"
          >
            详情
          </el-button>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-edit-outline"
            @click="resetCounter(slotProps.row)"
          >
            调整
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 重新计算 -->
    <ProDialog
      :value="reloadDrawer"
      title="重新统计打印数量"
      width="60%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="reloadDrawer = false"
    >
      <div class="tit-boxs">
        <el-button type="success" @click="showDialogFn">选择客户信息</el-button>
      </div>
      <el-form
        ref="proform_child1"
        label-width="140px"
        class="demo-ruleForm"
        :rules="
          reloadForm.colorAdjust || reloadForm.blackWhiteAdjust
            ? reloadFormRules
            : reloadFormRulesReset
        "
        :model="reloadForm"
      >
        <el-row>
          <el-col :span="24" style="display: flex; flex-wrap: wrap">
            <el-form-item label="客户编码:">
              <el-input
                v-model="reloadForm.seqId"
                placeholder="请输入客户编码"
                style="width: 300px"
                disabled
              ></el-input>
            </el-form-item>
            <el-form-item label="设备组:" prop="deviceGroupId">
              <el-select
                v-model="reloadForm.deviceGroupId"
                style="width: 300px"
                placeholder="请选择设备组"
              >
                <el-option
                  v-for="item in deviceGroupList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="年月:" prop="cycle">
              <el-date-picker
                v-model="reloadForm.cycle"
                style="width: 300px"
                type="month"
                placeholder="选择年月"
                format="yyyy-MM"
                value-format="yyyy-MM"
              >
              </el-date-picker>
            </el-form-item>
            <!--            <el-form-item v-if="reloadForm.seqId" label="黑白计数器调整:">-->
            <!--              <el-input-number-->
            <!--                v-model="reloadForm.blackWhiteAdjust"-->
            <!--                size="medium"-->
            <!--                :step="100"-->
            <!--              ></el-input-number>-->
            <!--            </el-form-item>-->
            <!--            <el-form-item v-if="reloadForm.seqId" label="彩色计数器调整:">-->
            <!--              <el-input-number-->
            <!--                v-model="reloadForm.colorAdjust"-->
            <!--                size="medium"-->
            <!--                :step="100"-->
            <!--              ></el-input-number>-->
            <!--            </el-form-item>-->
          </el-col>
        </el-row>
      </el-form>
      <div class="footer-btn">
        <el-button
          type="primary"
          :disabled="!reloadForm.seqId"
          @click="calculate"
        >
          统计
        </el-button>
        <el-button @click="reloadDrawer = false"> 取消 </el-button>
      </div>
    </ProDialog>
    <!--  调整黑白彩色计数器  -->
    <ProDialog
      :value="resetDialog"
      title="调整计数器"
      width="50%"
      :confirm-loading="false"
      :no-footer="true"
      @cancel="resetDialog = false"
    >
      <el-form
        ref="resetCounterForm"
        label-width="180px"
        class="demo-ruleForm"
        :model="resetCounterForm"
        :rules="resetCounterRules"
      >
        <el-row>
          <el-col :span="24" style="display: flex; flex-wrap: wrap">
            <el-form-item
              label="黑白计数器起数调整值:"
              prop="blackWhiteAdjustInception"
            >
              <el-input-number
                v-model="resetCounterForm.blackWhiteAdjustInception"
                size="medium"
                :step="100"
              ></el-input-number>
            </el-form-item>
            <el-form-item
              label="黑白计数器止数调整值:"
              prop="blackWhiteAdjustCutOff"
            >
              <el-input-number
                v-model="resetCounterForm.blackWhiteAdjustCutOff"
                size="medium"
                :step="100"
              ></el-input-number>
            </el-form-item>
            <el-form-item
              label="彩色计数器起数调整值:"
              prop="colorAdjustInception"
            >
              <el-input-number
                v-model="resetCounterForm.colorAdjustInception"
                size="medium"
                :step="100"
              ></el-input-number>
            </el-form-item>

            <el-form-item
              label="彩色计数器止数调整值:"
              prop="colorAdjustCutOff"
            >
              <el-input-number
                v-model="resetCounterForm.colorAdjustCutOff"
                size="medium"
                :step="100"
              ></el-input-number>
            </el-form-item>
            <el-form-item
              label="五色计数器起数调整值:"
              prop="colorAdjustInception"
            >
              <el-input-number
                v-model="resetCounterForm.fiveAdjustInception"
                size="medium"
                :step="100"
              ></el-input-number>
            </el-form-item>

            <el-form-item
              label="五色计数器止数调整值:"
              prop="colorAdjustCutOff"
            >
              <el-input-number
                v-model="resetCounterForm.fiveAdjustCutOff"
                size="medium"
                :step="100"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="footer-btn">
        <el-button type="primary" @click="handleReset">确认</el-button>
        <el-button @click="resetDialog = false">取消</el-button>
      </div>
    </ProDialog>
    <!--  选择客户信息  -->
    <ProDialog
      :value="showDialog"
      title="客户信息"
      width="60%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <ProTable
        ref="ProTables"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="creatColumns"
        show-pagination
        row-key="id"
        :local-pagination="localPaginations"
        :data="customTableData"
        sticky
        :query-param="queryParams"
        :height="430"
        :show-setting="false"
        @loadData="loadData1"
      >
        <template #actions="slotProps">
          <span class="fixed-width">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-edit-outline"
              @click="sureSelectCustom(slotProps.row)"
            >
              确定
            </el-button>
          </span>
        </template>
      </ProTable>
    </ProDialog>
    <!-- 记录详情-->
    <ProDrawer
      :value="detailDrawer"
      :title="showDetailTitle"
      size="70%"
      :confirm-loading="confirmLoadings"
      :no-footer="true"
      @cancel="closeDetail"
    >
      <el-descriptions :column="3" border>
        <el-descriptions-item label="店铺名称">
          {{ detailData.customerName }}
        </el-descriptions-item>
        <el-descriptions-item label="客户编号">
          {{ detailData.customerSeq }}
        </el-descriptions-item>
        <el-descriptions-item label="品牌">
          {{ detailData.brand }}
        </el-descriptions-item>
        <el-descriptions-item label="机型">
          {{ detailData.machine }}
        </el-descriptions-item>
        <el-descriptions-item label="设备组名称" :span="2">
          {{ detailData.deviceGroup?.label }}
        </el-descriptions-item>
        <el-descriptions-item label="销售人员">
          {{ detailData.salesmanName }}
        </el-descriptions-item>
        <el-descriptions-item label="维修工程师">
          {{ detailData.enginnerName }}
        </el-descriptions-item>
        <el-descriptions-item label="合约类型">
          {{ detailData.treatyType?.label }}
        </el-descriptions-item>
        <el-descriptions-item label="计张方式">
          {{ detailData.paperType }}
        </el-descriptions-item>
        <el-descriptions-item label="核算方式">
          {{ detailData.accountMode?.label }}
        </el-descriptions-item>
        <el-descriptions-item label="价格类型">
          {{ detailData.priceType?.label }}
        </el-descriptions-item>
        <el-descriptions-item label="黑色保底印量">
          {{ detailData.blackGuarantee }}
        </el-descriptions-item>
        <el-descriptions-item label="彩色保底印量" :span="2">
          {{ detailData.colorGuarantee }}
        </el-descriptions-item>
        <el-descriptions-item label="初始黑白计数器">
          {{ detailData.blackWhiteInception }}
        </el-descriptions-item>
        <el-descriptions-item label="初始彩色计数器">
          {{ detailData.colorInception }}
        </el-descriptions-item>
        <el-descriptions-item label="初始五色计数器">
          {{ detailData.fiveColourIncption }}
        </el-descriptions-item>
        <el-descriptions-item label="截止黑白计数器">
          {{ detailData.blackWhiteCutoff }}
        </el-descriptions-item>

        <el-descriptions-item label="截止彩色计数器">
          {{ detailData.colorCutoff }}
        </el-descriptions-item>
        <el-descriptions-item label="截止五色计数器">
          {{ detailData.fiveColourCutoff }}
        </el-descriptions-item>
        <el-descriptions-item label="黑白计数器起数调整值">
          {{ detailData.blackWhiteAdjustInception }}
        </el-descriptions-item>
        <el-descriptions-item label="彩色计数器起数调整值">
          {{ detailData.colorAdjustInception }}
        </el-descriptions-item>
        <el-descriptions-item label="彩色计数器止数调整值">
          {{ detailData.colorAdjustCutOff }}
        </el-descriptions-item>
        <el-descriptions-item label="黑白计数器止数调整值">
          {{ detailData.blackWhiteAdjustCutOff }}
        </el-descriptions-item>
        <el-descriptions-item label="彩色计数器起数调整值">
          {{ detailData.colorAdjustCutOff }}
        </el-descriptions-item>
        <el-descriptions-item label="五色计数器止数调整值">
          {{ detailData.fiveAdjustCutOff }}
        </el-descriptions-item>
        <el-descriptions-item label="黑白印量">
          {{ detailData.blackWhitePoint }}
        </el-descriptions-item>
        <el-descriptions-item label="彩色印量">
          {{ detailData.colorPoint }}
        </el-descriptions-item>
        <el-descriptions-item label="五色印量">
          {{ detailData.fiveColourPoint }}
        </el-descriptions-item>
        <el-descriptions-item label="黑白废张数">
          {{ detailData.blackWhiteExclude }}
        </el-descriptions-item>
        <el-descriptions-item label="彩色废张数">
          {{ detailData.colorExclude }}
        </el-descriptions-item>
        <el-descriptions-item label="五色废张数">
          {{ detailData.fiveExclude }}
        </el-descriptions-item>
        <el-descriptions-item label="黑白单价">
          {{ detailData.blackWhitePrice }}
        </el-descriptions-item>
        <el-descriptions-item label="彩色单价">
          {{ detailData.colorPrice }}
        </el-descriptions-item>
        <el-descriptions-item label="五色单价">
          {{ detailData.fiveColourPrice }}
        </el-descriptions-item>
        <el-descriptions-item label="黑白抄表费">
          {{ detailData.blackWhiteAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="彩色抄表费">
          {{ detailData.colorAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="五色抄表费">
          {{ detailData.fiveColourAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="总金额">
          {{ detailData.amount }}
        </el-descriptions-item>
        <el-descriptions-item label="年月" span="2">
          {{ detailData.cycle }}
        </el-descriptions-item>
        <el-descriptions-item label="起始抄表时间">
          {{ detailData.beginTime }}
        </el-descriptions-item>
        <el-descriptions-item label="截止抄表时间">
          {{ detailData.endTime }}
        </el-descriptions-item>
      </el-descriptions>
    </ProDrawer>
    <AddMeterOrder ref="addMeterOrder" />
  </div>
</template>
<script>
import AddMeterOrder from "@/views/order/components/addMeterOrder.vue";
import {
  meterListApi,
  rePrintListApi,
  resetPrintMeterListApi,
} from "@/api/statisics";
import { cloneDeep } from "lodash";
import { filterParam, filterParamRange } from "@/utils";
import { productListApi } from "@/api/dispose";
import { dictTreeByCodeApi, roleMemberApi } from "@/api/user";
import {
  getCustomerDeviceGroupByPageApi,
  getCustomerByPageApi,
} from "@/api/customer";
export default {
  name: "Exhibition",
  components: { AddMeterOrder },
  data() {
    return {
      num: 0,
      // 列表
      spareiTypeList: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      input: "",
      productTreeOption: [],
      productIdName: "",
      queryParam: {},
      columns: [
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          valueType: "product",
          isSearch: true,
          clearable: true,
          // searchSlot: "deviceTree",
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          valueType: "input",
          isSearch: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          width: 140,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          valueType: "select",
          isTable: true,
        },
        {
          dataIndex: "machine",
          title: "机型",
          valueType: "select",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          valueType: "input",
          isTable: true,
          isSearch: true,
          placeholder: "1号机： 1",
          option: [],
          formatter: (row) => row.deviceGroup?.label,
        },
        {
          dataIndex: "cycle",
          title: "抄表月份",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "month",
          pickerFormat: "yyyy-MM",
          attrs: { "value-format": "yyyy-MM" },
        },
        {
          dataIndex: "cycle",
          title: "年月",
          isTable: true,
        },
        {
          dataIndex: "serType",
          title: "合约类型",
          valueType: "select",
          isTable: true,
          isSearch: true,
          multiple: true,
          option: [
            // {
            //   label: "散修",
            //   value: "SCATTERED",
            // },
            // {
            //   label: "购机不保",
            //   value: "NO_WARRANTY",
            // },
            // {
            //   label: "购机质保",
            //   value: "WARRANTY",
            // },
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
          ],
          formatter: (row) => row.serType?.label,
        },
        {
          dataIndex: "salesmanName",
          title: "销售人员",
          isTable: true,
        },
        {
          dataIndex: "salesmanIds",
          title: "销售人员",
          valueType: "select",
          isSearch: true,
          multiple: true,
          option: [],
          optionMth: () =>
            roleMemberApi("1787125965588070402", {
              pageNumber: 1,
              pageSize: 9999,
            }),
          optionskey: {
            label: "name",
            value: "id",
          },
        },
        {
          dataIndex: "enginnerName",
          title: "维修工程师",
          isTable: true,
        },
        {
          dataIndex: "enginnerIds",
          title: "维修工程师",
          valueType: "select",
          isSearch: true,
          multiple: true,
          option: [],
          optionMth: () =>
            roleMemberApi("1002", { pageNumber: 1, pageSize: 9999 }),
          optionskey: {
            label: "name",
            value: "id",
          },
        },
        {
          dataIndex: "colorType",
          title: "色彩类型",
          isTable: true,
          formatter: (row) => row.colorType?.label,
        },
        {
          dataIndex: "paperType",
          title: "计数方式",
          isTable: true,
        },
        {
          dataIndex: "blackWhiteExclude",
          title: "黑白废张",
          isTable: true,
        },
        {
          dataIndex: "blackWhitePoint",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "blackWhitePrice",
          title: "黑白单价",
          isTable: true,
        },

        {
          dataIndex: "colorExclude",
          title: " 彩色废张",
          isTable: true,
        },
        {
          dataIndex: "colorPoint",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "colorPrice",
          title: "彩色单价",
          isTable: true,
        },

        {
          dataIndex: "amount",
          title: "总金额",
          valueType: "input",
          width: 90,
          isTable: true,
        },
        {
          dataIndex: "moneyRange",
          title: "抄表费",
          isSearch: true,
          startPlaceholder: "输入金额",
          endPlaceholder: "输入金额",
          valueType: "inputRange",
        },
        {
          dataIndex: "blackRange",
          title: "黑白印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "colorRange",
          title: "彩色印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "totalRange",
          title: "总印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "Actions",
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          width: 150,
          tableSlot: "actions",
        },
      ],
      creatColumns: [
        {
          dataIndex: "name",
          title: "店铺名称",
          isTable: true,
        },
        {
          dataIndex: "name",
          title: "店铺名称",
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "seqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
        },

        // {
        //   dataIndex: "shopRecruitment",
        //   title: "店铺名称",
        //   isTable: true,
        // },
        {
          dataIndex: "phone",
          title: "电话号码",
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      localPaginations: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      customTableData: [],
      queryParams: {},
      reloadDrawer: false,
      confirmLoadings: false,
      showDialog: false,
      reloadForm: {},
      modelList: [],
      deviceGroupPagination: {
        pageNumber: 1,
        pageSize: 9999,
      },
      deviceGroupList: [],
      cycle: "",
      detailDrawer: false,
      showDetailTitle: " - 抄表记录详情",
      detailData: {},
      userTureList: [],
      workerList: [],
      workerTureList: [],
      resetDialog: false,
      resetCounterForm: {
        blackWhiteAdjustInception: 0,
        blackWhiteAdjustCutOff: 0,
        colorAdjustInception: 0,
        colorAdjustCutOff: 0,
        fiveAdjustInception: 0,
        fiveAdjustCutOff: 0,
      },
      resetCounterRules: {
        blackWhiteAdjustInception: [
          { required: true, message: "请输入黑白起数调整值", trigger: "blur" },
        ],
        blackWhiteAdjustCutOff: [
          { required: true, message: "请输入黑白止数调整值", trigger: "blur" },
        ],
        colorAdjustInception: [
          { required: true, message: "请输入彩色起数调整值", trigger: "blur" },
        ],
        colorAdjustCutOff: [
          { required: true, message: "请输入彩色止数调整值", trigger: "blur" },
        ],
        fiveAdjustInception: [
          { required: true, message: "请输入五色起数调整值", trigger: "blur" },
        ],
        fiveAdjustCutOff: [
          { required: true, message: "请输入无色止数调整值", trigger: "blur" },
        ],
      },
      reloadFormRules: {
        cycle: [{ required: true, message: "请选择校准日期", trigger: "blur" }],
        deviceGroupId: [
          { required: true, message: "请选择需校准设备组", trigger: "blur" },
        ],
      },
      reloadFormRulesReset: {
        cycle: [
          { required: false, message: "请选择校准日期", trigger: "blur" },
        ],
        deviceGroupId: [
          { required: false, message: "请选择需校准设备组", trigger: "blur" },
        ],
      },
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
    this.getProductTree();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );

      const result = [
        {
          geAmount: null,
          leAmount: null,
          data: parameter.moneyRange,
        },
        {
          beginBlackWhite: null,
          endBlackWhite: null,
          data: parameter.blackRange,
        },
        {
          beginColor: null,
          endColor: null,
          data: parameter.colorRange,
        },
        {
          beginCount: null,
          endCount: null,
          data: parameter.totalRange,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.moneyRange;
      delete requestParameters.blackRange;
      delete requestParameters.colorRange;
      delete requestParameters.totalRange;
      meterListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    loadData1(parameter) {
      const requestParameters = Object.assign(this.queryParams, parameter);
      getCustomerByPageApi(requestParameters)
        .then((res) => {
          console.log(res, "res");
          this.customTableData = res.data.rows;
          this.localPaginations = {
            pageNumber: parameter.pageNumber,
            pageSize: parameter.pageSize,
            total: +res.data.total,
          };
        })
        .finally(() => {
          this.$refs.ProTables
            ? (this.$refs.ProTables.listLoading = false)
            : null;
        });
    },
    // 新增对账单
    handleAddMeter() {
      this.$refs.addMeterOrder.show();
    },
    reload() {
      this.reloadDrawer = true;
    },
    // 查看详情
    showDetail(row) {
      this.showDetailTitle = row.customerName + this.showDetailTitle;
      this.detailData = row;
      this.detailDrawer = true;
    },
    resetCounter(row) {
      console.log(row, "调整");
      this.resetCounterForm.id = row.id;
      this.resetDialog = true;
    },
    /**
     * @description: 从新调整计数器
     */
    handleReset() {
      this.$refs["resetCounterForm"].validate((valid) => {
        if (valid) {
          resetPrintMeterListApi(this.resetCounterForm).then((res) => {
            this.$refs["resetCounterForm"].resetFields();
            delete this.resetCounterForm.id;
            this.resetDialog = false;
            this.$message.success(res.message);
          });
        }
      });
    },
    closeDetail() {
      this.detailData = {};
      this.showDetailTitle = " - 抄表记录详情";
      this.detailDrawer = false;
    },
    showDialogFn() {
      this.showDialog = true;
      this.$nextTick((e) => {
        this.$refs.ProTables.refresh();
      });
    },
    // 确认用户
    async sureSelectCustom(row) {
      row.consigneeName = row.name;
      row.consigneePhone = row.tel;
      this.reloadForm = {};

      this.$nextTick((e) => {
        this.reloadForm = row;
      });
      const result = await getCustomerDeviceGroupByPageApi({
        ...this.deviceGroupPagination,
        customerId: row.id,
      });
      if (result.code === 200 && result.data) {
        const data = result.data.rows;
        this.deviceGroupList = data.map((item) => {
          return {
            value: item.id,
            label: `${item.deviceGroup.label} - ${item.productInfo}`,
          };
        });
      }

      this.showDialog = false;
    },
    // 计算
    calculate() {
      console.log(this.reloadForm);
      const { seqId, cycle, deviceGroupId, blackWhiteAdjust, colorAdjust } =
        this.reloadForm;

      this.$refs["proform_child1"].validate(async (valid) => {
        if (valid) {
          const params = {
            seqId,
            cycle,
            deviceGroupId,
            blackWhiteAdjust,
            colorAdjust,
          };
          await rePrintListApi(params)
            .then((res) => {
              this.reloadDrawer = false;
              if (res.code === 200) {
                this.$message.success(res.message);
                // this.$refs["proform_child1"].resetFields();
                this.reloadForm = {};
              }
            })
            .catch((err) => {
              this.$message.error(err || err.message || "计算失败");
            });
        } else {
          return false;
        }
      });
    },
    async getProductTree() {
      try {
        const result = await productListApi({ pageNumber: 1, pageSize: 9999 });
        if (result.code === 200 && result.data) {
          this.productTreeOption = result.data;
        }
      } catch (error) {
        console.log(error);
      }
    },
    formSubmit(val) {
      console.log(val);
    },
    handleProductTree(item) {
      console.log(item);
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.footer-btn {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}
.tit-boxs {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px 7px;
  font-size: 16px;
  font-weight: 800;
}
</style>
