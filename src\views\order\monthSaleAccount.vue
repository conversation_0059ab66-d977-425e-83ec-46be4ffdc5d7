<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:54
 * @Description: 
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      row-key="label"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="550"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right" style="font-size: 14px; gap: 16px">
          <div>下单客户数：{{ monthSaleData?.customerNum }}</div>
          <div>3月内下单客户数：{{ monthSaleData?.inQuarterCustomerNum }}</div>
          <div>月内新增客户数：{{ monthSaleData?.monthAddCustomerNum }}</div>
          <div>销售总额：{{ monthSaleData?.totalAmount }}</div>
          <div>订单总额：{{ monthSaleData?.orderAmount }}</div>
          <div>报修总额：{{ monthSaleData?.workAmount }}</div>
          <div>退款总额：{{ monthSaleData?.refundAmount }}</div>
          <div>收款金额：{{ monthSaleData?.actureAmount }}</div>
        </div>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            type="edit"
            size="mini"
            icon="el-icon-refresh"
            @click="resetAmount(row.currMonth)"
            >更新</el-button
          >
        </div>
      </template>
    </ProTable>
  </div>
</template>
<script>
import { divideAmount, filterParam, filterParamRange } from "@/utils";
import {
  getSaleDetailApi,
  monthSaleListApi,
  resetMonthAmountApi,
} from "@/api/statisics";
import { cloneDeep } from "lodash";
import { Message } from "element-ui";

export default {
  name: "MonthSaleCount",
  mixins: [],
  props: {},
  data() {
    return {
      tableData: [],
      columns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
          attrs: { "value-format": "yyyy-MM" },
          width: 100,
        },
        {
          dataIndex: "orderNum",
          title: "销售订单数",
          isTable: true,
          // isSearch: true,
          valueType: "inputRange",
          startPlaceholder: "最小订单数",
          endPlaceholder: "最大订单数",
        },
        {
          dataIndex: "orderAmount",
          title: "销售金额",
          isTable: true,
          // isSearch: true,
          valueType: "inputRange",
          startPlaceholder: "最小金额",
          endPlaceholder: "最大金额",
          formatter: (row) => divideAmount(row.orderAmount, 100),
        },
        {
          dataIndex: "workNum",
          title: "工单数",
          isTable: true,
        },
        {
          dataIndex: "workPartAmount",
          title: "普通工单耗材费",
          isTable: true,
          formatter: (row) => divideAmount(row.workPartAmount, 100),
        },
        {
          dataIndex: "workLaborAmount",
          title: "普通工单人工费",
          isTable: true,
          formatter: (row) => divideAmount(row.workLaborAmount, 100),
        },
        {
          dataIndex: "receiptAmount",
          title: "抄表费用",
          isTable: true,
          formatter: (row) => divideAmount(row.receiptAmount, 100),
        },
        {
          dataIndex: "applyNum",
          title: "领料单数",
          isTable: true,
          valueType: "inputRange",
          startPlaceholder: "最小领料数",
          endPlaceholder: "最大领料数",
        },
        {
          dataIndex: "applyAmount",
          title: "领料单金额",
          isTable: true,
          valueType: "inputRange",
          startPlaceholder: "最小金额",
          endPlaceholder: "最大金额",
          formatter: (row) => divideAmount(row.applyAmount, 100),
        },
        {
          dataIndex: "pactWorkNum",
          title: "全/半保工单数",
          isTable: true,
        },
        {
          dataIndex: "pactPartAmount",
          title: "全/半保工单耗材费用",
          valueType: "select",
          isSearch: false,
          isTable: true,
          clearable: true,
          formatter: (row) => divideAmount(row.pactPartAmount, 100),
        },
        {
          dataIndex: "pactLaborAmount",
          title: "全/半保人工费用",
          valueType: "select",
          isSearch: false,
          isTable: true,
          multiple: true,
          clearable: true,
          formatter: (row) => divideAmount(row.pactLaborAmount, 100),
        },
        {
          dataIndex: "yearGrowth",
          title: "同比增长",
          isTable: true,
          // isSearch: true,
          valueType: "inputRange",
          startPlaceholder: "",
          endPlaceholder: "",
          formatter: (row) => (row.yearGrowth ? row.yearGrowth + "%" : "/"),
        },
        {
          dataIndex: "sequentialGrowth",
          title: "环比增长",
          isTable: true,
          // isSearch: true,
          valueType: "inputRange",
          startPlaceholder: "",
          endPlaceholder: "",
          formatter: (row) =>
            row.sequentialGrowth ? row.sequentialGrowth + "%" : "/",
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isTable: true,
          formatter: (row) => divideAmount(row.totalAmount, 100),
        },
        {
          dataIndex: "actureAmount",
          title: "实付金额",
          isTable: true,
          // isSearch: true,
          valueType: "inputRange",
          startPlaceholder: "最小金额",
          endPlaceholder: "最大金额",
          formatter: (row) => divideAmount(row.actureAmount, 100),
        },
        {
          dataIndex: "refundAmount",
          title: "退款金额",
          isTable: true,
          isSearch: false,
          formatter: (row) => divideAmount(row.refundAmount, 100),
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        startDate: null,
        endDate: null,
        costDate: [],
      },
      monthSaleData: {},
    };
  },
  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.currMonth,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.currMonth;
      monthSaleListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      getSaleDetailApi().then((res) => {
        this.monthSaleData = res.data || {};
      });
    },
    resetAmount(cycle) {
      resetMonthAmountApi({ cycle }).then((res) => {
        Message.success("更新成功");
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.sale-data-box {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  flex: 1;
  div {
    text-wrap: nowrap;
    //font-weight: bold;
    font-size: 16px;
    color: red;
    text-align: left;
  }
}
</style>
