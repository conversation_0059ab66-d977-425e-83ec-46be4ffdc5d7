<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:53
 * @Description: 
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-search
      show-loading
      show-pagination
      @loadData="loadData"
    >
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #reportTime>
        <el-date-picker
          v-model="reportTime"
          style="width: 100%"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :format="'yyyy-MM-dd HH:mm:ss'"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </template>
      <template> </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleDetails(row)"
          >
            查看</el-button
          >
          <el-button
            v-if="!row.status"
            type="edit"
            size="mini"
            icon="el-icon-circle-check"
            @click="handleResetData(row, 1)"
          >
            有效</el-button
          >
          <el-button
            v-if="row.status"
            type="danger"
            size="mini"
            icon="el-icon-circle-close"
            @click="handleResetData(row, 0)"
          >
            无效</el-button
          >
        </div>
      </template>
    </ProTable>

    <!--  详情弹窗  -->
    <ProDialog
      :value="showDialog"
      title="详情"
      width="1000px"
      :confirm-loading="dialogLoading"
      top="20px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <ProForm
        ref="ProForm"
        :form-param="details"
        :form-list="columns"
        :confirm-loading="dialogLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="'info'"
        :no-footer="true"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import { Message } from "element-ui";
import { getCounterListApi, editCounterStatusApi } from "@/api/iot";
import { productAllApi } from "@/api/dispose";
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
export default {
  name: "Counter",
  components: { ProTable },
  data() {
    return {
      productIdName: [],
      options: [],
      tableData: [],
      columns: [
        // {
        //   dataIndex: "companyName",
        //   title: "公司",
        //   isTable: true,
        //   isForm: true,
        //   isSearch: true,
        //   valueType: "input",
        //   formSpan: 12,
        // },
        {
          dataIndex: "deviceSeqId",
          title: "机器编号",
          isTable: true,
          isForm: true,
          isSearch: true,
          valueType: "input",
          formSpan: 12,
          minWidth: 100,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isForm: true,
          isSearch: true,
          valueType: "input",
          formSpan: 12,
          minWidth: 100,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          formatter: (row) => row.deviceGroup.label,
        },
        {
          dataIndex: "deviceGroupName",
          title: "设备组名称",
          isSearch: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "deviceGroups",
          title: "设备组名称",
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "machine",
          title: "主机型号",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "paperType",
          title: "计数方式",
          isTable: true,
          isForm: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6800),
          optionkey: {
            label: "label",
            value: "value",
          },
          formSpan: 12,
        },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "cyanCounter",
          title: "彩色计数器",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "magentaCounter",
          title: "第3色计数器",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "yellowCounter",
          title: "第4色计数器",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "fifthCounter",
          title: "第5色计数器",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },

        {
          dataIndex: "reportTime",
          title: "上报时间",
          isTable: true,
          isForm: false,
          isSearch: true,
          searchSlot: "reportTime",
          valueType: "input",
          formSpan: 12,
          width: 150,
        },
        {
          dataIndex: "fifthCounter",
          title: "第6色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "seventhCounter",
          title: "第7色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "eightthCounter",
          title: "第8色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "ninthCounter",
          title: "第9色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "tenthCounter",
          title: "第10色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "eleventhCounter",
          title: "第11色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "twelfthCounter",
          title: "第12色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "thirteenthCounter",
          title: "第13色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "fourteenthCounter",
          title: "第14色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "fifteenthCounter",
          title: "第15色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "sixteenthCounter",
          title: "第16色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "seventeenthCounter",
          title: "第17色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "eightteenthCounter",
          title: "第18色计数器",
          isTable: false,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "reportTime",
          title: "上报时间",
          isTable: false,
          isForm: true,
          isSearch: false,
          searchSlot: "reportTime",
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "productIds",
          title: "品牌/系列",
          placeholder: "品牌/产品树/系列",
          isSearch: true,
          clearable: true,
          // searchSlot: "fullIdPath",
          valueType: "product",
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => (row.status === 1 ? "有效" : "无效"),
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 160,
          tableSlot: "action",
        },
      ],
      queryParam: {},
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      reportTime: null,
      showDialog: false,
      dialogLoading: false,
      details: {},
    };
  },
  mounted() {
    this.getProductThird();
    this.refresh();
  },
  methods: {
    async loadData(params) {
      this.queryParam = filterParam(Object.assign({}, this.queryParam, params));
      // TODO 设置上报时间 reportTime
      if (this.reportTime) {
        this.queryParam["reportTimeStart"] = this.reportTime[0];
        this.queryParam["reportTimeEnd"] = this.reportTime[1];
      }
      try {
        const requestParameters = cloneDeep(this.queryParam);
        const result = await getCounterListApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    handleResetData(row, status) {
      this.$confirm("此操作会重新计算日印量和月印量, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const arg = {
          id: row.id,
          status,
        };
        editCounterStatusApi(arg).then((res) => {
          this.refresh();
          Message.success("状态修改成功");
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    handleDetails(row) {
      this.details = row;
      this.details.deviceGroups = row.deviceGroup.label;
      this.showDialog = true;
    },
    handleSelect(arr) {
      this.queryParam.productIds = arr.map((item) => item[item.length - 1]);
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
        // this.$refs.ProTable.refresh();
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
