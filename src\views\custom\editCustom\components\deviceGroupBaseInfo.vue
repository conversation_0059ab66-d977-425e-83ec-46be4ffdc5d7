<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-15 17:07:23
 * @Description: 
 -->
<template>
  <div class="edit-device-group">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-index
      show-loading
      :show-search="false"
      show-pagination
      @loadData="loadData">
      <template #btn>
        <el-button
          v-if="type !== 'info'"
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleEdit(null, 'add')">
          新增
        </el-button>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleEdit(row.id, 'info')">
            查看
          </el-button>
          <el-button
            v-if="type !== 'info'"
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="handleEdit(row.id, 'edit')">
            编辑
          </el-button>
          <!--          <el-button-->
          <!--              type="primary"-->
          <!--              size="mini"-->
          <!--              v-if="!row.status"-->
          <!--              icon="el-icon-plus"-->
          <!--              @click="handleStatus(row, true)"-->
          <!--          >启用</el-button-->
          <!--          >-->
          <!--          <el-button-->
          <!--            type="danger"-->
          <!--            size="mini"-->
          <!--            v-else-->
          <!--            icon="el-icon-delete"-->
          <!--            @click="handleStatus(row, false)"-->
          <!--            >禁用</el-button-->
          <!--          >-->
          <el-button
            icon="el-icon-full-screen"
            @click="handleGenerateQRCode(row)">
            二维码
          </el-button>
          <!--<el-button-->
          <!--  v-if="row.machineNum && type !== 'info'"-->
          <!--  icon="el-icon-refresh"-->
          <!--  @click="handleTrade(row)"-->
          <!--&gt;-->
          <!--  换机-->
          <!--</el-button>-->
          <el-button
            v-if="type !== 'info'"
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="handleDelete(row)">
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 新增/编辑 -->
    <ProDialog
      :title="dialogTitle"
      :value="showDialog"
      width="80%"
      :confirm-btn-loading="dialogLoading"
      top="10px"
      @ok="handleDialogOk"
      @cancel="closeDialog">
      <ProForm
        ref="ProForm"
        :form-param="formParam"
        :form-list="formcolumns"
        :confirm-loading="dialogLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="editType"
        :no-footer="true"
        @proSubmit="formSubmit">
        <template #productId>
          <el-cascader
            v-model="formParam.productId"
            filterable
            :filter-method="filterMethod"
            clearable
            style="width: 100%"
            :disabled="editType === 'info'"
            :options="productTreeOption"
            :props="{ label: 'name', value: 'id' }"
            @change="handleProductTree"></el-cascader>
        </template>
        <template #deviceGroup>
          <el-select v-model="formParam.deviceGroup">
            <el-option
              v-for="item in deviceGroupOptions"
              :key="item.value"
              style="width: 100%">
              {{ item.name }}
            </el-option>
          </el-select>
        </template>
        <template #deviceGroupImg>
          <ProUpload
            :file-list="formParam.deviceGroupImg"
            :type="editType"
            :limit="1"
            @uploadSuccess="handleUploadSuccess"
            @uploadRemove="handleUploadRemove" />
        </template>
        <template #naram>
          <div>
            <zipFilesProUpload
              v-if="editType === 'edit' || editType === 'add'"
              :limit="5"
              :file-list="naramFileList"
              @uploadSuccess="handleZipUploadSuccess"
              @uploadRemove="handleZipUploadRemove" />
            <el-button
              v-if="formParam.naramFile?.length > 0"
              @click="handleReviewFile('naramFile')">
              查看清单
            </el-button>
          </div>
        </template>
        <template #logFile>
          <div>
            <zipFilesProUpload
              v-if="editType === 'edit' || editType === 'add'"
              :limit="5"
              :file-list="logFileList"
              @uploadSuccess="handleLogFileUploadSuccess"
              @uploadRemove="handleLogFileUploadRemove" />
            <el-button
              v-if="formParam.logFile?.length > 0"
              @click="handleReviewFile('logFile')">
              查看日志文件清单
            </el-button>
          </div>
        </template>
        <template #deviceGroupPart>
          <HostParts
            v-model="formParam.deviceAccessories"
            :machine-num="formParam.machineNum"
            :edit-type="editType" />
        </template>
      </ProForm>
    </ProDialog>
    <proDialog
      title="下载NARAM数据清单"
      :value="showNaramDialog"
      width="500px"
      :no-footer="true"
      @cancel="showNaramDialog = false">
      <div class="file-container">
        <div
          v-for="item in formParam.naramFile"
          :key="item.key"
          class="file-list">
          <el-link type="primary" :href="item.url">{{ item.name }}</el-link>
        </div>
      </div>
    </proDialog>
    <proDialog
      title="下载日志文件清单"
      :value="showLogFileDialog"
      width="500px"
      :no-footer="true"
      @cancel="showLogFileDialog = false">
      <div class="file-container">
        <div
          v-for="item in formParam.logFile"
          :key="item.key"
          class="file-list">
          <el-link type="primary" :href="item.url">{{ item.name }}</el-link>
        </div>
      </div>
    </proDialog>
    <!-- 下载二维码 -->
    <!--<el-dialog-->
    <!--  :visible.sync="showQrCodeDialog"-->
    <!--  title="报修二维码"-->
    <!--  width="300px"-->
    <!--  :modal="false"-->
    <!--  custom-class="QRDialog"-->
    <!--  @ok="QrCodeDialogOk"-->
    <!--  @cancel="QrCodeDialogCancel"-->
    <!--&gt;-->
    <!--  <div ref="QRContent" class="QR-box">-->
    <!--    <div class="qr-title">报修二维码</div>-->
    <!--    <div class="qr-subtitle">一机一码</div>-->
    <!--    <el-image :src="qrCodeUrl" fit="contain" class="qr-image"></el-image>-->
    <!--    <div class="qr-footer">四川至简智印</div>-->
    <!--  </div>-->
    <!--  <div-->
    <!--    slot="footer"-->
    <!--    class="dialog-footer"-->
    <!--    style="display: flex; justify-content: space-evenly"-->
    <!--  >-->
    <!--    <el-button type="primary" size="small" @click="QrCodeDialogOk">-->
    <!--      下载二维码-->
    <!--    </el-button>-->
    <!--    <el-button size="small" @click="QrCodeDialogCancel">关 闭</el-button>-->
    <!--  </div>-->
    <!--</el-dialog>-->
    <el-dialog
      :visible.sync="showQrCodeDialog"
      title="报修二维码"
      width="420px"
      :modal="false"
      custom-class="QRDialog"
      @ok="QrCodeDialogOk"
      @cancel="QrCodeDialogCancel">
      <div ref="QRContent" class="QR-box">
        <div class="qr-left">
          <div class="qr-title">微信扫码报修</div>
          <div class="qr-subtitle">一机一码</div>
          <div class="qr-info">
            <div class="info-item">
              设备组名称: {{ qrInfo.deviceGroup?.label }}
            </div>
            <div class="info-item">机器型号: {{ qrInfo.productInfo }}</div>
          </div>
        </div>
        <div class="qr-right">
          <el-image :src="qrCodeUrl" fit="contain" class="qr-image"></el-image>
        </div>
      </div>
      <div
        slot="footer"
        style="
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 20px;
        ">
        <el-button type="primary" size="small" @click="QrCodeDialogOk">
          下载二维码
        </el-button>
        <el-button size="small" @click="QrCodeDialogCancel">关 闭</el-button>
      </div>
    </el-dialog>
    <deviceTrade v-model="tradeVisible" :info="tradeDeviceInfo"></deviceTrade>
  </div>
</template>

<script>
import zipFilesProUpload from "@/components/ProUpload/zipFiles.vue";
import ProUpload from "@/components/ProUpload/index.vue";
import HostParts from "@/views/custom/editCustom/components/hostParts.vue";
import DeviceTrade from "./deviceTrade.vue";
import html2canvas from "html2canvas";
import {
  getCustomerDeviceGroupByPageApi,
  updateCustomerDeviceGroupApi,
  addCustomerDeviceGroupApi,
  deleteCustomerDeviceGroupApi,
  updateCustomerDeviceGroupStatusApi,
  getCustomerDeviceGroupDetailsApi,
} from "@/api/customer";
import { productListApi } from "@/api/dispose";
import { Loading, Message, MessageBox } from "element-ui";
import {
  roleMemberApi,
  userListApi,
  dictTreeByCodeApi,
  userDropApi,
} from "@/api/user";

import QRCode from "qrcode";
import { cloneDeep } from "lodash";

export default {
  name: "EditDeviceGroup",
  components: { ProUpload, zipFilesProUpload, HostParts, DeviceTrade },
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
  },

  data() {
    const self = this;
    return {
      columns: [
        {
          title: "设备组编号",
          dataIndex: "deviceSeqId",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          width: 120,
        },
        {
          title: "设备组名称",
          dataIndex: "deviceGroup",
          isTable: true,
          formatter: (row) => row.deviceGroup?.label,
          // tableSlot: "deviceGroup",
          isForm: true,
          formSpan: 12,
          valueType: "select",
          formSlot: "deviceGroup",
          option: [],
          // optionMth: () => userDropApi(this.id),
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
          prop: [
            {
              required: true,
              message: "请选择设备组名称",
              trigger: "change",
            },
          ],
        },
        {
          title: "品牌",
          dataIndex: "brand",
          isTable: true,
        },
        {
          title: "机型全称",
          dataIndex: "modelFullName",
          isTable: true,
          minWidth: 120,
        },

        {
          title: "设备状态",
          dataIndex: "deviceStatus",
          isTable: true,
          // tableSlot: "deviceStatus",
          formatter: (row) => row.deviceStatus?.label,
        },
        {
          title: "启动状态",
          dataIndex: "status",
          isTable: true,
          formatter: (row) => (row.status ? "启用" : "禁用"),
        },
        {
          title: "移动端可见",
          dataIndex: "dataShowState",
          isTable: true,
          formatter: (row) => (row.dataShowState ? "是" : "否"),
        },
        {
          title: "安装客户端",
          dataIndex: "regCliState",
          isTable: true,
          formatter: (row) => (row.regCliState == "1" ? "是" : "否"),
        },
        {
          title: "服务类型",
          dataIndex: "serType",
          isTable: true,
          formatter: (row) => row.serType?.label,
        },
        {
          title: "负责工程师",
          dataIndex: "operatName",
          isTable: true,
        },
        {
          title: "操作",
          dataIndex: "action",
          isTable: true,
          tableSlot: "action",
          tooltip: false,
          width: 280,
        },
      ],
      formcolumns: [
        {
          title: "设备组名称",
          dataIndex: "deviceGroup",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => {
            if (self.editType === "add") return userDropApi(this.id);
            return dictTreeByCodeApi(700);
          },
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请输入设备组名称",
              trigger: "change",
            },
          ],
        },
        {
          title: "品牌/机型",
          dataIndex: "productId",
          isForm: true,
          clearable: true,
          valueType: "select",
          formSpan: 6,
          formSlot: "productId",
          disabled: false,
          prop: [
            {
              required: true,
              message: "请输入关联品牌产品树",
              trigger: "change",
            },
          ],
        },
        {
          title: "设备状态",
          dataIndex: "deviceStatus",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(900),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请输入设备状态",
              trigger: "change",
            },
          ],
        },
        {
          title: "启用状态",
          dataIndex: "status",
          isTable: true,
          tableSlot: "status",
          isForm: true,
          valueType: "select",
          option: [
            {
              label: "启用",
              value: true,
            },
            {
              label: "禁用",
              value: false,
            },
          ],
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请选择账号状态",
              trigger: "change",
            },
          ],
        },
        {
          title: "设备新旧",
          dataIndex: "deviceOn",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1100),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请输入设备新旧",
              trigger: "change",
            },
          ],
        },
        // {
        //   title: "服务类型",
        //   dataIndex: "treatyType",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "select",
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(1600),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        //   prop: [
        //     {
        //       required: true,
        //       message: "请输入合约类型",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          title: "服务类型",
          dataIndex: "serType",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [
            {
              label: "散修",
              value: "SCATTERED",
            },
            {
              label: "购机不保",
              value: "NO_WARRANTY",
            },
            {
              label: "购机质保",
              value: "WARRANTY",
            },
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "质保服务",
              value: "QA",
            },
            {
              label: "质保含部件",
              value: "QA_COMPONENT",
            },
            {
              label: "维保服务",
              value: "MAINTENANCE",
            },
            {
              label: "其它",
              value: "OTHER",
            },
          ],
          prop: [
            {
              required: true,
              message: "请输入合约类型",
              trigger: "change",
            },
          ],
        },
        {
          title: "维修状态",
          dataIndex: "fixStatus",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1500),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请输入维修状态",
              trigger: "change",
            },
          ],
        },
        // {
        //   title: "服务类型",
        //   dataIndex: "serType",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "select",
        //   option: [
        //     {
        //       label: "散修",
        //       value: "1",
        //     },
        //     {
        //       label: "全保",
        //       value: "2",
        //     },
        //     {
        //       label: "租赁",
        //       value: "3",
        //     },
        //     {
        //       label: "半保",
        //       value: "4",
        //     },
        //     {
        //       label: "签约",
        //       value: "5",
        //     },
        //     {
        //       label: "数据",
        //       value: "6",
        //     },
        //   ],
        // },
        {
          title: "安装客户端",
          dataIndex: "regCliState",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [
            {
              label: "是",
              value: "1",
            },
            {
              label: "否",
              value: "0",
            },
          ],
        },
        {
          title: "是否统计",
          dataIndex: "enableStatistics",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          title: "负责工程师",
          dataIndex: "operatId",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [],
        },
        {
          title: "移动端是否可见",
          dataIndex: "dataShowState",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [
            {
              label: "是",
              value: 1,
            },
            {
              label: "否",
              value: 0,
            },
          ],
        },
        // {
        //   title: "入住黑白计数器",
        //   dataIndex: "blackWhiteCounter",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        // {
        //   title: "入住彩色计数器",
        //   dataIndex: "colorCounter",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        // {
        //   title: "统计开始时间",
        //   dataIndex: "statisticsStartDate",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "date-picker",
        // },
        // {
        //   title: "统计操作人员",
        //   dataIndex: "statisticsOperatName",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "select",
        //   option: [],
        // },
        // {
        //   title: "统计黑白计数器",
        //   dataIndex: "statisticsBlackWhiteCounter",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        // {
        //   title: "统计彩色计数器",
        //   dataIndex: "statisticsColoursCounter",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        // {
        //   title: "签约时间",
        //   dataIndex: "signDate",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "date-picker",
        // },
        // {
        //   title: "签约操作人员",
        //   dataIndex: "signOperatName",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "select",
        //   option: [],
        // },
        // {
        //   title: "签约黑白计数器",
        //   dataIndex: "signBlackWhiteCounter",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        // {
        //   title: "签约彩色计数器",
        //   dataIndex: "signColoursCounter",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        {
          title: "校准黑白计数器",
          dataIndex: "adjustBlackWhite",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          title: "校准彩色计数器",
          dataIndex: "adjustColor",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          dataIndex: "paperType",
          title: "计数方式",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6800),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   title: "黑白保底印量",
        //   dataIndex: "blackGuarantee",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "input",
        //   inputType: "number",
        // },
        // {
        //   title: "彩色保底印量",
        //   dataIndex: "colorGuarantee",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 6,
        //   valueType: "input",
        //   inputType: "number",
        // },
        {
          title: "机器序列号",
          dataIndex: "deviceSequence",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          title: "机器MAC地址",
          dataIndex: "macNumber",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          title: "产地版本",
          dataIndex: "placeOrigin",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          title: "供电电压",
          dataIndex: "supplyVoltage",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          title: "NARAM数据清单",
          dataIndex: "naramFile",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "input",
          formSlot: "naram",
        },
        {
          title: "日志文件清单",
          dataIndex: "logFile",
          isForm: true,
          clearable: true,
          formSpan: 6,
          valueType: "input",
          formSlot: "logFile",
        },
        {
          title: "设备组照片",
          dataIndex: "deviceGroupImg",
          isForm: true,
          valueType: "upload",
          formSlot: "deviceGroupImg",
          formSpan: 24,
        },
        {
          dataIndex: "deviceGroupPart",
          title: "关联选配件",
          isForm: true,
          formOtherSlot: "deviceGroupPart",
          formSpan: 24,
        },
        // {
        //   dataIndex: "priceType",
        //   title: "价格类型",
        //   isForm: true,
        //   valueType: "radio",
        //   option: [
        //     {
        //       label: "固定价",
        //       value: "IMMOBILIZATION",
        //     },
        //     {
        //       label: "阶梯价",
        //       value: "LADDER",
        //     },
        //   ],
        //   formSpan: 6,
        // },
        // {
        //   title: "黑白打印单价",
        //   dataIndex: "blackWhitePrice",
        //   isForm: false,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        // {
        //   title: "彩色打印单价",
        //   dataIndex: "colorPrice",
        //   isForm: false,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        // {
        //   title: "五彩打印单价",
        //   dataIndex: "fiveColourPrice",
        //   isForm: false,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "accountMode",
        //   title: "核算方式",
        //   isForm: false,
        //   valueType: "select",
        //   option: [
        //     {
        //       label: "统一计价",
        //       value: "UNIFY",
        //     },
        //     {
        //       label: "分段计价",
        //       value: "FLOAT",
        //     },
        //   ],
        //   formSpan: 6,
        // },
        // {
        //   dataIndex: "ladder",
        //   title: "阶梯价",
        //   isForm: false,
        //   formSlot: "ladder",
        //   formSpan: 24,
        // },
      ],
      tableData: [],
      queryParam: {},
      naramFileList: [],
      logFileList: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      showDialog: false,
      dialogLoading: false,
      dialogTitle: "",
      editType: "add",
      formParam: {},
      productTreeOption: [],
      userTureList: [],
      workerList: [],
      workerTureList: [],
      showNaramDialog: false,
      showLogFileDialog: false,
      qrCodeUrl: "",
      qrInfo: {},
      showQrCodeDialog: false,
      deviceGroupOptions: [],
      tradeVisible: false, //换机显隐控制
      tradeDeviceInfo: {},
    };
  },
  watch: {
    "formParam.priceType": {
      handler(val) {
        const { formcolumns, formParam } = this;
        const updateIsForm = (columns, keys, isForm) => {
          columns.forEach((item) => {
            if (keys.includes(item.dataIndex)) {
              item.isForm = isForm;
            }
          });
        };
        if (val === "IMMOBILIZATION") {
          // 隐藏 accountMode 和 ladder
          updateIsForm(formcolumns, ["accountMode", "ladder"], false);
          // 根据 设备色彩类型 显示或隐藏其他字段
          const colorValue = formParam.color?.value;
          if (colorValue === "1703") {
            // 五彩机
            updateIsForm(
              formcolumns,
              ["blackWhitePrice", "colorPrice", "fiveColourPrice"],
              true
            );
          } else if (colorValue === "1701") {
            // 彩色机
            updateIsForm(formcolumns, ["blackWhitePrice", "colorPrice"], true);
          } else if (colorValue === "1702") {
            // 黑白机
            updateIsForm(formcolumns, ["blackWhitePrice"], true);
          }
        } else if (val === "LADDER") {
          // 显示 accountMode 和 ladder
          updateIsForm(formcolumns, ["accountMode", "ladder"], true);
          // 隐藏价格相关字段
          updateIsForm(
            formcolumns,
            ["blackWhitePrice", "colorPrice", "fiveColourPrice"],
            false
          );
        }
      },
      deep: true,
    },
  },
  async mounted() {
    await this.getProductTree();
    await this.refresh();
    this.operatList();
  },
  methods: {
    filterMethod(node, val) {
      if (
        !!~node.text.indexOf(val) ||
        !!~node.text.toUpperCase().indexOf(val.toUpperCase())
      ) {
        return true;
      }
    },
    // 员工列表处理
    operatList() {
      this.userList = [];
      userListApi({ pageNumber: 1, pageSize: 10000 }).then((res) => {
        this.userTureList = res.data.rows;
        res.data.rows.map((item) => {
          this.userList.push({
            value: item.name,
            label: item.name,
          });
        });
      });
      this.formcolumns.forEach((item) => {
        if (
          item.dataIndex === "statisticsOperatName" ||
          item.dataIndex === "signOperatName"
        ) {
          item.option = this.userList;
        }
      });
      // 工程师列表
      this.workerList = [];
      roleMemberApi("1002", { pageNumber: 1, pageSize: 10000 }).then((res) => {
        this.workerTureList = res.data.rows;
        res.data.rows.map((item) => {
          this.workerList.push({
            value: item.id,
            label: item.name,
          });
        });
      });
      this.formcolumns.forEach((item) => {
        if (item.dataIndex === "operatId") {
          item.option = this.workerList;
        }
      });
    },
    async loadData(params) {
      try {
        const result = await getCustomerDeviceGroupByPageApi({
          ...params,
          customerId: this.id,
        });
        if (result.code === 200 && result.data) {
          const data = result.data.rows;
          this.tableData = data.map((item) => {
            const productArr = this.findProductTreeName(
              this.productTreeOption,
              item.productId
            )
              .fullIdPath.split("/")
              .splice(1);
            item.productId = [...productArr];
            return {
              ...item,
              brand:
                this.findProductTreeName(this.productTreeOption, productArr[0])
                  ?.name || "/",
              product:
                this.findProductTreeName(this.productTreeOption, productArr[1])
                  ?.name || "/",
              series:
                this.findProductTreeName(this.productTreeOption, productArr[2])
                  ?.name || "/",
              model:
                this.findProductTreeName(this.productTreeOption, productArr[3])
                  ?.name || "/",
              modelFullName:
                this.findProductTreeName(this.productTreeOption, productArr[3])
                  ?.name || "/",
            };
          });
          this.localPagination.total = +result.data.total;
        }
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    // 获取设备组
    async getDeviceGroupOptions() {
      try {
        const result = await userDropApi(this.id);
        if (result.code === 200) {
          this.deviceGroupOptions = result.data;
        }
      } catch (e) {
        this.deviceGroupOptions = [];
      }
    },
    async handleEdit(row, type) {
      if (!row) {
        row = {};
      }
      this.formParam = {};

      if (type !== "add") {
        const result = await getCustomerDeviceGroupDetailsApi(row);
        if (result.code === 200) {
          const data = result.data;
          if (
            data.deviceGroupImg &&
            !Array.isArray(data.deviceGroupImg) &&
            Object.keys(data.deviceGroupImg).length !== 0
          ) {
            data.deviceGroupImg = new Array(data.deviceGroupImg);
          } else {
            data.deviceGroupImg = [];
          }

          data.deviceGroup && (data.deviceGroup = data.deviceGroup.value);
          data.deviceType && (data.deviceType = data.deviceType.value);
          data.deviceStatus
            ? (data.deviceStatus = data.deviceStatus.value)
            : (data.deviceStatus = "901");
          data.deviceOn
            ? (data.deviceOn = data.deviceOn.value)
            : (data.deviceOn = "1102");
          data.treatyType
            ? (data.treatyType = data.treatyType.value)
            : (data.treatyType = "1201");
          data.serType
            ? (data.serType = data.serType.value)
            : (data.serType = "SCATTERED");
          data.priceType = data.priceType
            ? data.priceType.value
            : (data.priceType = "IMMOBILIZATION");
          data.fixStatus
            ? (data.fixStatus = data.fixStatus.value)
            : (data.fixStatus = "1501");
          if (type === "add") {
            data.enableStatistics = false;
            data.status = true;
          }
          this.formParam = { ...data };
        }
      } else {
        await this.getDeviceGroupOptions();
        this.formParam = {};
        this.$set(
          this.formParam,
          "deviceGroup",
          this.deviceGroupOptions[0]?.value
        );
        this.$set(this.formParam, "deviceStatus", "901");
        this.$set(this.formParam, "status", true);
        this.$set(this.formParam, "fixStatus", "1501");
        this.$set(this.formParam, "dataShowState", 1);
        this.$set(this.formParam, "paperType", "A4");
      }

      this.editType = type;
      this.dialogTitle =
        type === "add"
          ? "新增设备组"
          : type === "info"
          ? "设备组详情"
          : "编辑设备组";
      this.showDialog = true;
      if (type === "edit") {
        this.formcolumns[0].isForm = false;
        this.formcolumns[1].isForm = false;
      } else {
        this.formcolumns[0].isForm = true;
        this.formcolumns[1].isForm = true;
      }
    },
    handleDelete(row) {
      MessageBox.confirm("删除设备组信息。确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const result = await deleteCustomerDeviceGroupApi(row.id);
          if (result.code === 200) {
            Message.success("删除成功");
            this.refresh();
          }
        })
        .catch(() => {
          console.log("取消删除");
        });
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    closeDialog() {
      this.showDialog = false;
      this.$nextTick(() => {
        Object.keys(this.formParam).forEach((key) => {
          delete this.formParam[key];
        });
      });
    },
    async formSubmit(val) {
      if (this.formParam.statisticsStartDate) {
        this.formParam.statisticsStartDate = this.$moment(
          this.formParam.statisticsStartDate
        ).format("YYYY-MM-DD HH:MM:SS");
      }
      if (this.formParam.signDate) {
        this.formParam.signDate = this.$moment(this.formParam.signDate).format(
          "YYYY-MM-DD HH:MM:SS"
        );
      }
      this.userTureList.forEach((item) => {
        if (item.name === val.signOperatName) {
          val.signOperatId = item.id;
        } else if (item.name === val.statisticsOperatName) {
          val.statisticsOperatId = item.id;
        }
      });
      // await this.workerTureList.map((item) => {
      //   if (item.name == val.operatName) {
      //     val.operatId = item.id;
      //   }
      // });
      try {
        this.dialogLoading = true;
        const editApi =
          this.editType === "add"
            ? addCustomerDeviceGroupApi
            : updateCustomerDeviceGroupApi;
        const args = {
          ...val,
          customerId: this.id,
        };
        if (args.deviceGroupImg) {
          args.deviceGroupImg = args.deviceGroupImg[0]
            ? args.deviceGroupImg[0]
            : {};
        }
        if (args.productId && Array.isArray(args.productId)) {
          args.productId = args.productId[args.productId.length - 1];
        }
        const result = await editApi(args);
        if (result.code === 200) {
          this.closeDialog();
          Message.success("保存成功");
          this.refresh();
        }
      } finally {
        this.dialogLoading = false;
      }
    },
    generateQRCode(text) {
      return QRCode.toDataURL(text, {
        errorCorrectionLevel: "H", // 容错级别
      });
    },
    // 生成报修二维码
    async handleGenerateQRCode(row) {
      const pageUrl = `https://plat.sczjzy.com.cn/QRRepair?productId=${row.id}`;
      this.qrCodeUrl = await this.generateQRCode(pageUrl);
      this.qrInfo = cloneDeep(row);
      // this.qrCodeUrl = qrCodeUrl;
      this.showQrCodeDialog = true;
    },
    QrCodeDialogOk() {
      const loading = Loading.service({
        fullscreen: true,
        text: "正在生成图片...",
      });
      this.$nextTick(() => {
        html2canvas(this.$refs.QRContent).then((canvas) => {
          const imgData = canvas.toDataURL("image/png");
          const a = document.createElement("a");
          a.href = imgData;
          document.body.append(a);
          a.download = `报修-二维码.png`;
          a.click();
          document.body.removeChild(a);
          loading.close();
        });
      });
      // const link = document.createElement("a");
      // link.href = this.qrCodeUrl;
      // link.download = "qrcode.png";
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
    },
    QrCodeDialogCancel() {
      this.showQrCodeDialog = false;
    },
    handleZipUploadSuccess(result) {
      if (!this.formParam.naramFile) {
        this.$set(this.formParam, "naramFile", []);
      }
      this.formParam.naramFile.push(result);
    },
    handleZipUploadRemove() {
      this.formParam.naramFile = [];
    },
    handleLogFileUploadSuccess(result) {
      if (!this.formParam.logFile) {
        this.$set(this.formParam, "logFile", []);
      }
      this.formParam.logFile.push(result);
    },
    handleLogFileUploadRemove() {
      this.formParam.logFile = [];
    },
    handleReviewFile(type) {
      if (type === "naramFile") {
        this.showNaramDialog = true;
      } else {
        this.showLogFileDialog = true;
      }
    },

    handleUploadSuccess(val) {
      this.formParam.deviceGroupImg = [val];
    },
    handleUploadRemove(file) {
      const index = this.formParam.deviceGroupImg.findIndex(
        (val) => val.key === file.key
      );
      this.formParam.deviceGroupImg.splice(index, 1);
    },
    async getProductTree() {
      try {
        const result = await productListApi({ pageNumber: 1, pageSize: 9999 });
        if (result.code === 200 && result.data) {
          this.productTreeOption = result.data;
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleProductTree(val) {
      // this.formParam.productId = val[val.length - 1]
    },
    findProductTreeName(data, id) {
      let result;
      for (var i = 0; i < data.length; i++) {
        const item = data[i];
        if (item.id === id) {
          //找到id相等的则返回父id
          return item;
        } else if (item.children && item.children.length > 0) {
          //如果有子集，则把子集作为参数重新执行本方法
          result = this.findProductTreeName(item.children, id);
          if (result) {
            return result;
          }
        }
      }
      //如果执行循环中都没有return，则在此return
      return result;
    },
    async handleStatus(row, type) {
      const { status, id } = row;
      if (status === type) return;
      try {
        const result = await updateCustomerDeviceGroupStatusApi({
          id,
          status: type,
        });
        if (result.code === 200) {
          Message.success("操作成功");
          this.refresh();
        }
      } catch (err) {
        Message.error(err.message);
      }
    },

    handleUploadSuccesss(result, itema) {
      // item.installVideo = cloneDeep(result);
      // item.push(result);
      this.tableData1.map((item) => {
        item.installVideo = cloneDeep(result);
      });
    },
    handleUploadRemoves(file, itema) {
      // item.installVideo = null;
      // const index = item.findIndex((val) => val.key === file.key);
      // if (index === -1) return;
      // item.splice(index, 1);
      this.tableData1.map((item) => {
        item.installVideo = null;
      });
    },
    //换机
    handleTrade(data) {
      this.tradeVisible = true;
      this.tradeDeviceInfo = cloneDeep(data);
    },
  },
};
</script>

<style scoped lang="scss">
.file-container {
  display: flex;
  gap: 50px;
  flex-wrap: wrap;

  .file-list {
    font-size: 18px;
  }
}
.QR-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  background: transparent;

  .qr-left {
    flex: 1;
    text-align: left;
    padding-right: 20px;

    .qr-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }

    .qr-subtitle {
      font-size: 14px;
      color: #666;
      margin-bottom: 15px;
    }

    .qr-info {
      .info-item {
        font-size: 13px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 5px;
      }
    }
  }

  .qr-right {
    flex-shrink: 0;
    width: 150px;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;

    .qr-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}
//.QR-box {
//  display: flex;
//  flex-direction: column;
//  align-items: center;
//  text-align: center;
//  width: 100%;
//  max-width: 300px;
//  margin: 0 auto;
//  padding: 10px;
//
//  .qr-title {
//    font-size: 18px;
//    font-weight: bold;
//    margin-bottom: 5px;
//  }
//
//  .qr-subtitle {
//    font-size: 14px;
//    //margin-bottom: 10px;
//  }
//
//  .qr-image {
//    width: 100%;
//    max-width: 150px;
//    //margin-bottom: 5px;
//  }
//
//  .qr-footer {
//    font-size: 12px;
//    color: #888;
//  }
//}
.ladder {
  display: flex;
  flex-direction: column;
  gap: 10px;
  .ladder-list {
    display: flex;
    align-items: center;
    //justify-content: space-between;

    .ladder-item {
      width: 350px;
      display: flex;
      flex-wrap: nowrap;
      gap: 20px;
      margin-right: 20px;
      span {
        text-wrap: nowrap;
      }
    }
  }
}
</style>
