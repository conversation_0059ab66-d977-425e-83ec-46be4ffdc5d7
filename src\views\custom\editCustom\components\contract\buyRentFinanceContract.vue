<!--
 * @Author: wskg
 * @Date: 2025-02-10 10:30:19
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-17 18:44:24
 * @Description: 购机、租赁、融资合约
 -->
<template>
  <div class="app-container">
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="65%"
      :confirm-button-disabled="confirmLoading"
      :method-type="methodType"
      :no-footer="methodType === 'info'"
      confirm-text="确认提交"
      @ok="handleSubmit"
      @cancel="closeDrawer">
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="formSubmit">
        <template #isSupplement>
          <el-radio-group
            v-model="form.isSupplement"
            :disabled="methodType !== 'add'">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </template>
        <template #isInstall>
          <el-radio-group
            v-model="form.isInstall"
            :disabled="methodType !== 'add'"
            @input="handleInstallChange">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </template>
        <!-- 客户地址信息 -->
        <template #customerInfo>
          <el-col :span="8" style="padding-left: 0; padding-right: 13px">
            <el-form-item label="收货地址：" prop="addressId">
              <el-select
                v-model="form.addressId"
                style="width: 100%"
                placeholder="请选择收货地址"
                clearable
                :disabled="methodType === 'info'"
                @change="handleAddressChange">
                <el-option
                  v-for="item in addressList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系人：" prop="consignee">
              <el-input
                v-model="form.consignee"
                placeholder="请输入联系人"
                :disabled="methodType === 'info'"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话：" prop="consigneePhone">
              <el-input
                v-model="form.consigneePhone"
                placeholder="请输入联系电话"
                :disabled="methodType === 'info'"></el-input>
            </el-form-item>
          </el-col>
        </template>
        <template #signId>
          <el-select
            v-model="form.signId"
            style="width: 100%"
            :disabled="methodType === 'info'"
            filterable>
            <el-option
              v-for="item in userList"
              :key="item.value"
              :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </template>
        <template #agentId>
          <el-select
            v-model="form.agentId"
            style="width: 100%"
            :disabled="true"
            filterable>
            <el-option
              v-for="item in userList"
              :key="item.value"
              :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </template>
        <!-- 合约附件 -->
        <template #attachments>
          <div>
            <!-- v-if="methodType !== 'info'"-->
            <PDFFiles
              :file-list="form.attachments"
              :limit="5"
              :disabled="methodType === 'info'"
              is-auth
              permit-code="@ums:manage:contract:download"
              @uploadSuccess="handleLogFileUploadSuccess" />
            <!--<div v-else>-->
            <!--  <p v-for="item in form.attachments" :key="item.key">-->
            <!--    {{ item.name }}-->
            <!--  </p>-->
            <!--</div>-->
          </div>
        </template>
        <!-- 合约机器信息 -->
        <template #signMachineInfo>
          <!-- 补录 -->
          <ProTable
            v-if="form.isSupplement"
            ref="ProTable"
            :show-search="false"
            :show-pagination="false"
            :show-setting="false"
            :show-loading="false"
            :height="350"
            :columns="columns"
            :data="tableData">
            <template #btn>
              <el-button
                v-if="methodType !== 'info'"
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="handleDispatch">
                选择机器
              </el-button>
            </template>
            <template #bindHost="{ row }">
              <el-select
                v-if="row.hostType !== '2008'"
                v-model="row.bindHost"
                filterable
                clearable
                style="width: 100%">
                <el-option
                  v-for="item in hostNumberOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </template>
            <template #action="{ row, index }">
              <div class="fixed-width">
                <el-button
                  icon="el-icon-view"
                  @click="handleViewOrEdit(row, 'info', index)">
                  查看
                </el-button>
                <el-button
                  v-if="methodType !== 'info'"
                  icon="el-icon-edit"
                  @click="handleViewOrEdit(row, 'edit', index)">
                  编辑
                </el-button>
                <el-button
                  v-if="methodType !== 'info'"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDelete(row)">
                  移除
                </el-button>
              </div>
            </template>
          </ProTable>
          <!-- 新增 -->
          <ProTable
            v-else
            ref="ProTable"
            :show-search="false"
            :show-pagination="false"
            :show-setting="false"
            :show-loading="false"
            :height="350"
            :columns="buyColumns"
            :data="tableData">
            <template #btn>
              <el-button
                v-if="methodType === 'add'"
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="addBuyMachine">
                添加机器
              </el-button>
            </template>
            <!-- 主机类型 -->
            <template #type="{ row }">
              <el-select
                v-model="row.hostType"
                placeholder="请选择主机类型"
                style="width: 100%"
                size="small"
                :disabled="methodType === 'info'"
                @change="(e) => handleHostTypeChange(e, row)">
                <el-option
                  v-for="item in hostTypeListOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </template>
            <!-- 机器型号 -->
            <template #productId="{ row }">
              <div
                v-if="row.hostType !== '2008'"
                style="
                  display: flex;
                  justify-content: space-between;
                  gap: 20px;
                ">
                <el-input
                  v-model="row.productInfo"
                  disabled
                  size="small"
                  placeholder="请选择选配件" />
                <el-button
                  v-if="methodType !== 'info'"
                  type="primary"
                  size="mini"
                  @click="handleSelectSpare(row)">
                  选择
                </el-button>
              </div>
              <el-cascader
                v-else
                ref="ProductIds"
                v-model="row.productId"
                filterable
                clearable
                :options="options"
                style="width: 100%"
                size="small"
                placeholder="请选择机器型号"
                :show-all-levels="false"
                :disabled="methodType === 'info'"
                :props="{
                  label: 'name',
                  value: 'id',
                  children: 'children',
                  expandTrigger: 'click',
                }"
                leaf-only
                @change="(e) => handleProductIdChange(e, row)"></el-cascader>
            </template>
            <template #percentage="{ row }">
              <el-select
                v-model="row.percentage"
                size="small"
                placeholder="请选择成色"
                clearable
                filterable
                :disabled="methodType === 'info'">
                <el-option
                  v-for="item in percentageOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </template>
            <template #deviceOn="{ row }">
              <el-select
                v-model="row.deviceOn"
                style="width: 100%"
                size="small"
                placeholder="请选择设备新旧"
                clearable
                filterable
                :disabled="methodType === 'info'">
                <el-option
                  v-for="item in deviceOnOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </template>
            <template #action="{ row, index }">
              <div class="fixed-width">
                <el-button
                  icon="el-icon-view"
                  @click="handleViewOrEdit(row, 'info', index)">
                  查看
                </el-button>
                <el-button
                  v-if="methodType !== 'info'"
                  icon="el-icon-edit"
                  @click="handleViewOrEdit(row, 'edit', index)">
                  编辑
                </el-button>
                <el-button
                  v-if="methodType !== 'info'"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDelete(row, index)">
                  移除
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 购机合约明细 -->
    <BuyMachineInfo
      ref="buyMachineInfo"
      :is-supplement="form.isSupplement"
      :contract-type="contractType"
      :customer-id="customerId"
      @confirmContractInfo="confirmContractInfo" />
    <!-- 租赁合约明细 -->
    <RentContractInfo
      ref="rentContractInfo"
      :is-supplement="form.isSupplement"
      :edit-type="methodType"
      :contract-type="contractType"
      :customer-id="customerId"
      @confirmContractInfo="confirmContractInfo" />
    <!-- 融资合约明细 -->
    <FinancingContractInfo
      ref="financingContractInfo"
      :is-supplement="form.isSupplement"
      :edit-type="methodType"
      :contract-type="contractType"
      :customer-id="customerId"
      @confirmContractInfo="confirmContractInfo" />
    <!-- 选择出库机器 -->
    <ChooseDispatchMachine
      :dialog-visible.sync="dispatchDialogVisible"
      :selected-data="tableData"
      @confirmDispatch="confirmDispatch" />
    <!-- 选配件选择 -->
    <SparePart
      ref="sparePart"
      :dialog-visible.sync="sparePartDialog"
      :spare-type="hostType"
      @confirm="confirmSpare" />
  </div>
</template>

<script>
import PDFFiles from "@/components/ProUpload/pdfFiles.vue";
import BuyMachineInfo from "@/views/custom/editCustom/components/contract/buyMachineInfo.vue";
import ChooseDispatchMachine from "@/views/custom/editCustom/components/contract/chooseDispatchMachine.vue";
import SparePart from "@/views/procure/cpns/sparePart.vue";
import RentContractInfo from "@/views/custom/editCustom/components/contract/rentContractInfo.vue";
import FinancingContractInfo from "@/views/custom/editCustom/components/contract/financingContractInfo.vue";

import { cloneDeep } from "lodash";
import { addCustomerContractApi, getCustomerUserListApi } from "@/api/customer";
import { getAddressListApi } from "@/api/operator";
import { Message } from "element-ui";
import { transformFormParams } from "@/utils";
import { productAllApi } from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";
import { mapGetters } from "vuex";

export default {
  name: "BuyRentFinanceContract",
  components: {
    FinancingContractInfo,
    RentContractInfo,
    SparePart,
    BuyMachineInfo,
    PDFFiles,
    ChooseDispatchMachine,
  },
  props: {
    contractType: {
      type: String,
      default: "1201",
    },
  },
  data() {
    const that = this;
    return {
      drawerVisible: false,
      drawerTitle: "",
      confirmLoading: false,
      methodType: "add", // form类型 add/edit/info
      form: {},
      formColumns: [
        {
          dataIndex: "isSupplement",
          title: "是否为补录合约",
          isForm: true,
          formSpan: 8,
          formSlot: "isSupplement",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "customerInfo",
          title: "客户信息",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "customerInfo",
        },
        {
          dataIndex: "code",
          title: "合同编号",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入合同编号",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入合同名称",
              trigger: "change",
            },
          ],
        },

        // {
        //   dataIndex: "addToDeviceGroup",
        //   title: "添加机器信息",
        //   isForm: true,
        //   formSlot: "addToDeviceGroup",
        //   formSpan: 8,
        // },
        {
          dataIndex: "remark",
          title: "摘要",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
        },
        {
          dataIndex: "signTime",
          title: "签约时间",
          isForm: true,
          formSpan: 8,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          prop: [
            {
              required: true,
              message: "请选择签约时间",
              trigger: "change",
            },
          ],
        },
        // {
        //   dataIndex: "startTime",
        //   title: "合约开始时间",
        //   isForm: true,
        //   formSpan: 8,
        //   valueType: "date-picker",
        //   pickerType: "date",
        //   pickerFormat: "yyyy-MM-dd",
        //   valueFormat: "yyyy-MM-dd",
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择合约开始时间",
        //       trigger: "change",
        //     },
        //   ],
        // },
        // {
        //   dataIndex: "endTime",
        //   title: "合约截止时间",
        //   isForm: true,
        //   formSpan: 8,
        //   valueType: "date-picker",
        //   pickerType: "date",
        //   pickerFormat: "yyyy-MM-dd",
        //   valueFormat: "yyyy-MM-dd",
        //   prop: [
        //     {
        //       required: true,
        //       message: "请选择合约截止时间",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "signId",
          title: "销售",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          formSlot: "signId",
          option: [],
        },
        {
          dataIndex: "agentId",
          title: "商务",
          isForm: true,
          formSpan: 8,
          valueType: "select",
          formSlot: "agentId",
          option: [],
        },
        {
          dataIndex: "isInstall",
          title: "是否需要安装",
          isForm: true,
          formSpan: 8,
          formSlot: "isInstall",
          option: [],
        },
        {
          dataIndex: "attachments",
          title: "合约附件",
          isForm: true,
          formSpan: 16,
          formSlot: "attachments",
        },
        {
          dataIndex: "signMachineInfo",
          title: "签约机器信息",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "signMachineInfo",
        },
      ],
      userList: [],
      ladderList: [],
      // 补录签约机器信息
      columns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => {
            return that.dictMap[row.hostType] || "未知类型";
          },
        },
        // {
        //   dataIndex: "bindHost",
        //   title: "绑定主机",
        //   isTable: true,
        //   tableSlot: "bindHost",
        //   width: 170,
        // },
        {
          dataIndex: "productInfo",
          title: "品牌/型号",
          isTable: true,
        },
        // {
        //   dataIndex: "brand",
        //   title: "",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "status",
        //   title: "合同状态",
        //   isTable: true,
        //   formatter: (row) => row.status?.label,
        // },
        {
          dataIndex: "settleStatus",
          title: "结算状态",
          isTable: true,
          formatter: (row) => {
            if (row.settleStatus?.label) {
              return row.settleStatus?.label;
            } else {
              switch (row.settleStatus) {
                case "0":
                  return "未结算";
                case "1":
                  return "已结算";
                default:
                  return "";
              }
            }
          },
        },
        {
          dataIndex: "settleMethod",
          title: "结算方式",
          isTable: true,
          formatter: (row) => {
            if (row.settleMethod?.label) {
              return row.settleMethod?.label;
            } else {
              switch (row.settleMethod) {
                case "FULL":
                  return "全款";
                case "INSTALLMENT":
                  return "分期付款";
                default:
                  return "";
              }
            }
          },
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => {
            if (row.serType?.label) {
              return row.serType?.label;
            } else {
              switch (row.serType) {
                case "NO_WARRANTY":
                  return "购机不保";
                case "WARRANTY":
                  return "购机质保";
                case "BUY_HALF":
                  return "购机半保";
                case "BUY_FULL":
                  return "购机全保";
                case "PACKAGE_HALF":
                  return "包量半保";
                case "PACKAGE_ALL":
                  return "包量全保";
                case "RENT_HALF":
                  return "租赁半保";
                case "RENT_FULL":
                  return "租赁全保";
                case "FINANCING_HALF":
                  return "融资半保";
                case "FINANCING_FULL":
                  return "融资全保";
                case "OTHER":
                  return "其它";
                default:
                  return "";
              }
            }
          },
        },
        {
          dataIndex: "action",
          title: "操作",
          tooltip: false,
          isTable: true,
          tableSlot: "action",
          width: 200,
        },
      ],
      // 新增
      buyColumns: [
        {
          dataIndex: "type",
          title: "主机类型",
          isTable: true,
          tableSlot: "type",
          minWidth: 100,
        },
        {
          dataIndex: "productId",
          title: "机器型号/选配件",
          isTable: true,
          tableSlot: "productId",
          minWidth: 120,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          tableSlot: "deviceOn",
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          tableSlot: "percentage",
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => {
            if (row.serType?.label) {
              return row.serType?.label;
            } else {
              switch (row.serType) {
                case "NO_WARRANTY":
                  return "购机不保";
                case "WARRANTY":
                  return "购机质保";
                case "BUY_HALF":
                  return "购机半保";
                case "BUY_FULL":
                  return "购机全保";
                case "PACKAGE_HALF":
                  return "包量半保";
                case "PACKAGE_ALL":
                  return "包量全保";
                case "RENT_HALF":
                  return "租赁半保";
                case "RENT_FULL":
                  return "租赁全保";
                case "FINANCING_HALF":
                  return "融资半保";
                case "FINANCING_FULL":
                  return "融资全保";
                case "OTHER":
                  return "其它";
                default:
                  return "";
              }
            }
          },
        },
        // {
        //   dataIndex: "totalAmount",
        //   title: "机器总价（元）",
        //   isTable: true,
        //   tableSlot: "totalAmount",
        //   minWidth: 100,
        // },
        // {
        //   dataIndex: "intentionAmount",
        //   title: "定金（元）",
        //   isTable: true,
        //   tableSlot: "intentionAmount",
        //   minWidth: 100,
        // },
        {
          dataIndex: "action",
          title: "操作",
          tooltip: false,
          isTable: true,
          width: 200,
          tableSlot: "action",
          fixed: "right",
        },
      ],
      tableData: [],
      editType: "edit", // 列表合同明细类型 edit/info
      dispatchDialogVisible: false,
      currentRow: null,
      addressList: [],
      fullAddressList: [],
      hostNumberOptions: [],
      percentageOptions: [],
      deviceOnOptions: [],
      hostType: "", // 主机类型
      hostTypeListOptions: [],
      options: [],
      sparePartDialog: false,
      selectedSparePart: null,
      currentIndex: 0, // 当前操作索引
      customerId: "",
      dictMap: {},
    };
  },
  computed: {
    ...mapGetters({ permits: "permits" }),
    hasDownloadPermission() {
      return this.permits.some(
        (item) => item.permit === "@ums:manage:contract:download"
      );
    },
  },
  watch: {
    "form.isSupplement": {
      handler(val) {
        if (val) {
          this.formColumns.forEach((item) => {
            if (item.dataIndex === "code") {
              item.isForm = true;
            }
          });
        } else {
          this.formColumns.forEach((item) => {
            if (item.dataIndex === "code") {
              item.isForm = false;
            }
          });
        }
        if (!this.form.isSupplement && this.methodType === "info") {
          this.formColumns.forEach((item) => {
            if (item.dataIndex === "code") {
              item.isForm = true;
            }
          });
        }
      },
      immediate: true,
    },
    contractType: {
      handler(val) {
        if (val === "1201") {
          this.columns.forEach((item) => {
            if (
              item.dataIndex === "settleStatus" ||
              item.dataIndex === "settleMethod"
            ) {
              item.isTable = true;
            }
          });
        } else {
          this.columns.forEach((item) => {
            if (
              item.dataIndex === "settleStatus" ||
              item.dataIndex === "settleMethod"
            ) {
              item.isTable = false;
            }
          });
        }
      },
    },
  },
  mounted() {
    this.init();
    this.getProductType();
    this.getDeviceOnOptions();
    this.getPercentage();
    this.loadDictMap();
  },
  methods: {
    visible(val, type) {
      this.addressList = [];
      this.fullAddressList = [];
      this.tableData = [];
      this.userList = [];
      this.hostNumberOptions = [];
      this.methodType = type;
      this.form = cloneDeep(val);
      this.customerId = val.customerId;
      if (
        this.form.isSupplement === undefined ||
        this.form.isSupplement === null
      ) {
        this.$set(this.form, "isSupplement", true);
      }
      if (type === "add") {
        this.$set(
          this.form,
          "agentId",
          JSON.parse(localStorage.getItem("userInfo")).id
        );
        this.$set(this.form, "isInstall", true);
      }

      if (
        this.form.customerContractItems &&
        this.form.customerContractItems.length >= 1
      ) {
        this.tableData = this.form.customerContractItems.map((item) => {
          // if (item.fullIdPath) {
          //   const inputString = item.fullIdPath;
          //   const trimmedString = inputString.replace(/^\/|\/$/g, "");
          //   const parts = trimmedString.split("/");
          //   item.fullIdPath =
          //     Array.isArray(parts) &&
          //     parts.reduce((acc, part, index) => {
          //       if (index === 0) {
          //         acc.push(`/${part}`);
          //       } else {
          //         acc.push(`${acc[index - 1]}/${part}`);
          //       }
          //       return acc;
          //     }, []);
          // }
          return transformFormParams(item, ["productId"]);
        });
      }
      this.operatList();
      this.getCustomerAddressList();

      this.$nextTick(() => {
        this.drawerTitle = this.titleMap(type);
        this.drawerVisible = true;
      });
    },
    loadDictMap() {
      dictTreeByCodeApi(2000).then((res) => {
        this.dictMap = res.data.reduce((acc, item) => {
          acc[item.value] = item.label;
          return acc;
        }, {});
      });
    },
    handleSubmit() {
      this.$refs.ProForm.handleSubmit();
    },
    async formSubmit(val) {
      try {
        // 非补录情况下，地址信息为必填
        if (!val.isSupplement) {
          if (!val.addressId) {
            this.$message.warning("请选择收货地址");
            return;
          }
          if (!val.consignee) {
            this.$message.warning("请填写联系人");
            return;
          }
          if (!val.consigneePhone) {
            this.$message.warning("请填写联系电话");
            return;
          }
        }
        if (!this.tableData.length) {
          this.$message.warning("请选择机器信息");
          return;
        }
        this.confirmLoading = true;
        const args = {
          ...val,
          customerContractItems: this.tableData,
          contractType: this.contractType,
        };
        if (!args.isSupplement && this.methodType === "add") {
          delete args.code;
        }
        const result = await addCustomerContractApi(args);
        if (result.code === 200) {
          this.$message.success("操作成功");
          this.$emit("refresh");
          this.drawerVisible = false;
        }
      } finally {
        this.confirmLoading = false;
      }
    },
    // 机器发货
    handleMachineDispatch(row) {
      this.$refs.dispatchMachine.show(row);
    },
    // 新增购机合约
    handleHostTypeChange(e, row) {
      row.productId = "";
      this.$set(row, "productIdName", []);
      this.$set(row, "productInfo", "");
    },
    handleSelectSpare(row) {
      this.selectedSparePart = null;
      if (!row.hostType && row.hostType !== "2008") {
        Message.warning("请选择主机类型");
        return;
      }
      this.selectedSparePart = row;
      this.hostType = row.hostType;
      this.sparePartDialog = true;
    },
    handleProductIdChange(e, row) {
      if (e) {
        row.productId = e[e.length - 1];
      } else {
        row.productId = "";
      }
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    async getProductType() {
      try {
        const result = await dictTreeByCodeApi(2000);
        if (result.code === 200) {
          this.hostTypeListOptions = result.data;
        }
      } catch (error) {
        this.hostTypeListOptions = [];
      }
    },
    async getDeviceOnOptions() {
      try {
        const result = await dictTreeByCodeApi(1100);
        if (result.code === 200) {
          this.deviceOnOptions = result.data;
        }
      } catch (error) {
        this.deviceOnOptions = [];
      }
    },
    async getPercentage() {
      try {
        const result = await dictTreeByCodeApi(2500);
        if (result.code === 200) {
          this.percentageOptions = result.data;
        }
      } catch (error) {
        this.percentageOptions = [];
      }
    },

    // 新增购买机器信息
    addBuyMachine() {
      this.tableData.push({
        productId: null,
        percentage: null,
        deviceOn: null,
      });
    },
    confirmSpare(row) {
      this.$set(this.selectedSparePart, "productInfo", row.modeType);
      this.selectedSparePart.productId = row.id;
      this.sparePartDialog = false;
    },
    // 列表明细
    handleViewOrEdit(row, type, index = 0) {
      this.currentRow = cloneDeep(row);
      if (!this.form.isSupplement) {
        this.currentIndex = index;
      }
      if (this.contractType === "1201") {
        this.$refs.buyMachineInfo.visible(this.currentRow, type);
      } else if (this.contractType === "1265") {
        this.$refs.rentContractInfo.visible(this.currentRow, type);
      } else if (this.contractType === "1230") {
        this.$refs.financingContractInfo.visible(this.currentRow, type);
      }
    },
    // 移除
    handleDelete(row, index) {
      if (row.machineNum) {
        this.tableData = this.tableData.filter(
          (item) => item.machineNum !== row.machineNum
        );
      } else {
        this.tableData.splice(index, 1);
      }
    },
    // 确认机器合约信息
    confirmContractInfo(info) {
      if (this.currentRow.machineNum) {
        const index = this.tableData.findIndex(
          (item) => item.machineNum === this.currentRow.machineNum
        );
        if (index !== -1) {
          this.$set(this.tableData, index, cloneDeep(info));
        }
      } else {
        this.$set(this.tableData, this.currentIndex, cloneDeep(info));
      }
    },
    handleLogFileUploadSuccess(result) {
      if (!this.form.attachments) {
        this.$set(this.form, "attachments", []);
      }
      this.form.attachments.push(result);
    },
    handleDispatch() {
      this.dispatchDialogVisible = true;
    },
    confirmDispatch(row) {
      this.tableData = cloneDeep(row);
      this.dispatchDialogVisible = false;
    },
    // 员工列表处理
    operatList() {
      this.userList = [];
      getCustomerUserListApi().then((res) => {
        res.data.map((item) => {
          this.userList.push({
            value: item.id,
            label: item.name,
          });
        });
      });
    },
    async getCustomerAddressList() {
      // 获取用户可用地址
      try {
        if (this.form.customerId) {
          const res = await getAddressListApi(this.form.customerId);
          this.fullAddressList = res.data;
          res.data.map((item) => {
            this.addressList.push({
              value: item.id,
              label: item.address,
            });
          });
        }
      } catch (e) {
        Message.error(e.message);
      }
    },
    handleAddressChange(val) {
      const addressInfo = this.fullAddressList.find((item) => item.id === val);
      if (addressInfo) {
        this.$set(this.form, "consignee", addressInfo.contact);
        this.$set(this.form, "consigneePhone", addressInfo.phone);
      }
    },
    handleIsSupplementChange() {
      this.tableData = [];
    },
    titleMap(type) {
      let title = "";
      switch (this.contractType) {
        case "1201":
          title = "购机";
          break;
        case "1265":
          title = "租赁";
          break;
        case "1230":
          title = "融资";
          break;
        default:
          title = "购机";
      }
      const data = {
        add: `新增${title}合约`,
        edit: `编辑${title}合约`,
        info: `查看${title}合约`,
      };
      return data[type];
    },
    closeDrawer() {
      this.drawerVisible = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
