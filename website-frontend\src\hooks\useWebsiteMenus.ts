import { useQuery } from '@tanstack/react-query';
import { websitePublicApi } from '../services/websitePublic';
import { queryKeys } from './useWebsiteApi';

export interface WebsiteMenuItem {
  key: string;
  label: string;
  path: string;
}

/**
 * 获取网站导航菜单
 */
export function useWebsiteMenus() {
  return useQuery({
    queryKey: queryKeys.publicMenus,
    queryFn: async () => {
      try {
        return await websitePublicApi.getMenus();
      } catch (error) {
        console.warn('Menus API 调用失败:', error);
        return [] as WebsiteMenuItem[];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 分钟
    // 默认 refetchOnWindowFocus = true，实现实时性
    retry: 1,
  });
}