/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-24 18:26:29
 * @Description: API管理
 */

import { get, post, put, del, down, postDown } from "@/utils/request";

// ================== 仓库管理 ==================

export const warehousePageApi = (data) => get(`/warehouse/page`, data);
export const warehouseAddApi = (data) => post(`/warehouse`, data);
export const warehouseEditApi = (data) => put(`/warehouse`, data);
export const warehouseDelApi = (id) => del(`/warehouse/${id}`);
export const warehouseListApi = (data) => get(`/warehouse/list`, data);

export const uploadApi = `/file/upload`; // 文件上传

export const ExportApi = (id) => down(`/report/download/${id}`); // 下载

export const regionTreeApi = (data) => get(`/region/tree`, data);

// ==================  机器仓库  =================
// 机器仓库分页查询
export const machineWarehousePageApi = (data) =>
  get(`/storage-inventory/mechinePage`, data);
// 修改储位与计数器
export const updateMachineWarehouseApi = (data) =>
  put(`/storage-inventory/updateBatch`, data);

// ==================库品管理====================
export const storageInventoryPage = (data) =>
  post(`/storage-article/inventoryPage`, data);
export const storagePageApi = (data) => get(`/storage-inventory/page`, data);
export const storageAddApi = (data) => post(`/storage-inventory`, data);
export const storageEditApi = (data) => put(`/storage-inventory`, data);
export const storageInfoApi = (data) => get(`/storage-inventory/getOne`, data);

export const storageDelApi = (id) => del(`/storage-inventory/${id}`);
export const storageUpApi = `/storage-inventory/import`;
// 库品数据汇总
export const storageStatisticsApi = (data) =>
  get(`/storage-inventory/summary`, data);
// 库品导入
export const storageImportApi = `/storage-inventory/import`;
// 库品数据导出
export const storageExportApi = (data) =>
  postDown(`/storage-inventory/exportInventory`, data);
// 下载导入库品模板
export const storageDownTemplateApi = () =>
  down("/storage-inventory/downTemplate");

export const addStorageInventoryApi = (data) =>
  post(`/storage-inventory/add`, data);

export const inFlowPageApi = (data) =>
  get(`/storage-inventory/inFlowPage`, data); //入库记录
export const outFlowPageApi = (data) =>
  get(`/storage-inventory/outFlowPage`, data); //出库记录
// 添加到采购计划
export const addToPurchasePlanApi = (data) => post(`/purchase-require`, data);

// ================== 出库管理 ==================

export const getOutboundListApi = () => get(`/storage-out-warehouse/list`);
export const getOutboundByPageApi = (data) =>
  get(`/storage-out-warehouse/page`, data);
export const addOutboundApi = (data) => post(`/storage-out-warehouse`, data);
export const updateOutboundApi = (data) =>
  put(`/storage-out-warehouse/update`, data);
export const getOutboundDetailApi = (id) =>
  get(`/storage-out-warehouse/getOne`, { id });
export const deleteOutboundApi = (id) => del(`/storage-out-warehouse/${id}`);
export const examineOutboundApi = (data) => put(`/storage-out-warehouse`, data);

export const outStatusListApi = () =>
  get(`/storage-out-warehouse/out-status-list`);

export const updateOutStorageRemark = (data) =>
  put(`/storage-out-warehouse/update`, data);

// ================== 物品管理 ==================

export const articlePageApi = (data) => get(`/storage-article/page`, data);
export const articleAddApi = (data) => post(`/storage-article`, data);
export const articleEditApi = (data) => put(`/storage-article`, data);

export const articleDelApi = (id) => del(`/storage-article/${id}`);
export const articleListApi = (data) => get(`/storage-article/list`, data);

export const articleGetDetailApi = (id) => get(`/storage-article/detail/${id}`);
// 导入物品
export const importThingApi = "/storage-article/import";
// 下载导入模板
export const downloadThingTemplateApi = () =>
  down("/storage-article/downTemplate");

// ================== 组合物品配置管理 ====================
export const combinationItemConfigApi = (data) =>
  get(`/assembly-config/page`, data);
export const updateCombinationItemConfigApi = (data) =>
  put(`/assembly-config`, data);
export const deleteCombinationItemConfigApi = (id) =>
  del(`/assembly-config/${id}`);

// ================== 入库管理 ==================
export const getInboundListApi = () => get(`/storage-in-warehouse/list`);
export const getInboundByPageApi = (data) =>
  get(`/storage-in-warehouse/page`, data);
export const addInboundApi = (data) => post(`/storage-in-warehouse`, data);
export const updateInboundApi = (data) => post(`/storage-in-warehouse`, data);
export const getInboundDetailApi = (id) =>
  get(`/storage-in-warehouse/getOne`, { id });
export const deleteInboundApi = (id) => del(`/storage-in-warehouse/${id}`);
export const examineInboundApi = (data) =>
  put(`/storage-in-warehouse/updateStatus`, data);
export const downloadInboundTemplateApi = `/api/storage-in-warehouse/downTemplate`;
export const importInboundApi = "/storage-in-warehouse/import";
//

export const manufacturerListApi = () => get(`/manufacturer/list`);

export const productAssocTreeApi = (id) =>
  get(`/product-part/product-association-tree/${id}`);

export const updateRemarksApi = (data) =>
  put(`/storage-in-warehouse/updateRemarks`, data);
export const locationListApi = (data) =>
  get(`/storage-inventory/location-list`, data);
export const inOutTypeListApi = (data) =>
  get(`/storage-in-warehouse/in-out-type-list`, data);
export const inStatusListApi = (data) =>
  get(`/storage-in-warehouse/in-status-list`, data);
export const fixLocationApi = (data) =>
  post(`/storage-in-warehouse/fixLocation`, data);
export const batchList = (data) =>
  get(`/storage-out-warehouse/batchList`, data);

export const checkOneApi = (data) => put(`/storage-out-warehouse`, data);
export const inOutInfoApi = (id) =>
  get(`/storage-out-warehouse/goodsOutWareDetail/${id}`);

//发货单
export const invoicePageApi = (data) => get(`/storage-invoice/page`, data);
export const invoiceAddApi = (data) => post(`/storage-invoice`, data);

export const invoiceEditApi = (data) => put(`/storage-article`, data);
export const invoiceDelApi = (id) => get(`/storage-invoice/closeInvoice/${id}`);
export const invoiceInfoApi = (id) => get(`/storage-invoice/${id}`);

export const selectOutWarehouse = (data) =>
  get(`/storage-invoice/selectOutWarehouse`, data);

export const verifyOutWarehouse = (data) =>
  post(`/storage-invoice/verifyOutWarehouse`, data);
export const invoiceArticleList = (data) =>
  post(`/storage-invoice/invoiceArticleListWithLogistics`, data);

// export const invoiceArticleList = (data) =>
//   post(`/storage-invoice/invoiceArticleList`, data);

// ============== 仓库盘点 =================
export const getWarehouseInventoryListApi = (data) => post(`/stock/page`, data);
// 耗材盘点
export const checkWarehouseInventoryApi = (data) =>
  post(`/stock/stockDetail`, data);
// 导出耗材盘点详情
export const exportWarehouseConsumableInventoryApi = (data) =>
  postDown(`/stock/exportArticle`, data);
// 机器盘点查询
export const getMachineInventoryListApi = (data) =>
  post(`/stock/stockMachine`, data);
// 机器盘点详情分页查询
export const getMachineInventoryDetailListApi = (data) =>
  post(`/stock/takeMachine`, data);
// 机器盘点详情汇总
export const getMachineInventoryDetailTotalApi = (data) =>
  post(`/stock/takeMachineTotal`, data);
// 单行编辑暂存
export const editWarehouseInventoryApi = (data) => post(`/stock/stash`, data);
// 暂存盘点内容
export const saveWarehouseInventoryApi = (data) => post(`/stock`, data);
// 提交盘点内容
export const submitWarehouseInventoryApi = (data) =>
  post(`/stock/submit`, data);
// 盘点数据明细查询
export const getWarehouseInventoryDetailApi = (id) => get(`/stock/${id}`);
// 库存盘点明细分页查询
export const getWarehouseInventoryDetailListApi = (data) =>
  post(`/stock/detailPage`, data);
// 复核盘点数据
export const checkWarehouseInventoryDetailApi = (data) =>
  post(`/stock/approve`, data);
// 盘点数据统计
export const getWarehouseInventoryStatisticsApi = (data) =>
  post(`/stock/stockDetailStatistics`, data);
// 报损报溢
export const lossAndOverflowPageApi = (data) =>
  post(`/stock/pageDiscrepancy`, data);
export const lossAndOverflowListApi = (data) => post(`/stock/pageQuery`, data);
// 报损报溢审核
export const lossAndOverflowApproveApi = (data) =>
  post(`/stock/approveDiscrepancy`, data);
// 报损报溢数据汇总
export const lossAndOverflowTotalApi = (data) =>
  post(`/stock/discrepancyTotal`, data);
// 删除
export const deleteWarehouseInventoryApi = (id) => del(`/stock/${id}`);
// 导出机器盘点数据
export const exportWarehouseInventoryApi = (data) =>
  postDown(`/stock/export`, data);
// 机器盘点汇总数据
export const getMachineInventoryTotalApi = (data) =>
  post(`/stock/stockDetailTotal`, data);
// 耗材盘点一键入库
export const batchInWarehouseApi = (id) => post(`/stock/inStock/${id}`);

// ======================  呆滞物品  ======================
// 呆滞物品列表
export const getStagnantListApi = (data) =>
  post(`/storage-inventory/unsalablePage`, data);
// 修改呆滞状态
export const updateStagnantApi = (data) =>
  post(`/storage-inventory/updateUnsalable`, data);
// 总数据
export const getStagnantTotalApi = (data) =>
  post(`storage-inventory/getTotalInverntory`, data);

// ===============================  机器管理  ==============================
// 机器列表
export const getMachinePageApi = (data) => post(`/machine/page`, data);
// 详情
export const getMachineDetailApi = (id) => get(`/machine/${id}`);
// 编辑
export const updateMachineApi = (data) => post(`/machine`, data);
// 编辑机器储位
export const updateMachineLocationApi = (data) =>
  post(`/machine/updateLocation`, data);
// 机器仓库数据汇总
export const getMachineTotalApi = (data) => get(`machine/summary`);
// 导入机器数据
export const importMachineApi = "/machine/import";
// 下载导入机器数据模板
export const downloadMachineTemplateApi = () => down("/machine/downTemplate");
// 导出机器数据
export const exportMachineApi = (data) => postDown(`/machine/export`, data);
// 机器出入库记录
export const getMachineRecordApi = (data) => post(`/machine-flow/page`, data);

// ===============================  月末库存  ==============================
// 耗材月末库存分页查询
export const getConsumableEndOfMonthApi = (data) =>
  post(`/month-storage-inventory/page`, data);
// 详情
export const getConsumableEndOfMonthDetailApi = (data) =>
  post(`/month-storage-inventory/detail`, data);
// 导出库存数据
export const exportConsumableEndOfMonthApi = (data) =>
  postDown(`/month-storage-inventory/export`, data);
// 获取统计数据
export const getConsumableEndOfMonthTotalApi = (data) =>
  post(`/month-storage-inventory/detailStatistics`, data);

// 机器月末库存分页查询
export const getMachineEndOfMonthApi = (data) =>
  post(`/month-machine-inventory/page`, data);
// 详情
export const getMachineEndOfMonthDetailApi = (data) =>
  post(`/month-machine-inventory/detail`, data);
// 导出库存数据
export const exportMachineEndOfMonthApi = (data) =>
  postDown(`/month-machine-inventory/export`, data);
// 获取统计数据
export const getMachineEndOfMonthTotalApi = (data) =>
  post(`/month-machine-inventory/detailStatistics`, data);
