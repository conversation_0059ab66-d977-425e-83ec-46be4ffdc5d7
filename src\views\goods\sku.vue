<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 11:20:18
 * @Description: 商品管理
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-badge
          :value="calculateSelection.length"
          class="item"
          :hidden="calculateSelection.length === 0"
        >
          <el-button
            type="success"
            class="add-btn"
            size="mini"
            icon="el-icon-plus"
            @click="handleCalculate"
            >PM成本核算计划</el-button
          >
        </el-badge>
      </template>
      <template #type="slotProps">
        {{ slotProps.row.type.label }}
      </template>
      <template #picsUrl="{ row }">
        <el-image
          v-if="row.picsUrl && row.picsUrl.length > 0"
          style="max-width: 100px; max-height: 100px"
          :src="getPicsUrlImg(row)"
          :preview-src-list="[getPicsUrlImg(row)]"
        ></el-image>
      </template>
      <template #saleAttrVals="slotProps">
        <span
          v-for="(item, index) in slotProps.row.saleAttrVals"
          :key="index"
          style="border: 1px solid #ddd"
          >{{ item.name }}: {{ item.val }}
        </span>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleChange"
        ></el-cascader>
      </template>
      <template #saleStatus="slotProps">
        {{ slotProps.row.saleStatus ? "已上架" : "未上架" }}
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleCheck(slotProps.row)"
          >
            查看
          </el-button>
          <el-button
            v-if="
              calculateSelection.findIndex(
                (item) => item.saleSkuId === slotProps.row.saleSkuId
              ) === -1 && slotProps.row.isPm
            "
            type="primary"
            size="mini"
            icon="el-icon-plus"
            @click="handleAddSelectedCalculate(slotProps.row)"
          >
            加入PM成本核算计划
          </el-button>
          <el-button
            v-if="
              calculateSelection.findIndex(
                (item) => item.saleSkuId === slotProps.row.saleSkuId
              ) !== -1 && slotProps.row.isPm
            "
            type="danger"
            size="mini"
            icon="el-icon-plus"
            @click="handleRemoveCalculate(slotProps.row)"
          >
            移除PM成本核算计划
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="95%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <div class="dialog-content-box">
        <el-steps
          v-if="methodType != 'info'"
          align-center
          :active="active"
          finish-status="success"
          class="steps-box"
        >
          <el-step title="商品基本信息"></el-step>
          <el-step title="商品规格库存"></el-step>
          <el-step title="商品详细信息"></el-step>
          <el-step title="商品其他信息"></el-step>
        </el-steps>
        <div :style="{ 'padding-top': methodType != 'info' ? '85px' : '0px' }">
          <div v-show="active == 0 || methodType == 'info'" class="boxa box0">
            <div class="tit-box">商品基本信息</div>
            <div class="sp-content jbxx-box">
              <el-form
                ref="proform_child1"
                :model="form"
                :disabled="methodType == 'info'"
                label-width="140px"
                class="demo-ruleForm"
              >
                <el-row>
                  <el-col :span="24">
                    <el-form-item
                      label="商品名称:"
                      :rules="[
                        {
                          required: true,
                          trigger: 'blur',
                          message: '请输入商品名称',
                        },
                      ]"
                      prop="name"
                    >
                      <el-input
                        v-model="form.name"
                        style="width: 300px"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="24">
                  <el-form-item label="商品品牌:" prop="brandId">
                    <el-select v-model="form.brandId" :multiple="false">
                      <el-option
                        v-for="(item, index) in formcolumns.brandList"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col> -->
                  <el-col :span="24">
                    <el-form-item
                      label="商品分类:"
                      :rules="[
                        {
                          required: true,
                          trigger: 'change',
                          message: '请选择商品分类',
                        },
                      ]"
                      prop="categoryId"
                    >
                      <el-select
                        v-model="form.categoryId"
                        style="width: 300px"
                        @change="handleCategoryChange"
                      >
                        <el-option
                          v-for="item in formcolumns.categoryList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col v-show="form.categoryId" :span="24">
                    <el-form-item label="分类属性:" prop="saleAttrVals">
                      <div
                        v-for="(item, index) in formcolumns.saleAttrValsList"
                        :key="index"
                      >
                        {{ item.name }}:
                        <el-radio-group
                          v-model="item.selectVal"
                          size="small"
                          style="margin-left: 20px"
                        >
                          <el-radio
                            v-for="(it, index) in item.children"
                            :key="index"
                            style="min-width: 120px; margin: 0 10px 10px 0"
                            :label="it.value"
                            border
                            >{{ it.label }}</el-radio
                          >
                        </el-radio-group>
                      </div>

                      <!-- <el-tree class="filter-tree" :data="formcolumns.saleAttrValsList" :props="{
                      children: 'children',
                      label: 'label',
                    }" default-expand-all ref="tree">
                      <span slot-scope="{ node, data }">
                       
                        <el-radio-group v-if="!data.isParent" v-model="node.parent.data.selectVal">
                          <el-radio :label="data.value" @click.native.prevent="
                            radioClick(data.value, node.parent.data.id)
                            ">
                            {{ data.label }}
                          </el-radio>
                        </el-radio-group>
                        <span v-else>{{ data.label }}</span>
                      </span>
                    </el-tree> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item
                      label="商品图:"
                      :rules="[
                        {
                          required: true,
                          trigger: 'change',
                          message: '请选择商品图',
                        },
                      ]"
                      prop="picsUrl"
                    >
                      <ProUpload
                        :file-list="form.picsUrl"
                        :type="methodType"
                        :limit="5"
                        :multiple="true"
                        @uploadSuccess="handleUploadSuccess"
                        @uploadRemove="handleUploadRemove"
                      />
                      建议尺寸：800*800，默认首张图为主图，最多上传5张。
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="24">
                  <el-form-item label="商品状态:" :rules="[
                    {
                      required: true,
                      trigger: 'change',
                      message: '请选择商品状态',
                    },
                  ]" prop="saleStatus">
                    <el-radio-group v-model="form.saleStatus">
                      <el-radio v-for="(item, index) in formcolumns.saleStatusList" :label="item.value">{{ item.label
                      }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col> -->
                </el-row>
              </el-form>
            </div>
          </div>
          <div v-show="active == 1 || methodType == 'info'" class="boxa box1">
            <div class="tit-box">商品规格库存</div>
            <!-- <el-form
              ref="proform_child1"
              :model="form"
              :disabled="methodType == 'info'"
              label-width="140px"
              class="demo-ruleForm"
            >
              <el-row>
                <el-col :span="24">
                  <el-form-item
                    label="商品名称:"
                    :rules="[
                      {
                        required: true,
                        trigger: 'blur',
                        message: '请输入商品名称',
                      },
                    ]"
                    prop="name"
                  >
                    <el-input
                      v-model="form.name"
                      style="width: 300px"
                    ></el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="24">
                  <el-form-item
                    label="商品图:"
                    :rules="[
                      {
                        required: true,
                        trigger: 'change',
                        message: '请选择商品图',
                      },
                    ]"
                    prop="picsUrl"
                  >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form> -->
            <div class="sp-content jbxx-box">
              <productSpecifications
                ref="ProductSpecificationsRef"
                :item-id="itemId"
                :method-type="methodType"
                source-type="sku"
                @successProduct="handleSuccessProduct"
              ></productSpecifications>
            </div>
          </div>
          <div v-show="active == 2 || methodType == 'info'" class="boxa box2">
            <div class="tit-box">商品详细信息</div>
            <div class="sp-content editor-box">
              <div
                v-show="methodType == 'info'"
                v-html="proWangeEditorContent"
              ></div>
              <ProWangeEditor
                v-show="methodType != 'info'"
                ref="ProWangeEditorRef"
              ></ProWangeEditor>
            </div>
          </div>
          <div v-show="active == 3 || methodType == 'info'" class="boxa box3">
            <div class="tit-box">物流信息</div>
            <el-form
              ref="proform_child"
              :model="formOther"
              label-width="140px"
              class="demo-ruleForm"
              :disabled="methodType == 'info'"
            >
              <el-form-item
                label="物流方式"
                :rules="[
                  {
                    required: true,
                    trigger: 'change',
                  },
                ]"
                prop="wlfs"
              >
                <span>{{ formOther.wlfs }}</span>
              </el-form-item>
              <el-form-item label="运费设置" prop="shippingFee">
                <el-input
                  v-model="formOther.shippingFee"
                  style="width: 300px"
                  placeholder="请输入运费设置"
                  type="number"
                  :min="0"
                >
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </ProDrawer>

    <!--  成本核算弹窗  -->
    <ProDialog
      :value="showCalculateDialog"
      title="新增PM成本核算"
      width="80%"
      :confirm-loading="calculateDialogLoading"
      top="50px"
      :no-footer="false"
      @cancel="showCalculateDialog = false"
      @ok="handleConfirmCalculateDialog"
    >
      <ProForm
        ref="calculateForm"
        :form-param="calculateForm"
        :form-list="calculateColumns"
        :confirm-loading="calculateDialogLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="'add'"
        @proSubmit="handleCalculateSubmit"
      >
        <template #productId>
          <el-input v-model="calculateForm.productName" disabled></el-input>
        </template>
        <template #saleSkuIdList>
          <ProTable
            ref="ChooseGoodsTable"
            row-key="id"
            :data="calculateData"
            :columns="calculateTableColumns"
            :show-pagination="false"
            :show-search="false"
            :show-loading="false"
            :show-setting="false"
            :height="400"
          >
            <template #saleAttrVals="slotProps">
              <span
                v-for="(item, index) in slotProps.row.saleAttrVals"
                :key="index"
                style="border: 1px solid #ddd"
                >{{ item.name }}: {{ item.val }}
              </span>
            </template>
            <template #action="{ row }">
              <div class="fixed-width">
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleRemoveCalculateData(row)"
                  >移除</el-button
                >
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>
<script>
import {
  itemSummaryListApi,
  classifyListApi,
  classifyInfoApi,
  itemDetailByIdApi,
  addOfferPriceApi,
} from "@/api/goods";
import { getFullProductTreeApi, productAllApi } from "@/api/dispose";

import { brandListApi } from "@/api/brand";
import { isEmpty, cloneDeep } from "lodash";
import ProUpload from "@/components/ProUpload/index.vue";
import ProWangeEditor from "@/components/ProWangeEditor/index.vue";
import productSpecifications from "./productSpecifications.vue";

import { dictTreeByCodeApi, dictTreeByCodeApi2 } from "@/api/user";
import { Message } from "element-ui";
export default {
  name: "SkuManage",
  components: {
    ProWangeEditor,
    productSpecifications,
    ProUpload,
  },
  mixins: [],
  props: {},
  data() {
    return {
      itemId: "",
      active: 0,
      activeName: "0",
      // 列表
      productIdName: [],
      spareiTypeList: [],
      tableData: [],
      options: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        lastIds: null,
      },
      columns: [
        {
          dataIndex: "lastIds",
          title: "适用机型",
          isSearch: true,
          clearable: true,
          valueType: "product",
        },
        {
          dataIndex: "categoryId",
          title: "商品分类",
          isSearch: true,
          clearable: true,
          formSpan: 8,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          clearable: true,
          isSearch: true,
          formSpan: 8,
          valueType: "select",
          option: [
            { label: "上架", value: "ON_SALE" },
            { label: "下架", value: "NO_SALE" },
          ],
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          clearable: true,
          isTable: true,
          isSearch: true,
          formSpan: 16,
          valueType: "input",
          minWidth: 120,
        },

        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
          clearable: true,
          isSearch: true,
          formSpan: 16,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          width: 300,
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          clearable: true,
          isSearch: true,
          formSpan: 16,
          width: 120,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          clearable: true,
          isSearch: true,
          formSpan: 16,
          width: 180,
          valueType: "input",
        },

        // {
        //   dataIndex: "商品类型",
        //   title: "商品类型",
        //   isTable: true,
        // },
        {
          dataIndex: "categoryName",
          title: "商品分类",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "categoryName1",
          title: "备件登记",
          isTable: true,
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
          isTable: true,
          tableSlot: "saleStatus",
        },
        {
          dataIndex: "expectLife",
          title: "更换频次",
          isTable: true,
        },
        {
          dataIndex: "itemSaleAttrVals",
          title: "所属单元",
          isTable: true,
          formatter: (row) => row?.itemSaleAttrVals["61"] || "",
        },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
          formatter: (row) => row.type?.label,
        },
        {
          dataIndex: "soldOutNum",
          title: "已售卖数量",
          isTable: true,
        },
        {
          dataIndex: "spareLevel",
          title: "备件等级",
          isTable: false,
          width: 120,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(3400),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "repFrequency",
          title: "更换频次",
          isTable: false,
          width: 120,
          isSearch: true,
          valueType: "select",
          clearable: true,
          multiple: true,
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(3300),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "unitList",
          title: "所属单元",
          width: 150,
          isTable: false,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(3200),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "productPartTypeList",
          title: "物品小类",
          width: 150,
          isTable: false,
          isSearch: true,
          clearable: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "Actions",
          width: 240,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      methodType: "add", //新增
      confirmLoading: false,
      dialogTitle: "",
      dialogVisible: false,
      cloneDeepPicsUrl: [],
      form: {
        name: null,
        brandId: null,
        picsUrl: [],
        categoryId: null,
        saleAttrVals: [],
        saleStatus: "NO_SALE",
      },
      formcolumns: {
        brandList: [],
        categoryList: [],
        saleAttrValsList: [],
        saleStatusList: [
          { label: "上架", value: "ON_SALE" },
          { label: "下架", value: "NO_SALE" },
        ],
      },
      formOther: {
        wlfs: "快递配送",
        shippingFee: 0,
      },
      proWangeEditorContent: null,
      rowInfo: {},
      calculateSelection: [],
      cacheProductId: "",
      showCalculateDialog: false,
      calculateDialogLoading: false,
      calculateData: [],
      calculateForm: {},
      calculateColumns: [
        {
          dataIndex: "productId",
          title: "适用机型",
          isForm: true,
          formSlot: "productId",
          disable: true,
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请选择适用机型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "name",
          title: "报价单名称",
          isForm: true,
          valueType: "input",
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入报价单名称",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "saleSkuIdList",
          title: "销售商品列表",
          isForm: true,
          formSlot: "saleSkuIdList",
          prop: [
            {
              required: true,
              message: "请选择销售商品",
              trigger: "change",
            },
          ],
        },
      ],
      calculateTableColumns: [
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
        },

        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          width: 300,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
        },
      ],
    };
  },
  computed: {},
  watch: {},
  created() {
    if (this.$route.params.itemCode) {
      this.handleCheck(this.$route.params);
    }
  },
  mounted() {
    this.$refs.ProTable.refresh();
    this.init();
  },
  methods: {
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
        this.$refs.ProTable.refresh();
      });
      brandListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.formcolumns.brandList = (res.data.rows || []).map((item) => ({
          label: item.brandName,
          value: item.id,
        }));
      });
      classifyListApi({
        pageNumber: 1,
        pageSize: 99999,
      }).then((res) => {
        this.columns[1].option = (res.data.rows || []).map((item) => ({
          label: item.name,
          value: item.id,
        }));
      });
    },
    handleChange(item) {
      console.log(item);
      this.queryParam.lastIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.lastIds.push(id);
      });
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },

    /**
     * 商品分类切换
     * @param {String} val 选中的商品分类
     * @param {Object} echoData 回显数据
     */
    handleCategoryChange(val, echoData) {
      if (val) {
        classifyInfoApi({
          id: val,
        }).then((res) => {
          this.formcolumns.saleAttrValsList = (res.data?.tagList || []).map(
            (item) => ({
              ...item,
              isParent: true,
              label: item.name,
              value: item.id,
              selectVal: echoData?.[item.id] || null,
              children: item.value.map((itemC) => ({
                isParent: false,
                label: itemC,
                value: itemC,
              })),
            })
          );
        });
      } else {
        this.formcolumns.saleAttrValsList = [];
      }
    },

    /**
     * 第二步 商品规格成功
     */
    handleSuccessProduct() {
      this.active++;
    },

    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign({}, parameter);
      const newProductId = requestParameters.lastIds
        ? requestParameters.lastIds[0]
        : "";
      if (newProductId !== this.cacheProductId) {
        this.cacheProductId = newProductId;
        this.calculateSelection = [];
      }
      itemSummaryListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows.map((item) => ({
            ...item,
            saleStatus: item.saleStatus === "ON_SALE",
          }));
          this.localPagination.total = parseInt(res.data.total);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.form);
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
    },

    //触发详情
    handleCheck(row) {
      this.dialogTitle = `查看 - ${row.itemName}`;
      this.methodType = "info";
      this.dialogVisible = true;
      this.form.picsUrl = [];
      this.getQueryDetail(row);
      localStorage.removeItem("detailHtml");
    },

    getQueryDetail(row) {
      itemDetailByIdApi(row.itemId).then((res) => {
        const data = res.data || {};
        this.rowInfo = data;
        this.cloneDeepPicsUrl = cloneDeep(data.picsUrl || []);
        this.form = {
          ...this.form,
          name: data.name,
          brandId: data.brandId,
          picsUrl: cloneDeep(data.picsUrl || []),
          categoryId: data.categoryId,
          saleAttrVals: data.saleAttrVals || [],
          saleStatus: data.saleStatus,
        };
        this.formOther = {
          ...this.formOther,
          shippingFee: data.shippingFee,
        };
        this.handleCategoryChange(data.categoryId, data.saleAttrVals);
        this.$nextTick((_) => {
          if (this.methodType === "info") {
            this.$refs.ProductSpecificationsRef?.echo(data);
            this.$refs.ProWangeEditorRef?.echo(data.detailHtml);
            this.proWangeEditorContent = data.detailHtml;
          }
        });
      });
    },
    handleRemove(file) {
      console.log(file);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleDownload(file) {
      console.log(file);
    },
    // 图片处理
    handleUploadSuccess(result) {
      this.form.picsUrl.push(result);
    },
    handleUploadRemove(file) {
      const index = this.form.picsUrl.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      this.form.picsUrl.splice(index, 1);
    },
    /**
     * 可以去单选框的选中值
     */
    radioClick(ownVal, parentId) {
      const temp = this.formcolumns.saleAttrValsList.find(
        (item) => item.id == parentId
      );
      temp.selectVal == ownVal
        ? (temp.selectVal = "")
        : (temp.selectVal = ownVal);
    },
    handleAddSelectedCalculate(row) {
      this.calculateSelection.push(row);
    },
    handleRemoveCalculate(row) {
      const index = this.calculateSelection.findIndex(
        (val) => val.saleSkuId === row.saleSkuId
      );
      if (index === -1) return;
      this.calculateSelection.splice(index, 1);
    },
    handleRemoveCalculateData(row) {
      const index = this.calculateData.findIndex(
        (val) => val.saleSkuId === row.saleSkuId
      );
      if (index === -1) return;
      this.calculateData.splice(index, 1);
      this.handleRemoveCalculate(row);
      this.calculateForm.saleSkuIdList = this.calculateData.map(
        (item) => item.saleSkuId
      );
    },
    async handleCalculate() {
      console.log(this.queryParam.lastIds, this.calculateSelection);
      const productId = this.queryParam.lastIds
        ? this.queryParam.lastIds[0]
        : "";
      if (!productId) {
        Message.error("请选择机型");
        return;
      }
      if (this.calculateSelection.length === 0) {
        Message.error("请选择销售商品SKU");
        return;
      }
      this.calculateData = [...this.calculateSelection];
      const result = await getFullProductTreeApi(productId);
      const {
        data: { brand, tree, serial, machine },
      } = result;
      this.calculateForm = {
        productId: productId,
        productName: brand + "/" + tree + "/" + serial + "/" + machine,
        saleSkuIdList: this.calculateData.map((item) => item.saleSkuId),
      };
      this.showCalculateDialog = true;
    },
    handleConfirmCalculateDialog() {
      this.$refs.calculateForm.handleSubmit();
    },
    async handleCalculateSubmit() {
      try {
        this.calculateDialogLoading = true;
        const args = { ...this.calculateForm };
        if (args.saleSkuIdList.length === 0) {
          Message.error("销售商品列表不能为空");
          return;
        }
        delete args.productName;
        const result = await addOfferPriceApi(args);
        if (result.code === 200) {
          this.calculateData = [];
          this.calculateForm = {};
          this.calculateSelection = [];
          Message.success("操作成功");
          this.showCalculateDialog = false;
        }
      } catch (err) {
        Message.error(err.message);
      } finally {
        this.calculateDialogLoading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-upload--picture-card,
::v-deep .el-upload-list__item {
  width: 120px;
  height: 120px;
}

.sp-content {
  padding: 10px;
  width: 90%;
  margin: auto;
}

.add-sp-box {
  z-index: 2;
  position: absolute;
  bottom: 0;
  text-align: center;
  width: 100%;
  background: #fff;
  padding: 10px 0;
  border-top: 1px solid #ebeaea;
}

.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-content-box {
  position: relative;
  height: 100%;
  overflow: scroll;
  padding-bottom: 80px;
  .steps-box {
    position: absolute;
    width: 80%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    z-index: 2;
  }

  .content-fix {
    height: calc(100vh - 110px);
    overflow: auto;
  }

  .tit-box {
    width: 90%;
    padding: 5px 10px;
    color: #409eff;
    position: relative;
    margin: 20px auto;
    font-size: 16px;
    font-weight: 800;

    &::before {
      content: "";
      width: 5px;
      height: 20px;
      background: #409eff;
      display: inline-block;
      position: absolute;
      left: -1px;
      top: 4px;
    }
  }
}
.boxa {
}
</style>
