<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 18:40:04
 * @Description: 用料记录
 -->

<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      sticky
      row-key="label"
      :local-pagination="localPagination"
      :data="tableData"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:machine:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 450px"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>
<script>
import { usedListApi, usedListExportApi } from "@/api/statisics";
import { cloneDeep } from "lodash";
import { filterParam, filterParamRange, mulAmount } from "@/utils";
import { productAllApi } from "@/api/dispose";
import { handleExcelExport } from "@/utils/exportExcel";
export default {
  name: "UsedManage",
  data() {
    return {
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      options: [],
      queryParam: {},
      columns: [
        {
          dataIndex: "orderCode",
          title: "更换单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "lastIds",
          title: "品牌/产品树",
          isSearch: true,
          valueType: "product",
          clearable: true,
          // searchSlot: "fullIdPath",
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编码",
          minWidth: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          valueType: "input",
          isSearch: true,
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 130,
        },
        {
          dataIndex: "brand",
          title: "品牌/机型",
          isTable: true,
          formatter: (row) => {
            const brand = row?.brand ?? "";
            const machine = row?.machine ?? "";
            if (brand && machine) {
              return `${brand}/${machine}`;
            }
            return brand || machine;
          },
          minWidth: 130,
        },
        // {
        //   dataIndex: "machine",
        //   title: "机型",
        //   isTable: true,
        //   width: 120,
        // },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          placeholder: "1号机： 1",
          width: 120,
          formatter: (row) => row.deviceGroup.label,
        },
        {
          dataIndex: "skuSource",
          title: "仓库类型",
          isTable: true,
          // isSearch: true,
          width: 150,
          valueType: "select",
          // option: [
          //   { label: "工程师领料", value: "ENGINEER_APPLY" },
          //   { label: "商城购入", value: "MALL" },
          //   { label: "领料单", value: "APPLY" },
          //   { label: "客户自购", value: "CUSTOMER_REGISTER" },
          // ],
          formatter: (row) => {
            switch (row.skuSource) {
              case "ENGINEER_APPLY":
                return "工程师领料";
              case "MALL":
                return "商城购入";
              case "APPLY":
                return "领料单";
              case "CUSTOMER_REGISTER":
                return "客户自购";
              default:
                return "未知";
            }
          },
        },
        {
          dataIndex: "itemCode",
          title: "商品编码",
          width: 150,
          valueType: "input",
          isSearch: false,
          isTable: true,
        },
        {
          dataIndex: "code",
          title: "物品编码",
          width: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "name",
          width: 150,
          title: "物品名称",
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编码",
          width: 150,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          width: 130,
          isTable: true,
          formatter: (row) => row.manufacturerChannel.label,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "商品单价",
          isTable: true,
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isTable: true,
          // formatter: (row) => mulAmount(row.saleUnitPrice, row.num),
        },
        {
          dataIndex: "costPrice",
          title: "成本价",
          isTable: true,
        },
        {
          dataIndex: "totalCostAmount",
          title: "总成本",
          isTable: true,
          // formatter: (row) => mulAmount(row.saleUnitPrice, row.num),
        },
        {
          dataIndex: "location",
          title: "颜色位置",
          isTable: true,
          formatter: (row) => {
            try {
              return row.location && Array.isArray(JSON.parse(row.location))
                ? JSON.parse(row.location).join("、")
                : "";
            } catch (error) {
              return "";
            }
          },
        },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          width: 150,
          isTable: true,
        },
        {
          dataIndex: "colorCounter",
          title: "彩色计数器",
          width: 150,
          isTable: true,
        },
        // {
        //   dataIndex: "payAmount",
        //   title: "金额",
        //   isTable: true,
        // },
        {
          dataIndex: "createdAt",
          title: "更换时间",
          isTable: true,
          span: 4,
          width: 150,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
        },
        {
          dataIndex: "replacer",
          title: "更换人",
          isTable: true,
        },
      ],
      exportLoading: false,
      requestParameters: {},
    };
  },

  mounted() {
    this.$refs.ProTable.refresh();
    productAllApi().then((res) => {
      this.options = res.data;
      this.$refs.ProTable.refresh();
    });
  },
  methods: {
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startDate: null,
          endDate: null,
          data: this.queryParam.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);

      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      delete requestParameters.productIdName;
      this.requestParameters = requestParameters;
      usedListApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    handleExport() {
      this.$confirm("此操作将导出用料记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          usedListExportApi,
          this.requestParameters,
          "用料记录数据",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    handleSelect(item) {
      this.queryParam.lastIds = [];
      item.map((el) => {
        this.queryParam.lastIds.push(el[el.length - 1]);
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
