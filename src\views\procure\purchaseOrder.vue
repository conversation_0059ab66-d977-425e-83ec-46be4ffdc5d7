<!--
 * @Author: yangzhong
 * @Date: 2023-12-06 16:33:34
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:51
 * @Description: 采购申请单
-->
<template>
  <div class="purchase-order view app-container">
    <ProTable
      ref="ProTable"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-pagination
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleEdit(null, 'add')"
        >
          供应源采购
        </el-button>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleTemporary()"
        >
          临时采购
        </el-button>
        <!--        <el-button-->
        <!--          plain-->
        <!--          type="primary"-->
        <!--          class="add-btn"-->
        <!--          size="mini"-->
        <!--          icon="el-icon-share"-->
        <!--          @click="exportCustom"-->
        <!--          >导出</el-button-->
        <!--        >-->
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
          <el-button
            v-if="row.status === '0' && row.orderStatus?.value !== 'CLOSE'"
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'check')"
          >
            复核
          </el-button>
          <el-button
            v-if="row.status === '1' && row.orderStatus?.value !== 'COMPLETE'"
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="showProviderOrder(row.purchaseCode)"
          >
            供应商订单
          </el-button>
          <el-button
            v-if="
              row.status === '1' &&
              !row.canClose &&
              !(
                row.orderStatus?.value === 'COMPLETE' ||
                row.orderStatus?.value === 'CLOSE'
              )
            "
            type="btn3"
            icon="el-icon-warning-outline"
            @click="returnGoods(row)"
          >
            退货
          </el-button>
          <el-button
            v-if="row?.canClose && row.orderStatus?.value !== 'CLOSE'"
            type="danger"
            size="mini"
            icon="el-icon-circle-close"
            @click="handleCloseOrder(row.id)"
          >
            关闭
          </el-button>
        </div>
      </template>
    </ProTable>

    <!-- 新增物品采购 -->
    <addPurchase
      ref="addPurchase"
      :purchase-code="purchaseCode"
      @refresh="refresh"
    />

    <!--  查看供应商订单  -->
    <ProDialog
      ref="providerDialog"
      :value="providerDialog"
      :title="providerTitle"
      width="65%"
      top="5vh"
      :no-footer="true"
      @cancel="providerDialog = false"
    >
      <ProTable
        ref="deliveryProTable"
        :data="providerTableData"
        :columns="providerColumns"
        :show-search="false"
        :show-setting="false"
        :show-loading="false"
        :show-pagination="false"
        :height="300"
      ></ProTable>
    </ProDialog>
    <!--  退货单  -->
    <returnGoods ref="returnGoods" />
    <!-- 临时采购单 -->
    <temporaryOrder ref="temporaryOrderRef" @refresh="refresh"></temporaryOrder>
  </div>
</template>

<script>
import { closePurchaseApi, pagePurchaseApi } from "@/api/procure";
import { warehouseListApi } from "@/api/store";
import { Message } from "element-ui";
import { exportExcel } from "@/utils/exportExcel";
import { pageProviderApi } from "@/api/manufacturer";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
export default {
  name: "PurchaseOrder",
  components: {
    addPurchase: () => import("./cpns/addPurchase.vue"),
    returnGoods: () => import("./cpns/returnGoods.vue"),
    temporaryOrder: () => import("./temporaryOrder.vue"),
  },
  data() {
    return {
      tableData: [],
      queryParam: {},
      columns: [
        {
          dataIndex: "warehouseId",
          isSearch: true,
          clearable: true,
          title: "仓库名称",
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          optionskey: {
            label: "name",
            value: "id",
          },
        },
        {
          dataIndex: "purchaseCode",
          title: "采购申请单编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "purchaseCode",
          title: "申请单编号",
          isSearch: true,
          clearable: true,
          placeholder: "采购申请单编号",
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "initiatorTime",
          title: "采购时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
        },
        {
          dataIndex: "status",
          title: "确认状态",
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            {
              label: "待确认",
              value: 0,
            },
            {
              label: "已确认",
              value: 1,
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "warehouseName",
          title: "采购仓库",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "planNum",
          title: "计划采购数量",
          isTable: true,
          align: "center",
          width: 105,
        },
        {
          dataIndex: "approveNum",
          title: "确认采购数量",
          isTable: true,
          align: "center",
          width: 105,
        },
        {
          dataIndex: "price",
          title: "采购金额",
          isTable: true,
        },
        {
          dataIndex: "deliveryTime",
          title: "期望发货时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "arrivalTime",
          title: "期望到货时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "status",
          title: "确认状态",
          isTable: true,
          formatter: (row) => (row.status === "0" ? "待确认" : "已确认"),
        },
        {
          dataIndex: "initiatorName",
          title: "采购发起人",
          isTable: true,
          width: 90,
        },
        {
          dataIndex: "initiatorTime",
          title: "采购发起时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "orderStatus",
          title: "状态",
          isTable: true,
          formatter: (row) => row.orderStatus?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "待确认",
              value: "WAIT_CONFIRM",
            },
            {
              label: "待供应商确认",
              value: "SUPPY_CONFIRM",
            },
            {
              label: "发货中",
              value: "WAIT_DELIVERY",
            },
            {
              label: "待收货",
              value: "WAIT_RECEIVE",
            },
            {
              label: "已收货",
              value: "SUCCESS",
            },
            {
              label: "已完结",
              value: "COMPLETE",
            },
            {
              label: "已关闭",
              value: "CLOSE",
            },
          ],
          minWidth: 100,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 240,
          fixed: "right",
          tableSlot: "action",
        },
      ],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      selection: [],
      purchaseCode: "12", // 没发现用途，解决报错问题
      rowData: {},
      deliveryType: "info",
      deliveryTitle: "确认收货",
      providerDialog: false,
      providerTitle: "供应商订单",
      providerTableData: [],
      providerColumns: [
        {
          dataIndex: "code",
          title: "供应商订单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 200,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "deliveryTime",
          title: "期望发货时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          width: 150,
        },
        // {
        //   dataIndex: "initiatorName",
        //   title: "采购人",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        // },
        // {
        //   dataIndex: "initiatorPhone",
        //   title: "电话",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   width: 120,
        // },
        {
          dataIndex: "status",
          title: "审核状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            {
              label: "待确认",
              value: 0,
            },
            {
              label: "已确认",
              value: 1,
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "refundStatus",
          title: "退货状态",
          isTable: true,
          formatter: (row) => row.refundStatus?.label,

          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            {
              label: "无",
              value: 0,
            },
            {
              label: "已退货",
              value: 1,
            },
            {
              label: "部分退货",
              value: 2,
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "invoiceStatus",
          title: "开票状态",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formatter: (row) => (row.invoiceStatus === 1 ? "已开票" : "未开票"),
          option: [
            {
              label: "未开票",
              value: 0,
            },
            {
              label: "已开票",
              value: 1,
            },
          ],
        },
        {
          dataIndex: "amount",
          title: "金额",
          isTable: true,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          width: 150,
        },
      ],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(params) {
      this.queryParam = filterParam(Object.assign({}, this.queryParam, params));
      const searchRange = [
        {
          initiatorTimeStart: null,
          initiatorTimeEnd: null,
          data: params.initiatorTime,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      pagePurchaseApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },

    refresh() {
      this.$refs.ProTable.refresh();
    },
    handleEdit(row, type) {
      this.$refs.addPurchase.show(row, type);
    },

    handleDelete() {},
    handleSelectionChange(val) {
      this.selection = val;
    },
    // 退货
    returnGoods(row) {
      this.$refs.returnGoods.show(row);
    },
    // 查看供应商订单
    showProviderOrder(purchaseCode) {
      const data = {
        pageNumber: 1,
        pageSize: 99,
        purchaseCode,
      };
      pageProviderApi(data).then((res) => {
        this.providerTableData = res.data.rows;
        this.providerDialog = true;
      });
    },
    // 关闭订单
    handleCloseOrder(id) {
      this.$confirm("是否关闭当前采购申请单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        closePurchaseApi(id).then((res) => {
          Message.success("关闭成功");
          this.localPagination = {
            pageNumber: 1,
            pageSize: 10,
            total: 0,
          };
          this.$nextTick(() => {
            this.refresh();
          });
        });
      });
    },
    exportCustom() {
      if (this.selection.length === 0) {
        Message.warning("请选择要导出的客户数据");
        return;
      }
      const fieldMap = {};
      this.columns
        .filter((item) => item.isExport === true)
        .map((item) => {
          fieldMap[item.dataIndex] = item.title;
        });
      exportExcel(this.selection, fieldMap, "客户列表");
    },
    handleTemporary() {
      this.$refs.temporaryOrderRef.show();
    },
  },
};
</script>
