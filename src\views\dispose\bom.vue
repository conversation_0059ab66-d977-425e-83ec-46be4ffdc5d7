<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-30 14:02:30
 * @Description: BOM列表 
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :local-pagination="localPagination"
      :query-param="queryParam"
      :columns="columns"
      :data="tableData"
      show-selection
      @loadData="loadData"
      @handleSelectionChange="handleAllSelectionChange"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增零件
        </el-button>
        <el-button type="primary" size="mini" @click="allOpenEdit">
          批量编辑
        </el-button>
        <el-button
          type="primary"
          size="mini"
          @click="$refs.ProTable.$refs.ProElTable.clearSelection()"
        >
          清空选择
        </el-button>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>

      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>

          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="85%"
      :confirm-loading="confirmLoading"
      :confirm-button-disabled="confirmButtonDisabled"
      :top="'10%'"
      :no-footer="methodType == 'info'"
      :confirm-text="methodType === 'add' ? '确认新增' : '保存'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <div style="margin-bottom: 20px">
        <el-button
          v-if="methodType == 'add'"
          type="primary"
          plain
          style="width: 100px; padding: 12px 0; margin-right: 20px"
          @click="showOemDialog"
        >
          选择零件
        </el-button>
        <span v-if="choosePartSelection.ch">
          已选择： {{ choosePartSelection.ch }}（{{
            choosePartSelection.oemNumber
          }}）
        </span>
      </div>
      <el-button
        v-if="methodType == 'add' && choosePartSelection.ch"
        type="primary"
        plain
        style="width: 100px; padding: 12px 0"
        @click="showModDialog"
      >
        选择机型
      </el-button>
      <DataTable
        ref="ProTable1"
        :columns="columns1"
        :show-setting="false"
        :show-pagination="false"
        :show-search="false"
        row-key="index"
        :data="tableData1"
        sticky
        height="80vh"
        style="width: 100%; margin-top: 20px"
        :show-table-operator="false"
      >
        <template #ch="slotProps">
          <el-input
            v-model="slotProps.row.ch"
            :disabled="methodType == 'info'"
            type="text"
            size="small"
            placeholder="零件中文名称"
          ></el-input>
        </template>
        <template #en="slotProps">
          <el-input
            v-model="slotProps.row.en"
            :disabled="methodType == 'info'"
            placeholder="零件英文名称"
            size="small"
            type="text"
          ></el-input>
        </template>
        <template #unit="slotProps">
          <el-select
            v-model="slotProps.row.unit"
            placeholder="所属单元"
            size="small"
            @change="changeSelect($event, 'unit')"
          >
            <el-option
              v-for="item in unitoptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <template #num="slotProps">
          <el-input
            v-model="slotProps.row.num"
            :disabled="methodType == 'info'"
            placeholder="数量"
            type="number"
            :min="1"
            size="small"
            oninput="value=value.replace(/^0|[^0-9]/g, '')"
            @change="changeSelect($event, 'num')"
            @input="changeSelect($event, 'num')"
          ></el-input>
        </template>
        <template #standard="slotProps">
          <el-select
            v-model="slotProps.row.standard"
            placeholder="是否标件"
            size="small"
            @change="changeSelect($event, 'standard')"
          >
            <el-option label="是" :value="true"> </el-option>
            <el-option label="否" :value="false"> </el-option>
          </el-select>
        </template>
        <template #installVideo="{ row }">
          <div class="video-container">
            <div
              v-for="item in row.installVideo"
              :key="item.url"
              class="video-item"
            >
              <div class="video-title">
                <i class="el-icon-video-camera"></i>
                <span class="name" @click="handleVideoClick(item)">
                  {{ item.name }}
                </span>
              </div>
              <i
                class="el-icon-delete delete"
                @click="handleVideoDelete(item, row)"
              ></i>
            </div>
          </div>
          <!--  :limit="3 - row.installVideo" -->
          <ProUploadFile
            :type="methodType"
            :limit="3"
            :file-size="400"
            :show-file-list="false"
            :accept="'video/mp4,video/ogg,video/flv,video/avi,video/wmv,video/rmvb'"
            :file-type="[
              'video/mp4',
              'video/ogg',
              'video/flv',
              'video/avi',
              'video/wmv',
              'video/rmvb',
            ]"
            :file-list="row.installVideo"
            :multiple="false"
            @uploadSuccess="(res) => handleUploadSuccess(res, row)"
            @uploadRemove="(res) => handleUploadRemove(res, row)"
          />
        </template>

        <template #position="slotProps">
          <div v-if="slotProps.row.isPm">
            <el-tag
              v-for="(tag, ind) in slotProps.row.position"
              :key="tag"
              closable
              :disable-transitions="false"
              @close="handleClose(ind, slotProps.row)"
            >
              {{ tag }}
            </el-tag>

            <!-- <el-input
              v-if="slotProps.row.inputVisible"
              ref="saveTagInput"
              v-model="slotProps.row.inputValue"
              class="input-new-tag"
              size="small"
              @keyup.enter.native="handleInputConfirm(slotProps.row)"
              @blur="handleInputConfirm(slotProps.row)"
            >
            </el-input> -->
            <el-select
              v-model="slotProps.row.inputValue"
              placeholder="选择位置"
              filterable
              :multiple="true"
              size="small"
              @change="changeTags($event, slotProps.row)"
            >
              <el-option
                v-for="(item, i) in tagOptionList"
                :key="i"
                :label="item.label"
                :value="{ value: item.value, label: item.label }"
              ></el-option>
            </el-select>
            <!-- <el-button
              v-else
              class="button-new-tag"
              size="small"
              @click="slotProps.row.inputVisible = true"
              >+ 标签</el-button -->
          </div>
          <span v-else>/</span>
        </template>
        <template #isPm="slotProps">
          <el-select
            v-model="slotProps.row.isPm"
            placeholder="是否PM"
            size="small"
            @change="changePm(slotProps.row)"
          >
            <el-option label="是" :value="true"> </el-option>
            <el-option label="否" :value="false"> </el-option>
          </el-select>
        </template>
        <template #pmCycle="slotProps">
          <el-input
            v-if="slotProps.row.isPm"
            v-model="slotProps.row.pmCycle"
            :disabled="methodType == 'info'"
            placeholder="厂商PM周期"
            type="number"
            :min="1"
            size="small"
            oninput="value=value.replace(/^0|[^0-9]/g, '')"
            @input="changeSelect($event, 'pmCycle')"
          ></el-input>
          <span v-else>/</span>
        </template>
        <template #correctedLifespan="slotProps">
          <el-input
            v-if="slotProps.row.isPm"
            v-model="slotProps.row.correctedLifespan"
            :disabled="methodType == 'info'"
            placeholder="运营修正生命周期"
            type="number"
            :min="1"
            size="small"
            oninput="value=value.replace(/^0|[^0-9]/g, '')"
            @input="changeSelect($event, 'correctedLifespan')"
          ></el-input>
          <span v-else>/</span>
        </template>
        <!-- 编辑里面 -->
        <template #repFrequency="slotProps">
          <el-select
            v-if="slotProps.row.isPm"
            v-model="slotProps.row.repFrequency"
            placeholder="更换频次"
            @change="changeSelect($event, 'repFrequency')"
          >
            <el-option
              v-for="item in frequencyoptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <span v-else>/</span>
        </template>
        <!-- 编辑里面 -->
        <template #spareLevel="slotProps">
          <el-select
            v-if="slotProps.row.isPm"
            v-model="slotProps.row.spareLevel"
            placeholder="备件等级"
            @change="changeSelect($event, 'spareLevel')"
          >
            <el-option
              v-for="item in gradeoptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <span v-else>/</span>
        </template>
        <template #actions="slotProps">
          <span v-if="methodType == 'add'" class="fixed-width">
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDeleteGoods(slotProps.row)"
            >
              删除
            </el-button>
          </span>
        </template>
      </DataTable>
    </ProDrawer>
    <!-- 选择零件弹窗 -->
    <ProDialog
      :value="showPartDialog"
      title="选择零件"
      width="1200px"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @ok="handleChooseDialogConfirm"
      @cancel="showPartDialog = false"
    >
      <OEM
        v-if="showPartDialog"
        :show-dialog.sync="showPartDialog"
        @chooseOem="handleSelectionChange"
      ></OEM>
    </ProDialog>

    <!-- 选择机型弹窗 -->
    <ProDialog
      :value="showModelDialog"
      title="选择机型"
      width="60%"
      :confirm-loading="false"
      confirm-text="确认选择"
      top="50px"
      @ok="handleChooseDialogConfirm1"
      @cancel="showModelDialog = false"
    >
      <ModelList
        ref="ModelList"
        :choose-part-selection="choosePartSelection"
        @choose="handleSelectionChange1"
      >
      </ModelList>
    </ProDialog>
    <!-- 视频播放弹窗 -->
    <ProDialog
      :value="showVideoDialog"
      title="拆装视频播放"
      width="1250px"
      :confirm-loading="false"
      :no-footer="true"
      confirm-text="确认选择"
      top="50px"
      @cancel="closeVideoDialog"
    >
      <div style="text-align: center">
        <video
          :src="videoUrl"
          class="avatar"
          style="width: 1200px"
          controls="controls"
        ></video>
      </div>
    </ProDialog>
  </div>
</template>
<script>
import {
  bomListApi,
  bomAddApi,
  bomDelApi,
  bomEditApi,
  productAllApi,
  accessoryProductTreeApi,
  accessoryProductPathApi,
} from "@/api/dispose";
import { dictTreeByCodeApi, dictTreeByCodeApi2 } from "@/api/user";
import ProUploadFile from "@/components/ProUpload/files.vue";

import ModelList from "@/views/dispose/components/modelList.vue";
import OEM from "@/views/dispose/components/oem.vue";
import { combineArrays } from "@/utils/index";

import { cloneDeep } from "lodash";

export default {
  name: "Bom",
  components: { ModelList, OEM, ProUploadFile },
  mixins: [],
  props: {},
  data() {
    return {
      unitoptions: [],
      frequencyoptions: [],
      gradeoptions: [],
      showPartDialog: false,
      showModelDialog: false,
      choosePartSelection: {},
      chooseModelSelection: [],
      tagOptionList: [],
      options: [],
      // 列表
      deviceProductTree: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {},
      columns: [
        {
          dataIndex: "lastIds",
          isSearch: true,
          clearable: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
          title: "品牌/产品树",
        },
        {
          dataIndex: "id",
          title: "BOMID",
          minWidth: 170,
          isSearch: true,
          isTable: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "关键字",
          isSearch: true,
          valueType: "input",
          placeholder: "OEM编号/零件中英文名称",
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "ch",
          title: "零件中文名称",
          isTable: true,
          minWidth: 160,
        },
        {
          dataIndex: "en",
          title: "零件英文名称",
          isTable: true,
          minWidth: 160,
        },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
          minWidth: 100,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          optionskey: {
            label: "label",
            value: "value",
          },
          formatter: (row) => row.type.label,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "machine",
          title: "机型",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "unit",
          title: "所属单元",
          isTable: true,
          width: 80,
          formatter: (row) => row.unit.label,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(3200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "standard",
          title: "是否标件",
          isTable: true,
          formatter: (row) => (row.standard ? "是" : "否"),
          width: 80,
        },
        {
          dataIndex: "isPm",
          title: "是否PM件",
          isTable: true,
          width: 90,
          formatter: (row) => (row.isPm ? "是" : "否"),
        },
        {
          dataIndex: "pmCycle",
          title: "厂商PM周期",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "correctedLifespan",
          title: "运营修正生命周期",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "repFrequency",
          title: "更换频次",
          isTable: true,
          width: 120,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(3300),
          formatter: (row) => row.repFrequency.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "spareLevel",
          title: "备件等级",
          isTable: true,
          width: 120,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(3400),
          formatter: (row) => row.spareLevel.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "Actions",
          width: 140,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],

      //新增
      methodType: "add",
      confirmLoading: false,
      form: {},
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      confirmButtonDisabled: false,
      columns1: [],
      tableData1: [],
      dataDifference: false, //数据存在差异
      newArr: [],
      showVideoDialog: false,
      videoUrl: "",
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    productAllApi().then((res) => {
      this.options = res.data;
      this.$refs.ProTable.refresh();
    });
    dictTreeByCodeApi(11000).then((res) => {
      this.tagOptionList = res.data;
    });
    dictTreeByCodeApi(3200).then((res) => {
      this.unitoptions = res.data;
    });
    dictTreeByCodeApi(3300).then((res) => {
      this.frequencyoptions = res.data;
    });
    dictTreeByCodeApi(3400).then((res) => {
      this.gradeoptions = res.data;
    });
  },
  methods: {
    handleVideoClick(item) {
      if (!item.url) {
        this.$message.error("视频链接有误，请重新上传视频");
        return;
      }
      this.videoUrl = item.url;
      this.showVideoDialog = true;
    },
    closeVideoDialog() {
      this.showVideoDialog = false;
      this.videoUrl = null;
    },
    changeTags(e, v) {
      e.map((item) => {
        v.position.push(item.label);
        v.position = [...new Set(v.position)];
      });
    },
    changePm(row) {
      this.$set(row, "pmCycle", null);
      this.$set(row, "correctedLifespan", null);
      this.$set(row, "repFrequency", null);
      this.$set(row, "spareLevel", null);

      // row.pmCycle = null;
      // row.correctedLifespan = null;
      // row.repFrequency = null;
      // row.spareLevel = null;
      this.tableData1.map((item) => {
        item.isPm = row.isPm;
      });
    },
    showOemDialog() {
      this.showPartDialog = true;
    },
    showModDialog() {
      this.showModelDialog = true;

      this.$nextTick(() => {
        this.$refs.ModelList.$refs.ProTable.$refs.ProElTable.clearSelection();
        if (this.chooseModelSelection.length > 0) {
          this.chooseModelSelection.map((row) => {
            this.$refs.ModelList.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
              row
            );
          });
        }
      });
    },
    handleChange1(item) {
      this.$set(this.queryParam, "type", item[item.length - 1]);
    },
    getBrand(id) {
      accessoryProductTreeApi(id).then((res) => {
        this.deviceProductTree = res.data;
      });
    },
    handleSelect(item) {
      this.queryParam.lastIds = [];
      item.map((el) => {
        this.queryParam.lastIds.push(el[el.length - 1]);
      });
    },
    handleChange(item) {
      this.$set(this.form, "productIds", []);
      item.map((el) => {
        this.form.productIds.push(el[el.length - 1]);
      });
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = cloneDeep(
        Object.assign(this.queryParam, parameter)
      );
      delete requestParameters.productIdName;
      bomListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      // this.confirmLoading = true;
      // this.confirmButtonDisabled = true;
      this.methodType === "add" ? this.create() : this.update();

      // this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.columns1 = [
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "ch",
          title: "零件中文名称",
          isTable: true,
          width: 150,
          tableSlot: "ch",
        },
        {
          dataIndex: "en",
          title: "零件英文名称",
          isTable: true,
          width: 150,
          tableSlot: "en",
        },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "product",
          title: "机型",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "unit",
          title: "所属单元",
          isTable: true,
          width: 150,
          tableSlot: "unit",
        },
        {
          dataIndex: "installVideo",
          title: "拆装视频",
          isTable: true,
          width: 250,
          tableSlot: "installVideo",
        },

        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          width: 120,
          tableSlot: "num",
        },
        {
          dataIndex: "standard",
          title: "是否标件",
          isTable: true,
          width: 100,
          tableSlot: "standard",
        },
        {
          dataIndex: "isPm",
          title: "是否PM",
          isTable: true,
          width: 100,
          tableSlot: "isPm",
        },
        {
          dataIndex: "position",
          title: "更换位置",
          isTable: true,
          width: 100,
          tableSlot: "position",
        },
        {
          dataIndex: "pmCycle",
          title: "厂商PM周期",
          isTable: true,
          width: 100,
          tableSlot: "pmCycle",
        },
        {
          dataIndex: "correctedLifespan",
          title: "运营修正生命周期",
          isTable: true,
          width: 100,
          tableSlot: "correctedLifespan",
        },
        {
          dataIndex: "repFrequency", //搜索
          title: "更换频次",
          isTable: true,
          width: 100,
          tableSlot: "repFrequency",
        },
        {
          dataIndex: "spareLevel",
          title: "备件等级",
          isTable: true,
          width: 100,
          tableSlot: "spareLevel",
        },
        {
          dataIndex: "Actions",
          width: 120,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tableSlot: "actions",
        },
      ];
      this.tableData1 = [];
      this.choosePartSelection = {};
      this.chooseModelSelection = [];
      this.dialogTitle = "新增零件BOM";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;

      // this.$nextTick((e) => {
      //   this.$refs["proform"].resetFormParam();
      // });
    },
    //响应新增
    create() {
      if (this.tableData1.length === 0) {
        this.$message.warning("请选择零件、机型！");
        return;
      }
      let cango = true;
      this.tableData1.map((ele) => {
        if (!ele.isPm) {
          if (!ele.ch || !ele.en || !ele.unit || !ele.num) {
            cango = false;
          } else {
            cango = true;
          }
        } else {
          if (
            !ele.ch ||
            !ele.en ||
            !ele.unit ||
            !ele.num ||
            !ele.pmCycle ||
            !ele.correctedLifespan ||
            !ele.repFrequency ||
            !ele.spareLevel
          ) {
            cango = false;
          } else {
            cango = true;
          }
        }
      });
      if (!cango) {
        this.$message.warning("请完善表格！");
        return;
      }
      this.$confirm("是否确认新增?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.confirmButtonDisabled = true;
        this.confirmLoading = true;
        bomAddApi(this.tableData1)
          .then(() => {
            this.$message.success("新增成功");
            this.dialogVisible = false;
            this.$refs.ProTable.refresh();
          })
          .finally(() => {
            this.confirmLoading = false;
            this.confirmButtonDisabled = false;
          });
      });
    },
    //触发编辑
    handleUpdate(row) {
      const obj = cloneDeep(row);
      obj.type = obj.type.label;
      obj.unit = obj.unit.value;
      obj.product = obj.machine;
      obj.repFrequency = obj.repFrequency.value;
      obj.spareLevel = obj.spareLevel.value;
      obj.inputVisible = false;
      obj.inputValue = "";
      obj.position = obj.position || [];
      // obj.installVideo = [obj.installVideo] || [];
      this.tableData1 = [];
      this.columns1 = [
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "ch",
          title: "零件中文名称",
          isTable: true,
          width: 150,
          tableSlot: "ch",
        },
        {
          dataIndex: "en",
          title: "零件英文名称",
          isTable: true,
          width: 150,
          tableSlot: "en",
        },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "product",
          title: "机型",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "unit",
          title: "所属单元",
          isTable: true,
          width: 150,
          tableSlot: "unit",
        },
        {
          dataIndex: "installVideo",
          title: "拆装视频",
          isTable: true,
          width: 250,
          tableSlot: "installVideo",
        },

        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          width: 120,
          tableSlot: "num",
        },
        {
          dataIndex: "standard",
          title: "是否标件",
          isTable: true,
          width: 100,
          tableSlot: "standard",
        },
        {
          dataIndex: "isPm",
          title: "是否PM",
          isTable: true,
          width: 100,
          tableSlot: "isPm",
        },
        {
          dataIndex: "position",
          title: "更换位置",
          isTable: true,
          width: 150,
          tableSlot: "position",
        },
        {
          dataIndex: "pmCycle",
          title: "厂商PM周期",
          isTable: true,
          width: 150,
          tableSlot: "pmCycle",
        },
        {
          dataIndex: "correctedLifespan",
          title: "运营修正生命周期",
          isTable: true,
          width: 150,
          tableSlot: "correctedLifespan",
        },
        {
          dataIndex: "repFrequency",
          title: "更换频次",
          isTable: true,
          width: 150,
          tableSlot: "repFrequency",
        },
        {
          dataIndex: "spareLevel",
          title: "备件等级",
          isTable: true,
          width: 150,
          tableSlot: "spareLevel",
        },
      ];
      this.choosePartSelection = {};
      this.chooseModelSelection = [];
      this.dialogTitle = "编辑";
      this.tableData1.push(obj);
      this.methodType = "edit";
      this.dialogVisible = true;
    },
    //响应编辑
    update() {
      if (this.tableData1.length === 0) {
        this.$message.warning("请选择零件、机型！");
        return;
      }
      let cango = true;
      this.tableData1.map((ele) => {
        if (!ele.isPm) {
          if (!ele.ch || !ele.en || !ele.unit || !ele.num) {
            cango = false;
          } else {
            cango = true;
          }
        } else {
          if (
            !ele.ch ||
            !ele.en ||
            !ele.unit ||
            !ele.num ||
            !ele.pmCycle ||
            !ele.correctedLifespan ||
            !ele.repFrequency ||
            !ele.spareLevel
          ) {
            cango = false;
          } else {
            cango = true;
          }
        }
      });
      if (!cango) {
        this.$message.warning("请完善表格！");
        return;
      }
      this.$confirm("是否确认保存修改内容?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.confirmButtonDisabled = true;

        this.confirmLoading = true;
        bomEditApi(this.tableData1)
          .then(() => {
            this.$message.success("修改成功");
          })
          .finally(() => {
            this.confirmLoading = false;
            this.dialogVisible = false;
            this.confirmButtonDisabled = false;
            this.$refs.ProTable.refresh();
            this.$refs.ProTable.$refs.ProElTable.clearSelection();
          });
      });
    },

    // 触发详情
    handleInfo(row) {
      this.tableData1 = [];
      this.dialogTitle = "查看";
      this.resetFrom();
      this.form = cloneDeep(row);
      this.form.type = this.form.type.value;
      this.$set(this.form, "productIdName", []);
      const productArr = [];
      const productidArr = [];
      accessoryProductPathApi(row.id).then((res) => {
        res.data.map((el) => {
          const arr = el.split("/");
          arr.shift();
          productidArr.push(arr[arr.length - 1]);
          productArr.push(arr);
        });
      });

      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
        setTimeout(() => {
          this.$set(this.form, "productIdName", productArr);
          this.$set(this.form, "productIds", productidArr);
        }, 300);
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        bomDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },

    handleChooseDialogConfirm() {
      this.partData.push(...this.choosePartSelection);
      const idMap = {};
      this.partData = this.partData.reduce((preVal, curVal) => {
        idMap[curVal.id] ? "" : (idMap[curVal.id] = preVal.push(curVal));
        return preVal;
      }, []);
      this.choosePartData = [];
      this.showPartDialog = false;
      // this.partLocalPagination.total = this.partData.length;
    },

    handleSelectionChange(val) {
      this.choosePartSelection = val;
      this.showPartDialog = false;
      if (this.chooseModelSelection.length > 0) {
        this.handleChooseDialogConfirm1();
      }
    },
    handleAllSelectionChange(row) {
      this.newArr = row;
    },
    allOpenEdit() {
      if (!this.queryParam.name) {
        this.$message.warning("请先查询OEM编号");
        return;
      }
      if (this.newArr.length == 0) {
        this.$message.warning("请选择要批量编辑的数据");
        return;
      }
      this.dataDifference = false;
      this.tableData1 = [];
      const oem = this.newArr[0].oemNumber;
      this.newArr.map((item) => {
        this.tableData1.push({
          brand: item.brand,
          ch: item.ch,
          correctedLifespan: item.correctedLifespan,
          createdAt: item.createdAt,
          deleted: item.deleted,
          en: item.en,
          id: item.id,
          isPm: item.isPm,
          num: item.num,
          oemNumber: item.oemNumber,
          partId: item.partId,
          pmCycle: item.pmCycle,
          product: item.machine,
          productId: item.productId,
          repFrequency: item.repFrequency.value,
          spareLevel: item.spareLevel.value,
          standard: item.standard,
          type: item.type.label,
          unit: item.unit.value,
          installVideo: item.installVideo,
          inputVisible: false,
          inputValue: "",
          position: [],
        });
      });
      this.newArr.forEach((item) => {
        if (oem != item.oemNumber) {
          this.dataDifference = true;
        }
      });
      this.columns1 = [
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "ch",
          title: "零件中文名称",
          isTable: true,
          width: 150,
          tableSlot: "ch",
        },
        {
          dataIndex: "en",
          title: "零件英文名称",
          isTable: true,
          width: 150,
          tableSlot: "en",
        },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "product",
          title: "机型",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "unit",
          title: "所属单元",
          isTable: true,
          width: 150,
          tableSlot: "unit",
        },
        {
          dataIndex: "installVideo",
          title: "拆装视频",
          isTable: true,
          width: 250,
          tableSlot: "installVideo",
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          width: 120,
          tableSlot: "num",
        },
        {
          dataIndex: "standard",
          title: "是否标件",
          isTable: true,
          width: 120,
          tableSlot: "standard",
        },
        {
          dataIndex: "isPm",
          title: "是否PM",
          isTable: true,
          width: 120,
          tableSlot: "isPm",
        },
        {
          dataIndex: "pmCycle",
          title: "厂商PM周期",
          isTable: true,
          width: 120,
          tableSlot: "pmCycle",
        },
        {
          dataIndex: "correctedLifespan",
          title: "运营修正生命周期",
          isTable: true,
          width: 120,
          tableSlot: "correctedLifespan",
        },
        {
          dataIndex: "repFrequency",
          title: "更换频次",
          isTable: true,
          width: 120,
          tableSlot: "repFrequency",
        },
        {
          dataIndex: "spareLevel",
          title: "备件等级",
          isTable: true,
          width: 120,
          tableSlot: "spareLevel",
        },
      ];
      this.choosePartSelection = {};
      this.chooseModelSelection = [];
      this.dialogTitle = "编辑";
      this.methodType = "edit";

      if (this.dataDifference) {
        this.$message.warning("所选数据OEM编号存在差异");
        return;
      }

      this.dialogVisible = true;
    },
    handleChooseDialogConfirm1() {
      this.tableData1 = [];

      const arr = combineArrays(
        [this.choosePartSelection],
        this.chooseModelSelection
      );

      arr.map((ele) => {
        this.tableData1.push({
          oemNumber: ele.a1.oemNumber,
          ch: ele.a1.ch, //零件中文名称
          en: ele.a1.en, //零件英文名称
          type: ele.a1.type.label,
          partId: ele.a1.id, //	零件id
          productId: ele.a2.id, //	机型id
          brand: ele.a2.brand,
          product: ele.a2.name,
          num: null, //	拥有数量
          isPm: false, //是否PM
          pmCycle: null, //	厂商PM周期
          repFrequency: null, //	更换频次
          spareLevel: null, //	备件等级
          standard: false, //	是否标件
          unit: null, //	所属单元(字典项码)
          correctedLifespan: null, //运营修正生命周期
          installVideo: [],
          inputVisible: false,
          inputValue: "",
          position: [],
        });
      });
      this.showModelDialog = false;
    },

    handleSelectionChange1(val) {
      this.chooseModelSelection = cloneDeep(val);
    },
    handleDeleteGoods(row) {
      this.tableData1.splice(
        this.tableData1.findIndex((item) => item.id === row.id),
        1
      );
      this.chooseModelSelection.splice(
        this.chooseModelSelection.findIndex(
          (item) => item.id === row.productId
        ),
        1
      );
    },

    changeSelect(e, val) {
      this.tableData1.map((item) => {
        item[val] = e;
      });
    },
    // 图片处理
    handleUploadSuccess(result, item) {
      // item.installVideo = cloneDeep(result);
      // item.push(result);
      if (!item.installVideo || item.installVideo.length === 0) {
        this.$set(item, "installVideo", []);
      }
      item.installVideo.push(result);
      // this.tableData1.map((item) => {
      //
      // });
    },
    handleVideoDelete(item, row) {
      const index = row.installVideo.findIndex((val) => val.key === item.key);
      if (index === -1) return;
      row.installVideo.splice(index, 1);
    },
    handleUploadRemove(file, item) {
      const index = item.installVideo.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      item.splice(index, 1);
    },
    handleInputConfirm(data) {
      const inputValue = data.inputValue;
      if (inputValue) {
        data.position.push(inputValue);
        data.inputVisible = false;
        data.inputValue = "";
      }
    },
    handleClose(index, v) {
      v.position.splice(index, 1);
      // this.tagList[index].children.splice(ind, 1);
    },
  },
};
</script>
<style lang="scss" scoped>
.video-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 5px;
  .video-item {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    cursor: pointer;
    margin-bottom: 5px;
    .video-title {
      flex: 1;

      display: flex;
      align-items: center;
      &:hover {
        color: #409eff;
      }
      span {
        margin-left: 5px;
        display: inline-block;

        max-width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .delete {
      color: #f56c6c;
    }
  }
}
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
