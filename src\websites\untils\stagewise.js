/**
 * Stagewise Toolbar 集成工具
 * 用于在Vue 2项目中集成Stagewise开发工具
 */

import { initToolbar } from '@stagewise/toolbar'

/**
 * 初始化Stagewise工具栏
 * @param {Object} options - 配置选项
 * @param {boolean} options.enabled - 是否启用工具栏（默认仅在开发环境启用）
 * @param {string} options.position - 工具栏位置 ('top-left', 'top-right', 'bottom-left', 'bottom-right')
 * @param {Object} options.theme - 主题配置
 */
export function initStagewise(options = {}) {
  // 默认配置
  const defaultOptions = {
    enabled: process.env.NODE_ENV === 'development',
    position: 'bottom-right',
    theme: {
      primary: '#1890ff',
      background: '#ffffff',
      text: '#333333'
    }
  }

  const config = { ...defaultOptions, ...options }

  // 仅在启用时初始化
  if (!config.enabled) {
    return
  }

  try {
    // 初始化工具栏
    const toolbar = initToolbar({
      position: config.position,
      theme: config.theme,
      // 项目信息
      project: {
        name: 'benyin-web',
        framework: 'Vue 2',
        version: '2.7.16'
      },
      // 开发环境配置
      development: {
        hotReload: true,
        sourceMap: true
      }
    })

    console.log('Stagewise toolbar initialized successfully')
    return toolbar
  } catch (error) {
    console.error('Failed to initialize Stagewise toolbar:', error)
  }
}

/**
 * 在Vue组件中使用Stagewise
 * 可以在组件的mounted钩子中调用
 */
export function useStagewise() {
  return {
    // 标记当前组件
    markComponent(componentName) {
      if (window.stagewise) {
        window.stagewise.markComponent(componentName)
      }
    },
    
    // 添加调试信息
    addDebugInfo(info) {
      if (window.stagewise) {
        window.stagewise.addDebugInfo(info)
      }
    },
    
    // 记录性能指标
    recordPerformance(metric, value) {
      if (window.stagewise) {
        window.stagewise.recordPerformance(metric, value)
      }
    }
  }
}

/**
 * Vue插件形式的Stagewise集成
 */
export default {
  install(Vue, options = {}) {
    // 初始化工具栏
    const toolbar = initStagewise(options)
    
    // 将工具栏实例添加到Vue原型
    Vue.prototype.$stagewise = toolbar
    
    // 添加全局混入，为所有组件提供Stagewise功能
    Vue.mixin({
      mounted() {
        // 自动标记组件
        if (this.$options.name && window.stagewise) {
          window.stagewise.markComponent(this.$options.name)
        }
      }
    })
    
    // 添加全局方法
    Vue.stagewise = toolbar
  }
}
