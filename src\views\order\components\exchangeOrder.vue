<!--
 * @Author: wskg
 * @Date: 2025-03-08 13:56:14
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:48
 * @Description: 新增换机单
 -->
<template>
  <div class="app-container">
    <ProDrawer
      :value="drawerVisible"
      title="新增换机工单"
      size="70%"
      :confirm-button-disabled="confirmButLoading"
      :confirm-text="'确定创建'"
      @ok="handleDrawerOk"
      @cancel="handleDrawerCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="editForm"
        :form-list="formColumns"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :open-type="editType"
        :confirm-loading="confirmButLoading"
        @proSubmit="proSubmit"
      >
        <template #customerName>
          <div class="title-box" style="margin-top: 0">基础信息</div>
          <div class="tit-box">
            <el-button type="success" size="mini" @click="showDialogFn">
              选择换机客户
            </el-button>
          </div>
          <el-form
            ref="proFormChild"
            :model="editForm"
            :rules="editFormRules"
            :disabled="editType === 'info'"
            label-width="120px"
            class="demo-ruleForm"
          >
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="客户编码 : " prop="seqId">
                  <el-input
                    v-model="editForm.seqId"
                    placeholder="请选择客户信息"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="店铺名称 : " prop="customerName">
                  <el-input
                    v-model="editForm.customerName"
                    placeholder="请选择客户信息"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="设备组名称 : " prop="deviceGroupId">
                  <el-select
                    v-model="editForm.deviceGroupId"
                    style="width: 100%"
                    placeholder="请选择设备组"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="item in deviceGroupOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="店铺地址 : " prop="addressId">
                  <el-select v-model="editForm.addressId" style="width: 100%">
                    <el-option
                      v-for="item in addressOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="联系人 : " prop="contact">
                  {{ editForm.contact }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="联系电话 : " prop="phone">
                  {{ editForm.phone }}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
        <template #info>
          <div class="title-box" style="margin-top: 0">换机信息</div>
        </template>
        <!-- 安装工程师 -->
        <template #engineerId>
          <el-select
            v-model="editForm.engineerId"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in engineerOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
        <!-- 是否包邮 -->
        <template #freeShipping>
          <el-checkbox v-model="editForm.freeShipping"></el-checkbox>
        </template>
        <!-- 更换机器列表 -->
        <template #machineList>
          <div class="title-box" style="margin-top: 0">更换机器列表</div>
          <ProTable
            ref="ProTable"
            :show-search="false"
            :show-table-operator="false"
            :show-loading="false"
            row-key="id"
            :data="changeTableData"
            :columns="columns"
            :query-param="queryParam"
            :local-pagination="localPagination"
            :height="300"
          >
            <template #amount="{ row }">
              <el-input
                v-model="row.amount"
                placeholder="请输入价格"
                style="width: 100%"
                size="small"
              ></el-input>
            </template>
            <template #actions="{ row }">
              <div class="fixed-width">
                <el-button
                  icon="el-icon-refresh"
                  @click="changeDeviceAccessories(row)"
                >
                  更换
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <ProDialog
      :value="showDialog"
      title="客户信息"
      width="80%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <ProTable
        ref="ProTables"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="creatColumns"
        row-key="id"
        :local-pagination="localPagination"
        :data="tableData"
        sticky
        :query-param="queryParams"
        :height="430"
        :show-setting="false"
        @loadData="loadData"
      >
        <template #actions="{ row }">
          <span class="fixed-width">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-circle-check"
              @click="sureSelectCustom(row)"
            >
              确定
            </el-button>
          </span>
        </template>
      </ProTable>
    </ProDialog>
    <!-- v-model="dialogVisible"-->
    <choose-device
      :dialog-visible.sync="dialogVisible"
      :host-type="actDeviceAccessories.hostType?.value"
      @confirmDispatch="setDevice"
    ></choose-device>
  </div>
</template>

<script>
import {
  changeDeviceApi,
  getCustomerByPageApi,
  getCustomerDeviceListApi,
  getExchangeDeviceGroupListApi,
} from "@/api/customer";
import { meterUpdateApi } from "@/api/statisics";
import ChooseDevice from "./chooseDevices.vue";
import { cloneDeep } from "lodash";
import { getAddressListApi } from "@/api/operator";
import { roleMemberApi } from "@/api/user";

export default {
  name: "CreateExchangeOrder",
  components: { ChooseDevice },
  data() {
    return {
      drawerVisible: false,
      editForm: {},
      formColumns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isForm: true,
          formOtherSlot: "customerName",
          formSpan: 24,
        },
        {
          dataIndex: "info",
          title: "换机信息",
          isForm: true,
          formOtherSlot: "info",
          formSpan: 24,
        },
        {
          dataIndex: "expectInstallTime",
          title: "期望换机时间",
          isForm: true,
          valueType: "date-picker",
          valueFormat: "yyyy-MM-dd",
          prop: [
            {
              required: true,
              message: "请选择期望换机时间",
              trigger: "change",
            },
          ],
          formSpan: 6,
        },
        {
          dataIndex: "engineerId",
          title: "安装工程师",
          isForm: true,
          formType: "select",
          formSlot: "engineerId",
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请选择安装工程师",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "freeShipping",
          title: "是否包邮",
          isForm: true,
          formSlot: "freeShipping",
          formSpan: 6,
        },
        {
          dataIndex: "freight",
          title: "运费",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 6,
        },
        {
          dataIndex: "installAmount",
          title: "安装费",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 6,
        },
        {
          dataIndex: "precautions",
          title: "注意事项",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          formSpan: 18,
        },
        {
          dataIndex: "machineList",
          title: "机器列表",
          isForm: true,
          formOtherSlot: "machineList",
          formSpan: 24,
        },
      ],
      editFormRules: {
        seqId: [
          {
            required: true,
            message: "请选择客户名称",
            trigger: "change",
          },
        ],
        customerName: [
          {
            required: true,
            message: "请选择客户名称",
            trigger: "change",
          },
        ],
        deviceGroupId: [
          {
            required: true,
            message: "请选择设备组名称",
            trigger: "change",
          },
        ],
        addressId: [
          {
            required: true,
            message: "请选择客户地址",
            trigger: "change",
          },
        ],
      },
      tradeForm: [
        {
          label: "期望换机时间",
          key: "expectInstallTime",
          formType: "date",
        },
        {
          label: "更换机型编号",
          key: "machineNum",
          formType: "devicePicker",
        },
        {
          label: "品牌机型",
          key: "productName",
        },
        {
          label: "服务类型",
          formatter: (row) => row.serType?.label,
          key: "serType",
        },
        {
          label: "安装工程师",
          key: "engineerId",
          formType: "select",
          options: () => {
            return [
              { label: "选项1", value: 1 },
              { label: "选项2", value: 2 },
            ];
          },
        },
        {
          label: "供电电压",
          key: "electric",
        },
        {
          label: "计数方式",
          key: "paperType",
        },
        {
          label: "黑白计数器",
          key: "blackWhiteCounter",
        },
        {
          label: "彩色计数器",
          key: "colorCounter",
        },
        {
          label: "五色计数器",
          key: "fiveColourCounter2",
        },
        {
          label: "运费",
          key: "freight",
          formType: "number",
        },
        {
          label: "安装费",
          key: "installAmount",
          formType: "number",
        },
        {
          label: "是否包邮",
          key: "freeShipping",
          formType: "checkbox",
        },
        {
          label: "注意事项",
          key: "precautions",
          formType: "textarea",
          span: 12,
        },
      ],
      queryParam: {},

      columns: [
        {
          dataIndex: "accessoryCode",
          title: "新换机器编号",
          isTable: true,
        },
        {
          dataIndex: "originCode",
          title: "现场机器编号",
          isTable: true,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
        },
        {
          dataIndex: "modeType",
          title: "机器/配件型号",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "价格",
          isTable: true,
          tableSlot: "amount",
          width: 120,
        },
        {
          dataIndex: "actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 100,
        },
      ],
      changeTableData: [],
      editType: "add",
      // 客户信息
      showDialog: false,
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      queryParams: {},
      creatColumns: [
        // {
        //   dataIndex: "shopRecruitment",
        //   title: "店铺名称",
        //   isTable: true,
        // },
        {
          dataIndex: "name",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "seqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },

        {
          dataIndex: "legalPersonTel",
          title: "法人电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      confirmButLoading: false,
      deviceGroupOptions: [], // 设备组
      deviceGroupList: [],
      actDeviceAccessories: {
        hostType: {},
      },
      dialogVisible: false,
      addressOptions: [],
      addressInfoList: [],
      engineerOptions: [],
    };
  },
  watch: {
    "editForm.deviceGroupId": {
      handler(val) {
        if (val) {
          this.getExchangeList(val);
        }
      },
    },
    "editForm.customerId": {
      handler(val) {
        this.getCustomerProductList(val);
        this.getCustomerAddressList(val);
      },
    },
    "editForm.addressId": {
      handler(val) {
        const addressInfo = this.addressInfoList.find(
          (item) => item.id === val
        );
        if (addressInfo) {
          this.editForm.contact = addressInfo.contact;
          this.editForm.phone = addressInfo.phone;
        }
      },
    },
  },
  methods: {
    show() {
      this.editForm = {};
      this.deviceGroupOptions = [];
      this.deviceGroupList = [];
      this.changeTableData = [];
      this.$nextTick(() => {
        this.drawerVisible = true;
        this.getWorkers();
      });
    },
    changeDeviceAccessories(row) {
      this.actDeviceAccessories = row;
      this.dialogVisible = true;
    },
    handleDrawerOk() {
      this.$refs.proFormChild.validate((valid) => {
        if (valid) {
          this.$refs.ProForm.handleSubmit();
        }
      });
    },
    async proSubmit(val) {
      try {
        let exchangeInfos = this.changeTableData.filter((i) => i.originCode);
        if (exchangeInfos.length > 0) {
          exchangeInfos = exchangeInfos
            .filter((i) => i.accessoryCode)
            .map((i) => {
              return {
                machineCode: i.originCode,
                newMachineCode: i.accessoryCode,
                hostType: i.hostType?.value,
                deviceGroupId: i.deviceGroupId,
                productName: i.modeType,
                id: i.id,
                amount: i.amount,
              };
            });
        } else {
          this.$message.error("更换列表没有新换机器编号");
          return;
        }
        this.confirmButLoading = true;
        const args = {
          ...val,
          type: "EXCHANGE",
          exchangeInfos: exchangeInfos,
        };
        const result = await changeDeviceApi(args);
        if (result.code === 200) {
          this.$message.success("操作成功");
          this.$emit("refresh");
          this.drawerVisible = false;
        }
      } finally {
        this.confirmButLoading = false;
      }
    },
    extractIds(data) {
      const ids = [];
      function recurse(items) {
        items.forEach((item) => {
          ids.push(item.code);
          if (item.children && Array.isArray(item.children)) {
            recurse(item.children);
          }
        });
      }
      recurse(data);
      return ids;
    },
    handleDrawerCancel() {
      this.drawerVisible = false;
    },
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParams, parameter);
      getCustomerByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTables
            ? (this.$refs.ProTables.listLoading = false)
            : null;
        });
    },
    showDialogFn() {
      this.showDialog = true;
      this.$nextTick((e) => {
        this.queryParams = {};
        this.$refs.ProTables.refresh();
      });
    },
    // 获取客户设备组列表
    getCustomerProductList(customerId) {
      if (!customerId) {
        return;
      }
      getCustomerDeviceListApi(customerId).then((res) => {
        this.deviceGroupList = res.data;
        this.deviceGroupOptions = res.data
          .filter((item) => {
            return item.machineNum;
          })
          .map((item) => {
            return {
              label: item.deviceGroup.label + " / " + item.productInfo,
              value: item.id,
            };
          });
      });
    },
    // 获取换机列表
    async getExchangeList(deviceId) {
      if (!deviceId) {
        return;
      }
      try {
        const res = await getExchangeDeviceGroupListApi(deviceId);
        if (res.code === 200) {
          this.changeTableData = res.data.map((i) => {
            return {
              ...i,
              originCode: i.accessoryCode,
              accessoryCode: "",
            };
          });
          console.log(this.changeTableData, "this.changeTableData");
        }
      } catch (e) {
        this.changeTableData = [];
      }
    },
    getWorkers() {
      roleMemberApi("1002", { pageNumber: 1, pageSize: 10000 }).then((res) => {
        this.engineerOptions = res.data.rows.map((item) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      });
    },
    // 获取客户地址列表
    getCustomerAddressList(customerId) {
      if (!customerId) {
        return;
      }
      getAddressListApi(customerId).then((res) => {
        this.addressInfoList = res.data;
        this.addressOptions = res.data.map((item) => {
          return {
            label: item.address,
            value: item.id,
          };
        });
      });
    },
    // 确认用户
    async sureSelectCustom(row) {
      this.editForm.seqId = row.seqId;
      this.editForm.customerName = row.name;
      this.$set(this.editForm, "customerId", row.id);
      this.showDialog = false;
    },
    setDevice(data) {
      if (data.length < 1) {
        return;
      }
      const obj = data[0];
      const index = this.changeTableData.findIndex(
        (o) => o.id == this.actDeviceAccessories.id
      );
      // if (!this.actDeviceAccessories.originCode) {
      //   this.actDeviceAccessories.originCode = cloneDeep(
      //     this.actDeviceAccessories.accessoryCode
      //   );
      // }
      this.actDeviceAccessories.accessoryCode = obj.machineNum;
      this.actDeviceAccessories.modeType = obj.productName;
      this.changeTableData[index] = this.actDeviceAccessories;
    },
  },
};
</script>

<style scoped lang="scss">
.tit-box {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px 7px;
  font-size: 16px;
  font-weight: 800;
}
</style>
