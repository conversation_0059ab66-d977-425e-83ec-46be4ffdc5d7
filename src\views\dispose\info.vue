<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-15 14:10:51
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 11:30:26
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="收款配置" name="fourth">
        <Payee v-model="accountForm" @save="submitConfig" />
      </el-tab-pane>
      <el-tab-pane label="平台介绍" name="first">
        <About :id="id" :introduce="platformIntroduce" @save="submitConfig" />
      </el-tab-pane>
      <el-tab-pane label="注册协议" name="second">
        <RegistrationProtocol
          :id="id"
          :register="registerProtocol"
          @save="submitConfig"
        />
      </el-tab-pane>
      <el-tab-pane label="隐私协议" name="third">
        <PrivacyProtocol
          :id="id"
          :privacy="privacyProtocol"
          @save="submitConfig"
        />
      </el-tab-pane>
      <el-tab-pane label="收费标准" name="sixth">
        <Fee :id="id" :introduce="feeIntroduce" @save="submitConfig" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Payee from "./components/base/payee.vue";
import About from "./components/base/about.vue";
import RegistrationProtocol from "./components/base/registrationProtocol.vue";
import PrivacyProtocol from "./components/base/privacyProtocol.vue";
import Fee from "./components/base/fee.vue";
import { getBaseCompanyInfoApi, setBaseCompanyInfoApi } from "@/api";
export default {
  name: "BaseInfo",
  components: {
    Payee,
    About,
    RegistrationProtocol,
    PrivacyProtocol,
    Fee,
  },
  data() {
    return {
      activeName: "fourth",
      id: "",
      accountForm: {},
      platformIntroduce: "", // 平台介绍
      privacyProtocol: "", // 隐私协议
      registerProtocol: "", // 注册协议
      feeIntroduce: "", // 收费标准
    };
  },
  mounted() {
    this.getBaseInfo();
  },
  methods: {
    getBaseInfo() {
      getBaseCompanyInfoApi().then((res) => {
        const result = res.data;
        this.id = result.id;
        this.accountForm = {
          companyName: result.companyName, // 公司名称
          programName: result.programName, // 小程序名称
          accountName: result.accountName, // 账户名称
          bankName: result.bankName, // 账户名称
          accountNumber: result.accountNumber, // 开户银行
          phoneNumber: result.phoneNumber, // 客服电话
          aliQrcode: result.aliQrcode,
          wechatQrcode: result.wechatQrcode,
          paymentQrcode: result.paymentQrcode,
          logoImage: result.logoImage,
        };
        this.platformIntroduce = result.platformIntroduce;
        this.privacyProtocol = result.privacyProtocol;
        this.registerProtocol = result.registerProtocol;
        this.feeIntroduce = result.feeIntroduce;
      });
    },
    submitConfig(result) {
      if (this.id) {
        const args = {
          ...result,
          id: this.id,
        };
        setBaseCompanyInfoApi(args).then((res) => {
          this.$message.success("配置保存成功");
        });
      } else {
        const args = {
          ...result,
        };
        setBaseCompanyInfoApi(args).then((res) => {
          this.getBaseInfo();
          this.$message.success("配置保存成功");
        });
      }
    },
  },
};
</script>

<style scoped lang="scss"></style>
