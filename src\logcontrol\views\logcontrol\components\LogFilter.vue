<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 日志过滤组件
-->
<template>
  <div class="log-filter">
    <el-form :model="filterForm" label-width="80px" @submit.native.prevent>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="日志级别">
            <el-select
              v-model="filterForm.level"
              placeholder="选择级别"
              clearable
              style="width: 100%"
              @change="handleFilterChange"
            >
              <el-option label="DEBUG" value="DEBUG" />
              <el-option label="INFO" value="INFO" />
              <el-option label="WARN" value="WARN" />
              <el-option label="ERROR" value="ERROR" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="日志类型">
            <el-select
              v-model="filterForm.logType"
              placeholder="选择类型"
              clearable
              style="width: 100%"
              @change="handleFilterChange"
            >
              <el-option label="位置日志" value="LOCATION" />
              <el-option label="业务日志" value="BUSINESS" />
              <el-option label="崩溃日志" value="CRASH" />
              <el-option label="性能日志" value="PERFORMANCE" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="设备">
            <el-select
              v-model="filterForm.deviceId"
              placeholder="选择设备"
              clearable
              filterable
              style="width: 100%"
              :loading="loading"
              @change="handleFilterChange"
            >
              <el-option
                v-for="device in devices"
                :key="device.id"
                :label="device.name || device.deviceId"
                :value="device.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="用户">
            <el-select
              v-model="filterForm.userId"
              placeholder="选择用户"
              clearable
              filterable
              style="width: 100%"
              :loading="loading"
              @change="handleFilterChange"
            >
              <el-option
                v-for="user in users"
                :key="user.userId"
                :label="`${user.userCode} - ${user.userName}`"
                :value="user.userId"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="关键词">
            <el-input
              v-model="filterForm.keyword"
              placeholder="搜索关键词"
              @keyup.enter.native="handleFilterChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
              @change="handleFilterChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label=" ">
            <el-button type="primary" @click="handleFilterChange">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'LogFilter',
  props: {
    devices: {
      type: Array,
      default: () => []
    },
    users: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filterForm: {
        dateRange: [],
        level: '',        // 日志级别（用于前端显示）
        logType: '',      // 日志类型（后端接口参数）
        deviceId: '',
        userId: '',       // 用户ID（唯一标识）
        keyword: ''
      }
    }
  },
  methods: {
    handleFilterChange() {
      this.$emit('filter-change', { ...this.filterForm })
    },
    
    resetFilter() {
      this.filterForm = {
        dateRange: [],
        level: '',        // 日志级别
        logType: '',      // 日志类型
        deviceId: '',
        userId: '',       // 用户ID
        keyword: ''
      }
      this.handleFilterChange()
    }
  }
}
</script>

<style lang="scss" scoped>
.log-filter {
  .el-form {
    .el-row {
      margin-bottom: 18px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .el-form-item {
      margin-bottom: 0;

      .el-form-item__label {
        color: #606266;
        font-weight: 500;
        text-align: right;
        padding-right: 12px;
      }
    }

    // 按钮样式
    .el-button {
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }

    // 按钮行对齐
    .el-col:last-child .el-form-item {
      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
