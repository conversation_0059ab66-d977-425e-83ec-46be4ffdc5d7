<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-16 09:54:56
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-17 20:53:52
 * @Description: 毛利统计
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-rule
      :columns="columns"
      :data="tableData"
      @loadData="loadData">
      <template #rule>
        <div class="rules-tips">
          <h3 class="rule-title">工程师毛利统计规则（按工单下单日期）</h3>

          <div class="rule-item">
            <span class="rule-number">统计周期：</span>
            <span class="rule-text">
              以工单下单日期为准，例如统计 1 月数据，即 1月1日 00:00:00 至
              1月31日 23:59:59
            </span>
          </div>

          <ol>
            <li>
              <div class="rule-item">
                <span class="rule-number">工单状态定义（共9种状态）：</span>
                <span class="rule-text">
                  <span class="warning">待接单</span>
                  、
                  <span class="warning">工程师接单</span>
                  、
                  <span class="warning">工程师出发</span>
                  、
                  <span class="warning">工程师到达</span>
                  、
                  <span class="warning">待确认维修报告</span>
                  、
                  <span class="warning">已完成</span>
                  、
                  <span class="warning">待结算</span>
                  、
                  <span class="warning">待审核</span>
                  、
                  <span class="warning">关闭</span>
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">收入与支出统计：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  抄表收入：统计期间所有抄表的实收金额总和
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  销售收入：服务类型为
                  <span class="warning">散修</span>
                  且状态为
                  <span class="warning">已完成</span>
                  的工单总费用之和
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  总支出：状态为
                  <span class="warning">已完成</span>
                  的工单中，所有耗材成本价格总和
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  总毛利：抄表收入 + 销售收入 - 总支出
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">客户与设备统计：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  客户数：该工程师统计期间所负责的不同客户数量（数据来源：客户-商务信息（技术支持））
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  机器台数：该工程师负责的客户名下所有机器数量之和（数据来源：客户-机器信息（负责工程师））
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">印量与维修统计：</span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  黑白总印量：统计期间所有设备的黑白印量总和（抄表对账-抄表记录）
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  彩色总印量：统计期间所有设备的彩色印量总和（抄表对账-抄表记录）
                </span>
              </div>
              <div class="rule-item">
                <span class="rule-text">
                  总维修次数：状态为
                  <span class="warning">待确认维修报告</span>
                  、
                  <span class="warning">已完成</span>
                  、
                  <span class="warning">待结算</span>
                  、
                  <span class="warning">待审核</span>
                  的工单数量
                </span>
              </div>
            </li>
          </ol>
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 详情 -->
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="75%"
      :no-footer="true"
      @cancel="handleDrawerClose">
      <ProTable
        ref="detailTable"
        :query-param="detailQueryParam"
        :local-pagination="detailLocalPagination"
        :columns="detailColumns"
        :data="detailTableData"
        :show-setting="false"
        :use-infinite-scroll="true"
        :has-more="hasMore"
        @loadData="handleLoadData">
        <template #btn>
          <div class="title-box-right" style="font-size: 14px; gap: 10px">
            <div>抄表收入： {{ totalData?.actualReceipt || 0 }}</div>
            <div>销售收入： {{ totalData?.engineerCustomerIncome || 0 }}</div>
            <div>总成本： {{ totalData?.engineerCustomerCost || 0 }}</div>
            <div>总毛利： {{ totalData?.actualProfit || 0 }}</div>
            <div>黑白印量： {{ totalData?.blackNums || 0 }}</div>
            <div>彩色印量： {{ totalData?.colorNums || 0 }}</div>
            <div>总维修次数： {{ totalData?.repairTimes || 0 }}</div>
          </div>
        </template>
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button icon="el-icon-view" @click="handleDetail(row)">
              查看
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDrawer>
    <!-- 工程师接单明细 -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="60%"
      top="6%"
      :no-footer="true"
      @cancel="dialogVisible = false">
      <ProForm
        ref="ProFrom"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :form-param="formParam"
        :form-list="formColumns"
        :open-type="'info'"
        :confirm-loading="confirmLoading"></ProForm>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  engineerGrossProfitApi,
  engineerGrossProfitDetailApi,
  engineerGrossProfitSummaryApi,
} from "@/api/repair";

export default {
  name: "GrossMargin",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "engineerCode",
          title: "工程师账号",
          // isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "engineerName",
          title: "工程师名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "monthly",
          title: "年月",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          valueFormat: "yyyy-MM",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "actualReceipt",
          title: "抄表收入",
          isTable: true,
        },
        {
          dataIndex: "saleIncome",
          title: "销售收入",
          isTable: true,
        },
        {
          dataIndex: "totalExpenditure",
          title: "总支出",
          isTable: true,
        },
        {
          dataIndex: "actualProfit",
          title: "总毛利",
          isTable: true,
        },
        {
          dataIndex: "customerNum",
          title: "客户数",
          isTable: true,
        },
        {
          dataIndex: "machineNum",
          title: "机器台数",
          isTable: true,
        },
        {
          dataIndex: "blackNums",
          title: "黑白总印量",
          isTable: true,
        },
        {
          dataIndex: "colorNums",
          title: "彩色总印量",
          isTable: true,
        },
        {
          dataIndex: "repairNums",
          title: "总维修次数",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 100,
        },
      ],
      tableData: [],
      editType: "info",
      drawerVisible: false,
      drawerTitle: "",
      // 明细数据
      detailQueryParam: {
        monthly: null,
        engineerId: null,
      },
      detailLocalPagination: {
        pageNumber: 1,
        pageSize: 15,
        total: 0,
      },
      detailColumns: [
        {
          dataIndex: "seqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "客户名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "actualReceipt",
          title: "抄表收入",
          isTable: true,
        },
        {
          dataIndex: "engineerCustomerIncome",
          title: "销售收入",
          isTable: true,
        },
        {
          dataIndex: "engineerCustomerCost",
          title: "总成本",
          isTable: true,
        },
        {
          dataIndex: "actualProfit",
          title: "总毛利",
          isTable: true,
        },
        {
          dataIndex: "blackNums",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorNums",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "repairTimes",
          title: "维修次数",
          isTable: true,
        },
        {
          dataIndex: "totalWorkTime",
          title: "总工时(分钟)",
          isTable: true,
        },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   tableSlot: "action",
        //   width: 100,
        // },
      ],
      detailTableData: [],
      hasMore: true,
      totalData: {},
      // 明细
      dialogVisible: false,
      dialogTitle: "张三接单明细",
      formParam: {},
      formColumns: [
        {
          dataIndex: "code",
          title: "工程师账号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "name",
          title: "工程师名称",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "orderNum",
          title: "接单次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        // {
        //   dataIndex: "overTimeOrderNum",
        //   title: "超时接单次数",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        //   searchSpan: 6,
        // },
        {
          dataIndex: "departNum",
          title: "出发次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "arriveNum",
          title: "到达次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "overTime",
          title: "超时到达次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "reportRepair",
          title: "提交报告次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "confirmReport",
          title: "客户确认次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "waitOrder",
          title: "未确认工单数量",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "avgOrderTime",
          title: "平均接单时长",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "avgHalfwayTime",
          title: "平均路上时长",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "avgRepairTime",
          title: "平均维修时长",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "avgReportTime",
          title: "平均报告确认时长",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "totalAmount",
          title: "抄表收入",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "blackWhite",
          title: "抄表黑白印量",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "colorPrint",
          title: "抄表彩色印量",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "num",
          title: "总维修次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "otherNum",
          title: "散客维修次数",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "otherAmount",
          title: "散客维修收入",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
      ],
      confirmLoading: false,
      monthly: null,
      engineerId: null,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startMonthly: null,
          endMonthly: null,
          data: parameter.monthly,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.monthly;
      engineerGrossProfitApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.detailQueryParam = {};
      this.editType = type;
      this.drawerTitle = `${row.monthly} - 毛利统计详情`;
      this.detailQueryParam = {
        monthly: row.monthly,
        engineerId: row.engineerId,
      };
      this.detailLocalPagination = {
        pageNumber: 1,
        pageSize: 15,
        total: 0,
      };
      this.hasMore = true;
      // .monthly = row.monthly;
      // this.engineerId = row.engineerId;
      this.drawerVisible = true;
      this.$nextTick(() => {
        this.$refs.detailTable.refresh();
      });
    },
    // 加载明细数据
    handleLoadData(parameter) {
      this.detailQueryParam = filterParam(
        Object.assign({}, this.detailQueryParam, parameter)
      );
      const requestParameters = cloneDeep(this.detailQueryParam);
      // requestParameters.monthly = this.monthly;
      // requestParameters.engineerId = this.engineerId;
      engineerGrossProfitDetailApi(requestParameters)
        .then((res) => {
          if (parameter.pageNumber === 1) {
            this.detailTableData = res.data.rows;
            this.detailLocalPagination.pageNumber = 1;
          } else {
            this.detailTableData = [...this.detailTableData, ...res.data.rows];
            this.detailLocalPagination.pageNumber = parameter.pageNumber;
          }
          this.detailLocalPagination.total = +res.data.total;

          // 判断是否还有更多数据
          this.hasMore =
            this.detailTableData.length < this.detailLocalPagination.total;
          this.$refs.detailTable && this.$refs.detailTable.resetScrolling();
        })
        .finally(() => {
          this.$refs.detailTable &&
            (this.$refs.detailTable.listLoading = false);
        });
      this.getTotalData(requestParameters);
    },
    getTotalData(params) {
      engineerGrossProfitSummaryApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    handleDetail(row) {
      this.dialogTitle = `${row.name} - 月度统计明细`;
      this.dialogVisible = true;
    },
    handleDrawerClose() {
      this.drawerVisible = false;
      this.monthly = null;
      this.engineerId = null;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
