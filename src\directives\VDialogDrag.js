export default {
  bind(el, binding, vnode, oldVnode) {
    if (binding.value) {
      const dialogHeaderEl = el.querySelector(".dialog_header");
      dialogHeaderEl.style.cursor = "move";
      dialogHeaderEl.onmousedown = function (e) {
        var x = e.clientX - el.offsetLeft;
        var y = e.clientY - el.offsetTop;
        document.onmousemove = function (eve) {
          el.style.left = eve.clientX - x + "px";
          el.style.top = eve.clientY - y + "px";
        };
        document.onmouseup = function () {
          document.onmousemove = null;
          document.onmouseup = null;
        };
      };
    }
  },
};
