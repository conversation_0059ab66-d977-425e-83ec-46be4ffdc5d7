/*
 * @Author: AI Assistant
 * @Date: 2025-01-29
 * @Description: Android应用版本管理API接口
 */

import { get, post, put, del } from "@/utils/request";

// ================== 应用版本管理API ==================

/**
 * 版本列表分页查询
 * @param {Object} data 查询参数
 * @returns {Promise} 分页数据
 */
export const getVersionPage = (data) => post("/admin/app-version/page", data);

/**
 * 发布新版本
 * @param {Object} data 版本数据
 * @returns {Promise} 版本ID
 */
export const publishVersion = (data) => post("/admin/app-version/publish", data);

/**
 * 更新版本信息
 * @param {Number} id 版本ID
 * @param {Object} data 更新数据
 * @returns {Promise} 更新结果
 */
export const updateVersion = (id, data) => {
  // 将数据转换为查询参数
  const params = new URLSearchParams();
  Object.keys(data).forEach(key => {
    if (data[key] !== null && data[key] !== undefined) {
      params.append(key, data[key]);
    }
  });

  return put(`/admin/app-version/${id}?${params.toString()}`);
};

/**
 * 设置/取消强制更新
 * @param {Number} id 版本ID
 * @param {Boolean} force 是否强制
 * @returns {Promise} 操作结果
 */
export const toggleForceUpdate = (id, force) =>
  post(`/admin/app-version/${id}/force?force=${force}`);

/**
 * 紧急操作
 * @param {String} action 操作类型 (rollback/pause/resume)
 * @param {Object} params 操作参数
 * @returns {Promise} 操作结果
 */
export const emergencyAction = (action, params = {}) => 
  post(`/admin/app-version/emergency/${action}`, params);

/**
 * 删除版本
 * @param {Number} id 版本ID
 * @returns {Promise} 删除结果
 */
export const deleteVersion = (id) => del(`/admin/app-version/${id}`);

/**
 * 获取版本详情
 * @param {Number} id 版本ID
 * @returns {Promise} 版本详情
 */
export const getVersionDetail = (id) => get(`/admin/app-version/${id}`);

/**
 * 获取下载日志
 * @param {Object} data 查询参数
 * @returns {Promise} 下载日志分页数据
 */
export const getDownloadLogs = (data) => post("/admin/app-version/download-logs/page", data);

// ================== 客户端更新检查API（用于测试） ==================

/**
 * 检查应用更新（客户端接口）
 * @param {Number} currentVersionCode 当前版本号
 * @param {String} deviceId 设备ID
 * @returns {Promise} 更新检查结果
 */
export const checkAppUpdate = (currentVersionCode, deviceId) => 
  get("/app/update", { currentVersionCode, deviceId });

// ================== 定向发布API ==================

/**
 * 设置定向发布
 * @param {Number} versionId 版本ID
 * @param {Object} targetData 目标数据
 * @returns {Promise} 操作结果
 */
export const setTargetedRelease = (versionId, targetData) => {
  const requestData = {
    deviceIds: targetData.deviceIds || [],
    groupIds: targetData.groupIds || [],
    overrideExisting: targetData.overrideExisting || false
  };

  // 添加用户名称信息 - 格式：姓名 (编码)
  if (targetData.users && targetData.users.length > 0) {
    requestData.users = targetData.users.map(user => ({
      id: user.id,
      name: user.name && user.code ?
        `${user.name} (${user.code})` :
        (user.displayName || user.name || '未知用户')
    }));
  }

  // 添加设备名称信息 - 格式：设备ID(品牌 型号 系统版本)
  if (targetData.devices && targetData.devices.length > 0) {
    requestData.devices = targetData.devices.map(device => ({
      id: device.id,
      name: device.deviceId && device.brand && device.model ?
        `${device.deviceId}(${device.brand} ${device.model} ${device.osVersion || ''})`.trim() :
        (device.displayName || device.deviceId || '未知设备')
    }));
  }

  // 不添加用户组信息，app端无法识别用户组
  // 用户组信息只通过groupIds传递给后端用于记录

  return post(`/admin/app-version/${versionId}/targeted-release`, requestData);
};

/**
 * 转为全局发布
 * @param {Number} versionId 版本ID
 * @returns {Promise} 操作结果
 */
export const setGlobalRelease = (versionId) =>
  put(`/admin/app-version/${versionId}/global-release`);

/**
 * 查看版本分发情况
 * @param {Number} versionId 版本ID
 * @returns {Promise} 分发关系列表
 */
export const getVersionDistributions = async (versionId) => {
  const response = await get(`/admin/app-version/${versionId}/distributions`);

  // 如果API返回标准格式 {code, message, data}，则返回data部分
  if (response && response.code === 200 && response.data) {
    return Array.isArray(response.data) ? response.data : [];
  }

  // 如果直接返回数组，则直接返回
  if (Array.isArray(response)) {
    return response;
  }

  // 其他情况返回空数组
  return [];
};

/**
 * 获取用户列表（用于定向发布选择）
 * @returns {Promise} 用户列表
 */
export const getUserList = async () => {
  const { userListApi } = await import('@/api/user');
  const response = await userListApi({ pageNumber: 1, pageSize: 1000 });

  if (response.code === 200 && response.data && response.data.rows) {
    return response.data.rows.map(user => ({
      id: user.id,
      name: user.name,
      code: user.code,
      username: user.code,
      displayName: `${user.name || '未知用户'} (${user.code || '无编码'})`
    }));
  }

  return [];
};

/**
 * 获取设备列表（用于定向发布选择）
 * @returns {Promise} 设备列表
 */
export const getDeviceList = async () => {
  const { deviceApi } = await import('@/logcontrol/api/deviceApi');
  const response = await deviceApi.getDeviceList();

  if (response.code === 200 && response.data) {
    return response.data.map(device => ({
      id: device.id,
      deviceId: device.deviceId,
      brand: device.brand,
      model: device.model,
      osVersion: device.osVersion,
      displayName: `${device.deviceId} (${device.model} ${device.osVersion})`
    }));
  }

  return [];
};

/**
 * 获取用户组列表（用于定向发布选择）
 * @returns {Promise} 用户组列表
 */
export const getRoleList = async () => {
  const response = await get('/magina/manage/role', {
    pageNumber: 1,
    pageSize: 1000  // 获取所有角色，设置较大的pageSize
  });

  if (response.code === 200 && response.data && response.data.rows) {
    return response.data.rows.map(role => ({
      id: role.id,
      name: role.name,
      code: role.code,
      memberCount: parseInt(role.members) || 0,
      displayName: `${role.name} (${role.members}人)`
    }));
  }

  return [];
};

/**
 * 获取用户组成员列表
 * @param {String} roleId 用户组ID
 * @returns {Promise} 成员列表
 */
export const getRoleMembers = async (roleId) => {
  const response = await get(`/magina/manage/user-role/member-page/${roleId}`, {
    pageNumber: 1,
    pageSize: 1000,  // 获取该角色的所有成员
    isFilter: true
  });

  if (response.code === 200 && response.data && response.data.rows) {
    return response.data.rows.map(user => ({
      id: user.id,
      name: user.name,
      code: user.code,
      mobileNumber: user.mobileNumber,
      displayName: `${user.name} (${user.code})`
    }));
  }

  return [];
};

/**
 * 批量获取多个用户组的成员列表
 * @param {Array} groupIds 用户组ID数组
 * @returns {Promise} 所有成员的用户ID数组（去重）
 */
export const getGroupMembersUserIds = async (groupIds) => {
  if (!groupIds || groupIds.length === 0) {
    return [];
  }

  try {
    // 并行获取所有用户组的成员
    const memberPromises = groupIds.map(groupId => getRoleMembers(groupId));
    const memberResults = await Promise.all(memberPromises);

    // 合并所有成员并去重
    const allMembers = memberResults.flat();
    const uniqueUserIds = [...new Set(allMembers.map(member => member.id))];

    return uniqueUserIds;
  } catch (error) {
    console.error('获取用户组成员失败:', error);
    return [];
  }
};

// ================== 辅助方法 ==================

/**
 * 格式化文件大小
 * @param {Number} bytes 字节数
 * @returns {String} 格式化后的大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 获取更新类型文本
 * @param {Object} version 版本对象
 * @returns {String} 更新类型文本
 */
export const getUpdateTypeText = (version) => {
  if (version.adminForce) return '管理员强制';
  if (version.isForce) return '强制更新';
  return '可选更新';
};

/**
 * 获取更新类型颜色
 * @param {Object} version 版本对象
 * @returns {String} Element UI标签颜色
 */
export const getUpdateTypeColor = (version) => {
  if (version.adminForce) return 'danger';
  if (version.isForce) return 'warning';
  return 'primary';
};

/**
 * 获取发布类型文本
 * @param {String} releaseType 发布类型
 * @returns {String} 发布类型文本
 */
export const getReleaseTypeText = (releaseType) => {
  const typeMap = {
    'GLOBAL': '全局发布',
    'TARGETED': '定向发布'
  };
  return typeMap[releaseType] || '未知';
};

/**
 * 获取发布类型颜色
 * @param {String} releaseType 发布类型
 * @returns {String} Element UI标签颜色
 */
export const getReleaseTypeColor = (releaseType) => {
  const colorMap = {
    'GLOBAL': 'success',
    'TARGETED': 'primary'
  };
  return colorMap[releaseType] || 'info';
};

/**
 * 验证版本名称格式
 * @param {String} versionName 版本名称
 * @returns {Boolean} 是否有效
 */
export const validateVersionName = (versionName) => {
  // 支持以下格式：
  // 1.0.0 (标准三段式版本号)
  // 1.0 (两段式版本号)
  // 1.0-debug (带后缀的两段式)
  // 1.0.1-debug (带后缀的三段式)
  // 1.0.0-alpha, 1.0.0-beta, 1.0.0-rc1 等
  const versionRegex = /^\d+\.\d+(\.\d+)?(-[a-zA-Z0-9]+)?$/;
  return versionRegex.test(versionName);
};

/**
 * 验证APK文件
 * @param {File} file 文件对象
 * @returns {Object} 验证结果
 */
export const validateApkFile = (file) => {
  const maxSize = 100 * 1024 * 1024; // 100MB
  const allowedTypes = ['application/vnd.android.package-archive'];
  
  if (!file) {
    return { valid: false, message: '请选择APK文件' };
  }
  
  if (file.size > maxSize) {
    return { valid: false, message: '文件大小不能超过100MB' };
  }
  
  if (!file.name.toLowerCase().endsWith('.apk')) {
    return { valid: false, message: '只能上传APK文件' };
  }
  
  return { valid: true };
};

/**
 * 移除分发关系（逻辑删除）
 * @param {String} versionId 版本ID
 * @param {String} targetType 目标类型（USER、DEVICE、GROUP）
 * @param {String} targetId 目标ID
 * @returns {Promise} 操作结果
 */
export const removeDistribution = (versionId, targetType, targetId) =>
  del(`/admin/app-version/${versionId}/distributions/${targetType}/${targetId}`);

/**
 * 取消激活分发关系
 * @param {String} versionId 版本ID
 * @param {String} targetType 目标类型（USER、DEVICE、GROUP）
 * @param {String} targetId 目标ID
 * @returns {Promise} 操作结果
 */
export const deactivateDistribution = (versionId, targetType, targetId) =>
  put(`/admin/app-version/${versionId}/distributions/${targetType}/${targetId}/deactivate`);

/**
 * 重新激活分发关系
 * @param {String} versionId 版本ID
 * @param {String} targetType 目标类型（USER、DEVICE、GROUP）
 * @param {String} targetId 目标ID
 * @returns {Promise} 操作结果
 */
export const activateDistribution = (versionId, targetType, targetId) =>
  put(`/admin/app-version/${versionId}/distributions/${targetType}/${targetId}/activate`);

/**
 * 获取所有分发关系配置列表（基础版）
 * @param {Boolean} isActiveOnly 是否只返回激活状态的分发关系，默认 false
 * @returns {Promise} 分发关系列表
 */
export const getDistributions = (isActiveOnly = false) =>
  get('/admin/app-version/distributions', { isActiveOnly });

/**
 * 获取所有分发关系详细信息（包含版本信息）
 * @param {Boolean} isActiveOnly 是否只返回激活状态的分发关系，默认 false
 * @returns {Promise} 详细分发关系列表
 */
export const getDistributionsDetail = (isActiveOnly = false) =>
  get('/admin/app-version/distributions/detail', { isActiveOnly });
