<!--
 * @Author: yangzhong
 * @Date: 2023-11-01 11:35:37
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-03 14:46:46
 * @Description: 
-->
<template>
  <ProDrawer
    :value="showDrawer"
    :title="title"
    size="80%"
    :no-footer="true"
    :destroy-on-close="true"
    @cancel="cancel"
  >
    <el-tabs
      type="border-card"
      class="tabs"
      :stretch="true"
      :before-leave="handleTabBeforeLeave"
    >
      <el-tab-pane label="基础信息" lazy>
        <div class="tab-content">
          <EditBaseInfo
            :id="customId"
            ref="childTab"
            :type="editType"
            :base-info="baseInfo"
            @closeDrawer="cancel"
            @refresh="refresh"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="员工信息" lazy>
        <div class="tab-content">
          <EditStaff :id="customId" ref="childTab" :type="editType" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="商务信息" lazy>
        <div class="tab-content">
          <EditBusiness
            :id="customId"
            ref="childTab"
            :type="editType"
            @closeDrawer="cancel"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="机器信息" lazy>
        <div class="tab-content">
          <EditDeviceGroup :id="customId" ref="childTab" :type="editType" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="地址管理" lazy>
        <div class="tab-content">
          <Address :id="customId" :type="editType" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="积分管理" lazy>
        <div class="tab-content">
          <IntegralManager :id="customId" :type="editType" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="联网设置" lazy>
        <div class="tab-content">
          <Reporting :id="customId" ref="childTab" :type="editType" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="用户标签" lazy>
        <div class="tab-content">
          <spanTag :id="customId" ref="childTab" :type="editType" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="拜访记录" lazy>
        <div class="tab-content">
          <editAccessRecord
            :id="customId"
            ref="childTab"
            :shop-name="shopName"
            :seq-id="seqId"
            :type="editType"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="购买意向" lazy>
        <div class="tab-content">
          <BuyPurpose
            :id="customId"
            :seq-id="seqId"
            :shop-name="shopName"
            :type="editType"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="合约记录" lazy>
        <div class="tab-content">
          <Agreement
            :id="customId"
            :seq-id="seqId"
            :shop-name="shopName"
            :type="editType"
          />
        </div>
      </el-tab-pane>
      <!--<el-tab-pane label="客户报价" lazy>-->
      <!--  <div class="tab-content">客户报价</div>-->
      <!--</el-tab-pane>-->
      <el-tab-pane label="访问记录" lazy>
        <div class="tab-content">
          <statisticalData :id="customId" ref="childTab" :type="editType" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="搜索记录" lazy>
        <div class="tab-content">
          <SearchRecord :id="customId" ref="childTab" :type="editType" />
        </div>
      </el-tab-pane>

      <el-tab-pane label="耗材仓库" lazy>
        <div class="tab-content">
          <Store :id="customId" :type="editType" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="领料记录" lazy>
        <div class="tab-content">
          <Apply :id="customId" :type="editType" />
        </div>
      </el-tab-pane>
      <!--<el-tab-pane label="订单&开票" lazy>-->
      <!--  <div class="tab-content">订单&开票</div>-->
      <!--</el-tab-pane>-->
      <el-tab-pane label="客户价值" lazy>
        <div class="tab-content">
          <Worth :id="customId" :type="editType" />
        </div>
      </el-tab-pane>
    </el-tabs>
  </ProDrawer>
</template>

<script>
import { Message } from "element-ui";

export default {
  name: "EditCustom",
  components: {
    EditBaseInfo: () => import("./editBaseInfo.vue"),
    EditBusiness: () => import("./editBusiness.vue"),
    EditStaff: () => import("./editStaff.vue"),
    EditDeviceGroup: () => import("./editDeviceGroup.vue"),
    Reporting: () => import("./reporting.vue"),
    editAccessRecord: () => import("./editAccessRecord.vue"),
    statisticalData: () => import("./statisticalData.vue"),
    spanTag: () => import("./span.vue"),
    SearchRecord: () => import("./searchRecord.vue"),
    BuyPurpose: () => import("./buyPurpose.vue"),
    Agreement: () => import("./agreement.vue"),
    Worth: () => import("./worth.vue"),
    Store: () => import("./store.vue"),
    Apply: () => import("./apply"),
    Address: () => import("./address.vue"),
    IntegralManager: () => import("./integralMan.vue"),
  },
  data() {
    return {
      // 是否显示抽屉
      showDrawer: false,
      title: "",
      customId: null,
      editType: null,
      baseInfo: null,
      shopName: null,
      seqId: null,
    };
  },
  methods: {
    open(type, row) {
      if (row) {
        const { id, name, seqId } = row;
        this.customId = id;
        this.shopName = name;
        this.seqId = seqId;
        // 处理赋值问题
        const baseInfo = JSON.parse(JSON.stringify(row));
        Object.keys(baseInfo).forEach((key) => {
          baseInfo[key] = baseInfo[key].label
            ? baseInfo[key].value
            : baseInfo[key];
          if (JSON.stringify(baseInfo[key]) === "{}") {
            baseInfo[key] = "";
          }
        });
        this.baseInfo = baseInfo;
      }
      this.editType = type;
      this.title = this.getTitleByType(type, type === "add" ? "" : row.seqId);
      this.showDrawer = true;
    },
    cancel() {
      this.showDrawer = false;
      this.customId = null;
      this.name = null;
      this.seqId = null;
      this.editType = null;
      this.baseInfo = null;
    },
    getTitleByType(type, code) {
      const titleMap = {
        edit: `编辑客户 - 客户编号${code}`,
        add: `新增客户`,
        info: `客户信息 - 客户编号${code}`,
      };
      return titleMap[type];
    },
    // tab离开监听
    handleTabBeforeLeave(activeName, oldActiveName) {
      // 新建客户时，必须要先保存基础信息之后才可以继续填写其他几个tab
      if (this.editType === "add" && activeName !== "0") {
        Message.warning("请先保存基础信息");
        return false;
      }
    },
    refresh() {
      this.$emit("refresh");
    },
  },
};
</script>

<style lang="scss" scoped></style>
