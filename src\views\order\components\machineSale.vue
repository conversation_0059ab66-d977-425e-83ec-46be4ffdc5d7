<template>
  <div>
    <ProTable
      ref="ProTable"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :query-param="queryParam"
      :local-pagination="localPagination"
      sticky
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <div class="box">
          <div class="left">
            <el-button
              type="success"
              class="add-btn"
              size="mini"
              icon="el-icon-plus"
              @click="createdOreder()"
            >
              创建订单
            </el-button>
          </div>
          <div class="title-box-right">
            <div>总订单数量：{{ totalData?.orderNum || 0 }}</div>
            <div>总商品金额：{{ totalData?.itemAmount || 0 }}</div>
            <div>总配送费：{{ totalData?.shippingFeeAmount || 0 }}</div>
            <div>总订单金额：{{ totalData?.totalAmount || 0 }}</div>
            <div>实付金额：{{ totalData?.paidAmount || 0 }}</div>
          </div>
        </div>
      </template>
      <template #type="slotProps">
        {{ slotProps.row?.type?.label }}
      </template>
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
      <template #actions="{ row }">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleInfo(row)"
          >
            查看
          </el-button>
          <el-button
            v-if="
              row.orderStatus === 'WAIT_DELIVER' ||
              row.orderStatus === 'PART_DELIVER'
            "
            icon="el-icon-present"
            @click="handleDispatch(row)"
          >
            发货
          </el-button>
          <el-button
            v-if="
              row.orderType?.value === 'APPLY' &&
              row.orderStatus === 'WAIT_AUDIT'
            "
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="showAuditDialog(row)"
          >
            审核
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <PreviewMachineOrder ref="previewMachineOrder" />

    <ProDialog
      :value="showDialogAudit"
      width="80%"
      title="审核订单"
      :no-footer="true"
      @cancel="showDialogAudit = false"
    >
      <el-descriptions title="订单信息" border>
        <el-descriptions-item label="用户名">
          {{ auditData.consigneeName }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          {{ auditData.consigneePhone }}
        </el-descriptions-item>
        <el-descriptions-item label="订单编号">
          {{ auditData.orderNum }}
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag size="small">
            {{ auditData.orderStatus === "WAIT_AUDIT" ? "待审核" : "已审核" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订单金额">
          {{ auditData.actualGoodsAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="联系地址">
          {{ auditData.consigneeFullAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="支付方式"
          ><el-tag size="small">
            {{ auditData.payMode?.label }}
          </el-tag></el-descriptions-item
        >
        <el-descriptions-item label="携带方式">
          <el-tag size="small">
            {{ auditData.logisticsProvider?.label }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订单创建时间">
          {{ auditData.orderTime }}
        </el-descriptions-item>
      </el-descriptions>

      <p class="tit-box m-b-12">商品信息</p>
      <ProTable
        ref="ProSPXXTable"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="spxxColumns"
        :show-pagination="false"
        :show-loading="false"
        :show-setting="false"
        :data="auditData.tradeOrderDetailList || []"
        :show-search="false"
        :show-table-operator="false"
        sticky
        :height="200"
      >
        <template #storageArticle="{ row }">
          {{ row.storageArticle?.minUnit }}
        </template>
      </ProTable>

      <div class="footer-btn">
        <el-popconfirm
          title="确定通过该物料吗？"
          confirm-button-text="通过"
          confirm-button-type="success"
          placement="top"
          @confirm="handleAudit(auditData, 'APPROVE')"
        >
          <el-button slot="reference" type="primary"> 通过 </el-button>
        </el-popconfirm>
        <el-popconfirm
          title="确定驳回该物料吗？"
          confirm-button-text="驳回"
          confirm-button-type="warning"
          placement="top"
          @confirm="handleAudit(auditData, 'REFUSE')"
        >
          <el-button slot="reference" type="warning"> 驳回 </el-button>
        </el-popconfirm>
      </div>
    </ProDialog>
    <AddMachineOrder ref="addMachineOrder" @refresh="refresh" />
    <DispatchGoods ref="dispatchGoods" @refresh="refresh" />
  </div>
</template>
<script>
import {
  operatorTradeOrderPageApi,
  operatorTradeOrderDetailApi,
  optionsGetRegionApi,
  operatorTradeOrderAuditApi,
  getOrderStatisticsApi,
} from "@/api/operator";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";
import { filterParam, filterParamRange } from "@/utils";

export default {
  name: "MachineSale",
  components: {
    AddMachineOrder: () =>
      import("@/views/order/components/addMachineOrder.vue"),
    PreviewMachineOrder: () =>
      import("@/views/order/components/previewMachineOrder.vue"),
    DispatchGoods: () => import("@/views/order/components/dispatchGoods.vue"),
  },
  props: {},
  data() {
    return {
      showDialogAudit: false,
      auditData: {},
      optionsGetRegion: [],
      // 列表
      tableData: [],
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      queryParam: {
        orderNum: "",
      },
      columns: [
        {
          dataIndex: "orderStatusList",
          title: "订单状态",
          isSearch: true,
          isTable: true,
          formSpan: 8,
          valueType: "select",
          multiple: true,
          formatter: (row) => {
            switch (row.orderStatus) {
              case "WAIT_PAY":
                return "待支付";
              case "WAIT_DELIVER":
                return "待发货";
              case "PART_DELIVER":
                return "部分发货";
              case "WAIT_RECEIVE":
                return "待收货";
              case "SUCCESS":
                return "已完成";
              case "CLOSED":
                return "已取消";
              case "WAIT_AUDIT":
                return "待审核";
            }
          },
          option: [
            { label: "待支付", value: "WAIT_PAY" },
            { label: "待发货", value: "WAIT_DELIVER" },
            { label: "待收货", value: "WAIT_RECEIVE" },
            { label: "已完成", value: "SUCCESS" },
            { label: "已取消", value: "CLOSED" },
            { label: "待审核", value: "WAIT_AUDIT" },
          ],
          minWidth: 80,
        },
        {
          dataIndex: "orderType",
          title: "订单类型",
          isTable: true,
          formatter: (row) => row.orderType?.label,
          minWidth: 80,
          // isSearch: true,
          // valueType: "select",
          // option: [
          //   { label: "销售订单", value: "SALE" },
          //   { label: "领料单", value: "APPLY" },
          // ],
        },
        {
          dataIndex: "payTime",
          title: "支付时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          clearable: true,
        },
        // {
        //   dataIndex: "logisticsProviders",
        //   title: "配送方式",
        //   isSearch: true,
        //   multiple: true,
        //   valueType: "select",
        //   option: [
        //     { label: "京东快递", value: "jdl" },
        //     { label: "闪送", value: "iss" },
        //     { label: "自提", value: "self" },
        //     { label: "工程师带", value: "passing" },
        //     { label: "线下物流", value: "logistics" },
        //   ],
        // },
        {
          dataIndex: "orderNum",
          title: "订单号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
          minWidth: 60,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
          minWidth: 70,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
          minWidth: 70,
        },
        {
          dataIndex: "consigneeFullAddress",
          title: "收货地址",
          isTable: true,
          minWidth: 250,
        },
        {
          dataIndex: "payModes",
          title: "支付方式",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            { label: "微信支付", value: "WECHART" },
            { label: "线下支付", value: "OFFLINE" },
            { label: "期结", value: "CYCLE" },
          ],
        },
        {
          dataIndex: "consigneePhone",
          title: "手机号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        // {
        //   dataIndex: "logisticsProvider",
        //   title: "配送方式",
        //   isTable: true,
        //   formatter: (row) => row.logisticsProvider?.label,
        // },
        {
          dataIndex: "payMode",
          title: "支付方式",
          isTable: true,
          formatter: (row) => row.payMode.label,
        },
        {
          dataIndex: "actualGoodsAmount",
          title: "商品金额",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "shippingFee",
          title: "配送费",
          isTable: true,
        },
        {
          dataIndex: "actualAmount",
          title: "订单金额",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "createdAt",
          title: "下单时间",
          isTable: true,
          width: 150,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
        },
        {
          dataIndex: "payTime",
          title: "支付时间",
          isTable: true,
          width: 150,
          formatter: (row) => (row.payTime ? row.payTime : "/"),
        },
        {
          dataIndex: "orderNum",
          title: "订单号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "Actions",
          width: 150,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      //新增
      confirmLoading: false,
      dialogTitle: "",
      dialogVisible: false,
      form: {},
      spxxColumns: [
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          minWidth: 100,
        },
        {
          dataIndex: "itemName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 100,
        },
        {
          dataIndex: "fullAmount",
          title: "机器总价（元）",
          isTable: true,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "定金（元）",
          isTable: true,
        },
        {
          dataIndex: "discountAmount",
          title: "会员减免",
          isTable: true,
        },
        {
          dataIndex: "payAmount",
          title: "小计（元）",
          isTable: true,
        },
      ],
      totalData: {},
    };
  },
  created() {
    this.optionsGetRegionAFn();
  },
  mounted() {
    if (this.$route.query.id) {
      this.queryParam.orderNum = this.$route.query.id;
    }
    this.refresh();
  },
  methods: {
    // 审核订单
    async handleAudit(row, status) {
      const params = {
        id: row.id,
        auditStatus: status,
        remark: row.remark,
      };
      try {
        const result = await operatorTradeOrderAuditApi(params);
        if (result.code === 200) {
          if (status === "APPROVE") {
            Message.success("审核通过");
          } else {
            Message.success("已驳回");
          }
          this.showDialogAudit = false;
          this.$refs.ProTable.refresh();
        }
      } catch (e) {
        Message.error("系统错误");
      }
    },
    // 审核领料订单
    showAuditDialog(row) {
      this.showDialogAudit = true;
      this.auditData = row;
      operatorTradeOrderDetailApi(row.orderNum).then((res) => {
        this.auditData = res.data.tradeOrder;
      });
    },
    // 获取统计数据
    getOrderStatisticsFn(params) {
      getOrderStatisticsApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    // 创建订单
    createdOreder() {
      this.$refs.addMachineOrder.show();
    },
    optionsGetRegionAFn() {
      optionsGetRegionApi().then((res) => {
        this.optionsGetRegion = res.data;
      });
    },
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          orderTimeStart: null,
          orderTimeEnd: null,
          data: parameter.createdAt,
        },
        {
          payTimeStart: null,
          payTimeEnd: null,
          data: parameter.payTime,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = { ...this.queryParam, isMechine: true };
      delete requestParameters.createdAt;
      delete requestParameters.payTime;
      operatorTradeOrderPageApi(requestParameters)
        .then((res) => {
          const result = [];
          this.localPagination.total = parseInt(res.data.total);
          if (res.data.rows.length === 0) {
            this.tableData = [];
            return;
          }

          res.data.rows.forEach(async (item, index) => {
            const code = item.consigneeRegionCode;
            result[index] = item;
            if (result.length === res.data.rows.length) {
              this.tableData = result;
            }
          });
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
      this.getOrderStatisticsFn(requestParameters);
    },
    // handleDispatch
    handleDispatch(row) {
      this.$refs.dispatchGoods.show(row);
    },
    // 触发详情
    handleInfo(row) {
      this.$refs.previewMachineOrder.show(row);
    },
    /**
     * @description 获取省市区区域数据
     * @param node
     * @param {Function} resolve
     * @returns {Promise<void>}
     */
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 处理省市区数据
     * @param list
     * @returns {*}
     */
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },

    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>
<style lang="scss" scoped>
.box {
  display: flex;
  justify-content: space-between;
  flex: 1;
}
.footer-btn {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

::v-deep .el-upload--picture-card,
::v-deep .el-upload-list__item {
  width: 120px;
  height: 120px;
}

.sp-content {
  margin-top: 20px;
}

.add-sp-box {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translate(-50%, 0);
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.steps-box {
  position: relative;
  width: 80%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  z-index: 2;
}

.order-fix {
  margin-left: 20px;
  font-size: 14px;

  .red {
    color: #d14b50;
  }

  .text-content {
    display: flex;
    justify-content: space-between;
    .text-p {
      //&.right {
      //  position: relative;
      //  left: 85%;
      //  top: 0;
      //}

      color: #606266;

      .p-label {
        color: #606266;
        font-weight: 700;
      }

      //margin-top: 15px;
      margin: 30px 0;
    }
  }

  .content-fixed {
    display: flex;
    justify-content: space-between;

    .text-p {
      flex: 1;
      display: flex;
    }
  }

  .btn-p {
    margin-top: 15px;

    .el-button {
      padding: 8px 29px;
    }
  }

  .order-border-box {
    border: dashed 1px #ccc;
    padding: 10px;
  }

  .title-p {
    background: #d9d9d9;
    color: #232323;
    padding: 5px;
  }
}

.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}

.tit-boxs {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px 7px;
  font-size: 16px;
  font-weight: 800;

  // &::before {
  //   content: "";
  //   width: 5px;
  //   height: 20px;
  //   background: #409eff;
  //   display: inline-block;
  //   position: absolute;
  //   left: -1px;
  //   top: 4px;
  // }
}

.change-number {
  width: 100%;
  display: flex;
  align-items: center;

  .input {
    width: 30%;
    // height: 30px;
    // line-height: 30px;
    text-align: center;

    ::v-deep .el-input__inner {
      height: 20px !important;
      line-height: 20px !important;
    }
  }

  .add {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    display: flex;
    user-select: none;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    border: 1px solid #606266;
    color: #606266;
  }
}

.totalNumber {
  width: 100%;
  display: flex;
  align-items: center;

  padding: 0 20px;
  box-sizing: border-box;
  color: #606266;
  line-height: 26px;
  font-weight: 700;

  .totalNumber-list {
    margin-right: 30px;
  }
}
</style>
