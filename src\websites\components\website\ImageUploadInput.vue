<template>
  <div class="image-upload-input">
    <!-- 紧凑模式 -->
    <div v-if="compact" class="compact-mode">
      <!-- 手动输入框 -->
      <el-input
        v-if="allowManualInput"
        :value="value"
        @input="handleManualInput"
        :placeholder="placeholder"
        :disabled="disabled"
        size="small"
        class="compact-input"
      >
        <template slot="append">
          <el-button
            icon="el-icon-view"
            :disabled="!value"
            @click="showPreview"
            size="small"
          />
        </template>
      </el-input>

      <div class="compact-content">
        <el-upload
          action="#"
          :http-request="customUpload"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :disabled="disabled || uploading"
          accept="image/*"
          drag
          class="compact-upload"
        >
          <div class="compact-upload-content">
            <i v-if="!uploading" class="el-icon-upload"></i>
            <i v-else class="el-icon-loading"></i>
            <div class="compact-text">
              {{ uploading ? '上传中...' : '上传图片' }}
            </div>
          </div>
        </el-upload>

        <!-- 预览和操作 -->
        <div v-if="value && !uploading" class="compact-preview">
          <div v-if="value && !value.startsWith('http')" class="emoji-preview">
            {{ value }}
          </div>
          <img v-else :src="value" alt="预览图" @click="showPreview" />
          <div class="compact-actions">
            <el-button v-if="value.startsWith('http')" size="mini" icon="el-icon-view" @click="showPreview" />
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="removeImage" />
          </div>
        </div>
      </div>

      <!-- 进度条 -->
      <el-progress
        v-if="uploading"
        :percentage="uploadProgress"
        :show-text="false"
        class="upload-progress"
      />
    </div>
    
    <!-- 完整模式 -->
    <div v-else class="full-mode">
      <!-- 手动输入URL -->
      <el-input
        v-if="allowManualInput"
        :value="value"
        @input="handleManualInput"
        :placeholder="placeholder"
        :disabled="disabled"
        class="url-input"
      >
        <template slot="append">
          <el-button 
            icon="el-icon-view" 
            :disabled="!value"
            @click="showPreview"
          />
        </template>
      </el-input>
      
      <!-- 拖拽上传区域 -->
      <el-upload
        action="#"
        :http-request="customUpload"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :disabled="disabled || uploading"
        accept="image/*"
        drag
        class="drag-upload"
        :class="{ 'drag-over': dragOver }"
        @dragenter="dragOver = true"
        @dragleave="dragOver = false"
        @drop="dragOver = false"
      >
        <div class="upload-content">
          <i v-if="!uploading" class="el-icon-upload upload-icon"></i>
          <i v-else class="el-icon-loading upload-icon"></i>
          <div class="upload-text">
            {{ uploading ? '上传中...' : '拖拽图片到此处上传，或点击选择文件' }}
          </div>
          <div class="upload-hint">
            支持 JPG、PNG、GIF、WebP 格式，最大 {{ formatFileSize(maxSize) }}
          </div>
        </div>
      </el-upload>
      
      <!-- 进度条 -->
      <el-progress 
        v-if="uploading" 
        :percentage="uploadProgress" 
        class="upload-progress"
      />
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="previewVisible"
      width="65%"
      center
      :modal="true"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="true"
    >
      <div class="preview-content" style="display: flex; justify-content: center; align-items: center; width: 100%; text-align: center; padding: 0; margin: 0;">
        <img :src="value" alt="预览图" class="preview-image" style="margin: 0 auto; display: block; max-width: 100%; max-height: 85vh; min-width: 60%;" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { uploadFile } from '@/api/upload'
import { websiteImageApi } from '@/websites/api/website'

export default {
  name: 'ImageUploadInput',

  // 静态方法：记录图片信息到数据库
  recordImageInfo: async function(file, uploadResult, category = 'content') {
    try {
      const { websiteImageApi } = await import('@/websites/api/website')

      // 映射分类到枚举值（与React项目保持一致）
      const categoryMap = {
        // 页面内容分类
        'homepage': 'HOMEPAGE',
        'service': 'SERVICE',
        'case': 'CASE',
        'banner': 'BANNER',
        'logo': 'LOGO',
        'team': 'TEAM',
        'equipment': 'EQUIPMENT',
        'gallery': 'GALLERY',

        // 具体功能分类映射
        'company': 'HOMEPAGE',        // 公司照片 -> 首页相关
        'milestone': 'HOMEPAGE',      // 里程碑图片 -> 首页相关
        'icon': 'LOGO',              // 图标 -> Logo类
        'qrcode': 'LOGO',            // 二维码 -> Logo类
        'wechat': 'LOGO',            // 微信相关 -> Logo类
        'qq': 'LOGO',                // QQ相关 -> Logo类
        'avatar': 'TEAM',            // 头像 -> 团队类
        'member': 'TEAM',            // 成员照片 -> 团队类
        'device': 'EQUIPMENT',       // 设备图片 -> 设备类
        'repair': 'EQUIPMENT',       // 维修相关 -> 设备类
        'content': 'GALLERY',        // 通用内容 -> 图片库
        'image': 'GALLERY',          // 通用图片 -> 图片库

        // 兼容旧的分类
        'about': 'HOMEPAGE',
        'contact': 'GALLERY',
        'config': 'GALLERY',
        'other': 'GALLERY'
      }

      const imageData = {
        originalName: file.name,
        cosKey: uploadResult.key,
        cosUrl: uploadResult.url,
        fileSize: file.size,
        mimeType: file.type,
        category: categoryMap[category] || 'GALLERY',
        description: `上传的${category}图片`,
        isPublic: true
      }

      await websiteImageApi.recordImageUpload(imageData)
      console.log('图片信息已记录到数据库')
      return imageData
    } catch (error) {
      console.error('记录图片信息失败:', error)
      throw error
    }
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    category: {
      type: String,
      default: 'content'
    },
    maxSize: {
      type: Number,
      default: 5 * 1024 * 1024 // 5MB
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '图片URL或拖拽上传'
    },
    allowManualInput: {
      type: Boolean,
      default: true
    },
    compact: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      uploading: false,
      uploadProgress: 0,
      previewVisible: false,
      dragOver: false
    }
  },
  
  methods: {
    // 手动输入URL
    handleManualInput(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    },
    
    // 上传前验证
    beforeUpload(file) {
      // 文件类型验证
      const isImage = file.type.startsWith('image/')
      if (!isImage) {
        this.$message.error(`文件 ${file.name} 格式不支持，请选择 JPG、PNG、GIF、WebP 格式的图片`)
        return false
      }

      // 文件大小验证
      if (file.size > this.maxSize) {
        this.$message.error(`文件 ${file.name} 大小超过限制（${this.formatFileSize(this.maxSize)}）`)
        return false
      }

      return true
    },

    // 自定义上传
    async customUpload(options) {
      const file = options.file

      try {
        this.uploading = true
        this.uploadProgress = 0

        // 使用Vue项目的上传API
        const result = await uploadFile(file)

        // 暂时不记录到数据库，等用户保存内容时再记录
        // 这样避免用户多次更换图片时产生无用的数据库记录

        this.uploading = false
        this.uploadProgress = 100

        // 成功回调
        this.$emit('input', result.url)
        this.$emit('change', result.url)
        this.$message.success('图片上传成功！')

      } catch (error) {
        this.uploading = false
        this.uploadProgress = 0
        console.error('图片上传失败:', error)
        this.$message.error('图片上传失败：' + (error.message || '未知错误'))
      }
    },

    // 记录图片信息到数据库（与React项目保持一致）
    async recordImageUpload(file, uploadResult) {
      try {
        // 映射分类到枚举值（与React项目保持一致）
        const categoryMap = {
          // 页面内容分类
          'homepage': 'HOMEPAGE',
          'service': 'SERVICE',
          'case': 'CASE',
          'banner': 'BANNER',
          'logo': 'LOGO',
          'team': 'TEAM',
          'equipment': 'EQUIPMENT',
          'gallery': 'GALLERY',

          // 具体功能分类映射
          'company': 'HOMEPAGE',        // 公司照片 -> 首页相关
          'milestone': 'HOMEPAGE',      // 里程碑图片 -> 首页相关
          'icon': 'LOGO',              // 图标 -> Logo类
          'qrcode': 'LOGO',            // 二维码 -> Logo类
          'wechat': 'LOGO',            // 微信相关 -> Logo类
          'qq': 'LOGO',                // QQ相关 -> Logo类
          'avatar': 'TEAM',            // 头像 -> 团队类
          'member': 'TEAM',            // 成员照片 -> 团队类
          'device': 'EQUIPMENT',       // 设备图片 -> 设备类
          'repair': 'EQUIPMENT',       // 维修相关 -> 设备类
          'content': 'GALLERY',        // 通用内容 -> 图片库
          'image': 'GALLERY',          // 通用图片 -> 图片库

          // 兼容旧的分类
          'about': 'HOMEPAGE',
          'contact': 'GALLERY',
          'config': 'GALLERY',
          'other': 'GALLERY'
        }

        const imageData = {
          originalName: file.name,
          cosKey: uploadResult.key,
          cosUrl: uploadResult.url,
          fileSize: file.size,
          mimeType: file.type,
          category: categoryMap[this.category] || 'GALLERY',
          description: `上传的${this.category}图片`,
          isPublic: true
        }

        await websiteImageApi.recordImageUpload(imageData)
        console.log('图片信息已记录到数据库')
      } catch (error) {
        console.error('记录图片信息失败:', error)
        throw error
      }
    },

    // 显示预览
    showPreview() {
      if (this.value) {
        this.previewVisible = true
      }
    },
    
    // 移除图片
    removeImage() {
      this.$emit('input', '')
      this.$emit('change', '')
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style lang="scss" scoped>
.image-upload-input {
  .compact-mode {
    .compact-input {
      margin-bottom: 8px;
    }

    .compact-content {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .compact-upload {
      width: 80px;
      height: 80px;
      flex-shrink: 0;

      ::v-deep .el-upload {
        width: 100% !important;
        height: 100% !important;

        .el-upload-dragger {
          width: 100% !important;
          height: 100% !important;
          display: flex !important;
          flex-direction: column !important;
          align-items: center !important;
          justify-content: center !important;
          border: 1px dashed #d9d9d9 !important;
          padding: 0 !important;
          margin: 0 !important;

          &:hover, &.is-dragover {
            border-color: #409eff !important;
          }
        }
      }

      .compact-upload-content {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
        width: 100% !important;
        height: 100% !important;
        padding: 0 !important;
        margin: 0 !important;

        i {
          font-size: 20px !important;
          color: #409eff !important;
          margin: 0 0 4px 0 !important;
          display: block !important;
          line-height: 1 !important;
        }

        .compact-text {
          font-size: 11px !important;
          color: #606266 !important;
          line-height: 1.2 !important;
          margin: 0 !important;
          padding: 0 !important;
        }
      }
    }
    
    .compact-preview {
      position: relative;
      width: 80px;
      height: 80px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
        cursor: pointer;
      }

      .emoji-preview {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        background: #f5f7fa;
      }

      .compact-actions {
        position: absolute;
        top: 2px;
        right: 2px;
        display: flex;
        gap: 2px;
        opacity: 0;
        transition: opacity 0.3s;

        ::v-deep .el-button {
          padding: 5px 7px !important;
          min-height: 28px !important;
          height: 28px !important;
          width: 28px !important;
          border-radius: 4px !important;

          i {
            font-size: 13px !important;
            margin: 0 !important;
          }

          span {
            display: none !important;
          }
        }

        ::v-deep .el-button--mini {
          padding: 5px 7px !important;
          min-height: 28px !important;
          height: 28px !important;
          width: 28px !important;
        }
      }

      &:hover .compact-actions {
        opacity: 1;
      }
    }
  }
  
  .full-mode {
    .url-input {
      margin-bottom: 16px;
    }
    
    .drag-upload {
      ::v-deep .el-upload {
        width: 100%;
        
        .el-upload-dragger {
          width: 100%;
          height: 180px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border: 2px dashed #d9d9d9;
          border-radius: 6px;
          transition: border-color 0.3s;
          
          &:hover, &.is-dragover {
            border-color: #409eff;
          }
          
          .upload-content {
            text-align: center;
            
            .upload-icon {
              font-size: 48px;
              color: #c0c4cc;
              margin-bottom: 16px;
            }
            
            .upload-text {
              color: #606266;
              font-size: 14px;
              margin-bottom: 8px;
            }
            
            .upload-hint {
              color: #909399;
              font-size: 12px;
            }
          }
        }
      }
      
      &.drag-over {
        ::v-deep .el-upload-dragger {
          border-color: #409eff;
          background-color: #f0f9ff;
        }
      }
    }
  }
  
  .upload-progress {
    margin-top: 8px;
  }
  
  .preview-content {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    min-height: 60vh !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    text-align: center !important;

    .preview-image {
      max-width: 65% !important;
      max-height: 60vh !important;
      width: auto !important;
      height: auto !important;
      object-fit: contain !important;
      border-radius: 8px !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
      display: block !important;
      margin: 0 auto !important;
      min-width: 60% !important;
    }
  }
}

// 修复对话框遮罩层问题
::v-deep .el-dialog__wrapper {
  z-index: 2000 !important;
}

::v-deep .el-overlay {
  z-index: 2000 !important;
}

::v-deep .el-dialog {
  z-index: 2001 !important;
}

// 优化图片预览对话框样式
::v-deep .el-dialog__body {
  padding: 5px !important;
  margin: 0 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

::v-deep .el-dialog__header {
  padding: 10px 15px 5px !important;
}

::v-deep .el-dialog {
  margin: 15px auto !important;
}

// 强制图片预览居中
::v-deep .preview-content {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  text-align: center !important;
}

::v-deep .preview-image {
  display: block !important;
  margin: 0 auto !important;
}

// 针对图片预览对话框的特殊样式
::v-deep .el-dialog[aria-label="图片预览"] {
  .el-dialog__body {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 20px !important;
  }

  .preview-content {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
  }

  .preview-image {
    margin: 0 auto !important;
    display: block !important;
  }
}

// 全局图片预览样式（最高优先级）
.el-dialog__wrapper .el-dialog__body .preview-content {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  text-align: center !important;
  padding: 0 !important;
  margin: 0 !important;
}

.el-dialog__wrapper .el-dialog__body .preview-image {
  margin: 0 auto !important;
  display: block !important;
  max-width: 100% !important;
  max-height: 85vh !important;
  min-width: 60% !important;
  width: auto !important;
  height: auto !important;
}

// 进一步减少对话框边距
::v-deep .el-dialog__wrapper {
  padding: 10px !important;
}

// 确保图片能够充分利用空间
::v-deep .preview-content {
  min-height: 70vh !important;
  padding: 0 !important;
  margin: 0 !important;
}

// 强制紧凑模式上传区域居中
.compact-upload ::v-deep .el-upload-dragger {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 !important;
  text-align: center !important;
}

.compact-upload .compact-upload-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-align: center !important;
  padding: 0 !important;
  margin: 0 !important;
}

.compact-upload .compact-upload-content i {
  display: block !important;
  margin: 0 auto 4px auto !important;
  text-align: center !important;
}

// 紧凑模式预览按钮样式
.compact-preview .compact-actions .el-button {
  padding: 5px 7px !important;
  min-height: 28px !important;
  height: 28px !important;
  width: 28px !important;
  border-radius: 4px !important;
  font-size: 13px !important;
  line-height: 1 !important;

  i {
    font-size: 13px !important;
    margin: 0 !important;
  }

  span {
    display: none !important;
  }
}

.compact-preview .compact-actions .el-button--mini {
  padding: 5px 7px !important;
  min-height: 28px !important;
  height: 28px !important;
  width: 28px !important;
}

.compact-preview .compact-actions .el-button--danger {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
  color: white !important;
}
</style>
