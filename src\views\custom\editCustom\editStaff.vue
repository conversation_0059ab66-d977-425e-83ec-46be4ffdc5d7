<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:06:16
 * @Description: 
 -->
<template>
  <div class="edit-staff">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :height="600"
      show-index
      show-loading
      :show-search="false"
      show-pagination
      :reserve-selection="true"
      @loadData="loadData"
    >
      <template v-if="type !== 'info'" #btn>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-plus"
          @click="handleEditStaff()"
          >新增</el-button
        >
      </template>

      <template #status="{ row }">
        {{ row.status ? "启用" : "禁用" }}
      </template>
      <template #role="{ row }">
        {{ row.role.label }}
      </template>
      <template #vxGroupName="{ row }">
        {{ row.vxGroupName.label }}
      </template>

      <template #action="{ row }">
        <div v-if="type !== 'info'" class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleEditStaff(row)"
            >编辑</el-button
          >
          <!--        <el-button v-if="!row.status" type="primary" size="mini" icon="el-icon-plus" @click="handleStatus(row, true)">启用</el-button>-->
          <!--        <el-button v-else type="danger" size="mini" icon="el-icon-delete" @click="handleStatus(row, false)">禁用</el-button>-->
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDeleteStaff(row)"
            >删除</el-button
          >
        </div>
        <div v-else>/</div>
      </template>
    </ProTable>
    <ProDialog
      :value="showDialog"
      :title="dialogTitle"
      width="600px"
      :confirm-loading="dialogLoading"
      top="5%"
      @ok="handleDialogOk"
      @cancel="closeDialog"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParam"
        :form-list="columns"
        :confirm-loading="dialogLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="staffType"
        :no-footer="true"
        @proSubmit="formSubmit"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import ProDialog from "@/components/ProDialog/index.vue";
import ProForm from "@/components/ProForm/index.vue";
import {
  getCustomerStaffByPageApi,
  deleteCustomerStaffApi,
  addCustomerStaffApi,
  updateCustomerStaffApi,
  updateCustomerStaffStatusApi,
} from "@/api/customer";
import { dictTreeByCodeApi } from "@/api/user";
import { Message, MessageBox } from "element-ui";

export default {
  name: "EditStaff",
  components: { ProTable, ProDialog, ProForm },
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
  },
  data() {
    const self = this;
    return {
      // 表格数据
      columns: [
        {
          dataIndex: "name",
          title: "姓名",
          isTable: true,
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入员工姓名",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "tel",
          title: "手机号",
          isTable: true,
          isForm: true,
          valueType: "input",
          disabled: false,
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入手机号",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "role",
          title: "角色",
          isTable: true,
          tableSlot: "role",
          isForm: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(500),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择角色",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "vxId",
          title: "微信ID",
          isTable: true,
          isForm: true,
          valueType: "input",
          disabled: false,
          clearable: true,
          formSpan: 24,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入微信ID",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "vxNikeName",
          title: "微信昵称",
          isTable: true,
          isForm: true,
          valueType: "input",
          disabled: false,
          clearable: true,
          formSpan: 24,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入微信昵称",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "repairPower",
          title: "维修能力",
          isTable: true,
          isForm: true,
          valueType: "input",
          disabled: false,
          clearable: true,
          formSpan: 24,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入维修能力",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "scoreNum",
          title: "评价分数",
          isTable: true,
          isForm: true,
          valueType: "input",
          disabled: false,
          clearable: true,
          formSpan: 24,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入评价分数",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "vxGroupName",
          title: "答疑群",
          isTable: true,
          tableSlot: "vxGroupName",
          isForm: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(2600),
          optionskey: {
            label: "label",
            value: "value",
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请选择所在答疑群",
          //     trigger: "change",
          //   },
          // ],
        },

        {
          dataIndex: "status",
          title: "账号状态",
          isTable: true,
          tableSlot: "status",
          isForm: true,
          valueType: "select",
          option: [
            {
              label: "启用",
              value: true,
            },
            {
              label: "禁用",
              value: false,
            },
          ],
          prop: [
            {
              required: true,
              message: "请选择账号状态",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
        },
      ],
      tableData: [],
      queryParam: {},
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },

      // 表单数据
      showDialog: false,
      dialogTitle: "新增员工",
      dialogLoading: false,
      formParam: {},
      staffType: "add",
    };
  },
  mounted() {
    // 2600
    this.refresh();
  },
  methods: {
    async loadData(params) {
      try {
        const result = await getCustomerStaffByPageApi({
          ...params,
          customerId: this.id,
        });
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    handleEditStaff(row) {
      if (!row) {
        this.dialogTitle = "新增员工";
        this.staffType = "add";
        this.formParam = {};
        const index = this.columns.findIndex(
          (item) => item.dataIndex === "tel"
        );
        this.columns[index].disabled = false;
        this.$set(this.columns, 1, { ...this.columns[index] });
      } else {
        this.dialogTitle = "编辑员工";
        this.staffType = "edit";
        const index = this.columns.findIndex(
          (item) => item.dataIndex === "tel"
        );
        this.columns[index].disabled = true;
        this.$set(this.columns, 1, { ...this.columns[index] });
        const formParam = JSON.parse(JSON.stringify(row));
        Object.keys(formParam).forEach((key) => {
          formParam[key] = formParam[key].label
            ? formParam[key].value
            : formParam[key];
        });
        this.formParam = formParam;
      }
      this.showDialog = true;
    },
    handleDeleteStaff(row) {
      MessageBox.confirm("确定删除该员工？").then(async () => {
        try {
          await deleteCustomerStaffApi(row.id);
          this.$message.success("删除成功");
          this.refresh();
        } catch (error) {
          Message.error(error.message);
        }
      });
    },
    handleAddStaff() {},
    handleReset() {
      this.queryParam = {};
      this.$refs.ProTable.refresh();
    },
    // 表单相关
    closeDialog() {
      this.showDialog = false;
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    async formSubmit(val) {
      try {
        this.dialogLoading = true;
        const editApi =
          this.staffType === "add"
            ? addCustomerStaffApi
            : updateCustomerStaffApi;
        const result = await editApi({ ...val, customerId: this.id });
        if (result.code === 200) {
          Message.success("操作成功");
          this.closeDialog();
          this.refresh();
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.dialogLoading = false;
      }
    },
    async handleStatus(row, type) {
      try {
        const { status, id } = row;
        if (status === type) return;

        const result = await updateCustomerStaffStatusApi({ id, status: type });
        if (result.code === 200) {
          Message.success("操作成功");
          this.refresh();
        }
      } catch (err) {
        Message.error(err.message);
      }
    },
  },
};
</script>
