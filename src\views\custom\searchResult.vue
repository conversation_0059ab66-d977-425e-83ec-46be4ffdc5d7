<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-05-16 15:19:47
 * @FilePath: \benyin-web\src\views\custom\searchResult.vue
 * @Description: 
 * 
-->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="搜索明细" name="搜索明细">
        <SearchInfo />
      </el-tab-pane>
      <el-tab-pane label="日搜索" name="日搜索">
        <DaySearch />
      </el-tab-pane>
      <!-- <el-tab-pane label="搜索统计" name="搜索统计">搜索统计</el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
import SearchInfo from "@/views/custom/components/searchInfo.vue";
import DaySearch from "@/views/custom/components/daySearch.vue";
export default {
  name: "SearchResult",
  components: { SearchInfo, DaySearch },
  data() {
    return {
      activeName: "搜索明细",
    };
  },
};
</script>

<style scoped lang="scss"></style>
