<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 崩溃率图表组件
-->
<template>
  <div class="crash-rate-chart">
    <div ref="chart" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'CrashRate<PERSON><PERSON>',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    data: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    loading(val) {
      if (this.chart) {
        if (val) {
          this.chart.showLoading()
        } else {
          this.chart.hideLoading()
        }
      }
    }
  },
  mounted() {
    // 延迟初始化，确保容器尺寸已确定
    this.$nextTick(() => {
      setTimeout(() => {
        this.initChart()
      }, 100)
    })
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart()
      
      if (this.loading) {
        this.chart.showLoading()
      }
    },
    
    updateChart() {
      if (!this.chart) return

      // 确保图表尺寸正确
      this.chart.resize()

      const dates = this.data.map(item => item.date)
      const crashRates = this.data.map(item => (item.crashRate || 0).toFixed(2))
      
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            return `${params[0].name}<br/>崩溃率: ${params[0].value}%`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates,
          axisLabel: {
            formatter: function(value) {
              return value.split('-').slice(1).join('/')
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '崩溃率 (%)',
          axisLabel: {
            formatter: '{value}%'
          },
          min: 0,
          max: function(value) {
            return Math.max(value.max * 1.2, 5)
          }
        },
        series: [
          {
            name: '崩溃率',
            type: 'bar',
            data: crashRates,
            itemStyle: {
              color: function(params) {
                const value = parseFloat(params.value)
                if (value >= 5) return '#F56C6C'
                if (value >= 2) return '#E6A23C'
                return '#67C23A'
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      this.chart.setOption(option)
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.crash-rate-chart {
  width: 100%;
  height: 100%;

  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 280px;
    max-height: 320px;
  }
}
</style>
