<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-11 16:20:51
 * @Description: 
 -->
<template>
  <div class="address">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-if="type !== 'info'"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleEdit(null, 'add')"
        >
          新增
        </el-button>
      </template>
      <template #isDefault="{ row }">
        <el-switch
          v-model="row.isDefault"
          :disabled="type === 'info'"
          @change="(e) => handleIsDefault(e, row.id)"
        ></el-switch>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            icon="el-icon-edit"
            :disabled="type === 'info'"
            @click="handleEdit(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            :disabled="type === 'info'"
            icon="el-icon-delete"
            @click="handleDelete(row.id)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="showDrawer"
      :title="drawerTitle"
      size="45%"
      :confirm-loading="confirmLoading"
      :method-type="methodType"
      @ok="handleSubmit"
      @cancel="cancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParams"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
      >
        <template #regionAddress>
          <div class="location">
            <el-cascader
              v-model="formParams.regionCode"
              :disabled="type === 'info'"
              style="width: 400px"
              filterable
              :options="addressOption"
              :props="{
                label: 'name',
                value: 'code',
                children: 'children',
                expandTrigger: 'click',
              }"
              @change="handleReginChange"
            ></el-cascader>
            <span></span>
            <el-input
              v-model="formParams.address"
              :disabled="type === 'info'"
              placeholder="请输入详细地址"
            ></el-input>
          </div>
        </template>
        <template #longitude>
          <el-input
            v-model="location.longitude"
            :disabled="type === 'info'"
            placeholder="请输入经度"
          ></el-input>
        </template>
        <template #latitude>
          <el-input
            v-model="location.latitude"
            :disabled="type === 'info'"
            placeholder="请输入纬度"
          ></el-input>
        </template>
        <template #isDefault>
          <el-switch v-model="formParams.isDefault"> </el-switch>
        </template>
        <template #location>
          <el-button
            icon="el-icon-location-information"
            circle
            @click="handleAddress"
          ></el-button>
        </template>
      </ProForm>
    </ProDrawer>
    <TXMap ref="map" @confirm="confirmAddress" />
  </div>
</template>

<script>
import {
  addCustomerAddressApi,
  deleteCustomerAddressApi,
  getCustomerAddressApi,
  setDefaultAddressApi,
  updateCustomerAddressApi,
} from "@/api/customer";
import { regionTreeApi } from "@/api/store";
import { Message } from "element-ui";
import { cloneDeep } from "lodash";
import TXMap from "@/components/map/TXMap.vue";

export default {
  name: "Address",
  components: { TXMap },
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
  },
  data() {
    return {
      showDialog: false,
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          dataIndex: "contact",
          title: "收件人",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "phone",
          title: "收件人电话",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "regionName",
          title: "所在地区",
          isTable: true,
          width: 200,
        },
        {
          dataIndex: "fullAddress",
          title: "详细地址",
          isTable: true,
        },
        {
          dataIndex: "isDefault",
          title: "是否默认",
          isTable: true,
          tableSlot: "isDefault",
          width: 120,
        },

        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 200,
        },
      ],
      tableData: [],
      drawerTitle: "新增地址",
      showDrawer: false,
      methodType: "add",
      confirmLoading: false,
      formParams: {},
      formColumns: [
        {
          dataIndex: "contact",
          title: "收件人",
          isForm: true,
          formSpan: 16,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入收件人",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "phone",
          title: "收件人电话",
          isForm: true,
          formSpan: 16,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入收件人电话",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "regionCode",
          title: "详细地址",
          isForm: true,
          formSpan: 24,
          formSlot: "regionAddress",
          prop: [
            {
              required: true,
              message: "请输入详细地址",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "precisionAddress",
          title: "门牌号",
          isForm: true,
          formSpan: 24,
          valueType: "input",
        },
        {
          dataIndex: "longitude",
          title: "经度",
          isForm: true,
          formSpan: 12,
          formSlot: "longitude",
        },
        {
          dataIndex: "latitude",
          title: "纬度",
          isForm: true,
          formSpan: 12,
          formSlot: "latitude",
        },
        {
          dataIndex: "isDefault",
          title: "是否默认",
          isForm: true,
          formSpan: 8,
          formSlot: "isDefault",
        },
        {
          dataIndex: "location",
          title: "定位",
          isForm: true,
          formSpan: 8,
          formSlot: "location",
        },
      ],
      formLoading: false,
      addressCode: [],
      addressOption: [],
      dialogTitle: "选取位置信息",
      location: {
        longitude: "",
        latitude: "",
      },
    };
  },
  mounted() {
    this.refresh();
    this.getRegion();
  },
  methods: {
    loadData(params) {
      getCustomerAddressApi(this.id)
        .then((res) => {
          this.tableData = res.data;
          this.localPagination.total = Number(res.data.length);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.methodType = type;
      this.drawerTitle = type === "add" ? "新增地址" : "编辑地址";
      this.formParams = {};
      if (type !== "add") {
        this.formParams = cloneDeep(row);
        this.location = this.formParams.location || {
          longitude: "",
          latitude: "",
        };
      }
      this.showDrawer = true;
    },
    handleIsDefault(val, id) {
      setDefaultAddressApi(id).then((res) => {
        Message.success("操作成功");
        this.refresh();
      });
    },
    handleAddress() {
      this.$refs.map.show(this.formParams.location, this.formParams.address);
    },
    // 确认地址选取
    confirmAddress(e) {
      this.formParams = Object.assign(this.formParams, e);
      this.location = e.location;
    },
    // 确认选取
    handleSubmit() {
      const editApi =
        this.methodType === "add"
          ? addCustomerAddressApi
          : updateCustomerAddressApi;
      const arg = {
        ...this.formParams,
        location: this.location,
        customerId: this.id,
      };
      editApi(arg).then((res) => {
        Message.success("操作成功");
        this.showDrawer = false;
        this.refresh();
      });
    },
    handleDelete(id) {
      this.$confirm("此操作将永久删除该地址, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteCustomerAddressApi(id).then((res) => {
          Message.success("删除成功");
          this.refresh();
        });
      });
    },
    handleReginChange(val) {
      this.formParams.regionCode = val[val.length - 1];
      // this.handleRegionAddress();
    },
    async getRegion(node, resolve) {
      const result = await regionTreeApi();
      this.addressOption = result.data;
    },
    // handleRegionAddress() {
    //   if (this.formParams.regionCode && this.formParams.address) {
    //     this.formParams[
    //       "address"
    //     ] = `${this.formParams.regionCode},${this.formParams.address}`;
    //   } else {
    //     this.formParams["address"] = "";
    //   }
    // },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    cancel() {
      this.showDrawer = false;
    },
  },
};
</script>

<style scoped lang="scss">
.location {
  display: flex;

  & > span {
    display: block;
    margin: 0 10px;
  }
}
</style>
