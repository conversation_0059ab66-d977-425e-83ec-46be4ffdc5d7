<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:20
 * @Description: 粉量上报
 -->

<template>
  <div class="view app-container">
    <!-- 单行样式 -->
    <!-- :table-row-class-name="tableRowClassName"-->
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <!-- <el-button plain type="primary" class="add-btn" size="mini" icon="el-icon-share"
                    @click="exportCustom">导出</el-button> -->
        <!-- <el-button type="success" class="add-btn" size="mini" icon="el-icon-plus"
                    @click="dialogVisible = true">新建</el-button> -->
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #reportTime>
        <el-date-picker
          v-model="reportTime"
          style="width: 100%"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :format="'yyyy-MM-dd HH:mm:ss'"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleDetails(row)"
          >
            查看
          </el-button>
        </div>
      </template>
    </ProTable>

    <!--  详情弹窗  -->
    <ProDialog
      :value="showDialog"
      title="详情"
      width="1000px"
      :confirm-loading="dialogLoading"
      top="20px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <ProForm
        ref="ProForm"
        :form-param="details"
        :form-list="columns"
        :confirm-loading="dialogLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="'info'"
        :no-footer="true"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import { Message } from "element-ui";
import { getPowderApi } from "@/api/iot";
import { productAllApi } from "@/api/dispose";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
export default {
  name: "Powder",
  components: { ProTable },
  data() {
    return {
      productIdName: [],
      options: [],
      tableData: [],
      columns: [
        {
          dataIndex: "machineNumber",
          title: "机器编号",
          isForm: true,
        },
        {
          dataIndex: "macNumber",
          title: "机器编号",
          isSearch: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isForm: true,
          isSearch: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          formatter: (row) => row.deviceGroup?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(700),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "deviceGroupName",
        //   title: "设备组名称",
        //   isSearch: true,
        //   valueType: "input",
        //   formSpan: 12,
        // },
        {
          dataIndex: "deviceGroups",
          title: "设备组名称",
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "machine",
          title: "主机型号",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "toneybottleName",
          title: "粉瓶",
          isTable: true,
          isForm: true,
          isSearch: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "toneyNum",
          title: "数量（%）",
          isTable: true,
          isForm: true,
          isSearch: true,
          valueType: "inputRange",
          formSpan: 12,
        },
        {
          dataIndex: "productList",
          title: "品牌/系列",
          isSearch: true,
          placeholder: "品牌/产品树/系列",
          clearable: true,
          // searchSlot: "fullIdPath",
          valueType: "product",
        },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "cyanCounter",
          title: "彩色计数器",
          isTable: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "reportTime",
          title: "上报时间",
          isTable: true,
          isForm: true,
          isSearch: true,
          searchSlot: "reportTime",
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 100,
        },
      ],
      queryParam: {},
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      reportTime: null,
      showDialog: false,
      dialogLoading: false,
      details: {},
    };
  },
  mounted() {
    this.getProductThird();
    this.refresh();
  },
  methods: {
    async loadData(params) {
      this.queryParam = filterParam(Object.assign({}, this.queryParam, params));
      if (this.reportTime) {
        this.queryParam.reportTimeStart = this.reportTime[0];
        this.queryParam.reportTimeEnd = this.reportTime[1];
      } else {
        delete this.queryParam.reportTimeStart;
        delete this.queryParam.reportTimeEnd;
      }
      const result = [
        {
          geToneyNum: null,
          leToneyNum: null,
          data: params.toneyNum,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      try {
        const requestParameters = cloneDeep(this.queryParam);
        delete requestParameters.toneyNum;
        // TODO 设置上报时间 reportTime
        const result = await getPowderApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.toneyNum >= 80) {
        return "success-row";
      } else if (row.toneyNum <= 40) {
        return "warning-row";
      }
      return "";
    },
    exportCustom() {
      if (this.selection.length === 0) {
        Message.warning("请选择要导出的客户数据");
        return;
      }
      const fieldMap = {};
      this.columns
        .filter((item) => item.isExport === true)
        .map((item) => {
          fieldMap[item.dataIndex] = item.title;
        });
      exportExcel(this.selection, fieldMap, "客户列表");
    },
    handleDetails(row) {
      this.details = row;
      this.details.deviceGroups = row.deviceGroup.label;
      this.showDialog = true;
    },
    handleSelect(arr) {
      this.queryParam.productList = arr.map((item) => item[item.length - 1]);
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
        // this.$refs.ProTable.refresh();
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
