<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-03 17:02:40
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <!--      <template #btn>-->
      <!--        <el-button-->
      <!--          type="success"-->
      <!--          icon="el-icon-plus"-->
      <!--          size="mini"-->
      <!--          @click="handleEdit(null, 'add')"-->
      <!--          >新增</el-button-->
      <!--        >-->
      <!--      </template>-->
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          collapse-tags
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #counter="{ row }">
        <el-input
          v-model="row.counter"
          placeholder="请输入计数器"
          type="text"
          :disabled="!row.isEdits"
          style="width: 130px"
          @click="row.isEdits = true"
        ></el-input>

        <el-link
          v-if="!row.isEdits"
          style="margin-left: 10px"
          icon="el-icon-edit"
          :underline="false"
          @click="clicka(row, 'isEditsId')"
          >编辑</el-link
        >
        <el-link
          v-if="row.isEdits"
          style="margin-left: 10px"
          :underline="false"
          @click="changekwFn(row)"
          >保存</el-link
        >
        <el-link
          v-if="row.isEdits"
          style="margin-left: 10px"
          :underline="false"
          type="info"
          @click="changekwFn()"
          >取消</el-link
        >
      </template>
      <template #location="{ row }">
        <el-input
          v-model="row.location"
          placeholder="请输入储位"
          type="text"
          :disabled="!row.isEdit"
          style="width: 130px"
          @click="row.isEdit = true"
        ></el-input>

        <el-link
          v-if="!row.isEdit"
          style="margin-left: 10px"
          icon="el-icon-edit"
          :underline="false"
          @click="clicka(row, 'isEditId')"
          >编辑</el-link
        >
        <el-link
          v-if="row.isEdit"
          style="margin-left: 10px"
          :underline="false"
          @click="changekwFn(row)"
          >保存</el-link
        >
        <el-link
          v-if="row.isEdit"
          style="margin-left: 10px"
          :underline="false"
          type="info"
          @click="changekwFn()"
          >取消</el-link
        >
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <el-button icon="el-icon-edit" @click="handleEdit(row, 'edit')">
            编辑
          </el-button>
          <el-button icon="el-icon-eleme" @click="handleLend(row)">
            借件
          </el-button>
          <el-button icon="el-icon-warning-outline">拆散</el-button>
          <el-button icon="el-icon-setting">维修</el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="showDrawer"
      size="80%"
      :title="drawerTitle"
      :confirm-loading="false"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :confirm-loading="false"
        :form-list="formColumns"
        :form-param="formParam"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
      >
        <template #fullIdPath>
          <el-cascader
            ref="ProductIds"
            v-model="productIdName"
            filterable
            clearable
            :options="options"
            style="width: 100%"
            :props="{
              label: 'name',
              value: 'fullIdPath',
              children: 'children',
              expandTrigger: 'click',
            }"
            leaf-only
            collapse-tags
            @change="handleSelectForm"
          ></el-cascader>
        </template>
        <template #naram>
          <div>
            <zipFilesProUpload
              v-if="methodType === 'edit'"
              :limit="5"
              :file-list="naramFileList"
              @uploadSuccess="handleZipUploadSuccess"
              @uploadRemove="handleZipUploadRemove"
            />
            <el-button
              v-if="formParam.naramFile?.length > 0"
              @click="handleReviewFile('naramFile')"
              >查看清单</el-button
            >
          </div>
        </template>
        <template #logFile>
          <div>
            <zipFilesProUpload
              v-if="methodType === 'edit'"
              :limit="5"
              :file-list="logFileList"
              @uploadSuccess="handleLogFileUploadSuccess"
              @uploadRemove="handleLogFileUploadRemove"
            />
            <el-button
              v-if="formParam.logFile?.length > 0"
              @click="handleReviewFile('logFile')"
              >查看日志文件清单</el-button
            >
          </div>
        </template>
        <template #deviceGroupImg>
          <ProUpload
            :file-list="formParam.deviceGroupImg"
            :type="methodType"
            :limit="4"
            @uploadSuccess="(e) => handleUploadSuccess(e, 'deviceGroupImg')"
            @uploadRemove="(e) => handleUploadRemove(e, 'deviceGroupImg')"
          />
        </template>
        <template #proofImg>
          <ProUpload
            :file-list="formParam.proofImg"
            :type="methodType"
            :limit="4"
            @uploadSuccess="(e) => handleUploadSuccess(e, 'proofImg')"
            @uploadRemove="(e) => handleUploadRemove(e, 'proofImg')"
          />
        </template>
        <template #printVideo>
          <video
            v-for="item in formParam.printVideo"
            :key="item.url"
            :src="item?.url"
            class="avatar"
            style="width: 150px"
            controls="controls"
          ></video>
          <ProUploadFile
            :type="methodType"
            :limit="4"
            :file-size="400"
            :accept="'video/mp4,video/ogg,video/flv,video/avi,video/wmv,video/rmvb'"
            :file-type="[
              'video/mp4',
              'video/ogg',
              'video/flv',
              'video/avi',
              'video/wmv',
              'video/rmvb',
            ]"
            :multiple="false"
            @uploadSuccess="(res) => handleUploadSuccess(res, 'printVideo')"
            @uploadRemove="(res) => handleUploadRemove(res, 'printVideo')"
          />
        </template>
      </ProForm>
    </ProDrawer>
    <!--  借件  -->
    <LendPart ref="lendPart" />
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { filterParam } from "@/utils";
import { productAllApi } from "@/api/dispose";
import zipFilesProUpload from "@/components/ProUpload/zipFiles.vue";
import ProUpload from "@/components/ProUpload/index.vue";
import ProUploadFile from "@/components/ProUpload/files.vue";
import LendPart from "@/views/store/components/lendPart.vue";
import {
  machineWarehousePageApi,
  updateMachineWarehouseApi,
} from "@/api/store";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "MachineStore",
  components: { ProUploadFile, ProUpload, zipFilesProUpload, LendPart },
  data() {
    return {
      productIdName: [],
      options: [],
      methodType: "add",
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        // {
        //   dataIndex: "articleCode",
        //   title: "物品编号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "machine",
          title: "主机型号",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "fullIdPath",
          title: "机型",
          isSearch: true,
          searchSlot: "fullIdPath",
        },
        // {
        //   dataIndex: "partType",
        //   title: "选配件型号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   width: 100,
        // },
        {
          dataIndex: "type",
          title: "设备类型",
          isTable: true,
          formatter: (row) => row.type?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(800),
          optionskey: {
            label: "label",
            value: "value",
          },
          width: 100,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "produce",
          title: "生产类型",
          isTable: true,
          formatter: (row) => row.produce?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1800),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "color",
          title: "色彩类型",
          isTable: true,
          formatter: (row) => row.color?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1700),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "batchCode",
          title: "序列号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "counter",
          title: "总计数器",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          width: 240,
          tableSlot: "counter",
        },
        {
          dataIndex: "warehouseName",
          title: "归属仓库",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "saleStatus",
          title: "销售状态",
          isTable: true,
          formatter: (row) => {
            switch (row.saleStatus) {
              case "ON_SALE":
                return "在售";
              case "NO_SALE":
                return "停售";
              default:
                return "";
            }
          },
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "在售",
              value: "ON_SALE",
            },
            {
              label: "停售",
              value: "NO_SALE",
            },
          ],
        },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          isSearch: true,
          tableSlot: "location",
          valueType: "input",
          width: 240,
        },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   width: 300,
        //   tableSlot: "action",
        //   fixed: "right",
        // },
      ],
      tableData: [],
      showDrawer: false,
      drawerTitle: "新增机器",
      formParam: {},
      formColumns: [
        {
          dataIndex: "productId",
          title: "品牌/机型",
          isForm: true,
          formSpan: 12,
          formSlot: "fullIdPath",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isForm: true,
          formSpan: 12,
          valueType: "input",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isForm: true,
          formSpan: 8,
          disabled: true,
          valueType: "input",
        },
        {
          dataIndex: "machine",
          title: "机型",
          isForm: true,
          formSpan: 8,
          disabled: true,
          valueType: "input",
        },
        {
          dataIndex: "partType",
          title: "选配件型号",
          isForm: true,
          formSpan: 8,
          disabled: true,
          valueType: "input",
        },
        {
          dataIndex: "deviceSequence",
          title: "序列号",
          isForm: true,
          formSpan: 8,
          valueType: "input",
        },
        {
          dataIndex: "blackWhiteCount",
          title: "黑白计数器",
          isForm: true,
          formSpan: 8,
          valueType: "input",
        },
        {
          dataIndex: "colorCount",
          title: "彩色计数器",
          isForm: true,
          formSpan: 8,
          valueType: "input",
        },
        {
          dataIndex: "totalCount",
          title: "总计数器",
          isForm: true,
          formSpan: 8,
          valueType: "input",
        },
        {
          title: "NARAM数据",
          dataIndex: "naramFile",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
          formSlot: "naram",
        },
        {
          title: "日志数据",
          dataIndex: "logFile",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
          formSlot: "logFile",
        },
        {
          title: "机器照片",
          dataIndex: "deviceGroupImg",
          isForm: true,
          valueType: "upload",
          formSlot: "deviceGroupImg",
          formSpan: 24,
        },
        {
          title: "样张照片",
          dataIndex: "proofImg",
          isForm: true,
          valueType: "upload",
          formSlot: "proofImg",
          formSpan: 24,
        },
        {
          title: "打印视频",
          dataIndex: "printVideo",
          isForm: true,
          valueType: "upload",
          formSlot: "printVideo",
          formSpan: 24,
        },
      ],
      naramFileList: [],
      logFileList: [],
      isEditId: "",
      isEditsId: "",
    };
  },
  mounted() {
    this.refresh();
    this.init();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      machineWarehousePageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows.map((ele) => {
            ele.isEdit = ele.id === this.isEditId;
            ele.isEdits = ele.id === this.isEditsId;
            return ele;
          });
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    handleEdit(row, type) {
      this.methodType = type;
      this.drawerTitle = this.getDrawerTitle(type);
      this.showDrawer = true;
    },
    // 借件
    handleLend(row) {
      this.$refs.lendPart.show(row);
    },
    getDrawerTitle(type) {
      switch (type) {
        case "add":
          return "新增机器";
        case "edit":
          return "编辑机器信息";
        default:
          return "查看机器信息";
      }
    },
    closeDrawer() {
      this.showDrawer = false;
    },
    clicka(row, type) {
      this[type] = row.id;
      this.$refs.ProTable.refresh();
    },
    changekwFn(row) {
      if (row) {
        updateMachineWarehouseApi({
          id: row.id,
          location: row.location,
          counter: row.counter,
        })
          .then(() => {
            this.$message.success("修改成功");
          })
          .finally(() => {
            this.isEditsId = null;
            this.isEditId = null;
            this.$refs.ProTable.refresh();
          });
      } else {
        this.isEditsId = null;
        this.isEditId = null;
        this.$refs.ProTable.refresh();
      }
    },
    handleSelectForm(item) {
      this.formParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.formParam.productIds.push(id);
      });
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
    handleZipUploadSuccess(result) {
      if (!this.formParam.naramFile) {
        this.$set(this.formParam, "naramFile", []);
      }
      this.formParam.naramFile.push(result);
    },
    handleZipUploadRemove() {
      this.formParam.naramFile = [];
    },
    handleLogFileUploadSuccess(result) {
      if (!this.formParam.logFile) {
        this.$set(this.formParam, "logFile", []);
      }
      this.formParam.logFile.push(result);
    },
    handleLogFileUploadRemove() {
      this.formParam.logFile = [];
    },
    handleReviewFile(type) {
      if (type === "naramFile") {
        this.showNaramDialog = true;
      } else {
        this.showLogFileDialog = true;
      }
    },

    handleUploadSuccess(result, type) {
      if (!this.formParam[type]) {
        this.$set(this.formParam, type, []);
      }
      this.formParam[type].push(result);
    },
    handleUploadRemove(file, type) {
      const index = this.formParam[type].findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParam[type].splice(index, 1);
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
