<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-27 11:28:53
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-28 16:55:16
 * @Description: 机器采购退货
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleEdit(null, 'add')"
        >
          新增机器退货
        </el-button>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <el-button
            v-if="row.status?.value === 'REJECT'"
            icon="el-icon-edit"
            @click="handleEdit(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            v-if="row.status?.value === 'WAIT_APPROVE'"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'audit')"
          >
            审核
          </el-button>
          <el-button
            v-if="
              row.status?.value !== 'COMPLETE' && row.status?.value !== 'CLOSED'
            "
            type="danger"
            icon="el-icon-circle-close"
            @click="handleDel(row)"
          >
            关闭
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="visibleDrawer"
      :title="drawerTitle"
      size="85%"
      :destroy-on-close="true"
      :confirm-text="'确认退货'"
      :confirm-button-disabled="formLoading"
      :no-confirm-footer="true"
      @cancel="handleCloseDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="editType"
        @proSubmit="formSubmit"
      >
        <template #purchaseCode>
          <el-button
            v-if="editType === 'add'"
            type="text"
            @click="handleChoosePurchase"
          >
            {{ form.purchaseCode ? form.purchaseCode : "选择采购申请单号" }}
          </el-button>
          <div v-else>{{ form.purchaseCode }}</div>
        </template>
        <template #refundType>
          <el-select
            v-if="editType === 'add' || editType === 'edit'"
            v-model="form.refundType"
            placeholder="请选择退货类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in refundTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <div v-else>{{ form.refundType?.label }}</div>
        </template>
        <template #returnParams>
          <div class="title-box">机器退货列表</div>
          <div class="machine-list">
            <ProTable
              ref="returnProTable"
              :columns="returnColumns"
              :data="returnMachineData"
              :show-search="false"
              :show-setting="false"
              :show-loading="showLoading"
              :table-row-class-name="dialogTableClass"
              :show-selection="editType === 'add' || editType === 'edit'"
              height="60vh"
              @handleSelectionChange="handleSelectionChange"
            >
              <!-- 退款类型 -->
              <template #returnType="{ row }">
                <el-select
                  v-if="editType === 'add' && row.status?.value !== 'RETURN'"
                  v-model="row.returnType"
                  placeholder="请选择退货类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in returnTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <div v-else>{{ row.returnType?.label }}</div>
              </template>
              <!-- 退款金额 -->
              <template #refundAmount="{ row }">
                <el-input
                  v-if="
                    (editType === 'add' || editType === 'edit') &&
                    row.status?.value !== 'RETURN'
                  "
                  v-model="row.refundAmount"
                  type="number"
                  placeholder="请输入退款金额"
                  style="width: 100%"
                ></el-input>
                <div v-else>{{ row.refundAmount }}</div>
              </template>
              <!-- 退货原因 -->
              <template #reason="{ row }">
                <el-input
                  v-if="row.status?.value !== 'RETURN'"
                  v-model="row.reason"
                  type="textarea"
                  :rows="1"
                  :disabled="editType === 'audit' || editType === 'info'"
                  placeholder="请输入退货原因"
                  style="width: 100%"
                ></el-input>
              </template>
            </ProTable>
          </div>
        </template>
        <template #remark>
          <el-input
            v-if="editType !== 'add'"
            v-model="form.remark"
            type="textarea"
            :disabled="editType === 'info'"
            :autosize="{
              minRows: 3,
              maxRows: 6,
            }"
            maxlength="255"
            show-word-limit
            placeholder="请输入备注"
            style="width: 100%"
          ></el-input>
        </template>
      </ProForm>
      <template #footer>
        <div v-if="editType === 'add' || editType === 'edit'">
          <el-button
            :loading="formLoading"
            type="primary"
            @click="handleDrawerOk"
          >
            确认退货
          </el-button>
          <el-button @click="handleCloseDrawer">取消</el-button>
        </div>
        <div v-if="editType === 'audit'">
          <el-button type="danger" @click="handleAudit(false)">
            驳回
          </el-button>
          <el-button type="primary" @click="handleAudit(true)">
            审核通过
          </el-button>
          <el-button @click="handleCloseDrawer">取消</el-button>
        </div>
      </template>
    </ProDrawer>
    <!-- 选择机器采购单号 -->
    <ProDialog
      :value="visibleDialog"
      top="2%"
      title="选择机器采购单号"
      width="70%"
      no-footer
      @cancel="visibleDialog = false"
    >
      <ProTable
        ref="PurchaseProTable"
        :query-param="purchaseQueryParam"
        :local-pagination="purchaseLocalPagination"
        :columns="purchaseColumns"
        :data="purchaseTableData"
        :height="390"
        @loadData="loadPurchaseData"
      >
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button
              icon="el-icon-circle-check"
              @click="confirmChoosePurchase(row)"
            >
              确认单号
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam, filterParamRange, mulAmount } from "@/utils";
import { cloneDeep } from "lodash";
import {
  addMachineReturnApi,
  closeMachineReturnApi,
  getMachinePurchaseApi,
  getMachinePurchaseReceiveListApi,
  getMachineReturnApi,
  getMachineReturnDetailApi,
  updateMachineReturnApi,
} from "@/api/procure";

export default {
  name: "MachineRefund",
  components: {},
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          title: "退货单号",
          dataIndex: "code",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          title: "订单编号",
          dataIndex: "purchaseCode",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          title: "供应商",
          dataIndex: "manufacturerName",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        // {
        //   title: "快递单号",
        //   dataIndex: "trackingNumber",
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          title: "退货状态",
          dataIndex: "status",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "待审核",
              value: "WAIT_APPROVE",
            },
            {
              label: "退款中",
              value: "WAIT_REFUND",
            },
            {
              label: "完成",
              value: "COMPLETE",
            },
            {
              label: "驳回",
              value: "REJECT",
            },
            {
              label: "关闭",
              value: "CLOSED",
            },
          ],
          minWidth: 80,
        },
        // {
        //   title: "开票状态",
        //   dataIndex: "invoiceMethod",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          title: "退货人",
          dataIndex: "createdBy",
          isTable: true,
          isSearch: true,
          valueType: "input",
          formatter: (row) => row.createdBy?.name,
          minWidth: 100,
        },
        {
          title: "退货日期",
          dataIndex: "createdAt",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          title: "退货数量",
          dataIndex: "number",
          isTable: true,
          minWidth: 80,
        },
        {
          title: "审核备注",
          dataIndex: "remark",
          isTable: true,
          minWidth: 180,
        },
        {
          title: "操作",
          dataIndex: "action",
          isTable: true,
          tableSlot: "actions",
          fixed: "right",
          width: 210,
        },
      ],
      tableData: [],
      visibleDrawer: false,
      drawerTitle: "申请退货",
      editType: "info",
      form: {},
      formColumns: [
        {
          dataIndex: "purchaseCode",
          title: "采购申请单编号",
          isForm: true,
          formSpan: 6,
          formSlot: "purchaseCode",
          valueType: "text",
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        // {
        //   dataIndex: "warehouseName",
        //   title: "退货仓库",
        //   isForm: true,
        //   formSpan: 6,
        //   valueType: "text",
        // },
        {
          dataIndex: "refundType",
          title: "退款类型",
          isForm: true,
          formSpan: 6,
          formSlot: "refundType",
          prop: [
            {
              required: true,
              message: "请选择退款类型",
              trigger: "change",
            },
          ],
        },
        // {
        //   dataIndex: "returnAmount",
        //   title: "退货金额",
        //   isForm: true,
        //   formSpan: 8,
        //   valueType: "text",
        // },
        {
          dataIndex: "returnParams",
          title: "商品详情",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "returnParams",
        },
        {
          dataIndex: "remark",
          title: "备注",
          isForm: true,
          formSpan: 24,
          formSlot: "remark",
        },
      ],
      refundTypeOptions: [
        {
          label: "现金退款",
          value: "CASH",
        },
        {
          label: "冲抵货款",
          value: "GOODS",
        },
      ],
      formLoading: false,
      showLoading: false,
      returnColumns: [
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          align: "center",
          formatter: (row) => row.status?.label,
          minWidth: 80,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          align: "center",
          minWidth: 120,
        },
        {
          dataIndex: "productName",
          title: "机器/选配件型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
          minWidth: 80,
        },
        // {
        //   dataIndex: "tagName",
        //   title: "标签型号",
        //   isTable: true,
        //   align: "center",
        //   minWidth: 120,
        // },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 80,
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          minWidth: 80,
        },
        {
          dataIndex: "purchasePrice",
          title: "单价",
          isTable: true,
          minWidth: 100,
        },
        // {
        //   dataIndex: "originCode",
        //   title: "原机器编号",
        //   isTable: true,
        //   align: "center",
        //   minWidth: 120,
        // },
        // {
        //   dataIndex: "deviceSequence",
        //   title: "机器序列号",
        //   isTable: true,
        //   align: "center",
        //   minWidth: 120,
        // },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "returnType",
          title: "退款类型",
          isTable: true,
          tableSlot: "returnType",
          minWidth: 120,
        },
        {
          dataIndex: "refundAmount",
          title: "退款金额",
          isTable: true,
          tableSlot: "refundAmount",
          minWidth: 120,
        },
        {
          dataIndex: "reason",
          title: "退货原因",
          isTable: true,
          tableSlot: "reason",
          minWidth: 250,
        },
      ],
      returnMachineData: [],
      confirmReturnMachine: [],
      returnTypeOptions: [
        {
          label: "退货退款",
          value: "ALL",
        },
        {
          label: "退款不退货",
          value: "REFUND",
        },
        {
          label: "退货不退款",
          value: "RETURN",
        },
      ],
      // 选择退货机器弹窗
      visibleDialog: false,
      purchaseQueryParam: {},
      purchaseLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      purchaseColumns: [
        {
          dataIndex: "purchaseCode",
          title: "采购申请单编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "purchaseCode",
          title: "申请单编号",
          isSearch: true,
          placeholder: "采购申请单编号",
          valueType: "input",
        },
        {
          dataIndex: "warehouseName",
          title: "采购仓库",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        // {
        //   dataIndex: "warehouseId",
        //   isSearch: true,
        //   title: "仓库名称",
        //   valueType: "select",
        //   option: [],
        //   optionMth: () => warehouseListApi({ status: 1401 }),
        //   optionskey: {
        //     label: "name",
        //     value: "id",
        //   },
        //   minWidth: 120,
        // },
        {
          dataIndex: "number",
          title: "采购数量",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "amount",
          title: "采购金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "status",
          title: "确认状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          // isSearch: true,
          // valueType: "select",
          // multiple: true,
          // option: [
          //   {
          //     label: "待确认",
          //     value: "WAIT_APPROVE",
          //   },
          //   {
          //     label: "待收货",
          //     value: "WAIT_RECEIVE",
          //   },
          //   {
          //     label: "收货中",
          //     value: "RECEIVING",
          //   },
          //   {
          //     label: "已完成",
          //     value: "SUCCESS",
          //   },
          //   {
          //     label: "驳回",
          //     value: "REJECT",
          //   },
          //   {
          //     label: "关闭",
          //     value: "CLOSED",
          //   },
          // ],
        },
        {
          dataIndex: "createdBy",
          title: "采购发起人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
          minWidth: 100,
        },
        {
          dataIndex: "createdAt",
          title: "采购发起时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          fixed: "right",
          width: 120,
        },
      ],
      purchaseTableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      getMachineReturnApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    loadPurchaseData(parameter) {
      this.purchaseQueryParam = filterParam(
        Object.assign({}, this.purchaseQueryParam, parameter)
      );
      const searchRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.purchaseQueryParam, searchRange);
      const requestParameters = cloneDeep(this.purchaseQueryParam);
      delete requestParameters.createdAt;
      getMachinePurchaseApi({
        ...requestParameters,
        status: ["RECEIVING", "SUCCESS"],
      })
        .then((res) => {
          this.purchaseTableData = res.data.rows;
          this.purchaseLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.PurchaseProTable &&
            (this.$refs.PurchaseProTable.listLoading = false);
          setTimeout(() => {
            this.$refs.PurchaseProTable.$refs.ProElTable.doLayout();
          }, 300);
        });
    },
    // 选择采购单号
    handleChoosePurchase() {
      this.purchaseQueryParam = {};
      this.visibleDialog = true;
      this.$nextTick(() => {
        this.$refs.PurchaseProTable.refresh();
      });
    },
    // 确认选择采购单号
    confirmChoosePurchase(row) {
      this.confirmReturnMachine = [];
      this.returnMachineData = [];
      this.$refs.returnProTable.$refs.ProElTable.clearSelection();
      this.$set(this.form, "purchaseCode", row.purchaseCode);
      this.$set(this.form, "manufacturerName", row.manufacturerName || "");
      this.$set(this.form, "warehouseName", row.warehouseName || "");
      this.showLoading = true;
      getMachinePurchaseReceiveListApi(row.id)
        .then((res) => {
          if (res.code === 200) {
            this.form = res.data;
            this.returnMachineData = this.form.machines.map((item) => {
              return {
                ...item,
                returnType: item.status?.value !== "RETURN" ? "ALL" : "",
                refundAmount:
                  item.status?.value !== "RETURN" ? item.purchasePrice : "",
              };
            });
          }
        })
        .finally(() => {
          this.showLoading = false;
        });
      this.visibleDialog = false;
    },
    handleSelectionChange(data) {
      this.confirmReturnMachine = data;
    },
    handleEdit(row, type) {
      this.editType = type;
      this.form = {};
      this.drawerTitle =
        type === "add"
          ? "申请退货"
          : type === "info"
          ? "退货详情"
          : type === "edit"
          ? "编辑退货信息"
          : "审核退货单";
      this.confirmReturnMachine = [];
      this.returnMachineData = [];
      if (type === "add") {
        this.returnColumns.forEach((item) => {
          if (item.dataIndex === "status") {
            item.isTable = true;
          }
        });
      } else {
        this.returnColumns.forEach((item) => {
          if (item.dataIndex === "status") {
            item.isTable = false;
          }
        });
        getMachineReturnDetailApi(row.id).then((res) => {
          this.form = res.data;
          if (type === "edit") {
            this.form.refundType = this.form.refundType?.value || "ALL";
          }
          this.returnMachineData = this.form.machineReturnDetails;
        });
      }
      this.visibleDrawer = true;
    },
    // 关闭申请单
    handleDel(row) {
      this.$confirm("是否关闭当前机器采购退货单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        closeMachineReturnApi(row.id).then((res) => {
          this.$message.success("关闭成功");
          this.refresh();
        });
      });
    },
    // 审核采购退货单
    handleAudit(status) {
      const confirmText = status ? "通过" : "驳回";
      this.$confirm(`确定${confirmText}该机器采购退货单?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: status ? "success" : "error",
      }).then(() => {
        const args = {
          id: this.form.id,
          isPass: status,
          remark: this.form.remark,
        };
        updateMachineReturnApi(args).then((res) => {
          this.$message.success("操作成功");
          this.visibleDrawer = false;
          this.refresh();
        });
      });
    },
    handleDrawerOk() {
      if (!this.form.purchaseCode) {
        this.$message.error("请选择采购申请单编号");
        return;
      }

      this.$refs.ProForm.handleSubmit();
    },
    formSubmit(val) {
      if (!this.confirmReturnMachine.length) {
        this.$message.error("请选择退货机器");
        return;
      }
      try {
        this.$confirm("此操作会将勾选的机器提交申请退货, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.formLoading = true;
          const data = cloneDeep(this.confirmReturnMachine);
          data.forEach((item) => {
            delete item.status;
            delete item.id;
          });
          const params = {
            id: this.editType === "add" ? null : this.form.id,
            refundType: val.refundType,
            machineReturnDetails: data,
            purchaserCode: val.purchaseCode,
          };
          addMachineReturnApi(params)
            .then((res) => {
              this.$message.success("操作成功");
              this.refresh();
              this.handleCloseDrawer();
            })
            .finally(() => {
              this.formLoading = false;
            });
        });
      } catch (e) {
        this.$message.error(e.message);
      }
    },
    handleCloseDrawer() {
      this.form = {};
      this.visibleDrawer = false;
    },
    dialogTableClass({ row }) {
      // let list = cloneDeep(this.returnMachineData);
      // if (list.length) {
      //   list = list.map((o) => o.status.value);
      // }

      if (row.status?.value === "RETURN") {
        return "hide-select";
      } else {
        return "";
      }
    },
    handleRefundNumChange(val, row) {
      row.refundAmount = mulAmount(val, row.price);
      let total = 0;
      this.tableData.forEach((item) => {
        total += mulAmount(item.price, item.currNum);
      });
      if (!this.form.refundAmount) {
        this.$set(this.form, "returnAmount", total.toFixed(2));
      }
      this.form.returnAmount = total;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
:deep(.machine-list) {
  .hide-select .el-checkbox {
    display: none;
  }
}
</style>
