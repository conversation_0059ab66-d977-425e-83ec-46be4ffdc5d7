<template>
  <div class="social-media-editor">
    <!-- 编辑按钮 -->
    <el-button
      :class="className"
      type="text"
      @click="open = true"
    >
      <span>编辑{{ label }}</span>
      <el-badge v-if="isChanged" dot />
    </el-button>

    <!-- 编辑对话框 -->
    <el-dialog
      :title="`编辑${label}信息`"
      :visible.sync="open"
      width="600px"
      :before-close="handleCancel"
      append-to-body
      class="social-media-dialog"
    >
      <div class="edit-form">
        <!-- 联系方式 -->
        <div class="form-section">
          <label class="section-label">账号/联系方式（可选）</label>
          <el-input
            v-model="tempNumber"
            placeholder="请输入社交媒体账号或联系方式"
          />
          <div class="field-hint">
            可选项：输入社交媒体账号、用户名或联系方式。可留空仅显示二维码
          </div>
        </div>

        <!-- 自定义图标 -->
        <div class="form-section">
          <label class="section-label">自定义图标（可选）</label>
          <ImageUploadInput
            v-model="tempIcon"
            category="icon"
            :maxSize="100 * 1024"
            :compact="true"
            placeholder="拖拽图标到此处上传，或输入emoji表情/图片URL"
            :allowManualInput="true"
          />
          <div class="field-hint">
            支持上传图片文件、emoji表情（如：{{ defaultIcon }}）或直接输入图片链接。图片建议尺寸：32x32px，文件大小不超过100KB
          </div>
        </div>

        <!-- 二维码 -->
        <div class="form-section">
          <label class="section-label">二维码图片</label>
          <ImageUploadInput
            v-model="tempQrCode"
            category="qrcode"
            :maxSize="500 * 1024"
            :compact="true"
            placeholder="拖拽二维码图片到此处上传，或输入图片URL"
            :allowManualInput="true"
          />
          <div class="field-hint">
            支持上传图片文件或直接输入图片链接。建议尺寸：256x256px或512x512px，文件大小不超过500KB
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImageUploadInput from './ImageUploadInput.vue'

export default {
  name: 'SocialMediaEditor',
  components: {
    ImageUploadInput
  },
  props: {
    type: {
      type: String,
      required: true,
      validator: value => ['wechat', 'qq'].includes(value)
    },
    number: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    qrCode: {
      type: String,
      default: ''
    },
    isChanged: {
      type: Boolean,
      default: false
    },
    className: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      open: false,
      tempNumber: '',
      tempIcon: '',
      tempQrCode: ''
    }
  },
  computed: {
    label() {
      return '社交媒体'
    },
    defaultIcon() {
      return this.type === 'wechat' ? '🙂' : '😊'
    }
  },
  watch: {
    number: {
      handler(val) {
        this.tempNumber = val
      },
      immediate: true
    },
    icon: {
      handler(val) {
        this.tempIcon = val
      },
      immediate: true
    },
    qrCode: {
      handler(val) {
        this.tempQrCode = val
      },
      immediate: true
    }
  },
  methods: {
    handleSave() {
      const numberKey = this.type === 'wechat' ? 'wechatNumber' : 'qqNumber'
      const iconKey = this.type === 'wechat' ? 'wechatIcon' : 'qqIcon'
      const qrCodeKey = this.type === 'wechat' ? 'wechatQrCode' : 'qqQrCode'
      
      this.$emit('change', numberKey, this.tempNumber.trim())
      this.$emit('change', iconKey, this.tempIcon.trim())
      this.$emit('change', qrCodeKey, this.tempQrCode.trim())
      
      this.open = false
    },
    handleCancel() {
      this.tempNumber = this.number
      this.tempIcon = this.icon
      this.tempQrCode = this.qrCode
      this.open = false
    },
    handleImageError(e) {
      e.target.style.display = 'none'
    }
  }
}
</script>

<style lang="scss" scoped>
.social-media-editor {
  .edit-form {
    .form-section {
      margin-bottom: 16px;

      .section-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 6px;
        color: #303133;
      }

      .field-hint {
        margin-top: 4px;
        color: #909399;
        font-size: 12px;
        line-height: 1.4;
      }


    }
  }
  
  .dialog-footer {
    text-align: right;
  }
}

// 社交媒体编辑对话框样式优化
::v-deep .social-media-dialog {
  .el-dialog__body {
    padding: 16px 20px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .el-dialog__header {
    padding: 16px 20px 8px;
  }

  .el-dialog__footer {
    padding: 8px 20px 16px;
  }
}
</style>
