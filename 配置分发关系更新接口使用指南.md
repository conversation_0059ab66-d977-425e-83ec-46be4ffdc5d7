# 配置分发关系更新接口使用指南

## 🆕 新增的更新接口

### 1. 更新分发关系激活状态
```javascript
PUT /logcontrol/config/distribution/{distributionId}/status
```

**请求参数：**
```javascript
{
  "isActive": true  // 或 false
}
```

**使用方法：**
```javascript
// 启用分发关系
const enableDistribution = async (distributionId) => {
  const response = await fetch(`/logcontrol/config/distribution/${distributionId}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      isActive: true
    })
  });
  return response.json();
};

// 停用分发关系
const disableDistribution = async (distributionId) => {
  const response = await fetch(`/logcontrol/config/distribution/${distributionId}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      isActive: false
    })
  });
  return response.json();
};

// 切换分发关系状态
const toggleDistribution = async (distributionId, isActive) => {
  const response = await fetch(`/logcontrol/config/distribution/${distributionId}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      isActive: isActive
    })
  });
  return response.json();
};
```

### 2. 更新分发关系的配置
```javascript
PUT /logcontrol/config/distribution/{distributionId}/config
```

**请求参数：**
```javascript
{
  "configId": 123  // 新的配置ID
}
```

**使用方法：**
```javascript
// 更换分发关系的配置
const changeDistributionConfig = async (distributionId, newConfigId) => {
  const response = await fetch(`/logcontrol/config/distribution/${distributionId}/config`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      configId: newConfigId
    })
  });
  return response.json();
};
```

### 3. 删除分发关系（按ID）
```javascript
DELETE /logcontrol/config/distribution/{distributionId}
```

**使用方法：**
```javascript
// 根据分发关系ID删除
const deleteDistributionById = async (distributionId) => {
  const response = await fetch(`/logcontrol/config/distribution/${distributionId}`, {
    method: 'DELETE'
  });
  return response.json();
};
```

## 💡 完整的分发关系管理类

```javascript
class ConfigDistributionManager {
  
  // 原有方法...
  async getDistributions(filters = {}) {
    const params = new URLSearchParams();
    if (filters.targetType) params.append('targetType', filters.targetType);
    if (filters.keyword) params.append('keyword', filters.keyword);
    
    const response = await fetch(`/logcontrol/config/assignments?${params}`);
    return response.json();
  }
  
  // 新增：更新激活状态
  async updateActiveStatus(distributionId, isActive) {
    const response = await fetch(`/logcontrol/config/distribution/${distributionId}/status`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ isActive })
    });
    return response.json();
  }
  
  // 新增：更换配置
  async updateConfig(distributionId, configId) {
    const response = await fetch(`/logcontrol/config/distribution/${distributionId}/config`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ configId })
    });
    return response.json();
  }
  
  // 新增：根据ID删除分发关系
  async deleteById(distributionId) {
    const response = await fetch(`/logcontrol/config/distribution/${distributionId}`, {
      method: 'DELETE'
    });
    return response.json();
  }
  
  // 使用示例
  async example() {
    // 1. 获取分发列表
    const distributions = await this.getDistributions();
    
    // 2. 停用某个分发关系
    await this.updateActiveStatus(12345, false);
    
    // 3. 更换分发关系的配置
    await this.updateConfig(12345, 999);
    
    // 4. 重新启用分发关系
    await this.updateActiveStatus(12345, true);
    
    // 5. 删除分发关系
    await this.deleteById(12345);
  }
}
```

## 📊 React组件增强版

```jsx
import React, { useState, useEffect } from 'react';
import { Table, Button, Select, Input, Space, message, Switch, Modal } from 'antd';

const ConfigDistributionPage = () => {
  const [distributions, setDistributions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({});
  const [configOptions, setConfigOptions] = useState([]);
  
  const distributionManager = new ConfigDistributionManager();
  
  // 加载分发列表
  const loadDistributions = async () => {
    setLoading(true);
    try {
      const data = await distributionManager.getDistributions(filters);
      setDistributions(data);
    } catch (error) {
      message.error('加载失败');
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    loadDistributions();
  }, [filters]);
  
  // 切换激活状态
  const handleToggleActive = async (distributionId, isActive) => {
    try {
      await distributionManager.updateActiveStatus(distributionId, isActive);
      message.success(isActive ? '启用成功' : '停用成功');
      loadDistributions();
    } catch (error) {
      message.error('操作失败');
    }
  };
  
  // 更换配置
  const handleChangeConfig = async (distributionId, newConfigId) => {
    try {
      await distributionManager.updateConfig(distributionId, newConfigId);
      message.success('配置更换成功');
      loadDistributions();
    } catch (error) {
      message.error('配置更换失败');
    }
  };
  
  // 删除分发关系
  const handleDelete = async (distributionId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个分发关系吗？',
      onOk: async () => {
        try {
          await distributionManager.deleteById(distributionId);
          message.success('删除成功');
          loadDistributions();
        } catch (error) {
          message.error('删除失败');
        }
      }
    });
  };
  
  const columns = [
    { title: '目标类型', dataIndex: 'targetType', key: 'targetType' },
    { title: '目标ID', dataIndex: 'targetId', key: 'targetId' },
    { title: '目标名称', dataIndex: 'targetName', key: 'targetName' },
    { title: '配置名称', dataIndex: 'configName', key: 'configName' },
    { title: '分发状态', dataIndex: 'distributionStatus', key: 'distributionStatus' },
    {
      title: '激活状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive, record) => (
        <Switch 
          checked={isActive} 
          onChange={(checked) => handleToggleActive(record.distributionId, checked)}
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            type="primary" 
            size="small"
            onClick={() => showChangeConfigModal(record)}
          >
            更换配置
          </Button>
          <Button 
            danger 
            size="small"
            onClick={() => handleDelete(record.distributionId)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];
  
  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Select
          placeholder="选择目标类型"
          style={{ width: 120 }}
          onChange={(value) => setFilters({...filters, targetType: value})}
          allowClear
        >
          <Select.Option value="USER">用户</Select.Option>
          <Select.Option value="DEVICE">设备</Select.Option>
        </Select>
        
        <Input
          placeholder="搜索关键词"
          style={{ width: 200 }}
          onChange={(e) => setFilters({...filters, keyword: e.target.value})}
        />
      </Space>
      
      <Table
        columns={columns}
        dataSource={distributions}
        loading={loading}
        rowKey="distributionId"
      />
    </div>
  );
};

export default ConfigDistributionPage;
```

## 📝 接口总结

| 接口 | 方法 | 用途 | 状态 |
|------|------|------|------|
| `/logcontrol/config/distribution/{id}/status` | PUT | 更新激活状态 | ✅ 新增 |
| `/logcontrol/config/distribution/{id}/config` | PUT | 更换配置 | ✅ 新增 |
| `/logcontrol/config/distribution/{id}` | DELETE | 删除分发关系 | ✅ 新增 |

## 🎯 使用场景

1. **临时停用分发关系**：使用状态更新接口暂停配置分发
2. **配置升级**：使用配置更新接口将分发关系指向新版本配置
3. **精确删除**：使用ID删除接口精确删除特定的分发关系

## ⚠️ 注意事项

1. **状态更新**：停用分发关系后，目标用户/设备会降级到默认配置
2. **配置验证**：更换配置时会验证新配置是否存在
3. **删除确认**：建议在前端添加删除确认对话框
4. **权限控制**：根据用户权限控制操作按钮的显示
