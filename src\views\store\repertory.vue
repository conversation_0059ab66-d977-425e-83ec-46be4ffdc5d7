<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-07-03 14:06:35
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 14:42:57
 * @Description: 月末库存数据
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="耗材库存" name="first" lazy>
        <Consumable />
      </el-tab-pane>
      <el-tab-pane label="机器库存" name="second" lazy>
        <Machine />
      </el-tab-pane>
      <!--<el-tab-pane label="机器库存" name="third" lazy>-->
      <!--  <admin-panel></admin-panel>-->
      <!--</el-tab-pane>-->
    </el-tabs>
  </div>
</template>

<script>
// import "./components/repertory/dist-admin/admin-panel.iife";
import Consumable from "@/views/store/components/repertory/consumable.vue";
import Machine from "@/views/store/components/repertory/machine.vue";
export default {
  name: "Repertory",
  components: {
    Consumable,
    Machine,
    // adminPanel: () =>
    //   import("./components/repertory/dist-admin/admin-panel.iife"),
  },
  data() {
    return {
      activeName: "first",
    };
  },
};
</script>

<style scoped lang="scss"></style>
