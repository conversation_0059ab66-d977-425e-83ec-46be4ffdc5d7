<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-09 17:29:42
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-01 18:20:34
 * @Description: 应付账单汇总
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:finance:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>开单总金额： {{ totalData?.currAmount || 0 }}</div>
          <div>结算总金额： {{ totalData?.settleAmount || 0 }}</div>
          <div>期末总余额： {{ totalData?.terminalAmount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
      <template #periodAdjust="{ row }">
        <el-input-number
          v-model="row.periodAdjust"
          placeholder="调整金额"
          size="small"
          :controls="false"
          :disabled="!row.isedit"
          style="width: 90px"
          @click="row.isedit = true"
        ></el-input-number>

        <el-link
          v-if="!row.isedit"
          style="margin-left: 10px"
          icon="el-icon-edit"
          :underline="false"
          @click="editAdjust(row)"
        >
          编辑
        </el-link>
        <el-link
          v-if="row.isedit"
          style="margin-left: 10px"
          :underline="false"
          @click="handleAdjust(row)"
        >
          保存
        </el-link>
        <el-link
          v-if="row.isedit"
          style="margin-left: 10px"
          :underline="false"
          type="info"
          @click="handleAdjust()"
        >
          取消
        </el-link>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button @click="handleAdjust(row)">期初调整</el-button>
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { handleExcelExport } from "@/utils/exportExcel";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import {
  payableSummaryListApi,
  payableSummaryStatisticsApi,
  payableSummaryExportApi,
  payableSummaryAdjustApi,
} from "@/api/finance";

export default {
  name: "PaySummary",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "yearMonths",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "month",
          pickerFormat: "yyyy-MM",
          valueFormat: "yyyy-MM",
          minWidth: 100,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "manufacturerCode",
          title: "供应商编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isTable: true,
          formatter: (row) =>
            `${row?.province ?? ""}${row?.city ?? ""}${row?.area ?? ""}`,
          isSearch: true,
          searchSlot: "regionPath",
          minWidth: 160,
        },
        // {
        //   dataIndex: "province",
        //   title: "省",
        //   isTable: true,
        //   minWidth: 80,
        // },
        // {
        //   dataIndex: "city",
        //   title: "市",
        //   isTable: true,
        //   minWidth: 80,
        // },
        // {
        //   dataIndex: "area",
        //   title: "区",
        //   isTable: true,
        //   minWidth: 80,
        // },
        // {
        //   dataIndex: "industryAttr",
        //   title: "行业属性",
        //   isTable: true,
        //   minWidth: 100,
        // },
        {
          dataIndex: "materialAmount",
          title: "耗材应付",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "machineAmount",
          title: "机器应付",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "materialPreAmount",
          title: "耗材预付",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "machinePreAmount",
          title: "机器预付",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "periodAmount",
          title: "期初余额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "periodAdjust",
          title: "期初调整金额",
          isTable: true,
          tableSlot: "periodAdjust",
          width: 200,
        },
        {
          dataIndex: "currAmount",
          title: "开单金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "settleAmount",
          title: "本单结算",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "" + "",
          title: "期末余额",
          isTable: true,
          minWidth: 100,
        },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   tableSlot: "action",
        //   minWidth: 100,
        //   fixed: "right",
        // },
      ],
      tableData: [],
      totalData: {},
      requestParameters: {},
      statLoading: false,
      exportLoading: false,
      editId: null,
    };
  },
  mounted() {
    const currentMonth = new Date().getMonth() + 1;
    // 获取当前年月
    this.queryParam["yearMonths"] = `${new Date().getFullYear()}-${
      currentMonth < 10 ? "0" + currentMonth : currentMonth
    }`;
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      this.requestParameters = requestParameters;
      payableSummaryListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.tableData.map((ele) => {
            ele.isedit = ele.id === this.editId;
          });
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      payableSummaryStatisticsApi(this.requestParameters)
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    editAdjust(row) {
      this.editId = row.id;
      this.$refs.ProTable.refresh();
    },
    handleAdjust(row) {
      if (row) {
        const currentMonth = new Date().getMonth() + 1;
        if (
          row.yearMonths !==
          `${new Date().getFullYear()}-${
            currentMonth < 10 ? "0" + currentMonth : currentMonth
          }`
        ) {
          this.$message.error("只能调整当前月份的期初余额");
          return;
        }
        payableSummaryAdjustApi({
          id: row.id,
          periodAdjust: row.periodAdjust,
        }).then(() => {
          this.$message.success("修改成功");
          this.editId = null;
          this.refresh();
        });
      } else {
        this.editId = null;
        this.refresh();
      }
    },
    // handleAdjust(row) {
    //   const currentMonth = new Date().getMonth() + 1;
    //   if (
    //     row.yearMonths !==
    //     `${new Date().getFullYear()}-${
    //       currentMonth < 10 ? "0" + currentMonth : currentMonth
    //     }`
    //   ) {
    //     this.$message.error("只能调整当前月份的期初余额");
    //     return;
    //   }
    //   this.$prompt("请输入调整期初的金额", "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     closeOnClickModal: false,
    //     inputPattern: /^-?\d+(\.\d+)?$/,
    //     inputErrorMessage: "请输入有效的金额（可包含负数和小数）",
    //     inputValue: row.periodAdjust,
    //     inputType: "number",
    //   }).then(({ value }) => {
    //     const args = {
    //       id: row.id,
    //       periodAdjust: value,
    //     };
    //
    //     payableSummaryAdjustApi(args).then((res) => {
    //       this.$message.success("调整成功");
    //       this.refresh();
    //     });
    //   });
    // },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        this.$message.error(error.message);
      }
    },
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleExport() {
      this.$confirm("此操作将导出应付款汇总数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          payableSummaryExportApi,
          this.requestParameters,
          "应付款汇总数据",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
