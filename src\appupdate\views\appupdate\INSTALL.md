# Android应用版本管理功能 - 安装指南

## 📦 安装步骤

### 1. 文件部署
所有必要的文件已经创建完成，包括：

```
src/
├── views/appupdate/                 # 主功能模块
│   ├── index.vue                   # 主页面
│   ├── components/                 # 子组件
│   │   ├── VersionList.vue         # 版本列表
│   │   ├── PublishDialog.vue       # 发布对话框
│   │   ├── EditDialog.vue          # 编辑对话框
│   │   └── EmergencyDialog.vue     # 紧急操作
│   ├── README.md                   # 功能说明
│   └── INSTALL.md                  # 安装指南
├── api/
│   └── appVersion.js               # API接口
└── router/
    └── index.js                    # 路由配置（已更新）
```

### 2. 依赖检查
确保项目中已安装以下依赖（通常已存在）：

```json
{
  "vue": "^2.6.14",
  "element-ui": "^2.15.12",
  "axios": "^0.27.2",
  "crypto-js": "^4.2.0",
  "moment": "^2.30.1"
}
```

### 3. 后端API对接
确保后端已实现以下API接口：

#### 管理端接口
- `POST /api/admin/app-version/page` - 版本列表分页查询
- `POST /api/admin/app-version/publish` - 发布新版本
- `PUT /api/admin/app-version/{id}` - 更新版本信息
- `POST /api/admin/app-version/{id}/force` - 设置/取消强制更新
- `POST /api/admin/app-version/emergency/{action}` - 紧急操作
- `DELETE /api/admin/app-version/{id}` - 删除版本

#### 客户端接口
- `GET /api/app/update` - 检查应用更新

### 4. 权限配置
在后端权限系统中添加以下权限：

```sql
-- 添加应用更新管理权限
INSERT INTO `b_permission` (`id`, `name`, `code`, `type`, `parent_id`, `path`, `component`, `icon`, `sort_order`, `status`) VALUES
(1001, '应用更新管理', 'app-update', 'MENU', 0, '/appupdate', 'Layout', 'el-icon-mobile-phone', 100, 'ENABLED'),
(1002, '版本列表', 'app-update:version:list', 'BUTTON', 1001, '', '', '', 1, 'ENABLED'),
(1003, '发布版本', 'app-update:version:publish', 'BUTTON', 1001, '', '', '', 2, 'ENABLED'),
(1004, '编辑版本', 'app-update:version:edit', 'BUTTON', 1001, '', '', '', 3, 'ENABLED'),
(1005, '删除版本', 'app-update:version:delete', 'BUTTON', 1001, '', '', '', 4, 'ENABLED'),
(1006, '强制更新', 'app-update:version:force', 'BUTTON', 1001, '', '', '', 5, 'ENABLED'),
(1007, '紧急操作', 'app-update:emergency', 'BUTTON', 1001, '', '', '', 6, 'ENABLED');
```

### 5. 数据库表创建
确保后端已创建以下数据库表：

```sql
-- 应用版本表
CREATE TABLE `app_version` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `version_name` varchar(20) NOT NULL COMMENT '版本名称(如1.3.0)',
  `version_code` int NOT NULL COMMENT '版本号(递增数字)',
  `apk_file_name` varchar(255) NOT NULL COMMENT 'APK文件名',
  `cos_key` varchar(500) NOT NULL COMMENT 'COS存储key',
  `cos_url` varchar(500) NOT NULL COMMENT 'COS访问URL',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_md5` varchar(32) NOT NULL COMMENT '文件MD5校验值',
  `update_log` text COMMENT '更新说明',
  `is_force` tinyint(1) DEFAULT '0' COMMENT '是否强制更新',
  `admin_force` tinyint(1) DEFAULT '0' COMMENT '管理员强制标志',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `download_count` int DEFAULT '0' COMMENT '下载次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint COMMENT '创建人',
  `updated_by` bigint COMMENT '更新人',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_version_name` (`version_name`),
  UNIQUE KEY `uk_version_code` (`version_code`),
  KEY `idx_version_code` (`version_code`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用版本表';

-- 版本下载记录表
CREATE TABLE `app_version_download_log` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `version_id` bigint NOT NULL COMMENT '版本ID',
  `device_id` varchar(100) COMMENT '设备ID',
  `ip_address` varchar(45) COMMENT 'IP地址',
  `user_agent` varchar(500) COMMENT '用户代理',
  `download_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
  `status` varchar(20) DEFAULT 'SUCCESS' COMMENT '下载状态',
  PRIMARY KEY (`id`),
  KEY `idx_version_id` (`version_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_download_time` (`download_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本下载记录表';
```

## 🚀 启动测试

### 1. 启动项目
```bash
npm run serve
# 或
yarn serve
```

### 2. 访问功能
打开浏览器访问：
```
http://localhost:3000/#/appupdate
```

### 3. 功能测试
1. **版本列表**：检查表格数据加载和分页功能
2. **搜索功能**：测试版本名称、类型、状态筛选
3. **发布版本**：测试文件上传和版本发布流程
4. **编辑版本**：测试版本信息修改功能
5. **紧急操作**：测试回退、暂停、恢复功能

## 🔧 配置调整

### 1. API地址配置
如果后端API地址不同，请修改 `public/config.js`：

```javascript
window.config = {
  api: {
    baseURL: "http://your-api-domain:8080/api",
    uploadURL: "http://your-api-domain:8080/api",
  }
};
```

### 2. 文件上传配置
如果需要调整文件上传限制，请修改 `src/api/appVersion.js` 中的 `validateApkFile` 函数。

### 3. 权限控制
如果需要调整权限控制，请在路由配置中添加相应的权限检查。

## 🐛 常见问题

### 1. 页面无法访问
- 检查路由配置是否正确
- 检查用户是否有相应权限
- 检查菜单配置是否正确

### 2. API调用失败
- 检查后端服务是否启动
- 检查API地址配置是否正确
- 检查网络连接是否正常

### 3. 文件上传失败
- 检查腾讯云COS配置是否正确
- 检查文件大小和格式是否符合要求
- 检查上传权限是否足够

## 📞 技术支持

如遇到安装或使用问题，请：
1. 查看浏览器控制台错误信息
2. 检查后端日志
3. 参考README.md中的故障排除部分
4. 联系开发团队

---

**安装完成后，Android应用版本管理功能即可正常使用！**
