<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:52
 * @Description: 设备列表
 -->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增设备
        </el-button>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 500px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>

      <template #brand="slotProps">
        <el-popover
          placement="bottom"
          title="当前设备关联产品树"
          width="400"
          trigger="click"
        >
          <div style="margin: 20px">
            <el-tree
              :data="deviceProductTree"
              :props="{
                children: 'children',
                label: 'name',
              }"
              default-expand-all
            ></el-tree>
          </div>

          <el-button
            slot="reference"
            size="mini"
            @click="getBrand(slotProps.row.id)"
          >
            查看品牌
          </el-button>
        </el-popover>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            详情
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType == 'info'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
        <template #fullIdPath>
          <el-cascader
            ref="ProductIds"
            v-model="form.productIdName"
            filterable
            :disabled="methodType == 'info'"
            :options="options"
            style="width: 100%"
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
              expandTrigger: 'click',
              multiple: false,
            }"
            clearable
            leaf-only
            @change="handleChange"
          ></el-cascader>
        </template>
        <template #picsUrl>
          <ProUpload
            :file-list="form.picsUrl"
            :type="methodType"
            :limit="1"
            @uploadSuccess="(e) => handleUploadSuccess(e, 'picsUrl')"
            @uploadRemove="(e) => handleUploadRemove(e, 'picsUrl')"
          />
          建议尺寸：800*800，最多上传1张。
        </template>
      </ProForm>
    </ProDrawer>
  </div>
</template>
<script>
import {
  deviceListApi,
  deviceAddApi,
  deviceDelApi,
  deviceEditApi,
  productAllApi,
  deviceProductTreeApi,
  deviceProductPathApi,
} from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";

import { isEmpty, cloneDeep } from "lodash";
import { interval } from "rxjs";
import ProUpload from "@/components/ProUpload/index.vue";

export default {
  name: "Device",
  components: { ProUpload },
  mixins: [],
  props: {},
  data() {
    return {
      // 列表
      options: [],
      deviceProductTree: [],
      tableData: [],
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      queryParam: {
        lastIds: [],
      },
      columns: [
        {
          dataIndex: "lastIds",
          title: "品牌名称",
          isSearch: true,
          clearable: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },

        // {
        //   dataIndex: "brand",
        //   title: "产品树",
        //   isTable: true,
        //   tableSlot: "brand",
        // },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "tree",
          title: "产品树",
          isTable: true,
        },
        {
          dataIndex: "serial",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "machine",
          title: "机型",
          isTable: true,
        },
        {
          dataIndex: "fullMachine",
          title: "全称",
          isTable: true,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          formatter: (row) => row.hostType.label,
          optionMth: () => dictTreeByCodeApi(2000),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "name",
        //   title: "机型名称",
        //   isSearch: true,
        //   clearable: true,
        //   span: 4,
        //   valueType: "input",
        // },

        // {
        //   dataIndex: "typeName",
        //   title: "设备名称",
        //   isTable: true,
        // },
        {
          dataIndex: "type",
          title: "设备类型",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          formatter: (row) => row.type.label,
          optionMth: () => dictTreeByCodeApi(800),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "hostType",
        //   title: "主机类型",
        //   isTable: true,
        //   formatter: (row) => row.hostType.label,
        //   isSearch: true,
        //   clearable: true,
        //   valueType: "select",
        //   formSpan: 8,
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(2000),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        {
          dataIndex: "produce",
          title: "生产类型",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          formatter: (row) => row.produce.label,
          optionMth: () => dictTreeByCodeApi(1800),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "color",
          title: "色彩类型",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
          formatter: (row) => row.color.label,
          optionMth: () => dictTreeByCodeApi(1700),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "Actions",
          width: 240,
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],

      //新增
      methodType: "add",
      confirmLoading: false,
      form: {},
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      formcolumns: [
        {
          dataIndex: "productIds",
          isForm: true,
          clearable: true,
          formSlot: "fullIdPath",
          title: "品牌/产品树信息",
          valueType: "select",
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请选择品牌/产品树",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "num",
          isForm: true,
          clearable: true,
          title: "机器编号",
          valueType: "input",
          formSpan: 12,
        },
        {
          clearboth: true,
          dataIndex: "type",
          title: "设备类型",
          isForm: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(800),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择设备类型",
              trigger: "change",
            },
          ],
        },

        {
          dataIndex: "color",
          title: "色彩类型",
          isForm: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(1700),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择色彩类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isForm: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(2000),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择主机类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "produce",
          title: "生产类型",
          isForm: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(1800),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择生产类型",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "scan",
          title: "支持复印/扫描",
          isForm: true,
          valueType: "select",
          formSpan: 8,
          option: [],
          optionMth: () => dictTreeByCodeApi(1900),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择是否支持复印/扫描",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "hostPrinciple",
          isForm: true,
          title: "主机原理",
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "lifespan",
          title: "寿命印张数",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },

        {
          dataIndex: "lifeYear",
          title: "寿命年",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "bwSpeed",
          title: "黑白打印速度",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "clSpeed",
          title: "彩色打印速度",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "size",
          title: "尺寸",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "installationSize",
          title: "安装尺寸",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "weight",
          title: "重量（KG）",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "ratio",
          title: "打印分辨率",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },

        {
          dataIndex: "electric",
          title: "电源（V)",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "power",
          title: "功率（W）",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "normalSize",
          title: "标称尺寸",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "current",
          title: "电流（A)",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "scenario",
          title: "适用场景",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          wordlimit: "500",
          clearable: true,
          formSpan: 24,
        },
        // {
        //   dataIndex: "goods",
        //   title: "商品信息",
        //   isForm: true,
        //   formOtherSlot: "goods",
        //   formSpan: 24,
        // },
        // {
        //   dataIndex: "isSale",
        //   title: "是否上架",
        //   isForm: true,
        //   formSpan: 3,
        //   valueType: "switch",
        //   option: [
        //     {
        //       label: "是",
        //       value: true,
        //     },
        //     {
        //       label: "否",
        //       value: false,
        //     },
        //   ],
        // },
        // {
        //   dataIndex: "intentionAmount",
        //   title: "定金",
        //   isForm: true,
        //   formSpan: 5,
        //   valueType: "input",
        //   inputType: "number",
        //   // prop: [
        //   //   {
        //   //     required: true,
        //   //     message: "请输入定金",
        //   //     trigger: "change",
        //   //   },
        //   // ],
        // },
        // {
        //   dataIndex: "totalAmount",
        //   title: "售价",
        //   isForm: true,
        //   formSpan: 5,
        //   valueType: "input",
        //   inputType: "number",
        //   // prop: [
        //   //   {
        //   //     required: true,
        //   //     message: "请输入售价",
        //   //     trigger: "change",
        //   //   },
        //   // ],
        // },
        // {
        //   dataIndex: "picsUrl",
        //   title: "商品主图",
        //   isForm: true,
        //   formSlot: "picsUrl",
        //   formSpan: 24,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请上传商品主图",
        //       trigger: "change",
        //     },
        //   ],
        // },
        // {
        //   dataIndex: "descriptions",
        //   title: "商品详细信息",
        //   isForm: true,
        //   formOtherSlot: "descriptions",
        //   formSpan: 24,
        // },
      ],
      //字典项
      roleId: null,
      dialogTitleR: "",
      dialogVisibleR: false,
      dialogVisibleU: false,
    };
  },
  mounted() {
    productAllApi().then((res) => {
      this.options = res.data;
      this.$refs.ProTable.refresh();
    });
  },
  methods: {
    getBrand(id) {
      deviceProductTreeApi(id).then((res) => {
        console.log(res);
        this.deviceProductTree = res.data;
      });
    },
    handleSelect(item) {
      this.queryParam.lastIds = [];
      item.map((el) => {
        this.queryParam.lastIds.push(el[el.length - 1]);
      });
    },
    handleChange(item) {
      this.$set(this.form, "productIds", [item[item.length - 1]]);
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = cloneDeep(
        Object.assign(this.queryParam, parameter)
      );
      delete requestParameters.productIdName;
      console.log(requestParameters);
      deviceListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.dialogTitle = "新增设备";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;

      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应新增
    create() {
      const obj = cloneDeep(this.form);
      delete obj.productIdName;
      deviceAddApi(obj)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑";
      this.resetFrom();
      this.form = cloneDeep(row);
      this.form.produce = this.form.produce.value;
      this.form.scan = this.form.scan.value;
      this.form.type = this.form.type.value;
      this.form.hostType = this.form.hostType.value;
      this.form.color = this.form.color.value;
      this.$set(this.form, "productIdName", []);
      let productArr = [];
      const productidArr = [];
      deviceProductPathApi(row.id).then((res) => {
        console.log(res);
        const arr = res.data[0].split("/");
        arr.shift();
        productidArr.push(arr[arr.length - 1]);
        productArr = arr;
      });

      this.methodType = "edit";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
        setTimeout(() => {
          this.$set(this.form, "productIdName", productArr);
          this.$set(this.form, "productIds", productidArr);
          console.log(this.form.productIdName);
          console.log(this.form.productIds);
        }, 300);
      });
    },
    //响应编辑
    update() {
      const obj = cloneDeep(this.form);
      delete obj.productIdName;
      deviceEditApi(obj)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看";
      this.resetFrom();
      this.form = cloneDeep(row);
      this.form.produce = this.form.produce.value;
      this.form.scan = this.form.scan.value;
      this.form.type = this.form.type.value;
      this.form.hostType = this.form.hostType.value;
      this.form.color = this.form.color.value;
      this.$set(this.form, "productIdName", []);
      let productArr = [];
      const productidArr = [];
      deviceProductPathApi(row.id).then((res) => {
        console.log(res);
        const arr = res.data[0].split("/");
        arr.shift();
        productidArr.push(arr[arr.length - 1]);
        productArr = arr;
      });

      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
        setTimeout(() => {
          this.$set(this.form, "productIdName", productArr);
          this.$set(this.form, "productIds", productidArr);
          console.log(this.form.productIdName);
          console.log(this.form.productIds);
        }, 300);
      });
    },
    handleUploadSuccess(result, type) {
      if (!this.form[type]) {
        this.$set(this.form, type, []);
      }
      this.form[type].push(result);
    },
    handleUploadRemove(file, type) {
      const index = this.form[type].findIndex((val) => val.key === file.key);
      if (index === -1) return;
      this.form[type].splice(index, 1);
    },
    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deviceDelApi(data.id).then(() => {
          this.$message.success("删除成功");
          this.localPagination = {
            pageNumber: 1,
            pageSize: 10,
            total: 0,
          };
          this.$nextTick(() => {
            this.$refs.ProTable.refresh();
          });
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
