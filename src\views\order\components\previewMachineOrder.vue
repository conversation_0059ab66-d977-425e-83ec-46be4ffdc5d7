<template>
  <div class="app-container">
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="95%"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <el-steps :active="active" finish-status="success" class="steps-box">
        <el-step title="买家下单"></el-step>
        <el-step title="买家付款"></el-step>
        <el-step title="发货"></el-step>
        <el-step title="买家确认收货"></el-step>
      </el-steps>
      <!-- 买家下单 -->
      <div v-if="orderInfo" ref="dialogContent" class="order-fix">
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="订单信息"
            :column="4"
            border
          >
            <el-descriptions-item label="订单状态">
              {{ getOrderStatusChinese(orderDetailInfo?.orderStatus) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单编号">
              {{ orderDetailInfo.orderNum }}
            </el-descriptions-item>
            <el-descriptions-item label="配送方式">
              {{ orderDetailInfo?.logisticsProvider?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="关联客户">
              {{ orderInfo.companyName }}
            </el-descriptions-item>
            <el-descriptions-item label="下单用户">
              {{ orderInfo?.buyerName }}
            </el-descriptions-item>
            <el-descriptions-item label="客户等级">
              {{ orderDetailInfo?.customerLevel?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="下单手机号">
              {{ orderDetailInfo?.consigneePhone }}
            </el-descriptions-item>
            <el-descriptions-item label="支付方式">
              {{ orderDetailInfo?.payMode?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="支付金额（元）">
              {{ orderDetailInfo?.actualAmount }}
            </el-descriptions-item>
            <el-descriptions-item label="收货地址" :span="3">
              {{ orderDetailInfo?.consigneeFullAddress }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderDetailInfo?.orderStatus === 'WAIT_PAY'"
              label="关闭订单"
            >
              <el-button type="primary" size="mini" @click="showGbddDialog()">
                关闭订单
              </el-button>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="order-border-box" style="margin-top: 20px">
          <el-descriptions
            class="margin-top"
            title="服务选项"
            :column="4"
            border
          >
            <el-descriptions-item label="安装费用">
              {{ orderDetailInfo?.installAmount }}
            </el-descriptions-item>
            <el-descriptions-item label="预计安装日期">
              {{ orderDetailInfo?.installDate }}
            </el-descriptions-item>
            <el-descriptions-item label="是否包邮">
              {{ orderDetailInfo?.freeShipping ? "是" : "否" }}
            </el-descriptions-item>
            <el-descriptions-item label="服务类型">
              {{ orderDetailInfo?.treatyType?.label }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="
                orderDetailInfo?.treatyType?.value === '1202' ||
                orderDetailInfo?.treatyType?.value === '1230'
              "
              label="服务截止时间"
            >
              {{ orderDetailInfo?.contractDate }}
            </el-descriptions-item>
            <el-descriptions-item label="质保维修次数">
              {{ orderDetailInfo?.guaranteeRepairCount }}
            </el-descriptions-item>
            <el-descriptions-item label="质保印量">
              {{ orderDetailInfo?.guaranteeCount }}
            </el-descriptions-item>
            <el-descriptions-item label="质保截止日期">
              {{ orderDetailInfo?.guaranteeDate }}
            </el-descriptions-item>
            <el-descriptions-item label="结算方式">
              {{ orderDetailInfo?.settleMethod?.label }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderDetailInfo.settleMethod?.value === 'INSTALLMENT'"
              label="分期期数"
            >
              {{ orderDetailInfo?.installmentNum }}
              <el-button
                type="primary"
                size="mini"
                class="m-l-20"
                @click="dialogXfdmVisible = true"
              >
                分期明细
              </el-button>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 商品信息 -->
        <div class="m-t-8">
          <p class="tit-box m-b-12">机器信息</p>
          <ProTable
            ref="ProSPXXTable"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="columns"
            :show-pagination="false"
            :show-loading="false"
            :data="orderDetailInfo.tradeOrderDetailList || []"
            :show-setting="false"
            :show-search="false"
            :show-table-operator="false"
            sticky
            :height="200"
          >
            <template #storageArticle="{ row }">
              {{ row.storageArticle?.minUnit }}
            </template>
          </ProTable>
          <div class="text-content">
            <p class="text-p">
              <label class="p-label">订单总金额（元）：</label>
              <span class="p-content">
                {{ orderDetailInfo.fullAmount }}
              </span>
            </p>

            <p class="text-p">
              <label class="p-label">会员减免（元）：</label>
              <span class="p-content">
                -{{ orderDetailInfo.discountAmount }}
              </span>
            </p>

            <p class="text-p">
              <label class="p-label">订单运费（元）：</label>
              <span class="p-content">
                {{ orderDetailInfo.shippingFee }}
              </span>
            </p>
            <p class="text-p">
              <label class="p-label">实收款（元）：</label>
              <span class="p-content">
                {{ orderDetailInfo.actualAmount }}
              </span>
            </p>
            <p v-if="orderDetailInfo.buyerRemark" class="text-p m-b-8">
              <label class="p-label">订单备注：</label>
              <span class="p-content">{{ orderDetailInfo.buyerRemark }}</span>
            </p>
          </div>
        </div>

        <!-- 交易明细 -->
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="交易明细"
            :column="3"
            border
          >
            <el-descriptions-item label="订单来源">
              {{ getOrderStatusChineses(orderDetailInfo.orderSource) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单创建时间">
              {{ orderDetailInfo.createdAt }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderDetailInfo.payTime"
              label="订单支付时间"
            >
              {{ orderDetailInfo.payTime }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderInfo.deliveryTime"
              label="订单发货时间"
            >
              {{ orderInfo.deliveryTime }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderDetailInfo.finishTime"
              label="订单确认收货时间"
            >
              {{ orderDetailInfo.finishTime }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 物流信息 -->
        <div
          v-if="tradeInfo?.length > 0"
          class="m-t-8 box-box"
          style="clear: both"
        >
          <p class="tit-box m-b-12">物流信息</p>
          <div style="overflow: hidden">
            <div
              v-for="(item, index) in tradeInfo"
              :key="index"
              style="float: left; margin-right: 20px"
              @click="changeTrade(item)"
            >
              <div
                :class="
                  tradeInfoDetail.waybillNumber == item.waybillNumber
                    ? 'trade active'
                    : 'trade'
                "
              >
                <text class="iconfont iconbaoguo"></text>
                <div class="info">
                  <div>{{ item.packageName }}</div>
                  <div>共{{ item.expectedNumber }}件</div>
                </div>
              </div>
            </div>
          </div>
          <div class="tradedetail">
            <div>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'jdl'"
                class="iconfont iconsr_jingdong"
                style="color: red; font-size: 50rpx"
              ></text>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'iss'"
                class="iconfont iconshansonghuise"
                style="color: #ee822f; font-size: 50rpx"
              ></text>
              <text
                v-if="tradeInfoDetail?.logisticsProvider?.value === 'self'"
                class="iconfont iconziti"
                style="color: #ee822f; font-size: 50rpx"
              ></text>

              <text style="margin-left: 20rpx"
                >{{ tradeInfoDetail?.logisticsProvider?.label }}:{{
                  tradeInfoDetail?.waybillNumber
                }}</text
              >
            </div>
            <div style="margin-top: 30px">
              <el-timeline>
                <el-timeline-item
                  v-for="item in tradeInfoDetail.traces"
                  :key="item.id"
                  :timestamp="item.providerStatus + '    ' + item.operatedAt"
                >
                  {{ item.operationRemark }}
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </div>
      </div>
    </ProDrawer>
    <!-- 关闭订单弹框 -->
    <ProDialog
      :value="dialogGbddVisible"
      :title="'操作提示'"
      width="600px"
      :top="'10%'"
      :no-footer="false"
      @ok="handleGbddDialogOk"
      @cancel="dialogGbddVisible = false"
    >
      <div class="m-b-12">关闭后，此笔订单将失效，确定关闭？</div>
      <el-form ref="gbddFormRef" :model="gbddForm" label-width="50px">
        <el-form-item
          prop="closeReason"
          label="说明"
          :rules="[{ required: true, message: '请填写说明', trigger: 'blur' }]"
        >
          <el-input v-model="gbddForm.closeReason" type="textarea"></el-input>
        </el-form-item>
      </el-form>
    </ProDialog>
    <!-- 分期明细 -->
    <ProDialog
      :value="dialogXfdmVisible"
      :title="'分期明细'"
      :no-footer="true"
      @cancel="dialogXfdmVisible = false"
    >
      <ProTable
        :show-loading="false"
        :show-search="false"
        :show-pagination="false"
        :show-setting="false"
        :show-index="false"
        :height="400"
        :columns="phaseColumns"
        :data="phaseTableData"
      ></ProTable>
    </ProDialog>
  </div>
</template>

<script>
import {
  operatorTradeOrderCloseApi,
  operatorTradeOrderDetailApi,
  OrderTracesApi,
} from "@/api/operator";

export default {
  name: "PreviewMachineOrder",
  data() {
    return {
      dialogVisible: false,
      dialogTitle: "",
      active: 0,
      orderInfo: null,
      orderDetailInfo: {},
      tradeInfoDetail: {},
      columns: [
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          minWidth: 100,
        },
        {
          dataIndex: "itemName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 100,
        },
        {
          dataIndex: "fullAmount",
          title: "机器总价（元）",
          isTable: true,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "定金（元）",
          isTable: true,
        },
        {
          dataIndex: "discountAmount",
          title: "会员减免",
          isTable: true,
        },
        {
          dataIndex: "payAmount",
          title: "小计（元）",
          isTable: true,
        },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   width: 100,
        //   tableSlot: "action",
        // },
      ],
      tradeInfo: [],
      //   关闭订单
      dialogGbddVisible: false,
      gbddForm: {
        closeReason: null,
      },
      // 分期详情弹窗
      dialogXfdmVisible: false,
      phaseColumns: [
        {
          dataIndex: "installmentIndex",
          title: "期数",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "应付金额",
          isTable: true,
        },
        {
          dataIndex: "status",
          title: "付款状态",
          isTable: true,
          formatter: (row) => (row.status ? "已支付" : "待支付"),
        },
        {
          dataIndex: "payDate",
          title: "应付时间",
          isTable: true,
        },
        {
          dataIndex: "actualPayDate",
          title: "实付时间",
          isTable: true,
        },
      ],
      phaseTableData: [],
    };
  },
  methods: {
    show(row) {
      this.orderInfo = null;
      this.orderDetailInfo = null;
      this.phaseTableData = [];
      this.dialogTitle = "查看 - " + row.orderNum;
      this.dialogVisible = true;
      operatorTradeOrderDetailApi(row.orderNum).then((res) => {
        this.orderInfo = res.data;
        this.orderDetailInfo = res.data.tradeOrder;
        this.phaseTableData =
          this.orderDetailInfo?.tradeOrderInstallments || [];
      });
      OrderTracesApi(row.orderNum).then((res) => {
        this.tradeInfo = res.data;
        this.tradeInfoDetail = this.tradeInfo[0];
      });
      this.dialogVisible = true;
    },
    changeTrade(data) {
      this.tradeInfoDetail = data;
    },
    /**
     * 显示关闭订单弹框
     */
    showGbddDialog() {
      this.dialogGbddVisible = true;
      this.gbddForm = this.$options.data().gbddForm;
    },
    handleGbddDialogOk() {
      this.$refs["gbddFormRef"].validate((valid) => {
        if (valid) {
          this.dialogGbddVisible = false;
          operatorTradeOrderCloseApi({
            ...this.gbddForm,
            tradeOrderNum: this.orderDetailInfo.orderNum,
          })
            .then((res) => {
              this.$message.success("关闭订单成功");
              this.$emit("refresh");
            })
            .finally((_) => {
              this.dialogVisible = false;
            });
        } else {
          return false;
        }
      });
    },
    getOrderStatusChinese(orderStatus) {
      let value = "";
      switch (orderStatus) {
        case "CLOSED":
          value = "订单关闭";
          this.active = null;
          break;
        case "PAID":
          value = "已支付";
          this.active = 2;
          break;
        case "SUCCESS":
          value = "交易成功";
          this.active = 4;
          break;
        case "WAIT_DELIVER":
          value = "待发货";
          this.active = 2;
          break;
        case "WAIT_PAY":
          value = "待支付";
          this.active = 1;
          break;
        case "WAIT_RECEIVE":
          value = "待收货";
          this.active = 3;
          break;
      }
      return value;
    },

    getOrderStatusChineses(orderStatus) {
      let value = "";
      switch (orderStatus) {
        case "ITEM":
          value = "商品直接下单";
          break;
        case "CART":
          value = "购物车下单";
          break;
        case "SELLER_CREATE":
          value = "卖家代客下单";
          break;
      }
      return value;
    },
  },
};
</script>

<style scoped lang="scss">
.steps-box {
  position: relative;
  width: 80%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  z-index: 2;
}
.order-fix {
  margin-left: 20px;
  font-size: 14px;

  .red {
    color: #d14b50;
  }

  .text-content {
    display: flex;
    justify-content: space-between;
    .text-p {
      //&.right {
      //  position: relative;
      //  left: 85%;
      //  top: 0;
      //}

      color: #606266;

      .p-label {
        color: #606266;
        font-weight: 700;
      }

      //margin-top: 15px;
      margin: 30px 0;
    }
  }

  .content-fixed {
    display: flex;
    justify-content: space-between;

    .text-p {
      flex: 1;
      display: flex;
    }
  }

  .btn-p {
    margin-top: 15px;

    .el-button {
      padding: 8px 29px;
    }
  }

  .order-border-box {
    border: dashed 1px #ccc;
    padding: 10px;
  }

  .title-p {
    background: #d9d9d9;
    color: #232323;
    padding: 5px;
  }
}

.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
.box-box {
  .tradedetail {
    width: 100%;
    height: 100%;
    clear: both;
    margin-top: 20px;
  }

  .trade {
    width: 140px;
    padding: 10px 0;
    text-align: center;
    border: 1px solid #555555;
    border-radius: 20px;
    cursor: pointer;
  }

  .info {
    display: inline-block;

    text {
      display: block;
      text-align: left;
    }
  }

  .iconbaoguo {
    font-size: 40px;
  }

  .trade.active {
    border: 1px solid #ee822f;
    color: #ee822f;
  }
}

.tit-boxs {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px 7px;
  font-size: 16px;
  font-weight: 800;

  // &::before {
  //   content: "";
  //   width: 5px;
  //   height: 20px;
  //   background: #409eff;
  //   display: inline-block;
  //   position: absolute;
  //   left: -1px;
  //   top: 4px;
  // }
}
</style>
