<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-06-30 14:40:25
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:48
 * @Description: 入库明细
 -->
<template>
  <div>
    <!-- 新增编辑弹窗 -->
    <ProDrawer
      :value="showDrawer"
      :title="drawerTitle"
      size="85%"
      :no-footer="true"
      @cancel="handleCloseDrawer"
    >
      <ProForm
        ref="editForm"
        :form-param="editForm"
        :form-list="columns"
        :confirm-loading="editFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="editType"
      >
        <template #shopWaybill>
          <el-button type="text" size="mini" @click="handleOpenDetail">
            {{ editForm?.shopWaybill }}
          </el-button>
        </template>
        <template #inType>
          {{ editForm?.inType?.label }}
        </template>
        <template #goodsDataList>
          <div class="title-box">入库货品信息</div>
          <ProTable
            ref="ProTable1"
            row-key="id"
            :data="goodTableData"
            :columns="goodColumns"
            show-index
            :show-search="false"
            :show-setting="false"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            height="65vh"
            :show-loading="false"
          >
            <template #articleImages="{ row }">
              <el-image
                v-if="row.articleImages && row.articleImages.length !== 0"
                style="width: 100px; height: 100px"
                :src="row?.articleImages?.[0].url"
              ></el-image>
              <div v-else>暂无</div>
            </template>
            <template #action="{ row }">
              <div class="fixed-width">
                <el-button
                  v-if="row.auditInWarehouseNumber > 0"
                  size="mini"
                  type="primary"
                  icon="none"
                  @click="handleRkLook(row)"
                >
                  入库明细
                </el-button>
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 入库明细 -->
    <ProDialog
      :value="dialogVisible"
      title="入库明细"
      width="400px"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <div style="height: 400px; overflow-y: scroll">
        <el-timeline>
          <el-timeline-item v-for="(item, index) in detailData" :key="index">
            <el-card>
              <div>入库数量：{{ item?.number }}</div>
              <div>批次号：{{ item?.batchCode }}</div>
              <div>入库时间：{{ item?.time }}</div>
              <div>操作人：{{ item?.operatorName }}</div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </ProDialog>
    <!-- 入库单关联采购单明细 -->
    <ProcureWaybill ref="procureWaybill" />
  </div>
</template>

<script>
import ProcureWaybill from "@/views/store/components/procureWaybill.vue";
import { getInboundDetailApi, warehouseListApi } from "@/api/store";
import { goodsLook } from "@/api/manufacturer";

export default {
  name: "WarehousingDetail",
  components: { ProcureWaybill },
  data() {
    return {
      showDrawer: false,
      drawerTitle: "入库单明细",
      editType: "info",
      editForm: {},
      columns: [
        {
          dataIndex: "shopWaybill",
          title: "关联单号",
          isForm: true,
          formSlot: "shopWaybill",
          formSpan: 8,
          // fun: {
          //   click() {
          //     if (that.editForm.inType === "工程师退料入库") {
          //       that.getEngineerInfo(that.editForm.shopWaybill);
          //     } else if (
          //       that.editForm.inType === "商城退货入库" ||
          //       that.editForm.inType === "退回仓库入库"
          //     ) {
          //       that.$refs.returnWaybill.show(that.editForm.shopWaybill);
          //     } else if (that.editForm.inType === "采购入库") {
          //       that.$refs.procureWaybill.show(that.editForm.shopWaybill);
          //     } else if (that.editForm.inType === "维修入库") {
          //       that.$refs.repairWaybill.show(that.editForm.shopWaybill);
          //     } else if (that.editForm.inType === "拆机入库") {
          //       that.$refs.splitWaybill.show(that.editForm.shopWaybill);
          //     } else {
          //       this.$message.warning("暂不支持该类型入库单关联信息查看");
          //     }
          //   },
          // },
        },
        {
          dataIndex: "warehouseId",
          title: "归属仓库",
          isForm: true,
          valueType: "select",
          option: [],
          optionMth: () => warehouseListApi({ status: 1401 }),
          formSpan: 8,
          optionskey: {
            label: "name",
            value: "id",
          },
        },
        {
          dataIndex: "inType",
          title: "入库类型",
          isForm: true,
          formSlot: "inType",
          formSpan: 8,
        },
        // {
        //   dataIndex: "mainWaybill",
        //   title: "关联物流主单号",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 12,
        // },
        //
        // {
        //   dataIndex: "secondlyWaybill",
        //   title: "关联物流子单号",
        //   isForm: true,
        //   formSlot: "text",
        //   formSpan: 12,
        // },
        // {
        //   dataIndex: "reverseOrderId",
        //   title: "关联逆向单号",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 12,
        // },
        {
          dataIndex: "goodsDataList",
          title: "入库货品信息",
          isForm: true,
          formOtherSlot: "goodsDataList",
        },
        {
          dataIndex: "remarks",
          title: "备注",
          isForm: true,
          valueType: "text",
          width: 350,
        },
      ],
      editFormLoading: false,
      showCheckBtn: false,
      goodTableData: [],
      goodColumns: [
        {
          dataIndex: "name",
          title: "物品名称",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "articleImages",
          title: "物品图片",
          isTable: true,
          width: 120,
          tableSlot: "articleImages",
        },
        {
          dataIndex: "code",
          title: "物品编号",
          isTable: true,
          minWidth: 160,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "inWarehouseNumber",
          title: "应入库量",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "auditInWarehouseNumber",
          title: "已入库量",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          minWidth: 100,
          // tableSlot: "price",
        },
        {
          dataIndex: "sumWarehouseNumber",
          title: "库存数量",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "batchCode",
          title: "批次号",
          minWidth: 120,
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 100,
          tableSlot: "action",
        },
      ],
      // 入库明细
      dialogVisible: false,
      detailData: [],
    };
  },
  methods: {
    async show(row) {
      this.editType = "info";
      this.editForm = await this.getDetails(row.id);
      // this.editForm.inType = row.inType.label;
      this.goodTableData = this.editForm.inWarehouseGoodsList;
      this.showDrawer = true;
    },
    async getDetails(id) {
      try {
        this.editFormLoading = true;
        const result = await getInboundDetailApi(id);
        if (result.code === 200 && result.data) {
          return result.data;
        }
      } finally {
        this.editFormLoading = false;
      }
    },
    handleCloseDrawer() {
      this.showDrawer = false;
    },
    handleOpenDetail() {
      if (!this.editForm.shopWaybill) {
        return;
      }
      this.$refs.procureWaybill.show(this.editForm.shopWaybill);
    },
    handleRkLook(row) {
      goodsLook(row.id).then((res) => {
        this.dialogVisible = true;
        this.detailData = res.data;
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
