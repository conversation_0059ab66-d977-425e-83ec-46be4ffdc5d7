<!--
 * @Description: 角色管理
 * @Autor: shh
 * @Date: 2022-11-16 16:42:14
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-10-11 14:15:46
-->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :height="520"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #machine="slotProps">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in slotProps.row.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <el-button slot="reference" size="mini">适用机型</el-button>
        </el-popover>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleChange"
        ></el-cascader>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleInfo(slotProps.row)"
          >
            查看
          </el-button>
          <el-button
            v-if="slotProps.row.status?.value == 'in_appeal'"
            type="danger"
            size="mini"
            icon="el-icon-circle-close"
            @click="cancelAppeal(slotProps.row.id)"
          >
            关闭申诉单
          </el-button>
        </span>
      </template>
    </ProTable>

    <!--  详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="70%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <div class="dialog-content-box">
        <div class="card-box">
          <el-descriptions :column="1">
            <el-descriptions-item label="申诉单编号">{{
              form.code
            }}</el-descriptions-item>
            <el-descriptions-item label="申诉状态">{{
              form.status?.label
            }}</el-descriptions-item>
            <el-descriptions-item label="店铺名称">{{
              form.customerName
            }}</el-descriptions-item>
            <el-descriptions-item label="报修设备组">{{
              form.deviceGroup?.label
            }}</el-descriptions-item>
            <el-descriptions-item label="品牌/机型">
              {{ form.productInfo }}
            </el-descriptions-item>

            <el-descriptions-item label="关联工单号">{{
              form.workOrderCode
            }}</el-descriptions-item>
            <el-descriptions-item label="申诉理由">{{
              form.reason
            }}</el-descriptions-item>
            <!-- <el-descriptions-item label="故障照片">
              <div style="display: flex; width: 100%; flex-wrap: wrap">
                <el-image
                  v-for="(item, index) in form.excPics"
                  :key="index"
                  class="imgs"
                  :src="item.url"
                  alt=""
                  :preview-src-list="[item.url]"
                >
                </el-image>
              </div>
            </el-descriptions-item> -->
            <el-descriptions-item label="申诉发起时间">{{
              form.createdAt
            }}</el-descriptions-item>
            <el-descriptions-item
              v-if="form.appealEndTime"
              label="申诉结束时间"
              >{{ form.appealEndTime }}</el-descriptions-item
            >

            <el-descriptions-item label="接单工程师">{{
              form.engineer?.name
            }}</el-descriptions-item>
            <el-descriptions-item label="相关图片">
              <div>
                <el-image
                  v-for="(item, index) in form.picUrls"
                  :key="index"
                  style="width: 100px; height: 100px; margin-right: 10px"
                  :src="item.url"
                  :preview-src-list="[item.url]"
                >
                </el-image>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="btn-box">
          <div
            v-if="form.status?.value == 'in_appeal'"
            class="cancel-button"
            @click="cancelAppeal(form.id)"
          >
            关闭申诉单
          </div>
        </div>
      </div>
    </ProDrawer>
  </div>
</template>
<script>
import {
  roleListApi,
  roleAddApi,
  roleDelApi,
  roleEditApi,
  roleMemberAddApi,
  menuListApi,
  roleLimitApi,
  roleLimitGetApi,
  getTerminals,
} from "@/api/user";

import { isEmpty, cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
import { productAllApi } from "@/api/dispose";
import { filterName, getAllParentArr } from "@/utils";
import { appealByPageApi, cancelAppealApi } from "@/api/repair";
import { MessageBox } from "element-ui";

export default {
  name: "Appeal",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      options: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {},
      columns: [
        {
          dataIndex: "productIds",
          title: "机型",
          isTable: false,
          isSearch: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
        {
          dataIndex: "code",
          title: "申诉单编号",
          isTable: true,
          isSearch: true,
          span: 4,
          width: 200,
          valueType: "input",
          clearable: true,
        },
        {
          dataIndex: "workOrderCode",
          title: "工单编号",
          isTable: true,
          isSearch: true,
          span: 4,
          width: 200,
          valueType: "input",
          clearable: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          clearable: true,
        },

        {
          dataIndex: "deviceGroup",
          title: "报修设备组",
          formatter: (row) => row.deviceGroup.label,
          isTable: true,
        },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isTable: true,
          // searchSlot: "fullIdPath",
          // tableSlot: "machine",
        },
        {
          dataIndex: "status",
          title: "申诉状态",
          formatter: (row) => row.status.label,
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [
            { label: "申诉中", value: "in_appeal" },
            { label: "已完成", value: "completed" },
            { label: "已关闭", value: "close" },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
          multiple: false,
          // collapseTags: true,
          clearable: true,
        },
        {
          dataIndex: "createdAt",
          title: "发起时间",
          isTable: true,
        },

        {
          dataIndex: "Actions",
          width: 240,
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],

      //新增
      methodType: "add",
      confirmLoading: false,

      form: {},
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      formcolumns: [
        {
          dataIndex: "name",
          isForm: true,
          title: "品牌/机型",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入名称",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "code",
          isForm: true,
          title: "基础上门费",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入路由",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "code",
          isForm: true,
          title: "普通维修价",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入路由",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "code",
          isForm: true,
          title: "客户端客户维修折扣",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入路由",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "code",
          isForm: true,
          title: "VIP客户维修折扣",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入路由",
              trigger: "change",
            },
          ],
        },
      ],
      productIdName: "",
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    this.$refs.ProTable.refresh();
    this.init();
  },
  methods: {
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
      dictTreeByCodeApi(2100).then((res) => {
        this.goodsTypeOptions = res.data;
      });
    },

    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(
        { ...this.queryParam },
        parameter
      );
      appealByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.code;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.methodType = "info";
      this.dialogVisible = true;
      // this.$nextTick((e) => {
      //   this.$refs["proform"].resetFormParam();
      // });
    },

    handleChange(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
    // 关闭申诉单
    cancelAppeal(id) {
      MessageBox.confirm("确定要取消该申诉单吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        cancelAppealApi(id).then(() => {
          this.$message.success("取消成功");
          this.$refs.ProTable.refresh();
        });
      });
    },
  },
};
</script>
<style>
.el-checkbox {
  line-height: 40px;
}

.el-collapse {
  border: none;
}

.el-collapse-item__header {
  border: none;
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-item__content {
  padding: 0;
}

.el-collapse-item:last-child {
  margin: auto;
}
</style>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tree-li {
  list-style: none;
  margin: 0;

  li {
    list-style: none;
    display: inline-block;
    margin-left: 20px;
  }
}
.card-box {
  margin: 10px 0;
  padding: 0 20px;
  .imgs {
    height: 120px;
    margin: 10px;
    max-width: calc((100% - 80px) / 4);
  }
  .imgs1 {
    height: 120px;
    width: 120px;
    float: left;
  }
}
.btn-box {
  position: fixed;
  bottom: 0;
  width: 70%;
  background: #fff;
  right: 0;
  padding: 20px;
}
</style>
