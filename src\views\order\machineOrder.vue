<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="机器销售单" name="first">
        <MachineSale />
      </el-tab-pane>
      <el-tab-pane label="销售明细" name="second">
        <MachineOrderDetail />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MachineSale from "@/views/order/components/machineSale.vue";
import MachineOrderDetail from "@/views/order/components/machineOrderDetail.vue";
export default {
  name: "MachineOrder",
  components: { MachineSale, MachineOrderDetail },
  data() {
    return {
      activeName: "first",
    };
  },
};
</script>

<style scoped lang="scss"></style>
