<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-07-23 11:27:58
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 11:28:57
 * @Description: 维修工单收费标准
 -->

<template>
  <div class="app-container">
    <div class="edit">
      <WangeEditor ref="WangeEditorRef" :content="introduce" :height="600" />
    </div>
    <div>
      <el-button
        type="primary"
        size="small"
        style="margin-top: 20px"
        @click="handleSubmit"
      >
        保存收费标准
      </el-button>
    </div>
  </div>
</template>

<script>
import WangeEditor from "@/components/ProWangeEditor/index.vue";
export default {
  name: "Fee",
  components: {
    WangeEditor,
  },
  props: {
    introduce: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      content: "",
      platformIntroduce: "",
    };
  },
  watch: {
    introduce: {
      handler(val) {
        this.$refs.WangeEditorRef?.echo(val);
      },
    },
  },
  methods: {
    handleSubmit() {
      const result = this.$refs.WangeEditorRef.getContent();
      if (!this.id) {
        this.$message.error("请先保存收款配置信息");
        return;
      }
      this.$emit("save", { platformIntroduce: result });
    },
  },
};
</script>

<style scoped lang="scss">
.edit {
  text-align: left;
}
</style>
