<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 日志详情组件
-->
<template>
  <el-dialog
    title="日志详情"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    :modal="false"
  >
    <div v-loading="loading" class="log-detail">
      <el-descriptions :column="2" border :label-style="{ width: '120px' }" v-if="logData">
        <el-descriptions-item label="日志ID">
          {{ logData.id }}
        </el-descriptions-item>
        <el-descriptions-item label="日志级别">
          <el-tag :type="getLogLevelType(logData.level)">
            {{ logData.level }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="日志类型">
          <el-tag :type="getLogTypeType(logData.logType)" size="mini">
            {{ logData.logType }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="标签">
          {{ logData.tag }}
        </el-descriptions-item>
        <el-descriptions-item label="设备ID">
          {{ logData.deviceId }}
        </el-descriptions-item>
        <el-descriptions-item label="用户编码">
          {{ logData.userCode }}
        </el-descriptions-item>
        <el-descriptions-item label="用户姓名">
          {{ logData.userName }}
        </el-descriptions-item>
        <el-descriptions-item label="应用版本">
          {{ logData.appVersion }}
        </el-descriptions-item>
        <el-descriptions-item label="时间戳" :span="2">
          {{ logData.timestamp }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">
          {{ logData.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="日志内容" :span="2">
          <div class="log-message">
            {{ logData.message }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="扩展数据" :span="2" v-if="logData.extraData">
          <pre class="log-details">{{ formatExtraData(logData.extraData) }}</pre>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="copyLogContent">复制内容</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'LogDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    logData: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    getLogLevelType(level) {
      const types = {
        'DEBUG': 'info',
        'INFO': 'success',
        'WARN': 'warning',
        'ERROR': 'danger'
      }
      return types[level] || ''
    },

    getLogTypeType(logType) {
      const types = {
        'LOCATION': 'primary',
        'BUSINESS': 'success',
        'CRASH': 'danger',
        'PERFORMANCE': 'warning'
      }
      return types[logType] || ''
    },

    formatExtraData(extraData) {
      if (typeof extraData === 'string') {
        try {
          return JSON.stringify(JSON.parse(extraData), null, 2)
        } catch {
          return extraData
        }
      }
      return JSON.stringify(extraData, null, 2)
    },
    
    handleClose() {
      this.dialogVisible = false
    },
    
    copyLogContent() {
      if (!this.logData) return

      const content = `
日志ID: ${this.logData.id}
日志级别: ${this.logData.level}
日志类型: ${this.logData.logType}
标签: ${this.logData.tag}
设备ID: ${this.logData.deviceId}
用户编码: ${this.logData.userCode}
用户姓名: ${this.logData.userName}
应用版本: ${this.logData.appVersion}
时间戳: ${this.logData.timestamp}
创建时间: ${this.logData.createdAt}
日志内容: ${this.logData.message}
${this.logData.extraData ? '扩展数据: ' + this.formatExtraData(this.logData.extraData) : ''}
      `.trim()

      navigator.clipboard.writeText(content).then(() => {
        this.$message.success('内容已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.log-detail {
  .log-message {
    word-break: break-all;
    white-space: pre-wrap;
  }

  .log-details {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
  }

  // 确保标题宽度一致
  :deep(.el-descriptions__label) {
    width: 120px !important;
    min-width: 120px !important;
    text-align: right;
    padding-right: 12px;
    font-weight: 500;
  }

  // 确保内容区域对齐
  :deep(.el-descriptions__content) {
    word-break: break-all;
  }
}
</style>
