<!--
 * @Description: 部门管理
 * @Autor: shh
 * @Date: 2022-11-16 16:42:14
 * @LastEditors: shanhaihong <EMAIL>
 * @LastEditTime: 2024-01-09 11:04:00
-->
<template>
  <div class=" ">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      show-selection
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="400"
      default-expand-all
      :query-param="queryParam"
      :show-index="false"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
    </ProTable>
  </div>
</template>
<script>
import { SeriesListApi } from "@/api/dispose";
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";

export default {
  name: "SeriesList",
  data() {
    return {
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      queryParam: {
        fullIdPath: null,
        name: null,
      },
      columns: [
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },

        {
          dataIndex: "serial",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "productIds",
          title: "品牌/系列",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "name",
          title: "系列名称",
          isSearch: true,
          valueType: "input",
        },
      ],
    };
  },

  created() {
    // this.init();
  },
  mounted() {},
  methods: {
    init() {
      this.queryParam = {
        fullIdPath: null,
        name: null,
      };

      this.localPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.$nextTick(() => {
        this.$refs.ProTable.refresh();
      });
      // productThirdApi().then((res) => {
      //   this.options = res.data;
      //
      // });
    },
    handleSelect(item) {
      this.form.parentId = item[item.length - 1];
      // this.fullIdPathIdName = item
    },
    /**
     * 加载列表数据
     * @param parameter
     */
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      SeriesListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    handleSelectionChange(row) {
      this.$emit("choose", row);
    },
  },
};
</script>
