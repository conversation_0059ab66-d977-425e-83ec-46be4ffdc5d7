<template>
  <div class="ladder-prices" @click.stop>
    <div class="title-box" style="margin-top: 0">赠送服务</div>
    <el-col :span="24">
      <ProTable
        ref="ProTable"
        :show-pagination="false"
        :show-search="false"
        :show-setting="false"
        :show-loading="false"
        :columns="columns"
        :height="250"
        :data="infoData.customerContractGives"
      >
        <template #btn>
          <el-button
            v-if="editType !== 'info'"
            type="success"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >
            新增
          </el-button>
        </template>
        <template #giveType="{ row }">
          <el-select
            v-model="row.giveType"
            :disabled="editType === 'info'"
            size="small"
          >
            <el-option
              v-for="item in giveTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </template>
        <template #articleName="{ row, index }">
          <div v-if="row.giveType === 'PART'">
            <el-button
              v-if="!row.articleName"
              :disabled="editType === 'info'"
              type="text"
              @click="checkSku(row, index)"
            >
              选择
            </el-button>
            <el-button
              v-else
              type="text"
              :disabled="editType === 'info'"
              @click="checkSku(row, index)"
            >
              {{ row.articleName }}
            </el-button>
          </div>
          <!--<el-input v-else v-model="row.articleName" size="small" />-->
        </template>
        <template #saleAttrVals="slotProps">
          <div v-if="slotProps.row.saleAttrVals">
            <span
              v-for="(item, index) in slotProps.row.saleAttrVals"
              :key="index"
              style="border: 1px solid #ddd"
            >
              {{ item.name }}: {{ item.val }}
            </span>
          </div>
        </template>
        <template #quantity="{ row }">
          <el-input-number
            v-model="row.quantity"
            :min="0"
            size="small"
            :disabled="editType === 'info'"
            :controls="false"
          />
        </template>
        <template #action="{ row, index }">
          <div class="fixed-width">
            <el-button
              v-if="editType !== 'info'"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(row, index)"
            >
              删除
            </el-button>
          </div>
        </template>
      </ProTable>
    </el-col>
    <ChooseGoods
      ref="giftGoods"
      :dialog-visible.sync="giftGoodsDialog"
      @confirmSelect="confirmSelectGoods"
      @selectionChange="selectionChange"
    />
  </div>
</template>

<script>
import ChooseGoods from "@/views/custom/editCustom/components/contract/chooseGoods.vue";
import { cloneDeep, merge } from "lodash";
import { transformFormParams } from "@/utils";

export default {
  name: "GiftGoods",
  components: {
    ChooseGoods,
  },
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    editType: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      giftGoodsDialog: false,
      columns: [
        {
          dataIndex: "giveType",
          title: "礼品类型",
          isTable: true,
          tableSlot: "giveType",
          minWidth: 120,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          tableSlot: "articleName",
          minWidth: 120,
        },
        {
          dataIndex: "articleCode",
          title: "物品编码",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku属性",
          isTable: true,
          tableSlot: "saleAttrVals",
          width: 150,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "quantity",
          title: "赠送数量",
          isTable: true,
          tableSlot: "quantity",
          align: "center",
          minWidth: 120,
        },
        {
          dataIndex: "action",
          title: "操作",
          tooltip: false,
          isTable: true,
          tableSlot: "action",
          minWidth: 80,
        },
      ],
      giveTypeOptions: [
        {
          label: "零件耗材",
          value: "PART",
        },
        {
          label: "印量",
          value: "PRINT",
        },
        {
          label: "黑白印量",
          value: "BLACK_PRINT",
        },
        {
          label: "彩色印量",
          value: "COLOR_PRINT",
        },
        {
          label: "维修",
          value: "TICKET",
        },
        {
          label: "积分",
          value: "POINTS",
        },
      ],
      currentActRow: null,
      currentIndex: null,
    };
  },
  computed: {
    infoData: {
      get() {
        if (
          this.value.customerContractGives &&
          this.value.customerContractGives.length > 0
        ) {
          this.value.customerContractGives.forEach((item) => {
            return transformFormParams(item);
          });
        }
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  created() {
    // 设置默认值
    const defaultValues = {
      customerContractGives: [],
    };
    // 合并默认值和传入的值
    this.$emit("input", {
      ...defaultValues,
      ...this.value,
    });
  },

  methods: {
    checkSku(row, index) {
      this.currentActRow = row;
      this.currentIndex = index;
      this.giftGoodsDialog = true;
    },
    handleAdd() {
      this.currentActRow = null;
      this.currentIndex = null;
      this.infoData.customerContractGives.push({
        giveType: "",
        articleName: "",
        articleCode: "",
        price: null,
        quantity: null,
        saleAttrVals: null,
      });
    },
    handleDelete(row, index) {
      this.infoData.customerContractGives.splice(index, 1);
    },
    confirmSelectGoods(val) {
      this.currentActRow.articleName = val.itemName;
      this.currentActRow.articleCode = val.articleCode;
      this.currentActRow.price = val.saleUnitPrice;
      this.currentActRow.skuId = val.saleSkuId;
      this.currentActRow.saleAttrVals = val.saleAttrVals;
      const resultList = cloneDeep(this.infoData.customerContractGives);
      const row = resultList[this.currentIndex];
      resultList[this.currentIndex] = merge({}, row, this.currentActRow);
      this.giftGoodsDialog = false;
    },
  },
};
</script>

<style scoped lang="scss">
.ladder-prices {
  width: 100%;
  display: inline-block;
}
</style>
