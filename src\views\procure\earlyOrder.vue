<!--
 * @Author: wskg
 * @Date: 2024-09-05 14:58:44
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-18 17:59:30
 * @Description: 采购 - 预警采购单
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :data="tableData"
      :columns="columns"
      show-selection
      show-rule
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #btn>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-plus"
          @click="handleAddPurchase"
        >
          生成采购单
        </el-button>
        <el-button
          v-auth="['@ums:manage:early:delete']"
          type="danger"
          size="mini"
          icon="el-icon-delete"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
        <el-button
          v-auth="['@ums:manage:early:delete']"
          type="danger"
          size="mini"
          icon="el-icon-delete"
          @click="handleAllDelete"
        >
          全部删除
        </el-button>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-refresh"
          @click="handleRefresh"
        >
          重新刷新
        </el-button>
        <el-button type="primary" size="mini" @click="handleClearCheck">
          取消勾选
        </el-button>
      </template>
      <template #rule>
        <div class="rules-tips">
          <h3 class="rule-title">采购需求新增规则</h3>
          <ol>
            <li>
              <div class="rule-item">
                <span class="rule-number">自动新增采购需求：</span>
              </div>
              <div class="rule-item" style="margin-left: 10px">
                <div class="rule-text">
                  <span class="highlight">库存量</span> +
                  <span class="highlight">工程师数量</span> +
                  <span class="highlight">在途量</span> <
                  <span class="highlight">预警值</span>
                </div>
              </div>
            </li>
            <li>
              <div class="rule-item">
                <span class="rule-number">申请数量：</span>
              </div>
              <div class="rule-item" style="margin-left: 10px">
                <div class="rule-text">
                  <span class="highlight">安全库存</span> -
                  <span class="highlight">库存量</span> -
                  <span class="highlight">工程师数量</span> -
                  <span class="highlight">在途量</span>
                </div>
              </div>
              <div class="rule-item" style="margin-left: 10px">
                <div class="rule-text">
                  <span class="warning">当没有设置安全库存时</span>，
                  默认采购需求数量 =
                  <span class="highlight">预警值</span>
                  *
                  <span class="highlight">2</span>
                </div>
              </div>
            </li>
          </ol>
        </div>
      </template>
      <template #warehouseId>
        <el-select
          v-model="queryParam.warehouseId"
          placeholder="请选择归属仓库"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in warehouseList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            v-if="row.status?.value !== 'PURCHASE'"
            size="mini"
            type="danger"
            icon="el-icon-circle-close"
            @click="handleDelete(row)"
          >
            取消
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="drawer"
      title="生成采购单"
      size="80%"
      :confirm-loading="false"
      :confirm-text="'确认'"
      @ok="confirmOrder"
      @cancel="handleClose"
    >
      <ProTable
        ref="oderProTable"
        :columns="orderColumns"
        :data="orderTableData"
        :show-loading="false"
        :show-search="false"
        :show-setting="false"
      >
        <template #number="{ row }">
          <el-input-number
            v-model="row.number"
            controls-position="right"
            size="mini"
            style="width: 50%"
            :min="1"
          ></el-input-number>
        </template>
        <template #price="{ row }">
          <el-input-number
            v-model="row.price"
            controls-position="right"
            size="mini"
            style="width: 70%"
          ></el-input-number>
        </template>
        <template #actions="{ row }">
          <div class="fixed-width">
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleRemoveSelect(row)"
            >
              移除
            </el-button>
          </div>
        </template>
      </ProTable>
      <div class="add-purchase-container">
        <ProForm
          ref="ProForm"
          :confirm-loading="false"
          :form-param="formParam"
          :form-list="formColumns"
          :layout="{ formWidth: '100%', labelWidth: '140px' }"
        ></ProForm>
      </div>
    </ProDrawer>
    <ProDialog
      :value="dialogVisible"
      width="85%"
      title="删除采购需求"
      top="2%"
      confirm-text="确认删除"
      :confirm-btn-loading="confirmLoading"
      @ok="handleDialogConfirm"
      @cancel="handleDialogClose"
    >
      <ProTable
        ref="delTable"
        :show-loading="false"
        :show-search="false"
        :show-setting="false"
        :show-pagination="false"
        :columns="columns"
        :data="delTableData"
      >
        <template #actions="{ row }">
          <div class="fixed-width">
            <el-button
              type="danger"
              icon="el-icon-delete"
              @click="handleRemoveDelSelect(row)"
            >
              移除
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  addPurchaseOrderApi,
  deletePurchaseWarningApi,
  getPurchaseWarningApi,
  updatePurchaseWarningApi,
  deletePurchaseWarningAllApi,
  refreshPurchaseWarningByWarehouseApi,
  deletePurchaseWarningBatchApi,
  refreshPurchaseWarningApi,
} from "@/api/procure";
import { warehouseListApi } from "@/api/store";

export default {
  name: "EarlyOrder",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      columns: [
        {
          dataIndex: "warehouseId",
          title: "归属仓库",
          isSearch: true,
          searchSlot: "warehouseId",
          // valueType: "select",
          // option: [],
          // optionMth: () => warehouseListApi({ status: 1401 }),
          // optionskey: {
          //   label: "name",
          //   value: "id",
          // },
        },
        {
          dataIndex: "purchaseCode",
          title: "采购单号",
          isTable: true,
          isSearch: true,
          minWidth: 160,
          valueType: "input",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          minWidth: 150,
          valueType: "input",
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          minWidth: 150,
          valueType: "input",
        },

        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          minWidth: 120,
          valueType: "input",
        },
        {
          dataIndex: "number",
          title: "申请数量",
          isTable: true,
        },
        {
          dataIndex: "warehouseName",
          title: "归属仓库",
          isTable: true,
          width: 120,
        },

        {
          dataIndex: "price",
          title: "价格",
          isTable: true,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商",
          isTable: true,
          minWidth: 160,
        },

        {
          dataIndex: "requireSource",
          title: "数据来源",
          formatter: (row) => row.requireSource?.label,
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "库存预警",
              value: "ALARM",
            },
            {
              label: "手动添加",
              value: "MANUAL",
            },
            {
              label: "工程师上报",
              value: "ENGINEER",
            },
          ],
        },
        {
          dataIndex: "status",
          title: "状态",
          formatter: (row) => row.status?.label,
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "上报",
              value: "CREATE",
            },
            {
              label: "生成采购单",
              value: "PURCHASE",
            },
            {
              label: "已取消",
              value: "CANCEL",
            },
          ],
          minWidth: 80,
        },
        {
          dataIndex: "createdName",
          title: "申请人",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "createdAt",
          title: "申请时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "Actions",
          width: 100,
          // fixed: "right",
          title: "操作",
          // align: "left",
          tooltip: false,
          isTable: true,
          tableSlot: "actions",
        },
      ],
      selectedPurchaseOrder: [],
      warehouseList: [],
      isEdit: false,
      drawer: false,
      orderColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          minWidth: 120,
          valueType: "input",
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          minWidth: 150,
          valueType: "input",
        },

        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          width: 180,
          valueType: "input",
        },
        {
          dataIndex: "number",
          title: "申请数量",
          isTable: true,
          tableSlot: "number",
          width: 180,
        },
        {
          dataIndex: "price",
          title: "价格",
          tableSlot: "price",
          isTable: true,
        },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tableSlot: "actions",
        },
      ],
      orderTableData: [],
      formParam: {},
      formColumns: [
        {
          dataIndex: "deliveryTime",
          title: "期望发货时间",
          isForm: true,
          formSpan: 8,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          prop: [
            {
              required: true,
              message: "请输入期望发货时间",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "arrivalTime",
          title: "期望到货时间",
          isForm: true,
          formSpan: 8,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          prop: [
            {
              required: true,
              message: "请输入期望发货时间",
              trigger: "change",
            },
          ],
        },
      ],
      dialogVisible: false,
      confirmLoading: false,
      delTableData: [],
    };
  },
  mounted() {
    this.refresh();
    this.getWarehouseList();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      getPurchaseWarningApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
    },
    handleBatchDelete() {
      if (!this.selectedPurchaseOrder.length) {
        this.$message.error("请先选择要删除的采购需求");
        return;
      }
      this.dialogVisible = true;
      this.delTableData = cloneDeep(this.selectedPurchaseOrder);
    },
    handleAllDelete() {
      this.$confirm("此操作将全部删除采购需求数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deletePurchaseWarningAllApi().then((res) => {
          this.$message.success("全部删除成功");
          this.refresh();
        });
      });
    },
    handleDialogConfirm() {
      if (!this.delTableData.length) {
        this.$message.error("请先选择要删除的采购需求");
        return;
      }
      const ids = this.extractIds(this.delTableData);
      this.confirmLoading = true;
      deletePurchaseWarningBatchApi(ids)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success("删除成功");
            this.handleDialogClose();
            this.handleClearCheck();
            this.refresh();
          }
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    handleDialogClose() {
      this.dialogVisible = false;
      this.delTableData = [];
      // this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },
    handleRefresh() {
      const editApi = this.queryParam.warehouseId
        ? refreshPurchaseWarningByWarehouseApi
        : refreshPurchaseWarningApi;
      editApi(this.queryParam.warehouseId).then((res) => {
        this.$message.success("刷新成功");
        this.refresh();
      });
    },
    handleClearCheck() {
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },
    handleRemoveDelSelect(row) {
      this.delTableData = this.delTableData.filter((item) => {
        return item.id !== row.id;
      });
    },
    extractIds(data) {
      const ids = [];
      function recurse(items) {
        items.forEach((item) => {
          ids.push(item.id);
          if (item.children && Array.isArray(item.children)) {
            recurse(item.children);
          }
        });
      }
      recurse(data);
      return ids;
    },
    confirmOrder() {
      if (!this.orderTableData.length) {
        this.$message.error("请先选择要采购的申请单");
        this.drawer = false;
        return;
      }
      this.$refs.ProForm.handleSubmit().then(() => {
        // const ids = this.orderTableData.map((item) => item.id);
        const params = {
          ...this.formParam,
          purchaseRequires: this.orderTableData,
        };
        addPurchaseOrderApi(params).then((res) => {
          this.refresh();
          this.$refs.ProTable.$refs.ProElTable.clearSelection();
          this.drawer = false;
          this.$message.success("生成成功");
        });
      });
    },
    handleAddPurchase() {
      if (!this.selectedPurchaseOrder.length) {
        this.$message.error("请先选择要采购的申请单");
        return;
      }
      const hasPurchaseItems = this.selectedPurchaseOrder.some(
        (item) => item.status.value === "PURCHASE"
      );
      if (hasPurchaseItems) {
        this.$confirm(
          "已选择数据中包含已生成采购单数据, 请重新选择！",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).then(() => {
          this.selectedPurchaseOrder.forEach((item) => {
            if (item.status.value === "PURCHASE") {
              this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
                item,
                false
              );
            }
          });
        });
      } else {
        this.orderTableData = this.selectedPurchaseOrder;
        this.drawer = true;
      }
    },
    handleDelete(row) {
      deletePurchaseWarningApi(row.id).then((res) => {
        this.$message.success("删除成功");
        this.localPagination = {
          pageNumber: 1,
          pageSize: 10,
          total: 0,
        };
        this.$nextTick(() => {
          this.refresh();
        });
      });
    },
    handleRemoveSelect(row) {
      this.orderTableData = this.orderTableData.filter((item) => {
        return item.id !== row.id;
      });
    },
    handleSelectionChange(val = []) {
      this.selectedPurchaseOrder = val;
    },
    handleClose() {
      this.drawer = false;
    },
    getWarehouseList() {
      warehouseListApi({ status: 1401 }).then((res) => {
        this.warehouseList = res.data;
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.add-purchase-container {
  margin-top: 20px;
}
</style>
