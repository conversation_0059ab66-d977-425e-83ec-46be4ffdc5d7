<!--
 * @Author: wskg
 * @Date: 2025-02-13 10:22:44
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 15:50:18
 * @Description: 毛机维修
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :height="500"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
      </template>
      <template #machinePics="{ row }">
        <el-image
          v-if="row.machinePics && row.machinePics.length > 0"
          style="max-width: 100px; max-height: 100px"
          :src="getMachinePicsImg(row)"
          :preview-src-list="[getMachinePicsImg(row)]"
        ></el-image>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <el-button
            v-if="row.status?.value === 'CONFIRM_REPORT'"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'audit')"
          >
            审核
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 查看、审核 -->
    <ProDrawer
      :value="drawerVisible"
      :no-footer="editType === 'info'"
      :title="drawerTitle"
      :confirm-text="'确认维修报告'"
      :confirm-button-disabled="confirmLoading"
      :no-confirm-footer="true"
      size="70%"
      @cancel="drawerCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParam"
        :form-list="formColumns"
        :open-type="editType"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
      >
        <template #process>
          <div class="title-box" style="border-bottom: none">维修进度跟踪</div>
          <div class="progress">
            <el-steps :active="activeIndex">
              <el-step
                title="领出维修"
                :description="formParam?.createdAt"
              ></el-step>
              <el-step
                title="提交维修报告"
                :description="
                  formParam.status?.value === 'CONFIRM_REPORT' ||
                  formParam.status?.value === 'COMPLETED'
                    ? formParam?.completedAt
                    : ''
                "
              ></el-step>
              <el-step
                title="已完成"
                :description="
                  formParam.status?.value === 'COMPLETED'
                    ? formParam?.auditAt
                    : ''
                "
              ></el-step>
            </el-steps>
            <!-- 时间差显示 -->
            <div class="duration-labels">
              <div v-if="formParam.completedAt" class="duration-item">
                {{ getTimeDiff(formParam.createdAt, formParam.completedAt) }}
              </div>
              <div v-if="formParam.auditAt" class="duration-item">
                {{ getTimeDiff(formParam.completedAt, formParam.auditAt) }}
              </div>
            </div>
          </div>
          <div class="title-box" style="border-bottom: none">工单信息</div>
        </template>
        <template #hostType>
          {{ formParam.hostType?.label || "" }}
        </template>
        <template #picUrls>
          <div v-if="formParam.picUrls && formParam.picUrls?.length > 0">
            <el-image
              v-for="item in formParam.picUrls"
              :key="item.url"
              style="width: 100px; height: 100px"
              :src="item.url"
              :preview-src-list="[item.url]"
            ></el-image>
          </div>
        </template>
        <template #machineRepairReplaces>
          <div
            class="title-box"
            style="border-bottom: none; padding-bottom: 0; margin-bottom: 0"
          >
            更换耗材零件清单
          </div>
          <ProTable
            :show-loading="false"
            :show-search="false"
            :show-pagination="false"
            :show-setting="false"
            :columns="replaceColumns"
            :data="formParam.machineRepairReplaces"
          >
            <template #picUrl="{ row }">
              <el-image
                v-if="
                  row.skuInfo &&
                  row.skuInfo.picUrl &&
                  row.skuInfo.picUrl.length > 0
                "
                :preview-src-list="[row?.skuInfo?.picUrl[0]?.url]"
                style="width: 100px; height: 100px"
                :src="row?.skuInfo?.picUrl[0]?.url"
              ></el-image>
            </template>

            <template #saleAttrVals="slotProps">
              <div
                v-for="attr in slotProps.row?.skuInfo?.saleAttrVals"
                :key="attr.val"
              >
                {{ attr.name }}:{{ attr.val }}
              </div>
            </template>
          </ProTable>
        </template>
        <template #remark>
          <el-input
            v-model="formParam.remark"
            type="textarea"
            maxlength="255"
            show-word-limit
            placeholder="请输入审核备注"
            :disabled="editType === 'info'"
            :autosize="{
              minRows: 3,
              maxRows: 6,
            }"
          ></el-input>
        </template>
      </ProForm>
      <!-- 驳回、确认维修报告、取消 -->
      <template #footer>
        <div v-if="editType === 'audit'" class="footer">
          <el-button
            type="danger"
            :loading="confirmLoading"
            @click="handleAudit(false)"
          >
            驳回
          </el-button>
          <el-button
            type="primary"
            :loading="confirmLoading"
            @click="handleAudit(true)"
          >
            审核通过
          </el-button>
          <el-button plain @click="drawerCancel">取消</el-button>
        </div>
      </template>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam, filterParamRange, getTimeDiff } from "@/utils";
import { cloneDeep } from "lodash";
import {
  machineRepairConfirmApi,
  machineRepairDetailApi,
  machineRepairListApi,
  machineRepairExportApi,
} from "@/api/repair";
import { dictTreeByCodeApi } from "@/api/user";
import { handleExcelExport } from "@/utils/exportExcel";

export default {
  name: "Imperfect",
  data() {
    return {
      activeIndex: 0,
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "维修单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "machinePics",
          title: "机器图片",
          isTable: true,
          tableSlot: "machinePics",
          width: 120,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(2000),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isTable: true,
          // isSearch: true,
          // valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          minWidth: 80,
        },
        // {
        //   dataIndex: "deviceStatusList",
        //   title: "设备状态",
        //   isSearch: true,
        //   multiple: true,
        //   valueType: "select",
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(6600),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "colorCounter",
          title: "彩色计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "replaceAmount",
          title: "耗材更换金额",
          isTable: true,
          minWidth: 110,
        },
        {
          dataIndex: "oriLocation",
          title: "放回位置",
          isTable: true,
          // isSearch: true,
          // valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "location",
          title: "维修位置",
          isTable: true,
          // isSearch: true,
          // valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "engineerName",
          title: "维修经办人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "auditName",
          title: "审核人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "维修中",
              value: "REPAIRING",
            },
            {
              label: "提交维修报告",
              value: "CONFIRM_REPORT",
            },
            {
              label: "完成",
              value: "COMPLETED",
            },
          ],
          minWidth: 100,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "completedAt",
          title: "入库时间",
          isTable: true,
          // isSearch: true,
          // valueType: "date-picker",
          // pickerType: "daterange",
          // pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          fixed: "right",
          width: 140,
        },
      ],
      tableData: [],
      // drawer
      editType: "info",
      confirmLoading: false,
      drawerVisible: false,
      drawerTitle: "",
      formParam: {},
      formColumns: [
        {
          dataIndex: "process",
          title: "维修进度",
          isForm: true,
          formOtherSlot: "process",
          formSpan: 24,
        },
        {
          dataIndex: "code",
          title: "维修单号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isForm: true,
          formSlot: "hostType",
          formSpan: 6,
        },
        // {
        //   dataIndex: "productName",
        //   title: "机器型号",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 6,
        // },
        {
          dataIndex: "location",
          title: "储位",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "colorCounter",
          title: "彩色计数器",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "fiveColorCounter",
          title: "五色计数器",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "replaceAmount",
          title: "耗材更换金额",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "description",
          title: "问题描述",
          isForm: true,
          valueType: "text",
          isWrap: true,
          formSpan: 24,
        },
        {
          dataIndex: "picUrls",
          title: "维修图片",
          isForm: true,
          formSlot: "picUrls",
          formSpan: 24,
        },
        {
          dataIndex: "machineRepairReplaces",
          title: "更换零件清单",
          isForm: true,
          formOtherSlot: "machineRepairReplaces",
          formSpan: 24,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isForm: true,
          formSlot: "remark",
          // valueType: "input",
          // inputType: "textarea",
          formSpan: 24,
        },
      ],
      // 更换零件清单
      replaceColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          minWidth: 160,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          minWidth: 140,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          minWidth: 140,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
          minWidth: 100,
        },

        {
          dataIndex: "picUrl",
          title: "物品图片",
          isTable: true,
          tableSlot: "picUrl",
          width: 150,
        },
        {
          dataIndex: "num",
          title: "更换数量",
          minWidth: 80,
          isTable: true,
        },
        {
          dataIndex: "costPrice",
          title: "成本单价",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "totalCostPrice",
          title: "成本总价",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "销售单价",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "location",
          title: "更换位置",
          formatter: (row) =>
            Array.isArray(row.location) ? row.location.join("、") : "",
          isTable: true,
          minWidth: 140,
        },
      ],
      stepList: ["REPAIRING", "CONFIRM_REPORT", "COMPLETED"],
      exportLoading: false,
      requestParameters: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    getTimeDiff,
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      this.requestParameters = requestParameters;
      machineRepairListApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.editType = type;
      this.formParam = {};
      this.drawerTitle = `${row.productName} - 维修报告`;
      machineRepairDetailApi(row.id).then((res) => {
        this.formParam = res.data;
        this.activeIndex = this.stepList.indexOf(this.formParam.status?.value);
        if (this.formParam.status?.value !== "REPAIRING") {
          this.activeIndex += 1;
        }
        this.drawerVisible = true;
      });
    },
    handleAudit(isPass) {
      const confirmText = isPass ? "通过" : "驳回";
      this.$confirm(`此操作将${confirmText}该维修报告, 是否继续?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.confirmLoading = true;
        const args = {
          id: this.formParam.id,
          isPass: isPass,
          remark: this.formParam.remark,
        };
        machineRepairConfirmApi(args)
          .then((res) => {
            this.$message.success("操作成功");
            this.drawerVisible = false;
            this.refresh();
          })
          .finally(() => {
            this.confirmLoading = false;
          });
      });
    },
    handleExport() {
      this.$confirm("此操作将导出毛机维修记录数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          machineRepairExportApi,
          this.requestParameters,
          "毛机维修记录数据",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    drawerCancel() {
      this.drawerVisible = false;
    },
    getMachinePicsImg(row) {
      return row?.machinePics?.[0]?.url;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.progress {
  position: relative;
  width: 90%;
  margin: auto;
}
.duration-labels {
  position: absolute;
  top: -10px;
  bottom: 0;
  display: flex;
  width: calc(100% - 112px);
  pointer-events: none;
}

.duration-item {
  width: 50%;
  font-size: 12px;
  color: #409eff;
  //color: #909399;
  text-align: center;
}
</style>
