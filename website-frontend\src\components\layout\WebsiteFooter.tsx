import { Layout, Row, Col, Divider, Modal } from 'antd';
import { 
  PhoneOutlined, 
  MailOutlined, 
  EnvironmentOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useState } from 'react';
import { usePublicConfig } from '@/hooks/useWebsiteApi';
import { useWebsiteMenus } from '@/hooks/useWebsiteMenus';

const { Footer } = Layout;

/**
 * 企业官网桌面端底部组件
 */
export function WebsiteFooter() {
  const { data: config } = usePublicConfig();
  const { data: menus = [] } = useWebsiteMenus();
  
  // 图片预览状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  
  // 地图选择弹窗状态
  const [mapModalVisible, setMapModalVisible] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState('');





  // 获取公司名称或标题，提供多层兜底
  const getCompanyName = () => {
    return config?.companyName?.trim() || 
           config?.siteTitle?.trim() || 
           '复印机维修服务公司';
  };

  // 获取公司描述，处理空值情况
  const getCompanyProfile = () => {
    return config?.companyProfile?.trim() || 
           '专业从事复印机、打印机等办公设备维修服务，拥有多年行业经验，为企业提供高质量的设备维护解决方案。';
  };



  const companyName = getCompanyName();

  // 图片预览处理函数
  const handleImagePreview = (imageUrl: string, title: string) => {
    setPreviewImage(imageUrl);
    setPreviewTitle(title);
    setPreviewVisible(true);
  };

  // 地址点击导航处理函数
  const handleAddressClick = (address: string) => {
    if (!address?.trim()) return;
    
    // 检测设备类型和浏览器，选择最佳的导航方案
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobile = /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/.test(userAgent);
    const isIOS = /iphone|ipad|ipod/.test(userAgent);
    const isAndroid = /android/.test(userAgent);
    
    const encodedAddress = encodeURIComponent(address.trim());
    
    try {
      if (isMobile) {
        if (isIOS) {
          // iOS设备优先使用Apple Maps
          const appleMapUrl = `http://maps.apple.com/?q=${encodedAddress}`;
          window.open(appleMapUrl, '_blank');
        } else {
          // Android和其他设备使用百度地图
          const baiduMapUrl = `https://map.baidu.com/search/?querytype=s&wd=${encodedAddress}`;
          window.open(baiduMapUrl, '_blank');
        }
      } else {
        // 桌面端显示地图选择弹窗
        setSelectedAddress(address.trim());
        setMapModalVisible(true);
      }
    } catch (error) {
      console.error('打开地图导航失败:', error);
      // 降级方案：复制地址到剪贴板
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(address.trim()).then(() => {
          alert('地址已复制到剪贴板，请手动打开地图应用搜索');
        }).catch(() => {
          alert('无法打开地图导航，请手动搜索地址：' + address.trim());
        });
      } else {
        alert('无法打开地图导航，请手动搜索地址：' + address.trim());
      }
    }
  };

  // 打开指定地图服务
  const openMapService = (mapType: string) => {
    if (!selectedAddress) return;
    
    const encodedAddress = encodeURIComponent(selectedAddress);
    let mapUrl = '';
    
    switch (mapType) {
      case 'baidu':
        mapUrl = `https://map.baidu.com/search/?querytype=s&wd=${encodedAddress}`;
        break;
      case 'gaode':
        mapUrl = `https://www.amap.com/search?query=${encodedAddress}`;
        break;
      case 'tencent':
        mapUrl = `https://map.qq.com/m/search/${encodedAddress}`;
        break;
      default:
        mapUrl = `https://map.baidu.com/search/?querytype=s&wd=${encodedAddress}`;
    }
    
    window.open(mapUrl, '_blank');
    setMapModalVisible(false);
  };

  return (
    <Footer className="bg-gray-900 text-gray-300 mt-auto">
      {/* 主要内容区 */}
      <div className="max-w-full px-4 lg:px-8 py-6">
        <Row gutter={[24, 24]}>
          {/* 公司信息 */}
          <Col xs={24} sm={12} lg={6}>
            <div className="space-y-4 flex flex-col h-full">
              {/* Logo */}
              <div className="flex items-center space-x-4">
                {config?.footerLogoUrl?.trim() ? (
                  <img
                    src={config.footerLogoUrl.trim()}
                    alt="logo"
                    className="w-10 h-10 object-contain flex-shrink-0"
                    onError={(e) => (e.currentTarget.style.display = 'none')}
                  />
                ) : (
                  <div className="w-10 h-10 bg-blue-600 rounded-lg flex-shrink-0 flex items-center justify-center">
                    <span className="text-white font-bold text-lg">
                      {companyName.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                <div>
                  <h3 className="text-white font-bold text-lg">
                    {companyName}
                  </h3>
                </div>
              </div>

              {/* 公司描述 */}
              <p className="text-sm text-gray-400 leading-relaxed flex-grow">
                {getCompanyProfile()}
              </p>
            </div>
          </Col>

          {/* 快速链接 */}
          <Col xs={12} sm={6} lg={3}>
            <div className="space-y-4 text-left">
              <h4 className="text-white font-semibold text-base">快速链接</h4>
              <ul className="space-y-2 pl-0 list-none">
                {menus.map(menu => (
                  <li key={menu.path}>
                    <a href={menu.path} className="text-gray-400 hover:text-white transition-colors text-sm">
                      {menu.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </Col>

          {/* 服务项目 */}
          <Col xs={12} sm={6} lg={3}>
            <div className="space-y-4 text-left">
              <h4 className="text-white font-semibold text-base">服务项目</h4>
              <div className="text-sm text-gray-400 leading-relaxed">
                {(config?.businessType || '').split(',').map((item, index) => (
                  <p key={index} className="mb-1">{item.trim()}</p>
                ))}
              </div>
            </div>
          </Col>

          {/* 联系方式 */}
          <Col xs={24} sm={12} lg={4}>
            <div className="space-y-4">
              <h4 className="text-white font-semibold text-base">联系我们</h4>
              <div className="space-y-3">
                {/* 联系电话 - 有值时显示 */}
                {config?.contactPhone?.trim() && (
                  <div className="flex items-center space-x-3">
                    <PhoneOutlined className="text-blue-400" />
                    <a 
                      href={`tel:${config.contactPhone.trim()}`}
                      className="text-sm text-gray-300 hover:text-white transition-colors"
                    >
                      {config.contactPhone.trim()}
                    </a>
                  </div>
                )}
                
                {/* 客服电话 - 与联系电话不同且有值时显示 */}
                {config?.servicePhone?.trim() && 
                 config.servicePhone.trim() !== config?.contactPhone?.trim() && (
                  <div className="flex items-center space-x-3">
                    <PhoneOutlined className="text-blue-400" />
                    <a 
                      href={`tel:${config.servicePhone.trim()}`}
                      className="text-sm text-gray-300 hover:text-white transition-colors"
                    >
                      {config.servicePhone.trim()}
                    </a>
                  </div>
                )}

                {/* 邮箱 - 有值时显示 */}
                {config?.contactEmail?.trim() && (
                  <div className="flex items-center space-x-3">
                    <MailOutlined className="text-blue-400" />
                    <a 
                      href={`mailto:${config.contactEmail.trim()}`}
                      className="text-sm text-gray-300 hover:text-white transition-colors"
                    >
                      {config.contactEmail.trim()}
                    </a>
                  </div>
                )}

                {/* 地址 - 有值时显示，支持点击导航 */}
                {config?.companyAddress?.trim() && (
                  <div className="flex items-start space-x-3">
                    <EnvironmentOutlined className="text-blue-400 mt-1" />
                    <button
                      onClick={() => handleAddressClick(config.companyAddress.trim())}
                      className="text-sm leading-relaxed text-gray-300 hover:text-white transition-colors cursor-pointer text-left border-none bg-transparent p-0 underline decoration-dotted underline-offset-2 hover:decoration-solid"
                      title="点击在地图中查看"
                    >
                      {config.companyAddress.trim()}
                    </button>
                  </div>
                )}

                {/* 营业时间 - 有值时显示 */}
                {config?.businessHours?.trim() && (
                  <div className="flex items-center space-x-3">
                    <ClockCircleOutlined className="text-blue-400" />
                    <span className="text-sm">{config.businessHours.trim()}</span>
                  </div>
                )}

                {/* 如果没有任何联系信息，显示默认提示 */}
                {!config?.contactPhone?.trim() && 
                 !config?.servicePhone?.trim() && 
                 !config?.contactEmail?.trim() && 
                 !config?.companyAddress?.trim() && (
                  <div className="text-sm text-gray-400">
                    联系信息配置中...
                  </div>
                )}
              </div>
            </div>
          </Col>

          {/* 社交媒体 */}
          {(config?.wechatNumber?.trim() || config?.qqNumber?.trim() || 
            config?.wechatIcon?.trim() || config?.qqIcon?.trim() ||
            config?.wechatQrCode?.trim() || config?.qqQrCode?.trim()) && (
            <Col xs={24} sm={12} lg={4}>
              <div className="space-y-4">
                <h4 className="text-white font-semibold text-base text-center">社交媒体</h4>
                <div className="space-y-4">
                  {/* 微信 */}
                  {(config?.wechatNumber?.trim() || config?.wechatIcon?.trim() || config?.wechatQrCode?.trim()) && (
                    <div className="flex flex-col items-center space-y-1">
                      {/* 图标和联系方式 */}
                      {(config?.wechatIcon?.trim() || config?.wechatNumber?.trim()) && (
                        <div className="flex items-center space-x-3 w-fit">
                          {/* 只在有自定义图标时显示图标容器 */}
                          {config?.wechatIcon?.trim() && (
                            <div 
                              className="w-6 h-6 flex items-center justify-center hover:opacity-80 transition-opacity flex-shrink-0"
                              title="微信联系方式"
                            >
                              {config.wechatIcon.startsWith('http://') || config.wechatIcon.startsWith('https://') ? (
                                <img 
                                  src={config.wechatIcon.trim()} 
                                  alt="微信图标" 
                                  className="w-6 h-6 object-contain"
                                  onError={(e) => (e.currentTarget.style.display = 'none')}
                                />
                              ) : (
                                <span className="text-lg leading-none">{config.wechatIcon.trim()}</span>
                              )}
                            </div>
                          )}
                          {config?.wechatNumber?.trim() && (
                            <span className="text-sm text-gray-300">{config.wechatNumber.trim()}</span>
                          )}
                        </div>
                      )}
                      
                      {/* 二维码直接显示 */}
                      {config?.wechatQrCode?.trim() && (
                        <div className="flex justify-center w-full">
                          <img 
                            src={config.wechatQrCode.trim()} 
                            alt="二维码" 
                            className="w-28 h-28 object-contain rounded border border-gray-600 cursor-pointer hover:opacity-80 transition-opacity"
                            onError={(e) => (e.currentTarget.style.display = 'none')}
                            onClick={() => handleImagePreview(config.wechatQrCode.trim(), '二维码')}
                          />
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* QQ */}
                  {(config?.qqNumber?.trim() || config?.qqIcon?.trim() || config?.qqQrCode?.trim()) && (
                    <div className="flex flex-col items-center space-y-1">
                      {/* 图标和联系方式 */}
                      {(config?.qqIcon?.trim() || config?.qqNumber?.trim()) && (
                        <div className="flex items-center space-x-3 w-fit">
                          {/* 只在有自定义图标时显示图标容器 */}
                          {config?.qqIcon?.trim() && (
                            <div 
                              className="w-6 h-6 flex items-center justify-center hover:opacity-80 transition-opacity flex-shrink-0"
                              title="QQ联系方式"
                            >
                              {config.qqIcon.startsWith('http://') || config.qqIcon.startsWith('https://') ? (
                                <img 
                                  src={config.qqIcon.trim()} 
                                  alt="QQ图标" 
                                  className="w-6 h-6 object-contain"
                                  onError={(e) => (e.currentTarget.style.display = 'none')}
                                />
                              ) : (
                                <span className="text-lg leading-none">{config.qqIcon.trim()}</span>
                              )}
                            </div>
                          )}
                          {config?.qqNumber?.trim() && (
                            <span className="text-sm text-gray-300">{config.qqNumber.trim()}</span>
                          )}
                        </div>
                      )}
                      
                      {/* 二维码直接显示 */}
                      {config?.qqQrCode?.trim() && (
                        <div className="flex justify-center w-full">
                          <img 
                            src={config.qqQrCode.trim()} 
                            alt="二维码" 
                            className="w-28 h-28 object-contain rounded border border-gray-600 cursor-pointer hover:opacity-80 transition-opacity"
                            onError={(e) => (e.currentTarget.style.display = 'none')}
                            onClick={() => handleImagePreview(config.qqQrCode.trim(), '二维码')}
                          />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </Col>
          )}

          {/* 服务理念与核心优势 */}
          <Col xs={24} sm={12} lg={(config?.wechatNumber?.trim() || config?.qqNumber?.trim() || 
            config?.wechatIcon?.trim() || config?.qqIcon?.trim() ||
            config?.wechatQrCode?.trim() || config?.qqQrCode?.trim()) ? 4 : 8}>
            <div className="space-y-4">
              <h4 className="text-white font-semibold text-base">服务理念</h4>
              <p className="text-sm text-gray-400 leading-relaxed">
                {config?.servicePhilosophy || ''}
              </p>
            </div>
            <div className="space-y-4 mt-8">
              <h4 className="text-white font-semibold text-base">核心优势</h4>
              <p className="text-sm text-gray-400 leading-relaxed">
                {config?.coreAdvantages || ''}
              </p>
            </div>
          </Col>
        </Row>
      </div>

      <Divider className="bg-gray-700 my-0" />

      {/* 底部版权信息 */}
      <div className="max-w-full px-4 lg:px-8 py-6">
        <div className="flex flex-col md:flex-row justify-center items-center text-sm text-gray-400">
          <div className="flex items-center gap-2 mb-4 md:mb-0">
            <span>{config?.copyrightNotice || `© ${new Date().getFullYear()} ${getCompanyName()}`}</span>
            {config?.icpNumber?.trim() && (
              <>
                <span className="text-gray-600">|</span>
                <a
                  href={config?.icpLink || '#'}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-white"
                >
                  {config?.icpNumber}
                </a>
              </>
            )}
            {config?.policeNumber?.trim() && (
              <>
                <span className="text-gray-600">|</span>
                <a
                  href={config?.policeLink || '#'}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-white"
                >
                  {config?.policeNumber}
                </a>
              </>
            )}
          </div>
          {/* 已移除隐私政策和服务条款链接 */}
        </div>
      </div>

      {/* 图片预览模态框 */}
      <Modal
        title={previewTitle}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width="90vw"
        style={{ maxWidth: '600px' }}
        centered
        className="image-preview-modal"
      >
        <div className="text-center">
          <img 
            src={previewImage}
            alt={previewTitle}
            className="max-w-full max-h-[70vh] object-contain rounded shadow-lg cursor-zoom-out"
            onClick={() => setPreviewVisible(false)}
          />
          <p className="text-gray-500 mt-4 text-sm">点击图片或外区域关闭预览</p>
        </div>
      </Modal>

      {/* 地图选择弹窗 */}
      <Modal
        title="选择地图服务"
        open={mapModalVisible}
        onCancel={() => setMapModalVisible(false)}
        footer={null}
        width={480}
        centered
        className="map-selection-modal"
      >
        <div className="space-y-3">
          <p className="text-gray-600 text-sm mb-4">
            选择您偏好的地图服务来查看地址：<span className="font-medium text-gray-800">{selectedAddress}</span>
          </p>
          
          <div className="grid grid-cols-1 gap-3">


            {/* 百度地图 */}
            <button
              onClick={() => openMapService('baidu')}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all group"
            >
              <div className="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mr-4 group-hover:bg-red-600 transition-colors">
                <span className="text-white font-bold text-lg">百</span>
              </div>
              <div className="text-left">
                <h4 className="font-medium text-gray-900 group-hover:text-blue-600">百度地图</h4>
                <p className="text-sm text-gray-500">国内精准定位，路况实时更新</p>
              </div>
            </button>

            {/* 高德地图 */}
            <button
              onClick={() => openMapService('gaode')}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all group"
            >
              <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-600 transition-colors">
                <span className="text-white font-bold text-lg">高</span>
              </div>
              <div className="text-left">
                <h4 className="font-medium text-gray-900 group-hover:text-blue-600">高德地图</h4>
                <p className="text-sm text-gray-500">阿里旗下，导航精准便捷</p>
              </div>
            </button>

            {/* 腾讯地图 */}
            <button
              onClick={() => openMapService('tencent')}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all group"
            >
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-700 transition-colors">
                <span className="text-white font-bold text-lg">腾</span>
              </div>
              <div className="text-left">
                <h4 className="font-medium text-gray-900 group-hover:text-blue-600">腾讯地图</h4>
                <p className="text-sm text-gray-500">腾讯出品，微信小程序支持好</p>
              </div>
            </button>
          </div>

          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-400 text-center">
              点击任意地图服务将在新标签页打开对应的网页版地图
            </p>
          </div>
        </div>
      </Modal>
    </Footer>
  );
} 