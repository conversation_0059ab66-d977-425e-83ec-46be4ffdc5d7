<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-28 16:39:07
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:23
 * @Description: 报销单
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <el-button
            v-if="row.status?.value === 'WAIT_APPROVE'"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'edit')"
          >
            审核
          </el-button>
          <el-button
            v-if="
              row.status?.value !== 'PASS' && row.status?.value !== 'CLOSED'
            "
            type="danger"
            icon="el-icon-circle-close"
            @click="handleDel(row)"
          >
            关闭
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="visibleDrawer"
      :title="drawerTitle"
      size="75%"
      :no-confirm-footer="true"
      @cancel="handleCloseDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :open-type="editType"
      >
        <template #items>
          <div class="title-box">报销明细</div>
          <ProTable
            :show-loading="false"
            :show-search="false"
            :show-setting="false"
            :columns="detailColumns"
            :data="detailTableData"
            height="65vh"
          >
            <!-- 账单科目 -->
            <template #financeSubject="{ row }">
              <!--<el-input-->
              <!--  v-model="row.financeSubject"-->
              <!--  :disabled="editType === 'info'"-->
              <!--  placeholder="请输入账单科目"-->
              <!--  style="width: 100%"-->
              <!--/>-->
              <el-select
                v-model="row.financeSubject"
                :disabled="editType === 'info'"
                placeholder="请选择账单科目"
                style="width: 100%"
              >
                <el-option
                  v-for="item in financeSubjectList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
            <template #departId="{ row }">
              <el-select
                v-model="row.departId"
                :disabled="editType === 'info'"
                placeholder="请选择部门"
                style="width: 100%"
              >
                <el-option
                  v-for="item in departList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
            <template #voucherPic="{ row }">
              <div style="display: flex; gap: 20px">
                <el-image
                  v-for="src in row.voucherPic"
                  :key="src.url"
                  style="width: 100px; height: 100px"
                  fit="fit"
                  :src="src.url"
                  :preview-src-list="getPreviewSrc(row.voucherPic)"
                ></el-image>
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
      <template #footer>
        <div v-if="editType === 'edit'" class="drawer-footer">
          <el-button type="danger" @click="handleAudit(false)">
            驳回
          </el-button>
          <el-button
            :loading="confirmLoading"
            type="primary"
            @click="handleAudit(true)"
          >
            审核通过
          </el-button>
          <el-button @click="handleCloseDrawer">关闭</el-button>
        </div>
      </template>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import {
  expenseAuditApi,
  expenseDetailApi,
  expenseListApi,
} from "@/api/finance";
import { getCustomerUserListApi } from "@/api/customer";
import { departListApi, dictTreeByCodeApi } from "@/api/user";

export default {
  name: "Bill",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "报销单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "createdBy",
          title: "申请人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
          minWidth: 100,
        },
        {
          dataIndex: "userId",
          title: "申请人",
          isSearch: true,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "applyAmount",
          title: "申请报销金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "actualAmount",
          title: "实际报销金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "auditName",
          title: "审核人",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "createdAt",
          title: "申请时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "待审核",
              value: "WAIT_APPROVE",
            },
            {
              label: "驳回",
              value: "REJECT",
            },
            {
              label: "审核通过",
              value: "PASS",
            },
            {
              label: "关闭",
              value: "CLOSED",
            },
          ],
          minWidth: 100,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "actions",
          fixed: "right",
          width: 210,
        },
      ],
      tableData: [],
      // drawer
      visibleDrawer: false,
      drawerTitle: "报销明细",
      editType: "add",
      // ProForm
      form: {},
      confirmLoading: false,
      formColumns: [
        {
          dataIndex: "code",
          title: "报销单号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "applyAmount",
          title: "申请报销金额",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "createdAt",
          title: "申请时间",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "items",
          title: "报销明细",
          isForm: true,
          formOtherSlot: "items",
          formSpan: 24,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          wordlimit: 255,
          autosize: {
            minRows: 3,
            maxRows: 5,
          },
          formSpan: 24,
        },
      ],
      // 报销明细列表
      detailColumns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "occurAt",
          title: "发生时间",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "applyAmount",
          title: "申请报销金额",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "reason",
          title: "申请原因",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "financeSubject",
          title: "账单科目",
          isTable: true,
          tableSlot: "financeSubject",
          minWidth: 120,
        },
        {
          dataIndex: "departId",
          title: "部门名称",
          isTable: true,
          tableSlot: "departId",
          minWidth: 120,
        },
        {
          dataIndex: "voucherPic",
          title: "票据",
          isTable: true,
          tableSlot: "voucherPic",
          minWidth: 250,
        },
      ],
      detailTableData: [],
      userList: [],
      departList: [],
      financeSubjectList: [],
    };
  },
  mounted() {
    this.refresh();
    getCustomerUserListApi().then((res) => {
      res.data.map((item) => {
        this.userList.push({
          value: item.id,
          label: item.name,
        });
      });
      this.columns[2].option = this.userList;
    });
    departListApi({}).then((res) => {
      this.departList = res.data.map((i) => {
        return {
          value: i.id,
          label: i.name,
        };
      });
    });
    dictTreeByCodeApi(6900).then((res) => {
      this.financeSubjectList = res.data;
    });
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParams = cloneDeep(this.queryParam);
      expenseListApi(requestParams)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.editType = type;
      this.drawerTitle = `${row.createdBy.name} - 报销单`;
      expenseDetailApi(row.id)
        .then((res) => {
          this.form = res.data;
          this.detailTableData = this.form.expenseItems || [];
          this.detailTableData.forEach((item) => {
            Object.entries(item).forEach(([key, value]) => {
              if (value === null) return;
              if (typeof value === "object") {
                // 如果是空对象则删除该属性
                if (Object.keys(value).length === 0) {
                  delete item[key];
                  return;
                }
                // 获取对象的value属性值
                item[key] = value.value ?? value;
              } else {
                // 非对象类型直接赋值
                item[key] = value;
              }
            });
          });
        })
        .finally(() => {
          this.visibleDrawer = true;
        });
    },
    // 审核
    handleAudit(status) {
      this.$confirm("是否确认审核该报销单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const args = {
          id: this.form.id,
          isPass: status,
          expenseItems: this.detailTableData,
          remark: this.form.remark,
        };
        expenseAuditApi(args).then((res) => {
          this.$message.success("审核成功");
          this.handleCloseDrawer();
          this.refresh();
        });
        // auditMachineReturnApi(row.id, { status }).then((res) => {
        //   this.$message.success("审核成功");
        //   this.refresh();
        // });
      });
    },
    handleDel(row) {
      this.$confirm("是否确认关闭该报销单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        // closeMachineReturnApi(row.id).then((res) => {
        //   this.$message.success("关闭成功");
        //   this.refresh();
        // });
      });
    },
    getPreviewSrc(pics) {
      return pics.map((item) => {
        return item.url;
      });
    },
    handleCloseDrawer() {
      this.visibleDrawer = false;
      this.$nextTick(() => {
        this.form = {};
        this.detailTableData = [];
      });
    },

    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
