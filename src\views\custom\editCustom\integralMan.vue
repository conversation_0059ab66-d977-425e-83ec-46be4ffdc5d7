<template>
  <div class="app-container">
    <div style="padding: 20px 0">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-statistic
            group-separator=","
            :precision="0"
            :value="totalData.acquireNumber || 0"
            title="累计获得积分"
          >
          </el-statistic>
        </el-col>
        <el-col :span="8">
          <el-statistic
            group-separator=","
            :precision="0"
            :value="totalData.useNumber || 0"
            title="累计消费积分"
          >
          </el-statistic>
        </el-col>
        <el-col :span="8">
          <el-statistic
            group-separator=","
            :precision="0"
            :value="totalData.unused || 0"
            title="未使用积分"
          >
          </el-statistic>
        </el-col>
      </el-row>
    </div>
    <div>
      <ProTable
        ref="ProTable"
        :show-search="false"
        :local-pagination="localPagination"
        :columns="columns"
        :data="tableData"
        :height="500"
        @loadData="loadData"
      >
        <template #btn>
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="increaseInterval"
          >
            增加积分
          </el-button>
          <el-button
            type="success"
            size="mini"
            icon="el-icon-refresh"
            :loading="refreshLoading"
            @click="getCustomerIntegralStat(1)"
          >
            刷新积分
          </el-button>
        </template>
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </ProTable>
    </div>
    <ProDialog
      :value="dialogVisible"
      title="添加积分"
      confirm-text="确定增加"
      :confirm-btn-loading="confirmLoading"
      width="30%"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="ProForm"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :form-param="form"
        :form-list="formColumns"
        :open-type="editType"
        @proSubmit="formSubmit"
      ></ProForm>
    </ProDialog>
  </div>
</template>

<script>
import {
  addCustomerIntegralApi,
  deleteCustomerIntegralApi,
  getCustomerIntegralByPageApi,
  getCustomerIntegralStatApi,
} from "@/api/customer";

export default {
  name: "IntegralManager",
  props: {
    id: {
      type: String,
      default: "",
    },
    editType: {
      type: String,
      default: "edit",
    },
  },
  data() {
    return {
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "businessCode",
          title: "业务单号",
          isTable: true,
        },
        {
          dataIndex: "source",
          title: "来源",
          isTable: true,
          formatter: (row) => row.source?.label,
        },
        {
          dataIndex: "points",
          title: "积分",
          isTable: true,
          formatter: (row) => {
            return row.type === 1 ? "+" + row.points : "-" + row.points;
          },
        },
        {
          dataIndex: "createdBy",
          title: "创建人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          // width: 150,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 100,
        },
      ],
      tableData: [],
      totalData: {},
      refreshLoading: false,
      dialogVisible: false,
      confirmLoading: false,
      form: {},
      formColumns: [
        {
          dataIndex: "points",
          title: "积分数量",
          isForm: true,
          valueType: "input",
          inputType: "number",
          prop: [
            {
              required: true,
              message: "请输入赠送积分数量",
              trigger: "blur",
            },
            {
              validator(rule, value, callback) {
                if (value <= 0) {
                  callback(new Error("积分数量必须大于0"));
                } else if (value > 10000) {
                  callback(new Error("单次增加积分数量不能大于10000"));
                } else {
                  callback();
                }
              },
            },
          ],
        },
        {
          dataIndex: "remark",
          title: "备注",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          wordlimit: "256",
          autosize: { minRows: 3, maxRows: 6 },
          prop: [
            {
              required: true,
              message: "请输入备注",
              trigger: "blur",
            },
          ],
        },
      ],
    };
  },
  mounted() {
    this.refresh();
    this.getCustomerIntegralStat();
  },
  methods: {
    loadData(parameter) {
      const requestParameters = Object.assign({}, parameter);
      getCustomerIntegralByPageApi({
        ...requestParameters,
        customerId: this.id,
      })
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleDelete(row) {
      this.$confirm("此操作将导致积分余额变动, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        if (
          this.totalData.unused <= 0 &&
          row.type === 1 &&
          this.totalData.unused - row.points < 0
        ) {
          this.$message.error("当前未使用积分不足，无法进行此次扣减操作");
          return;
        }
        deleteCustomerIntegralApi(row.id).then((res) => {
          this.$message.success("删除成功");
          this.refresh();
          this.getCustomerIntegralStat();
        });
      });
    },
    getCustomerIntegralStat(flag = 0) {
      if (flag) {
        this.refreshLoading = true;
      }
      getCustomerIntegralStatApi({ customerId: this.id })
        .then((res) => {
          const { acquireNumber = 0, useNumber = 0 } = res.data;
          this.totalData = res.data;
          this.totalData.unused = acquireNumber - useNumber;
          if (flag) {
            this.$message.success("刷新成功");
          }
        })
        .finally(() => {
          this.refreshLoading = false;
        });
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    formSubmit(val) {
      try {
        this.confirmLoading = true;
        const args = {
          ...val,
          customerId: this.id,
        };
        addCustomerIntegralApi(args).then((res) => {
          this.$message.success("操作成功");
          this.refresh();
          this.getCustomerIntegralStat();
          this.dialogVisible = false;
        });
      } finally {
        this.confirmLoading = false;
      }
    },
    handleDialogCancel() {
      this.dialogVisible = false;
    },
    increaseInterval() {
      this.dialogVisible = true;
      // this.$prompt("请输入增加积分数量", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   inputType: "number",
      //   inputValue: 100,
      //   inputErrorMessage: "请输入有效的正整数",
      //   inputValidator: (value) => {
      //     if (!value) {
      //       return "请输入有效的正整数";
      //     }
      //     const parsedValue = parseInt(value);
      //     if (isNaN(parsedValue) || parsedValue <= 0) {
      //       return "请输入有效的正整数";
      //     }
      //     if (value > 10000) {
      //       return "单次赠送积分不能超过10000";
      //     }
      //     return true;
      //   },
      // }).then(({ value }) => {
      //   const pointsToAdd = parseInt(value);
      //   addCustomerIntegralApi({ customerId: this.id, points: pointsToAdd })
      //     .then((res) => {
      //       if (res.code === 200) {
      //         this.$message({
      //           type: "success",
      //           message: `成功增加 ${pointsToAdd} 积分`,
      //         });
      //         this.getCustomerIntegralStat(); // Refresh the stats
      //         this.refresh();
      //       } else {
      //         this.$message.error(res.message || "增加积分失败");
      //       }
      //     })
      //     .catch((error) => {
      //       this.$message.error("操作失败：" + (error.message || error));
      //     });
      // });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-statistic {
  .head {
    font-size: 16px;
  }
  .con {
    font-size: 14px;
  }
}
</style>
