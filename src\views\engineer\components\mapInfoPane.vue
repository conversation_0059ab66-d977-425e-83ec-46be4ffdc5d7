<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-19 15:51:27
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-30 15:38:42
 * @Description: 工程师位置地图信息窗口
 -->

<template>
  <div class="info-pane">
    <div
      class="info-container"
      :class="{
        overdue: overdue,
        'repair-overdue': repairOverdue,
      }"
    >
      <!-- 头部信息 -->
      <div class="info-line header-row">
        <span id="info-title" class="name">
          {{ infoData?.name }}
          {{
            repairOverdue ? "（⚠️维修超时）" : overdue ? "（⚠️接单超时）" : ""
          }}
        </span>
        <span
          v-if="infoData?.type === 'engineer'"
          id="info-status"
          class="status-tag"
          :class="engineerStatusClass"
        >
          {{ infoData?.waitingWorkNum > 0 ? "忙碌" : "空闲" }}
        </span>
        <span v-else id="info-status" class="status-tag" :class="statusClass">
          {{ infoData?.status?.label }}
        </span>
      </div>

      <!-- 客户信息 -->
      <template v-if="infoData?.type === 'customer'">
        <div v-show="showDetail" class="info-line">
          报修机型：{{ infoData?.productInfo }}
        </div>
        <div v-show="showName" class="info-line">
          维修工程师：{{ infoData?.engineerId?.name }}
        </div>
        <div v-show="showTime" class="info-line">
          报修时间：{{ infoData?.createdAt }}
        </div>
        <div
          v-show="showDetail && (overdue || repairOverdue)"
          class="info-line"
        >
          超时时长：{{
            repairOverdue ? repairOverdueTime : overdue ? overdueTime : ""
          }}
        </div>
      </template>

      <!-- 工程师信息 -->
      <template v-else-if="infoData?.type === 'engineer'">
        <div v-show="showName" class="info-rows">
          <div class="info-line">
            剩余工单：
            <span class="status-blue bgc">
              {{ infoData?.waitingWorkNum || 0 }}
            </span>
          </div>
          <div class="info-line">
            今日完成：
            <span class="status-green bgc">
              {{ infoData?.completedWorkNum || 0 }}
            </span>
          </div>
        </div>
        <div v-show="showTime" class="info-rows">
          <!--<div class="info-line">-->
          <!--  指派工单：-->
          <!--  <span class="status-orange bgc">-->
          <!--    {{ infoData?.deliveryWorkNum || 0 }}-->
          <!--  </span>-->
          <!--</div>-->
          <div class="info-line">
            总工单数：
            <span class="status-orange bgc">
              {{ infoData?.todayWorkNum || 0 }}
            </span>
          </div>
        </div>
        <div v-show="showDetail" class="info-rows">
          上报时间： {{ infoData?.reportTime }}
        </div>
      </template>
    </div>
    <div class="point"></div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    infoData: {
      type: Object,
      default: () => {},
    },
    zoom: {
      type: Number,
      default: 11,
    },
  },
  data() {
    return {
      overdue: false,
      repairOverdue: false,
      overdueTime: "",
      repairOverdueTime: "",
    };
  },

  computed: {
    currentZoom() {
      return this.zoom || 11;
    },
    showName() {
      return this.currentZoom >= 11;
    },
    showTime() {
      return this.currentZoom >= 11.5;
    },
    showDetail() {
      return this.currentZoom >= 12.5;
    },
    engineerStatusClass() {
      const value = +this.infoData?.waitingWorkNum;
      return value > 0 ? "status-orange" : "status-green";
    },
    statusClass() {
      const value = this.infoData?.status?.value;
      switch (value) {
        case "pending_orders":
          return "status-orange";
        case "engineer_arrive":
          return "status-green";
        case "engineer_receive":
          return "status-blue";
        case "completed":
          return "status-gray";
        case "wait_confirmed_report":
          return "status-orange";
        case "engineer_departure":
          return "status-purple";
        default:
          return "status-green";
      }
    },
  },
  watch: {
    infoData: {
      handler(val) {
        if (!val) return;
        this.repairOverdue = this.isRepairOverdue(val);
        this.overdue = this.isOverdue(val);
      },
      deep: true,
      immediate: true,
    },
  },

  methods: {
    // 报修 -> 工程师到达超过4小时算超时
    isOverdue(item) {
      if (!item || typeof item !== "object" || !("createdAt" in item)) {
        return false;
      }

      const parseDate = (str) => {
        if (!str) return null;
        return new Date(
          str.replace("年", "-").replace("月", "-").replace("日", "")
        );
      };

      const start = parseDate(item.createdAt);
      const end = item.actualArriveTime
        ? parseDate(item.actualArriveTime)
        : new Date();

      // 验证日期有效性
      if (!(start instanceof Date) || isNaN(start.getTime())) return false;
      if (!(end instanceof Date) || isNaN(end.getTime())) return false;
      this.overdueTime = this.formatEffectiveTime(start, end);
      // console.log(this.getEffectiveHours(start, end) > 4, "51212");
      return this.getEffectiveHours(start, end) > 4;
    },
    // 到达 -> 提交报告时间超过4小时算超时
    isRepairOverdue(item) {
      if (!item || typeof item !== "object" || !("actualArriveTime" in item)) {
        return false;
      }
      const parseDate = (str) => {
        if (!str) return null;
        return new Date(
          str.replace("年", "-").replace("月", "-").replace("日", "")
        );
      };

      const start = parseDate(item.actualArriveTime);
      const end = item.sendReportTime
        ? parseDate(item.sendReportTime)
        : new Date();

      // 验证日期有效性
      if (!(start instanceof Date) || isNaN(start.getTime())) return false;
      if (!(end instanceof Date) || isNaN(end.getTime())) return false;
      this.repairOverdueTime = this.formatEffectiveTime(start, end);
      return this.getEffectiveHours(start, end) > 4;
    },
    formatEffectiveTime(start, end) {
      const totalHours = this.getEffectiveHours(start, end);
      if (totalHours <= 0) return "";

      const hours = Math.floor(totalHours);
      const minutes = Math.round((totalHours - hours) * 60);

      return `${hours}小时${minutes > 0 ? minutes + "分钟" : ""}`;
    },
    // 计算有效工作时间（仅工作日 8:30~18:00）
    getEffectiveHours(start, end) {
      if (
        !(start instanceof Date) ||
        !(end instanceof Date) ||
        isNaN(start) ||
        isNaN(end)
      ) {
        console.error("无效时间参数");
        return 0;
      }

      const WORK_START_HOUR = 8;
      const WORK_START_MIN = 30;
      const WORK_END_HOUR = 18;
      let totalHours = 0;

      const current = new Date(start);
      const finish = new Date(end);

      while (current < finish) {
        const day = current.getDay();

        // 排除周日（可加条件 day !== 6 排除周六）
        if (day !== 0) {
          const workStart = new Date(current);
          workStart.setHours(WORK_START_HOUR, WORK_START_MIN, 0, 0);

          const workEnd = new Date(current);
          workEnd.setHours(WORK_END_HOUR, 0, 0, 0);

          const effectiveStart = Math.max(
            current.getTime(),
            workStart.getTime()
          );
          const effectiveEnd = Math.min(end.getTime(), workEnd.getTime());

          if (effectiveStart < effectiveEnd) {
            totalHours += (effectiveEnd - effectiveStart) / 36e5; // 1000*60*60
          }
        }
        // 下一天 00:00
        current.setDate(current.getDate() + 1);
        current.setHours(0, 0, 0, 0);
      }
      return totalHours;
    },
  },
};
</script>

<style lang="scss" scoped>
.info-pane {
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    Arial, sans-serif;
  pointer-events: auto;

  .info-container {
    min-width: 180px;
    background-color: #fff;
    padding: 10px 12px;
    border-radius: 12px;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 9999;

    .info-rows {
      display: flex;
      justify-content: space-between;
      gap: 10px;
      max-height: 32px;
      transition: max-height 0.3s ease;
      overflow: hidden;
    }

    .info-line {
      padding: 4px 0;
      color: #333;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-height: 24px;
      transition: max-height 0.3s ease;
      overflow: hidden;
    }

    .header-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 24px;
    }

    .name {
      font-weight: bold;
      color: #222;
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #1890ff;
      }
    }

    .status-tag {
      padding: 2px 6px;
      border-radius: 8px;
      font-size: 12px;
      font-weight: 600;
      line-height: 1.2;
      cursor: pointer;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    // 状态颜色
    .status-green {
      background-color: #f6ffed;
      color: #52c41a;
    }
    .status-blue {
      background-color: #e6f7ff;
      color: #1890ff;
    }
    .status-gray {
      background-color: #f5f5f5;
      color: #8c8c8c;
    }
    .status-orange {
      background-color: #fff7e6;
      color: #fa8c16;
    }
    .status-purple {
      background-color: #f9f0ff;
      color: #722ed1;
    }
    .bgc {
      background-color: transparent;
    }
  }

  //.info-container.overdue {
  //  background-color: #fff1f0;
  //  border-left: 4px solid #ff4d4f;
  //  box-shadow: 0 3px 10px rgba(255, 77, 79, 0.12);
  //
  //  .name {
  //    color: #ff4d4f;
  //    font-weight: 600;
  //  }
  //}

  .info-container.repair-overdue,
  .info-container.overdue {
    background-color: #fffbe6;
    border-left: 4px solid #faad14;
    box-shadow: 0 3px 10px rgba(250, 173, 20, 0.12);

    .name {
      color: #faad14;
      font-weight: 600;
    }
  }

  .point {
    width: 10px;
    height: 10px;
    background-color: #fff;
    transform: rotate(45deg);
    position: absolute;
    bottom: -5px;
    left: 0;
    right: 0;
    margin: auto;
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: -1;
  }
}

// 错误状态动画
@keyframes pulse-error {
  0% {
    box-shadow: 0 0 8px rgba(255, 77, 79, 0.4);
  }
  50% {
    box-shadow: 0 0 14px rgba(255, 77, 79, 0.8);
  }
  100% {
    box-shadow: 0 0 8px rgba(255, 77, 79, 0.4);
  }
}

.info-container.overdue {
  animation: pulse-error 2s infinite;
}

// 警告状态动画
@keyframes pulse-warning {
  0% {
    box-shadow: 0 0 8px rgba(250, 173, 20, 0.4);
  }
  50% {
    box-shadow: 0 0 14px rgba(250, 173, 20, 0.8);
  }
  100% {
    box-shadow: 0 0 8px rgba(250, 173, 20, 0.4);
  }
}

.info-container.repair-overdue {
  animation: pulse-warning 2s infinite;
}
</style>
