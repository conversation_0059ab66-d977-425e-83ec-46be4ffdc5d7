import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Spin, Form, Input, Select, Button, message, Modal } from 'antd';
import {
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  FieldTimeOutlined
} from '@ant-design/icons';
import { useContact, usePublicConfig, useTencentMapKey, useAddInquiry } from '../../../hooks/useWebsiteApi';
import { isMobileDevice } from '../../../hooks/useDevice';
import MapPreview from '../../../components/common/MapPreview';

const { Option } = Select;
const { TextArea } = Input;

const ContactPage: React.FC = () => {
  const { data: contentData, isLoading: isContentLoading } = useContact();
  const { data: publicConfig } = usePublicConfig();
  const addInquiryMutation = useAddInquiry();
  const [form] = Form.useForm();

  const handleAddressClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (!contactInfo.address) return;

    const address = contactInfo.address;
    const encodedAddress = encodeURIComponent(address);
    const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;

    // iOS: Show a modal with map choices
    if (/iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream) {
      Modal.info({
        title: '选择导航应用',
        content: (
          <div className="space-y-1 text-center">
            <a className="block p-3 text-blue-600 border-b cursor-pointer" href={`http://maps.apple.com/?q=${encodedAddress}&dirflg=d`} target="_blank">Apple 地图</a>
            <a className="block p-3 text-blue-600 border-b cursor-pointer" href={`iosamap://path?sourceApplication=benyin&dname=${encodedAddress}&dev=0`} target="_blank">高德地图</a>
            <a className="block p-3 text-blue-600 border-b cursor-pointer" href={`baidumap://map/place/search?query=${encodedAddress}`} target="_blank">百度地图</a>
            <a className="block p-3 text-blue-600 cursor-pointer" href={`comgooglemaps://?q=${encodedAddress}&directionsmode=driving`} target="_blank">Google 地图</a>
          </div>
        ),
        okText: '取消',
        closable: true,
        icon: null,
      });
      return;
    }

    // Android: Use geo intent to show app chooser
    if (/android/i.test(userAgent)) {
      window.open(`geo:0,0?q=${encodedAddress}`, '_blank');
      return;
    }
    
    // Fallback for other devices (e.g., desktop)
    window.open(`https://map.baidu.com/search/?querytype=s&wd=${encodedAddress}`, '_blank');
  };

  const onFinish = async (values: any) => {
    try {
      await addInquiryMutation.mutateAsync(values);
      message.success('您的咨询已成功提交，我们会尽快与您联系！');
      form.resetFields();
    } catch (error) {
      message.error('提交失败，请稍后重试。');
    }
  };

  if (isContentLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" />
      </div>
    );
  }

  // 修复：直接使用contentData，解析content字段
  const content = contentData?.content ? JSON.parse(contentData.content) : {};
  const config = content.config || {};



  const heroTitle = config.heroTitle || '联系我们';
  const heroSubtitle = config.heroSubtitle || '24小时快速响应，专业维修服务';
  const formTitle = config.formTitle || '在线咨询';
  const mapTitle = config.mapTitle || '位置地图';
  const ctaTitle = config.ctaTitle || '需要维修服务？';
  const ctaSubtitle = config.ctaSubtitle || '立即联系我们，获得专业的设备维修解决方案';
  const ctaButtonText = config.ctaButtonText || '立即联系我们';

  const businessTypeSource = (config.businessType && config.businessType.trim()) ? config.businessType : (publicConfig?.businessType || '复印机维修,打印机维修,设备保养,耗材更换');

  const contactInfo = {
    address: config.companyAddress || '北京市朝阳区XXX路XXX号',
    phone: config.servicePhone || '************',
    hours: config.businessHours || '周一至周五 9:00-18:00',
    email: config.contactEmail || '<EMAIL>',
  };

  const commitments = config.commitments || [
    '24小时内快速响应',
    '上门服务，现场检修',
    '质量保证，售后无忧',
    '合理收费，透明价格',
  ];

  return (
    <div className="bg-gray-50">
      {/* 英雄区 */}
      <section className="bg-gray-800 text-white py-16 text-center min-h-[276px]">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">{heroTitle}</h1>
          <p className="text-xl">{heroSubtitle}</p>
        </div>
      </section>

      {/* 主体内容区 */}
      <section className="py-6 md:py-12 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 items-stretch">
            
            {/* 左侧：在线咨询表单 */}
            <div className="lg:col-span-3 bg-white p-8 rounded-lg shadow-lg h-full">
              <h2 className="text-2xl font-semibold mb-6">{formTitle}</h2>
              <Form
                form={form}
                name="inquiry"
                layout="vertical"
                onFinish={onFinish}
                initialValues={{ serviceType: '复印机维修' }}
              >
                
                <Form.Item
                  label="店铺名称"
                  name="company"
                  rules={[{ required: true, max: 100, message: '名称不能超过100个字符' }]}
                >
                  <Input placeholder="请输入您的店铺名称" />
                </Form.Item>

                <Form.Item
                  label="姓名"
                  name="name"
                  rules={[
                    { required: true, message: '请输入您的姓名' },
                    { min: 2, max: 20, message: '姓名长度应在2-20个字符之间' }
                  ]}
                >
                  <Input placeholder="请输入您的姓名" />
                </Form.Item>

                <Form.Item
                  label="联系电话"
                    name="phone"
                  rules={[{ required: true, message: '请输入您的手机号码' }]}
                >
                  <Input placeholder="请输入您的手机号码" />
                </Form.Item>

                <Form.Item
                  label="服务类型"
                  name="subject"
                  rules={[{ required: true, message: '请选择咨询类型' }]}
                >
                  <Select placeholder="请选择咨询类型">
                    {businessTypeSource
                      .split(',')
                      .map((type: string) => type.trim())
                      .filter((type: string) => type)
                      .map((type: string) => (
                        <Option key={`service-type-${type}`} value={type}>
                          {type}
                        </Option>
                      ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  label="详细需求"
                  name="content"
                  rules={[
                    { required: true, message: '请详细描述您的需求' },
                    { min: 10, max: 1000, message: '咨询内容长度应在10-1000个字符之间' }
                  ]}
                >
                  <TextArea rows={4} placeholder="请详细描述您的设备问题或服务需求" />
                </Form.Item>

                <Form.Item>
                  <Button type="primary" htmlType="submit" block size="large" loading={addInquiryMutation.isPending}>
                    提交咨询
                  </Button>
                </Form.Item>
              </Form>
            </div>

            {/* 右侧：联系信息和服务承诺 */}
            <div className="lg:col-span-2 flex flex-col justify-between h-full">
              <div className="bg-white p-8 rounded-lg shadow-lg">
                <h3 className="text-2xl font-semibold mb-6">联系信息</h3>
                <div className="space-y-4 text-gray-700">
                  <a href="#" onClick={handleAddressClick} className="flex items-start cursor-pointer hover:text-blue-500 transition-colors">
                    <EnvironmentOutlined className="text-blue-500 mt-1 mr-4" />
                    <span>{contactInfo.address}</span>
                  </a>
                  <a href={`tel:${contactInfo.phone}`} className="flex items-start cursor-pointer hover:text-blue-500 transition-colors">
                    <PhoneOutlined className="text-blue-500 mt-1 mr-4" />
                    <span>{contactInfo.phone}</span>
                  </a>
                  <a href={`mailto:${contactInfo.email}`} className="flex items-start cursor-pointer hover:text-blue-500 transition-colors">
                    <MailOutlined className="text-blue-500 mt-1 mr-4" />
                    <span>{contactInfo.email}</span>
                  </a>
                  <div className="flex items-start">
                    <FieldTimeOutlined className="text-blue-500 mt-1 mr-4" />
                    <span>{contactInfo.hours}</span>
                  </div>                  
                </div>
              </div>

              <div className="bg-indigo-500 text-white p-8 rounded-lg shadow-lg">
                <h3 className="text-2xl font-semibold mb-6">{config.commitmentTitle || '服务承诺'}</h3>
                <ul className="space-y-3 pl-0 list-none">
                  {commitments.map((item: string, index: number) => (
                    <li key={index} className="flex items-center">
                      <span className="text-green-300 mr-2">✓</span>
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* 位置导航 */}
          <div className="mt-12 bg-white rounded-lg shadow-lg px-0 md:px-8 pt-4 pb-8">
            <h2 className="text-2xl font-semibold mb-6 text-center">{mapTitle}</h2>
            <div className="h-96 rounded-lg overflow-hidden">
              <MapPreview address={contactInfo.address} height="100%" showNavigateButton />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage; 