<template>
  <div class="service-distribution-chart">
    <div v-if="loading" class="chart-loading">
      <div class="loading-spinner">
        <i class="el-icon-loading"></i>
        <span>加载中...</span>
      </div>
    </div>
    
    <div v-else-if="error" class="chart-error">
      <div class="error-content">
        <i class="el-icon-warning"></i>
        <div class="error-text">
          <div class="error-title">数据获取错误</div>
          <div class="error-description">服务类型分布数据暂时无法加载</div>
        </div>
      </div>
    </div>
    
    <div v-else-if="!hasData" class="chart-empty">
      <div class="empty-content">
        <i class="el-icon-pie-chart"></i>
        <div class="empty-text">
          <div class="empty-title">暂无数据</div>
          <div class="empty-description">暂时没有服务类型分布数据</div>
        </div>
      </div>
    </div>
    
    <div v-else ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ServiceDistributionChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '服务类型分布'
    }
  },
  
  data() {
    return {
      chart: null,
      resizeTimer: null,
      intersectionObserver: null
    }
  },
  
  computed: {
    hasData() {
      return this.data && Array.isArray(this.data) && this.data.length > 0
    },
    
    chartOptions() {
      if (!this.hasData) return null
      
      // 生成颜色数组
      const colors = [
        '#1890ff',
        '#52c41a', 
        '#faad14',
        '#f5222d',
        '#722ed1',
        '#13c2c2',
        '#eb2f96',
        '#fa541c'
      ]
      
      const chartData = this.data.map((item, index) => ({
        name: item.serviceType,
        value: Number(item.count) || 0,
        percentage: Number(item.percentage) || 0,
        itemStyle: {
          color: colors[index % colors.length]
        }
      }))

      const totalCount = this.data.reduce((sum, item) => sum + (Number(item.count) || 0), 0)
      
      return {
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            // 优先使用API返回的百分比，如果没有则计算
            const percentage = params.data.percentage ||
              (totalCount > 0 ? ((params.value / totalCount) * 100).toFixed(1) : 0)
            return `${params.marker} ${params.name}<br/>数量: ${params.value}<br/>占比: ${percentage}%`
          }
        },
        legend: {
          orient: 'horizontal',
          bottom: 10,
          left: 'center',
          data: this.data.map(item => item.serviceType),
          itemGap: 16,
          itemHeight: 14,
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '服务类型',
            type: 'pie',
            radius: ['20%', '45%'],
            center: ['50%', '35%'],
            avoidLabelOverlap: true,
            itemStyle: {
              borderRadius: 8,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'outside',
              formatter: (params) => {
                // 优先使用API返回的百分比，如果没有则计算
                const percentage = params.data.percentage ||
                  (totalCount > 0 ? ((params.value / totalCount) * 100).toFixed(1) : 0)
                return `${percentage}%`
              },
              fontSize: 11,
              color: '#666',
              distance: 18
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold'
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            labelLine: {
              show: true,
              length: 10,
              length2: 8,
              lineStyle: {
                color: '#999',
                width: 1
              }
            },
            data: chartData
          }
        ],
        grid: {
          top: '5%',
          bottom: '30%',
          left: '5%',
          right: '5%'
        }
      }
    }
  },
  
  watch: {
    data: {
      handler() {
        this.renderChart()
      },
      deep: true
    },
    
    loading() {
      if (!this.loading && this.hasData) {
        this.$nextTick(() => {
          this.renderChart()
        })
      }
    }
  },
  
  mounted() {
    if (this.hasData && !this.loading) {
      this.$nextTick(() => {
        this.renderChart()
      })
    }

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)

    // 设置IntersectionObserver来监听容器可见性
    this.setupIntersectionObserver()
  },

  activated() {
    // 页面被激活时重新渲染图表
    this.$nextTick(() => {
      if (this.hasData && !this.loading) {
        this.renderChart()
      }
    })
  },

  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer)
    }
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  
  methods: {
    renderChart() {
      if (!this.$refs.chartContainer || !this.chartOptions) return

      // 确保容器有尺寸
      const container = this.$refs.chartContainer
      if (container.offsetWidth === 0 || container.offsetHeight === 0) {
        // 如果容器尺寸为0，延迟重试
        setTimeout(() => {
          this.renderChart()
        }, 100)
        return
      }

      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = echarts.init(container)
      this.chart.setOption(this.chartOptions)

      // 确保图表适应容器
      this.chart.resize()
    },

    handleResize() {
      if (this.chart) {
        // 防抖处理
        if (this.resizeTimer) {
          clearTimeout(this.resizeTimer)
        }
        this.resizeTimer = setTimeout(() => {
          this.chart.resize()
        }, 100)
      }
    },

    setupIntersectionObserver() {
      if (!window.IntersectionObserver) return

      this.intersectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && entry.intersectionRatio > 0) {
            // 容器变为可见时，重新渲染图表
            this.$nextTick(() => {
              if (this.hasData && !this.loading) {
                this.renderChart()
              }
            })
          }
        })
      }, {
        threshold: 0.1 // 当10%的容器可见时触发
      })

      // 观察图表容器
      this.$nextTick(() => {
        if (this.$refs.chartContainer) {
          this.intersectionObserver.observe(this.$refs.chartContainer)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.service-distribution-chart {
  height: 100%;
  width: 100%;
  
  .chart-loading,
  .chart-error,
  .chart-empty {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #1890ff;
    
    i {
      font-size: 32px;
    }
    
    span {
      font-size: 14px;
    }
  }
  
  .error-content,
  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #999;
    
    i {
      font-size: 48px;
    }
    
    .error-text,
    .empty-text {
      text-align: center;
      
      .error-title,
      .empty-title {
        font-size: 16px;
        color: #666;
        margin-bottom: 4px;
      }
      
      .error-description,
      .empty-description {
        font-size: 14px;
        color: #999;
      }
    }
  }
  
  .chart-container {
    height: 100%;
    width: 100%;
    min-height: 300px;
  }
}
</style>
