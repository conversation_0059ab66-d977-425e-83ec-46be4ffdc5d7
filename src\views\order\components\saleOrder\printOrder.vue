<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-07 17:54:17
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:19
 * @Description: 打印出货单
 -->
<template>
  <ProDialog
    :value="visible"
    title=""
    width="1000px"
    :no-footer="true"
    @cancel="handleCancel"
  >
    <div ref="printContent" class="print-content">
      <div class="header">
        <h2>四川至简智印科技有限公司（销售出货单）</h2>
        <div class="date">
          <span class="label">订单日期：</span>
          <span>{{ orderInfo?.payTime }}</span>
        </div>
      </div>

      <div class="info-section">
        <div class="info-rows">
          <div class="info-row">
            <span class="label">店铺名称：</span>
            <span class="value">
              <!--{{ orderInfo?.customerName }}-->
              {{
                orderInfo?.license
                  ? `${orderInfo?.customerName}（${orderInfo.license}）`
                  : orderInfo?.customerName
              }}
            </span>
          </div>
          <div class="info-row">
            <span class="label">订单号：</span>
            <span class="value">
              {{ orderInfo?.orderNum }}
            </span>
          </div>
          <!--<div class="info-row">-->
          <!--  <span class="label">日期：</span>-->
          <!--  <span class="value">-->
          <!--    {{ currentDate }}-->
          <!--  </span>-->
          <!--</div>-->
        </div>

        <div class="info-row">
          <span class="label">收货地址：</span>
          <span class="value">
            {{ orderInfo?.consigneeAddress }}
          </span>
        </div>
      </div>

      <div class="table-section">
        <table class="goods-table">
          <thead>
            <tr>
              <th>商品名称</th>
              <th>规格</th>
              <th>单位</th>
              <th>数量</th>
              <th>单价</th>
              <th>合计金额</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in tradeOrderDetail" :key="index">
              <td>{{ item.itemName }}</td>
              <td>
                {{ getSaleAttrVals(item) }}
              </td>
              <td>{{ item.storageArticle?.minUnit }}</td>
              <td>{{ item.itemNum }}</td>
              <td>{{ item.actualUnitPrice }}</td>
              <td>{{ item.actualPayAmount }}</td>
            </tr>
            <tr class="total-row">
              <td colspan="2" style="font-weight: bold">
                运费：{{ orderInfo?.shippingFee }}
              </td>
              <td colspan="1" style="font-weight: bold"></td>
              <td colspan="1" style="font-weight: bold">
                {{ getSaleNumber }}
              </td>
              <td colspan="2" style="font-weight: bold">
                合计金额：{{ orderInfo?.actualAmount }}
              </td>
              <!--<td colspan="4" style="font-weight: bold">-->
              <!--  商品金额：{{ orderInfo?.actualGoodsAmount }}-->
              <!--</td>-->
            </tr>
            <tr class="total-row">
              <td colspan="6" style="font-weight: bold; text-align: left">
                大写金额: {{ convertToChinese(orderInfo?.actualAmount) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="footer">
        <div class="signature">
          <div class="signature-item">
            <span class="label">公司地址：</span>
            <span class="value">成都市成华区航天路118号1C203</span>
          </div>
          <div class="signature-item">
            <span class="label">座机：</span>
            <span class="value">028-62302212</span>
          </div>
          <div class="signature-item">
            <span class="label">客服：</span>
            <span class="value">17744209190</span>
          </div>
        </div>

        <div class="signature">
          <div class="signature-item">
            <span class="label">制单人：</span>
            <span class="value">{{ userInfo?.name }}</span>
          </div>
          <div class="signature-item">
            <span class="label">制单时间：</span>
            <span class="value">{{ currentDate }}</span>
          </div>
          <div class="signature-item">
            <span class="label">客户（签章）：</span>
            <span class="value"></span>
          </div>
        </div>
        <div class="remark">
          <span class="label">备注：</span>
          <span class="value" contenteditable>
            <!--  placeholder="请输入备注信息"-->
            <!--<el-input v-model="remark" style="border: none" />-->
          </span>
        </div>

        <div class="tips">
          <p>此单经签字确认为欠款单据（产生法律效力）</p>
          <p>
            温馨提示：
            夏季高温，粉易结块，及时验货并按要求妥善保管。如客户自身原因造成的质量问题，本公司概不负责。原装耗材及配件一经售出，不予退换。
          </p>
        </div>
      </div>
    </div>

    <div class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handlePrint">打印</el-button>
      <!--<el-button type="success" @click="handleDownloadPDF">下载PDF</el-button>-->
    </div>
  </ProDialog>
</template>

<script>
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { operatorTradeOrderDetailApi } from "@/api/operator";

export default {
  name: "PrintOrder",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      currentDate: new Date().toLocaleString(),
      baseInfo: {},
      tradeOrderDetail: [],
      userInfo: JSON.parse(localStorage.getItem("userInfo")),
      remark: "",
    };
  },
  computed: {
    getSaleNumber() {
      let total = 0;
      this.tradeOrderDetail.forEach((item) => {
        total += item.itemNum;
      });
      return total;
    },
  },
  watch: {
    orderInfo: {
      handler(val) {
        console.log(val, "valla");
        this.remark = "";
        operatorTradeOrderDetailApi(val.orderNum)
          .then((res) => {
            this.baseInfo = res.data;
            this.tradeOrderDetail =
              res.data.tradeOrder.tradeOrderDetailList || [];
          })
          .finally((_) => {
            this.infoLoading = false;
            this.confirmLoading = false;
          });
      },
    },
  },
  methods: {
    handleCancel() {
      this.$emit("update:visible", false);
    },
    async handlePrint() {
      const printWindow = window.open("", "_blank");
      const content = this.$refs.printContent.innerHTML;
      printWindow.document.write(`
        <html>
          <head>
            <title>销售出货单</title>
            <style>
              ${this.getPrintStyles()}
            </style>
          </head>
          <body>
            ${content}
          </body>
        </html>
      `);
      printWindow.document.close();
      // printWindow.print();
    },
    async handleDownloadPDF() {
      const loading = this.$loading({
        lock: true,
        text: "正在生成PDF...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      try {
        const canvas = await html2canvas(this.$refs.printContent, {
          scale: 2,
          useCORS: true,
        });

        const imgData = canvas.toDataURL("image/png");
        const pdf = new jsPDF("p", "mm", "a4");
        const imgWidth = 210;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
        pdf.save(`销售出货单_${this.orderInfo?.orderNum}.pdf`);
      } catch (error) {
        this.$message.error("生成PDF失败");
        console.error(error);
      } finally {
        loading.close();
      }
    },
    getPrintStyles() {
      return `
        @page {
          size: auto;
          margin: 20mm; 
        }
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 40px 20px 0 20px;
        }
        .print-content {
          width: 100%;
          max-width: 1000px;
          margin: 0 auto;
        }
        .header {
          text-align: center;
          margin-bottom: 5px;
          position: relative;
          
        }
        .header h2 {
          margin: 0;
          font-size: 20px;
          
        }
        .date {
          margin-top: 10px;
          margin-left: auto;
          position: absolute;
          top: 0;
          right: 0;
          .label{
            font-weight: bold;
          }
        }
        .info-section {
          margin-bottom: 5px;
        }
        .info-rows {
          display: flex;
          justify-content: space-between;
        }
        .info-row {
          margin-bottom: 5px;
        }
        .label {
          font-weight: bold;
        }
        .goods-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 5px;
        }
        .goods-table th,
        .goods-table td {
          border: 1px solid #000;
          text-align: center;
        }
        .goods-table th {
        }
        .footer {
        }
        .remark {
          margin-bottom: 5px;
          display: flex;
        }
        .remark .label {
          font-weight: bold;
          // margin-right: 10px;
          // padding-top: 8px;
        }
        .remark .value {
          flex: 1;
          min-width: 0;
          line-height: 1.5;
          font-family: Arial, sans-serif;
        }
        // .remark .value input {
        //   width: 100%;
        //   border: none;
        //   padding: 8px 0;
        //   background: transparent;
        //   line-height: 2;
        //   resize: none;
        //   font-family: Arial, sans-serif;
        // }
        .signature {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }
        .signature-item {
          width: 400px;
        }
        .tips {
          p {
            margin: 0px;
          }
        }
        @media print {
          @page {
            margin: 0;
            
          }
          body {
            margin: 0;
          }
          .no-print {
            display: none;
          }
          .print-content {
            margin: 0;
          }
        }
      `;
    },
    /**
     * 转换suk属性
     * @param item
     * @returns {string}
     */
    getSaleAttrVals(item) {
      let result = "";
      const saleAttrVals = item?.saleSkuInfo?.saleAttrVals || [];
      if (saleAttrVals.length > 0) {
        saleAttrVals.forEach((item, index) => {
          result += item.val;
          if (index < saleAttrVals.length - 1) {
            result += " \/ ";
          }
        });
      }
      return result;
    },
    /**
     * 数字转中文
     * @param num
     * @returns {string}
     */
    convertToChinese(num) {
      if (!num) return "";
      // 将字符串转换为数字
      const amount = parseFloat(num);
      if (isNaN(amount)) return "";

      // 定义数字对应的中文
      const chineseNum = [
        "零",
        "壹",
        "贰",
        "叁",
        "肆",
        "伍",
        "陆",
        "柒",
        "捌",
        "玖",
      ];
      const chineseUnit = ["", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿"];
      const chineseDecimal = ["角", "分"];

      // 处理整数部分
      const integerPart = Math.floor(amount);
      const decimalPart = Math.round((amount - integerPart) * 100);

      let result = "";

      // 处理整数部分
      if (integerPart > 0) {
        const str = integerPart.toString();
        let zeroCount = 0;
        let unitIndex = str.length - 1;
        for (let i = 0; i < str.length; i++) {
          const digit = parseInt(str[i]);
          const unit = chineseUnit[unitIndex];
          if (digit === 0) {
            zeroCount++;
          } else {
            if (zeroCount > 0) {
              result += chineseNum[0];
              zeroCount = 0;
            }
            result += chineseNum[digit] + unit;
          }
          unitIndex--;
        }
        result += "元";
      }

      // 处理小数部分
      if (decimalPart > 0) {
        const jiao = Math.floor(decimalPart / 10);
        const fen = decimalPart % 10;

        if (jiao > 0) {
          result += chineseNum[jiao] + chineseDecimal[0];
        }
        if (fen > 0) {
          result += chineseNum[fen] + chineseDecimal[1];
        }
      } else {
        result += "整";
      }
      // 处理连续的零
      result = result
        .replace(/零+/g, "零")
        .replace(/零+$/, "")
        .replace(/零元$/, "元整");
      return result;
    },
  },
};
</script>

<style lang="scss" scoped>
.print-content {
  //padding: 20px;
  background: #fff;
}

.header {
  text-align: center;
  margin-bottom: 5px;
  position: relative;

  h2 {
    margin: 0;
    font-size: 20px;
  }

  .date {
    margin-top: 10px;
    margin-left: auto;
    position: absolute;
    top: 0;
    right: 0;
    .label {
      font-weight: bold;
    }
  }
}

.info-section {
  margin-bottom: 5px;
  .info-rows {
    display: flex;
    justify-content: space-between;
  }
  .info-row {
    margin-bottom: 5px;

    .label {
      font-weight: bold;
    }
  }
}

.table-section {
  margin-bottom: 5px;
}

.goods-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 5px;

  th,
  td {
    border: 1px solid #000;
    padding: 4px;
    text-align: center;
  }

  th {
    //background-color: #f5f5f5;
  }

  .total-row {
    td {
      font-weight: bold;
    }
  }
}

.footer {
  .remark {
    display: flex;
    margin-bottom: 5px;
    align-items: center;

    .label {
      font-weight: bold;
      //margin-right: 10px;
      //padding-top: 8px;
    }
    .value {
      flex: 1;
      min-width: 0;
      font-family: Arial, sans-serif;
      line-height: 1.5;
    }
    //::v-deep(.value) {
    //  flex: 1;
    //  min-width: 0;
    //  color: #606266;
    //  font-family: Arial, sans-serif;
    //
    //  .el-input {
    //    input {
    //      width: 100%;
    //      border: none;
    //      padding: 8px 0;
    //      background: transparent;
    //      //font-size: 14px;
    //      line-height: 1.5;
    //      resize: none;
    //      font-family: Arial, sans-serif;
    //    }
    //  }
    //}
  }

  .signature {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  .signature-item {
    width: 400px;

    .label {
      font-weight: bold;
    }
  }

  .tips {
    font-weight: bold;
  }
}

.dialog-footer {
  text-align: right;
  margin-top: 40px;
  .el-button {
    margin-left: 10px;
  }
}
</style>
