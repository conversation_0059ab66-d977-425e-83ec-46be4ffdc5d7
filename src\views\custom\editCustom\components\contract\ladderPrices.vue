<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-06-27 10:58:07
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-30 14:03:51
 * @Description: 
 -->
<template>
  <div class="ladder-prices" @click.stop>
    <!-- 顶部配置区域 -->
    <el-row :gutter="24" class="config-row">
      <el-col :span="6">
        <el-form-item label="核算方式">
          <el-select
            v-model="infoData.accountMode"
            class="full-width"
            :disabled="editType === 'info'"
          >
            <el-option
              v-for="item in accountModeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 添加按钮 -->
    <el-button
      v-if="editType !== 'info'"
      type="primary"
      size="mini"
      class="add-button"
      @click="addPriceRange"
    >
      添加阶梯价格
    </el-button>

    <!-- 阶梯价格列表 -->
    <div class="ladder-list">
      <el-row :gutter="24">
        <el-col
          v-for="(item, index) in infoData.repairMonthlyPrices"
          :key="index"
          :span="24"
          class="ladder-col"
        >
          <div class="ladder-item">
            <el-row :gutter="12" class="price-row" type="flex" align="middle">
              <el-col :span="6">
                <el-form-item label="色彩类型：">
                  <el-select
                    v-model="item.colorType"
                    size="small"
                    class="full-width"
                    :disabled="editType === 'info'"
                  >
                    <el-option
                      v-for="color in colorOptions"
                      :key="color.value"
                      :label="color.label"
                      :value="color.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="数量范围：">
                  <div class="range-input">
                    <el-input-number
                      v-model="item.startCount"
                      :min="0"
                      :controls="false"
                      placeholder="起始数量"
                      size="small"
                      :disabled="editType === 'info'"
                    />
                    <span class="range-separator">-</span>
                    <el-input-number
                      v-model="item.endCount"
                      :min="item.startCount"
                      :controls="false"
                      placeholder="结束数量"
                      size="small"
                      :disabled="editType === 'info'"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="单价：">
                  <el-input
                    v-model="item.price"
                    type="number"
                    :min="0"
                    :placeholder="getColorLabel(item.colorType) + '单价'"
                    size="small"
                    :disabled="editType === 'info'"
                  >
                    <template #suffix>元/张</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="4" class="operation-col">
                <el-button
                  v-if="editType !== 'info'"
                  type="text"
                  class="delete-btn"
                  icon="el-icon-delete"
                  size="small"
                  @click="deletePriceRange(index)"
                >
                  删除
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: "LadderPrices",
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    editType: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      accountModeOptions: [
        {
          label: "统一计价",
          value: "UNIFY",
        },
        {
          label: "分段计价",
          value: "FLOAT",
        },
      ],
      colorOptions: [
        { label: "黑白", value: "1702" },
        { label: "彩色", value: "1701" },
        { label: "黑白+彩色", value: "1700" },
        { label: "第五色", value: "1703" },
      ],
    };
  },
  computed: {
    infoData: {
      get() {
        const value = this.value;
        if (value.repairMonthlyPrices && value.repairMonthlyPrices.length > 0) {
          value.repairMonthlyPrices = value.repairMonthlyPrices.map((item) => {
            Object.keys(item).forEach((key) => {
              if (
                typeof item[key] === "object" &&
                item[key] !== null &&
                item[key].label !== undefined
              ) {
                item[key] = item[key].value;
              }
            });
            return item;
          });
        }
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  created() {
    // 设置默认值
    const defaultValues = {
      priceType: "LADDER",
      accountMode: "UNIFY",
      repairMonthlyPrices: [],
    };

    // 合并默认值和传入的值
    this.$emit("input", {
      ...defaultValues,
      ...this.value,
      repairMonthlyPrices: Array.isArray(this.value.repairMonthlyPrices)
        ? this.value.repairMonthlyPrices
        : defaultValues.repairMonthlyPrices,
    });
  },

  methods: {
    getColorLabel(colorType) {
      const colorMap = {
        1702: "黑白",
        1701: "彩色",
        1703: "第五色",
        1700: "黑白+彩色",
      };
      return colorMap[colorType] || "";
    },
    addPriceRange() {
      if (!this.infoData.repairMonthlyPrices) {
        this.infoData.repairMonthlyPrices = [];
      }
      this.infoData.repairMonthlyPrices.push({
        colorType: "1702",
        startCount: 0,
        endCount: 0,
        price: 0,
      });
    },

    deletePriceRange(index) {
      this.infoData.repairMonthlyPrices.splice(index, 1);
    },
  },
};
</script>

<style scoped lang="scss">
.ladder-prices {
  width: 100%;
  display: inline-block;
  margin-bottom: 16px;

  .config-row {
    margin-bottom: 16px;
  }

  .ladder-list {
    .ladder-item {
      padding: 12px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border-bottom: 1px solid #ebeef5;
      //margin-bottom: 16px;
      .ladder-header {
        .el-divider {
          margin: 8px 0;
        }
      }
      .ladder-col {
        margin-bottom: 16px;

        &:nth-last-child(-n + 2) {
          margin-bottom: 0;
        }
      }
    }
  }
  .operation-col {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .delete-btn {
    color: #f56c6c;
    padding: 0;
    font-size: 14px;
  }

  .range-input {
    display: flex;
    align-items: center;

    .range-separator {
      margin: 0 8px;
      color: #909399;
    }

    :deep(.el-input-number) {
      .el-input__inner {
        padding-right: 8px;
      }
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 0; // 移除表单项底部边距
  }

  :deep(.el-input),
  :deep(.el-input-number),
  :deep(.el-select) {
    width: 100%;
  }

  .full-width {
    width: 100%;
  }
  &:last-child {
    margin-bottom: 0;
  }
}
.add-button {
  margin-bottom: 16px;
}

.delete-btn {
  float: right;
  padding: 0;
  font-size: 14px;
}
</style>
