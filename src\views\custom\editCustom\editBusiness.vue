<template>
  <div class="edit-business">
    <ProForm
      ref="ProForm"
      :form-param="formParam"
      :form-list="columns"
      :confirm-loading="formLoading"
      :layout="{ formWidth: '100%', labelWidth: '140px' }"
      :open-type="type"
      @proSubmit="formSubmit"
    >
      <template #openBillType="{ row }">
        {{ row.openBillType == 1 ? "增值税普通发票" : "增值税专用发票" }}
      </template>
      <template #financeAccount="{ row }">
        {{
          row.financeAccount == 1
            ? "单店私账支付"
            : row.financeAccount == 2
            ? "多店私账支付"
            : row.financeAccount == 3
            ? "单店对公支付"
            : "多店对公支付"
        }}
      </template>
      <template #settleMethod="{ row }">
        {{
          row.settleMethod == 1
            ? "私转现结"
            : row.settleMethod == 2
            ? "公转现接"
            : row.settleMethod == 3
            ? "私转月付"
            : row.settleMethod == 4
            ? "公转月付"
            : "预充抵扣"
        }}
      </template>
      <template #bankCardImg>
        <ProUpload
          :file-list="formParam.bankCardImg"
          :type="type"
          :limit="3"
          @uploadSuccess="handleUploadSuccess"
          @uploadRemove="handleUploadRemove"
        />
        <span v-if="type !== 'info'">
          仅支持上传png、jpg格式且10M大小内的图片。
        </span>
      </template>
      <template #licenseImg>
        <ProUpload
          :file-list="formParam.licenseImg"
          :type="type"
          :limit="3"
          @uploadSuccess="handleLicenseImgUploadSuccess"
          @uploadRemove="handleLicenseImgUploadRemove"
        />
        <span v-if="type !== 'info'">
          仅支持上传png、jpg格式且10M大小内的图片。
        </span>
      </template>
      <template #idCardImg>
        <ProUpload
          :file-list="formParam.idCardImg"
          :type="type"
          :limit="2"
          @uploadSuccess="handleCardImgUploadSuccess"
          @uploadRemove="handleCardImgUploadRemove"
        />
        <span v-if="type !== 'info'">
          仅支持上传png、jpg格式且10M大小内的图片。
        </span>
      </template>
      <template #qualificationImg>
        <ProUpload
          :file-list="formParam.qualificationImg"
          :type="type"
          :limit="2"
          @uploadSuccess="handleQualificationImgUploadSuccess"
          @uploadRemove="handleQualificationImgUploadRemove"
        />
        <span v-if="type !== 'info'">
          仅支持上传png、jpg格式且10M大小内的图片。
        </span>
      </template>
    </ProForm>

    <div v-if="type !== 'info'" class="dialog-footer1">
      <div class="btn-box">
        <el-button type="primary" @click="handleOk">保存</el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import ProForm from "@/components/ProForm/index.vue";
import { Message } from "element-ui";
import {
  getCustomerBusinessInfoApi,
  addCustomerBusinessApi,
  updateCustomerBusinessApi,
  getGroupListApi,
} from "@/api/customer";
import ProUpload from "@/components/ProUpload/index.vue";
import { dictTreeByCodeApi, roleMemberApi, userListApi } from "@/api/user";
export default {
  name: "EditBusiness",
  components: { ProForm, ProUpload },
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
  },
  data() {
    return {
      formParam: {},
      columns: [
        {
          dataIndex: "license",
          title: "营业执照名称",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
        },
        {
          dataIndex: "creditCode",
          title: "统一信用代码",
          isForm: true,
          clearable: true,
          formSpan: 8,
          valueType: "input",
        },
        // 原开票类型 现数据库
        // {
        //   dataIndex: "tickeType",
        //   title: "开票类型",
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请输入开票类型",
        //       trigger: "change",
        //     },
        //   ],
        //   formSpan: 8,
        // },
        {
          dataIndex: "openBillType",
          title: "开票类型",
          isForm: true,
          valueType: "select",
          option: [
            {
              value: 1,
              label: "增值税普通发票",
            },
            {
              value: 2,
              label: "增值税专用发票",
            },
          ],
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "invoiceAddr",
          title: "开票地址",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "account",
          title: "账户名",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "bank",
          title: "开户银行",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "bankClient",
          title: "开户网点",
          valueType: "input",
          isForm: true,
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "bankAccount",
          title: "银行账号",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "finance",
          title: "财务姓名",
          isForm: true,
          valueType: "input",
          clearable: true,
          prop: [],
          formSpan: 8,
        },
        {
          dataIndex: "financeTel",
          title: "财务电话",
          isForm: true,
          valueType: "input",
          clearable: true,
          prop: [],
          formSpan: 8,
        },
        {
          dataIndex: "billEmail",
          title: "收票邮箱",
          isForm: true,
          valueType: "input",
          clearable: true,
          prop: [],
          formSpan: 8,
        },
        {
          dataIndex: "salesmanId",
          title: "销售人员",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
        },
        {
          dataIndex: "businessmanId",
          title: "技术支持",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 8,
          option: [],
        },
        // {
        //   dataIndex: "groupId",
        //   title: "关联集团",
        //   isForm: true,
        //   clearable: true,
        //   formSpan: 8,
        //   valueType: "select",
        //   option: [],
        //   optionMth: () => getGroupListApi(),
        //   optionskey: {
        //     label: "name",
        //     value: "id",
        //   },
        // },
        {
          dataIndex: "settleMethod",
          title: "结算方式",
          isForm: true,
          valueType: "select",
          optionMth: () => dictTreeByCodeApi(4200),
          option: [
            // {
            //   value: 1,
            //   label: "现结",
            // },
            // {
            //   value: 2,
            //   label: "月付",
            // },
            // {
            //   value: 3,
            //   label: "第三方支付",
            // },
            // {
            //   value: 4,
            //   label: "预充抵扣",
            // },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
          formSpan: 8,
        },
        {
          dataIndex: "financeAccount",
          title: "财务核算",
          isForm: true,
          valueType: "select",
          option: [
            {
              value: 1,
              label: "单店私账支付",
            },
            {
              value: 2,
              label: "多店私账支付",
            },
            {
              value: 3,
              label: "单店对公支付",
            },
            {
              value: 4,
              label: "多店对公支付",
            },
          ],
          clearable: true,
          formSpan: 8,
        },
        {
          dataIndex: "bankCardImg",
          title: "银行卡照片",
          formSlot: "bankCardImg",
          isForm: true,
          formSpan: 24,
        },
        {
          dataIndex: "licenseImg",
          title: "营业执照",
          isForm: true,
          formSpan: 24,
          formSlot: "licenseImg",
        },
        {
          dataIndex: "idCardImg",
          title: "法人身份证",
          isForm: true,
          formSpan: 24,
          formSlot: "idCardImg",
        },
        {
          dataIndex: "qualificationImg",
          title: "纳税资格证明",
          isForm: true,
          formSpan: 24,
          formSlot: "qualificationImg",
        },
      ],
      formLoading: false,
      businessType: "add",
      showPreviewImg: false,
      previewImgUrl: "",
      saleList: [], //销售人员列表
      workerList: [], // 技术支持
      commercialPersonnel: [], // 商务人员列表
    };
  },
  async mounted() {
    await this.loadData();
    await this.opratortList();
  },
  methods: {
    // 员工列表处理
    opratortList() {
      const fetchRoleMembers = (roleId, targetList, dataIndex) => {
        return roleMemberApi(roleId, { pageNumber: 1, pageSize: 9999 }).then(
          (res) => {
            res.data.rows.map((item) => {
              targetList.push({
                value: item.id,
                label: item.name,
              });
            });
            this.columns.forEach((column) => {
              if (column.dataIndex === dataIndex) {
                column.option = targetList;
              }
            });
          }
        );
      };

      // 获取销售人员列表
      fetchRoleMembers("1787125965588070402", this.saleList, "salesmanId");

      // 获取技术支持人员列表
      fetchRoleMembers("1002", this.workerList, "businessmanId");
    },
    async formSubmit(val) {
      try {
        const editApi =
          this.businessType === "add"
            ? addCustomerBusinessApi
            : updateCustomerBusinessApi;
        this.formLoading = true;
        const args = {
          ...val,
          customerId: this.id,
        };

        if (args.bankCardImg) {
          args.bankCardImg = args.bankCardImg[0] ? args.bankCardImg[0] : {};
        }
        const result = await editApi(args);
        if (result.code === 200) {
          // this.loadData()
          this.handleClose();
          Message.success("保存成功");
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.formLoading = false;
      }
    },
    async loadData() {
      try {
        this.formLoading = true;
        if (!this.id) throw new Error({ message: "id不能为空" });
        const result = await getCustomerBusinessInfoApi(this.id);
        if (result.code !== 200) throw new Error(result);
        if (result.data) {
          const { data } = result;
          if (
            data.bankCardImg &&
            !Array.isArray(data.bankCardImg) &&
            Object.keys(data.bankCardImg).length !== 0
          ) {
            data.bankCardImg = new Array(data.bankCardImg);
          } else {
            data.bankCardImg = [];
          }

          this.formParam = data;
          this.businessType = "edit";
        } else {
          this.businessType = "add";
          // this.formParam = {
          //   bankCardImg: [],
          //   licenseImg: [],
          //   idCardImg: [],
          //   qualificationImg: [],
          // };
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.formLoading = false;
      }
    },
    handleOk() {
      // this.formParam
      if (this.formParam.visitTime) {
        this.formParam.visitTime = this.$moment(
          this.formParam.visitTime
        ).format("YYYY-MM-DD HH:MM:SS");
      }
      if (this.formParam.regPlatTime) {
        this.formParam.regPlatTime = this.$moment(
          this.formParam.regPlatTime
        ).format("YYYY-MM-DD HH:MM:SS");
      }
      if (this.formParam.signingTime) {
        this.formParam.signingTime = this.$moment(
          this.formParam.signingTime
        ).format("YYYY-MM-DD HH:MM:SS");
      }
      this.$refs.ProForm.handleSubmit();
    },
    handleClose() {
      this.$emit("closeDrawer");
    },
    handleUploadSuccess(val) {
      if (!this.formParam.bankCardImg) {
        this.$set(this.formParam, "bankCardImg", []);
      }
      this.formParam.bankCardImg.push(val);
    },
    handleUploadRemove(file) {
      const index = this.formParam.bankCardImg.findIndex(
        (val) => val.key === file.key
      );
      this.formParam.bankCardImg.splice(index, 1);
    },
    handleLicenseImgUploadSuccess(result) {
      if (!this.formParam.licenseImg) {
        this.$set(this.formParam, "licenseImg", []);
      }
      this.formParam.licenseImg.push(result);
    },
    handleLicenseImgUploadRemove(file) {
      const index = this.formParam.licenseImg.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParam.licenseImg.splice(index, 1);
    },
    handleCardImgUploadSuccess(result) {
      if (!this.formParam.idCardImg) {
        this.$set(this.formParam, "idCardImg", []);
      }
      this.formParam.idCardImg.push(result);
    },
    handleCardImgUploadRemove(file) {
      const index = this.formParam.idCardImg.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParam.idCardImg.splice(index, 1);
    },
    handleQualificationImgUploadSuccess(result) {
      if (!this.formParam.qualificationImg) {
        this.$set(this.formParam, "qualificationImg", []);
      }
      this.formParam.qualificationImg.push(result);
    },
    handleQualificationImgUploadRemove(file) {
      const index = this.formParam.qualificationImg.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParam.qualificationImg.splice(index, 1);
    },
  },
};
</script>
<style lang="less" scoped></style>
