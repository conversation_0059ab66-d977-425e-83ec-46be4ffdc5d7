import { get, PageResponse } from './api';
import { 
  WebsitePublicContentVo, 
  WebsitePublicImageVo, 
  WebsiteHomepageVo,
  WebsiteContentType,
  WebsiteImageCategory,
  SeoMeta
} from '../types/api';

/**
 * 网站前端展示API服务类
 * 对应后端 WebsitePublicController 的所有接口
 */
export class WebsitePublicApi {
  
  /**
   * 获取首页数据
   * GET /api/website-public/homepage
   */
  static async getHomepage(): Promise<WebsiteHomepageVo> {
    const response = await get<WebsiteHomepageVo>('/website-public/homepage');
    return response.data;
  }

  /**
   * 根据类型获取内容
   * GET /api/website-public/content/{type}
   */
  static async getContentByType(type: WebsiteContentType): Promise<WebsitePublicContentVo> {
    console.log(`🚀 API调用: /website-public/content/${type}`);
    const response = await get<WebsitePublicContentVo>(`/website-public/content/${type}`);
    console.log(`✅ API响应:`, response);
    return response.data;
  }

  /**
   * 获取服务介绍页面数据
   * GET /api/website-public/services
   */
  static async getServices(): Promise<WebsitePublicContentVo> {
    const response = await get<WebsitePublicContentVo>('/website-public/services');
    return response.data;
  }

  /**
   * 获取案例展示页面数据
   * GET /api/website-public/cases
   */
  static async getCases(): Promise<WebsitePublicContentVo[]> {
    const response = await get<WebsitePublicContentVo[]>('/website-public/cases');
    return response.data;
  }

  /**
   * 获取关于我们页面数据
   * GET /api/website-public/about
   */
  static async getAbout(): Promise<WebsitePublicContentVo> {
    const response = await get<WebsitePublicContentVo>('/website-public/about');
    return response.data;
  }

  /**
   * 获取联系我们页面数据
   * GET /api/website-public/contact
   */
  static async getContact(): Promise<WebsitePublicContentVo> {
    const response = await get<WebsitePublicContentVo>('/website-public/contact');
    return response.data;
  }

  /**
   * 根据ID获取内容详情
   * GET /api/website-public/content/detail/{id}
   */
  static async getContentDetail(id: number): Promise<WebsitePublicContentVo> {
    const response = await get<WebsitePublicContentVo>(`/website-public/content/detail/${id}`);
    return response.data;
  }

  /**
   * 根据分类获取图片列表
   * GET /api/website-public/images/{category}
   */
  static async getImagesByCategory(
    category: WebsiteImageCategory,
    params?: {
      current?: number;
      size?: number;
    }
  ): Promise<PageResponse<WebsitePublicImageVo>> {
    const response = await get<PageResponse<WebsitePublicImageVo>>(
      `/website-public/images/${category}`,
      { params }
    );
    return response.data;
  }

  /**
   * 获取公开图片列表
   * GET /api/website-public/images
   */
  static async getPublicImages(params?: {
    current?: number;
    size?: number;
    category?: WebsiteImageCategory;
  }): Promise<PageResponse<WebsitePublicImageVo>> {
    const response = await get<PageResponse<WebsitePublicImageVo>>(
      '/website-public/images',
      { params }
    );
    return response.data;
  }



  /**
   * 获取图片画廊数据
   * GET /api/website-public/gallery
   */
  static async getGallery(params?: {
    current?: number;
    size?: number;
  }): Promise<{
    images: PageResponse<WebsitePublicImageVo>;
    seoMeta: SeoMeta;
  }> {
    const response = await get<{
      images: PageResponse<WebsitePublicImageVo>;
      seoMeta: SeoMeta;
    }>('/website-public/gallery', { params });
    return response.data;
  }

  /**
   * 获取团队介绍数据
   * GET /api/website-public/team
   */
  static async getTeam(): Promise<{
    content: WebsitePublicContentVo;
    images: WebsitePublicImageVo[];
    seoMeta: SeoMeta;
  }> {
    const response = await get<{
      content: WebsitePublicContentVo;
      images: WebsitePublicImageVo[];
      seoMeta: SeoMeta;
    }>('/website-public/team');
    return response.data;
  }

  /**
   * 获取SEO信息
   * GET /api/website-public/seo/{type}
   */
  static async getSeoMeta(type: WebsiteContentType): Promise<SeoMeta> {
    const response = await get<SeoMeta>(`/website-public/seo/${type}`);
    return response.data;
  }

  /**
   * 增加页面浏览量
   * POST /api/website-public/view/{id}
   */
  static async incrementViewCount(id: number): Promise<void> {
    await get(`/website-public/view/${id}`, { showError: false });
  }

  /**
   * 获取网站导航菜单
   * GET /api/website-public/menus
   */
  static async getMenus(): Promise<{ key: string; label: string; path: string }[]> {
    const response = await get<{ key: string; label: string; path: string }[]>('/website-public/menus');
    // 修复：直接从response.data获取数据
    return response.data;
  }

  /**
   * 获取敏感配置的密文值（前端安全传输用）
   * 与react-modules项目API保持完全一致
   * GET /api/website-config/sensitive/{configKey}
   */
  static async getSensitiveConfigEncrypted(configKey: string): Promise<string | null> {
    try {
      const response = await get<string>(`/website-config/sensitive/${configKey}`);
      // 处理RestResponse格式
      if (response.data && typeof response.data === 'object' && 'data' in response.data) {
        return (response.data as any).data || null;
      }
      return response.data || null;
    } catch (error: any) {
      // 获取敏感配置失败，静默处理
      return null;
    }
  }
}

// 导出单例实例
export const websitePublicApi = WebsitePublicApi; 