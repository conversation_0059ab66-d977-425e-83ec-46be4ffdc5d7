
<template>
  <div class="ht-progress-2">
    <div class="relative svg-content" ref="SvgProgressRef">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        :width="`${r * 2}px`"
        :height="`${r * 2}px`"
        class="middle"
      >
        <defs>
          <linearGradient id="PSgrad_0" x1="0%" x2="86.603%" y1="50%" y2="0%">
            <stop offset="0%" stop-color="rgb(255,222,109)" stop-opacity="1" />
            <stop offset="100%" stop-color="rgb(81,192,255)" stop-opacity="1" />
          </linearGradient>
        </defs>
        <circle
          :cx="r"
          :cy="r"
          :r="r"
          stroke="#04313c"
          stroke-width="1px"
          opacity=".9"
          fill="#030b0f"
          stroke-linecap="butt"
          stroke-linejoin="miter"
        ></circle>
        <circle
          :cx="r"
          :cy="r"
          :r="gr - 15"
          fill-rule="evenodd"
          stroke="rgb(238, 238, 238,.3)"
          stroke-width="1px"
          stroke-dasharray="3"
          stroke-linecap="butt"
          stroke-linejoin="miter"
          fill-opacity="0.361"
          fill="none"
        ></circle>
        <circle
          :cx="r"
          :cy="r"
          :r="gr - 15"
          fill-rule="evenodd"
          stroke="#030b0f"
          stroke-width="3px"
          :stroke-dasharray="`calc(${gr - 15} * 3.1415926 * 1/2)`"
          stroke-linecap="butt"
          stroke-linejoin="miter"
          fill-opacity="1"
          fill="none"
        ></circle>
        <circle
          :cx="r"
          :cy="r"
          :r="gr"
          fill-rule="evenodd"
          stroke="rgb(4, 49, 60)"
          stroke-width="1.5px"
          stroke-linecap="butt"
          stroke-linejoin="miter"
          fill="none"
        ></circle>
        <circle
          :cx="r"
          :cy="r"
          :r="r - 15"
          stroke="rgb(4, 49, 60)"
          stroke-width="7px"
          opacity=".9"
          fill="rgba(255,255,255,0)"
          stroke-linecap="butt"
          stroke-linejoin="miter"
        ></circle>

        <circle
          :cx="r"
          :cy="r"
          :r="r - 15"
          stroke="url(#PSgrad_0)"
          stroke-width="7px"
          opacity=".9"
          :stroke-dasharray="
            ((r - 15) * 3.1415926 * 2) / 90 +
            ' ' +
            ((r - 15) * 3.1415926 * 2) / 45
          "
          fill="rgba(255,255,255,0)"
          stroke-linecap="butt"
          stroke-linejoin="miter"
        ></circle>
      </svg>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        class="val-svg middle"
        :width="`${gr * 2}`"
        :height="`${gr * 2}`"
      >
        <circle
          :cx="`${gr}`"
          :cy="`${gr}`"
          :r="`${gr}`"
          fill="transparent"
          stroke="rgba(4,49,60,0.3)"
          :stroke-width="`${gr * 2}`"
          :stroke-dasharray="`calc(${gr * 2} * 3.1415926)`"
          :stroke-dashoffset="`calc(${gr * 2} * 3.1415926 / 360 * (360 - ${
            3.6 * (resultData ? resultData.progress || 0 : progress)
          }) )`"
        ></circle>
        <circle
          :cx="`${gr}`"
          :cy="`${gr}`"
          :r="`${gr - 4}`"
          fill="transparent"
          stroke="url(#PSgrad_0)"
          :stroke-width="r / 30 > 7 ? r / 30 : 7"
          opacity="0.8"
          :stroke-dasharray="`calc(${gr * 2} * 3.1415926)`"
          :stroke-dashoffset="`calc(${gr * 2} * 3.1415926 / 360 * (360 - ${
            3.6 * (resultData ? resultData.progress || 0 : progress)
          }) ) `"
        ></circle>
      </svg>
      <div class="middle text">
        <h2
          :style="{
            color,
            fontFamily,
            fontSize: fontSize ? fontSize : defaultFontSize + 'px',
          }"
        >
          {{ resultData ? resultData.val || 0 : val }}
        </h2>
        <p
          :style="{
            color: unitColor,
            fontSize: (fontSize ? fontSize : defaultFontSize) * 0.6 + 'px',
          }"
        >
          {{ resultData ? resultData.unit : unit }}
        </p>
      </div>
    </div>
    <p
      class="category-text"
      :style="{
        fontFamily: categoryFamily,
        color: categoryColor,
        fontSize: categorySize,
      }"
    >
      {{ text }}
    </p>
  </div>
</template>
<script>
export default {
  name: "",
  components: {},
  props: {
    color: {
      default: "#64e4ff",
    },
    val: {
      default: "652.45",
    },
    text: {
      default: "当前负载率",
    },
    unit: {
      default: "%",
    },
    unitColor: {
      default: "#ffffff",
    },
    fontSize: {
      default: "",
    },
    unitSize: {
      default: "",
    },
    fontFamily: {
      default: "euro technic",
    },
    categoryFamily: {
      default: "时尚中黑简体",
    },
    categorySize: {
      default: "16px",
    },
    categoryColor: {
      default: "#ffffff",
    },
    progress: {
      default: 90,
    },
    resultData: {
      default: () => {
        return {
          // val: 45,
          // unit: "%",
          // progress: 45
        };
      },
    },
  },

  data() {
    return {
      SvgProgressRef: "",
      defaultFontSize: 16,
      r: 80,
      gr: 64,
    };
  },
  computed: {},
  watch: {
    val: {
      handler: function (oldval, newval) {
        let size = ((this.r - 40) * 2) / 5.2;
        let textWidth = this.val.toString().length;
        this.defaultFontSize =
          size * textWidth > this.r * 2 - 20
            ? parseInt((this.r * 2 - 20) / textWidth)
            : size;
      },
    },
    immediate: true,
    deep: true,

  },
  updated(){
  if (this.SvgProgressRef) {
    let width = this.SvgProgressRef.clientWidth;
    let height = this.SvgProgressRef.clientHeight;
    this.r = (width > height ? parseInt(height / 2) : parseInt(width / 2)) - 2;
    let rr = this.r < 100 ? this.r * 0.7 : this.r - 35;
    this.r = rr;
  }
},
  created() {},
  mounted() {
    if (this.SvgProgressRef) {
      const width = this.SvgProgressRef.clientWidth;
      const height = this.SvgProgressRef.clientHeight;
      this.r =
        (width > height ? parseInt(height / 2) : parseInt(width / 2)) - 2;
    }
  },
  destroyed() {},
  methods: {
    close() {
      this.$emit("close");
    },
  },
};
</script>

<!-- <script lang="ts" setup>
import {
  ref,
  onUpdated,
  defineProps,
  onMounted,
  defineEmits,
  watch,
} from "vue";
const props = defineProps({
  color: {
    default: "#64e4ff",
  },
  val: {
    default: "652.45",
  },
  text: {
    default: "当前负载率",
  },
  unit: {
    default: "%",
  },
  unitColor: {
    default: "#ffffff",
  },
  fontSize: {
    default: "",
  },
  unitSize: {
    default: "",
  },
  fontFamily: {
    default: "euro technic",
  },
  categoryFamily: {
    default: "时尚中黑简体",
  },
  categorySize: {
    default: "16px",
  },
  categoryColor: {
    default: "#ffffff",
  },
  progress: {
    default: 90,
  },
  resultData: {
    default: () => {
      return {
        // val: 45,
        // unit: "%",
        // progress: 45
      };
    },
  },
});
const emit = defineEmits(["handle"]);
const handle = () => {
  emit("handle", props.text);
};
const r = ref(80); //半径
const gr = ref(64); //进度搬家
const SvgProgressRef = ref();
const defaultFontSize = ref(16);
onUpdated(() => {
  if (this.SvgProgressRef) {
    let width = this.SvgProgressRef.clientWidth;
    let height = this.SvgProgressRef.clientHeight;
    this.r = (width > height ? parseInt(height / 2) : parseInt(width / 2)) - 2;
    let rr = this.r < 100 ? this.r * 0.7 : this.r - 35;
    gthis.r = rr;
  }
});
onMounted(() => {
  if (this.SvgProgressRef) {
    let width = this.SvgProgressRef.clientWidth;
    let height = this.SvgProgressRef.clientHeight;
    this.r = (width > height ? parseInt(height / 2) : parseInt(width / 2)) - 2;
  }
});
watch(
  [() => props.val, r],
  () => {
    let size = ((this.r - 40) * 2) / 5.2;
    let textWidth = props.val.toString().length;
    defaultFontSize.value =
      size * textWidth > this.r * 2 - 20
        ? parseInt((this.r * 2 - 20) / textWidth)
        : size;
  },
  {
    immediate: true,
  }
);
</script> -->

<style lang="scss" scoped>
.relative {
  position: relative;
}
.val-svg {
  border-radius: 50%;
  overflow: hidden;

  transform: translate(-50%, -50%) rotateZ(-90deg);
}
.text {
  text-align: center;
  h2 {
    line-height: 1.5em;
    font-weight: normal;
  }
}
.svg-content {
  height: calc(100% - 40px);
}

.category-text {
  text-align: center;
  color: #fff;
  position: relative;
  z-index: 9;
  font-size: 24px;
  line-height: 1;
  padding-top: 10px;
  font-family: "时尚中黑简体";
}
.ht-progress-2 {
  display: flex;
  flex-direction: column;
}
</style>
