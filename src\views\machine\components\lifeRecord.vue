<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:50
 * @Description: 零件寿命记录
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :local-pagination="localPagination"
      :query-param="queryParam"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <!--      <template #btn>-->
      <!--        <el-button-->
      <!--          type="success"-->
      <!--          size="mini"-->
      <!--          icon="el-icon-edit"-->
      <!--          @click="reModify"-->
      <!--          >改状态</el-button-->
      <!--        >-->
      <!--      </template>-->
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #status="{ row }"
        ><el-switch
          v-model="row.status"
          :active-value="1"
          :inactive-value="0"
          active-color="#13ce66"
          @change="(e) => handleChangeStatus(e, row)"
        >
        </el-switch>

        <!--        <el-select-->
        <!--          v-model="row.status"-->
        <!--          size="small"-->
        <!--          placeholder="请选择"-->
        <!--          @change="(e) => handleChangeStatus(e, row)"-->
        <!--        >-->
        <!--          <el-option-->
        <!--            v-for="item in statusOptions"-->
        <!--            :key="item.value"-->
        <!--            :label="item.label"-->
        <!--            :value="item.value"-->
        <!--          >-->
        <!--          </el-option>-->
        <!--        </el-select>-->
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-circle-check" @click="handleDetail(row)">
            详情
          </el-button>
        </div>
      </template>
    </ProTable>
    <!--  详情  -->
    <ProDrawer
      class="margin-top"
      :value="unfoldDrawer"
      size="40%"
      :title="drawerTitle"
      :top="'10%'"
      :no-footer="true"
      @cancel="closeDrawer"
    >
      <div class="order-border-box">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="品牌">{{
            partData?.brand
          }}</el-descriptions-item>
          <el-descriptions-item label="机型">{{
            partData.machine
          }}</el-descriptions-item>
          <el-descriptions-item label="设备组名称" span="2">{{
            partData.deviceGroup?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="合约类型">{{
            partData.treatyType?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="是否安装客户端">{{
            partData.regCliState == 0 ? "否" : "是"
          }}</el-descriptions-item>
          <el-descriptions-item label="零件名称">{{
            partData.articleName
          }}</el-descriptions-item>
          <el-descriptions-item label="物品编码">{{
            partData.articleCode
          }}</el-descriptions-item>
          <el-descriptions-item label="零件数量">{{
            partData.num
          }}</el-descriptions-item>
          <el-descriptions-item label="是否为PM件" span="2">{{
            partData.isPm ? "是" : "否"
          }}</el-descriptions-item>
          <el-descriptions-item label="本次维修类型">{{
            partData.currRepairType?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="上次维修类型">{{
            partData.lastRepairType?.label
          }}</el-descriptions-item>
          <el-descriptions-item label="本次更换日期">{{
            partData.currReplaceDate
          }}</el-descriptions-item>

          <el-descriptions-item label="上次更换日期">{{
            partData.lastReplaceDate
          }}</el-descriptions-item>
          <el-descriptions-item label="本次维修工单号">
            <el-link
              type="primary"
              @click="
                getOrderInfo(
                  partData.currWorkCode,
                  partData.currRepairType?.value
                )
              "
              >{{ partData.currWorkCode }}</el-link
            >
          </el-descriptions-item>
          <el-descriptions-item label="上次维修工单号">
            <el-link
              type="primary"
              @click="
                getOrderInfo(
                  partData.lastWorkCode,
                  partData.lastRepairType?.value
                )
              "
              >{{ partData.lastWorkCode }}</el-link
            >
          </el-descriptions-item>
          <el-descriptions-item label="间隔天数" span="2">
            {{ partData.invtervalDays }}天
          </el-descriptions-item>
          <el-descriptions-item label="起始黑白打印数">{{
            partData.blackWhiteInception
          }}</el-descriptions-item>
          <el-descriptions-item label="起始彩色打印数">{{
            partData.colorInception
          }}</el-descriptions-item>
          <el-descriptions-item label="截止黑白打印数">{{
            partData.blackWhiteCutoff
          }}</el-descriptions-item>
          <el-descriptions-item label="截止彩色打印数">{{
            partData.colorCutoff
          }}</el-descriptions-item>
          <el-descriptions-item label="黑白印量">{{
            partData.blackWhiteCount
          }}</el-descriptions-item>
          <el-descriptions-item label="彩色印量" span="2">{{
            partData.colorCount
          }}</el-descriptions-item>
          <el-descriptions-item label="总打印量">{{
            partData.totalCount
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </ProDrawer>
    <!-- 商品订单详情框  -->
    <ProDrawer
      :value="orderdialogVisible"
      :title="'商品订单详情'"
      size="60%"
      :top="'10%'"
      :no-footer="true"
      @cancel="orderdialogVisible = false"
    >
      <!-- 自修登记信息 -->
      <div v-if="selfOrderInfo" class="order-fix">
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="自修登记信息"
            :column="3"
            border
          >
            <el-descriptions-item label="设备编号">
              {{ selfOrderInfo?.deviceGroupId }}
            </el-descriptions-item>
            <el-descriptions-item label="工单号" span="2">
              {{ selfOrderInfo?.code }}
            </el-descriptions-item>
            <el-descriptions-item label="机型">
              {{ selfOrderInfo?.customerDeviceGroup.productInfo }}
            </el-descriptions-item>
            <el-descriptions-item label="设备组名称">
              {{ selfOrderInfo?.customerDeviceGroup.deviceGroup.label }}
            </el-descriptions-item>
            <el-descriptions-item label="设备组状态">
              {{ selfOrderInfo?.customerDeviceGroup.deviceStatus.label }}
            </el-descriptions-item>
            <el-descriptions-item label="维修人">
              {{ selfOrderInfo?.customerStaff.name }}
            </el-descriptions-item>
            <el-descriptions-item label="维修人手机号" span="2">
              {{ selfOrderInfo?.customerStaff.tel }}
            </el-descriptions-item>
            <el-descriptions-item label="黑白印量">
              {{ selfOrderInfo?.blackWhiteCount }}
            </el-descriptions-item>
            <el-descriptions-item label="彩色印量">
              {{ selfOrderInfo?.colorCount }}
            </el-descriptions-item>
            <el-descriptions-item label="上次维修到目前印量">
              {{ selfOrderInfo?.printCount }}
            </el-descriptions-item>
            <el-descriptions-item label="原因描述" span="3">
              {{ selfOrderInfo?.excDesc }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 商品信息 -->
        <div class="m-t-8">
          <p class="tit-box m-b-12">商品信息</p>
          <ProTable
            ref="ProSPXXTable"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="spxxColumns"
            :show-pagination="false"
            :show-loading="false"
            :data="selfOrderInfo.listReplaceDetails || []"
            :show-setting="false"
            :show-search="false"
            :show-table-operator="false"
            sticky
            :height="200"
          >
          </ProTable>
        </div>
      </div>
      <!-- 维修工单信息 -->
      <div v-if="workOrderInfo" class="order-fix">
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="维修工单信息"
            :column="3"
            border
          >
            <el-descriptions-item label="设备编号">
              {{ workOrderInfo?.deviceGroupId }}
            </el-descriptions-item>
            <el-descriptions-item label="工单号">
              {{ workOrderInfo?.code }}
            </el-descriptions-item>
            <el-descriptions-item label="合约类型">
              {{ workOrderInfo?.treatyType?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="机型">
              {{ workOrderInfo?.productInfo }}
            </el-descriptions-item>
            <el-descriptions-item label="设备组名称">
              {{ workOrderInfo?.deviceGroup?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="店铺名称">
              {{ workOrderInfo?.customer?.shopRecruitment }}
            </el-descriptions-item>
            <el-descriptions-item label="店铺名称">
              {{ workOrderInfo?.customer?.name }}
            </el-descriptions-item>
            <el-descriptions-item label="维修工程师">
              {{ workOrderInfo?.engineerId?.name }}
            </el-descriptions-item>
            <el-descriptions-item label="总金额（元）">
              {{ workOrderInfo?.totalPay ? workOrderInfo?.totalPay : 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="上门费用（元）">
              {{ workOrderInfo?.visitPay ? workOrderInfo?.visitPay : 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="维修费用（元）">
              {{ workOrderInfo?.repairPay ? workOrderInfo?.repairPay : 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="远途费（元）">
              {{
                workOrderInfo?.longWayVisitPay
                  ? workOrderInfo?.longWayVisitPay
                  : 0
              }}
            </el-descriptions-item>
            <el-descriptions-item label="换件费用（元）">
              {{ workOrderInfo?.replacePay ? workOrderInfo?.replacePay : 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="维修耗材费（元）">
              {{ workOrderInfo?.itemPay ? workOrderInfo?.itemPay : 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="工程师追加费用（元）">
              {{
                workOrderInfo?.engineerAdditionalPay
                  ? workOrderInfo?.engineerAdditionalPay
                  : 0
              }}
            </el-descriptions-item>
            <el-descriptions-item label="支付方式">
              {{ workOrderInfo?.payMode?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="原因描述" span="3">
              {{ workOrderInfo?.excDesc }}
            </el-descriptions-item>
            <el-descriptions-item label="故障码" span="3">
              {{ workOrderInfo?.errorCode }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <!-- 商品信息 -->
        <div class="m-t-8">
          <p class="tit-box m-b-12">故障照片</p>
          <div class="image-view">
            <div
              v-for="(item, index) in workOrderInfo?.excPics"
              :key="index"
              class="img-list"
            >
              <img :src="item.url" alt="" srcset="" />
              <div class="mask" @click="previewImg(item.url, index)"></div>
            </div>
          </div>
        </div>
      </div>
    </ProDrawer>

    <!-- 预览大图 -->
    <ProDialog
      :no-footer="true"
      :value="isShowPreviewImg"
      @cancel="isShowPreviewImg = false"
    >
      <div class="preview-img">
        <img :src="previewImgUrl" alt="" srcset="" />
      </div>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { productAllApi } from "@/api/dispose";
import { getLifeRecordApi, updateLifeStatisticsApi } from "@/api/machine";
import { Message } from "element-ui";
import {
  printChangeDetailListApi,
  printSelfDetailListApi,
  printWorkDetailListApi,
} from "@/api/statisics";

export default {
  name: "LifeRecord",
  data() {
    return {
      value: "",
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 160,
        },
        {
          dataIndex: "articleName",
          title: "零件名称",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 100,
        },
        {
          dataIndex: "location",
          title: "颜色位置",
          isTable: true,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "blackWhiteInception",
          title: "起始黑白计数器",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "blackWhiteCutoff",
          title: "截止黑白计数器",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "colorInception",
          title: "起始彩色计数器",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "colorCutoff",
          title: "截止彩色计数器",
          isTable: true,
          width: 120,
        },
        // {
        //   dataIndex: "replacing",
        //   title: "更换时总计数器",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "replaced",
        //   title: "被更换时总计数器",
        //   isTable: true,
        // },
        {
          dataIndex: "totalCount",
          title: "寿命",
          isTable: true,
        },
        {
          dataIndex: "currReplaceDate",
          title: "更换日期",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "status",
          title: "统计状态",
          isTable: true,
          align: "center",
          tableSlot: "status",
          width: 100,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: 1,
            },
            {
              label: "否",
              value: 0,
            },
          ],
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "machine",
          title: "机型",
          isTable: true,
          width: 110,
        },
        {
          dataIndex: "productIds",
          title: "系列",
          isSearch: true,
          clearable: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          fixed: "right",
          tableSlot: "actions",
        },
      ],
      tableData: [],
      productIdName: [],
      options: [],
      statusOptions: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      unfoldDrawer: false,
      drawerTitle: "",
      partData: {},
      isShowPreviewImg: false,
      previewImgUrl: "",
      orderdialogVisible: false,
      spxxColumns: [
        {
          dataIndex: "itemName",
          isTable: true,
          title: "商品名称",
        },
        {
          dataIndex: "itemId",
          isTable: true,
          title: "商品编号",
        },
        {
          dataIndex: "numberOem",
          isTable: true,
          title: "OEM编号",
          formatter: (row) => row.productPart?.oemNumber,
        },
        {
          dataIndex: "isPm",
          isTable: true,
          title: "PM件",
          formatter: (row) => (row.isPm ? "是" : "否"),
        },
        {
          dataIndex: "location",
          isTable: true,
          title: "零件位置",
        },

        {
          dataIndex: "num",
          isTable: true,
          title: "数量",
        },
      ],
      selfOrderInfo: null,
      workOrderInfo: null,
    };
  },
  mounted() {
    this.refresh();
    this.getProductThird();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      getLifeRecordApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
      // this.$refs.ProTable.listLoading = false;
    },
    // 修改状态
    reModify() {},
    handleDetail(row) {
      this.drawerTitle = `${row.articleName} - 更换详情`;
      printChangeDetailListApi(row.replacePartId).then((res) => {
        if (res.data) {
          this.partData = res.data;
        } else {
          this.partData = {};
        }
      });
      this.unfoldDrawer = true;
    },
    closeDrawer() {
      this.unfoldDrawer = false;
    },
    /**
     * @description 关联工单号订单详情
     * @param code 工单号
     * @param type 订单类型 SELF 自修 WORK工单
     */
    getOrderInfo(code, type) {
      this.selfOrderInfo = null;
      this.workOrderInfo = null;
      if (type === "SELF") {
        printSelfDetailListApi(code).then((res) => {
          this.selfOrderInfo = res.data;
          this.orderdialogVisible = true;
        });
      } else if (type === "WORK") {
        printWorkDetailListApi(code).then((res) => {
          this.workOrderInfo = res.data;
          this.orderdialogVisible = true;
        });
      }
    },
    // 预览大图
    previewImg(url, i) {
      this.previewImgUrl = url;
      this.isShowPreviewImg = true;
    },
    handleChangeStatus(val, row) {
      const params = {
        id: row.id,
        status: val,
      };
      updateLifeStatisticsApi(params).then((res) => {
        Message.success("状态修改成功");
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
      // this.$refs.ProTable.listLoading = false;
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        this.queryParam.productIds.push(el[el.length - 1]);
      });
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
      });
    },
  },
};
</script>

<style scoped lang="scss">
.image-view {
  width: 100%;
  padding: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  .img-list {
    width: 135px;
    height: 135px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    /* 鼠标经过有遮罩效果 */
    &:hover {
      position: relative;
      .mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &::after {
          content: "+";
          font-size: 36px;
          color: #fff;
        }
      }
    }
  }
}
.preview-img {
  width: 100%;
  height: 100%;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
