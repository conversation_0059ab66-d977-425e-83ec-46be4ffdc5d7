<!--
 * @Author: wskg
 * @Date: 2025-01-02 15:36:28
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:50
 * @Description: 创建机器销售单
 -->
<template>
  <div class="app-container">
    <ProDrawer
      :value="addDialog"
      :title="'创建机器销售单'"
      size="95%"
      :confirm-loading="confirmLoadings"
      :top="'10%'"
      :no-footer="true"
      @cancel="canceladdDialog"
    >
      <el-steps
        align-center
        :active="actives"
        finish-status="success"
        class="steps-box"
      >
        <el-step title="客户/机器选择"></el-step>
        <el-step title="订单预览"></el-step>
      </el-steps>
      <div v-show="actives === 0" style="margin-top: 30px">
        <div class="tit-boxs">
          <el-button type="success" @click="showDialogFn(0)">
            选择客户信息
          </el-button>
        </div>
        <el-form
          ref="proform_child1"
          :disabled="methodType === 'info'"
          label-width="140px"
          class="demo-ruleForm"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="客户编码:" prop="seqId">
                <el-input
                  v-model="calculateFormrr.seqId"
                  placeholder="请选择客户信息"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="店铺名称:" prop="name">
                <el-input
                  v-model="calculateFormrr.name"
                  placeholder="请选择客户信息"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="收货地址:" prop="addressId">
                <el-select
                  v-model="calculateFormrr.addressId"
                  style="width: 100%"
                  placeholder="请选择收货地址"
                  clearable
                  @change="handleAddressChange"
                >
                  <el-option
                    v-for="item in addressList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="联系人:" prop="consigneeName">
                <el-input
                  v-model="calculateFormrr.consigneeName"
                  placeholder="请输入联系人"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="联系电话:" prop="consigneePhone">
                <el-input
                  v-model="calculateFormrr.consigneePhone"
                  placeholder="请输入联系电话"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="销售人员:" prop="salesMan">
                <el-select
                  v-model="calculateFormrr.salesMan"
                  style="width: 100%"
                  placeholder="请选择销售人员"
                  clearable
                >
                  <el-option
                    v-for="item in salesManOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="tit-boxs" style="margin-bottom: 0">
          <!--<el-button type="success" @click="showDialogFn(1)">-->
          <!--  添加机器-->
          <!--</el-button>-->
          <el-button
            type="success"
            icon="el-icon-plus"
            @click="addOrderMachine"
          >
            添加机器
          </el-button>
        </div>
        <ProTable
          ref="ProTable2s"
          :show-setting="false"
          :show-loading="false"
          :columns="creatColumn4"
          :data="surenewArr"
          sticky
          :height="440"
        >
          <!-- 主机类型 -->
          <template #type="{ row }">
            <el-select
              v-model="row.hostType"
              placeholder="请选择主机类型"
              style="width: 100%"
              size="small"
              @change="(e) => handleHostTypeChange(e, row)"
            >
              <el-option
                v-for="item in hostTypeListOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
          <!-- 机器型号 -->
          <template #productId="{ row }">
            <div
              v-if="row.hostType !== '2008'"
              style="display: flex; justify-content: space-between; gap: 20px"
            >
              <el-input
                v-model="row.productName"
                disabled
                size="small"
                placeholder="请选择选配件"
              />
              <el-button
                type="primary"
                size="mini"
                @click="handleSelectSpare(row)"
              >
                选择
              </el-button>
            </div>
            <el-cascader
              v-else
              ref="ProductIds"
              filterable
              clearable
              collapse-tags
              :options="options"
              style="width: 100%"
              size="small"
              :props="{
                label: 'name',
                value: 'id',
                children: 'children',
                expandTrigger: 'click',
              }"
              leaf-only
              @change="(e) => handleProductIdChange(e, row)"
            ></el-cascader>
          </template>
          <template #percentage="{ row }">
            <el-select
              v-model="row.percentage"
              size="small"
              placeholder="请选择成色"
              clearable
              filterable
            >
              <el-option
                v-for="item in percentageOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
          <template #deviceOn="{ row }">
            <el-select
              v-model="row.deviceOn"
              style="width: 100%"
              size="small"
              placeholder="请选择设备新旧"
              clearable
              filterable
            >
              <el-option
                v-for="item in deviceOnOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
          <template #totalAmount="{ row }">
            <el-input-number
              v-model="row.totalAmount"
              style="width: 100%"
              :precision="2"
              :step="100"
              :min="0"
              :controls="false"
              size="small"
              placeholder="请输入销售总价"
            ></el-input-number>
          </template>

          <template #intentionAmount="{ row }">
            <el-input-number
              v-model="row.intentionAmount"
              style="width: 100%"
              :precision="2"
              :step="100"
              :min="0"
              size="small"
              :controls="false"
              placeholder="请输入定金"
            ></el-input-number>
          </template>
          <template #picsUrl="slotProps">
            <img
              style="max-width: 100px; max-height: 100px"
              :src="getPicsUrlImg(slotProps.row)"
            />
          </template>
          <template #actions="slotProps">
            <span class="fixed-width">
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                @click="surenewArr.splice(slotProps.index, 1)"
              >
                删除
              </el-button>
            </span>
          </template>
        </ProTable>

        <div
          class=""
          style="
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 15px;
          "
        >
          <el-button type="primary" @click="sureSelectGoods()">
            下一步
          </el-button>
          <el-button @click="canceladdDialog">取消</el-button>
        </div>
      </div>
      <div v-show="actives === 1" style="margin-top: 30px">
        <div class="tit-box">客户信息</div>
        <div>
          <el-form
            ref="proform_child2"
            :disabled="methodType === 'info'"
            :rules="dataInfoRules"
            :model="dataInfo"
            label-width="120px"
            class="demo-ruleForm"
          >
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="客户编码：" prop="customerSeqId">
                  <el-input
                    v-model="dataInfo.customerSeqId"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="店铺名称：" prop="customerName">
                  <el-input
                    v-model="dataInfo.customerName"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>

              <!--<el-col :span="6">-->
              <!--  <el-form-item label="员工名称：" prop="consigneeName">-->
              <!--    <el-input-->
              <!--      v-model="dataInfo.consigneeName"-->
              <!--      :disabled="true"-->
              <!--    ></el-input>-->
              <!--  </el-form-item>-->
              <!--</el-col>-->
              <el-col :span="6">
                <el-form-item label="联系人：" prop="consigneeName">
                  <el-input
                    v-model="dataInfo.consigneeName"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="联系电话：" prop="consigneePhone">
                  <el-input
                    v-model="dataInfo.consigneePhone"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="详细地址：" prop="consigneeAddress">
                  <el-input
                    v-model="dataInfo.consigneeAddress"
                    :disabled="true"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="tit-box">服务选项</div>
        <div>
          <el-form
            ref="proform_child3"
            :disabled="methodType === 'info'"
            :rules="dataInfoRules"
            :model="serveData"
            label-width="120px"
            class="demo-ruleForm"
          >
            <el-row :gutter="20">
              <!--<el-col :span="6">-->
              <!--  <el-form-item label="是否安装：" prop="isInstall">-->
              <!--    <el-radio-group v-model="dataInfo.isInstall">-->
              <!--      <el-radio :label="true">是</el-radio>-->
              <!--      <el-radio :label="false">否</el-radio>-->
              <!--    </el-radio-group>-->
              <!--  </el-form-item>-->
              <!--</el-col>-->
              <el-col :span="6">
                <el-form-item label="安装费用：" prop="installAmount">
                  <el-input
                    v-model="serveData.installAmount"
                    type="number"
                    min="0"
                    :precision="2"
                    placeholder="请输入安装费用"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="预计安装日期：" prop="installDate">
                  <el-date-picker
                    v-model="serveData.installDate"
                    style="width: 100%"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择预计安装日期"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="是否包邮：" prop="freeShipping">
                  <el-radio-group v-model="serveData.freeShipping">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  label="质保维修次数："
                  prop="guaranteeRepairCount"
                >
                  <el-input
                    v-model.number="serveData.guaranteeRepairCount"
                    type="number"
                    min="0"
                    placeholder="请输入质保维修次数"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="质保印量：" prop="guaranteeCount">
                  <el-input
                    v-model="serveData.guaranteeCount"
                    type="number"
                    min="0"
                    placeholder="请输入质保印量"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="质保截止日期：" prop="guaranteeDate">
                  <el-date-picker
                    v-model="serveData.guaranteeDate"
                    style="width: 100%"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择质保截止日期"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="结算方式：" prop="settleMethod">
                  <el-select
                    v-model="serveData.settleMethod"
                    style="width: 100%"
                    clearable
                  >
                    <el-option
                      v-for="item in settleMethodOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="serveData.settleMethod === 'INSTALLMENT'" :span="6">
                <el-form-item label="分期期数：" prop="installmentNum">
                  <el-input
                    v-model.number="serveData.installmentNum"
                    type="number"
                    min="0"
                    @change="updateInstallmentPrices"
                  />
                </el-form-item>
              </el-col>
              <el-col
                v-if="serveData.settleMethod === 'INSTALLMENT'"
                :span="24"
              >
                <el-form-item label="每期价格：">
                  <el-row :gutter="20" style="margin-left: 0; margin-right: 0">
                    <el-col
                      v-for="(price, index) in serveData.tradeOrderInstallments"
                      :key="index"
                      :span="4"
                    >
                      <el-form-item
                        :label="`第 ${index + 1} 期`"
                        :prop="'tradeOrderInstallments.' + index"
                        :rules="priceRules"
                        label-width="80px"
                      >
                        <el-input-number
                          v-model="
                            serveData.tradeOrderInstallments[index].amount
                          "
                          style="width: 100%"
                          size="small"
                          :controls="false"
                          :precision="2"
                          :min="0"
                        >
                        </el-input-number>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="服务类型：" prop="treatyType">
                  <el-radio-group v-model="serveData.treatyType">
                    <el-radio label="1200">散修</el-radio>
                    <el-radio label="1202">半保</el-radio>
                    <el-radio label="1230">全保</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col
                v-if="
                  serveData.treatyType === '1202' ||
                  serveData.treatyType === '1230'
                "
                :span="6"
              >
                <el-form-item label="价格类型：" prop="priceType">
                  <el-radio-group v-model="serveData.priceType">
                    <el-radio label="FIXED">固定价格</el-radio>
                    <el-radio label="LADDER">阶梯价</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col v-if="serveData.priceType === 'FIXED'" :span="6">
                <el-form-item label="黑白打印单价：" prop="blackWhitePrice">
                  <el-input
                    v-model="serveData.blackWhitePrice"
                    type="number"
                    min="0"
                  />
                </el-form-item> </el-col
              ><el-col v-if="serveData.priceType === 'FIXED'" :span="6">
                <el-form-item label="彩色打印单价：" prop="colorPrice">
                  <el-input
                    v-model="serveData.colorPrice"
                    type="number"
                    min="0"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="serveData.priceType === 'FIXED'" :span="6">
                <el-form-item label="五色打印单价：" prop="fiveColourPrice">
                  <el-input
                    v-model="serveData.fiveColourPrice"
                    type="number"
                    min="0"
                  />
                </el-form-item>
              </el-col>
              <el-col
                v-if="
                  serveData.treatyType === '1202' ||
                  serveData.treatyType === '1230'
                "
                :span="6"
              >
                <el-form-item label="服务截止时间：" prop="contractDate">
                  <el-date-picker
                    v-model="serveData.contractDate"
                    style="width: 100%"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择服务截止时间"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col v-if="serveData.priceType === 'LADDER'" :span="24">
                <el-form-item label="阶梯价：" prop="fiveColourPrice">
                  <div class="ladder">
                    <div>
                      <el-button
                        v-if="methodType !== 'info'"
                        type="success"
                        icon="el-icon-plus"
                        size="mini"
                        @click="handleAddLadder"
                      >
                        添加阶梯价
                      </el-button>
                    </div>
                    <div
                      v-for="(list, index) in ladderList"
                      :key="index"
                      class="ladder-list"
                    >
                      <div class="ladder-item">
                        <span>阶梯{{ index + 1 }}：</span>
                        <el-input
                          v-model="list.startCount"
                          placeholder="起始阶梯价格"
                          size="small"
                          clearable
                          :disabled="methodType === 'info'"
                        />
                        <span>-</span>
                        <el-input
                          v-model="list.endCount"
                          placeholder="截止阶梯价格"
                          size="small"
                          clearable
                          :disabled="methodType === 'info'"
                        />
                      </div>
                      <div class="ladder-item">
                        <span>阶梯{{ index + 1 }}黑白单价（元）：</span>
                        <el-input
                          v-model="list.blackWhitePrice"
                          placeholder="请输入黑白打印单价"
                          size="small"
                          clearable
                          :disabled="methodType === 'info'"
                        />
                      </div>
                      <div class="ladder-item">
                        <span>阶梯{{ index + 1 }}彩色单价（元）：</span>
                        <el-input
                          v-model="list.colorPrice"
                          placeholder="请输入彩色打印单价"
                          size="small"
                          clearable
                          :disabled="methodType === 'info'"
                        />
                      </div>
                      <div class="ladder-item">
                        <span>阶梯{{ index + 1 }}五色单价（元）：</span>
                        <el-input
                          v-model="list.fiveColourPrice"
                          placeholder="请输入彩色打印单价"
                          size="small"
                          clearable
                          :disabled="methodType === 'info'"
                        />
                      </div>
                      <i
                        v-if="methodType !== 'info'"
                        class="el-icon-delete"
                        style="
                          color: #f56c6c;
                          cursor: pointer;
                          margin-left: 10px;
                        "
                        @click="handleDeleteLadder(index)"
                      ></i>
                    </div>
                  </div>
                </el-form-item>
              </el-col>

              <!--<el-col v-if="serveData.settleMethod === 'INSTALLMENT'" :span="6">-->
              <!--  <el-form-item label="每期付款：" prop="installment">-->
              <!--    <el-input v-model="serveData.installment" disabled />-->
              <!--  </el-form-item>-->
              <!--</el-col>-->
            </el-row>
          </el-form>
        </div>
        <div class="tit-box">商品信息</div>
        <div style="padding: 5px 10px; margin: 0 auto">
          <ProTable
            ref="ProTable3"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="creatColumn3"
            show-pagination
            row-key="id"
            :data="tableData3"
            sticky
            :show-setting="false"
            :height="350"
          >
            <template #picsUrl="slotProps">
              <img
                style="max-width: 100px; max-height: 100px"
                :src="getPicsUrlImgs(slotProps.row.saleSkuInfo)"
              />
            </template>
          </ProTable>
        </div>
        <div class="totalNumber">
          <div class="totalNumber-list">
            订单总金额（元）：{{ dataInfo.fullAmount }}
          </div>
          <div class="totalNumber-list">
            会员减免（元）：{{ dataInfo.discountAmount }}
          </div>
          <div class="totalNumber-list">
            订单运费（元）：{{ dataInfo.shippingFee }}
          </div>
          <div class="totalNumber-list">
            应收（元）：{{ dataInfo.actualAmount }}
          </div>
          <div class="totalNumber-list">
            待收金额（元）：{{ dataInfo.arrersAmount }}
          </div>
        </div>
        <div
          style="
            width: 100%;
            margin-top: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          <el-button type="primary" @click="actives = 0"> 上一步 </el-button>
          <el-button
            type="primary"
            :loading="confirmBtnLoading"
            @click="sureOrderFn()"
          >
            确认订单
          </el-button>
        </div>
      </div>
    </ProDrawer>
    <ProDialog
      :value="showDialog"
      title="客户信息"
      width="80%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="showDialog = false"
    >
      <ProTable
        ref="ProTables"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="creatColumns"
        show-pagination
        row-key="id"
        :local-pagination="localPaginations"
        :data="tableData1"
        sticky
        :query-param="queryParams"
        :height="430"
        :show-setting="false"
        @loadData="loadData1"
      >
        <template #actions="slotProps">
          <span class="fixed-width">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-edit-outline"
              @click="sureSelectCustom(slotProps.row)"
            >
              确定
            </el-button>
          </span>
        </template>
      </ProTable>
    </ProDialog>
    <!--    选择机器信息-->
    <ProDialog
      :value="showDialog2"
      title="商品信息"
      width="80%"
      :confirm-loading="false"
      top="50px"
      :no-footer="true"
      @cancel="showDialog2 = false"
    >
      <ProTable
        ref="ProTable2"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :columns="creatColumn2"
        :local-pagination="localPagination2"
        :data="tableData2"
        show-selection
        sticky
        :query-param="queryParam2"
        :height="380"
        :show-setting="false"
        @loadData="loadData2"
        @handleSelectionChange="handleAllSelectionChange"
      >
        <template #picsUrl="slotProps">
          <img
            style="max-width: 100px; max-height: 100px"
            :src="getPicsUrlImg(slotProps.row)"
          />
        </template>
        <template #fullIdPath>
          <el-cascader
            ref="ProductIds"
            v-model="productIdName"
            filterable
            clearable
            collapse-tags
            :options="options"
            style="width: 100%"
            :props="{
              label: 'name',
              value: 'fullIdPath',
              children: 'children',
              expandTrigger: 'click',
              multiple: true,
            }"
            leaf-only
            @change="handleChange"
          ></el-cascader>
        </template>
        <template #actions="slotProps">
          <span class="fixed-width">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-zoom-out"
              @click="topageFn(slotProps.row)"
            >
              查看
            </el-button>
          </span>
        </template>
      </ProTable>
      <div
        class=""
        style="
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <el-button type="primary" @click="sureSelectGoodsFn">
          确认商品
        </el-button>
      </div>
    </ProDialog>
    <!-- 选配件选择 -->
    <SparePart
      ref="sparePart"
      :dialog-visible.sync="sparePartDialog"
      :spare-type="hostType"
      @confirm="confirmSpare"
    />
  </div>
</template>

<script>
import SparePart from "@/views/procure/cpns/sparePart.vue";
import { dictTreeByCodeApi } from "@/api/user";
import { nanoid } from "nanoid";
import {
  getCreatePageApi,
  getCustomerStaffPageApi,
  getPreviewPageApi,
  getStagePriceApi,
} from "@/api/goods";
import { Loading, Message } from "element-ui";
import { getAddressListApi } from "@/api/operator";
import { cloneDeep } from "lodash";
import { productAllApi } from "@/api/dispose";
import { getMachinePageApi } from "@/api/store";
import { getCustomerByPageApi, getCustomerUserListApi } from "@/api/customer";
import { addAmount, filterParam, filterParamRange } from "@/utils";

export default {
  name: "AddMachineOrder",
  components: {
    SparePart,
  },
  data() {
    return {
      addDialog: false,
      confirmLoadings: false,
      confirmBtnLoading: false,
      actives: 0,
      methodType: "add",
      calculateFormrr: {},
      optionsGetRegion: [],
      newArr: [],
      creatColumn4: [
        {
          dataIndex: "type",
          title: "主机类型",
          isTable: true,
          tableSlot: "type",
          minWidth: 100,
        },
        {
          dataIndex: "productId",
          title: "机器型号/选配件",
          isTable: true,
          tableSlot: "productId",
          minWidth: 120,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          tableSlot: "deviceOn",
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          tableSlot: "percentage",
        },
        {
          dataIndex: "totalAmount",
          title: "机器总价（元）",
          isTable: true,
          tableSlot: "totalAmount",
          minWidth: 100,
        },
        {
          dataIndex: "intentionAmount",
          title: "定金（元）",
          isTable: true,
          tableSlot: "intentionAmount",
          minWidth: 100,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 140,
          tableSlot: "actions",
          fixed: "right",
        },
      ],
      surenewArr: [],
      dataInfo: {},
      creatColumn3: [
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          minWidth: 100,
        },
        {
          dataIndex: "itemName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 100,
        },
        {
          dataIndex: "fullAmount",
          title: "机器总价（元）",
          isTable: true,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "定金（元）",
          isTable: true,
        },
        {
          dataIndex: "discountAmount",
          title: "会员减免",
          isTable: true,
        },
        {
          dataIndex: "payAmount",
          title: "小计（元）",
          isTable: true,
        },
      ],
      tableData3: [],
      psfsData: "logistics",
      // 收货地址列表
      addressList: [],
      //   选择客户
      showDialog: false,
      creatColumns: [
        {
          dataIndex: "seqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "name",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "legalPerson",
          title: "联系人",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "legalPersonTel",
          title: "联系电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        // {
        //   dataIndex: "role",
        //   title: "角色",
        //   isTable: true,
        //   formatter: (row) => row.role.label,
        // },
        {
          dataIndex: "Actions",
          width: 200,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      localPaginations: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      tableData1: [],
      queryParams: {},
      // 选择商品
      showDialog2: false,
      queryParam2: {
        lastIds: null,
      },
      creatColumn2: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(2000),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "productIds",
          title: "机型",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "tagName",
          title: "标签型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isSearch: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(2500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "picsUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picsUrl",
          width: 160,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "placeOrigin",
          title: "产地版本",
          isTable: true,
          // isSearch: true,
          // valueType: "input",
        },
        {
          dataIndex: "electric",
          title: "供电电压",
          isTable: true,
          // isSearch: true,
          // valueType: "input",
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1100),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6600),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 80,
        },
        // {
        //   dataIndex: "isSale",
        //   title: "是否上架",
        //   isTable: true,
        //   tableSlot: "isSale",
        //   isSearch: true,
        //   valueType: "select",
        //   option: [
        //     {
        //       label: "是",
        //       value: 1,
        //     },
        //     {
        //       label: "否",
        //       value: 0,
        //     },
        //   ],
        // },
        // {
        //   dataIndex: "blackWhiteCounter",
        //   title: "黑白计数器",
        //   isTable: true,
        //   // isSearch: true,
        //   // valueType: "inputRange",
        //   minWidth: 100,
        // },
        // {
        //   dataIndex: "colorCounter",
        //   title: "彩色计数器",
        //   isTable: true,
        //   // isSearch: true,
        //   // valueType: "inputRange",
        //   minWidth: 100,
        // },
        // {
        //   dataIndex: "fiveColourCounter",
        //   title: "五色计数器",
        //   isTable: true,
        //   // isSearch: true,
        //   // valueType: "inputRange",
        //   minWidth: 100,
        // },
        {
          dataIndex: "source",
          title: "来源",
          isTable: true,
          formatter: (row) => row.source?.label,
          minWidth: 100,
        },
        // {
        //   dataIndex: "status",
        //   title: "状态",
        //   isTable: true,
        //   formatter: (row) => row.status?.label,
        //   isSearch: true,
        //   multiple: true,
        //   valueType: "select",
        //   option: [
        //     {
        //       label: "已入库",
        //       value: "INVENTORY",
        //     },
        //     {
        //       label: "在售",
        //       value: "ON_SALE",
        //     },
        //     {
        //       label: "已售",
        //       value: "OVER_SALE",
        //     },
        //   ],
        //   minWidth: 80,
        // },
        // {
        //   dataIndex: "color",
        //   title: "色彩类型",
        //   isTable: true,
        //   formatter: (row) => row.color?.label,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(1700),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        {
          dataIndex: "createdAt",
          title: "入库时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   width: 140,
        //   tableSlot: "action",
        //   fixed: "right",
        // },
      ],
      localPagination2: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      tableData2: [],
      productIdName: [],
      options: [],
      settleMethodOptions: [
        {
          label: "全款",
          value: "FULL",
        },
        {
          label: "分期付款",
          value: "INSTALLMENT",
        },
      ],
      salesManOptions: [], // 销售人员
      machineOrderList: [
        {
          uid: nanoid(), // 使用nanoid生成唯一id
          productId: null,
        },
      ], // 选择下单机器
      percentageOptions: [],
      deviceOnOptions: [],
      machineIds: [],
      dataInfoRules: {
        // isInstall: [
        //   {
        //     required: true,
        //     message: "请选择是否安装",
        //     trigger: "change",
        //   },
        // ],
        installAmount: [
          {
            required: true,
            message: "请输入安装费用",
            trigger: "blur",
          },
        ],
        installDate: [
          {
            required: true,
            message: "请选择预计安装日期",
            trigger: "change",
          },
        ],
        freeShipping: [
          {
            required: true,
            message: "请选择是否包邮",
            trigger: "change",
          },
        ],
        guaranteeRepairCount: [
          {
            required: true,
            message: "请输入质保维修次数",
            trigger: "blur",
          },
        ],
        guaranteeCount: [
          {
            required: true,
            message: "请输入质保印量",
            trigger: "blur",
          },
        ],
        guaranteeDate: [
          {
            required: true,
            message: "请选择质保截止日期",
            trigger: "change",
          },
        ],
        treatyType: [
          {
            required: true,
            message: "请选择服务类型",
            trigger: "change",
          },
        ],
        contractDate: [
          {
            required: true,
            message: "请选择服务截止日期",
            trigger: "change",
          },
        ],
        settleMethod: [
          {
            required: true,
            message: "请选择结算方式",
            trigger: "change",
          },
        ],
        installmentNum: [
          { required: true, message: "请输入分期期数", trigger: "change" },
          {
            type: "number",
            min: 1,
            message: "分期期数必须大于0",
            trigger: "change",
          },
        ],
        priceType: [
          {
            required: true,
            message: "请选择价格类型",
            trigger: "change",
          },
        ],
      },
      priceRules: [
        { required: true, message: "请输入每期价格", trigger: "change" },
        // {
        //   min: 0,
        //   message: "每期价格必须大于等于0",
        //   trigger: "change",
        // },
      ],
      selectedSparePart: null,
      sparePartDialog: false,
      hostType: "", // 主机类型
      hostTypeListOptions: [],
      serveData: {},
      fullAddressList: [],
      ladderList: [], // 阶梯价
      installmentsList: [],
    };
  },
  mounted() {
    getCustomerUserListApi().then((res) => {
      // this.userTureList = res.data;
      this.salesManOptions = res.data.map((item) => {
        return {
          value: item.id,
          label: item.name,
        };
      });
    });
  },
  methods: {
    show() {
      this.addDialog = true;
      // this.$refs.ProTable2s?.refresh();
      this.surenewArr = [];
      this.addressList = [];
      this.calculateFormrr = {};
      this.dataInfo = {};
      this.serveData = {
        tradeOrderInstallments: [],
      };
      this.ladderList = [];
      this.$nextTick((e) => {
        this.init();
        this.getPercentage();
        this.getDeviceOnOptions();
        this.getProductType();
        // this.$refs.ProTable2s
        //   ? (this.$refs.ProTable2s.listLoading = false)
        //   : null;
      });
    },
    async updateInstallmentPrices() {
      if (!this.serveData.installDate) {
        this.$message.error("请选择预计安装日期");
        this.serveData.installmentNum = null;
        return;
      }
      const num = this.serveData.installmentNum;
      const result = await getStagePriceApi({
        arrersAmount: this.dataInfo.arrersAmount,
        installmentNum: num,
        installDate: this.serveData.installDate,
        customerId: this.serveData.customerId,
      });

      if (result.code === 200) {
        if (num > this.serveData.tradeOrderInstallments.length) {
          // 如果期数增加，添加新的价格输入框
          for (
            let i = this.serveData.tradeOrderInstallments.length;
            i < num;
            i++
          ) {
            this.serveData.tradeOrderInstallments.push({
              amount: null,
              installmentIndex: null,
              installmentNum: null,
              planPayDate: null,
            });
          }
        } else if (num < this.serveData.tradeOrderInstallments.length) {
          // 如果期数减少，移除多余的输入框
          this.serveData.tradeOrderInstallments.splice(num);
        }
        for (let i = 0; i < result.data.length; i++) {
          this.serveData.tradeOrderInstallments[i].amount = parseFloat(
            result.data[i].amount
          );
          this.serveData.tradeOrderInstallments[i].installmentIndex =
            result.data[i].installmentIndex;
          this.serveData.tradeOrderInstallments[i].installmentNum =
            result.data[i].installmentNum;
          this.serveData.tradeOrderInstallments[i].planPayDate =
            result.data[i].planPayDate;
        }
      }
    },
    async getProductType() {
      try {
        const result = await dictTreeByCodeApi(2000);
        if (result.code === 200) {
          this.hostTypeListOptions = result.data;
        }
      } catch (error) {
        this.hostTypeListOptions = [];
      }
    },
    async getDeviceOnOptions() {
      try {
        const result = await dictTreeByCodeApi(1100);
        if (result.code === 200) {
          this.deviceOnOptions = result.data;
        }
      } catch (error) {
        this.deviceOnOptions = [];
      }
    },
    async getPercentage() {
      try {
        const result = await dictTreeByCodeApi(2500);
        if (result.code === 200) {
          this.percentageOptions = result.data;
        }
      } catch (error) {
        this.percentageOptions = [];
      }
    },
    loadData1(parameter) {
      const requestParameters = Object.assign(this.queryParams, parameter);
      // getCustomerStaffPageApi(requestParameters)
      getCustomerByPageApi(requestParameters)
        .then((res) => {
          this.tableData1 = res.data.rows;
          this.localPaginations = {
            pageNumber: parameter.pageNumber,
            pageSize: parameter.pageSize,
            total: +res.data.total,
          };
        })
        .finally(() => {
          this.$refs.ProTables
            ? (this.$refs.ProTables.listLoading = false)
            : null;
        });
    },
    loadData2(parameter) {
      this.queryParam2 = filterParam(
        Object.assign({}, this.queryParam2, parameter)
      );
      const searchRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam2, searchRange);
      const requestParameters = cloneDeep(this.queryParam2);
      delete requestParameters.createdAt;
      requestParameters.isSale = true;
      getMachinePageApi(requestParameters)
        .then((res) => {
          this.tableData2 = res.data.rows;
          this.localPagination2 = {
            pageNumber: parameter.pageNumber,
            pageSize: parameter.pageSize,
            total: +res.data.total,
          };
          // clearSelection()
          this.$refs.ProTable2.$refs.ProElTable.clearSelection();
          if (this.surenewArr.length) {
            this.surenewArr.forEach((row) => {
              this.$refs.ProTable2.$refs.ProElTable.toggleRowSelection(
                row,
                true
              );
            });
          }
        })
        .finally(() => {
          this.$refs.ProTable2
            ? (this.$refs.ProTable2.listLoading = false)
            : null;
        });
    },
    // 选中的数据
    handleAllSelectionChange(row) {
      this.newArr = cloneDeep(row);
    },
    // 确认订单
    sureOrderFn() {
      this.$refs.proform_child3.validate(async (valid) => {
        if (valid) {
          try {
            this.confirmBtnLoading = true;
            const args = {
              ...this.serveData,
              // buyerId: this.calculateFormrr.id,
              customerId: this.calculateFormrr.id,
              logisticsProvider: this.psfsData,
              mobile: this.calculateFormrr.consigneePhone,
              id: this.calculateFormrr.ids,
              machineIds: this.machineIds,
              addressId: this.calculateFormrr.addressId,
              // payMode: "WECHART", // 默认支付方式
              payMode: "OFFLINE", // 默认支付方式
              isMechine: true,
            };
            if (args.priceType === "LADDER" && !this.ladderList.length) {
              return this.$message.warning("请先完善阶梯价信息");
            }
            if (
              args.settleMethod === "INSTALLMENT" &&
              !args.tradeOrderInstallments
            ) {
              return this.$message.warning("请完善分期信息");
            }
            // 计算输入每期的金额是否和总价相等
            if (args.settleMethod === "INSTALLMENT") {
              const total = args.tradeOrderInstallments.reduce(
                (acc, cur) => addAmount(acc, cur.amount),
                0
              );
              if (total !== this.dataInfo.arrersAmount) {
                return this.$message.warning("分期金额总和必须等于待收金额");
              }
            }
            args.repairMonthlyPrices = this.ladderList;
            const result = await getCreatePageApi(args);
            if (result.code === 200) {
              this.$message.success("创建订单成功");
              this.addDialog = false;
              this.actives = 0;
              this.machineIds = [];
              this.calculateFormrr = {};
              this.showDialog = false;
              this.showDialog2 = false;
              this.psfsData = "logistics";
              this.newArr = [];
              this.surenewArr = [];
              this.productIdName = [];
              this.$emit("refresh");
            }
          } finally {
            this.confirmBtnLoading = false;
          }
        }
      });
    },
    // 确认商品
    sureSelectGoodsFn() {
      if (this.newArr.length === 0) {
        this.$message.warning("请选择商品");
        return;
      }
      this.surenewArr = this.newArr;
      this.showDialog2 = false;
      // this.$refs.ProTable2s
      //   ? (this.$refs.ProTable2s.listLoading = false)
      //   : null;
    },
    // 确认商品
    sureSelectGoods() {
      try {
        if (Object.keys(this.calculateFormrr).length === 0) {
          this.$message.warning("请选择客户信息");
          return;
        }
        if (!this.calculateFormrr.addressId) {
          this.$message.error("请选择收货地址");
          return;
        }
        if (!this.surenewArr.length) {
          this.$message.warning("请选择销售机器");
          return;
        }
        // 检查surenewArr列表中每项字段是否存在
        this.surenewArr.forEach((item, index) => {
          if (
            !item.hostType ||
            !item.productId ||
            !item.deviceOn ||
            !item.percentage ||
            !item.intentionAmount ||
            !item.totalAmount
          ) {
            throw new Error(`请完善第${index + 1}行销售机器信息`);
          }
        });
        const loadingInstance = Loading.service({
          text: "正在加载中...", //显示在加载图标下方的加载文案
          background: "rgba(0, 0, 0, 0.3)", //遮罩背景色
        });
        this.confirmLoadings = true;
        this.machineIds = [];
        this.surenewArr.forEach((item) => {
          this.machineIds.push(item);
        });

        getPreviewPageApi({
          // buyerId: this.calculateFormrr.id,
          customerId: this.calculateFormrr.id,
          logisticsProvider: this.psfsData,
          mobile: this.calculateFormrr.consigneePhone,
          machineIds: this.machineIds,
          addressId: this.calculateFormrr.addressId,
          isMechine: true,
        })
          .then((res) => {
            this.actives = 1;
            res.data.customerName = this.calculateFormrr.name;
            res.data.customerSeqId = this.calculateFormrr.seqId;
            res.data.consigneeName = this.calculateFormrr.consigneeName;
            this.dataInfo = res.data;
            this.$set(this.serveData, "freeShipping", false); // 是否包邮
            this.$set(this.serveData, "isInstall", false); // 是否安装
            this.$set(this.serveData, "treatyType", "1200"); // 服务类型
            this.tableData3 = res.data.tradeOrderDetailList;
            this.calculateFormrr.consigneeAddress = res.data.consigneeAddress;
            this.calculateFormrr.ids = res.data.id;
            this.$nextTick((e) => {
              // 以服务的方式调用的 Loading 需要异步关闭
              loadingInstance.close();
            });
            this.confirmLoadings = false;
          })
          .finally(() => {
            this.$refs.ProTable3
              ? (this.$refs.ProTable3.listLoading = false)
              : null;
            this.$nextTick((e) => {
              // 以服务的方式调用的 Loading 需要异步关闭
              loadingInstance.close();
            });
          });
      } catch (e) {
        this.$message.warning(e.message);
      }
    },
    confirmSpare(row) {
      this.$set(this.selectedSparePart, "productName", row.modeType);
      this.selectedSparePart.productId = row.id;
      this.sparePartDialog = false;
    },
    handleHostTypeChange(e, row) {
      row.productId = "";
      this.$set(row, "productIdName", []);
      this.$set(row, "productName", "");
    },
    handleSelectSpare(row) {
      this.selectedSparePart = null;
      if (!row.hostType && row.hostType !== "2008") {
        Message.warning("请选择主机类型");
        return;
      }
      this.selectedSparePart = row;
      this.hostType = row.hostType;
      this.sparePartDialog = true;
    },
    handleProductIdChange(e, row) {
      if (e) {
        row.productId = e[e.length - 1];
      } else {
        row.productId = "";
      }
    },
    handleChange(value, id) {
      const item = this.machineOrderList.find((item) => item.uid === id);
      if (item) {
        item.productId = value;
      }
    },
    addCascader() {
      this.machineOrderList.push({
        uid: nanoid(),
        productId: null,
      });
    },
    removeCascader(id) {
      if (this.machineOrderList.length <= 1) {
        return;
      }
      const index = this.machineOrderList.findIndex((item) => item.uid === id);
      if (index > -1) {
        this.machineOrderList.splice(index, 1);
      }
    },
    topageFn(row) {
      this.$router.push({ name: "sku", params: row });
    },
    // 确认用户
    async sureSelectCustom(row) {
      // row.consigneeName = row.legalPerson || "";
      // row.consigneePhone = row.legalPersonTel || "";
      this.calculateFormrr = {};
      this.addressList = [];
      this.fullAddressList = [];
      // 获取用户可用地址
      try {
        const res = await getAddressListApi(row.id);
        this.fullAddressList = res.data;
        res.data.map((item) => {
          this.addressList.push({
            value: item.id,
            label: item.address,
          });
        });
      } catch (e) {
        Message.error(e.message);
      }
      this.$nextTick((e) => {
        this.calculateFormrr = row;
      });
      this.showDialog = false;
    },
    handleAddressChange(val) {
      const addressInfo = this.fullAddressList.find((item) => item.id === val);
      if (addressInfo) {
        this.$set(this.calculateFormrr, "consigneeName", addressInfo.contact);
        this.$set(this.calculateFormrr, "consigneePhone", addressInfo.phone);
      }
    },
    showDialogFn(val) {
      if (val) {
        this.init();
        this.showDialog2 = true;
        this.$nextTick((e) => {
          this.$refs.ProTable2.refresh();
        });
      } else {
        this.showDialog = true;
        this.$nextTick((e) => {
          this.$refs.ProTables.refresh();
        });
      }
    },
    addOrderMachine() {
      this.surenewArr.push({
        productId: null,
        percentage: null,
        deviceOn: null,
      });
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
      // classifyListApi({
      //   pageNumber: 1,
      //   pageSize: 99999,
      // }).then((res) => {
      //   this.creatColumn2[1].option = (res.data.rows || []).map((item) => ({
      //     label: item.name,
      //     value: item.id,
      //   }));
      // });
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    handleAddLadder() {
      this.ladderList.push({
        startCount: null,
        endCount: null,
        blackWhitePrice: null,
        colorPrice: null,
        fiveColorPrice: null,
      });
    },
    handleDeleteLadder(index) {
      this.ladderList.splice(index, 1);
    },
    getPicsUrlImgs(row) {
      return row?.picUrl?.[0]?.url;
    },
    canceladdDialog() {
      this.addDialog = false;
      this.actives = 0;
      this.optionsGetRegion = [];
      this.newArr = [];
      // this.$router.push({ name: "orders" });
      // this.$refs.ProTable?.refresh();
      // this.$refs.ProTable2?.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.machine-list {
  //margin: 20px 0;
  padding: 20px;
  //border: 1px solid #ebeef5;
  //border-radius: 4px;

  .machine-items {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .machine-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #f5f7fa;
    border-radius: 4px;
    transition: all 0.3s ease;
    flex: 0 0 calc(33% - 10px); /* 每行四个元素，考虑间隙 */

    &:hover {
      background: #ebeef5;
    }
  }
}

// 过渡动画
.machine-list-enter-active,
.machine-list-leave-active {
  transition: all 0.5s ease;
}

.machine-list-enter,
.machine-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.machine-list-move {
  transition: transform 0.5s ease;
}
.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
.tit-boxs {
  width: 90%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px 7px;
  font-size: 16px;
  font-weight: 800;

  // &::before {
  //   content: "";
  //   width: 5px;
  //   height: 20px;
  //   background: #409eff;
  //   display: inline-block;
  //   position: absolute;
  //   left: -1px;
  //   top: 4px;
  // }
}
.ladder {
  display: flex;
  flex-direction: column;
  gap: 10px;
  .ladder-list {
    display: flex;
    align-items: center;
    //justify-content: space-between;

    .ladder-item {
      width: 350px;
      display: flex;
      flex-wrap: nowrap;
      gap: 20px;
      margin-right: 20px;
      span {
        text-wrap: nowrap;
      }
    }
  }
}
</style>
