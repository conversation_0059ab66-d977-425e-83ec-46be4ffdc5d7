<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-09-19 18:44:25
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-12-26 15:49:45
 * @FilePath: \benyin-web\src\layout\components\Menu\index.vue
 * @Description: 侧边
 * 
-->

<template>
  <div>
    <el-scrollbar height="100vh" style="overflow: hidden">
      <el-menu
        :default-active="nextActive"
        :unique-opened="uniqueOpenedFlag"
        class="el-menu-vertical"
        router
        :mode="mode"
        @open="handleOpen"
        @close="handleClose"
        @select="setNextActive"
      >
        <div class="menu-container">
          <div class="menu-logo">
            <img style="width: 100%" src="/logo.png" alt="" />
          </div>
          <div v-for="(item, index) in routes" :key="index">
            <div
              v-if="!item.hidden"
              class="menu-item"
              :class="defaultActive === item?.value ? 'active' : null"
              @click="setData(item)"
            >
              <!-- <component class="menu-icon" v-if="item?.meta.icon" theme="outline" size="14" strokeWidth="3"
                :is="item.meta.icon" /> -->

              <div style="padding: 5px 0; user-select: none">
                {{ item.label }}
              </div>
            </div>
          </div>
        </div>
        <div
          v-show="!collapse"
          class="menu-item-collapse"
          :class="{ collapsed: collapse }"
        >
          <div class="menu-item-title">
            {{ proName }}
          </div>
          <div style="margin: 10px">
            <MenuItem :key="nextMenu?.value" :item="{ ...nextMenu }" />
          </div>
        </div>
      </el-menu>
    </el-scrollbar>
  </div>
</template>
<script>
import { isEmpty, cloneDeep } from "lodash";
import MenuItem from "./MenuItem.vue";
import { asyncRoutes } from "@/router";
import setting from "@/config/setting.js";
import { mapActions, mapGetters } from "vuex";
const { title } = setting;
export default {
  name: "Menu",
  components: {
    MenuItem,
  },
  mixins: [],
  data() {
    return {
      nextMenu: {},
      nextActive: "",
      mode: "vertical",
      defaultOpened: null,
      uniqueOpenedFlag: null,
      routes: [],
      defaultActive: null,
      proName: title,
    };
  },

  computed: {
    ...mapGetters({
      collapse: "collapse", // 是否展开菜单
    }),
  },

  watch: {
    $route: {
      handler(val) {
        // this.nextActive = val.value;
        if (val.matched[0].path) {
          this.defaultActive = val.matched[0].path;
          const routes = JSON.parse(localStorage.getItem("systemMenu"));
          const nextMenu = routes.find(
            (item) => item.value === val.matched[0].path
          );
          this.nextMenu = nextMenu;
          localStorage.setItem("nextMenu", JSON.stringify(nextMenu));
          localStorage.setItem("openMenu", JSON.stringify(this.defaultActive));
        } else {
          // this.defaultActive = "/index";
          this.defaultActive = "/index";
          this.nextMenu = [];
          localStorage.setItem("nextMenu", null);
          localStorage.setItem("openMenu", null);
        }
        this.nextActive = val.path;
        localStorage.setItem("nextActive", this.nextActive);
      },
      immediate: true,
      deep: true,
    },
    defaultActive: {
      handler(val) {
        if (val && (val === "/index" || val === "null")) {
          this.foldSideBar();
        } else {
          this.openSideBar();
        }
      },
      immediate: true,
    },
  },
  created() {
    // this.routes = localStorage.getItem("systemMenu") ? JSON.parse(localStorage.getItem("systemMenu")) : [];
    // console.log(this.routes)
  },
  mounted() {
    if (localStorage.getItem("systemMenu")) {
      this.routes = JSON.parse(localStorage.getItem("systemMenu"));
      setTimeout(() => {
        this.defaultActive =
          JSON.parse(localStorage.getItem("openMenu")) || "/index";
        this.nextActive = localStorage.getItem("nextActive") || null; //this.routes[0].children[0].value
        this.nextMenu = JSON.parse(localStorage.getItem("nextMenu")) || null; //this.routes[0]
        // setTimeout(() => {
        //   if (!localStorage.getItem("openMenu")) {
        //     localStorage.setItem("openMenu", this.routes[0].value)
        //     localStorage.setItem("nextMenu", JSON.stringify(this.routes[0]))
        //     localStorage.setItem("nextActive", this.routes[0].children[0].value)
        //   }
        // }, 300)
      }, 300);
    }
  },
  methods: {
    handleOpen() {},
    handleClose() {},
    setData(data) {
      this.defaultActive = data.value || data.value;
      if (data.value === "/index") {
        this.$router.push({ path: "/index" });
        this.nextMenu = [];
        localStorage.setItem("nextMenu", null);
        localStorage.setItem("openMenu", null);
      } else {
        this.nextMenu = data;
        localStorage.setItem("nextMenu", JSON.stringify(data));
        localStorage.setItem("openMenu", JSON.stringify(this.defaultActive));
      }
    },
    setNextActive(data) {
      this.nextActive = data;
      localStorage.setItem("nextActive", this.nextActive);
    },
    ...mapActions({
      changeCollapse: "settings/changeCollapse",
      openSideBar: "settings/openSideBar",
      foldSideBar: "settings/foldSideBar",
    }),
  },
};
</script>
<style lang="scss" scoped>
.el-menu-vertical {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  height: 100vh;
  overflow: hidden;
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #ddd;
    background-clip: padding-box;
    border: 3px solid transparent;
    border-radius: 7px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &::-webkit-scrollbar-track:hover {
    background-color: #f8fafc;
  }

  &:not(.el-menu--collapse) {
    width: 240px;
  }
  .menu-container {
    width: 30%;
    float: left;
    height: 100%;
    background: #293246;
    overflow: auto;
    .menu-logo {
      width: 50px;
      margin: auto;
      text-align: center;
      margin-top: 10px;
    }
    .menu-item {
      color: #fff;
      margin: 10px 5px;
      padding: 10px 0;
      text-align: center;
      border-radius: 5px;
      cursor: pointer;
    }
    .active {
      background: #4e88f3;
    }
  }
  .menu-item-collapse {
    width: 70%;
    height: 100%;
    overflow: auto;
    position: relative;
    transition: width $base-transition-time-4 ease-in-out;
    .menu-item-title {
      width: 100%;
      line-height: 60px;
      text-align: center;
      font-size: 22px;
      border-bottom: 1px solid #f6f6f6;
      background-color: #ffffff;
      position: sticky;
      top: 0;
      text-wrap: nowrap;
      z-index: 999;
    }
    &:not(.collapsed) {
      animation: expandWidth $base-transition-time-4 forwards;
    }
  }
  .collapsed {
    width: 0;
    animation: collapseWidth $base-transition-time-4 forwards;
  }
  @keyframes collapseWidth {
    from {
      width: 70%;
    }
    to {
      width: 0;
    }
  }
  @keyframes expandWidth {
    from {
      width: 0;
    }
    to {
      width: 70%;
    }
  }
}
</style>
