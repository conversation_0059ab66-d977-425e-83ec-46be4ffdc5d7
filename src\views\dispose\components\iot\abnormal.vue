<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:58:44
 * @Description: 异常日志
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
    </ProTable>
  </div>
</template>

<script>
import { getExceptionLogListApi } from "@/api/iot";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
export default {
  name: "Abnormal",
  data() {
    return {
      tableData: [],
      columns: [
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "ipAddress",
          title: "IP地址",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "port",
          title: "端口",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 80,
        },
        {
          dataIndex: "exceptionDescribe",
          title: "异常描述",
          isTable: true,
          isSearch: true,
          valueType: "input",
          inputType: "textarea",
          width: 500,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "mode",
          title: "型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "createdAt",
          title: "记录时间",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
        },
      ],
      queryParam: {},
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    async loadData(parameter) {
      try {
        this.queryParam = filterParam(
          Object.assign({}, this.queryParam, parameter)
        );
        const res = [
          {
            createdAtStartTime: null,
            createdAtEndTime: null,
            data: parameter.createdAt,
          },
        ];
        filterParamRange(this, this.queryParam, res);
        const requestParameters = cloneDeep(this.queryParam);
        delete requestParameters.createdAt;
        const result = await getExceptionLogListApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style lang="scss" scoped></style>
