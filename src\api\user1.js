/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-09 18:13:48
 * @Description:
 */

import { get, post, put, del } from "@/utils/request";
//   前缀用来识别权限ip
//=========登陆流程=========
export const captcha = (data) => post("/magina/anno/captcha", data); //验证码生成
export const key = (data) => post("/magina/anno/key", data); //生成加密令牌及公钥
export const login = (data) => post("/magina/system/login", data); //登陆
export const resources = () => get("/magina/system/resources"); //登录用户资源树
export const getInfo = () => get("/magina/department-user/list"); //获取用户所在部门
export const logout = () => put("/magina/system/logout"); //登出
// 设备管理人员
export const deviceManager = () =>
  get(
    "/magina/manage/user-role/member-page/1605103185486278658?pageNumber=1&pageSize=2000"
  ); //登录用户资源树
