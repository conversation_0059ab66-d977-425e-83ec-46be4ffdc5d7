<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 11:33:42
 * @Description: 
 -->
<template>
  <div class="editor">
    <div ref="toolbar" class="toolbar"></div>
    <div
      ref="editor"
      class="text"
      :style="{ height: height ? height + 'px' : '500px' }"
    ></div>
  </div>
</template>

<script>
import { uploadFile } from "@/api/upload";
import Editor from "wangeditor";
import { async } from "rxjs";
export default {
  name: "RichTextBox",
  props: {
    //显示内容
    content: {
      default() {
        return "";
      },
    },
    //文件上传
    url: {
      type: String,
      default: "",
    },
    //自定义参数
    headers: {
      default() {
        return {};
      },
    },
    height: {
      type: Number,
      default: 500,
    },
  },
  data() {
    return {
      editor: null,
      info_: null,
    };
  },
  watch: {
    content(val) {
      this.editor.txt.html(val);
    },
  },
  mounted() {
    this.setEditor();
  },
  methods: {
    echo(val) {
      this.editor.txt.html(val);
    },
    /**
     * @description: 初始化富文本编辑器
     * @param {*}
     * @return {*}
     */
    setEditor() {
      this.editor = new Editor(this.$refs.toolbar, this.$refs.editor);
      this.editor.config.uploadImgServer = window.config.api.uploadURL; // 配置服务器端地址
      this.editor.config.uploadImgHeaders = this.headers; // 自定义 header
      this.editor.config.uploadFileName = "file"; // 后端接受上传文件的参数名
      this.editor.config.uploadImgMaxSize = 10 * 1024 * 1024; // 将图片大小限制为 2
      this.editor.config.uploadImgMaxLength = 1; // 限制一次最多上传 1 张图片
      this.editor.config.uploadImgTimeout = 3 * 60 * 1000; // 设置超时时间

      // 配置菜单
      this.editor.config.menus = [
        "head", // 标题
        "bold", // 粗体
        "fontSize", // 字号
        "fontName", // 字体
        "italic", // 斜体
        "underline", // 下划线
        "strikeThrough", // 删除线
        "foreColor", // 文字颜色
        "backColor", // 背景颜色
        "link", // 插入链接
        //'list', // 列表
        "justify", // 对齐方式
        "quote", // 引用
        "emoticon", // 表情
        "image", // 插入图片
        "table", // 表格
        // 'video', // 插入视频
        //'code', // 插入代码
        "undo", // 撤销
        "redo", // 重复
      ];

      this.editor.config.uploadImgHooks = {
        before: (xhr, editor, files) => {
          let message = "上传失败";
          files.map(async (file) => {
            const result = await uploadFile(file);
            if (result.url) {
              message = "上传成功";
              console.log("message", message);
              const htmlTemp = this.editor.txt.html();
              this.editor.txt.html(`${htmlTemp}<img src="${result.url}"/>`);
            } else {
              message = "上传失败";
            }
          });
        },
        success: function (xhr, editor, result) {
          // 图片上传并返回结果，图片插入成功之后触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
          // console.log("success:",result)
        },
        fail: function (xhr, editor, result) {
          // 图片上传并返回结果，但图片插入错误时触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
        },
        error: function (xhr, editor) {
          // 图片上传出错时触发
          // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象
        },
        // 如果服务器端返回的不是 {errno:0, data: [...]} 这种格式，可使用该配置
        // （但是，服务器端返回的必须是一个 JSON 格式字符串！！！否则会报错）
        customInsert: function (insertImg, result, editor) {},
      };
      //关闭默认提示信息
      this.editor.config.customAlert = (info) => {
        // info 是需要提示的内容
        // this.$message.error(info);
      };
      this.editor.config.onchange = (html) => {
        // 绑定当前逐渐地值
        this.$emit("onchange", html);
      };
      // 创建富文本编辑器
      this.editor.create();
      if (this.content !== "") {
        this.editor.txt.html(this.content);
      }
      if (localStorage.getItem("detailHtml")) {
        this.editor.txt.html(localStorage.getItem("detailHtml"));
      }
    },
    getContent() {
      return this.editor.txt.html();
    },
  },
};
</script>

<style lang="scss" scoped>
.editor {
  width: 100%;
}

.toolbar {
  border: 1px solid #ccc;
}

.text {
  border: 1px solid #ccc;
  height: 500px;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>
