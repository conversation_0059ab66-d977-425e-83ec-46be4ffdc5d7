<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:58:43
 * @Description: 取值配置接口
 -->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增配置
        </el-button>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>

    <!--  新增弹窗  -->
    <ProDialog
      :value="showDialog"
      :title="dialogTitle"
      width="1000px"
      :confirm-loading="dialogLoading"
      top="10%"
      :no-footer="false"
      @ok="handleConfirmDialog"
      @cancel="handleCancelDialog"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParams"
        :form-list="columns"
        :confirm-loading="dialogLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="'add'"
        :no-footer="true"
        @proSubmit="formSubmit"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import { Message } from "element-ui";
import {
  addConfigureApi,
  editConfigureApi,
  delConfigureApi,
  getConfigureListApi,
} from "@/api/iot";
import { isEmpty, cloneDeep } from "lodash";
import { filterParam, filterParamRange } from "@/utils";

export default {
  name: "Allocation",
  components: { ProTable },
  data() {
    return {
      dialogTitle: "",
      tableData: [],
      columns: [
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          isSearch: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "item",
          title: "项",
          isTable: true,
          isSearch: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "value",
          title: "匹配值",
          isTable: true,
          isSearch: true,
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "way",
          title: "方式",
          isTable: true,
          isForm: true,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            { label: "精确", value: "精确" },
            { label: "模糊", value: "模糊" },
          ],
          formSpan: 12,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
          isForm: true,
          isSearch: true,
          valueType: "input",
          inputType: "textarea",
          formSpan: 24,
        },
        {
          dataIndex: "updatedAt",
          title: "上传时间",
          isSearch: true,
          clearable: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
        },
        {
          dataIndex: "Actions",
          width: 180,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      queryParam: {},
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      showDialog: false,
      dialogLoading: false,
      formParams: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    async loadData(parameter) {
      try {
        this.queryParam = filterParam(
          Object.assign({}, this.queryParam, parameter)
        );
        const res = [
          {
            createdAtStartTime: null,
            createdAtEndTime: null,
            data: parameter.updatedAt,
          },
        ];
        filterParamRange(this, this.queryParam, res);
        const requestParameters = cloneDeep(this.queryParam);
        delete requestParameters.updatedAt;
        const result = await getConfigureListApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    handleAdd() {
      this.formParams = {};
      this.showDialog = true;
      this.methodType = "add";
    },
    handleConfirmDialog() {
      this.$refs.ProForm.handleSubmit();
    },
    handleCancelDialog() {
      this.showDialog = false;
      this.$nextTick(() => {
        this.formParams = {};
      });
    },
    async formSubmit(val) {
      if (this.methodType === "add") {
        try {
          this.dialogLoading = true;
          const result = await addConfigureApi(val);
          if (result.code === 200) {
            Message.success("新增成功");
            this.handleCancelDialog();
            this.refresh();
          }
        } catch (err) {
          Message.error(err.message);
        } finally {
          this.dialogLoading = false;
        }
      } else {
        this.update();
      }
    },
    //触发编辑
    handleUpdate(row) {
      this.formParams = {};
      this.dialogTitle = "编辑";
      this.formParams = cloneDeep(row);
      this.showDialog = true;
      this.methodType = "edit";
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应编辑
    update() {
      editConfigureApi(this.formParams)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.showDialog = false;
          this.$refs.ProTable.refresh();
        });
    },
    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delConfigureApi(data.id).then(() => {
          this.$message.success("删除成功");
          this.localPagination = {
            pageNumber: 1,
            pageSize: 10,
            total: 0,
          };
          this.$nextTick(() => {
            this.$refs.ProTable.refresh();
          });
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
