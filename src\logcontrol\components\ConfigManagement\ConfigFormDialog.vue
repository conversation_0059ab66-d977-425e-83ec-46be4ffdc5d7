<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 配置表单对话框
-->
<template>
  <el-dialog
    :title="isEdit ? '编辑配置' : '创建配置'"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    :modal="false"
  >
    <el-form :model="form" :rules="rules" ref="form" label-width="120px">
      <el-form-item label="配置名称" prop="configName">
        <el-input v-model="form.configName" placeholder="请输入配置名称" />
      </el-form-item>
      
      <el-form-item label="基于模板" v-if="!isEdit">
        <el-select v-model="form.templateName" placeholder="选择模板（可选）" style="width: 100%" @change="handleTemplateChange">
          <el-option label="不使用模板" value="" />
          <el-option
            v-for="template in templates"
            :key="template.templateName"
            :label="template.displayName"
            :value="template.templateName"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="日志级别" prop="logLevel">
        <el-select v-model="form.logLevel" placeholder="选择日志级别">
          <el-option label="DEBUG" value="DEBUG" />
          <el-option label="INFO" value="INFO" />
          <el-option label="WARN" value="WARN" />
          <el-option label="ERROR" value="ERROR" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="位置日志" prop="enableLocationLog">
        <el-switch
          v-model="form.enableLocationLog"
          active-text="启用"
          inactive-text="禁用"
        />
        <div class="form-tip">
          <i class="el-icon-info"></i>
          是否启用位置日志记录
        </div>
      </el-form-item>

      <el-form-item label="位置日志间隔" prop="locationLogInterval">
        <el-input-number
          v-model="form.locationLogInterval"
          :min="1"
          :max="3600"
          :step="1"
          controls-position="right"
          style="width: 200px"
        />
        <span class="input-suffix">秒</span>
        <div class="form-tip">
          <i class="el-icon-info"></i>
          位置信息采集的时间间隔，必须大于0
        </div>
      </el-form-item>

      <el-form-item label="上传间隔" prop="logUploadInterval">
        <el-input-number
          v-model="form.logUploadInterval"
          :min="1"
          :max="3600"
          :step="1"
          controls-position="right"
          style="width: 200px"
        />
        <span class="input-suffix">秒</span>
        <div class="form-tip">
          <i class="el-icon-info"></i>
          日志上传的时间间隔，必须大于0
        </div>
      </el-form-item>

      <el-form-item label="最大文件数量" prop="maxLogFiles">
        <el-input-number
          v-model="form.maxLogFiles"
          :min="1"
          :max="50"
          controls-position="right"
          style="width: 200px"
        />
        <span class="input-suffix">个</span>
        <div class="form-tip">
          <i class="el-icon-info"></i>
          最大日志文件数量，必须大于0
        </div>
      </el-form-item>

      <el-form-item label="配置版本">
        <el-input
          v-model="form.configVersion"
          :placeholder="isEdit ? '配置版本' : '版本号将由系统自动生成'"
          :disabled="!isEdit"
          :readonly="!isEdit"
        />
        <div class="form-tip">
          <i class="el-icon-info"></i>
          {{ isEdit ? '配置版本号，如：1.0.0' : '创建时版本号由后端自动生成' }}
        </div>
      </el-form-item>

      <el-form-item label="激活状态" v-if="isEdit">
        <el-switch
          v-model="form.isActive"
          active-text="激活"
          inactive-text="未激活"
        />
      </el-form-item>

      <el-form-item label="配置说明">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入配置说明（可选）"
        />
      </el-form-item>
      
      <el-form-item label="自定义设置">
        <el-input
          v-model="form.customSettings"
          type="textarea"
          :rows="4"
          placeholder="请输入JSON格式的自定义设置（可选）"
        />
        <div class="form-tip">
          <i class="el-icon-info"></i>
          请输入有效的JSON格式，例如：{"key": "value"}
        </div>
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'ConfigFormDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: null
    },
    templates: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      form: {
        id: null,
        configName: '',
        templateName: '',
        logLevel: 'INFO',
        enableLocationLog: true,
        locationLogInterval: 3,
        logUploadInterval: 60,
        maxLogFiles: 5,
        configVersion: '',
        isActive: false,
        description: '',
        customSettings: ''
      },
      rules: {
        configName: [
          { required: true, message: '配置名称不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        logLevel: [
          { required: true, message: '日志级别不能为空', trigger: 'change' },
          { validator: this.validateLogLevel, trigger: 'change' }
        ],
        enableLocationLog: [
          { required: true, message: '位置日志设置不能为空', trigger: 'change' }
        ],
        locationLogInterval: [
          { required: true, message: '位置日志间隔不能为空', trigger: 'blur' },
          { validator: this.validatePositiveNumber, trigger: 'blur' }
        ],
        logUploadInterval: [
          { required: true, message: '上传间隔不能为空', trigger: 'blur' },
          { validator: this.validatePositiveNumber, trigger: 'blur' }
        ],
        maxLogFiles: [
          { required: true, message: '最大文件数量不能为空', trigger: 'blur' },
          { validator: this.validatePositiveNumber, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    isEdit() {
      return this.config && this.config.id
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  methods: {
    initForm() {
      if (this.config) {
        // 编辑模式，填充现有数据
        this.form = {
          ...this.form,
          ...this.config,
          // 后端已改为秒，直接使用
          locationLogInterval: this.config.locationLogInterval,
          logUploadInterval: this.config.logUploadInterval,
          customSettings: this.config.customSettings ? JSON.stringify(this.config.customSettings, null, 2) : ''
        }
      } else {
        // 创建模式，重置表单
        this.resetForm()
      }
      
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },
    
    handleTemplateChange(templateName) {
      if (!templateName) return

      const template = this.templates.find(t => t.templateName === templateName)
      if (template) {
        // 从模板填充所有相关字段
        this.form.logLevel = template.logLevel
        this.form.enableLocationLog = template.enableLocationLog
        // 后端已改为秒，直接使用
        this.form.locationLogInterval = template.locationLogInterval
        this.form.logUploadInterval = template.logUploadInterval
        this.form.maxLogFiles = template.maxLogFiles
        this.form.description = template.description
      }
    },
    
    handleConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 验证自定义设置JSON格式
          if (this.form.customSettings) {
            try {
              JSON.parse(this.form.customSettings)
            } catch (error) {
              this.$message.error('自定义设置必须是有效的JSON格式')
              return
            }
          }
          
          // 判断是从模板创建还是直接更新配置
          if (this.form.templateName && !this.form.id) {
            // 从模板创建配置
            const createData = {
              templateName: this.form.templateName,
              configName: this.form.configName,
              customizations: {
                logLevel: this.form.logLevel,
                enableLocationLog: this.form.enableLocationLog,
                // 后端已改为秒，直接使用
                locationLogInterval: this.form.locationLogInterval,
                logUploadInterval: this.form.logUploadInterval,
                maxLogFiles: this.form.maxLogFiles,
                isActive: this.form.isActive || false
              }
            }
            this.$emit('create-from-template', createData)
          } else {
            // 直接更新配置
            const configData = {
              id: this.form.id || null,  // 更新时必须提供，新建时设为null
              configName: this.form.configName,
              logLevel: this.form.logLevel,
              enableLocationLog: this.form.enableLocationLog,
              // 后端已改为秒，直接使用
              locationLogInterval: this.form.locationLogInterval,
              logUploadInterval: this.form.logUploadInterval,
              maxLogFiles: this.form.maxLogFiles,
              isActive: this.form.isActive || false
            }

            // 编辑时才发送版本号
            if (this.isEdit && this.form.configVersion) {
              configData.configVersion = this.form.configVersion
            }
            this.$emit('confirm', configData)
          }
        }
      })
    },
    
    handleClose() {
      this.dialogVisible = false
    },
    
    resetForm() {
      this.form = {
        id: null,
        configName: '',
        templateName: '',
        logLevel: 'INFO',
        enableLocationLog: true,
        locationLogInterval: 3,
        logUploadInterval: 60,
        maxLogFiles: 5,
        configVersion: '',
        isActive: false,
        description: '',
        customSettings: ''
      }
    },

    // 验证日志级别
    validateLogLevel(rule, value, callback) {
      const validLevels = ['DEBUG', 'INFO', 'WARN', 'ERROR']
      if (!validLevels.includes(value)) {
        callback(new Error('日志级别只能是 DEBUG/INFO/WARN/ERROR'))
      } else {
        callback()
      }
    },

    // 验证正数
    validatePositiveNumber(rule, value, callback) {
      if (!value || value <= 0) {
        callback(new Error('必须大于0'))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.input-suffix {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.form-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  
  i {
    margin-right: 4px;
  }
}
</style>
