<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-21 17:14:38
 * @Description: 收款清单
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      row-key="label"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <!--<template #btn>-->
      <!--  <el-button-->
      <!--    type="success"-->
      <!--    icon="el-icon-plus"-->
      <!--    size="mini"-->
      <!--    @click="handlePayRegister"-->
      <!--  >-->
      <!--    支付登记-->
      <!--  </el-button>-->
      <!--</template>-->
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            v-if="row.payMode?.value === 'OFFLINE'"
            icon="el-icon-view"
            @click="handleCheck(row)"
          >
            查看
          </el-button>
          <el-button
            v-if="row.status?.value === 'WAIT_APPROVE'"
            icon="el-icon-warning-outline"
            type="btn3"
            @click="handlePayOrder(row)"
          >
            审核
          </el-button>
          <el-button
            v-if="row.refundAmount > 0"
            icon="el-icon-document"
            @click="handleRefund(row.id)"
          >
            退款记录
          </el-button>
          <el-button
            v-if="
              row.payMode?.value === 'OFFLINE' &&
              row.amount > row.refundAmount &&
              !(+row.amount < +row.refundAmount)
            "
            icon="el-icon-eleme"
            @click="handleEditRefund(row.id, row.amount)"
          >
            线下退款
          </el-button>
        </div>
      </template>
    </ProTable>
    <!--  查看审核订单详情  -->
    <ProDrawer
      :no-footer="true"
      :title="drawerTitle"
      :top="'10%'"
      :value="checkVoucher"
      size="85%"
      @cancel="cancelVoucherDrawer"
    >
      <el-descriptions :column="3" border class="margin-top" title="订单信息">
        <el-descriptions-item label="订单状态">
          <el-tag
            v-if="descriptionsData?.status"
            :type="getStatusTag(descriptionsData?.status)"
            >{{ descriptionsData?.status?.label }}</el-tag
          >
        </el-descriptions-item>
        <el-descriptions-item label="订单编号">
          {{ descriptionsData?.tradeOrderNumber }}
        </el-descriptions-item>
        <el-descriptions-item label="总金额（元）">
          {{ descriptionsData?.totalAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="优惠减免金额（元）">
          {{ descriptionsData?.ticketAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="实付金额（元）">
          {{ descriptionsData?.amount }}
        </el-descriptions-item>
        <el-descriptions-item label="关联客户">
          {{ descriptionsData?.customerName }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ descriptionsData?.createdAt }}
        </el-descriptions-item>
      </el-descriptions>

      <p class="tit-box m-b-12">商品信息</p>
      <!-- :data="auditData.tradeOrderDetailList || []" -->
      <ProTable
        ref="ProSPXXTable"
        :columns="columns2"
        :data="descriptionsListData || []"
        :height="200"
        :show-loading="false"
        :show-pagination="false"
        :show-search="false"
        :show-setting="false"
        :show-table-operator="false"
        sticky
      >
        <template #storageArticle="{ row }">
          {{ row.storageArticle.minUnit }}
        </template>
        <template #orderDetail="{ row }">
          <div class="fixed-width">
            <el-button
              v-if="
                row.tradeOrderOrigin.value === 'REPAIR_ORDER' ||
                row.tradeOrderOrigin.value === 'SALES_ORDER'
              "
              icon="el-icon-view"
              @click="handleInfo(row)"
            >
              查看
            </el-button>
          </div>
        </template>
      </ProTable>
      <p class="tit-box m-b-12">支付凭证</p>
      <div class="img-content">
        <div class="image-view">
          <div
            v-for="(item, index) in voucherImgUrlList"
            :key="index"
            class="img-list"
          >
            <el-image
              :preview-src-list="voucherImgUrlList"
              :initial-index="index"
              style="width: 100%; height: 100%"
              :src="item"
            ></el-image>
          </div>
        </div>
      </div>
      <p class="tit-box m-b-12">备注</p>
      <div class="img-content">
        <div class="image-view">
          {{ descriptionsData?.remark }}
        </div>
      </div>
    </ProDrawer>
    <!--  订单审核  -->
    <ProDialog
      :no-footer="true"
      :value="isShowDialog"
      width="60%"
      @cancel="isShowDialog = false"
    >
      <template #title> 凭证审核 </template>

      <!-- 订单审核 -->
      <el-descriptions border title="订单信息">
        <el-descriptions-item label="订单类型">
          {{ descriptionsData.tradeOrderOrigin?.label }} </el-descriptions-item
        ><el-descriptions-item label="订单状态">
          {{ descriptionsData.status?.label }}
        </el-descriptions-item>
        <el-descriptions-item label="订单编号">{{
          descriptionsData?.tradeOrderNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="总金额（元）">
          {{ descriptionsData?.totalAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="优惠券减免金额（元）">
          {{ descriptionsData?.ticketAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="实付金额（元）">
          {{ descriptionsData?.amount }}
        </el-descriptions-item>
        <el-descriptions-item label="关联客户">
          {{ descriptionsData?.customerName }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{
          descriptionsData?.createdAt
        }}</el-descriptions-item>
      </el-descriptions>

      <!-- 订单凭证 -->
      <div class="img-content">
        <div class="title">订单凭证</div>
        <div class="image-view">
          <div
            v-for="(item, index) in voucherImgUrlList"
            :key="index"
            class="img-list"
          >
            <el-image
              :preview-src-list="voucherImgUrlList"
              :initial-index="index"
              style="width: 100%; height: 100%"
              :src="item"
            ></el-image>
          </div>
        </div>
      </div>
      <!--   订单备注   -->
      <div class="img-content">
        <div class="title">备注</div>
        <div class="image-view">
          <el-input
            v-model="descriptionsData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入内容"
          >
          </el-input>
        </div>
      </div>
      <div class="footer-btn">
        <el-button
          type="danger"
          @click="handleAudit(descriptionsData, 'REJECT')"
        >
          驳回
        </el-button>
        <el-button
          type="primary"
          @click="handleAudit(descriptionsData, 'PAID_SUCCESS')"
        >
          审核通过
        </el-button>
      </div>
    </ProDialog>
    <!-- 销售商品详情-->
    <saleOrderDetail ref="saleOrderDetail" :title="componentTitle" />
    <!-- 维修工单详情 -->
    <WorkOrderDetail ref="workOrderDetail" :title="componentTitle" />
    <!-- 登记支付信息 -->
    <RegisterPayInfo ref="registerPay" @refresh="refresh" />
    <!-- 退款清单 -->
    <RefundDetail ref="refundDetail" />
    <!-- 手动创建退款记录 -->
    <CreateRefund ref="createRefund" @refresh="refresh" />
  </div>
</template>
<script>
import WorkOrderDetail from "./components/workOrderDetail.vue";
import saleOrderDetail from "./components/saleOrderDetail.vue";
import RegisterPayInfo from "@/views/financing/components/registerPayInfo.vue";
import RefundDetail from "@/views/financing/components/refundDetail.vue";
import CreateRefund from "@/views/financing/components/createRefund.vue";
import { payOrderListApi, payVoucherAuditApi } from "@/api/pay";
import { cloneDeep } from "lodash";
import { filterParam } from "@/utils";
import { Message } from "element-ui";

export default {
  name: "OrderReview",
  components: {
    WorkOrderDetail,
    saleOrderDetail,
    RegisterPayInfo,
    RefundDetail,
    CreateRefund,
  },
  data() {
    return {
      tableData: [],
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      queryParam: {},
      columns: [
        {
          dataIndex: "tradeOrderNumber",
          title: "订单号",
          valueType: "input",
          isSearch: true,
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "wechatTradeNumber",
          title: "微信交易流水号",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "wechatTradeNumber",
          title: "交易流水号",
          placeholder: "请输入微信交易流水号",
          valueType: "input",
          isSearch: true,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isSearch: true,
          valueType: "input",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          minWidth: 150,
          isSearch: true,
          valueType: "input",
          isTable: true,
        },
        {
          dataIndex: "phone",
          title: "联系电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "tradeOrderOrigin",
          title: "订单类型",
          isTable: true,
          formatter: (row) => row.tradeOrderOrigin?.label,
          minWidth: 100,
        },
        {
          dataIndex: "tradeOrderOrigins",
          title: "订单类型",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            { label: "销售订单", value: "SALES_ORDER" },
            { label: "维修订单", value: "REPAIR_ORDER" },
            { label: "安装工单", value: "INSTALL_ORDER" },
            { label: "收款单", value: "RECEIPT_ORDER" },
            { label: "合约预付款", value: "CONTRACT_PRE" },
            { label: "合约尾款支付", value: "CONTRACT_ARR" },
            { label: "合同分期款", value: "INSTALLMENT" },
          ],
          minWidth: 100,
        },
        {
          dataIndex: "amount",
          title: "支付金额",
          isTable: true,
          formatter: (row) => row.amount,
          align: "center",
          minWidth: 100,
        },
        {
          dataIndex: "refundAmount",
          title: "退款金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "payMode",
          title: "支付方式",
          isTable: true,
          formatter: (row) => row.payMode?.label,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            { label: "微信支付", value: "WECHART" },
            { label: "线下支付", value: "OFFLINE" },
            // { label: "周期支付", value: "CYCLE" },
          ],
          minWidth: 100,
        },
        {
          dataIndex: "status",
          title: "支付状态",
          clearable: true,
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          valueType: "select",
          option: [
            { label: "已创建", value: "CREATED" },
            { label: "待支付", value: "REPAIR_ORDER" },
            { label: "支付成功", value: "PAID_SUCCESS" },
            { label: "支付失败", value: "PAID_FAILED" },
            { label: "待审核", value: "WAIT_APPROVE" },
            { label: "驳回", value: "REJECT" },
            { label: "关闭", value: "CLOSED" },
          ],
          minWidth: 100,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "finishedAt",
          title: "支付/审核时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          fixed: "right",
          tableSlot: "actions",
          width: 250,
        },
      ],
      columns2: [
        {
          dataIndex: "tradeOrderNumber",
          title: "订单号",
          width: 180,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "tradeOrderOrigin",
          title: "订单类型",
          isTable: true,
          isSearch: true,
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 12,
          formatter: (row) => row.tradeOrderOrigin?.label,
          option: [
            { label: "销售订单", value: "SALES_ORDER" },
            { label: "维修订单", value: "REPAIR_ORDER" },
          ],
          disabled: true,
          optionskey: {
            label: "label",
            value: "value",
          },
          filterOption: {
            key: "type",
            value: 1,
          },
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          // width: 150,
          isSearch: true,
          valueType: "input",
          isTable: true,
        },
        {
          dataIndex: "phone",
          title: "联系电话",
          isTable: true,
        },
        // {
        //   dataIndex: "orderAmount",
        //   title: "订单金额",
        //   isTable: true,
        //   formatter: (row) => row.orderAmount / 100,
        // },
        {
          dataIndex: "amount",
          title: "支付金额",
          formatter: (row) => row.amount,
          isTable: true,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
        },
        {
          dataIndex: "status",
          title: "审核状态",
          clearable: true,
          isSearch: true,
          valueType: "select",
          isTable: true,
          formatter: (row) => row.status?.label,
          option: [
            { label: "待审核", value: "WAIT_APPROVE" },
            { label: "通过", value: "PASS" },
            { label: "驳回", value: "REJECT" },
          ],
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          isSearch: true,
          width: 150,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
        },
        {
          dataIndex: "finishedAt",
          title: "完成时间",
          isTable: true,
          isSearch: false,
          width: 150,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
        },
        {
          dataIndex: "Actions",
          width: 160,
          fixed: "right",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "orderDetail",
        },
      ],
      drawerTitle: "查看 — ",
      checkVoucher: false,
      descriptionsListData: [],
      voucherImgUrlList: [],
      descriptionsData: {},
      isShowDialog: false,
      dialogVisible: false,
      dialogVisible1: false,
      componentTitle: "",
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      this.queryParam.startDate = this.queryParam.finishedAt
        ? this.queryParam.finishedAt[0]
        : null;
      this.queryParam.endDate = this.queryParam.finishedAt
        ? this.queryParam.finishedAt[1]
        : null;
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.finishedAt;
      payOrderListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    // 查看退款清单
    handleRefund(id) {
      this.$refs.refundDetail.show(id);
    },
    // 线下退款填写金额
    handleEditRefund(id, amount) {
      this.$refs.createRefund.show(id, amount);
    },
    handlePayOrder(row) {
      this.handleVoucherImg(row?.voucherImg);
      this.descriptionsData = cloneDeep(row);
      this.isShowDialog = true;
    },
    handleCheck(row) {
      this.drawerTitle = `查看 — ${row.customerSeqId}`;
      this.checkVoucher = true;
      this.descriptionsData = row;
      this.handleVoucherImg(row?.voucherImg);
      this.descriptionsListData.push(row);
    },
    handleAudit(row, status) {
      const confirmTitle = status === "PAID_SUCCESS" ? "通过" : "驳回";
      this.$confirm(`确认${confirmTitle}该订单的审核吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const params = {
          id: row.id,
          status: status,
          remark: row.remark,
        };
        try {
          const result = await payVoucherAuditApi(params);
          if (result.code === 200) {
            if (status === "PAID_SUCCESS") {
              Message.success("订单审核通过");
            } else {
              Message.warning("订单审核未通过");
            }
            this.isShowDialog = false;
            this.$refs.ProTable.refresh();
          }
        } catch (e) {
          Message.error("系统错误");
        }
      });
    },
    cancelVoucherDrawer() {
      this.checkVoucher = false;
      this.descriptionsListData = [];
    },
    handleInfo(row) {
      this.componentTitle = "查看 - " + row.tradeOrderNumber;
      if (
        row.tradeOrderOrigin &&
        row.tradeOrderOrigin?.value === "SALES_ORDER"
      ) {
        this.$refs.saleOrderDetail.show(row.tradeOrderNumber);
      } else if (
        row.tradeOrderOrigin &&
        row.tradeOrderOrigin?.value === "REPAIR_ORDER"
      ) {
        this.$refs.workOrderDetail.show(row.tradeOrderNumber);
      }
    },
    // 支付登记
    handlePayRegister() {
      this.$refs.registerPay.show();
    },
    getStatusTag(status) {
      switch (status?.value) {
        case "CREATED":
          return "success";
        case "REPAID_ORDER":
          return "warning";
        case "PAID_SUCCESS":
          return "success";
        case "PAID_FAILED":
          return "danger";
        case "WAIT_APPROVE":
          return "warning";
        case "REJECT":
          return "danger";
        case "CLOSED":
          return "info";
        default:
          return "";
      }
    },
    // 处理凭证图片
    handleVoucherImg(data) {
      this.voucherImgUrlList = [];
      data.map((item) => {
        this.voucherImgUrlList.push(item.url);
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>
<style lang="scss" scoped>
.footer-btn {
  display: flex;
  justify-content: center;
}
.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
::v-deep.img-content {
  display: flex;
  align-items: center;
  .title {
    width: 80px;
    font-weight: 700;
    font-size: 1rem;
    color: #000;
  }
  .image-view {
    width: 100%;
    padding: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    .img-list {
      width: 135px;
      height: 135px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      .el-image {
        object-fit: contain;
        .el-image__inner {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      /* 鼠标经过有遮罩效果 */
      //&:hover {
      //  position: relative;
      //  .mask {
      //    position: absolute;
      //    top: 0;
      //    left: 0;
      //    width: 100%;
      //    height: 100%;
      //    background: rgba(0, 0, 0, 0.5);
      //    display: flex;
      //    justify-content: center;
      //    align-items: center;
      //    cursor: pointer;
      //    &::after {
      //      content: "+";
      //      font-size: 36px;
      //      color: #fff;
      //    }
      //  }
      //}
    }
  }
}
</style>
