<template>
  <div class="statistcal">
    <div class="headTop">
      <div class="dateTab">
        <div class="tab1" @click="getDateDaysAgo('0')">
          <el-button
            size="small"
            :type="canvasDate == '0' ? 'primary' : ''"
            style="width: 80px"
            >今日</el-button
          >
        </div>
        <div class="tab2" @click="getDateDaysAgo('7')">
          <el-button
            size="small"
            :type="canvasDate == '7' ? 'primary' : ''"
            style="width: 80px"
            >7日</el-button
          >
        </div>
        <div class="tab3" @click="getDateDaysAgo('15')">
          <el-button
            size="small"
            :type="canvasDate == '15' ? 'primary' : ''"
            style="width: 80px"
            >15日</el-button
          >
        </div>
        <div class="tab4" @click="getDateDaysAgo('30')">
          <el-button
            size="small"
            :type="canvasDate == '30' ? 'primary' : ''"
            style="width: 80px"
            >30日</el-button
          >
        </div>
      </div>
      <div class="workBox">
        <div>最近一次访问时间: {{ orderData.lastPageTime }}</div>
        <div>最近一次订单时间: {{ orderData.lastTradeOrderTime }}</div>
        <div>最近一次工单时间: {{ orderData.lastWorkOrderTime }}</div>
        <div>最近一次数据上报时间: {{ orderData.lastIotTime }}</div>
        <div>累计访问次数: {{ orderData.pageCountNum }}</div>
        <div>累计搜索无果次数: {{ orderData.searchNoDataNum }}</div>
        <div>最近一个月访问次数: {{ orderData.lastThirtyDayCount }}</div>
      </div>
    </div>
    <div class="head">
      <div class="left">
        <!--        <div class="tab">-->
        <!--          <div class="tab1" @click="changeCanvas('MALL_HOME')">-->
        <!--            <el-button-->
        <!--              size="small"-->
        <!--              :type="canvasTitle == 'MALL_HOME' ? 'primary' : ''"-->
        <!--              style="width: 80px"-->
        <!--              >首页</el-button-->
        <!--            >-->
        <!--          </div>-->
        <!--          <div class="tab2" @click="changeCanvas('KNOWLEDGE_HOME')">-->
        <!--            <el-button-->
        <!--              size="small"-->
        <!--              :type="canvasTitle == 'KNOWLEDGE_HOME' ? 'primary' : ''"-->
        <!--              style="width: 80px"-->
        <!--              >知识库</el-button-->
        <!--            >-->
        <!--          </div>-->
        <!--          <div class="tab3" @click="changeCanvas('MALL_GOODS_DETAIL')">-->
        <!--            <el-button-->
        <!--              size="small"-->
        <!--              :type="canvasTitle == 'MALL_GOODS_DETAIL' ? 'primary' : ''"-->
        <!--              style="width: 80px"-->
        <!--              >商品详情</el-button-->
        <!--            >-->
        <!--          </div>-->
        <!--        </div>-->
        <div id="satisfiesCanvas" style="height: 400px"></div>
      </div>
    </div>
    <div class="bottom">
      <div>访问时间:</div>
      <div>访问手机号:</div>
      <div>访问一级页面:</div>
      <div>访问二级页面:</div>
      <div>访问三级页面:</div>
      <div>停留时长:</div>
    </div>
    <!--    <div class="bottom">-->
    <!--      <el-table :data="tableData" style="width: 100%">-->
    <!--        <el-table-column type="index" width="50"> </el-table-column>-->
    <!--        <el-table-column prop="eventSource" label="来源">-->
    <!--          <template slot-scope="scope">-->
    <!--            {{ scope.row.eventSource == "MALL_HOME" ? "首页" : "知识库" }}-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--        <el-table-column prop="eventType" label="类型">-->
    <!--          <template slot-scope="scope">-->
    <!--            {{-->
    <!--              scope.row.eventType == "NO_LOGIN"-->
    <!--                ? "未登录"-->
    <!--                : scope.row.eventType == "Key_word_SEARCH"-->
    <!--                ? "关键字搜索"-->
    <!--                : "关键字+参数搜索"-->
    <!--            }}-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--        <el-table-column prop="originParam" label="搜索参数"> </el-table-column>-->
    <!--        <el-table-column prop="createTime" label="操作时间"> </el-table-column>-->
    <!--        <el-table-column prop="total" label="查询结果">-->
    <!--          <template slot-scope="scope">-->
    <!--            {{ scope.row.total > 0 ? scope.row.total + "条" : "无果" }}-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--      </el-table>-->
    <!--    </div>-->
    <!--    <el-pagination-->
    <!--      background-->
    <!--      :current-page.sync="currentPage1"-->
    <!--      :page-size="10"-->
    <!--      layout="total, prev, pager, next"-->
    <!--      :total="total"-->
    <!--      @size-change="handleSizeChange"-->
    <!--      @current-change="handleCurrentChange"-->
    <!--    >-->
    <!--    </el-pagination>-->
  </div>
</template>
<script>
import * as echarts from "echarts";
import { dataCountApi, pageViewCountApi, searchOutApi } from "@/api/customer";
import ProTable from "@/components/ProTable/index.vue";
import { createSnapToPower } from "ol/resolutionconstraint";
export default {
  components: { ProTable },
  props: {
    id: {
      type: [String, null],
      default: null,
    },
  },
  data() {
    return {
      currentPage1: 1,
      total: null,
      orderData: {},
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      canvasDate: "0",
      canvasTitle: "MALL_HOME",
      xOption: [],
      yOption: [],
      option: {
        xAxis: {
          type: "category",
          data: this.xOption,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: this.yOption,
            type: "line",
            // smooth: true
          },
        ],
      },
      columns: [],
      currentISODateTime: "",
      dateDaysAgo: "",
    };
  },
  created() {
    const now = new Date();
    this.currentISODateTime = now.toISOString().slice(0, 10);
    if (this.dateDaysAgo == "") {
      this.dateDaysAgo = this.currentISODateTime;
    }
    this.refresh();
    searchOutApi({ customerId: this.id, pageNumber: 1, pageSize: 10 }).then(
      (res) => {
        this.tableData = res.data.rows;
        this.total = Number(res.data.total);
      }
    );
    pageViewCountApi({
      customerId: this.id,
      pageViewPathEnums: "MALL_HOME",
      startTime: "'" + this.currentISODateTime + "'",
      endTime: "'" + this.currentISODateTime + "'",
    }).then((res) => {
      // this.total = res.data.total
      res.data.map((item) => {
        this.xOption.push(item.time);
        this.yOption.push(item.count);
      });
      this.option.xAxis.data = this.xOption;
      this.option.series[0].data = this.yOption;
      this.satisfiesCanvas();
    });
    dataCountApi(this.id).then((res) => {
      this.orderData = res.data;
    });
  },
  mounted() {},
  methods: {
    async loadData(params) {
      try {
        const result = await searchOutApi({
          ...params,
          customerId: this.id,
        });
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    // 30
    getDateDaysAgo(val) {
      this.xOption = [];
      this.yOption = [];
      this.canvasDate = val;
      const now = new Date();
      now.setDate(now.getDate() - val); // 设置日期为30天前
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      this.dateDaysAgo = `${year}-${month}-${day}`;
      // 调用接口
      pageViewCountApi({
        customerId: this.id,
        pageViewPathEnums: this.canvasTitle,
        startTime: "'" + this.dateDaysAgo + "'",
        endTime: "'" + this.currentISODateTime + "'",
      }).then((res) => {
        res.data.map((item) => {
          this.xOption.push(item.time);
          this.yOption.push(item.count);
        });
        this.option.xAxis.data = this.xOption;
        this.option.series[0].data = this.yOption;
        this.satisfiesCanvas();
      });
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      searchOutApi({ customerId: this.id, pageNumber: val, pageSize: 10 }).then(
        (res) => {
          this.total = Number(res.data.total);
          this.tableData = res.data.rows;
        }
      );
    },
    changeCanvas(res) {
      this.canvasTitle = res;
      pageViewCountApi({
        customerId: this.id,
        pageViewPathEnums: res,
        startTime: "'" + this.dateDaysAgo + "'",
        endTime: "'" + this.currentISODateTime + "'",
      }).then((res) => {
        this.xOption = [];
        this.yOption = [];

        res.data.map((item) => {
          this.xOption.push(item.time);
          this.yOption.push(item.count);
        });
        this.option.xAxis.data = this.xOption;
        this.option.series[0].data = this.yOption;
        console.log(this.option);
        this.satisfiesCanvas();
      });
    },
    satisfiesCanvas() {
      var myChart = echarts.init(document.getElementById("satisfiesCanvas"));
      myChart.setOption(this.option);
    },

    refresh() {
      this.$emit("refresh");
    },
  },
};
</script>
<style lang="scss" scoped>
.statistcal {
  .headTop {
    display: flex;
    justify-content: space-around;

    .dateTab {
      display: flex;
      justify-content: flex-end;
      gap: 5px;
      margin-right: 30px;
    }

    .workBox {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: 20px;
      div {
        text-wrap: wrap;
        font-size: 16px;
        color: #6488cf;
      }
    }
  }

  .head {
    position: relative;
    margin-top: 20px;

    .left {
      //margin: 0 0 0 40px;
      flex: 1;
    }

    .tab {
      position: absolute;
      left: -5px;
      top: 50px;
      z-index: 999;

      div:not(:first-child) {
        margin-top: 5px;
      }
    }
  }

  .bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    div {
      font-size: 16px;
      color: #6488cf;
    }
  }
}
</style>
