<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:55
 * @Description: 工程师管理
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :layout="{ labelWidth: '80px' }"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleInfo(slotProps.row)"
          >
            查看
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>
        </span>
      </template>
    </ProTable>

    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="50%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType === 'info'"
      @cancel="dialogVisible = false"
      @ok="handleDialogOk"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
        <template #regionAddress1>
          <div style="max-height: 300px; overflow-y: scroll">
            <el-descriptions class="margin-top" :column="1" border>
              <el-descriptions-item v-for="item in form.region" :key="item.id">
                <template slot="label"> 地区 </template>
                {{ item.fullRegion }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </template>
        <template #regionAddress>
          <div class="location">
            <el-cascader
              v-model="addressCode"
              :value="addressCode"
              :disabled="methodType === 'info'"
              style="width: 100%"
              filterable
              clearable
              collapse-tags
              :options="options1"
              :props="{
                label: 'name',
                value: 'code',
                children: 'children',
                expandTrigger: 'click',
                multiple: true,
              }"
              leaf-only
              @change="handleReginChange"
            ></el-cascader>
            <span></span>
          </div>
        </template>
        <template #locationRegionCode>
          <div class="location">
            <el-cascader
              v-model="form.locationRegionCode"
              :value="form.locationRegionCode"
              :disabled="methodType === 'info'"
              style="width: 100%"
              filterable
              clearable
              collapse-tags
              :options="options1"
              :props="{
                label: 'name',
                value: 'code',
                children: 'children',
                expandTrigger: 'click',
              }"
              leaf-only
              @change="handleReginChange3"
            ></el-cascader>
            <span></span>
          </div>
        </template>
        <template #engineerSkills>
          <el-button
            v-if="methodType !== 'info'"
            type="primary"
            plain
            style="width: 100px; padding: 12px 0"
            @click="showModDialog"
          >
            选择机型
          </el-button>
          <DataTable
            ref="ProTable1"
            :columns="columns1"
            :show-setting="false"
            :show-pagination="false"
            :show-search="false"
            row-key="index"
            :data="tableData1"
            sticky
            :height="350"
            style="width: 100%; margin-top: 20px"
            :show-table-operator="false"
          >
            <template #skillExp="slotProps">
              <el-select
                v-model="slotProps.row.skillExp"
                placeholder="熟练程度"
                :disabled="methodType == 'info'"
              >
                <el-option
                  v-for="item in skillExpoptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </template>

            <template #actions="slotProps">
              <span v-if="methodType !== 'info'" class="fixed-width">
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDeleteGoods(slotProps.row)"
                >
                  删除
                </el-button>
              </span>
            </template>
          </DataTable>
        </template>
      </ProForm>
    </ProDrawer>

    <!-- 选择品牌/系列弹窗 -->
    <ProDialog
      :value="showModelDialog"
      title="选择品牌/系列"
      width="1200px"
      :confirm-loading="false"
      top="50px"
      @ok="handleChooseDialogConfirm1"
      @cancel="showModelDialog = false"
    >
      <SeriesList ref="ModelList" @choose="handleSelectionChange1"></SeriesList>
    </ProDialog>
  </div>
</template>
<script>
import {
  getEngineerByPageApi,
  updateEngineerApi,
  getEngineerInfoApi,
} from "@/api/repair";
import { regionTreeApi } from "@/api/store";
import { isEmpty, cloneDeep } from "lodash";
import { dictTreeByCodeApi } from "@/api/user";
import { productAllApi } from "@/api/dispose";
import { filterName, getAllParentArr } from "@/utils";
import SeriesList from "@/views/dispose/components/seriesList.vue";

export default {
  name: "EngineerTeam",
  components: { SeriesList },
  mixins: [],
  props: {},
  data() {
    return {
      options: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {},
      productIdName: null,
      showModelDialog: false,
      addressCode: [],
      options1: [],
      chooseModelSelection: {},
      skillExpoptions: [],
      columns: [
        {
          dataIndex: "code",
          title: "用户账号",
          isTable: true,
        },

        {
          dataIndex: "name",
          title: "工程师",
          isTable: true,
          isSearch: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "phone",
          title: "手机号",

          isSearch: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "mobileNumber",
          title: "手机号",
          isTable: true,
        },
        {
          dataIndex: "Actions",
          title: "操作",
          align: "left",
          width: 180,
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],

      //新增
      methodType: "add",
      confirmLoading: false,

      form: {},
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      formcolumns: [],

      columns1: [
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "serial",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "skillExp",
          title: "维修熟练度",
          isTable: true,
          tableSlot: "skillExp",
        },

        {
          dataIndex: "Actions",
          width: 120,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      tableData1: [],
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    this.$refs.ProTable.refresh();
    this.init();
  },
  methods: {
    init() {
      dictTreeByCodeApi(5000).then((res) => {
        this.skillExpoptions = res.data;
      });
      productAllApi().then((res) => {
        this.options = res.data;
      });
      dictTreeByCodeApi(2100).then((res) => {
        this.goodsTypeOptions = res.data;
      });
      regionTreeApi().then((res) => {
        this.options1 = res.data;
      });
    },

    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign({}, parameter);
      getEngineerByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.addressCode = [];
      this.chooseModelSelection = [];
      this.tableData1 = [];
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },

    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑 - " + row.name;
      this.resetFrom();
      this.formcolumns = [
        {
          dataIndex: "name",
          isForm: true,
          title: "用户名",
          valueType: "text",
          clearable: true,
          formSpan: 12,
        },

        {
          dataIndex: "mobileNumber",
          isForm: true,
          title: "手机号",
          valueType: "text",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "sex",
          isForm: true,
          title: "性别",
          valueType: "text",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "state",
          isForm: true,
          title: "账号状态",
          valueType: "text",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "orderNumber",
          isForm: true,
          title: "接单次数",
          valueType: "text",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "yearOfService",
          isForm: true,
          clearboth: true,
          title: "从业年限",
          valueType: "input",
          inputType: "number",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "locationRegionCode",
          isForm: true,
          title: "所在地址",
          formSlot: "locationRegionCode",
          clearable: true,
          formSpan: 12,
        },

        {
          dataIndex: "introduce",
          isForm: true,
          title: "工程师描述",
          valueType: "input",
          inputType: "textarea",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入工程师描述",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "regionCode",
          isForm: true,
          title: "维修范围",
          valueType: "input",
          formSlot: "regionAddress",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请选择维修范围",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "engineerSkills",
          isForm: true,
          title: "维修能力",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          formSlot: "engineerSkills",
          // prop: [
          //   {
          //     required: true,
          //     message: "请选择维修能力",
          //     trigger: "change",
          //   },
          // ],
        },
      ];
      getEngineerInfoApi(row.id).then((res) => {
        res.data.userManageVo.sex = res.data.userManageVo.sex.label;
        res.data.userManageVo.state = res.data.userManageVo.state.label;
        this.form = { ...res.data.engineerInfo, ...res.data.userManageVo };

        res.data.region?.map((el) => {
          const productArr = [];
          const arr = el.path.split("/");
          arr.shift();
          arr.map((item) => {
            productArr.push(+item);
          });
          this.addressCode.push(productArr);
        });
        res.data.engineerSkills.map((el) => {
          el.skillExp = el.skillExp.value;
          this.tableData1.push(el);
          this.chooseModelSelection.push({
            id: el.productId,
            brand: el.brand,
            serial: el.serial,
          });
        });
        this.methodType = "edit";
        this.dialogVisible = true;
        this.$nextTick((e) => {
          this.$refs["proform"].resetFormParam();
        });
      });
    },
    //响应编辑
    update() {
      let no = 0;
      this.tableData1.map((el) => {
        console.log(el);
        if (!el.skillExp) {
          no++;
        }
      });
      if (no > 0) {
        this.$message.warning("请完善工程师维修能力");
        this.confirmLoading = false;
        return false;
      }
      this.form.engineerSkills = cloneDeep(this.tableData1);
      console.log(this.form);
      updateEngineerApi(this.form)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.name;
      this.resetFrom();
      this.formcolumns = [
        {
          dataIndex: "name",
          isForm: true,
          title: "用户名",
          valueType: "text",
          clearable: true,
          formSpan: 12,
        },

        {
          dataIndex: "mobileNumber",
          isForm: true,
          title: "手机号",
          valueType: "text",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "sex",
          isForm: true,
          title: "性别",
          valueType: "text",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "state",
          isForm: true,
          title: "账号状态",
          valueType: "text",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "orderNumber",
          isForm: true,
          title: "接单次数",
          valueType: "text",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "yearOfService",
          isForm: true,
          clearboth: true,
          title: "从业年限",
          valueType: "text",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "locationRegionCode",
          isForm: true,
          title: "所在地址",
          formSlot: "locationRegionCode",
          clearable: true,
          formSpan: 12,
        },
        {
          dataIndex: "introduce",
          isForm: true,
          title: "工程师描述",
          valueType: "text",
          clearable: true,
          formSpan: 24,
          isWrap: true,
        },
        {
          dataIndex: "regionCode",
          isForm: true,
          title: "维修范围",
          formSlot: "regionAddress1",
          clearable: true,
          formSpan: 24,
        },
        {
          dataIndex: "engineerSkills",
          isForm: true,
          title: "维修能力",
          clearable: true,
          formSpan: 24,
          formSlot: "engineerSkills",
        },
      ];
      getEngineerInfoApi(row.id).then((res) => {
        res.data.userManageVo.sex = res.data.userManageVo.sex.label;
        res.data.userManageVo.state = res.data.userManageVo.state.label;
        this.form = {
          region: res.data.region || [],
          ...res.data.engineerInfo,
          ...res.data.userManageVo,
        };
        res.data.region?.map((el) => {
          const productArr = [];
          const arr = el.path.split("/");
          arr.shift();
          arr.map((item) => {
            productArr.push(+item);
          });
          this.addressCode.push(productArr);
        });
        console.log(this.addressCode);
        res.data.engineerSkills.map((el) => {
          el.skillExp = el.skillExp.value;
          this.tableData1.push(el);
          this.chooseModelSelection.push({
            id: el.productId,
            brand: el.brand,
            serial: el.serial,
          });
        });
        this.methodType = "info";
        this.dialogVisible = true;
        this.$nextTick((e) => {
          this.$refs["proform"].resetFormParam();
        });
      });
    },

    handleChange(item) {
      console.log(item);
      this.queryParam.productTreeId = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productTreeId.push(id);
      });
    },
    handleReginChange(val) {
      this.form.regionCode = [];
      val.map((el) => {
        const res = el[el.length - 1];

        // const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.form.regionCode.push(res);
      });
    },
    handleReginChange3(val) {
      this.form.locationRegionCode = val[val.length - 1];
    },
    showModDialog() {
      this.showModelDialog = true;
      this.$nextTick(() => {
        this.$refs.ModelList.$refs.ProTable.$refs.ProElTable.clearSelection();
        this.$refs.ModelList.init();
        if (this.chooseModelSelection.length > 0) {
          this.chooseModelSelection.map((row) => {
            this.$refs.ModelList.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
              row,
              true
            );
          });
        }
      });
    },
    handleSelectionChange1(val) {
      this.chooseModelSelection = cloneDeep(val);
    },
    handleChooseDialogConfirm1() {
      // this.tableData1 = [];
      this.chooseModelSelection.map((el) => {
        console.log(
          this.tableData1.find((item) => {
            return item.productId === el.id;
          })
        );
        if (
          !this.tableData1.find((item) => {
            return item.productId === el.id;
          })
        ) {
          const obj = {
            productId: el.id,
            brand: el.brand,
            serial: el.serial,
          };
          this.tableData1.push(obj);
        }
      });
      this.showModelDialog = false;
    },
    handleDeleteGoods(row) {
      this.tableData1.splice(
        this.tableData1.findIndex((item) => item.productId === row.productId),
        1
      );
      this.chooseModelSelection.splice(
        this.chooseModelSelection.findIndex(
          (item) => item.id === row.productId
        ),
        1
      );
    },
  },
};
</script>
<style>
.el-checkbox {
  line-height: 40px;
}

.el-collapse {
  border: none;
}

.el-collapse-item__header {
  border: none;
  border-bottom: 1px solid #ebeef5;
}

.el-collapse-item__content {
  padding: 0;
}

.el-collapse-item:last-child {
  margin: auto;
}
</style>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: wrap;
}

.tree-li {
  list-style: none;
  margin: 0;

  li {
    list-style: none;
    display: inline-block;
    margin-left: 20px;
  }
}
</style>
