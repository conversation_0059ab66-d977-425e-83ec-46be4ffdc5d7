import React from 'react';
import { Outlet } from 'react-router-dom';
import { useDevice } from '../../hooks/useDevice';
import { WebsiteHeader } from './WebsiteHeader';
import { WebsiteFooter } from './WebsiteFooter';
import { MobileWebsiteHeader } from './MobileWebsiteHeader';
import { MobileWebsiteFooter } from './MobileWebsiteFooter';

const WebsiteLayout: React.FC = () => {
  const { isMobile } = useDevice();

  return (
    <div className="min-h-screen flex flex-col">
      {/* 头部导航 */}
      {isMobile ? <MobileWebsiteHeader /> : <WebsiteHeader />}

      {/* 主要内容区域 - 添加上边距避开固定头部 */}
      <main className={`flex-1 ${isMobile ? 'pt-12' : 'pt-16'}`}>
        <Outlet />
      </main>

      {/* 底部 */}
      {isMobile ? <MobileWebsiteFooter /> : <WebsiteFooter />}
    </div>
  );
};

export default WebsiteLayout;