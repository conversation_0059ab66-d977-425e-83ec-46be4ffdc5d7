/*
 * @Description:
 * @Autor: shh
 * @Date: 2022-07-14 10:43:31
 * @LastEditors: shh
 * @LastEditTime: 2022-11-29 12:25:22
 */
const setting = require("./setting");
const { viewportRem } = setting;

(function (doc, win) {
  //只需要调整prop 即可改变转换基数
  var docEl = doc.documentElement;
  var resizeEvt =
    "orientationchange" in window ? "orientationchange" : "resize";
  var setRem = function () {
    var clientWidth = docEl.clientWidth;
    if (!clientWidth) return;
    docEl.style.fontSize =
      (clientWidth / viewportRem.viewportWidth) * viewportRem.rootValue + "px";
  };
  if (!doc.addEventListener) return;
  win.addEventListener(resizeEvt, setRem, false);
  doc.addEventListener("DOMContentLoaded", setRem, false);
  win.addEventListener("pageShow", setRem, false);
})(document, window);
