<!--
 * @Author: wskg
 * @Date: 2025-02-13 14:40:45
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 11:00:04
 * @Description: 机器拆机
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :height="500"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #picUrls="{ row }">
        <el-image
          v-if="row.picUrls && row.picUrls.length > 0"
          style="max-width: 100px; max-height: 100px"
          :src="getMachinePicsImg(row)"
          :preview-src-list="[getMachinePicsImg(row)]"
        ></el-image>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <!--<el-button icon="el-icon-view" @click="handleEdit(row, 'info')">-->
          <!--  查看-->
          <!--</el-button>-->
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <el-button
            v-if="row.status?.value === 'WAIT_APPROVE'"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'edit')"
          >
            审核
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 查看、审核 -->
    <ProDrawer
      :value="drawerVisible"
      :no-confirm-footer="true"
      :title="drawerTitle"
      :confirm-text="'确认拆机零件'"
      :confirm-button-disabled="confirmLoading"
      size="50%"
      @ok="handleAudit"
      @cancel="drawerCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParam"
        :form-list="formColumns"
        :open-type="editType"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        @proSubmit="formSubmit"
      >
        <template #hostType>
          {{ formParam.hostType?.label || "" }}
        </template>
        <template #articleCode>
          <div v-if="formParam.articleCode">
            {{ formParam.articleCode }}
          </div>
          <el-button
            v-if="!formParam.articleCode && editType === 'edit'"
            type="primary"
            size="mini"
            @click="handleAddArticle"
          >
            新增物品
          </el-button>
        </template>
        <template #picUrls>
          <div v-if="formParam.picUrls && formParam.picUrls?.length > 0">
            <el-image
              v-for="item in formParam.picUrls"
              :key="item.url"
              style="width: 100px; height: 100px"
              :src="item.url"
              :preview-src-list="[item.url]"
            >
            </el-image>
          </div>
        </template>
        <!--<template #machineRepairReplaces>-->
        <!--  <div class="title-box">更换耗材零件清单</div>-->
        <!--  <ProTable-->
        <!--    :show-loading="false"-->
        <!--    :show-search="false"-->
        <!--    :show-pagination="false"-->
        <!--    :show-setting="false"-->
        <!--    :columns="replaceColumns"-->
        <!--    :data="formParam.machineRepairReplaces"-->
        <!--  >-->
        <!--    <template #picUrl="slotProps">-->
        <!--      <el-image-->
        <!--        :preview-src-list="[slotProps.row?.skuInfo?.picUrl[0]?.url]"-->
        <!--        style="width: 100px; height: 100px"-->
        <!--        :src="slotProps.row?.skuInfo?.picUrl[0]?.url"-->
        <!--      ></el-image>-->
        <!--    </template>-->

        <!--    <template #saleAttrVals="slotProps">-->
        <!--      <div-->
        <!--        v-for="attr in slotProps.row?.skuInfo?.saleAttrVals"-->
        <!--        :key="attr.val"-->
        <!--      >-->
        <!--        {{ attr.name }}:{{ attr.val }}-->
        <!--      </div>-->
        <!--    </template>-->
        <!--  </ProTable>-->
        <!--</template>-->
      </ProForm>
      <template #footer>
        <div v-if="editType === 'edit'" class="footer">
          <el-button type="danger" @click="handleAudit('REJECT')">
            驳回
          </el-button>
          <el-button type="primary" @click="handleAudit('PASS')">
            审核通过
          </el-button>
          <el-button plain @click="drawerCancel">取消</el-button>
        </div>
      </template>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import {
  machineRepairConfirmApi,
  machineSplitAuditApi,
  machineSplitDetailApi,
  machineSplitListApi,
} from "@/api/repair";

export default {
  name: "Disassembly",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "拆机单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 130,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "picUrls",
          title: "零件图片",
          isTable: true,
          tableSlot: "picUrls",
          width: 120,
        },
        // {
        //   dataIndex: "articleCode",
        //   title: "物品编号",
        //   isTable: true,
        //   minWidth: 120,
        // },
        // {
        //   dataIndex: "articleName",
        //   title: "物品名称",
        //   isTable: true,
        //   minWidth: 120,
        // },
        {
          dataIndex: "partName",
          title: "零件名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        // {
        //   dataIndex: "manufacturerChannel",
        //   title: "制造渠道",
        //   isTable: true,
        //   formatter: (row) => row.manufacturerChannel?.label,
        //   minWidth: 80,
        // },
        {
          dataIndex: "type",
          title: "物品小类",
          isTable: true,
          formatter: (row) => row.type?.label,
          minWidth: 80,
        },
        // {
        //   dataIndex: "unit",
        //   title: "单位",
        //   isTable: true,
        //   minWidth: 80,
        // },
        {
          dataIndex: "quantity",
          title: "数量",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "costPrice",
          title: "成本单价",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "amount",
          title: "成本总价",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "salePrice",
          title: "售价",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "engineerName",
          title: "拆件人员",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "待审核",
              value: "WAIT_APPROVE",
            },
            {
              label: "驳回",
              value: "REJECT",
            },
            {
              label: "审核通过",
              value: "PASS",
            },
          ],
          minWidth: 100,
        },
        {
          dataIndex: "createdAt",
          title: "拆机日期",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          tooltip: false,
          fixed: "right",
          width: 140,
        },
      ],
      tableData: [],
      // drawer
      editType: "info",
      confirmLoading: false,
      drawerVisible: false,
      drawerTitle: "",
      formParam: {},
      formColumns: [
        {
          dataIndex: "code",
          title: "维修单号",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isForm: true,
          formSlot: "hostType",
          formSpan: 24,
        },
        {
          dataIndex: "partName",
          title: "零件名称",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isForm: true,
          formSlot: "articleCode",
          formSpan: 24,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "quantity",
          title: "数量",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入数量",
              trigger: "change",
            },
            {
              validator(rule, value, callback) {
                if (value <= 0) {
                  callback("数量不能小于0");
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "costPrice",
          title: "成本",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入成本",
              trigger: "change",
            },
            {
              validator(rule, value) {
                if (value < 0) {
                  return Promise.reject("成本不能小于0");
                }
                return Promise.resolve();
              },
            },
          ],
        },
        {
          dataIndex: "salePrice",
          title: "售价",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 12,
        },
        {
          dataIndex: "location",
          title: "储位",
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "picUrls",
          title: "零件图片",
          isForm: true,
          formSlot: "picUrls",
          formSpan: 24,
        },
      ],
      // 更换零件清单
      replaceColumns: [
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "picUrl",
          title: "商品主图",
          isTable: true,
          tableSlot: "picUrl",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku规格",
          isTable: true,
          width: 110,
          tableSlot: "saleAttrVals",
        },

        {
          dataIndex: "saleUnitPrice",
          title: "单价",
          isTable: true,
          width: 80,
        },
        {
          dataIndex: "num",
          title: "更换数量",
          width: 80,
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "location",
          title: "更换位置",
          width: 80,
          isTable: true,
        },
      ],
      auditType: "PASS",
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      machineSplitListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.editType = type;
      this.auditType = "PASS";
      this.formParam = {};
      this.drawerTitle = `${row.productName} - 拆机件`;
      machineSplitDetailApi(row.id).then((res) => {
        this.formParam = res.data;
        this.drawerVisible = true;
      });
    },
    handleAudit(type) {
      if (!this.formParam.articleCode) {
        this.$message.error(
          "该零件尚未关联物品编号，请先点击“新增物品”按钮，完成物品的新增操作"
        );
        return;
      }
      this.$refs.ProForm.handleSubmit();
      this.auditType = type;
    },
    formSubmit(val) {
      const text = this.auditType === "PASS" ? "通过" : "驳回";
      this.$confirm(`此操作将${text}该条数据的审核, 是否继续?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const args = {
          id: this.formParam.id,
          status: this.auditType,
          quantity: val.quantity,
          costPrice: val.costPrice,
          salePrice: val.salePrice,
          location: val.location,
        };
        machineSplitAuditApi(args).then((res) => {
          this.$message.success("操作成功");
          this.drawerVisible = false;
          this.refresh();
        });
      });
    },
    handleAddArticle() {
      this.drawerVisible = false;
      this.$router.push({
        path: "/thing",
        query: {
          type: "add",
          name: this.formParam.partName,
        },
      });
    },
    drawerCancel() {
      this.drawerVisible = false;
    },
    getMachinePicsImg(row) {
      return row?.picUrls?.[0]?.url;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
