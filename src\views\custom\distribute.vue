<!--
 * @Author: wskg
 * @Date: 2024-08-30 13:48:24
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 客户 - 分布统计
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <!--      <template #btn>-->
      <!--        <el-button type="success" icon="el-icon-refresh" size="mini"-->
      <!--          >重新计算</el-button-->
      <!--        >-->
      <!--      </template>-->
      <template #regionPath>
        <el-cascader
          style="width: 100%"
          :props="{ lazy: true, lazyLoad: getRegion, checkStrictly: true }"
          leaf-only
          clearable
          filterable
          @change="handleReginChange"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { Message } from "element-ui";
import { getCustomerDistributionByPageApi } from "@/api/customer";

export default {
  name: "Distribute",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      columns: [
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },

        {
          dataIndex: "province",
          title: "省",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "customerNum",
          title: "客户总数",
          isTable: true,
        },
        {
          dataIndex: "numSacle",
          title: "比例",
          isTable: true,
          formatter: (row) => (row.numSacle ? `${row.numSacle}%` : "/"),
        },
        {
          dataIndex: "saleAmount",
          title: "总营业额",
          isTable: true,
        },
        {
          dataIndex: "totalScale",
          title: "比例",
          isTable: true,
          formatter: (row) => (row.totalScale ? `${row.totalScale}%` : "/"),
        },
        // {
        //   dataIndex: "machineTurnover",
        //   title: "机器营业额",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "machineTurnoverRatio",
        //   title: "比例",
        //   isTable: true,
        // },
        {
          dataIndex: "orderAmount",
          title: "耗材营业额",
          isTable: true,
        },
        {
          dataIndex: "workAmount",
          title: "维修费等营业额",
          isTable: true,
        },
        {
          dataIndex: "receiptAmount",
          title: "全包等营业额",
          isTable: true,
        },
      ],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      getCustomerDistributionByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
