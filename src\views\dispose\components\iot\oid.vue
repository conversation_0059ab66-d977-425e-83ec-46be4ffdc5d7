<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:54
 * @Description: 
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd(null, 'add')"
        >
          新增配置
        </el-button>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleAdd(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 新建弹窗 -->
    <ProDialog
      :value="addDialog"
      :title="addType"
      :confirm-text="'保存'"
      width="50%"
      @ok="handleAddDialogOk"
      @cancel="handleAddDialogCancel"
    >
      <ProForm
        ref="addForm"
        :form-param="addForm"
        :form-list="addColumns"
        :confirm-loading="addFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="'add'"
        @proSubmit="addFormSubmit"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import { Message, MessageBox } from "element-ui";
import {
  getOidConfigApi,
  addOidConfigApi,
  deleteOidApi,
  putOidConfigApi,
} from "@/api/iot";
import { filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";

export default {
  name: "Oid",
  components: { ProTable },
  data() {
    return {
      tableData: [],
      columns: [
        {
          dataIndex: "sort",
          title: "序列值",
          isTable: true,
          clearable: true,
        },
        {
          dataIndex: "oidName",
          title: "Oid配置名",
          isTable: true,
          isSearch: true,
          valueType: "input",
          clearable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          isSearch: true,
          valueType: "input",
          clearable: true,
        },
        {
          dataIndex: "mode",
          title: "型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          clearable: true,
        },
        {
          dataIndex: "oid",
          title: "Oid",
          isTable: true,
          clearable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "updatedAt",
          title: "上传时间",
          isSearch: true,
          clearable: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
        },
        {
          dataIndex: "action",
          tableSlot: "action",
          width: 180,
          title: "操作",
          isTable: true,
          tooltip: false,
          isSearch: false,
        },
      ],
      addType: "新建",
      queryParam: {},
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      selection: [],
      addDialog: false,
      addForm: {},
      addFormLoading: false,
      addColumns: [
        {
          dataIndex: "oidName",
          title: "Oid配置名",
          width: 100,
          isForm: true,
          formSpan: 12,
          valueType: "input",
          clearable: true,
          prop: [
            {
              required: true,
              message: "请输入Oid配置名",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "brand",
          title: "品牌",
          width: 100,
          isForm: true,
          formSpan: 12,
          valueType: "input",
          clearable: true,
        },
        {
          dataIndex: "mode",
          title: "型号",
          width: 100,
          isForm: true,
          formSpan: 12,
          valueType: "input",
          clearable: true,
        },
        {
          dataIndex: "oid",
          title: "Oid",
          width: 100,
          isForm: true,
          formSpan: 12,
          valueType: "input",
          clearable: true,
        },
        {
          dataIndex: "sort",
          title: "序列值",
          width: 100,
          isForm: true,
          formSpan: 12,
          valueType: "input",
          clearable: true,
        },
      ],
    };
  },

  mounted() {
    this.refresh();
  },
  methods: {
    handleDelete(row) {
      MessageBox.confirm("Oid删除后，数据将不可恢复。确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const result = await deleteOidApi(row.id);
          if (result.code === 200) {
            Message.success("删除成功");
            this.localPagination = {
              pageNumber: 1,
              pageSize: 10,
              total: 0,
            };
            this.$nextTick(() => {
              this.refresh();
            });
          }
        })
        .catch(() => {
          console.log("取消删除");
        });
    },
    handleAdd(row, type) {
      if (type === "add") {
        this.addType = "新建";
      } else {
        this.addType = "编辑";
        this.addForm = cloneDeep(row);
      }
      this.addDialog = true;
    },
    async loadData(params) {
      try {
        this.queryParam = {
          ...this.queryParam,
          ...params,
        };
        const res = [
          {
            createdAtStartTime: null,
            createdAtEndTime: null,
            data: params.updatedAt,
          },
        ];
        filterParamRange(this, this.queryParam, res);
        const requestParameters = cloneDeep(this.queryParam);
        delete requestParameters.updatedAt;
        const result = await getOidConfigApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    handleSelectionChange(val) {
      this.selection = val;
    },
    // 刷新
    refresh() {
      this.$refs.ProTable.refresh();
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },
    // 新建保存
    handleAddDialogOk() {
      this.$refs.addForm.handleSubmit();
    },
    handleAddDialogCancel() {
      this.addDialog = false;
      this.$nextTick(() => {
        this.addForm = {};
      });
    },
    async addFormSubmit(val) {
      if (this.addType == "新建") {
        try {
          this.addFormLoading = true;
          const result = await addOidConfigApi(val);
          if (result.code === 200) {
            Message.success("添加成功");
            this.addDialog = false;
            this.$refs.ProTable.refresh();
          }
        } catch (err) {
          Message.error(err.message);
        } finally {
          this.addFormLoading = false;
        }
      } else {
        try {
          this.addFormLoading = true;
          const result = await putOidConfigApi(val);
          if (result.code === 200) {
            Message.success("编辑成功");
            this.addDialog = false;
            this.$refs.ProTable.refresh();
          }
        } catch (err) {
          Message.error(err.message);
        } finally {
          this.addFormLoading = false;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.custom {
  width: 100%;
}
</style>
