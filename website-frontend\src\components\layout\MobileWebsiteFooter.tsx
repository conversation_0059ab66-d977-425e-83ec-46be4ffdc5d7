import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Divider, Modal } from 'antd-mobile';
// 暂时注释图标导入，使用emoji代替
// import { 
//   PhoneOutline,
//   MailOutline,
//   EnvironmentOutline,
//   ClockCircleOutline
// } from 'antd-mobile-icons';
import { usePublicConfig } from '@/hooks/useWebsiteApi';
import { useWebsiteMenus } from '@/hooks/useWebsiteMenus';

/**
 * 企业官网移动端底部组件
 */
export function MobileWebsiteFooter() {
  const { data: config } = usePublicConfig();
  const { data: menus = [] } = useWebsiteMenus();
  const [mapUrl, setMapUrl] = useState('');
  
  // 图片预览状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  useEffect(() => {
    if (config?.companyAddress) {
      const address = encodeURIComponent(config.companyAddress);
      const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;
      
      // Android设备使用geo:协议，能调起地图选择器
      if (/android/i.test(userAgent)) {
        setMapUrl(`geo:0,0?q=${address}`);
      } 
      // iOS设备使用Apple Maps URL
      else if (/iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream) {
        setMapUrl(`http://maps.apple.com/?q=${address}`);
      } 
      // 其他设备或无法识别的，使用通用的Google Maps网页链接
      else {
        setMapUrl(`https://maps.google.com/?q=${address}`);
      }
    }
  }, [config?.companyAddress]);

  const handleAddressClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (!config?.companyAddress) return;

    const address = config.companyAddress;
    const encodedAddress = encodeURIComponent(address);
    const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;

    // iOS: Show a modal with map choices
    if (/iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream) {
      Modal.show({
        title: '选择导航应用',
        content: (
          <div className="space-y-1 text-center">
            <div className="p-3 text-blue-600 border-b cursor-pointer" onClick={() => window.open(`http://maps.apple.com/?q=${encodedAddress}&dirflg=d`, '_blank')}>Apple 地图</div>
            <div className="p-3 text-blue-600 border-b cursor-pointer" onClick={() => window.open(`iosamap://path?sourceApplication=benyin&dname=${encodedAddress}&dev=0`, '_blank')}>高德地图</div>
            <div className="p-3 text-blue-600 border-b cursor-pointer" onClick={() => window.open(`baidumap://map/place/search?query=${encodedAddress}`, '_blank')}>百度地图</div>
            <div className="p-3 text-blue-600 cursor-pointer" onClick={() => window.open(`comgooglemaps://?q=${encodedAddress}&directionsmode=driving`, '_blank')}>Google 地图</div>
          </div>
        ),
        closeOnAction: true,
        actions: [{ key: 'cancel', text: '取消' }],
      });
      return;
    }

    // Android: Use geo intent to show app chooser
    if (/android/i.test(userAgent)) {
      window.open(`geo:0,0?q=${encodedAddress}`, '_blank');
      return;
    }
    
    // Fallback for other devices (e.g., desktop)
    window.open(`https://maps.google.com/?q=${encodedAddress}`, '_blank');
  };

  // 图片预览处理函数
  const handleImagePreview = (imageUrl: string, title: string) => {
    setPreviewImage(imageUrl);
    setPreviewTitle(title);
    setPreviewVisible(true);
  };



  return (
    <footer className="bg-gray-900 text-gray-300 mt-auto">
      {/* 主要内容区 */}
      <div className="px-4 pt-4 md:pt-8 pb-2 md:pb-4">
        {/* 公司信息 */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center space-x-3 mb-3">
            {config?.footerLogoUrl?.trim() ? (
              <img
                src={config.footerLogoUrl.trim()}
                alt="logo"
                className="w-10 h-10 object-contain cursor-pointer"
                onError={(e) => (e.currentTarget.style.display = 'none')}
              />
            ) : (
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">
                  {(config?.companyName || config?.siteTitle)?.charAt(0).toUpperCase() || 'L'}
                </span>
              </div>
            )}
            <div>
              <h3 className="text-white font-bold text-lg">
                {config?.siteTitle || '复印机维修'}
              </h3>

            </div>
          </div>
          
          <p className="text-sm text-gray-400 leading-relaxed max-w-sm mx-auto">
            {config?.companyProfile || 
              '专业从事复印机、打印机等办公设备维修服务，为企业提供高质量的设备维护解决方案。'
            }
          </p>
        </div>


        {/* 快速链接 */}
        <div className="mb-6">
          <h4 className="text-white font-semibold text-sm mb-3 text-center">快速链接</h4>
          <div className="flex flex-wrap justify-center gap-2">
            {menus.map(menu => (
              <Link 
                key={menu.path}
                to={menu.path}
                className="px-3 py-1 bg-gray-800 text-gray-300 text-sm rounded-full hover:bg-gray-700 transition-colors"
              >
                {menu.label}
              </Link>
            ))}
          </div>
        </div>

        {/* 联系信息 */}
        <div className="mb-0">
          <h4 className="text-white font-semibold text-sm mb-4 text-center">联系我们</h4>
          <div className="space-y-3 max-w-sm mx-auto">
            {config?.servicePhone && (
              <a href={`tel:${config.servicePhone}`} className="flex items-center space-x-3 justify-center text-gray-300 hover:text-white transition-colors">
                <span className="text-base text-blue-400">📞</span>
                <span className="text-sm">{config.servicePhone}</span>
              </a>
            )}

            {config?.companyAddress && (
              <a 
                href="#"
                onClick={handleAddressClick}
                className="flex items-center space-x-3 justify-center text-gray-300 hover:text-white transition-colors"
              >
                <span className="text-base text-blue-400">📍</span>
                <span className="text-sm text-center leading-relaxed max-w-60">
                  {config.companyAddress}
                </span>
              </a>
            )}

            {/* 社交媒体 - QQ和微信并排显示 */}
            {(((config as any)?.wechatNumber?.trim() || (config as any)?.wechatIcon?.trim() || (config as any)?.wechatQrCode?.trim()) ||
              ((config as any)?.qqNumber?.trim() || (config as any)?.qqIcon?.trim() || (config as any)?.qqQrCode?.trim())) && (
              <div className="flex justify-center space-x-4">
                {/* 微信 */}
                {((config as any)?.wechatNumber?.trim() || (config as any)?.wechatIcon?.trim() || (config as any)?.wechatQrCode?.trim()) && (
                  <div className="flex flex-col items-center space-y-1">
                    {/* 图标和联系方式 */}
                    {((config as any)?.wechatIcon?.trim() || (config as any)?.wechatNumber?.trim()) && (
                      <div className="flex items-center space-x-3 text-gray-300 w-fit">
                        {/* 只在有自定义图标时显示图标 */}
                        {(config as any)?.wechatIcon?.trim() && (
                          <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                            {(config as any).wechatIcon.startsWith('http://') || (config as any).wechatIcon.startsWith('https://') ? (
                              <img 
                                src={(config as any).wechatIcon.trim()} 
                                alt="微信图标" 
                                className="w-6 h-6 object-contain"
                                onError={(e) => (e.currentTarget.style.display = 'none')}
                              />
                            ) : (
                              <span className="text-lg leading-none">{(config as any).wechatIcon}</span>
                            )}
                          </div>
                        )}
                        {(config as any)?.wechatNumber?.trim() && (
                          <span className="text-sm">{(config as any).wechatNumber.trim()}</span>
                        )}
                      </div>
                    )}
                    
                    {/* 二维码直接显示 */}
                    {(config as any)?.wechatQrCode?.trim() && (
                      <div className="flex justify-center">
                        <img 
                          src={(config as any).wechatQrCode.trim()} 
                          alt="二维码" 
                          className="w-32 h-32 object-contain rounded border border-gray-600 cursor-pointer hover:opacity-80 transition-opacity"
                          onError={(e) => (e.currentTarget.style.display = 'none')}
                          onClick={() => handleImagePreview((config as any).wechatQrCode.trim(), '二维码')}
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* QQ */}
                {((config as any)?.qqNumber?.trim() || (config as any)?.qqIcon?.trim() || (config as any)?.qqQrCode?.trim()) && (
                  <div className="flex flex-col items-center space-y-1">
                    {/* 图标和联系方式 */}
                    {((config as any)?.qqIcon?.trim() || (config as any)?.qqNumber?.trim()) && (
                      <div className="flex items-center space-x-3 text-gray-300 w-fit">
                        {/* 只在有自定义图标时显示图标 */}
                        {(config as any)?.qqIcon?.trim() && (
                          <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                            {(config as any).qqIcon.startsWith('http://') || (config as any).qqIcon.startsWith('https://') ? (
                              <img 
                                src={(config as any).qqIcon.trim()} 
                                alt="QQ图标" 
                                className="w-6 h-6 object-contain"
                                onError={(e) => (e.currentTarget.style.display = 'none')}
                              />
                            ) : (
                              <span className="text-lg leading-none">{(config as any).qqIcon}</span>
                            )}
                          </div>
                        )}
                        {(config as any)?.qqNumber?.trim() && (
                          <span className="text-sm">{(config as any).qqNumber.trim()}</span>
                        )}
                      </div>
                    )}
                    
                    {/* 二维码直接显示 */}
                    {(config as any)?.qqQrCode?.trim() && (
                      <div className="flex justify-center">
                        <img 
                          src={(config as any).qqQrCode.trim()} 
                          alt="二维码" 
                          className="w-32 h-32 object-contain rounded border border-gray-600 cursor-pointer hover:opacity-80 transition-opacity"
                          onError={(e) => (e.currentTarget.style.display = 'none')}
                          onClick={() => handleImagePreview((config as any).qqQrCode.trim(), '二维码')}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <Divider className="border-gray-700 my-0" />

      {/* 底部版权信息 */}
      <div className="px-4 py-1 md:py-4 text-center">
        {(() => {
          const notice = (config as any)?.copyrightNotice?.trim() || `© ${new Date().getFullYear()} ${config?.companyName || '复印机维修服务公司'}`;
          return (
            <div className="flex flex-wrap justify-center items-center gap-2">
              <div className="text-xs text-gray-400">{notice}</div>
              {(config as any)?.icpNumber?.trim() && (
                <a
                  href={(config as any)?.icpLink || '#'}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-gray-400 hover:text-white"
                >
                  {(config as any).icpNumber.trim()}
                </a>
              )}
              {(config as any)?.policeNumber?.trim() && (
                <a
                  href={(config as any)?.policeLink || '#'}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-gray-400 hover:text-white"
                >
                  {(config as any).policeNumber.trim()}
                </a>
              )}
            </div>
          );
        })()}
      </div>

      {/* 图片预览模态框 */}
      <Modal
        visible={previewVisible}
        title={previewTitle}
        onClose={() => setPreviewVisible(false)}
        content={
          <div className="text-center p-4">
            <img 
              src={previewImage}
              alt={previewTitle}
              className="max-w-full max-h-[70vh] object-contain rounded shadow-lg mx-auto"
            />
            <p className="text-gray-500 mt-4 text-sm">点击外区域关闭预览</p>
          </div>
        }
        closeOnAction
        closeOnMaskClick
      />
    </footer>
  );
}