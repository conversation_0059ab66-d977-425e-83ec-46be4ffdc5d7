<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-03 13:52:42
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:20
 * @Description: 绑定日志管理
 -->
<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    ></ProTable>
  </div>
</template>

<script>
import { getBindLogApi } from "@/api/iot";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";

export default {
  name: "BindLog",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "errType",
          title: "错误类型",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 80,
        },
        {
          dataIndex: "errorCode",
          title: "错误代码",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "configType",
          title: "配置类型",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "ipAddress",
          title: "IP地址",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "port",
          title: "端口号",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "exceptionDescribe",
          title: "异常描述",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "model",
          title: "机型",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "recordModel",
          title: "记录机型",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "deviceName",
          title: "设备名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "machineNo",
          title: "设备编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "errorTime",
          title: "上报时间",
          isTable: true,
          width: 150,
        },
      ],
      tableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          createdAtStartTime: null,
          createdAtEndTime: null,
          data: parameter.errorTime,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.errorTime;
      getBindLogApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
