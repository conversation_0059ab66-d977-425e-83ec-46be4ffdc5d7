<template>
  <div class="view app-container">
    <machineOrPartDistribute :type="'machine'" :columns="machineColumns" />
    <!-- <el-tabs v-model="activeName">
      <el-tab-pane label="区域机型分布" name="first" lazy>
       
      </el-tab-pane>
      <el-tab-pane label="选配件分布" name="second" lazy>
               <machineOrPartDistribute :type="'part'" :columns="partColumns" />
        选配件分布
      </el-tab-pane>
    </el-tabs> -->
  </div>
</template>

<script>
import machineOrPartDistribute from "@/views/machine/components/machineOrPartDistribute.vue";
import { mulAmount } from "@/utils";
export default {
  name: "DistributeStat",
  components: { machineOrPartDistribute },
  data() {
    return {
      activeName: "first",
      machineColumns: [
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "serial",
          title: "系列",
          isTable: true,
          width: 120,
        },
        // {
        //   dataIndex: "version",
        //   title: "版本",
        //   isTable: true,
        // },
        // {
        //   dataIndex: "voltage",
        //   title: "电压",
        //   isTable: true,
        // },
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          isSearch: true,
          valueType: "product",
          // searchSlot: "productId",
        },
        {
          dataIndex: "number",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "numberScale",
          title: "比例",
          isTable: true,
          formatter: (row) => mulAmount(row.numberScale, 100).toFixed(2) + "%",
        },
        {
          dataIndex: "printCount",
          title: "印量",
          isTable: true,
        },
        {
          dataIndex: "countScale",
          title: "比例",
          isTable: true,
          formatter: (row) =>
            mulAmount(row.printCountScale, 100).toFixed(2) + "%",
        },
        {
          dataIndex: "orderPay",
          title: "耗材营业额",
          isTable: true,
        },
        {
          dataIndex: "orderScale",
          title: "比例",
          isTable: true,
          formatter: (row) => mulAmount(row.orderScale, 100).toFixed(2) + "%",
        },
        {
          dataIndex: "workPay",
          title: "维修费营业额",
          isTable: true,
        },
        {
          dataIndex: "workScale",
          title: "比例",
          isTable: true,
          formatter: (row) => mulAmount(row.workScale, 100).toFixed(2) + "%",
        },
        {
          dataIndex: "mechinePay",
          title: "机器营业额",
          isTable: true,
        },
        {
          dataIndex: "mechineScale",
          title: "比例",
          isTable: true,
          formatter: (row) => mulAmount(row.mechineScale, 100).toFixed(2) + "%",
        },
      ],
      partColumns: [
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "productInfo",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          isSearch: true,
          valueType: "product",
          // searchSlot: "productId",
        },
        {
          dataIndex: "number",
          title: "数量",
          isTable: true,
        },
        {
          dataIndex: "numberRatio",
          title: "比例",
          isTable: true,
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss"></style>
