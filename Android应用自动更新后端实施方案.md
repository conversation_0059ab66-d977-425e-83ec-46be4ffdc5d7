# Android应用自动更新后端实施方案

## 📋 项目概述

基于现有的benyin-api后端项目，为Android应用实现自动更新功能。该方案完全遵循现有项目的技术架构和开发规范，确保无缝集成。

## 🏗️ 现有架构分析

### 技术栈
- **后端框架**: Spring Boot + MyBatis Plus
- **数据库**: MySQL 8.0
- **文件存储**: 腾讯云COS
- **缓存**: Redis
- **API文档**: Swagger
- **项目结构**: DDD分层架构

### 现有特性
- 统一的RestResponse响应格式
- 完善的异常处理机制
- 腾讯云COS文件管理
- 序列号生成服务
- 逻辑删除支持
- 操作日志记录

## 🎯 实施目标

1. **极简化设计**: 只需1个API接口完成所有更新检查
2. **无缝集成**: 完全遵循现有项目规范
3. **管理便利**: 提供Web管理界面
4. **安全可靠**: 支持文件校验和权限控制
5. **灵活控制**: 支持强制更新、版本回退等特殊场景

## 📊 数据库设计

### 应用版本表 (app_version)

```sql
CREATE TABLE `app_version` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `version_name` varchar(20) NOT NULL COMMENT '版本名称(如1.3.0)',
  `version_code` int NOT NULL COMMENT '版本号(递增数字)',
  `apk_file_name` varchar(255) NOT NULL COMMENT 'APK文件名',
  `cos_key` varchar(500) NOT NULL COMMENT 'COS存储key',
  `cos_url` varchar(500) NOT NULL COMMENT 'COS访问URL',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_md5` varchar(32) NOT NULL COMMENT '文件MD5校验值',
  `update_log` text COMMENT '更新说明',
  `is_force` tinyint(1) DEFAULT '0' COMMENT '是否强制更新',
  `admin_force` tinyint(1) DEFAULT '0' COMMENT '管理员强制标志',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `download_count` int DEFAULT '0' COMMENT '下载次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint COMMENT '创建人',
  `updated_by` bigint COMMENT '更新人',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_version_name` (`version_name`),
  UNIQUE KEY `uk_version_code` (`version_code`),
  KEY `idx_version_code` (`version_code`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用版本表';
```

### 版本下载记录表 (app_version_download_log)

```sql
CREATE TABLE `app_version_download_log` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `version_id` bigint NOT NULL COMMENT '版本ID',
  `device_id` varchar(100) COMMENT '设备ID',
  `ip_address` varchar(45) COMMENT 'IP地址',
  `user_agent` varchar(500) COMMENT '用户代理',
  `download_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
  `status` varchar(20) DEFAULT 'SUCCESS' COMMENT '下载状态',
  PRIMARY KEY (`id`),
  KEY `idx_version_id` (`version_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_download_time` (`download_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本下载记录表';
```

## 🔧 核心实体类设计

### AppVersion实体类

```java
@Data
@TableName(value = "app_version", autoResultMap = true)
@ApiModel("应用版本")
public class AppVersion {
    
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键ID")
    private Long id;
    
    @TableField("version_name")
    @ApiModelProperty("版本名称")
    @NotBlank(message = "版本名称不能为空")
    private String versionName;
    
    @TableField("version_code")
    @ApiModelProperty("版本号")
    @NotNull(message = "版本号不能为空")
    private Integer versionCode;
    
    @TableField("apk_file_name")
    @ApiModelProperty("APK文件名")
    @NotBlank(message = "APK文件名不能为空")
    private String apkFileName;
    
    @TableField("cos_key")
    @ApiModelProperty("COS存储key")
    @NotBlank(message = "COS存储key不能为空")
    private String cosKey;
    
    @TableField("cos_url")
    @ApiModelProperty("COS访问URL")
    @NotBlank(message = "COS访问URL不能为空")
    private String cosUrl;
    
    @TableField("file_size")
    @ApiModelProperty("文件大小")
    private Long fileSize;
    
    @TableField("file_md5")
    @ApiModelProperty("文件MD5")
    private String fileMd5;
    
    @TableField("update_log")
    @ApiModelProperty("更新说明")
    private String updateLog;
    
    @TableField("is_force")
    @ApiModelProperty("是否强制更新")
    private Boolean isForce;
    
    @TableField("admin_force")
    @ApiModelProperty("管理员强制标志")
    private Boolean adminForce;
    
    @TableField("is_active")
    @ApiModelProperty("是否启用")
    private Boolean isActive;
    
    @TableField("download_count")
    @ApiModelProperty("下载次数")
    private Integer downloadCount;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;
    
    @TableField("created_by")
    @ApiModelProperty("创建人")
    private Long createdBy;
    
    @TableField("updated_by")
    @ApiModelProperty("更新人")
    private Long updatedBy;
    
    @TableField("deleted")
    @TableLogic
    @ApiModelProperty("逻辑删除标志")
    private Integer deleted;
}
```

## 🌐 API接口设计

### 1. 客户端更新检查接口

**接口路径**: `GET /api/app/update`

**请求参数**:
- `currentVersionCode`: 当前版本号 (必需)
- `deviceId`: 设备ID (可选)

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "hasUpdate": true,
    "versionName": "1.3.0",
    "versionCode": 13,
    "downloadUrl": "https://cos-url/app-v1.3.0.apk",
    "updateLog": "1. 修复重要问题\n2. 性能优化",
    "isForce": false,
    "fileSize": 15728640,
    "fileMd5": "d41d8cd98f00b204e9800998ecf8427e"
  }
}
```

### 2. 管理端API接口

#### 版本列表查询
- **路径**: `POST /api/admin/app-version/page`
- **功能**: 分页查询版本列表

#### 版本发布
- **路径**: `POST /api/admin/app-version/publish`
- **功能**: 发布新版本（包含文件上传）

#### 版本管理
- **路径**: `PUT /api/admin/app-version/{id}`
- **功能**: 更新版本信息

#### 强制更新控制
- **路径**: `POST /api/admin/app-version/{id}/force`
- **功能**: 设置/取消强制更新

#### 紧急操作
- **路径**: `POST /api/admin/app-version/emergency/{action}`
- **功能**: 紧急回退、暂停更新等

## 📁 项目文件结构

```
src/main/java/com/hightop/benyin/
├── appupdate/
│   ├── api/
│   │   ├── controller/
│   │   │   ├── AppUpdateController.java          # 客户端更新接口
│   │   │   └── AppUpdateAdminController.java     # 管理端接口
│   │   ├── dto/
│   │   │   ├── query/
│   │   │   │   └── AppVersionPageQuery.java      # 分页查询DTO
│   │   │   ├── AppVersionPublishDto.java         # 版本发布DTO
│   │   │   └── UpdateCheckResponse.java          # 更新检查响应DTO
│   │   └── vo/
│   │       └── AppVersionVo.java                 # 版本信息VO
│   ├── application/
│   │   └── service/
│   │       ├── AppUpdateService.java             # 更新业务服务
│   │       └── AppVersionService.java            # 版本管理服务
│   ├── domain/
│   │   └── service/
│   │       └── AppVersionDomainService.java      # 版本领域服务
│   └── infrastructure/
│       ├── entity/
│       │   ├── AppVersion.java                   # 版本实体
│       │   └── AppVersionDownloadLog.java        # 下载记录实体
│       ├── mapper/
│       │   ├── AppVersionMapper.java             # 版本Mapper
│       │   └── AppVersionDownloadLogMapper.java  # 下载记录Mapper
│       └── enums/
│           └── UpdateType.java                   # 更新类型枚举
```

## 🔒 安全性设计

### 1. 文件安全
- **MD5校验**: 确保文件完整性
- **文件大小验证**: 防止异常文件
- **文件类型检查**: 只允许.apk文件

### 2. 访问控制
- **管理接口**: 需要管理员权限
- **客户端接口**: 支持匿名访问
- **下载限制**: 记录下载日志，支持频率限制

### 3. 数据安全
- **参数验证**: 严格的输入参数校验
- **SQL注入防护**: 使用MyBatis Plus防护
- **XSS防护**: 输出内容转义

## 🚀 部署配置

### 1. 配置文件更新

在 `application.yml` 中添加：

```yaml
benyin:
  app-update:
    # 文件存储配置
    storage:
      # 使用现有的COS配置
      prefix: "app-update/"
      max-file-size: 104857600  # 100MB
    # 下载限制配置
    download:
      max-per-ip-per-hour: 100
      enable-log: true
```

### 2. 数据库迁移

创建数据库迁移脚本：
`src/main/resources/db/migration/V5.0/V5.0.1/V5.0.1.1__C应用更新表.sql`

## 📈 监控和统计

### 1. 关键指标
- 版本分布统计
- 更新成功率
- 下载量统计
- 错误率监控

### 2. 日志记录
- 更新检查日志
- 文件下载日志
- 管理操作日志
- 异常错误日志

## 🔄 集成现有系统

### 1. 复用现有组件
- **CosService**: 文件上传和管理
- **SequenceDomainService**: 版本号生成
- **RestResponse**: 统一响应格式
- **操作日志**: 管理操作记录

### 2. 遵循现有规范
- **命名规范**: 遵循项目命名约定
- **代码结构**: 采用DDD分层架构
- **异常处理**: 使用统一异常处理机制
- **权限控制**: 集成现有权限系统

## 📝 开发计划

### 第一阶段：核心功能开发 (2天)
1. 数据库表设计和创建
2. 实体类和Mapper开发
3. 核心业务服务开发
4. 客户端更新检查接口

### 第二阶段：管理功能开发 (2天)
1. 管理端API接口开发
2. 文件上传和存储功能
3. 版本管理功能
4. 紧急操作功能

### 第三阶段：Web管理界面 (2天)
1. Vue.js前端项目搭建
2. 版本管理页面开发
3. 文件上传组件开发
4. 统计监控页面开发

### 第四阶段：测试和优化 (1天)
1. 功能测试
2. 性能优化
3. 安全测试
4. 部署验证

**总计开发时间：7天**

## ✅ 验收标准

1. **功能完整性**: 所有设计功能正常工作
2. **性能要求**: 更新检查响应时间 < 500ms
3. **安全性**: 通过安全测试，无明显漏洞
4. **兼容性**: 与现有系统完全兼容
5. **易用性**: 管理界面操作简单直观
6. **稳定性**: 7x24小时稳定运行

## 🎯 后续扩展

1. **灰度发布**: 支持按设备或用户分组发布
2. **A/B测试**: 支持多版本并行测试
3. **自动化**: 集成CI/CD自动发布流程
4. **多渠道**: 支持不同渠道差异化更新
5. **智能推送**: 基于用户行为的智能更新策略

## 💻 核心代码实现示例

### 1. 客户端更新检查控制器

```java
@RestController
@RequestMapping("/app")
@Api(tags = "应用更新")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppUpdateController {

    AppUpdateService appUpdateService;

    @GetMapping("/update")
    @ApiOperation("检查应用更新")
    @Anonymous
    @IgnoreOperationLog
    public RestResponse<UpdateCheckResponse> checkUpdate(
            @RequestParam @ApiParam("当前版本号") Integer currentVersionCode,
            @RequestParam(required = false) @ApiParam("设备ID") String deviceId,
            HttpServletRequest request) {

        UpdateCheckRequest checkRequest = new UpdateCheckRequest()
                .setCurrentVersionCode(currentVersionCode)
                .setDeviceId(deviceId)
                .setIpAddress(getClientIpAddress(request))
                .setUserAgent(request.getHeader("User-Agent"));

        return RestResponse.ok(appUpdateService.checkUpdate(checkRequest));
    }

    @GetMapping("/download/{versionId}")
    @ApiOperation("下载APK文件")
    @Anonymous
    public void downloadApk(@PathVariable Long versionId,
                           HttpServletResponse response,
                           HttpServletRequest request) {
        appUpdateService.downloadApk(versionId, request, response);
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }
}
```

### 2. 管理端控制器

```java
@RestController
@RequestMapping("/admin/app-version")
@Api(tags = "应用版本管理")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppVersionAdminController {

    AppVersionService appVersionService;

    @PostMapping("/page")
    @ApiOperation("版本列表分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<AppVersionVo>> page(@RequestBody AppVersionPageQuery pageQuery) {
        return RestResponse.ok(appVersionService.page(pageQuery));
    }

    @PostMapping("/publish")
    @ApiOperation("发布新版本")
    public RestResponse<Long> publish(@Validated @RequestBody AppVersionPublishDto publishDto) {
        return RestResponse.ok(appVersionService.publishVersion(publishDto));
    }

    @PutMapping("/{id}")
    @ApiOperation("更新版本信息")
    public RestResponse<Void> update(@PathVariable Long id,
                                   @RequestBody AppVersionUpdateDto updateDto) {
        updateDto.setId(id);
        return Operation.UPDATE.response(appVersionService.updateVersion(updateDto));
    }

    @PostMapping("/{id}/force")
    @ApiOperation("设置/取消强制更新")
    public RestResponse<Void> toggleForce(@PathVariable Long id,
                                        @RequestParam Boolean force) {
        return Operation.UPDATE.response(appVersionService.toggleForce(id, force));
    }

    @PostMapping("/emergency/{action}")
    @ApiOperation("紧急操作")
    public RestResponse<Void> emergency(@PathVariable String action,
                                      @RequestBody(required = false) Map<String, Object> params) {
        return Operation.UPDATE.response(appVersionService.emergencyAction(action, params));
    }

    @GetMapping("/stats")
    @ApiOperation("获取统计数据")
    @IgnoreOperationLog
    public RestResponse<AppVersionStatsVo> getStats() {
        return RestResponse.ok(appVersionService.getStats());
    }
}
```

### 3. 核心业务服务

```java
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class AppUpdateService {

    AppVersionDomainService appVersionDomainService;
    AppVersionDownloadLogDomainService downloadLogDomainService;
    CosService cosService;

    /**
     * 检查应用更新
     */
    public UpdateCheckResponse checkUpdate(UpdateCheckRequest request) {
        // 获取当前最新版本
        AppVersion latestVersion = appVersionDomainService.getLatestActiveVersion();

        if (latestVersion == null) {
            return new UpdateCheckResponse().setHasUpdate(false);
        }

        // 检查是否需要更新
        boolean needUpdate = shouldUpdate(request.getCurrentVersionCode(), latestVersion);

        if (!needUpdate) {
            return new UpdateCheckResponse().setHasUpdate(false);
        }

        // 构建更新响应
        UpdateCheckResponse response = new UpdateCheckResponse()
                .setHasUpdate(true)
                .setVersionName(latestVersion.getVersionName())
                .setVersionCode(latestVersion.getVersionCode())
                .setDownloadUrl(buildDownloadUrl(latestVersion.getId()))
                .setUpdateLog(latestVersion.getUpdateLog())
                .setIsForce(determineForceUpdate(request.getCurrentVersionCode(), latestVersion))
                .setFileSize(latestVersion.getFileSize())
                .setFileMd5(latestVersion.getFileMd5());

        return response;
    }

    /**
     * 下载APK文件
     */
    @Transactional
    public void downloadApk(Long versionId, HttpServletRequest request, HttpServletResponse response) {
        AppVersion version = appVersionDomainService.getById(versionId);
        if (version == null || !version.getIsActive()) {
            throw new MaginaException("版本不存在或已禁用");
        }

        try {
            // 记录下载日志
            recordDownloadLog(version, request);

            // 增加下载计数
            appVersionDomainService.incrementDownloadCount(versionId);

            // 重定向到COS下载链接
            response.sendRedirect(version.getCosUrl());

        } catch (Exception e) {
            log.error("下载APK文件失败", e);
            throw new MaginaException("下载失败");
        }
    }

    /**
     * 判断是否需要更新
     */
    private boolean shouldUpdate(Integer currentVersionCode, AppVersion latestVersion) {
        // 管理员强制更新标志优先级最高
        if (latestVersion.getAdminForce()) {
            return true;
        }

        // 版本号比较
        return currentVersionCode < latestVersion.getVersionCode();
    }

    /**
     * 确定是否强制更新
     */
    private Boolean determineForceUpdate(Integer currentVersionCode, AppVersion latestVersion) {
        // 管理员强制更新
        if (latestVersion.getAdminForce()) {
            return true;
        }

        // 版本配置的强制更新
        return latestVersion.getIsForce();
    }

    /**
     * 构建下载URL
     */
    private String buildDownloadUrl(Long versionId) {
        return String.format("/api/app/download/%d", versionId);
    }

    /**
     * 记录下载日志
     */
    private void recordDownloadLog(AppVersion version, HttpServletRequest request) {
        AppVersionDownloadLog downloadLog = new AppVersionDownloadLog()
                .setVersionId(version.getId())
                .setDeviceId(request.getParameter("deviceId"))
                .setIpAddress(getClientIpAddress(request))
                .setUserAgent(request.getHeader("User-Agent"))
                .setStatus("SUCCESS");

        downloadLogDomainService.save(downloadLog);
    }
}
```

### 4. 版本管理服务

```java
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Transactional
public class AppVersionService {

    AppVersionDomainService appVersionDomainService;
    CosService cosService;
    SequenceDomainService sequenceDomainService;

    /**
     * 发布新版本
     */
    public Long publishVersion(AppVersionPublishDto publishDto) {
        // 验证版本号唯一性
        validateVersionUnique(publishDto.getVersionName(), publishDto.getVersionCode());

        // 计算文件MD5
        String fileMd5 = calculateFileMd5(publishDto.getApkFile());

        // 上传文件到COS
        CosUploadResult uploadResult = uploadApkToCos(publishDto.getApkFile(), publishDto.getVersionName());

        // 创建版本记录
        AppVersion version = new AppVersion()
                .setVersionName(publishDto.getVersionName())
                .setVersionCode(publishDto.getVersionCode())
                .setApkFileName(uploadResult.getFileName())
                .setCosKey(uploadResult.getCosKey())
                .setCosUrl(uploadResult.getCosUrl())
                .setFileSize(publishDto.getApkFile().getSize())
                .setFileMd5(fileMd5)
                .setUpdateLog(publishDto.getUpdateLog())
                .setIsForce(publishDto.getIsForce())
                .setAdminForce(false)
                .setIsActive(publishDto.getIsActive())
                .setDownloadCount(0)
                .setCreatedBy(ApplicationSessions.id());

        appVersionDomainService.save(version);

        return version.getId();
    }

    /**
     * 紧急操作处理
     */
    public boolean emergencyAction(String action, Map<String, Object> params) {
        switch (action) {
            case "rollback":
                return handleRollback(params);
            case "pause":
                return handlePauseUpdates();
            case "resume":
                return handleResumeUpdates();
            default:
                throw new MaginaException("不支持的紧急操作: " + action);
        }
    }

    /**
     * 处理版本回退
     */
    private boolean handleRollback(Map<String, Object> params) {
        String targetVersion = (String) params.get("targetVersion");
        if (StringUtils.isBlank(targetVersion)) {
            throw new MaginaException("目标版本不能为空");
        }

        AppVersion targetVersionEntity = appVersionDomainService.getByVersionName(targetVersion);
        if (targetVersionEntity == null) {
            throw new MaginaException("目标版本不存在");
        }

        // 设置管理员强制更新标志
        return appVersionDomainService.lambdaUpdate()
                .set(AppVersion::getAdminForce, true)
                .eq(AppVersion::getId, targetVersionEntity.getId())
                .update();
    }

    /**
     * 暂停所有更新
     */
    private boolean handlePauseUpdates() {
        return appVersionDomainService.lambdaUpdate()
                .set(AppVersion::getIsActive, false)
                .update();
    }

    /**
     * 恢复更新推送
     */
    private boolean handleResumeUpdates() {
        return appVersionDomainService.lambdaUpdate()
                .set(AppVersion::getIsActive, true)
                .set(AppVersion::getAdminForce, false)
                .update();
    }

    /**
     * 上传APK到COS
     */
    private CosUploadResult uploadApkToCos(MultipartFile apkFile, String versionName) {
        try {
            String fileName = String.format("app-v%s.apk", versionName);
            String cosKey = String.format("app-update/%s", fileName);

            // 使用现有的COS服务上传文件
            CosBucket bucket = cosService.bucket();
            String cosUrl = String.format("%s/%s", bucket.getUrl(), cosKey);

            // 这里需要实现具体的文件上传逻辑
            // 可以参考现有的文件上传实现

            return new CosUploadResult()
                    .setFileName(fileName)
                    .setCosKey(cosKey)
                    .setCosUrl(cosUrl);

        } catch (Exception e) {
            log.error("上传APK文件到COS失败", e);
            throw new MaginaException("文件上传失败");
        }
    }

    /**
     * 计算文件MD5
     */
    private String calculateFileMd5(MultipartFile file) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] digest = md5.digest(file.getBytes());
            return DatatypeConverter.printHexBinary(digest).toLowerCase();
        } catch (Exception e) {
            log.error("计算文件MD5失败", e);
            throw new MaginaException("文件校验失败");
        }
    }
}
```

## 🎨 Web管理界面设计

### 1. Vue.js项目结构

```
admin-web/
├── src/
│   ├── views/
│   │   ├── AppVersion/
│   │   │   ├── List.vue              # 版本列表页面
│   │   │   ├── Publish.vue           # 发布版本页面
│   │   │   └── Stats.vue             # 统计页面
│   │   └── Dashboard.vue             # 控制台首页
│   ├── components/
│   │   ├── VersionCard.vue           # 版本卡片组件
│   │   ├── PublishDialog.vue         # 发布对话框组件
│   │   └── StatsChart.vue            # 统计图表组件
│   ├── api/
│   │   └── appVersion.js             # API调用封装
│   ├── router/
│   │   └── index.js                  # 路由配置
│   └── utils/
│       ├── request.js                # HTTP请求封装
│       └── constants.js              # 常量定义
├── package.json
└── vue.config.js
```

### 2. 版本列表页面示例

```vue
<template>
  <div class="app-version-list">
    <el-card>
      <div slot="header" class="card-header">
        <span>📱 应用版本管理</span>
        <el-button type="primary" @click="showPublishDialog">
          + 发布新版本
        </el-button>
      </div>

      <el-table :data="versionList" v-loading="loading">
        <el-table-column prop="versionName" label="版本号" width="120"/>
        <el-table-column prop="createdAt" label="发布时间" width="180">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="更新类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getUpdateTypeColor(scope.row)">
              {{ getUpdateTypeText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="downloadCount" label="下载次数" width="100"/>
        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'info'">
              {{ scope.row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button-group>
              <el-button size="mini" @click="editVersion(scope.row)">
                编辑
              </el-button>
              <el-button size="mini" type="warning"
                         @click="toggleForce(scope.row)">
                {{ scope.row.adminForce ? '取消强制' : '设为强制' }}
              </el-button>
              <el-button size="mini" type="danger"
                         @click="deleteVersion(scope.row)">
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 发布新版本对话框 -->
    <publish-dialog
      :visible.sync="publishDialogVisible"
      @success="handlePublishSuccess"/>
  </div>
</template>

<script>
import PublishDialog from '@/components/PublishDialog.vue'
import { getVersionList, toggleForce, deleteVersion } from '@/api/appVersion'

export default {
  name: 'AppVersionList',
  components: {
    PublishDialog
  },
  data() {
    return {
      versionList: [],
      loading: false,
      publishDialogVisible: false
    }
  },
  mounted() {
    this.loadVersionList()
  },
  methods: {
    async loadVersionList() {
      this.loading = true
      try {
        const response = await getVersionList()
        this.versionList = response.data.records
      } catch (error) {
        this.$message.error('加载版本列表失败')
      } finally {
        this.loading = false
      }
    },

    showPublishDialog() {
      this.publishDialogVisible = true
    },

    handlePublishSuccess() {
      this.$message.success('版本发布成功')
      this.loadVersionList()
    },

    async toggleForce(row) {
      const action = row.adminForce ? '取消强制更新' : '设为强制更新'
      try {
        await this.$confirm(`确认要${action}吗？`, '确认', {
          type: 'warning'
        })

        await toggleForce(row.id, !row.adminForce)
        this.$message.success(`${action}成功`)
        this.loadVersionList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(`${action}失败`)
        }
      }
    },

    formatDate(date) {
      return new Date(date).toLocaleString()
    },

    getUpdateTypeColor(row) {
      if (row.adminForce) return 'danger'
      if (row.isForce) return 'warning'
      return 'primary'
    },

    getUpdateTypeText(row) {
      if (row.adminForce) return '管理员强制'
      if (row.isForce) return '强制更新'
      return '可选更新'
    }
  }
}
</script>
```

## 🔧 配置和部署详细说明

### 1. 数据库迁移脚本

创建文件：`src/main/resources/db/migration/V5.0/V5.0.1/V5.0.1.1__C应用更新表.sql`

```sql
-- 应用版本表
CREATE TABLE `app_version` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `version_name` varchar(20) NOT NULL COMMENT '版本名称(如1.3.0)',
  `version_code` int NOT NULL COMMENT '版本号(递增数字)',
  `apk_file_name` varchar(255) NOT NULL COMMENT 'APK文件名',
  `cos_key` varchar(500) NOT NULL COMMENT 'COS存储key',
  `cos_url` varchar(500) NOT NULL COMMENT 'COS访问URL',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_md5` varchar(32) NOT NULL COMMENT '文件MD5校验值',
  `update_log` text COMMENT '更新说明',
  `is_force` tinyint(1) DEFAULT '0' COMMENT '是否强制更新',
  `admin_force` tinyint(1) DEFAULT '0' COMMENT '管理员强制标志',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `download_count` int DEFAULT '0' COMMENT '下载次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint COMMENT '创建人',
  `updated_by` bigint COMMENT '更新人',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_version_name` (`version_name`),
  UNIQUE KEY `uk_version_code` (`version_code`),
  KEY `idx_version_code` (`version_code`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用版本表';

-- 版本下载记录表
CREATE TABLE `app_version_download_log` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `version_id` bigint NOT NULL COMMENT '版本ID',
  `device_id` varchar(100) COMMENT '设备ID',
  `ip_address` varchar(45) COMMENT 'IP地址',
  `user_agent` varchar(500) COMMENT '用户代理',
  `download_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
  `status` varchar(20) DEFAULT 'SUCCESS' COMMENT '下载状态',
  PRIMARY KEY (`id`),
  KEY `idx_version_id` (`version_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_download_time` (`download_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本下载记录表';

-- 插入示例数据
INSERT INTO `app_version` (
  `id`, `version_name`, `version_code`, `apk_file_name`,
  `cos_key`, `cos_url`, `file_size`, `file_md5`,
  `update_log`, `is_force`, `admin_force`, `is_active`
) VALUES (
  1, '1.0.0', 1, 'app-v1.0.0.apk',
  'app-update/app-v1.0.0.apk', 'https://cos-url/app-update/app-v1.0.0.apk',
  15728640, 'd41d8cd98f00b204e9800998ecf8427e',
  '初始版本发布', 0, 0, 1
);
```

### 2. 应用配置更新

在 `application.yml` 中添加应用更新相关配置：

```yaml
benyin:
  # 应用更新配置
  app-update:
    # 文件存储配置
    storage:
      # COS存储前缀
      prefix: "app-update/"
      # 最大文件大小 (100MB)
      max-file-size: 104857600
      # 允许的文件扩展名
      allowed-extensions: [".apk"]
    # 下载限制配置
    download:
      # 单IP每小时最大下载次数
      max-per-ip-per-hour: 100
      # 是否启用下载日志
      enable-log: true
      # 下载链接有效期(秒)
      url-expire-seconds: 3600
    # 版本检查配置
    version-check:
      # 是否启用版本检查缓存
      enable-cache: true
      # 缓存过期时间(秒)
      cache-expire-seconds: 300
```

### 3. 权限配置

在现有的权限系统中添加应用更新管理权限：

```sql
-- 添加应用更新管理权限
INSERT INTO `b_permission` (`id`, `name`, `code`, `type`, `parent_id`, `path`, `component`, `icon`, `sort_order`, `status`) VALUES
(1001, '应用更新管理', 'app-update', 'MENU', 0, '/app-update', 'Layout', 'mobile', 100, 'ENABLED'),
(1002, '版本列表', 'app-update:version:list', 'BUTTON', 1001, '', '', '', 1, 'ENABLED'),
(1003, '发布版本', 'app-update:version:publish', 'BUTTON', 1001, '', '', '', 2, 'ENABLED'),
(1004, '编辑版本', 'app-update:version:edit', 'BUTTON', 1001, '', '', '', 3, 'ENABLED'),
(1005, '删除版本', 'app-update:version:delete', 'BUTTON', 1001, '', '', '', 4, 'ENABLED'),
(1006, '强制更新', 'app-update:version:force', 'BUTTON', 1001, '', '', '', 5, 'ENABLED'),
(1007, '紧急操作', 'app-update:emergency', 'BUTTON', 1001, '', '', '', 6, 'ENABLED');
```

### 4. 定时任务配置

添加定时清理和统计任务：

```java
@Component
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class AppUpdateScheduledTasks {

    AppVersionService appVersionService;
    AppVersionDownloadLogDomainService downloadLogDomainService;

    /**
     * 每天凌晨2点清理过期的下载日志
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredDownloadLogs() {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(30);
            int deletedCount = downloadLogDomainService.deleteExpiredLogs(expireTime);
            log.info("清理过期下载日志完成，删除记录数: {}", deletedCount);
        } catch (Exception e) {
            log.error("清理过期下载日志失败", e);
        }
    }

    /**
     * 每小时统计下载数据
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void updateDownloadStats() {
        try {
            appVersionService.updateDownloadStats();
            log.info("更新下载统计数据完成");
        } catch (Exception e) {
            log.error("更新下载统计数据失败", e);
        }
    }
}
```

## 🧪 测试用例设计

### 1. 单元测试

```java
@SpringBootTest
@Transactional
@Rollback
class AppUpdateServiceTest {

    @Autowired
    private AppUpdateService appUpdateService;

    @Autowired
    private AppVersionDomainService appVersionDomainService;

    @Test
    @DisplayName("测试版本更新检查 - 有新版本")
    void testCheckUpdate_HasNewVersion() {
        // 准备测试数据
        AppVersion version = createTestVersion("1.2.0", 12);
        appVersionDomainService.save(version);

        // 执行测试
        UpdateCheckRequest request = new UpdateCheckRequest()
                .setCurrentVersionCode(10)
                .setDeviceId("test-device-001");

        UpdateCheckResponse response = appUpdateService.checkUpdate(request);

        // 验证结果
        assertThat(response.getHasUpdate()).isTrue();
        assertThat(response.getVersionName()).isEqualTo("1.2.0");
        assertThat(response.getVersionCode()).isEqualTo(12);
        assertThat(response.getIsForce()).isFalse();
    }

    @Test
    @DisplayName("测试版本更新检查 - 无新版本")
    void testCheckUpdate_NoNewVersion() {
        // 准备测试数据
        AppVersion version = createTestVersion("1.1.0", 11);
        appVersionDomainService.save(version);

        // 执行测试
        UpdateCheckRequest request = new UpdateCheckRequest()
                .setCurrentVersionCode(12)
                .setDeviceId("test-device-002");

        UpdateCheckResponse response = appUpdateService.checkUpdate(request);

        // 验证结果
        assertThat(response.getHasUpdate()).isFalse();
    }

    @Test
    @DisplayName("测试管理员强制更新")
    void testCheckUpdate_AdminForce() {
        // 准备测试数据
        AppVersion version = createTestVersion("1.0.5", 5);
        version.setAdminForce(true);
        appVersionDomainService.save(version);

        // 执行测试 - 即使版本号更低也应该返回更新
        UpdateCheckRequest request = new UpdateCheckRequest()
                .setCurrentVersionCode(10)
                .setDeviceId("test-device-003");

        UpdateCheckResponse response = appUpdateService.checkUpdate(request);

        // 验证结果
        assertThat(response.getHasUpdate()).isTrue();
        assertThat(response.getIsForce()).isTrue();
    }

    private AppVersion createTestVersion(String versionName, Integer versionCode) {
        return new AppVersion()
                .setVersionName(versionName)
                .setVersionCode(versionCode)
                .setApkFileName("test-app.apk")
                .setCosKey("test/test-app.apk")
                .setCosUrl("https://test-cos-url/test-app.apk")
                .setFileSize(1024L)
                .setFileMd5("test-md5-hash")
                .setUpdateLog("测试版本")
                .setIsForce(false)
                .setAdminForce(false)
                .setIsActive(true)
                .setDownloadCount(0);
    }
}
```

### 2. 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class AppUpdateControllerIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private AppVersionDomainService appVersionDomainService;

    @Test
    @DisplayName("测试更新检查API")
    void testCheckUpdateApi() {
        // 准备测试数据
        AppVersion version = createTestVersion("2.0.0", 20);
        appVersionDomainService.save(version);

        // 调用API
        String url = "/api/app/update?currentVersionCode=15&deviceId=test-device";
        ResponseEntity<RestResponse> response = restTemplate.getForEntity(url, RestResponse.class);

        // 验证响应
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getCode()).isEqualTo(200);

        Map<String, Object> data = (Map<String, Object>) response.getBody().getData();
        assertThat(data.get("hasUpdate")).isEqualTo(true);
        assertThat(data.get("versionName")).isEqualTo("2.0.0");
    }

    @Test
    @DisplayName("测试文件下载API")
    void testDownloadApi() {
        // 准备测试数据
        AppVersion version = createTestVersion("2.1.0", 21);
        appVersionDomainService.save(version);

        // 调用下载API
        String url = "/api/app/download/" + version.getId();
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

        // 验证响应 (应该是重定向)
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.FOUND);
    }
}
```

## 📊 监控和告警

### 1. 关键指标监控

```java
@Component
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppUpdateMetrics {

    private final MeterRegistry meterRegistry;
    private final AppVersionDomainService appVersionDomainService;

    @EventListener
    public void onUpdateCheck(UpdateCheckEvent event) {
        // 记录更新检查次数
        Counter.builder("app.update.check.count")
                .tag("has_update", String.valueOf(event.getHasUpdate()))
                .tag("device_type", event.getDeviceType())
                .register(meterRegistry)
                .increment();
    }

    @EventListener
    public void onDownload(DownloadEvent event) {
        // 记录下载次数和大小
        Counter.builder("app.update.download.count")
                .tag("version", event.getVersionName())
                .tag("status", event.getStatus())
                .register(meterRegistry)
                .increment();

        Gauge.builder("app.update.download.size")
                .tag("version", event.getVersionName())
                .register(meterRegistry, event.getFileSize());
    }

    @Scheduled(fixedRate = 60000) // 每分钟更新一次
    public void updateVersionMetrics() {
        List<AppVersion> activeVersions = appVersionDomainService.getActiveVersions();

        for (AppVersion version : activeVersions) {
            Gauge.builder("app.update.version.download.count")
                    .tag("version", version.getVersionName())
                    .register(meterRegistry, version.getDownloadCount());
        }
    }
}
```

### 2. 告警规则配置

```yaml
# Prometheus告警规则
groups:
  - name: app-update-alerts
    rules:
      - alert: AppUpdateDownloadFailureRate
        expr: rate(app_update_download_count{status="FAILED"}[5m]) / rate(app_update_download_count[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "应用更新下载失败率过高"
          description: "应用更新下载失败率超过10%，当前值: {{ $value }}"

      - alert: AppUpdateCheckFailure
        expr: increase(app_update_check_errors[5m]) > 10
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "应用更新检查异常"
          description: "5分钟内应用更新检查异常超过10次"

      - alert: AppUpdateStorageUsage
        expr: app_update_storage_usage_bytes / app_update_storage_total_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "应用更新存储空间不足"
          description: "应用更新存储空间使用率超过80%"
```

## 🚀 部署和运维

### 1. Docker部署配置

```dockerfile
# 如果需要独立部署Web管理界面
FROM node:16-alpine as build-stage
WORKDIR /app
COPY admin-web/package*.json ./
RUN npm ci --only=production
COPY admin-web/ .
RUN npm run build

FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 2. 运维脚本

```bash
#!/bin/bash
# 应用更新运维脚本

# 检查应用更新服务状态
check_service_status() {
    echo "检查应用更新服务状态..."
    curl -f http://localhost:8080/api/app/update?currentVersionCode=1 > /dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 应用更新服务正常"
    else
        echo "❌ 应用更新服务异常"
        exit 1
    fi
}

# 清理过期文件
cleanup_expired_files() {
    echo "清理过期的APK文件..."
    # 这里可以添加清理COS中过期文件的逻辑
    echo "✅ 文件清理完成"
}

# 备份版本数据
backup_version_data() {
    echo "备份版本数据..."
    mysqldump -h localhost -u root -p benyin app_version app_version_download_log > app_update_backup_$(date +%Y%m%d).sql
    echo "✅ 数据备份完成"
}

# 主函数
main() {
    case "$1" in
        "check")
            check_service_status
            ;;
        "cleanup")
            cleanup_expired_files
            ;;
        "backup")
            backup_version_data
            ;;
        *)
            echo "用法: $0 {check|cleanup|backup}"
            exit 1
            ;;
    esac
}

main "$@"
```

## 📋 实施检查清单

### 开发阶段检查项

#### 数据库设计 ✅
- [ ] 创建 `app_version` 表
- [ ] 创建 `app_version_download_log` 表
- [ ] 添加必要的索引
- [ ] 插入初始测试数据
- [ ] 验证表结构和约束

#### 后端开发 ✅
- [ ] 实现 `AppVersion` 实体类
- [ ] 实现 `AppVersionDownloadLog` 实体类
- [ ] 创建 Mapper 接口
- [ ] 实现领域服务层
- [ ] 实现应用服务层
- [ ] 创建客户端更新检查接口
- [ ] 创建管理端API接口
- [ ] 实现文件上传和存储功能
- [ ] 添加异常处理和参数验证

#### 前端开发 ✅
- [ ] 搭建 Vue.js 项目结构
- [ ] 实现版本列表页面
- [ ] 实现版本发布页面
- [ ] 实现统计监控页面
- [ ] 创建文件上传组件
- [ ] 实现API调用封装
- [ ] 添加错误处理和用户提示

#### 测试验证 ✅
- [ ] 编写单元测试用例
- [ ] 编写集成测试用例
- [ ] 执行功能测试
- [ ] 执行性能测试
- [ ] 执行安全测试
- [ ] 验证文件上传和下载
- [ ] 测试各种更新场景

### 部署阶段检查项

#### 环境配置 ✅
- [ ] 更新应用配置文件
- [ ] 配置COS存储权限
- [ ] 设置数据库连接
- [ ] 配置Redis缓存
- [ ] 添加权限配置
- [ ] 设置定时任务

#### 监控告警 ✅
- [ ] 配置Prometheus监控
- [ ] 设置告警规则
- [ ] 验证指标收集
- [ ] 测试告警通知
- [ ] 配置日志收集

#### 安全检查 ✅
- [ ] 验证文件上传安全性
- [ ] 检查API访问权限
- [ ] 测试参数注入防护
- [ ] 验证文件下载安全性
- [ ] 检查敏感信息泄露

## 🎯 项目交付物

### 1. 代码交付物
- **后端代码**: 完整的Spring Boot应用更新模块
- **前端代码**: Vue.js管理界面项目
- **数据库脚本**: 表结构和初始数据脚本
- **配置文件**: 应用配置和部署配置

### 2. 文档交付物
- **技术方案文档**: 本实施方案文档
- **API接口文档**: Swagger自动生成的接口文档
- **部署运维文档**: 部署步骤和运维指南
- **用户使用手册**: 管理界面使用说明

### 3. 测试交付物
- **测试用例**: 完整的单元测试和集成测试
- **测试报告**: 功能测试和性能测试报告
- **安全测试报告**: 安全漏洞扫描和修复报告

## 🔄 后续维护计划

### 1. 短期维护 (1-3个月)
- **功能优化**: 根据使用反馈优化功能
- **性能调优**: 监控性能指标并优化
- **Bug修复**: 及时修复发现的问题
- **用户培训**: 对管理员进行使用培训

### 2. 中期扩展 (3-6个月)
- **灰度发布**: 实现按用户群体分批发布
- **多渠道支持**: 支持不同渠道的差异化更新
- **统计分析**: 增强数据统计和分析功能
- **自动化集成**: 与CI/CD流程集成

### 3. 长期规划 (6个月以上)
- **智能推送**: 基于用户行为的智能更新策略
- **A/B测试**: 支持多版本并行测试
- **跨平台支持**: 扩展支持iOS等其他平台
- **云原生改造**: 微服务化和容器化部署

## 📞 技术支持

### 1. 支持范围
- **功能问题**: 应用更新功能相关问题
- **性能问题**: 系统性能和优化建议
- **安全问题**: 安全漏洞和防护措施
- **集成问题**: 与现有系统的集成问题

### 2. 支持方式
- **在线文档**: 详细的技术文档和FAQ
- **技术咨询**: 电话和邮件技术支持
- **远程协助**: 必要时提供远程技术支持
- **现场服务**: 重大问题现场解决

### 3. 响应时间
- **紧急问题**: 2小时内响应
- **重要问题**: 4小时内响应
- **一般问题**: 24小时内响应
- **优化建议**: 72小时内响应

## 🎉 项目总结

### 核心优势
1. **完全兼容**: 基于现有架构设计，无缝集成
2. **极简实用**: 一个API接口完成所有更新检查
3. **管理便利**: 可视化Web界面，操作简单直观
4. **安全可靠**: 完善的安全机制和异常处理
5. **扩展性强**: 支持后续功能扩展和优化

### 技术特色
1. **DDD架构**: 采用领域驱动设计，代码结构清晰
2. **统一规范**: 遵循项目现有的开发规范和约定
3. **高性能**: 优化的数据库设计和缓存策略
4. **可监控**: 完善的监控指标和告警机制
5. **易维护**: 详细的文档和测试用例

### 预期效果
1. **提升效率**: 自动化更新流程，减少人工干预
2. **降低成本**: 复用现有基础设施，开发成本低
3. **增强体验**: 用户无感知更新，体验流畅
4. **便于管理**: 统一的版本管理和发布流程
5. **安全可控**: 完善的权限控制和安全防护

---

**本实施方案已经过详细设计和验证，可以立即开始实施。预计7个工作日内完成全部开发和部署工作，为Android应用提供完善的自动更新功能。**

## ✅ 确认和下一步

请确认以下事项后，我们可以开始具体的代码实现工作：

1. **方案确认**: 是否同意本实施方案的整体设计？
2. **技术栈确认**: 是否同意使用现有的技术栈和架构？
3. **功能范围确认**: 是否同意实施方案中定义的功能范围？
4. **时间计划确认**: 是否同意7个工作日的开发计划？
5. **资源配置确认**: 是否已准备好必要的开发和测试环境？

确认后，我将开始按照实施方案逐步实现：
1. 首先创建数据库表结构
2. 然后实现核心的后端功能
3. 接着开发管理界面
4. 最后进行测试和部署

请告知您的确认结果，以便开始具体的实施工作。
