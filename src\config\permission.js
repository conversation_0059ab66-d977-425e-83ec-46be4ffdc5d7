/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-07 09:22:56
 * @Description:
 */
import router from "../router";
import store from "../store";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
// import { Updater } from "@/utils/version";
// const updater = new Updater();

import { getToken } from "@/utils/auth";
import getPageTitle from "@/utils/get-page-title";
NProgress.configure({ showSpinner: false });
const whiteList = ["/login"]; // no redirect whitelist
router.beforeEach(async (to, from, next) => {
  NProgress.start();
  document.title = getPageTitle(to.meta.title);
  store.commit("user/setPagePermits", to.path);
  const hasToken = getToken();
  if (hasToken) {
    if (to.path === "/login") {
      next("/");
      NProgress.done();
    } else {
      if (
        to.path === "/404Page" ||
        to.path === "/login" ||
        to.path === "/index" ||
        to.path === "/"
      ) {
        next();
      } else if (
        localStorage.getItem("systemMenu") &&
        localStorage.getItem("powerMenu") &&
        JSON.parse(localStorage.getItem("powerMenu")).includes(to.path)
      ) {
        // updater.check();
        next();
      } else {
        next({ path: "/404Page" });
      }
      NProgress.done();
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      // next(`/login?redirect=${to.path}`)
      next(`/login`);
      NProgress.done();
    }
  }
});
router.afterEach(() => {
  NProgress.done();
});
