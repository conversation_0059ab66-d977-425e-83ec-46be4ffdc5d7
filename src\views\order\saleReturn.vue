<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-09-13 17:30:43
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:19
 * @FilePath: src/views/order/saleReturn.vue
 * @Description: 耗材退货
 * 
-->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="退货明细" name="first" lazy>
        <SalesReturn type="detail" :columns="detailColumns" />
      </el-tab-pane>
      <el-tab-pane label="按物品统计退货" name="second" lazy>
        <SalesReturn type="item" :columns="itemColumns" />
      </el-tab-pane>
      <el-tab-pane label="按供应商统计退货" name="third" lazy>
        <SalesReturn type="provider" :columns="providerColumns" />
      </el-tab-pane>
      <el-tab-pane label="按客户统计退货" name="fourth" lazy>
        <SalesReturn type="customer" :columns="customerColumns" />
      </el-tab-pane>
      <el-tab-pane label="按系列统计退货" name="fifth" lazy>
        <SalesReturn type="series" :columns="seriesColumns" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import SalesReturn from "@/views/order/components/salesReturn.vue";
import { dictTreeByCodeApi, dictTreeByCodeApi2 } from "@/api/user";
export default {
  name: "SaleReturn",
  components: { SalesReturn },
  data() {
    return {
      activeName: "first",
      detailColumns: [
        {
          dataIndex: "reverseOrderCode",
          title: "售后单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "tradeOrderNum",
          title: "关联订单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "itemCode",
          title: "商品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "refundUnitPrice",
          title: "商品单价",
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "reverseItemNum",
          title: "退货数量",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "createdAt",
          title: "退货时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 150,
        },
      ],
      itemColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          minWidth: 160,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "type",
          title: "物品种类",
          width: 150,
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi2(2100, 2101),
          formatter: (row) => row.type?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "unit",
        //   title: "所属单元",
        //   isTable: true,
        //   formatter: (row) => row.unit?.label,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(3200),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "itemNum",
          title: "购买数量",
          isTable: true,
        },
        {
          dataIndex: "payAmount",
          title: "购买金额",
          isTable: true,
        },
        {
          dataIndex: "reverseItemNum",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "refundAmount",
          title: "退货金额",
          isTable: true,
        },
      ],
      providerColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          title: "供应商名称",
          dataIndex: "manufacturerName",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          title: "供应商编号",
          dataIndex: "manufacturerCode",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "itemNum",
          title: "购买数量",
          isTable: true,
        },
        {
          dataIndex: "payAmount",
          title: "购买金额",
          isTable: true,
        },
        {
          dataIndex: "reverseItemNum",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "refundAmount",
          title: "退货金额",
          isTable: true,
        },
      ],
      customerColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "itemNum",
          title: "购买数量",
          isTable: true,
        },
        {
          dataIndex: "actualPayAmount",
          title: "购买金额",
          isTable: true,
        },
        {
          dataIndex: "reverseItemNum",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "refundAmount",
          title: "退货金额",
          isTable: true,
        },
      ],
      seriesColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "productIds",
          isSearch: true,
          clearable: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
          title: "系列",
        },
        // {
        //   dataIndex: "brand",
        //   title: "品牌",
        //   isTable: true,
        // },
        {
          dataIndex: "series",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "itemNum",
          title: "购买数量",
          isTable: true,
        },
        {
          dataIndex: "actualPayAmount",
          title: "购买金额",
          isTable: true,
        },
        {
          dataIndex: "reverseItemNum",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "refundAmount",
          title: "退货金额",
          isTable: true,
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss"></style>
