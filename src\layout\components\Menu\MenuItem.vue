<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-09-19 18:44:25
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-10-09 11:57:57
 * @FilePath: \benyin-web\src\layout\components\Menu\MenuItem.vue
 * @Description: 侧边二级菜单
 * 
-->

<template>
  <div v-if="item.value">
    <!-- <el-menu-item :key="item.value" :index="item.children ? item.children[0].value : item.value" v-if="!item.children">
      <template #title>
        <span class="title">
          {{ item.children ? item.children[0].label : item.label }}
        </span>
      </template>
    </el-menu-item> -->

    <template v-for="(option, index) in item.children">
      <el-menu-item :index="option.value" :key="index">

        <span class="title" style="user-select: none;">
          {{ option?.label }}
        </span>
      </el-menu-item>
      <!-- <menu-item v-if="option.children" :key="option.value" :item="option" /> -->
      <!-- <el-menu-item v-else :index="option.value" :key="index">

        <span class="title" @click="">
          {{ option?.label }}
        </span>
      </el-menu-item> -->
    </template>
  </div>
</template>

 
<script>

import { isEmpty, cloneDeep } from "lodash";

export default {
  name: "MenuItem",
  components: {
  },
  mixins: [],
  props: {
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      mode: 'vertical',
      isCollapse: false,
      defaultActive: null,
      defaultOpened: null,
      uniqueOpenedFlag: null,
      routes: []
    };
  },

  computed: {},

  watch: {},
  created() { },
  mounted() {
  },
  methods: {
    handleOpen() {

    },
    handleClose() {

    },

  },
};
</script>
<style lang="scss" scoped>
.menu-icon,
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: $base-icon-width-big !important;
  height: $base-icon-height-super-max !important;
  margin-right: $base-margin-5;
  visibility: initial !important;
}
</style>
