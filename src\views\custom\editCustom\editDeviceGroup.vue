<template>
  <div class="edit-device-group">
    <!--<el-tabs v-model="activeName">-->
    <!--  <el-tab-pane label="基础信息" name="基础信息">-->
    <!--    <DeviceGroupBaseInfo :id="id" :type="type" />-->
    <!--  </el-tab-pane>-->
    <!--  <el-tab-pane label="设备数据记录" name="设备数据记录"-->
    <!--    >设备数据记录</el-tab-pane-->
    <!--  >-->
    <!--  <el-tab-pane label="合约记录" name="合约记录">合约记录</el-tab-pane>-->
    <!--  <el-tab-pane label="印量记录" name="印量记录">印量记录</el-tab-pane>-->
    <!--  <el-tab-pane label="故障记录" name="故障记录">故障记录</el-tab-pane>-->
    <!--  <el-tab-pane label="保养记录" name="保养记录">保养记录</el-tab-pane>-->
    <!--  <el-tab-pane label="维修记录" name="维修记录">-->
    <!--    <RepairRecord />-->
    <!--  </el-tab-pane>-->
    <!--</el-tabs>-->
    <DeviceGroupBaseInfo :id="id" :type="type" />
  </div>
</template>

<script>
import DeviceGroupBaseInfo from "@/views/custom/editCustom/components/deviceGroupBaseInfo.vue";
// import RepairRecord from "@/views/custom/editCustom/components/repairRecord.vue";
export default {
  name: "EditDeviceGroup",
  components: { DeviceGroupBaseInfo },
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
  },
  data() {
    return {
      activeName: "基础信息",
    };
  },
  async mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.file-container {
  display: flex;
  gap: 50px;
  flex-wrap: wrap;
  .file-list {
    font-size: 18px;
  }
}
</style>
