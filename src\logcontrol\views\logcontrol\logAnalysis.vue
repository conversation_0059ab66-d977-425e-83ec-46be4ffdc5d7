<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 日志查看主页面
-->
<template>
  <div class="log-analysis app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">日志查看</h1>
        <p class="page-description">查看和分析系统日志记录</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <i class="el-icon-refresh"></i>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <LogStatistics :statistics="statistics" :loading="statisticsLoading" />

    <!-- 搜索过滤 -->
    <el-card class="filter-card">
      <LogFilter 
        @filter-change="handleFilterChange"
        :devices="devices"
        :users="users"
        :loading="filterLoading"
      />
    </el-card>

    <!-- 日志表格 -->
    <el-card class="table-card">
      <LogTable 
        :data="logList"
        :loading="loading"
        :pagination="pagination"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
        @row-click="showLogDetail"
      />
    </el-card>

    <!-- 日志详情弹窗 -->
    <LogDetail 
      :visible.sync="detailVisible"
      :log-data="selectedLog"
      :loading="detailLoading"
    />
  </div>
</template>

<script>
import { logApi } from '@/logcontrol/api/logApi'
import { deviceApi } from '@/logcontrol/api/deviceApi'
import LogStatistics from './components/LogStatistics.vue'
import LogFilter from './components/LogFilter.vue'
import LogTable from './components/LogTable.vue'
import LogDetail from './components/LogDetail.vue'

export default {
  name: 'LogAnalysis',
  components: {
    LogStatistics,
    LogFilter,
    LogTable,
    LogDetail
  },
  data() {
    return {
      loading: false,
      statisticsLoading: false,
      filterLoading: false,
      detailLoading: false,
      
      // 日志列表数据
      logList: [],
      
      // 统计数据
      statistics: {
        crashLogs: 0,
        locationLogs: 0,
        businessLogs: 0,
        testLogs: 0
      },
      
      // 过滤条件
      filterParams: {
        dateRange: [],
        logLevel: '',
        deviceId: '',
        userId: '',
        keyword: ''
      },
      
      // 分页参数
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      
      // 下拉选项数据
      devices: [],
      users: [],
      
      // 详情弹窗
      detailVisible: false,
      selectedLog: null
    }
  },
  
  mounted() {
    this.initData()
  },
  
  methods: {
    // 初始化数据（优化：减少重复请求）
    async initData() {
      // 先加载日志列表，然后基于日志数据生成统计和筛选选项
      await this.loadLogList()
      await this.loadStatistics()
      await this.loadFilterOptions()
    },
    
    // 加载统计数据（使用日志类型统计接口）
    async loadStatistics() {
      this.statisticsLoading = true
      try {
        // 使用日志类型统计接口
        const response = await logApi.getLogTypeStatistics()

        if (response.data && Array.isArray(response.data)) {
          // 处理日志类型统计数据
          const typeStats = response.data
          this.statistics = {
            crashLogs: this.getLogTypeCount(typeStats, 'CRASH'),
            locationLogs: this.getLogTypeCount(typeStats, 'LOCATION'),
            businessLogs: this.getLogTypeCount(typeStats, 'BUSINESS'),
            performanceLogs: this.getLogTypeCount(typeStats, 'PERFORMANCE')
          }
        } else {
          // 降级：使用基础统计数据
          this.fallbackToBasicStats()
        }
      } catch (error) {
        // 降级：使用基础统计数据
        this.fallbackToBasicStats()
      } finally {
        this.statisticsLoading = false
      }
    },

    // 降级方法：使用基础统计数据
    fallbackToBasicStats() {
      // 使用模拟的日志类型统计数据
      this.statistics = {
        crashLogs: 1346,
        locationLogs: 768,
        businessLogs: 541,
        performanceLogs: 6
      }
    },

    // 从日志类型统计中获取指定类型的数量
    getLogTypeCount(logTypeStats, type) {
      if (!logTypeStats || !Array.isArray(logTypeStats)) return 0
      const stat = logTypeStats.find(item => item.log_type === type)
      return stat ? parseInt(stat.count) || 0 : 0
    },
    
    // 加载过滤选项（使用专门的API获取完整的设备和用户信息）
    async loadFilterOptions() {
      this.filterLoading = true
      try {
        // 方案1：使用专门的设备API获取完整设备列表
        let devices = []
        try {
          const deviceResponse = await deviceApi.getDeviceList()
          const deviceList = deviceResponse.data || []
          devices = deviceList.map(device => ({
            id: device.deviceId || device.id,
            deviceId: device.deviceId || device.id,
            name: device.name || `设备 ${device.deviceId || device.id}`
          }))
        } catch (deviceError) {
          console.warn('获取设备列表失败，使用日志数据提取:', deviceError)
          // 降级方案：从日志数据中提取设备信息
          devices = await this.extractDevicesFromLogs()
        }

        // 方案2：从日志数据中提取用户信息（因为没有专门的用户API）
        const users = await this.extractUsersFromLogs()

        this.devices = devices
        this.users = users


      } catch (error) {

        // 使用模拟数据，不显示错误消息
        this.devices = [
          { id: 'device001', deviceId: 'device001', name: '设备 device001' },
          { id: 'device002', deviceId: 'device002', name: '设备 device002' },
          { id: 'device003', deviceId: 'device003', name: '设备 device003' }
        ]
        this.users = [
          { userId: '1730200832705589250', userName: '王季春', userCode: '********' },
          { userId: '1730200832705589251', userName: '李明', userCode: '********' },
          { userId: '1730200832705589252', userName: '张三', userCode: '********' }
        ]
      } finally {
        this.filterLoading = false
      }
    },
    
    // 加载日志列表（使用新的分页接口）
    async loadLogList() {
      this.loading = true
      try {
        // 构建分页和筛选参数
        const params = {
          pageNum: this.pagination.current,
          pageSize: this.pagination.size,
          ...this.filterParams
        }

        // 处理时间范围参数
        if (this.filterParams.dateRange && this.filterParams.dateRange.length === 2) {
          params.startTime = this.filterParams.dateRange[0]
          params.endTime = this.filterParams.dateRange[1]
          delete params.dateRange // 移除原始的dateRange参数
        }

        // 参数映射：将前端参数映射为后端接口参数
        if (this.filterParams.deviceId) {
          params.deviceId = this.filterParams.deviceId
        }
        if (this.filterParams.logType) {
          params.logType = this.filterParams.logType
        }
        if (this.filterParams.level) {
          params.logLevel = this.filterParams.level // 前端用level，后端用logLevel
        }
        if (this.filterParams.userId) {
          params.userId = this.filterParams.userId // 用户ID筛选（唯一标识）
        }



        // 使用新的分页接口
        const response = await logApi.getLogListWithPagination(params)

        if (response.data && response.data.list) {
          // 新接口返回格式：{ list, total, pageNum, pageSize, pages }
          this.logList = response.data.list
          // 注意：后端返回的total和pages是字符串类型，需要转换为数字
          this.pagination.total = parseInt(response.data.total) || 0
          this.pagination.current = response.data.pageNum || params.pageNum || 1
          this.pagination.size = response.data.pageSize || params.pageSize || 20




        } else {
          // 兼容旧接口返回格式
          this.logList = response.data || []
          this.pagination.total = this.logList.length


        }
      } catch (error) {

        this.$message.error('加载日志列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 处理过滤条件变化
    handleFilterChange(filters) {
      this.filterParams = { ...filters }
      this.pagination.current = 1
      this.loadLogList()
    },
    
    // 处理分页变化
    handlePageChange(page) {
      this.pagination.current = page
      this.loadLogList()
    },
    
    // 处理页大小变化
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadLogList()
    },
    
    // 显示日志详情
    showLogDetail(row) {
      this.selectedLog = row
      this.detailVisible = true
    },
    




    // 刷新数据
    async refreshData() {
      try {
        await this.initData()
        this.$message.success('数据刷新成功')
      } catch (error) {

        this.$message.error('数据刷新失败')
      }
    },

    // 辅助方法：从日志数据中提取设备信息（降级方案）
    async extractDevicesFromLogs() {
      try {
        // 获取更多日志数据来提取设备信息
        const response = await logApi.getLogListWithPagination({
          pageNum: 1,
          pageSize: 1000  // 获取更多数据以确保设备信息完整性
        })
        const logs = response.data?.list || []

        // 提取唯一的设备ID
        const deviceIds = [...new Set(logs.map(log => log.deviceId).filter(Boolean))]
        return deviceIds.map(id => ({
          id: id,
          deviceId: id,
          name: `设备 ${id}`
        }))
      } catch (error) {
        console.warn('从日志数据提取设备信息失败:', error)
        return []
      }
    },

    // 辅助方法：从日志数据中提取用户信息
    async extractUsersFromLogs() {
      try {
        // 获取更多日志数据来提取用户信息
        const response = await logApi.getLogListWithPagination({
          pageNum: 1,
          pageSize: 1000  // 获取更多数据以确保用户信息完整性
        })
        const logs = response.data?.list || []

        // 提取唯一的用户信息
        const uniqueUsers = logs.reduce((acc, log) => {
          if (log.userId && log.userName) {
            const key = log.userId
            if (!acc[key]) {
              acc[key] = {
                userId: log.userId,
                userName: log.userName,
                userCode: log.userCode || ''
              }
            }
          }
          return acc
        }, {})

        return Object.values(uniqueUsers)
      } catch (error) {
        console.warn('从日志数据提取用户信息失败:', error)
        return []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.log-analysis {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      .page-title {
        margin: 0 0 5px 0;
        font-size: 24px;
        font-weight: 500;
        color: #303133;
      }
      
      .page-description {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
    
    .header-actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }
  
  .filter-card {
    margin-bottom: 20px;

    .el-card__body {
      padding: 20px;
    }
  }

  .table-card {
    margin-bottom: 20px;
  }
}
</style>
