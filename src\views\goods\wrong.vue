<!--
 * @Author: wskg
 * @Date: 2024-08-28 10:11:00
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 问题商品
 -->
<template>
  <div class="view app-container">
    <CheckIn />
    <!--<el-tabs v-model="activeName">-->
    <!--  <el-tab-pane label="商品登记" name="商品登记">-->
    <!--    -->
    <!--  </el-tab-pane>-->
    <!--  <el-tab-pane label="按物品编号统计" name="按物品编号统计"-->
    <!--    >按物品编号统计</el-tab-pane-->
    <!--  >-->
    <!--  <el-tab-pane label="按供应商统计" name="按供应商统计"-->
    <!--    >按供应商统计</el-tab-pane-->
    <!--  >-->
    <!--</el-tabs>-->
  </div>
</template>

<script>
import CheckIn from "@/views/goods/components/checkIn.vue";
export default {
  name: "Wrong",
  components: { CheckIn },
  data() {
    return {
      activeName: "商品登记",
    };
  },
};
</script>

<style scoped lang="scss"></style>
