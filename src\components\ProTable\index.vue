<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-04 17:31:06
 * @Description: 
 -->
<template>
  <!--columns.filter((i) => i.isSearch)-->
  <div class="pro-table" :class="sticky ? 'sticky' : ''">
    <SearchForm
      v-if="showSearch"
      ref="SearchForm"
      :isrules="isrules"
      :search-list="searchList"
      :query-param="queryParam"
      :loading="listLoading"
      :layout="layout"
      @searchChange="searchChange"
      @search="refresh"
      @selectChange="selectChange"
    >
      <template #header>
        <slot name="searchHeader"></slot>
      </template>
      <template #form>
        <slot name="searchForm"></slot>
      </template>
      <template #footer>
        <slot name="searchFooter"></slot>
      </template>

      <template
        v-for="(item, index) in columns.filter((i) => i.isSearch)"
        #[item.searchSlot]
      >
        <div :key="index">
          <slot :name="item.searchSlot"></slot>
        </div>
      </template>
    </SearchForm>
    <div v-if="showTableOperator" class="table-operator">
      <div class="solt">
        <slot name="btn"></slot>
      </div>
      <TableSetting
        v-if="showSetting"
        :columns="tableColumns"
        :show-setting-btn="showSettingButton"
        :refresh="refresh"
        @reRender="updateTable"
      >
        <template #default>
          <slot name="setting"></slot>
        </template>
      </TableSetting>
      <TableRule v-if="showRule">
        <template #default>
          <slot name="rule"></slot>
        </template>
      </TableRule>
    </div>
    <el-table
      ref="ProElTable"
      :key="key"
      v-loading="showLoading && listLoading"
      :data="data"
      v-bind="$attrs"
      :height="
        height
          ? height
          : columns.filter((i) => i.isSearch).length > 10
          ? 458
          : columns.filter((i) => i.isSearch).length > 5 &&
            columns.filter((i) => i.isSearch).length < 10
          ? 520
          : columns.filter((i) => i.isSearch).length <= 5
          ? 580
          : null
      "
      :stripe="stripe"
      style="overflow: hidden"
      :style="{
        marginBottom:
          showPagination && localPaginationNew.total ? '0px' : '20px',
      }"
      :span-method="spanMethod"
      :tree-props="treeProps"
      :row-key="rowKey"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
      @row-click="(row) => handleRowClick(row)"
      @select="handleSelected"
      @select-all="handleSelectedAll"
      @sort-change="handleSortChange"
    >
      <template slot="empty">
        <el-empty description="暂无数据"></el-empty>
      </template>
      <el-table-column
        v-if="showSelection"
        :reserve-selection="true"
        type="selection"
        align="left"
        width="55"
      />
      <el-table-column
        v-if="showIndex"
        label="序号"
        width="50"
        align="left"
        type="index"
      />
      <el-table-column v-if="showExpand" type="expand">
        <!-- eslint-disable-next-line  -->
        <template slot-scope="props">
          <slot name="expandProps"></slot>
        </template>
      </el-table-column>
      <el-table-column
        v-for="item in tableColumns"
        :key="item.dataIndex"
        :label="item.title"
        :min-width="item.minWidth"
        :width="item.width"
        :align="item.align ? item.align : 'left'"
        :sortable="item.sortable || null"
        :sort-method="item.sortMethod"
        :prop="item.sortable ? item.dataIndex : null"
        :show-overflow-tooltip="item.tooltip ?? true"
        :formatter="item.formatter"
        :fixed="item.fixed"
      >
        <template v-if="!item.formatter" #default="scope">
          <span v-if="!item.tableSlot">{{ scope.row[item.dataIndex] }}</span>
          <span v-if="item.tableSlot === 'area'">
            {{ scope.row[item.dataIndex] }}
          </span>
          <span v-else-if="item.tableSlot === 'date'">
            {{ scope.row[item.dataIndex] | parseTime2 }}
          </span>
          <span v-else-if="item.tableSlot === 'datetime'">
            {{ scope.row[item.dataIndex] | parseTime }}
          </span>
          <slot
            v-else
            :name="item.tableSlot"
            :row="scope.row"
            :index="scope.$index"
          ></slot>
        </template>
      </el-table-column>
    </el-table>
    <div class="solt">
      <slot name="footer"></slot>
    </div>
    <Pagination
      v-if="showPagination && localPaginationNew.total"
      :page="localPaginationNew.pageNumber"
      layout="total,jumper, prev, pager, next"
      :page-size="localPaginationNew.pageSize"
      :total="localPaginationNew.total"
      :pager-count="pagerCount"
      @pagination="loadData"
    />
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import SearchForm from "@/components/SearchForm/index.vue";
import TableSetting from "@/components/ProTable/table-setting.vue";
import TableRule from "@/components/ProTable/table-rule.vue";
import { mapState } from "vuex";
import { cloneDeep } from "lodash";

export default {
  name: "ProTable",
  components: {
    Pagination,
    SearchForm,
    TableSetting,
    TableRule,
  },
  props: {
    isrules: {
      type: Boolean,
      default: false,
    },
    spanMethod: {
      type: Function,
      default: () => {},
    },
    //是否可展开
    showExpand: {
      type: Boolean,
      default: false,
    },
    lazyLoad: {
      type: Boolean,
      default: false,
    },
    // 是否为斑马纹 table
    stripe: {
      type: Boolean,
      default: true,
    },
    // 顶部搜索
    showSearch: {
      type: Boolean,
      default: true,
    },

    showOperator: {
      type: Boolean,
      default: false,
    },
    // 表单加载
    showLoading: {
      type: Boolean,
      default: true,
    },
    // 搜索参数
    queryParam: {
      type: Object,
      default: () => {
        return {};
      },
    },
    layout: {
      type: Object,
      default: () => {
        return {};
      },
    },
    treeProps: {
      type: Object,
      default: () => {
        return {};
      },
    },
    height: {
      type: [Number, String],
      default: 510,
    },
    // 分页大小
    pageSize: {
      type: Number,
      default: 15,
    },
    showSetting: {
      type: Boolean,
      default: true,
    },
    // 序号
    showIndex: {
      type: Boolean,
      default: true,
    },
    // 吸顶
    sticky: {
      type: Boolean,
      default: false,
    },
    // 选中
    showSelection: {
      type: Boolean,
      default: false,
    },
    // 显示分页
    showPagination: {
      type: Boolean,
      default: true,
    },
    showTableOperator: {
      type: Boolean,
      default: true,
    },
    // 总页数
    pagerCount: {
      type: Number,
      default: 11,
    },
    // 数据
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
    columns: {
      type: Array,
      default: () => {
        return [];
      },
    },
    localPagination: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 表格行样式
    tableRowClassName: {
      type: Function,
      default: () => {},
    },
    rowKey: {
      type: [String, Function],
      default: "id",
    },
    // 是否显示表格内容计算规则
    showRule: {
      type: Boolean,
      default: false,
    },
    // 是否显示设置按钮
    showSettingButton: {
      type: Boolean,
      default: false,
    },
    // 是否使用无限滚动加载
    useInfiniteScroll: {
      type: Boolean,
      default: false,
    },
    // 是否还有更多数据
    hasMore: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      search: "",
      searchList: [],
      ProElTable: "",
      key: 0,
      list: null,
      listLoading: true,
      tableColumns: [],
      paramesdata: {},
      localPaginationNew: {
        pageNumber: 1,
        pageSize: 10,
      },
      isScrolling: false,
    };
  },
  computed: {
    ...mapState({
      // 'isMobile': state => state.setting.isMobile,
      isLogo: (state) => state.setting.isLogo,
      // 'routes': state => state.routes.routes
    }),
    menuBgColor() {
      return "";
    },
  },
  watch: {
    columns: {
      handler() {
        this.initColumns();
      },
      deep: true,
    },
    localPagination: {
      handler(val) {
        this.localPaginationNew = cloneDeep(val);
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    if (!this.lazyLoad) {
      this.initColumns();
      // this.loadData()
    }
  },
  mounted() {
    if (this.useInfiniteScroll) {
      this.$refs.ProElTable.bodyWrapper.addEventListener(
        "scroll",
        this.handleScroll
      );
    }
  },
  beforeDestroy() {
    if (this.useInfiniteScroll) {
      this.$refs.ProElTable.bodyWrapper.removeEventListener(
        "scroll",
        this.handleScroll
      );
    }
  },
  methods: {
    initColumns() {
      this.tableColumns = cloneDeep(this.columns)
        .filter((i) => i.isTable)
        .map((i) => {
          return i;
        });
      this.searchList = cloneDeep(this.columns)
        .filter((i) => i.isSearch)
        .map((i) => {
          return i;
        });
    },
    setSelection(list) {
      if (this.$refs.ProElTable) {
        this.$refs.ProElTable.clearSelection();
        if (list && list.length) {
          list.forEach((id) => {
            const row = this.data.find((o) => o[this.rowKey] == id);
            this.$refs.ProElTable.toggleRowSelection(row, true);
          });
        }
      }
    },
    handleScroll(e) {
      if (this.isScrolling || !this.hasMore || this.listLoading) return;
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      // 当滚动到距离底部200px时触发加载
      if (scrollHeight - scrollTop - clientHeight < 249) {
        this.loadMoreData();
      }
    },
    loadMoreData() {
      if (!this.hasMore || this.listLoading) return;

      this.isScrolling = true;
      this.localPaginationNew.pageNumber += 1;
      this.listLoading = true;
      this.paramesdata = Object.assign({}, this.paramesdata, {
        pageNumber: this.localPaginationNew.pageNumber,
        pageSize: this.localPaginationNew.pageSize,
      });
      this.$emit("loadData", this.paramesdata);
    },
    loadData(val) {
      if (this.useInfiniteScroll) {
        if (val.page) {
          // 如果是分页组件触发的加载，重置页码
          this.localPaginationNew.pageNumber = 1;
          this.paramesdata = Object.assign(
            {},
            this.paramesdata,
            { pageNumber: 1, pageSize: this.localPaginationNew.pageSize },
            cloneDeep(val)
          );
        } else {
          // 如果是滚动加载触发的，使用当前页码
          this.paramesdata = Object.assign(
            {},
            this.paramesdata,
            {
              pageNumber: this.localPaginationNew.pageNumber,
              pageSize: this.localPaginationNew.pageSize,
            },
            cloneDeep(val)
          );
        }
        delete this.paramesdata.page;
        delete this.paramesdata.total;
        this.$emit("loadData", this.paramesdata);
      } else {
        this.localPaginationNew.pageNumber = val.page;
        this.paramesdata.pageNumber = val.page;
        this.listLoading = true;
        this.paramesdata = Object.assign(
          {},
          this.localPaginationNew,
          this.paramesdata,
          cloneDeep(val)
        );
        delete this.paramesdata.page;
        delete this.paramesdata.total;
        this.$emit("loadData", this.paramesdata);
      }

      this.$nextTick(() => {
        if (this.$refs.ProElTable && this.$refs.ProElTable.bodyWrapper) {
          this.$refs.ProElTable.bodyWrapper.scrollTop = 0;
        }
      });
    },
    refresh(val, isSearch) {
      if (isSearch || !this.localPaginationNew.pageNumber) {
        this.localPaginationNew.pageNumber = 1;
      }
      this.paramesdata = Object.assign(
        {},
        cloneDeep(val),
        this.localPaginationNew
      );
      delete this.paramesdata.page;
      delete this.paramesdata.total;
      this.listLoading = true;
      this.$emit("loadData", this.paramesdata);
    },
    resetScrolling() {
      this.isScrolling = false;
      this.listLoading = false;
    },
    updateTable() {
      this.key = this.key += 1;
    },
    selectChange(val) {
      this.$emit("selectChange", val);
    },
    searchChange(val) {
      this.$emit("searchChange", val);
    },
    handleSelectionChange(val) {
      this.$emit("handleSelectionChange", val);
    },
    handleSelected(selection, row) {
      this.$emit("handleSelected", selection, row);
    },
    handleSelectedAll(row) {
      this.$emit("handleSelectedAll", row);
    },
    handleSortChange(prop) {
      this.$emit("sortChange", prop);
    },
    handleRowClick(val) {
      this.$emit("rowClick", val);
    },
  },
};
</script>
<style>
.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f0f9eb;
}
</style>
