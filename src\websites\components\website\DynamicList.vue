<template>
  <div class="dynamic-list">
    <!-- 列表标题 -->
    <div class="list-header">
      <h4 class="list-title">{{ title }}</h4>
      <el-button size="mini" type="primary" icon="el-icon-plus" @click="addItem">
        添加{{ itemName }}
      </el-button>
    </div>
    
    <!-- 列表内容 -->
    <div class="list-content">
      <div
        v-for="(item, index) in localItems"
        :key="getItemKey(item, index)"
        class="list-item"
        :class="{ 'dragging': dragIndex === index }"
      >
        <!-- 拖拽手柄 -->
        <div class="drag-handle" @mousedown="startDrag(index)">
          <i class="el-icon-rank"></i>
        </div>
        
        <!-- 项目内容 -->
        <div class="item-content">
          <slot :item="item" :index="index" :updateItem="updateItem">
            <!-- 默认内容：简单文本编辑 -->
            <el-input
              v-if="typeof item === 'string'"
              v-model="localItems[index]"
              @input="handleItemChange"
              :placeholder="`请输入${itemName}`"
            />
            <!-- 对象类型的默认编辑 -->
            <div v-else class="object-editor">
              <div v-for="(value, key) in item" :key="key" class="field-editor">
                <label>{{ getFieldLabel(key) }}:</label>
                <el-input
                  v-model="item[key]"
                  @input="handleItemChange"
                  :placeholder="`请输入${getFieldLabel(key)}`"
                />
              </div>
            </div>
          </slot>
        </div>
        
        <!-- 操作按钮 -->
        <div class="item-actions">
          <el-button size="mini" icon="el-icon-top" @click="moveUp(index)" :disabled="index === 0">
            上移
          </el-button>
          <el-button size="mini" icon="el-icon-bottom" @click="moveDown(index)" :disabled="index === localItems.length - 1">
            下移
          </el-button>
          <el-button size="mini" type="danger" icon="el-icon-delete" @click="removeItem(index)">
            删除
          </el-button>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="localItems.length === 0" class="empty-state">
        <i class="el-icon-document-add"></i>
        <p>暂无{{ itemName }}，点击上方按钮添加</p>
      </div>
    </div>
    
    <!-- 添加项目对话框 -->
    <el-dialog
      :title="`添加${itemName}`"
      :visible.sync="addDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="add-form">
        <slot name="addForm" :newItem="newItem" :updateNewItem="updateNewItem">
          <!-- 默认添加表单 -->
          <el-input
            v-if="itemType === 'string'"
            v-model="newItem"
            :placeholder="`请输入${itemName}`"
            @keydown.enter="confirmAdd"
          />
          <div v-else class="object-form">
            <div v-for="(value, key) in newItem" :key="key" class="form-field">
              <el-form-item :label="getFieldLabel(key)">
                <el-input
                  v-model="newItem[key]"
                  :placeholder="`请输入${getFieldLabel(key)}`"
                />
              </el-form-item>
            </div>
          </div>
        </slot>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAdd">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DynamicList',
  props: {
    // 列表数据
    value: {
      type: Array,
      default: () => []
    },
    // 列表标题
    title: {
      type: String,
      default: '列表'
    },
    // 项目名称
    itemName: {
      type: String,
      default: '项目'
    },
    // 项目类型
    itemType: {
      type: String,
      default: 'string', // 'string' | 'object'
    },
    // 默认项目模板
    itemTemplate: {
      type: [String, Object],
      default: ''
    },
    // 字段标签映射
    fieldLabels: {
      type: Object,
      default: () => ({})
    },
    // 最大项目数量
    maxItems: {
      type: Number,
      default: 20
    },
    // 是否支持拖拽排序
    sortable: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      localItems: [],
      addDialogVisible: false,
      newItem: null,
      dragIndex: -1,
      dragStartY: 0
    }
  },
  
  watch: {
    value: {
      handler(newVal) {
        this.localItems = Array.isArray(newVal) ? [...newVal] : []
      },
      immediate: true,
      deep: true
    }
  },
  
  methods: {
    // 获取项目唯一键
    getItemKey(item, index) {
      if (typeof item === 'object' && item.id) {
        return item.id
      }
      return `item-${index}`
    },
    
    // 获取字段标签
    getFieldLabel(key) {
      return this.fieldLabels[key] || key
    },
    
    // 添加项目
    addItem() {
      if (this.localItems.length >= this.maxItems) {
        this.$message.warning(`最多只能添加${this.maxItems}个${this.itemName}`)
        return
      }
      
      // 初始化新项目
      if (this.itemType === 'string') {
        this.newItem = ''
      } else {
        this.newItem = typeof this.itemTemplate === 'object' 
          ? { ...this.itemTemplate }
          : {}
      }
      
      this.addDialogVisible = true
    },
    
    // 确认添加
    confirmAdd() {
      if (this.itemType === 'string' && !this.newItem.trim()) {
        this.$message.warning(`请输入${this.itemName}内容`)
        return
      }
      
      if (this.itemType === 'object') {
        const hasEmpty = Object.values(this.newItem).some(value => !value || !value.toString().trim())
        if (hasEmpty) {
          this.$message.warning('请填写完整信息')
          return
        }
      }
      
      this.localItems.push(this.itemType === 'string' ? this.newItem.trim() : { ...this.newItem })
      this.handleItemChange()
      this.addDialogVisible = false
    },
    
    // 移除项目
    removeItem(index) {
      this.$confirm(`确定要删除这个${this.itemName}吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.localItems.splice(index, 1)
        this.handleItemChange()
      })
    },
    
    // 上移项目
    moveUp(index) {
      if (index > 0) {
        const item = this.localItems.splice(index, 1)[0]
        this.localItems.splice(index - 1, 0, item)
        this.handleItemChange()
      }
    },
    
    // 下移项目
    moveDown(index) {
      if (index < this.localItems.length - 1) {
        const item = this.localItems.splice(index, 1)[0]
        this.localItems.splice(index + 1, 0, item)
        this.handleItemChange()
      }
    },
    
    // 更新项目
    updateItem(index, newValue) {
      this.$set(this.localItems, index, newValue)
      this.handleItemChange()
    },
    
    // 更新新项目
    updateNewItem(newValue) {
      this.newItem = newValue
    },
    
    // 处理项目变化
    handleItemChange() {
      this.$emit('input', [...this.localItems])
      this.$emit('change', [...this.localItems])
    },
    
    // 开始拖拽
    startDrag(index) {
      if (!this.sortable) return
      
      this.dragIndex = index
      this.dragStartY = event.clientY
      
      const handleMouseMove = (e) => {
        // 拖拽逻辑可以在这里实现
      }
      
      const handleMouseUp = () => {
        this.dragIndex = -1
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
      
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }
  }
}
</script>

<style lang="scss" scoped>
.dynamic-list {
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .list-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .list-content {
    .list-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 12px;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #c0c4cc;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      &.dragging {
        opacity: 0.5;
        transform: scale(0.95);
      }
      
      .drag-handle {
        cursor: move;
        color: #909399;
        padding: 4px;
        
        &:hover {
          color: #409eff;
        }
      }
      
      .item-content {
        flex: 1;
        
        .object-editor {
          .field-editor {
            margin-bottom: 12px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            label {
              display: block;
              margin-bottom: 4px;
              font-size: 14px;
              color: #606266;
            }
          }
        }
      }
      
      .item-actions {
        display: flex;
        gap: 4px;
        flex-shrink: 0;
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #909399;
      
      i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }
      
      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
  
  .add-form {
    .object-form {
      .form-field {
        margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dynamic-list {
    .list-content .list-item {
      flex-direction: column;
      
      .item-actions {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style>
