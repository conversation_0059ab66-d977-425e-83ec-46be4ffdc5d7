<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:54
 * @Description: 仓库管理
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增仓库
        </el-button>
      </template>
      <!-- <template #addr>
        <el-cascader
          ref="ProductIds"
          v-model="queryParam.city"
          filterable
          clearable
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template> -->

      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleInfo(slotProps.row)"
          >
            查看
          </el-button>

          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>

          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="50%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType == 'info'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '180px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
        <template #province>
          <el-cascader
            ref="ProductIds"
            v-model="form.pcity"
            filterable
            :disabled="methodType == 'info'"
            :options="options"
            style="width: 100%"
            :props="{
              label: 'name',
              value: 'code',
              children: 'children',
              expandTrigger: 'click',
            }"
            clearable
            leaf-only
            @change="handleChange"
          ></el-cascader>
        </template>
      </ProForm>
    </ProDrawer>
  </div>
</template>
<script>
import {
  warehousePageApi,
  warehouseAddApi,
  warehouseDelApi,
  warehouseEditApi,
  regionTreeApi,
} from "@/api/store";
import { dictTreeByCodeApi } from "@/api/user";

import { isEmpty, cloneDeep } from "lodash";

export default {
  name: "Warehouse",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      active: 9,
      // 列表
      spareiTypeList: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        name: null,
      },
      columns: [
        {
          dataIndex: "name",
          title: "仓库名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "type",
          title: "仓库类型",
          isTable: true,
          formatter: (row) => row.type.label,
        },
        {
          dataIndex: "addr",
          title: "详细地址",
          isTable: true,
          minWidth: 250,
        },
        {
          dataIndex: "status",
          title: "仓库状态",
          isTable: true,
          formatter: (row) => row.status.label,
        },

        {
          dataIndex: "remarks",
          title: "备注",
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 240,
        },
      ],
      options: [],
      //新增
      methodType: "add",
      confirmLoading: false,
      form: { parentId: "" },
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: { parentId: "" },
      formcolumns: [
        {
          dataIndex: "name",
          title: "仓库名称",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 18,
          prop: [
            {
              required: true,
              message: "请输入仓库名称",
              trigger: "change",
            },
          ],
        },
        {
          clearboth: true,
          dataIndex: "type",
          title: "仓库类型",
          isForm: true,
          valueType: "select",
          formSpan: 18,
          option: [],
          optionMth: () => dictTreeByCodeApi(1300),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择仓库类型",
              trigger: "change",
            },
          ],
        },
        {
          clearboth: true,
          dataIndex: "province",
          title: "仓库地址",
          isForm: true,
          formSlot: "province",
          formSpan: 18,
          prop: [
            {
              required: true,
              message: "请选择仓库地址",
              trigger: "change",
            },
          ],
        },
        {
          clearboth: true,
          dataIndex: "addr",
          title: "详细地址",
          valueType: "input",
          isForm: true,
          formSpan: 18,
          prop: [
            {
              required: true,
              message: "请输入详细地址",
              trigger: "change",
            },
          ],
        },

        {
          clearboth: true,
          dataIndex: "status",
          title: "仓库状态",
          isForm: true,
          valueType: "select",
          formSpan: 18,
          option: [],
          optionMth: () => dictTreeByCodeApi(1400),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择仓库状态",
              trigger: "change",
            },
          ],
        },
        {
          clearboth: true,
          dataIndex: "postalCode",
          title: "邮政编码",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 18,
        },
        {
          clearboth: true,
          dataIndex: "contact",
          title: "物流发货人",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 18,
          prop: [
            {
              required: true,
              message: "请输入物流发货人",
              trigger: "change",
            },
          ],
        },
        {
          clearboth: true,
          dataIndex: "company",
          title: "物流发货人公司",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 18,
        },
        {
          clearboth: true,
          dataIndex: "phone",
          title: "物流发货人电话",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 18,
          prop: [
            {
              required: true,
              message: "请输入物流发货人电话",
              trigger: "change",
            },
          ],
        },
        {
          clearboth: true,
          dataIndex: "remarks",
          title: "备注",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          clearable: true,
          formSpan: 18,
        },
      ],
      //字典项
      roleId: null,
      dialogTitleR: "",
      dialogVisibleR: false,
      dialogVisibleU: false,
    };
  },

  computed: {},

  watch: {
    $route: {
      handler(val) {
        if (val.query.name) {
          this.queryParam.name = val.query.name;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {},
  mounted() {
    regionTreeApi().then((res) => {
      this.options = res.data;
      this.$refs.ProTable.refresh();
    });
  },
  methods: {
    handleSelect(item) {
      this.$set(this.queryParam, "province", item[0]);
      this.$set(this.queryParam, "city", item[1]);
      this.$set(this.queryParam, "county", item[2]);
    },
    handleChange(item) {
      console.log(item);
      if (item.length > 2) {
        this.$set(this.form, "province", item[0]);
        this.$set(this.form, "city", item[1]);
        this.$set(this.form, "county", item[2]);
      } else {
        this.$set(this.form, "province", item[0]);
        this.$set(this.form, "city", "");
        this.$set(this.form, "county", item[1]);
      }
    },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      warehousePageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.dialogTitle = "新增仓库";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;

      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应新增
    create() {
      warehouseAddApi(this.form)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑 - " + row.name;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.form.status = row.status.value;
      this.form.type = row.type.value;
      this.form.pcity = row.city
        ? [parseInt(row.province), parseInt(row.city), parseInt(row.county)]
        : [parseInt(row.province), parseInt(row.county)];

      this.methodType = "edit";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应编辑
    update() {
      warehouseEditApi(this.form)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.name;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.form = cloneDeep(row);
      this.form.status = row.status.value;
      this.form.type = row.type.value;
      this.form.pcity = row.city
        ? [parseInt(row.province), parseInt(row.city), parseInt(row.county)]
        : [parseInt(row.province), parseInt(row.county)];

      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        warehouseDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
