<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:54
 * @Description: 
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      show-pagination
      row-key="label"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :height="550"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #actions="slotProps">
        <div class="fixed-width">
          <el-button
            plain
            size="mini"
            type="primary"
            icon="el-icon-circle-check"
            @click="handleCheck(slotProps.row)"
          >
            详情
          </el-button>
          <el-button
            plain
            size="mini"
            type="primary"
            icon="el-icon-edit"
            @click="handleSub(slotProps.row)"
          >
            减免
          </el-button>
        </div>
      </template>
    </ProTable>

    <!-- 查看收款单详情 -->
    <ProDrawer
      :value="showReceipt"
      :title="drawerTitle"
      size="85%"
      :top="'10%'"
      :no-footer="true"
      @cancel="showReceipt = false"
    >
      <el-descriptions title="收款详情" :column="1">
        <el-descriptions-item label="店铺名称">{{
          showReceiptInfo?.customerName
        }}</el-descriptions-item>
        <el-descriptions-item label="客户编号">{{
          showReceiptInfo?.customerSeq
        }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag
            :type="displayStatus(showReceiptInfo?.status?.value)"
            size="mini"
            >{{ showReceiptInfo?.status?.label }}</el-tag
          >
        </el-descriptions-item>
        <el-descriptions-item label="总金额" :span="2">
          {{
            divideAmount(showReceiptInfo?.totalAmount, 100)
          }}元</el-descriptions-item
        >
        <el-descriptions-item label="减免费用" :span="2">
          {{
            divideAmount(showReceiptInfo?.derateAmount, 100)
          }}元</el-descriptions-item
        >
        <el-descriptions-item label="应付金额" :span="2">
          {{ divideAmount(showReceiptInfo?.amount, 100) }}元
        </el-descriptions-item>
        <!--        <el-descriptions-item label="已付金额" :span="2">{{-->
        <!--          showReceiptInfo?.paidAmount / 100-->
        <!--        }}</el-descriptions-item>-->
        <!--        <el-descriptions-item label="待付金额" :span="2">{{-->
        <!--          showReceiptInfo?.amount / 100-->
        <!--        }}</el-descriptions-item>-->
      </el-descriptions>

      <!-- 商品信息 -->
      <div class="m-t-8">
        <p class="tit-box m-b-12">收款单信息</p>
        <ProTable
          ref="ProSPXXTable"
          :columns="receiptDetailColumns"
          :show-pagination="false"
          :show-loading="false"
          :data="receiptDetailData || []"
          :show-setting="false"
          :show-search="false"
          :show-table-operator="false"
          sticky
        >
          <template #storageArticle="{ row }">
            {{ row.storageArticle.minUnit }}
          </template>
        </ProTable>
      </div>
    </ProDrawer>

    <!--  调整抄表费用  -->
    <ProDialog
      :value="expenseDialog"
      title="减免抄表费用"
      width="40%"
      :confirm-loading="false"
      :confirm-text="'确认减免'"
      @ok="confirmSub"
      @cancel="expenseDialog = false"
    >
      <ProForm
        :form-param="form"
        :form-list="derateForm"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
      ></ProForm>
      <!--      <div class="expense">-->
      <!--        <el-input-number-->
      <!--          v-model="derateAmount"-->
      <!--          :precision="2"-->
      <!--          :step="10"-->
      <!--        ></el-input-number>-->
      <!--      </div>-->
      <!--      <div class="footer-btn">-->
      <!--        <el-button @click="expenseDialog = false">取消</el-button>-->
      <!--        <el-popconfirm-->
      <!--          style="gap: 20px"-->
      <!--          placement="top"-->
      <!--          title="确定要减免该收款的抄表费吗？"-->
      <!--          @confirm="confirmSub"-->
      <!--        >-->
      <!--          <el-button slot="reference" type="primary">减免</el-button>-->
      <!--        </el-popconfirm>-->
      <!--      </div>-->
    </ProDialog>
  </div>
</template>
<script>
import {
  printReceiveListApi,
  showMeterDetailApi,
  receiptReduceApi,
  receiptMonthRepairListApi,
} from "@/api/statisics";
import { cloneDeep } from "lodash";
import { filterParam, filterParamRange, divideAmount } from "@/utils";
import { dictTreeByCodeApi } from "@/api/user";
export default {
  name: "ReceiptManage",
  mixins: [],
  props: {},

  data() {
    return {
      active: 9,
      // 列表
      spareiTypeList: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      productTreeOption: [],
      queryParam: {},
      columns: [
        {
          dataIndex: "code",
          title: "收款单编号",
          valueType: "input",
          isSearch: true,
          isTable: true,
          minWidth: 160,
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          valueType: "input",
          isSearch: true,
          isTable: true,
          minWidth: 160,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          valueType: "input",
          isSearch: true,
          isTable: true,
          minWidth: 180,
        },
        {
          dataIndex: "cycle",
          title: "年月",
          isTable: true,
        },
        {
          dataIndex: "type",
          title: "合约类型",
          isTable: true,
          formatter: (row) => row.type?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "全半保费用",
              value: "ALL_INCLUSIVE",
            },
            {
              label: "包月维修费",
              value: "MONTH_REPAIR",
            },
          ],
          width: 100,
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isTable: true,
          formatter: (row) => divideAmount(row.totalAmount, 100),
        },
        {
          dataIndex: "derateAmount",
          title: "减免费用",
          isTable: true,
          formatter: (row) => divideAmount(row.derateAmount, 100),
        },
        {
          dataIndex: "amount",
          title: "应付金额",
          isTable: true,
          formatter: (row) => divideAmount(row.amount, 100),
        },
        {
          dataIndex: "paidAmount",
          title: "已付金额",
          isTable: true,
          formatter: (row) => divideAmount(row.paidAmount, 100),
        },
        {
          dataIndex: "status",
          title: "收款单状态",
          isTable: true,
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(600),
          formatter: (row) => row.status?.label,
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "updatedAt",
          title: "更新时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
          width: 200,
        },
        {
          dataIndex: "startYearRange",
          title: "开始年月",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
          attrs: { "value-format": "yyyy-MM" },
        },
        {
          dataIndex: "Actions",
          width: 160,
          fixed: "right",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      showReceipt: false,
      drawerTitle: "查看 - ",
      receiptDetailData: [],
      showReceiptInfo: {},
      receiptDetailColumns: [
        {
          dataIndex: "deviceGroup",
          title: "机器编号",
          formatter: (row) => row.deviceGroup.label,
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "machine",
          title: "	机型",
          isTable: true,
        },
        {
          dataIndex: "paperType",
          title: "计数方式",
          isTable: true,
        },
        {
          dataIndex: "cycle",
          title: "年月",
          isTable: true,
        },
        {
          dataIndex: "treatyType",
          title: "合约类型",
          isTable: true,
          option: [],
          formatter: (row) => row.treatyType.label,
          optionMth: () => dictTreeByCodeApi(1200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "blackWhiteInception",
          title: "初始黑白计数器",
          isTable: true,
        },
        {
          dataIndex: "blackWhiteCutoff",
          title: "截止黑白打印数",
          isTable: true,
        },
        {
          dataIndex: "blackWhitePoint",
          title: "黑白打印数量",
          isTable: true,
        },
        {
          dataIndex: "blackWhitePrice",
          title: "黑白打印单价",
          isTable: true,
        },
        {
          dataIndex: "blackWhiteAmount",
          title: "黑白打印金额",
          isTable: true,
        },
        {
          dataIndex: "colorInception",
          title: "起始彩色打印数",
          isTable: true,
        },
        {
          dataIndex: "colorCutoff",
          title: "截止彩色打印数",
          isTable: true,
        },
        {
          dataIndex: "colorPoint",
          title: "彩色打印数量",
          isTable: true,
        },
        {
          dataIndex: "colorPrice",
          title: "彩色打印单价",
          isTable: true,
        },
        {
          dataIndex: "colorAmount",
          title: "彩色打印金额",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "总金额",
          isTable: true,
        },
      ],
      expenseDialog: false,
      derateAmount: 0, // 减免费用
      customId: "",
      formLoading: false,
      form: {},
      derateForm: [
        {
          dataIndex: "derateAmount",
          title: "减免费用",
          isForm: true,
          formSpan: 12,
          valueType: "input-number",
        },
        {
          dataIndex: "remark",
          title: "备注",
          isForm: true,
          formSpan: 24,
          valueType: "input",
          inputType: "textarea",
        },
      ],
    };
  },

  watch: {},
  created() {},
  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    divideAmount,
    //加载表格
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startYearMonth: null,
          endYearMonth: null,
          data: parameter.startYearRange,
        },
      ];

      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.startYearRange;
      printReceiveListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    displayStatus(value) {
      switch (value) {
        case "601":
          return "warning";
        case "602":
          return "warning";
        case "604":
          return "success";
        case "605":
          return "warning";
        case "606":
          return "danger";
        default:
          return "primary";
      }
    },

    formSubmit(val) {
      console.log(val);
    },

    /**
     * 详情
     * @param row
     */
    handleCheck(row) {
      this.showReceipt = true;
      this.drawerTitle = `查看 - ${row.customerName}`;
      this.showReceiptInfo = row;
      const params = {
        cycle: row.cycle,
        customerId: row.customerId,
      };
      showMeterDetailApi(params)
        .then((res) => {
          this.receiptDetailData = res.data;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    confirmSub() {
      this.$confirm("是否确认减免该抄表费用?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const params = {
          ...this.form,
          id: this.customId,
        };
        receiptReduceApi(params).then((res) => {
          this.$refs.ProTable.refresh();
          this.$message.success("减免成功");
          this.form = {};
          this.expenseDialog = false;
        });
      });
    },
    /**
     * 减免费用
     * @param row
     */
    handleSub(row) {
      this.expenseDialog = true;
      this.customId = row.id;
    },
  },
};
</script>
<style lang="scss" scoped>
.expense {
  display: flex;
  justify-content: center;
  align-items: center;
  //margin: 20px 0;
}
.footer-btn {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 50px;
}
.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
</style>
