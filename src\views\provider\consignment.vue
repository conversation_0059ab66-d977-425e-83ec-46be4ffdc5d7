<!--
 * @Author: wskg
 * @Date: 2024-08-06 17:51:50
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:37:39
 * @Description: 供应商 - 发货单
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
          <el-button
            v-if="
              row.status?.value !== 'SUCCESS' && row.status?.value !== 'CLOSED'
            "
            size="mini"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'edit')"
          >
            发货
          </el-button>
          <el-button
            v-if="
              row.status?.value !== 'WAIT_DELIVERY' &&
              row.status?.value !== 'CLOSED'
            "
            icon="el-icon-warning-outline"
            @click="handleDeliveryDetail(row)"
          >
            发货详情
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="providerDrawer"
      :title="drawerTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :method-type="methodType"
      :no-footer="methodType === 'info'"
      :confirm-text="'确认发货'"
      :confirm-button-disabled="confirmButtonDisabled"
      @ok="confirmDeliverGoods"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :open-type="methodType"
      >
        <template #provinceName>
          {{ form.provinceName ?? "" }}{{ form.cityName ?? ""
          }}{{ form.countyName ?? "" }}
        </template>
        <template #shippingList>
          <div class="title-box" style="margin: 0">发货清单</div>
          <ProTable
            ref="itemsProTable"
            :row-key="(row) => row.id"
            :columns="itemsColumns"
            :data="itemsTableData"
            :local-pagination="itemsLocalPagination"
            :height="300"
            :show-setting="false"
            :show-search="false"
            :show-loading="false"
            sticky
          >
            <!--<template #batchCode="{ row }">-->
            <!--  <el-input-->
            <!--    v-model="row.batchCode"-->
            <!--    placeholder="请输入批次号"-->
            <!--    size="small"-->
            <!--    :disabled="-->
            <!--      methodType === 'info' ||-->
            <!--      row.status?.value === 'SUCCESS' ||-->
            <!--      !isOk(row)-->
            <!--    "-->
            <!--    clearable-->
            <!--  />-->
            <!--</template>-->
            <template #currNum="{ row }">
              <el-input-number
                v-model="row.currNum"
                size="small"
                :min="0"
                :disabled="
                  methodType === 'info' ||
                  row.status?.value === 'SUCCESS' ||
                  !isOk(row)
                "
                :controls="methodType === 'edit'"
                controls-position="right"
                style="width: 100%"
                placeholder="发货数量"
                @change="(e) => handleCurrNumChange(e, row)"
              ></el-input-number>
            </template>
          </ProTable>
        </template>
        <template #trackingList>
          <div class="title-box" style="margin: 0">发货记录</div>
          <ProTable
            ref="itemsProTable"
            :row-key="(row) => row.id"
            :columns="trackingColumns"
            :data="trackingTableData"
            :height="300"
            :show-setting="false"
            :show-search="false"
            :show-loading="false"
            sticky
          ></ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!--  发货详情  -->
    <ProDialog
      title="发货详情"
      :value="dialogVisible"
      :no-footer="true"
      width="60%"
      @cancel="dialogVisible = false"
    >
      <ProTable
        :data="deliveryRecordTableData"
        :columns="deliveryRecordColumns"
        :show-search="false"
        :show-setting="false"
        :show-loading="false"
        :height="400"
      ></ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getDeliveryDetailApi,
  getDeliveryRecordApi,
  pageDeliveryApi,
  submitDeliveryApi,
} from "@/api/manufacturer";
import { Message } from "element-ui";

export default {
  name: "Consignment",
  data() {
    return {
      methodType: "add",
      queryParam: {},
      columns: [
        {
          dataIndex: "code",
          title: "发货单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 170,
        },
        {
          dataIndex: "manufacturerOrderCode",
          title: "供应商采购单号",
          isTable: true,
          minWidth: 170,
        },
        {
          dataIndex: "manufacturerOrderCode",
          title: "采购单号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "receiveCompany",
          title: "所属公司",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "deliveryTime",
          title: "期望发货时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "datetimerange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          minWidth: 180,
        },
        // {
        //   dataIndex: "provinceName",
        //   title: "省",
        //   isTable: true,
        //   width: 60,
        // },
        // {
        //   dataIndex: "cityName",
        //   title: "市",
        //   isTable: true,
        //   width: 60,
        // },
        // {
        //   dataIndex: "countyName",
        //   title: "区",
        //   isTable: true,
        //   width: 60,
        // },

        {
          dataIndex: "manufacturerName",
          title: "供应商",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 170,
        },
        {
          dataIndex: "amount",
          title: "采购金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 100,
        },
        {
          dataIndex: "initiatorPhone",
          title: "电话",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "status",
          title: "发货状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          clearable: true,
          valueType: "select",
          width: 100,
          option: [
            {
              label: "已完成",
              value: "SUCCESS",
            },
            {
              label: "待发货",
              value: "WAIT_DELIVERY",
            },
            {
              label: "部分发货",
              value: "PART_DELIVERY",
            },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "address",
          title: "收货地址",
          isTable: true,
          width: 300,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          width: 220,
          fixed: "right",
          tableSlot: "actions",
        },
      ],
      tableData: [],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      providerDrawer: false,
      drawerTitle: "查看 - ",
      confirmLoading: false,
      formLoading: false,
      form: {},
      formColumns: [
        {
          dataIndex: "code",
          title: "订单号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "receiveCompany",
          title: "公司名称",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorPhone",
          title: "采购人电话",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "deliveryTime",
          title: "期待发货时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "provinceName",
          title: "省市区",
          isForm: true,
          formSpan: 6,
          formSlot: "provinceName",
          // valueType: "text",
        },
        // {
        //   dataIndex: "cityName",
        //   title: "市",
        //   isForm: true,
        //   formSpan: 6,
        //   valueType: "text",
        // },
        // {
        //   dataIndex: "countyName",
        //   title: "区",
        //   isForm: true,
        //   formSpan: 6,
        //   valueType: "text",
        // },
        {
          dataIndex: "address",
          title: "收货地址",
          isForm: true,
          formSpan: 12,
          valueType: "text",
          isWrap: true,
        },
        {
          dataIndex: "trackingNumber",
          title: "快递单号",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入快递单号",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "trackingType",
          title: "物流类型",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入物流类型",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "shippingList",
          title: "发货清单",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "shippingList",
        },
        {
          dataIndex: "trackingList",
          title: "发货记录",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "trackingList",
        },
      ],
      itemsColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
        // {
        //   dataIndex: "batchCode",
        //   title: "批次号",
        //   isTable: true,
        //   width: 200,
        //   tableSlot: "batchCode",
        // },
        {
          dataIndex: "number",
          title: "采购数量",
          isTable: true,
        },
        {
          dataIndex: "deliveryNum",
          title: "已发数量",
          isTable: true,
        },
        {
          dataIndex: "cancelNum",
          title: "已退数量",
          isTable: true,
        },
        {
          dataIndex: "status",
          title: "发货状态",
          isTable: true,
          formatter: (row) => row.status?.label,
        },
        {
          dataIndex: "currNum",
          title: "发货数量",
          isTable: true,
          width: 140,
          tableSlot: "currNum",
        },
        // {
        //   dataIndex: "packageNum",
        //   title: "包裹数量",
        //   isTable: true,
        //   width: 120,
        //   tableSlot: "packageNum",
        // },
      ],
      itemsTableData: [],
      itemsLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      numberTableData: [],
      dialogVisible: false,
      dialogForm: {},
      deliveryDetail: {},
      isEdit: false,
      confirmButtonDisabled: false,
      deliveryRecordTableData: [], // 发货详情
      deliveryRecordColumns: [
        // {
        //   dataIndex: "code",
        //   title: "发货单编号",
        //   isTable: true,
        //   width: 180,
        // },
        {
          dataIndex: "trackingNumber",
          title: "快递单号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "trackingType",
          title: "配送方式",
          isTable: true,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => row.status?.label,
        },
        {
          dataIndex: "number",
          title: "发货数量",
          isTable: true,
        },
        {
          dataIndex: "packageNum",
          title: "包裹数量",
          isTable: true,
        },
        {
          dataIndex: "receiveNum",
          title: "已收数量",
          isTable: true,
        },

        {
          dataIndex: "createdAt",
          title: "发货时间",
          isTable: true,
          width: 150,
        },
      ],
      trackingColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          minWidth: 170,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
        },
        {
          dataIndex: "trackingNumber",
          title: "快递单号",
          isTable: true,
        },
        {
          dataIndex: "trackingType",
          title: "物流类型",
          isTable: true,
        },
        {
          dataIndex: "createdAt",
          title: "发货时间",
          isTable: true,
          width: 160,
        },
        {
          dataIndex: "number",
          title: "发货数量",
          isTable: true,
        },
      ],
      trackingTableData: [],
    };
  },
  watch: {
    $route() {
      if (this.$route.query.code) {
        this.queryParam.manufacturerOrderCode = this.$route.query.code;
        this.refresh();
      }
    },
  },
  created() {
    this.queryParam.manufacturerOrderCode = this.$route.query.code;
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          deliveryTimeStart: null,
          deliveryTimeEnd: null,
          data: parameter.deliveryTime,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.deliveryTime;
      pageDeliveryApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    // 查看发货单
    getDeliveryDetail(id) {
      getDeliveryDetailApi(id).then((res) => {
        this.form = res.data;
        this.itemsTableData = this.form.manufacturerDeliveryGoodsList.map(
          (item) => {
            return {
              ...item,
              packageNum: 1, // 包裹数量默认为1
            };
          }
        );
        this.trackingTableData = this.form.manufacturerDeliveryRecords || [];
      });
    },
    // 查看 / 发货
    handleEdit(row, type) {
      this.drawerTitle =
        type === "info"
          ? `详情 - ${row.purchaseCode}`
          : type === "edit"
          ? `发货 - ${row.purchaseCode}`
          : `发货详情 - ${row.purchaseCode}`;
      this.form = {};
      this.methodType = type;
      const isInfoType = type === "info";

      // ["batchCode", "currNum", "packageNum"] 2025.2.11修改取消批次号,
      this.updateColumnsVisibility(
        isInfoType,
        this.itemsColumns,
        ["currNum", "packageNum"],
        !isInfoType
      );
      this.updateColumnsVisibility(
        isInfoType,
        this.formColumns,
        ["trackingNumber", "trackingType"],
        !isInfoType
      );

      this.deliveryDetail = cloneDeep(row);
      this.getDeliveryDetail(row.id);
      this.providerDrawer = true;
    },
    updateColumnsVisibility(type, columns, dataIndexes, isVisible) {
      columns.forEach((item) => {
        if (dataIndexes.includes(item.dataIndex)) {
          item.isTable = isVisible;
          item.isForm = isVisible;
        }
      });
    },
    handleDeliveryDetail(row) {
      this.dialogVisible = true;
      // const params = {
      // pageSize: 99,
      // pageNumber: 1,
      // deliveryCode: row.code,
      // };
      // pageReceiveApi(params).then((res) => {
      //   this.deliveryRecordTableData = res.data.rows;
      // });
      getDeliveryRecordApi(row.code).then((res) => {
        this.deliveryRecordTableData = res.data;
      });
    },
    // drawer确认发货
    confirmDeliverGoods() {
      this.confirmButtonDisabled = true;

      this.$refs.ProForm.handleSubmit()
        .then((res) => {
          this.$confirm("请确认发货批次号及发货数量", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            const data = this.itemsTableData.filter(
              (item) =>
                item.currNum !== undefined &&
                item.currNum !== null &&
                item.currNum !== "" &&
                item.currNum !== 0 &&
                item.status?.value !== "SUCCESS"
            );
            if (!data.length) {
              Message.error("请输入发货数量");
              return;
            }
            const params = {
              id: this.form.id,
              trackingNumber: this.form.trackingNumber,
              trackingType: this.form.trackingType,
              deliveryDetails: data,
            };
            submitDeliveryApi(params).then((res) => {
              Message.success("发货成功");
              this.getDeliveryDetail(this.deliveryDetail.id);
              this.providerDrawer = false;
              this.refresh();
            });
          });
        })
        .finally(() => {
          this.confirmButtonDisabled = false;
        });
    },
    isOk(row) {
      const num = row.num || 0;
      const deliveryNum = row.deliveryNum || 0;
      const cancelNum = row.cancelNum || 0;
      return num - deliveryNum - cancelNum <= 0;
    },
    handleCurrNumChange(e, row) {
      const { number, deliveryNum, cancelNum, currNum } = row;
      if (currNum > number - deliveryNum - cancelNum) {
        Message.error("发货数量不能大于采购数量");
        this.$nextTick(() => {
          row.currNum = number - deliveryNum - cancelNum;
        });
      }
    },
    handleLicenseImgUploadSuccess(result, row) {
      row.voucherImg.push(result);
    },
    handleLicenseImgUploadRemove(file, row) {
      const index = row.voucherImg.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      row.voucherImg.splice(index, 1);
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    closeDrawer() {
      this.deliveryDetail = {};
      this.providerDrawer = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
