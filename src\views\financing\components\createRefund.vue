<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-29 17:42:14
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 16:07:25
 * @Description: 线下支付填写退款金额
 -->
<template>
  <ProDialog
    :value="visibleDialog"
    title="新增退款记录"
    width="500px"
    :confirm-btn-loading="confirmLoading"
    confirm-text="确认退款登记"
    @ok="handleDialogOk"
    @cancel="handleCancelDialog"
  >
    <ProForm
      ref="ProForm"
      :form-param="form"
      :form-list="formColumns"
      :confirm-loading="confirmLoading"
      :layout="{ formWidth: '100%', labelWidth: '100px' }"
      @proSubmit="formSubmit"
    ></ProForm>
  </ProDialog>
</template>

<script>
import { addRefundApi } from "@/api/pay";

export default {
  name: "CreateRefund",
  data() {
    return {
      visibleDialog: false,
      form: {},
      formColumns: [
        {
          dataIndex: "payAmount",
          title: "支付金额",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "icbcRefundSerialNumber",
          title: "流水号",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
        },
        {
          dataIndex: "amount",
          title: "退款金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入退款金额",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "createdAt",
          title: "退款时间",
          isForm: true,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请选择退款时间",
              trigger: "change",
            },
          ],
        },
      ],
      confirmLoading: false,
    };
  },
  methods: {
    show(id, amount) {
      this.form.payOrderId = id;
      this.form.payAmount = amount;
      this.visibleDialog = true;
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    formSubmit(val) {
      this.$confirm("此操作将新增线下退款记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const args = {
          ...val,
          payOrderId: this.form.payOrderId,
        };
        this.confirmLoading = true;
        addRefundApi(args)
          .then((res) => {
            this.$message.success("操作成功");
            this.$emit("refresh");
            this.handleCancelDialog();
          })
          .finally(() => {
            this.confirmLoading = false;
          });
      });
    },
    handleCancelDialog() {
      this.visibleDialog = false;
      this.$nextTick(() => {
        this.form = {};
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
