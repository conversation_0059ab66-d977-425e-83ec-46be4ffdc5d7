<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 设备详情对话框组件
-->
<template>
  <el-dialog
    title="设备详情"
    :visible.sync="dialogVisible"
    width="900px"
    :before-close="handleClose"
    :modal="false"
  >
    <div v-if="device" class="device-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备ID" :span="2">
            {{ device.deviceId || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="品牌">
            {{ device.brand || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="型号">
            {{ device.model || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="安卓版本">
            {{ device.manufacturer || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="系统类型">
            <el-tag :type="getOsTypeColor(device.osType)">
              {{ device.osType || '未知' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="系统版本">
            {{ device.osVersion || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="SDK版本">
            {{ device.sdkVersion || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="设备类型">
            <el-tag :type="device.isEmulator ? 'warning' : 'success'">
              {{ device.isEmulator ? '模拟器' : '真机' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Root状态">
            <el-tag :type="device.isRooted ? 'danger' : 'success'">
              {{ device.isRooted ? '已Root' : '未Root' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 硬件信息 -->
      <div class="detail-section">
        <h3 class="section-title">硬件信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="屏幕分辨率">
            {{ device.screenResolution || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="屏幕密度">
            {{ formatScreenDensity(device.screenDensity) }}
          </el-descriptions-item>
          <el-descriptions-item label="CPU架构">
            {{ device.cpuAbi || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="总内存">
            {{ formatMemorySize(device.totalMemory) }}
          </el-descriptions-item>
          <el-descriptions-item label="可用存储" :span="2">
            {{ formatStorageSize(device.availableStorage) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 网络和地区信息 -->
      <div class="detail-section">
        <h3 class="section-title">网络和地区</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="网络类型">
            {{ device.networkType || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="系统语言">
            {{ device.language || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="时区">
            {{ device.timeZone || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户">
            {{ device.userName || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户编码">
            {{ device.userCode || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="应用版本" :span="2">
            {{ device.appVersion || '未知' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <!-- 配置信息 -->
      <div class="detail-section">
        <h3 class="section-title">权限/配置信息</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="配置版本">
            {{ device.currentConfigVersion || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="配置详情">
            <div class="config-details">
              <pre>{{ formatConfigDetails(device.currentConfigDetails) }}</pre>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="权限详情">
            <div class="permissions-info">
              <pre>{{ formatPermissions(device.permissionsInfo) }}</pre>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 统计信息 -->
      <div class="detail-section">
        <h3 class="section-title">统计信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="收集次数">
            {{ device.collectCount || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="首次收集时间">
            {{ device.firstCollectTime || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后更新时间" :span="2">
            {{ device.lastUpdateTime || '未知' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleViewLogs">查看日志</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'DeviceDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    device: {
      type: Object,
      default: null
    }
  },

  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
    },
    
    handleViewLogs() {
      if (this.device && this.device.deviceId) {
        // 跳转到日志查看页面，并传递设备ID作为过滤条件
        this.$router.push({
          name: 'LogControlManagement',
          query: {
            tab: 'logAnalysis',
            deviceId: this.device.deviceId
          }
        })
        this.handleClose()
      }
    },
    
    formatPermissions(permissionsInfo) {
      if (typeof permissionsInfo === 'string') {
        try {
          return JSON.stringify(JSON.parse(permissionsInfo), null, 2)
        } catch (error) {
          return permissionsInfo
        }
      } else if (typeof permissionsInfo === 'object') {
        return JSON.stringify(permissionsInfo, null, 2)
      }
      return permissionsInfo
    },

    // 获取系统类型颜色
    getOsTypeColor(osType) {
      const colorMap = {
        'Android': 'success',
        'iOS': 'primary',
        'HarmonyOS': 'warning'
      }
      return colorMap[osType] || 'info'
    },

    // 格式化屏幕密度
    formatScreenDensity(density) {
      if (!density) return '未知'
      if (typeof density === 'number') {
        return `${density}x`
      }
      return density
    },

    // 格式化内存大小
    formatMemorySize(bytes) {
      if (!bytes) return '未知'
      const size = parseInt(bytes)
      if (size >= 1024 * 1024 * 1024) {
        return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`
      } else if (size >= 1024 * 1024) {
        return `${(size / (1024 * 1024)).toFixed(2)} MB`
      }
      return `${size} B`
    },

    // 格式化存储大小
    formatStorageSize(bytes) {
      if (!bytes) return '未知'
      const size = parseInt(bytes)
      if (size >= 1024 * 1024 * 1024 * 1024) {
        return `${(size / (1024 * 1024 * 1024 * 1024)).toFixed(2)} TB`
      } else if (size >= 1024 * 1024 * 1024) {
        return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`
      } else if (size >= 1024 * 1024) {
        return `${(size / (1024 * 1024)).toFixed(2)} MB`
      }
      return `${size} B`
    },

    // 格式化配置详情
    formatConfigDetails(configDetails) {
      if (typeof configDetails === 'string') {
        try {
          return JSON.stringify(JSON.parse(configDetails), null, 2)
        } catch (error) {
          return configDetails
        }
      } else if (typeof configDetails === 'object') {
        return JSON.stringify(configDetails, null, 2)
      }
      return configDetails
    }
  }
}
</script>

<style lang="scss" scoped>
.device-detail {
  .detail-section {
    margin-bottom: 24px;
    
    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 8px;
    }
    
    .permissions-info,
    .config-details {
      background-color: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 16px;
      margin-top: 8px;

      pre {
        margin: 0;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.5;
        color: #606266;
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 200px;
        overflow-y: auto;
      }
    }

    // 权限信息和配置详情在描述列表中的统一样式
    .el-descriptions-item__content .permissions-info,
    .el-descriptions-item__content .config-details {
      margin-top: 0;
      border: none;
      background-color: transparent;
      padding: 0;

      pre {
        background-color: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 12px;
        max-height: 200px;
        overflow-y: auto;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
