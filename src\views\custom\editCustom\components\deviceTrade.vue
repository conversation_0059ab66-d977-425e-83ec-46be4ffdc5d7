<!--
 * @Description: 换机
 * @version: 
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-18 13:45:06
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-02-20 15:38:55
-->
<template>
  <ProDrawer
    :value="modelValue"
    title="换机"
    size="80%"
    :no-footer="false"
    :destroy-on-close="true"
    :no-confirm-footer="true"
    @cancel="cancel"
  >
    <el-divider content-position="left">当前设备信息</el-divider>
    <trade-form
      v-if="defaultData.deviceSeqId"
      v-model="defaultData"
      :column="formList"
    ></trade-form>
    <el-divider content-position="left">换机信息</el-divider>
    <trade-form
      v-if="defaultData.deviceSeqId"
      v-model="changeDevice"
      :column="tradeForm"
    ></trade-form>
    <el-divider content-position="left">配件</el-divider>
    <ProTable
      ref="ProTable"
      :show-search="false"
      :show-table-operator="false"
      :show-loading="false"
      row-key="id"
      :data="defaultData.deviceAccessories"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :height="300"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            icon="el-icon-refresh"
            @click="changeDeviceAccessories(row)"
          >
            更换
          </el-button>
        </div>
      </template>
    </ProTable>
    <choose-device
      v-model="dialogVisible"
      :host-type="actDeviceAccessories.hostType?.value"
      @confirmDispatch="setDevice"
    ></choose-device>
    <template #footer>
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="cancel">取消</el-button>
    </template>
  </ProDrawer>
</template>
<script>
import { cloneDeep } from "lodash";
import TradeForm from "./tradeForm.vue";
import { roleMemberApi } from "@/api/user";
import ChooseDevice from "./chooseDevices.vue";
import {
  getCustomerAddressApi,
  getDeviceGroupDetailApi,
  changeDeviceApi,
} from "@/api/customer";
export default {
  name: "DeviceTrade",
  components: { TradeForm, ChooseDevice },
  model: {
    prop: "modelValue",
    event: "update:modelValue",
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dialogVisible: false,
      actDeviceAccessories: {
        hostType: {},
      },
      defaultData: {},
      changeDevice: {},
      formList: [
        {
          label: "设备组名称",
          key: "deviceGroup",
          formatter: (row) => row.deviceGroup?.label,
        },
        {
          label: "品牌机型",
          key: "productInfo",
        },
        {
          label: "机器编号",
          key: "oriMachineNum",
        },
        {
          label: "服务类型",
          key: "serType",
          formatter: (row) => row.serType?.label,
        },
        {
          label: "设备类型",
          key: "deviceOn",
          formatter: (row) => row.deviceOn?.label,
        },
        {
          label: "负责工程师",
          key: "operatName",
        },
        {
          label: "供电电压",
          key: "supplyVoltage",
        },
        {
          label: "计数方式",
          key: "paperType",
        },

        {
          label: "黑白计数器",
          key: "adjustBlackWhite",
        },
        {
          label: "彩色计数器",
          key: "adjustColor",
        },
        {
          label: "五色计数器",
          key: "fiveColourCounter",
        },
        {
          label: "地址",
          key: "address",
          formType: "select",
          options: () => {
            return [
              { label: "选项1", value: 1 },
              { label: "选项2", value: 2 },
            ];
          },
        },
        {
          label: "联系人",
          key: "contact",
          formatter: (row) => {
            if (row.address) {
              try {
                const addressObj = JSON.parse(row.address);
                return addressObj.contact;
              } catch (e) {
                return row.contact || "";
              }
            } else {
              return "";
            }
          },
        },
        {
          label: "联系电话",
          key: "phone",
          formatter: (row) => {
            if (row.address) {
              try {
                const addressObj = JSON.parse(row.address);
                return addressObj.phone;
              } catch (e) {
                return row.phone || "";
              }
            } else {
              return "";
            }
          },
        },
        {
          label: "设备组照片",
          key: "deviceGroupImg",
          formatter: (row) => row.deviceGroupImg?.url,
          formType: "image",
          span: 24,
        },
      ],
      tradeForm: [
        {
          label: "期望换机时间",
          key: "expectInstallTime",
          formType: "date",
        },
        {
          label: "更换机型编号",
          key: "machineNum",
          formType: "devicePicker",
        },
        {
          label: "品牌机型",
          key: "productName",
        },
        {
          label: "服务类型",
          formatter: (row) => row.serType?.label,
          key: "serType",
        },
        {
          label: "安装工程师",
          key: "engineerId",
          formType: "select",
          options: () => {
            return [
              { label: "选项1", value: 1 },
              { label: "选项2", value: 2 },
            ];
          },
        },
        {
          label: "供电电压",
          key: "electric",
        },
        {
          label: "计数方式",
          key: "paperType",
        },
        {
          label: "黑白计数器",
          key: "blackWhiteCounter",
        },
        {
          label: "彩色计数器",
          key: "colorCounter",
        },
        {
          label: "五色计数器",
          key: "fiveColourCounter2",
        },
        {
          label: "运费",
          key: "freight",
          formType: "number",
        },
        {
          label: "安装费",
          key: "installAmount",
          formType: "number",
        },
        {
          label: "是否包邮",
          key: "freeShipping",
          formType: "checkbox",
        },
        {
          label: "注意事项",
          key: "precautions",
          formType: "textarea",
          span: 12,
        },
      ],
      machineDetail: {},
      tableData: [],
      columns: [
        {
          dataIndex: "accessoryCode",
          title: "机器编号",
          isTable: true,
        },
        {
          dataIndex: "originCode",
          title: "原机器编号",
          isTable: true,
        },
        {
          dataIndex: "hostType",
          title: "选配件类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
        },
        {
          dataIndex: "modeType",
          title: "配件型号",
          isTable: true,
        },
        {
          dataIndex: "actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 100,
        },
      ],
      queryParam: {},
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
    };
  },
  computed: {},
  watch: {
    modelValue: {
      handler(val) {
        if (!val) return;
        // this.defaultData = cloneDeep(this.info);

        this.getMechineDetail();
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.getWorkers();
  },

  methods: {
    getMechineDetail() {
      getDeviceGroupDetailApi(this.info.id).then((res) => {
        console.log("detail", res);
        const obj = res.data;
        obj.oriMachineNum = cloneDeep(this.info.machineNum);
        obj.address = "";
        obj.consigneePhone = "";
        obj.consignee = "";
        obj.addressId = "";
        delete obj.machineNum;
        this.defaultData = obj;
        this.changeDevice = {
          paperType: obj.paperType,
          serType: obj.serType,
          freight: 0,
          installAmount: 0,
          freeShipping: false,
          precautions: "",
          expectInstallTime: "",
        };
      });
      //用户收货地址查询
      getCustomerAddressApi(this.info.customerId).then((res) => {
        if (this.formList.length > 0) {
          const idx = this.formList.findIndex((item) => item.label === "地址");
          const address = res.data.map((o) => {
            return {
              label: o.address,
              value: JSON.stringify(o),
            };
          });
          this.formList[idx].options = () => address;
        }
      });
    },
    getWorkers() {
      roleMemberApi("1002", { pageNumber: 1, pageSize: 10000 }).then((res) => {
        const workerList = res.data.rows.map((item) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
        this.tradeForm[4].options = () => workerList;
      });
    },
    cancel() {
      this.$emit("update:modelValue", false);
    },
    submitForm() {
      const address = this.defaultData.address;
      if (address) {
        try {
          const addressObj = JSON.parse(address);
          this.defaultData.consignee = addressObj.contact;
          this.defaultData.consigneePhone = addressObj.phone;
          this.defaultData.addressId = addressObj.id;
          delete this.defaultData.address;
        } catch (e) {
          this.defaultData.contact = "";
          this.defaultData.phone = "";
          this.defaultData.addressId = null;
        }
      }
      //配件更换信息
      let exchangeInfos = this.defaultData.deviceAccessories.filter(
        (o) => o.originCode
      );
      if (exchangeInfos.length > 0) {
        exchangeInfos = exchangeInfos.map((o) => {
          return {
            machineCode: o.originCode,
            newMachineCode: o.accessoryCode,
            hostType: o.hostType?.value,
            deviceGroupId: o.deviceGroupId,
            productName: o.modeType,
            id: o.id,
          };
        });
      }
      const paramsObj = {
        deviceGroupId: this.defaultData.deviceGroup.value,
        addressId: this.defaultData.addressId,
        customerId: this.defaultData.customerId,
        paperType: this.defaultData.paperType,
        serType: this.defaultData.serType.value,
        expectInstallTime: this.changeDevice.expectInstallTime,
        machineNum: this.changeDevice.machineNum,
        oriMachineNum: this.defaultData.oriMachineNum,
        engineerId: this.changeDevice.engineerId,
        freeShipping: this.changeDevice.freeShipping,
        installAmount: this.changeDevice.installAmount,
        freight: this.changeDevice.freeShipping
          ? 0
          : this.changeDevice.freights
          ? this.changeDevice.freights
          : 0,
        type: "EXCHANGE",
        exchangeInfos: exchangeInfos,
      };
      changeDeviceApi(paramsObj).then((res) => {
        if (res.code == 200) {
          this.$message.success("换机申请提交成功！");
          this.$emit("update:modelValue", false);
        }
      });
    },
    changeDeviceAccessories(row) {
      this.actDeviceAccessories = row;
      this.dialogVisible = true;
    },
    setDevice(data) {
      if (data.length < 1) {
        return;
      }
      const obj = data[0];
      const index = this.defaultData.deviceAccessories.findIndex(
        (o) => o.id == this.actDeviceAccessories.id
      );
      if (!this.actDeviceAccessories.originCode) {
        this.actDeviceAccessories.originCode = cloneDeep(
          this.actDeviceAccessories.accessoryCode
        );
      }
      this.actDeviceAccessories.accessoryCode = obj.machineNum;
      this.actDeviceAccessories.modeType = obj.productName;
      this.defaultData.deviceAccessories[index] = this.actDeviceAccessories;
    },
  },
};
</script>
<style lang="scss" scoped></style>
