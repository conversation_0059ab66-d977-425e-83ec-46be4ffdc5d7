<!--
 * @Author: wskg
 * @Date: 2024-08-28 14:11:05
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:49:32
 * @Description: 工程师领料
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #actions="slotProps">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleInfo(slotProps.row)">
            查看
          </el-button>
          <el-button
            v-if="
              !(
                slotProps?.row?.status === 'DONE' ||
                slotProps?.row?.status === 'CANCEL'
              )
            "
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleCancel(slotProps.row)"
          >
            取消
          </el-button>
          <!-- <el-popconfirm
            v-if="
              !(
                slotProps?.row?.status === 'DONE' ||
                slotProps?.row?.status === 'CANCEL'
              )
            "
            title="确定要整单取消该领料单吗？"
            @confirm="handleCancel(slotProps.row)"
          >
            <el-button
              slot="reference"
              type="btn3"
              size="mini"
              icon="el-icon-edit-outline"
            >
              取消
            </el-button>
          </el-popconfirm> -->
        </div>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="80%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
        <template #status>
          {{
            form.status == "CREATE"
              ? "提交申请"
              : form.status == "WAIT_EX_WAREHOUSE"
              ? "待出库"
              : form.status == "REFUSE_EX_WAREHOUSE"
              ? "拒绝出库"
              : form.status == "PARTIAL_DONE"
              ? "部分完成"
              : form.status == "CANCEL"
              ? "已取消"
              : form.status == "DONE"
              ? "完成"
              : "/"
          }}</template
        >
        <template #location>
          <DataTable
            :columns="columns1"
            :show-setting="false"
            :show-pagination="false"
            :show-search="false"
            row-key="id"
            :data="form.applyDetailList"
            sticky
            :height="350"
          >
            <template #saleAttrVals="slotProps">
              <div
                v-for="attr in slotProps.row?.skuInfo?.saleAttrVals"
                :key="attr.val"
              >
                {{ attr.name }}:{{ attr.val }}
              </div>
            </template>
            <template #action="{ row }">
              <div class="fixed-width">
                <el-button
                  v-if="!(row?.status === 'DONE' || row?.status === 'CANCEL')"
                  type="btn3"
                  icon="el-icon-warning-outline"
                  @click="handleReturn(row)"
                >
                  取消</el-button
                >
              </div>
            </template>
          </DataTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!--    -->
    <ProDialog
      :value="showDialogReturn"
      title=""
      width="500px"
      :no-footer="false"
      :confirm-loading="dialogLoading"
      top="50px"
      @ok="handleDialogCancelConfirm"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="editFormCheck"
        :form-param="cancelReceiveForm"
        :form-list="cancelColumns"
        :confirm-loading="dialogLoading"
        :layout="{ formWidth: '100%', labelWidth: '150px' }"
        :open-type="'add'"
      >
        <template #returnNum>
          <el-input-number
            v-model="cancelReceiveForm.cancelNum"
            controls-position="right"
            :min="1"
            :max="returnMaxNum"
          ></el-input-number>
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>
<script>
import {
  pcListPageApi,
  detailInfoApi,
  cancelDetailApi,
  cancelOrderApi,
} from "@/api/repair";

import { isEmpty, cloneDeep } from "lodash";

export default {
  name: "APIManage",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: { keyword: "" },
      columns: [
        {
          dataIndex: "code",
          title: "领料单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },

        {
          dataIndex: "engineerName",
          title: "工程师姓名",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "engineerMobile",
          title: "工程师手机号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },

        {
          dataIndex: "createdAt",
          title: "发起时间",
          isTable: true,
        },

        {
          dataIndex: "status",
          title: "领料状态",
          isTable: true,
          formatter: (row) => {
            switch (row.status) {
              case "CREATE":
                return "提交申请";
              case "WAIT_EX_WAREHOUSE":
                return "待出库";
              case "REFUSE_EX_WAREHOUSE":
                return "拒绝出库";
              case "PARTIAL_DONE":
                return "部分完成";
              case "CANCEL":
                return "已取消";
              case "DONE":
                return "完成";
            }
          },
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "待出库",
              value: "WAIT_EX_WAREHOUSE",
            },
            {
              label: "部分完成",
              value: "PARTIAL_DONE",
            },
            {
              label: "取消",
              value: "CANCEL",
            },
            {
              label: "完成",
              value: "DONE",
            },
          ],
        },

        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 180,
        },
      ],
      columns1: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
        },
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku规格",
          isTable: true,
          tableSlot: "saleAttrVals",
        },
        {
          dataIndex: "num",
          title: "申请数量",
          isTable: true,
        },
        {
          dataIndex: "receivedNum",
          title: "已领数量",
          isTable: true,
        },
        {
          dataIndex: "cancelNum",
          title: "取消数量",
          isTable: true,
        },
        // {
        //   dataIndex: "returnNum",
        //   title: "已退数量",
        //   isTable: true,
        // },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
        },
      ],
      //新增
      methodType: "add",
      confirmLoading: false,
      form: {},
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      formcolumns: [
        {
          isForm: true,
          dataIndex: "code",
          title: "领料单编号",
          valueType: "text",
          formSpan: 8,
        },

        {
          dataIndex: "engineerName",
          title: "工程师姓名",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "engineerMobile",
          title: "工程师手机号",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "createdAt",
          title: "发起时间",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "status",
          title: "领料状态",
          isForm: true,
          valueType: "text",
          formSlot: "status",
          formSpan: 8,
        },

        {
          dataIndex: "location",
          title: "领料明细",
          formSlot: "location",
          isForm: true,
          formSpan: 24,
        },
      ],
      showDialogReturn: false,
      dialogLoading: false,
      cancelReceiveForm: {},
      cancelColumns: [
        {
          dataIndex: "num",
          title: "申请数量",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "receivedNum",
          title: "已领数量",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        // {
        //   dataIndex: "returnNum",
        //   title: "已退数量",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 24,
        // },
        {
          dataIndex: "cancelNum",
          isForm: true,
          title: "取消数量",
          formSlot: "returnNum",
          // valueType: "input-number",
          // min: 1,
          // step: 1,
          // clearable: true,
          // formSpan: 24,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入取消数量",
          //     trigger: "change",
          //   },
          // ],
        },
      ],
      returnMaxNum: 99,
    };
  },

  computed: {},

  watch: {},
  mounted() {
    if (this.$route.query.id) {
      this.queryParam.keyword = this.$route.query.id;
    }
    this.$refs.ProTable.refresh();
  },
  methods: {
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      pcListPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;

          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "领料单详情";
      this.resetFrom();
      detailInfoApi(row.id).then((res) => {
        this.form = cloneDeep(res.data);
      });
      this.form.engineerMobile = row.engineerMobile;
      this.form.engineerName = row.engineerName;
      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    /**
     * 退料
     * @param row
     */
    handleReturn(row) {
      this.returnMaxNum = row.num - row.receivedNum - row.cancelNum;
      this.cancelReceiveForm = {};
      this.cancelReceiveForm = cloneDeep(row);
      this.showDialogReturn = true;
    },
    /**
     * 领料单详情取消数量
     */
    handleDialogCancelConfirm() {
      console.log("ok");
      console.log(this.cancelReceiveForm);
      this.returnMaxNum = 99;
      const params = {
        id: this.cancelReceiveForm.id,
        cancelNum: this.cancelReceiveForm.cancelNum,
      };
      cancelDetailApi(params).then((res) => {
        this.$message.success("操作成功");
        this.cancelReceiveForm = {};
        this.handleInfo(this.form);
        this.showDialogReturn = false;
      });
    },
    /**
     * 整单取消数量
     */
    handleCancel(row) {
      this.$confirm("此操作将整单取消领料申请, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        cancelOrderApi({ id: row.id }).then((res) => {
          this.$message.success("操作成功");
          this.$refs.ProTable.refresh();
        });
      });
    },
    handleDialogCancel() {
      this.returnMaxNum = 99;
      this.showDialogReturn = false;
    },
    proSubmit() {},
  },
};
</script>
<style lang="scss" scoped></style>
