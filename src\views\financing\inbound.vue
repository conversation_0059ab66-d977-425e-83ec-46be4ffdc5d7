<!--
 * @Author: wskg
 * @Date: 2025-03-19 14:06:27
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 17:54:18
 * @Description: 采购单明细
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:finance:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>销售含税总额：{{ totalData?.totalAmount || 0 }}</div>
          <div>销售不含税总额：{{ totalData?.totalNoTaxAmount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import {
  purchaseInboundDetailExportApi,
  purchaseInboundDetailListApi,
  purchaseInboundDetailStatisticsApi,
} from "@/api/finance";
import { handleExcelExport } from "@/utils/exportExcel";
import { filterParam, filterParamRange } from "@/utils";

export default {
  name: "InBound",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      columns: [
        {
          dataIndex: "createDate",
          title: "入库日期",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 100,
        },
        {
          dataIndex: "code",
          title: "入库单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "payOrderCode",
          title: "付款单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "type",
          title: "类型",
          isTable: true,
          formatter: (row) => (row.type == 1 ? "采购入库" : "采购退库"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "采购入库",
              value: 1,
            },
            {
              label: "采购退库",
              value: 0,
            },
          ],
          width: 80,
        },
        {
          dataIndex: "manufacturerCode",
          title: "供应商编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },

        {
          dataIndex: "articleCode",
          title: "物品编码",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
          minWidth: 100,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "num",
          title: "数量",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
          minWidth: 100,
        },

        {
          dataIndex: "amount",
          title: "采购金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "noTaxAmount",
          title: "不含税金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "taxAmount",
          title: "税额",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "tax",
          title: "税率(%)",
          isTable: true,
          formatter: (row) => (row.tax ? row.tax + "%" : ""),
          minWidth: 80,
        },
        // {
        //   dataIndex: "status",
        //   title: "审核状态",
        //   isTable: true,
        //   formatter: (row) => row.status?.label,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [
        //     {
        //       label: "待审核",
        //       value: "WAIT_AUDIT",
        //     },
        //     {
        //       label: "已审核",
        //       value: "APPROVE",
        //     },
        //   ],
        //   minWidth: 100,
        // },
        {
          dataIndex: "remarks",
          title: "备注",
          isTable: true,
          minWidth: 200,
        },
        {
          dataIndex: "payOrderAmount",
          title: "付款单总金额",
          isTable: true,
          minWidth: 120,
        },
        // {
        //   dataIndex: "invoiceStatus",
        //   title: "开票状态",
        //   isTable: true,
        //   formatter: (row) => (row.invoiceStatus === 1 ? "已开票" : "未开票"),
        //   minWidth: 80,
        // },
      ],
      requestParameters: {},
      exportLoading: false,
      totalData: {},
      statLoading: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const paramsRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createDate,
        },
      ];
      filterParamRange(this, this.queryParam, paramsRange);
      this.requestParameters = cloneDeep(this.queryParam);
      delete this.requestParameters.createDate;
      purchaseInboundDetailListApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      purchaseInboundDetailStatisticsApi(this.requestParameters).then((res) => {
        this.totalData = res.data;
        this.statLoading = true;
      });
    },
    handleExport() {
      this.$confirm("此操作将导出耗材入库明细, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          purchaseInboundDetailExportApi,
          this.requestParameters,
          "耗材入库明细",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
