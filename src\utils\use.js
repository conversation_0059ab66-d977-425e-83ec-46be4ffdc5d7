/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-03 17:00:20
 * @Description: 全局组件注册
 */

import Vue from "vue";
import ProTable from "@/components/ProTable/index.vue";
import ProDialog from "@/components/ProDialog/index.vue";
import ProDrawer from "@/components/ProDrawer/index.vue";
import ProForm from "@/components/ProForm/index.vue";
import DataTable from "@/components/ProTable/data-index.vue";
Vue.component("ProTable", ProTable);
Vue.component("ProDialog", ProDialog);
Vue.component("ProDrawer", ProDrawer);
Vue.component("ProForm", ProForm);
Vue.component("DataTable", DataTable);
