<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-07-18 16:34:28
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-18 16:44:06
 * @Description: 
 -->
<template>
  <!-- Card容器 - 对应React的Card组件 -->
  <el-card class="w-full" :body-style="{ padding: 0 }">
    <!-- 未保存更改横幅 -->
    <div
      v-if="hasUnsavedChanges"
      class="bg-orange-50 border-b border-orange-200 px-4 py-2"
    >
      <span class="text-orange-600">
        <el-badge dot /> 有未保存的更改，请记得保存配置
      </span>
    </div>

    <!-- Header 预览区域 -->
    <div class="header-preview">
      <div class="header-content">
        <!-- Logo 与标题 -->
        <div class="header-left">
          <div
            v-if="config?.headerLogoUrl"
            class="logo-container"
            @click="openLogoEditor('header')"
          >
            <img
              :src="config.headerLogoUrl"
              alt="logo"
              class="logo-image"
              @error="handleImageError"
            />
          </div>
          <div
            v-else
            class="logo-placeholder"
            @click="openLogoEditor('header')"
          >
            {{ getHeaderIconTitle().charAt(0).toUpperCase() }}
          </div>
          <h1 class="site-title">
            <EditableConfigField
              :value="config?.siteTitle || ''"
              label="网站标题"
              type="text"
              placeholder="请输入网站标题"
              :required="true"
              :max-length="50"
              class-name="title-field"
              :is-changed="changedFields.has('siteTitle')"
              @change="(value) => handleConfigChange('siteTitle', value)"
            />
          </h1>
        </div>

        <!-- 导航链接 -->
        <div class="header-nav">
          <span v-for="menu in menus" :key="menu.path" class="nav-item">
            {{ menu.label }}
          </span>
        </div>
      </div>
    </div>

    <!-- Footer 预览区域 -->
    <div class="footer-preview">
      <!-- 主要内容区 -->
      <div class="footer-content">
        <div class="footer-grid">
          <!-- 公司信息 -->
          <div class="lg:col-span-2">
            <div class="space-y-4 flex flex-col h-full">
              <!-- Logo 和公司名称 -->
              <div class="flex items-center space-x-4">
                <div
                  v-if="config?.footerLogoUrl"
                  class="w-10 h-10 cursor-pointer"
                  @click="openLogoEditor('footer')"
                >
                  <img
                    :src="config.footerLogoUrl"
                    alt="logo"
                    class="w-10 h-10 object-contain"
                    @error="handleImageError"
                  />
                </div>
                <div
                  v-else
                  class="w-10 h-10 bg-blue-600 rounded-lg flex-shrink-0 flex items-center justify-center cursor-pointer"
                  @click="openLogoEditor('footer')"
                >
                  <span class="text-white font-bold text-lg">
                    {{ getCompanyName().charAt(0).toUpperCase() }}
                  </span>
                </div>
                <div>
                  <h3 class="text-white font-bold text-lg">
                    <EditableConfigField
                      :value="config?.companyName || ''"
                      label="公司名称"
                      type="text"
                      placeholder="请输入公司名称"
                      :required="true"
                      :max-length="100"
                      class-name="text-white inline-block"
                      :is-changed="changedFields.has('companyName')"
                      @change="
                        (value) => handleConfigChange('companyName', value)
                      "
                    />
                  </h3>
                </div>
              </div>

              <!-- 公司描述 -->
              <p class="text-sm text-gray-400 leading-relaxed flex-grow">
                <EditableConfigField
                  :value="config?.companyProfile || ''"
                  label="公司简介"
                  type="textarea"
                  placeholder="请输入公司简介"
                  :max-length="500"
                  class-name="text-gray-400"
                  :is-changed="changedFields.has('companyProfile')"
                  @change="
                    (value) => handleConfigChange('companyProfile', value)
                  "
                />
              </p>
            </div>
          </div>

          <!-- 快速链接 -->
          <div>
            <div class="space-y-4 text-left">
              <h4 class="text-white font-semibold text-base">快速链接</h4>
              <ul class="space-y-2 pl-0 list-none">
                <li v-if="menuLoading" class="text-gray-400 text-sm">
                  加载中...
                </li>
                <li v-for="menu in menus" v-else :key="menu.key || menu.path">
                  <span
                    class="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer"
                  >
                    {{ menu.label }}
                  </span>
                </li>
              </ul>
            </div>
          </div>

          <!-- 服务项目 -->
          <div>
            <div class="space-y-4">
              <h4
                class="text-white font-semibold text-base text-left"
                style="padding-left: 0.5rem"
              >
                服务项目
              </h4>
              <div class="text-sm text-gray-400 leading-relaxed text-left">
                <!-- 服务项目列表显示 -->
                <div
                  v-if="!isEditingBusinessType"
                  :class="[
                    'service-items-display',
                    { 'field-changed': changedFields.has('businessType') },
                  ]"
                  @click="startEditBusinessType"
                >
                  <div v-if="config?.businessType" class="service-items-list">
                    <div
                      v-for="(item, index) in getServiceItems(
                        config.businessType
                      )"
                      :key="index"
                      class="service-item"
                    >
                      {{ item }}
                    </div>
                  </div>
                  <span v-else class="field-placeholder">点击编辑服务项目</span>
                  <i
                    v-if="changedFields.has('businessType')"
                    class="el-icon-edit-outline field-changed-icon"
                  ></i>
                </div>

                <!-- 编辑模式 -->
                <el-dialog
                  title="编辑业务类型"
                  :visible.sync="isEditingBusinessType"
                  width="500px"
                  :before-close="handleCancelBusinessType"
                  append-to-body
                >
                  <div class="edit-form">
                    <el-form
                      ref="businessTypeForm"
                      :model="businessTypeForm"
                      label-width="80px"
                    >
                      <el-form-item label="业务类型" prop="value">
                        <el-input
                          ref="businessTypeInput"
                          v-model="businessTypeForm.value"
                          type="textarea"
                          :rows="3"
                          placeholder="请输入业务类型，多个项目用逗号分隔"
                          :maxlength="300"
                          show-word-limit
                        />
                      </el-form-item>
                      <div class="field-description">
                        提示：多个服务项目请用逗号分隔，显示时会自动换行且不显示逗号
                      </div>
                    </el-form>
                  </div>
                  <div slot="footer" class="dialog-footer">
                    <el-button @click="handleCancelBusinessType"
                      >取消</el-button
                    >
                    <el-button type="primary" @click="handleSaveBusinessType"
                      >保存</el-button
                    >
                  </div>
                </el-dialog>
              </div>
            </div>
          </div>

          <!-- 联系方式 -->
          <div class="lg:col-span-2">
            <div class="space-y-4">
              <h4 class="text-white font-semibold text-base text-left">
                联系我们
              </h4>
              <div class="space-y-3">
                <!-- 联系电话 -->
                <div class="flex items-center space-x-3">
                  <i class="el-icon-phone text-blue-400"></i>
                  <EditableConfigField
                    :value="config?.contactPhone || ''"
                    label="联系电话"
                    type="phone"
                    placeholder="请输入联系电话"
                    :pattern="/^(\d{3,4}-?\d{7,8}|\d{11})$/"
                    pattern-message="请输入正确的电话号码格式"
                    class-name="text-gray-300 text-sm"
                    :is-changed="changedFields.has('contactPhone')"
                    @change="
                      (value) => handleConfigChange('contactPhone', value)
                    "
                  />
                </div>

                <!-- 客服电话 -->
                <div class="flex items-center space-x-3">
                  <i class="el-icon-phone text-blue-400"></i>
                  <EditableConfigField
                    :value="config?.servicePhone || ''"
                    label="客服电话"
                    type="phone"
                    placeholder="请输入客服电话"
                    :pattern="/^(\d{3,4}-?\d{7,8}|\d{11})$/"
                    pattern-message="请输入正确的电话号码格式"
                    class-name="text-gray-300 text-sm"
                    :is-changed="changedFields.has('servicePhone')"
                    @change="
                      (value) => handleConfigChange('servicePhone', value)
                    "
                  />
                </div>

                <!-- 邮箱 -->
                <div class="flex items-center space-x-3">
                  <i class="el-icon-message text-blue-400"></i>
                  <EditableConfigField
                    :value="config?.contactEmail || ''"
                    label="联系邮箱"
                    type="email"
                    placeholder="请输入联系邮箱"
                    class-name="text-gray-300 text-sm"
                    :is-changed="changedFields.has('contactEmail')"
                    @change="
                      (value) => handleConfigChange('contactEmail', value)
                    "
                  />
                </div>

                <!-- 地址 -->
                <div class="flex items-start space-x-3">
                  <i class="el-icon-location text-blue-400 mt-1"></i>
                  <div class="text-sm leading-relaxed">
                    <EditableConfigField
                      :value="config?.companyAddress || ''"
                      label="公司地址"
                      type="textarea"
                      placeholder="请输入公司地址"
                      :max-length="200"
                      class-name="text-gray-300"
                      :is-changed="changedFields.has('companyAddress')"
                      @change="
                        (value) => handleConfigChange('companyAddress', value)
                      "
                    />
                  </div>
                </div>

                <!-- 营业时间 -->
                <div class="flex items-center space-x-3">
                  <i class="el-icon-time text-blue-400"></i>
                  <EditableConfigField
                    :value="config?.businessHours || ''"
                    label="营业时间"
                    type="text"
                    placeholder="请输入营业时间"
                    :max-length="100"
                    class-name="text-gray-300 text-sm"
                    :is-changed="changedFields.has('businessHours')"
                    @change="
                      (value) => handleConfigChange('businessHours', value)
                    "
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 社交媒体 -->
          <div
            v-if="
              config?.wechatNumber?.trim() ||
              config?.qqNumber?.trim() ||
              config?.wechatIcon?.trim() ||
              config?.qqIcon?.trim() ||
              config?.wechatQrCode?.trim() ||
              config?.qqQrCode?.trim()
            "
          >
            <div class="space-y-4">
              <h4 class="text-white font-semibold text-base text-center">
                社交媒体
              </h4>
              <div class="space-y-4">
                <!-- 微信 -->
                <div
                  v-if="
                    config?.wechatNumber?.trim() ||
                    config?.wechatIcon?.trim() ||
                    config?.wechatQrCode?.trim()
                  "
                  class="flex flex-col items-center space-y-1"
                >
                  <!-- 图标和联系方式 -->
                  <div
                    v-if="
                      config?.wechatIcon?.trim() || config?.wechatNumber?.trim()
                    "
                    class="flex items-center space-x-3 w-fit"
                  >
                    <!-- 只在有自定义图标时显示图标容器 -->
                    <div
                      v-if="config?.wechatIcon?.trim()"
                      class="w-6 h-6 flex items-center justify-center hover:opacity-80 transition-opacity flex-shrink-0"
                      title="社交媒体联系方式"
                    >
                      <img
                        v-if="
                          config.wechatIcon.startsWith('http://') ||
                          config.wechatIcon.startsWith('https://')
                        "
                        :src="config.wechatIcon.trim()"
                        alt="社交媒体图标"
                        class="w-6 h-6 object-contain"
                        @error="handleImageError"
                      />
                      <span v-else class="text-lg leading-none">{{
                        config.wechatIcon.trim()
                      }}</span>
                    </div>
                    <span
                      v-if="config?.wechatNumber?.trim()"
                      class="text-sm text-gray-300"
                      >{{ config.wechatNumber.trim() }}</span
                    >
                  </div>

                  <!-- 二维码直接显示 -->
                  <div
                    v-if="config?.wechatQrCode?.trim()"
                    class="flex justify-center w-full"
                  >
                    <img
                      :src="config.wechatQrCode.trim()"
                      alt="社交媒体二维码"
                      class="w-24 h-24 object-contain rounded border border-gray-600 cursor-pointer hover:opacity-80 transition-opacity"
                      @error="handleImageError"
                      @click="
                        handleImagePreview(
                          config.wechatQrCode.trim(),
                          '社交媒体二维码'
                        )
                      "
                    />
                  </div>

                  <!-- 编辑区域，使用SocialMediaEditor -->
                  <div class="mt-2">
                    <SocialMediaEditor
                      type="wechat"
                      :number="config?.wechatNumber || ''"
                      :icon="config?.wechatIcon || ''"
                      :qr-code="config?.wechatQrCode || ''"
                      :is-changed="
                        changedFields.has('wechatNumber') ||
                        changedFields.has('wechatIcon') ||
                        changedFields.has('wechatQrCode')
                      "
                      class-name="text-xs text-blue-400 hover:text-blue-300 underline cursor-pointer bg-transparent border-none p-0"
                      @change="handleConfigChange"
                    />
                  </div>
                </div>

                <!-- QQ -->
                <div
                  v-if="
                    config?.qqNumber?.trim() ||
                    config?.qqIcon?.trim() ||
                    config?.qqQrCode?.trim()
                  "
                  class="flex flex-col items-center space-y-1"
                >
                  <!-- 图标和联系方式 -->
                  <div
                    v-if="config?.qqIcon?.trim() || config?.qqNumber?.trim()"
                    class="flex items-center space-x-3 w-fit"
                  >
                    <!-- 只在有自定义图标时显示图标容器 -->
                    <div
                      v-if="config?.qqIcon?.trim()"
                      class="w-6 h-6 flex items-center justify-center hover:opacity-80 transition-opacity flex-shrink-0"
                      title="社交媒体联系方式"
                    >
                      <img
                        v-if="
                          config.qqIcon.startsWith('http://') ||
                          config.qqIcon.startsWith('https://')
                        "
                        :src="config.qqIcon.trim()"
                        alt="社交媒体图标"
                        class="w-6 h-6 object-contain"
                        @error="handleImageError"
                      />
                      <span v-else class="text-lg leading-none">{{
                        config.qqIcon.trim()
                      }}</span>
                    </div>
                    <span
                      v-if="config?.qqNumber?.trim()"
                      class="text-sm text-gray-300"
                      >{{ config.qqNumber.trim() }}</span
                    >
                  </div>

                  <!-- 二维码直接显示 -->
                  <div
                    v-if="config?.qqQrCode?.trim()"
                    class="flex justify-center w-full"
                  >
                    <img
                      :src="config.qqQrCode.trim()"
                      alt="社交媒体二维码"
                      class="w-24 h-24 object-contain rounded border border-gray-600 cursor-pointer hover:opacity-80 transition-opacity"
                      @error="handleImageError"
                      @click="
                        handleImagePreview(
                          config.qqQrCode.trim(),
                          '社交媒体二维码'
                        )
                      "
                    />
                  </div>

                  <!-- 编辑区域，使用SocialMediaEditor -->
                  <div class="mt-2">
                    <SocialMediaEditor
                      type="qq"
                      :number="config?.qqNumber || ''"
                      :icon="config?.qqIcon || ''"
                      :qr-code="config?.qqQrCode || ''"
                      :is-changed="
                        changedFields.has('qqNumber') ||
                        changedFields.has('qqIcon') ||
                        changedFields.has('qqQrCode')
                      "
                      class-name="text-xs text-blue-400 hover:text-blue-300 underline cursor-pointer bg-transparent border-none p-0"
                      @change="handleConfigChange"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 服务理念与核心优势 -->
          <div>
            <div class="space-y-4">
              <h4 class="text-white font-semibold text-base">服务理念</h4>
              <div class="text-sm text-gray-400 leading-relaxed">
                <EditableConfigField
                  :value="config?.servicePhilosophy || ''"
                  label="服务理念"
                  type="textarea"
                  placeholder="请输入服务理念"
                  :max-length="300"
                  class-name="text-gray-400"
                  :is-changed="changedFields.has('servicePhilosophy')"
                  @change="
                    (value) => handleConfigChange('servicePhilosophy', value)
                  "
                />
              </div>
            </div>
            <div class="space-y-4 mt-8">
              <h4 class="text-white font-semibold text-base">核心优势</h4>
              <div class="text-sm text-gray-400 leading-relaxed">
                <EditableConfigField
                  :value="config?.coreAdvantages || ''"
                  label="核心优势"
                  type="textarea"
                  placeholder="请输入核心优势"
                  :max-length="200"
                  class-name="text-gray-400"
                  :is-changed="changedFields.has('coreAdvantages')"
                  @change="
                    (value) => handleConfigChange('coreAdvantages', value)
                  "
                />
              </div>
            </div>
          </div>

          <!-- 系统配置 -->
          <div>
            <div class="space-y-4">
              <h4 class="text-white font-semibold text-base">系统配置</h4>

              <!-- 腾讯地图配置 -->
              <div class="space-y-2">
                <label class="text-xs text-gray-500">腾讯地图API Key</label>
                <MaskedConfigField
                  :value="config?.tencentMapKey || ''"
                  label="腾讯地图API Key"
                  placeholder="请输入腾讯地图API Key"
                  :max-length="100"
                  class-name="text-gray-300 text-sm block w-full"
                  :is-changed="changedFields.has('tencentMapKey')"
                  @change="
                    (value) => handleConfigChange('tencentMapKey', value)
                  "
                />
                <p class="text-xs text-gray-500 mt-1">
                  用于地图组件显示，申请地址:
                  <a
                    href="https://lbs.qq.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="text-blue-400 hover:text-blue-300"
                    >https://lbs.qq.com/</a
                  >
                </p>
              </div>

              <!-- 域名配置 -->
              <div class="space-y-2 mt-4">
                <label class="text-xs text-gray-500">网站域名</label>
                <EditableConfigField
                  :value="config?.websiteDomain || ''"
                  label="网站域名"
                  type="text"
                  placeholder="请输入网站域名，如：https://example.com"
                  :max-length="200"
                  class-name="text-gray-300 text-sm block w-full"
                  :is-changed="changedFields.has('websiteDomain')"
                  @change="
                    (value) => handleConfigChange('websiteDomain', value)
                  "
                />
                <p class="text-xs text-gray-500 mt-1">
                  用于预览功能，配置前端展示网站的域名地址
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-gray-700 my-0" style="height: 1px"></div>

      <!-- 底部版权信息 -->
      <div class="copyright-section">
        <div class="copyright-content">
          <div class="copyright-links">
            <EditableConfigField
              :value="
                config?.copyrightNotice ||
                `© ${new Date().getFullYear()} ${getCompanyName()}`
              "
              label="版权信息"
              type="text"
              placeholder="请输入版权信息"
              :max-length="100"
              class-name="text-gray-400 text-sm"
              :is-changed="changedFields.has('copyrightNotice')"
              @change="(value) => handleConfigChange('copyrightNotice', value)"
            />
            <span class="text-gray-600">|</span>
            <LinkEditableField
              :text="config?.icpNumber || ''"
              :link="config?.icpLink || ''"
              text-key="icpNumber"
              link-key="icpLink"
              :is-changed="
                changedFields.has('icpNumber') || changedFields.has('icpLink')
              "
              class-name="text-gray-400 text-sm"
              @change="handleConfigChange"
            />
            <span class="text-gray-600">|</span>
            <LinkEditableField
              :text="config?.policeNumber || ''"
              :link="config?.policeLink || ''"
              text-key="policeNumber"
              link-key="policeLink"
              :is-changed="
                changedFields.has('policeNumber') ||
                changedFields.has('policeLink')
              "
              class-name="text-gray-400 text-sm"
              @change="handleConfigChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Logo编辑模态框 -->
    <el-dialog
      :title="
        logoEditor === 'header' ? '编辑头部 Logo URL' : '编辑底部 Logo URL'
      "
      :visible.sync="logoEditorVisible"
      width="500px"
      append-to-body
    >
      <el-input
        v-model="logoTempUrl"
        placeholder="请输入图片链接 (支持 http(s)://)"
      />
      <div v-if="logoTempUrl" class="mt-4">
        <img
          :src="logoTempUrl"
          alt="预览"
          class="max-h-24"
          @error="handleImageError"
        />
      </div>
      <div slot="footer">
        <el-button @click="logoEditorVisible = false">取消</el-button>
        <el-button type="primary" @click="saveLogoUrl">保存</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import EditableConfigField from "./EditableConfigField.vue";
import MaskedConfigField from "./MaskedConfigField.vue";
import SocialMediaEditor from "./SocialMediaEditor.vue";
import LinkEditableField from "./LinkEditableField.vue";
import { CONFIG_FIELD_INFO } from "@/websites/config/websiteConfig";
import { websitePublicApi } from "@/websites/api/website";

export default {
  name: "VisualConfigPreview",
  components: {
    EditableConfigField,
    MaskedConfigField,
    SocialMediaEditor,
    LinkEditableField,
  },
  props: {
    config: {
      type: Object,
      default: () => ({}),
    },
    hasUnsavedChanges: {
      type: Boolean,
      default: false,
    },
    changedFields: {
      type: Set,
      default: () => new Set(),
    },
  },
  data() {
    return {
      CONFIG_FIELD_INFO,
      logoEditorVisible: false,
      logoEditor: "header", // 'header' | 'footer'
      logoTempUrl: "",
      isEditingBusinessType: false,
      businessTypeForm: {
        value: "",
      },
      menus: [],
      menuLoading: false,
    };
  },
  watch: {
    config: {
      handler() {
        // 配置变化时的处理逻辑
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.loadMenus();
  },
  methods: {
    // 加载菜单数据
    async loadMenus() {
      this.menuLoading = true;
      try {
        const response = await websitePublicApi.getMenus();

        // 处理API响应数据结构
        let menuData = [];
        if (response && response.data) {
          menuData = response.data;
        } else if (Array.isArray(response)) {
          menuData = response;
        }

        // 转换菜单数据格式，确保包含必要的字段
        this.menus = menuData.map((item) => ({
          key: item.key || item.id,
          label: item.label || item.name,
          path: item.path || "",
        }));
      } catch (error) {
        // 使用默认菜单作为后备
        this.menus = [
          { key: "ABOUT_PAGE", label: "关于我们", path: "/about" },
          { key: "SERVICE_PAGE", label: "服务项目", path: "/services" },
          { key: "CASE_PAGE", label: "成功案例", path: "/cases" },
          { key: "CONTACT_PAGE", label: "联系我们", path: "/contact" },
        ];
      } finally {
        this.menuLoading = false;
      }
    },

    handleConfigChange(key, value) {
      this.$emit("configChange", key, value);
    },

    getHeaderIconTitle() {
      return this.config?.siteTitle || this.config?.companyName || "Logo";
    },

    getCompanyName() {
      return this.config?.companyName || "公司名称";
    },

    openLogoEditor(type) {
      this.logoEditor = type;
      this.logoTempUrl =
        type === "header"
          ? this.config?.headerLogoUrl || ""
          : this.config?.footerLogoUrl || "";
      this.logoEditorVisible = true;
    },

    saveLogoUrl() {
      const key =
        this.logoEditor === "header" ? "headerLogoUrl" : "footerLogoUrl";
      this.handleConfigChange(key, this.logoTempUrl.trim());
      this.logoEditorVisible = false;
    },

    handleImageError(e) {
      e.target.style.display = "none";
    },

    // 图片预览功能
    handleImagePreview(imageUrl, title) {
      if (!imageUrl) return;

      // 使用Element UI的MessageBox显示图片预览
      this.$msgbox({
        title: title || "图片预览",
        message: `<div style="text-align: center;">
          <img src="${imageUrl}" alt="${title}" style="max-width: 100%; max-height: 400px; object-fit: contain;" />
        </div>`,
        dangerouslyUseHTMLString: true,
        showCancelButton: false,
        confirmButtonText: "关闭",
        customClass: "image-preview-dialog",
      }).catch(() => {
        // 用户点击关闭按钮或按ESC键
      });
    },

    // 业务类型相关方法
    getServiceItems(businessType) {
      if (!businessType) return [];
      return businessType
        .split(",")
        .map((item) => item.trim())
        .filter((item) => item);
    },

    startEditBusinessType() {
      this.businessTypeForm.value = this.config?.businessType || "";
      this.isEditingBusinessType = true;
      this.$nextTick(() => {
        if (this.$refs.businessTypeInput) {
          this.$refs.businessTypeInput.focus();
        }
      });
    },

    handleCancelBusinessType() {
      this.isEditingBusinessType = false;
      this.businessTypeForm.value = "";
    },

    handleSaveBusinessType() {
      const value = this.businessTypeForm.value.trim();
      this.handleConfigChange("businessType", value);
      this.isEditingBusinessType = false;
      this.businessTypeForm.value = "";
    },
  },
};
</script>

<style lang="scss" scoped>
// 保持与Tailwind CSS相同的视觉效果，但使用普通CSS
.w-full {
  width: 100%;
}

// 未保存更改横幅
.bg-orange-50 {
  background-color: #fff7ed;
}

.border-orange-200 {
  border-color: #fed7aa;
}

.text-orange-600 {
  color: #ea580c;
}

.border-b {
  border-bottom-width: 1px;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

// Header 预览区域样式
.header-preview {
  background-color: #ffffff;
  color: #1f2937;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid #e5e7eb;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media (min-width: 768px) {
  .header-content {
    padding: 1rem 1rem;
  }
}

@media (min-width: 1024px) {
  .header-content {
    padding: 1rem 1.5rem;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-container {
  width: 2rem;
  height: 2rem;
  cursor: pointer;
}

.logo-image {
  width: 2rem;
  height: 2rem;
  object-fit: contain;
}

.logo-placeholder {
  width: 2rem;
  height: 2rem;
  background-color: #2563eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: 700;
  cursor: pointer;
  border-radius: 4px;
}

.site-title {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 700;
  margin: 0;
  cursor: pointer;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  font-size: 0.875rem;
}

.nav-item {
  cursor: pointer;
  transition: color 150ms ease;
  color: #4b5563;
}

.nav-item:hover {
  color: #2563eb;
}

// Footer 预览区域样式
.footer-preview {
  background-color: #111827;
  color: #d1d5db;
  margin: 0;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 0.1rem;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .footer-content {
    padding: 2.5rem 0.2rem;
  }

  .footer-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .footer-content {
    padding: 3rem 0.3rem;
  }

  .footer-grid {
    grid-template-columns: repeat(8, 1fr);
    gap: 1rem;
  }

  .lg\:col-span-2 {
    grid-column: span 2;
  }
}

// 通用样式
.bg-orange-50 {
  background-color: #fff7ed;
}
.border-orange-200 {
  border-color: #fed7aa;
}
.text-orange-600 {
  color: #ea580c;
}
.border-b {
  border-bottom: 1px solid;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.text-gray-400 {
  color: #9ca3af;
}
.cursor-pointer {
  cursor: pointer;
}
.w-full {
  width: 100%;
}
.max-h-24 {
  max-height: 6rem;
}

// 保持所有其他Tailwind CSS类的样式效果
.space-x-3 > * + * {
  margin-left: 0.75rem;
}
.space-x-4 > * + * {
  margin-left: 1rem;
}
.space-x-6 > * + * {
  margin-left: 1.5rem;
}
.space-y-2 > * + * {
  margin-top: 0.5rem;
}
.space-y-3 > * + * {
  margin-top: 0.75rem;
}
.space-y-4 > * + * {
  margin-top: 1rem;
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.items-start {
  align-items: flex-start;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.flex-col {
  flex-direction: column;
}
.flex-grow {
  flex-grow: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.flex-1 {
  flex: 1 1 0%;
}
.grid {
  display: grid;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.gap-6 {
  gap: 1.5rem;
}
.gap-2 {
  gap: 0.5rem;
}
.text-white {
  color: #ffffff;
}
.text-gray-300 {
  color: #d1d5db;
}
.text-gray-500 {
  color: #6b7280;
}
.text-gray-600 {
  color: #4b5563;
}
.text-blue-400 {
  color: #60a5fa;
}
.text-blue-300 {
  color: #93c5fd;
}
.text-green-400 {
  color: #4ade80;
}
.bg-white {
  background-color: #ffffff;
}
.bg-blue-600 {
  background-color: #2563eb;
}
.bg-gray-900 {
  background-color: #111827;
}
.bg-gray-700 {
  background-color: #374151;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-semibold {
  font-weight: 600;
}
.font-medium {
  font-weight: 500;
}
.leading-relaxed {
  line-height: 1.625;
}
.object-contain {
  object-fit: contain;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded {
  border-radius: 0.25rem;
}
.border-t {
  border-top: 1px solid;
}
.border {
  border: 1px solid;
}
.border-gray-700 {
  border-color: #374151;
}
.border-gray-600 {
  border-color: #4b5563;
}
.w-6 {
  width: 1.5rem;
}
.h-6 {
  height: 1.5rem;
}
.w-8 {
  width: 2rem;
}
.h-8 {
  height: 2rem;
}
.w-10 {
  width: 2.5rem;
}
.h-10 {
  height: 2.5rem;
}
.w-16 {
  width: 4rem;
}
.h-16 {
  height: 4rem;
}
.w-20 {
  width: 5rem;
}
.h-20 {
  height: 5rem;
}
.w-24 {
  width: 6rem;
}
.h-24 {
  height: 6rem;
}
.w-28 {
  width: 7rem;
}
.h-28 {
  height: 7rem;
}
.w-32 {
  width: 8rem;
}
.h-32 {
  height: 8rem;
}
.ml-9 {
  margin-left: 2.25rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pl-0 {
  padding-left: 0;
}
.list-none {
  list-style-type: none;
}
.text-left {
  text-align: left;
}
.inline {
  display: inline;
}
.inline-block {
  display: inline-block;
}
.block {
  display: block;
}
.hidden {
  display: none;
}
.transition-colors {
  transition: color 150ms ease;
}
.hover\:text-blue-600:hover {
  color: #2563eb;
}
.hover\:text-white:hover {
  color: #ffffff;
}
.hover\:text-blue-300:hover {
  color: #93c5fd;
}

// 版权区域样式
.copyright-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem 0.5rem;
}

.copyright-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.copyright-links {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #9ca3af;
  flex-wrap: wrap;
  justify-content: center;
}

.copyright-text {
  cursor: pointer;
  color: #9ca3af;

  &:hover {
    color: #d1d5db;
  }
}

.copyright-placeholder {
  cursor: pointer;
  color: #6b7280;

  &:hover {
    color: #9ca3af;
  }
}

@media (min-width: 768px) {
  .copyright-section {
    padding: 1.5rem 1rem;
  }

  .copyright-content {
    flex-direction: row;
  }

  .copyright-links {
    justify-content: center;
  }
}

// 服务项目显示样式
.service-items-display {
  position: relative;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  min-height: 20px;
  display: block;

  &:hover {
    background-color: rgba(64, 158, 255, 0.1);
    border: 1px dashed #409eff;
  }

  &.field-changed {
    background-color: rgba(245, 166, 35, 0.1);
    border: 1px solid #f5a623;

    .field-changed-icon {
      position: absolute;
      top: 4px;
      right: 4px;
      color: #f5a623;
      font-size: 12px;
    }
  }

  .service-items-list {
    .service-item {
      margin-bottom: 4px;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .field-placeholder {
    color: #c0c4cc;
    font-style: italic;
  }
}

.field-description {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

@media (min-width: 1024px) {
  .copyright-section {
    padding: 1.5rem 1.5rem;
  }
}

// 图片预览弹窗样式
:global(.image-preview-dialog) {
  .el-message-box {
    max-width: 90vw;
    max-height: 90vh;
  }

  .el-message-box__content {
    padding: 20px;
  }

  .el-message-box__message {
    margin: 0;
  }
}

@media (min-width: 768px) {
  .md\:flex {
    display: flex;
  }
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\:flex-row {
    flex-direction: row;
  }
  .header-nav {
    display: flex;
  }
}
</style>
