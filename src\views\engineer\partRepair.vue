<!--
 * @Author: wskg
 * @Date: 2025-02-13 10:22:44
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-08 09:55:03
 * @Description: 毛机维修
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :height="500"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #imageFiles="{ row }">
        <el-image
          v-if="row.imageFiles && row.imageFiles.length > 0"
          style="max-width: 100px; max-height: 100px"
          :src="getMachinePicsImg(row)"
          :preview-src-list="[getMachinePicsImg(row)]"
        ></el-image>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <el-button
            v-if="row.status?.value === 'CONFIRM_REPORT'"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'edit')"
          >
            审核
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 查看、审核 -->
    <ProDrawer
      :value="drawerVisible"
      :no-footer="editType === 'info'"
      :title="drawerTitle"
      :confirm-text="'确认维修报告'"
      :confirm-button-disabled="confirmLoading"
      :no-confirm-footer="true"
      size="70%"
      @cancel="drawerCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParam"
        :form-list="formColumns"
        :open-type="editType"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
      >
        <template #picUrls>
          <div v-if="formParam.picUrls && formParam.picUrls?.length > 0">
            <el-image
              v-for="item in formParam.picUrls"
              :key="item.url"
              style="width: 100px; height: 100px"
              :src="item.url"
              :preview-src-list="[item.url]"
            >
            </el-image>
          </div>
        </template>
        <template #componentRepairReplaces>
          <div class="title-box">更换耗材零件清单</div>
          <ProTable
            :show-loading="false"
            :show-search="false"
            :show-pagination="false"
            :show-setting="false"
            :columns="replaceColumns"
            :data="formParam.componentRepairReplaces"
          >
            <template #picUrl="slotProps">
              <el-image
                :preview-src-list="[slotProps.row?.skuInfo?.picUrl[0]?.url]"
                style="width: 100px; height: 100px"
                :src="slotProps.row?.skuInfo?.picUrl[0]?.url"
              ></el-image>
            </template>

            <template #saleAttrVals="slotProps">
              <div
                v-for="attr in slotProps.row?.skuInfo?.saleAttrVals"
                :key="attr.val"
              >
                {{ attr.name }}:{{ attr.val }}
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
      <!-- 驳回、确认维修报告、取消 -->
      <template #footer>
        <div v-if="editType === 'edit'" class="footer">
          <el-button type="danger" @click="handleAudit(false)">
            驳回
          </el-button>
          <el-button type="primary" @click="handleAudit(true)">
            审核通过
          </el-button>
          <el-button plain @click="drawerCancel">取消</el-button>
        </div>
      </template>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import {
  componentsRepairAuditApi,
  componentsRepairDetailApi,
  componentsRepairListApi,
} from "@/api/repair";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "PartRepair",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "code",
          title: "维修单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "imageFiles",
          title: "物品图片",
          isTable: true,
          tableSlot: "imageFiles",
          width: 120,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isTable: true,
          minWidth: 150,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造商渠道",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.manufacturerChannel?.label,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },
        // {
        //   dataIndex: "partBrand",
        //   title: "品牌",
        //   isTable: true,
        // },
        {
          dataIndex: "engineerName",
          title: "维修经办人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "auditName",
          title: "审核人",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "维修中",
              value: "REPAIRING",
            },
            {
              label: "提交维修报告",
              value: "CONFIRM_REPORT",
            },
            {
              label: "完成",
              value: "COMPLETED",
            },
          ],
          minWidth: 100,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "completedAt",
          title: "报告提交时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          tooltip: false,
          fixed: "right",
          width: 140,
        },
      ],
      tableData: [],
      // drawer
      editType: "info",
      confirmLoading: false,
      drawerVisible: false,
      drawerTitle: "",
      formParam: {},
      formColumns: [
        {
          dataIndex: "code",
          title: "维修单号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        // {
        //   dataIndex: "productName",
        //   title: "机器型号",
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 6,
        // },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "isStore",
          title: "是否入库",
          isForm: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请选择是否入库",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "amount",
          title: "价格",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 6,
          prop: [
            {
              required: true,
              message: "请输入价格",
              trigger: "change",
            },
            {
              validator(rule, value, callback) {
                if (value < 0) {
                  callback("价格不能小于0");
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "description",
          title: "问题描述",
          isForm: true,
          valueType: "text",
          isWrap: true,
          formSpan: 24,
        },
        {
          dataIndex: "picUrls",
          title: "维修图片",
          isForm: true,
          formSlot: "picUrls",
          formSpan: 24,
        },
        {
          dataIndex: "componentRepairReplaces",
          title: "更换零件清单",
          isForm: true,
          formOtherSlot: "componentRepairReplaces",
          formSpan: 24,
        },
      ],
      // 更换零件清单
      replaceColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          minWidth: 140,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          formatter: (row) => row.manufacturerChannel?.label,
          minWidth: 100,
        },

        {
          dataIndex: "picUrl",
          title: "物品图片",
          isTable: true,
          tableSlot: "picUrl",
          minWidth: 200,
        },
        {
          dataIndex: "saleUnitPrice",
          title: "单价",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "costPrice",
          title: "成本价",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "num",
          title: "更换数量",
          minWidth: 80,
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "location",
          title: "更换位置",
          minWidth: 140,
          isTable: true,
          formatter: (row) =>
            Array.isArray(row.location) ? row.location.join("、") : "",
        },
      ],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      componentsRepairListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.editType = type;
      this.formParam = {};
      this.drawerTitle = `${row.articleName} - 维修报告`;
      componentsRepairDetailApi(row.id).then((res) => {
        this.formParam = res.data;
        this.drawerVisible = true;
      });
    },
    handleAudit(isPass) {
      this.$refs.ProForm.handleSubmit().then((val) => {
        const confirmText = isPass ? "通过" : "驳回";
        this.$confirm(`此操作将${confirmText}该维修报告, 是否继续?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          const args = {
            id: this.formParam.id,
            isStore: this.formParam.isStore,
            amount: this.formParam.amount,
            isPass: isPass,
          };
          componentsRepairAuditApi(args).then((res) => {
            this.$message.success("操作成功");
            this.drawerVisible = false;
            this.refresh();
          });
        });
      });
    },
    drawerCancel() {
      this.drawerVisible = false;
    },
    getMachinePicsImg(row) {
      return row?.imageFiles?.[0]?.url;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
