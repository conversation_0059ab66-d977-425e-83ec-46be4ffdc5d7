<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-09-19 18:44:25
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-03 17:04:22
 * @FilePath: src/components/InputRange/index.vue
 * @Description: 
 * 
-->
<template>
  <div class="numerical-range-container" style="width: 100%">
    <div class="input-range" :class="{ focused: isFocused }">
      <div class="content">
        <el-input
          v-model="localStartValue"
          :placeholder="$props.startPlaceholder"
          :disabled="disabled"
          :size="size"
          @input="updateValues"
          @focus="handleFocus"
          @blur="handleBlur"
        />
        <span :class="{ disBgc: disabled }">-</span>
        <el-input
          v-model="localEndValue"
          :placeholder="$props.endPlaceholder"
          :disabled="disabled"
          :size="size"
          @input="updateValues"
          @focus="handleFocus"
          @blur="handleBlur"
        />
      </div>
      <div v-if="!disabled" class="close-icon">
        <i
          class="el-icon-circle-close"
          :class="{ close: isEmpty }"
          @click="resetValues"
        ></i>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "InputRange",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    startPlaceholder: {
      type: String,
      default: "起始印量",
    },
    endPlaceholder: {
      type: String,
      default: "截止印量",
    },
    size: {
      type: String,
      default: "medium",
    },
  },
  data() {
    return {
      localStartValue: null,
      localEndValue: null,
      isFocused: false,
    };
  },
  computed: {
    isEmpty() {
      return !!this.localStartValue || !!this.localEndValue;
    },
  },
  watch: {
    value: {
      handler(newValue) {
        if (newValue && Array.isArray(newValue)) {
          this.localStartValue = newValue[0];
          this.localEndValue = newValue[1];
        }
      },
      immediate: true,
    },
  },
  methods: {
    updateValues() {
      this.$emit("update:values", [this.localStartValue, this.localEndValue]);
    },
    handleFocus() {
      this.isFocused = true;
    },
    handleBlur() {
      const isValidNumber = /^-?\d+(\.\d+)?$/;
      if (!isValidNumber.test(this.localStartValue)) {
        this.localStartValue = null;
      }
      if (!isValidNumber.test(this.localEndValue)) {
        this.localEndValue = null;
      }
      this.$emit("update:values", [this.localStartValue, this.localEndValue]);
      this.isFocused = false;
    },
    resetValues() {
      this.localStartValue = null;
      this.localEndValue = null;
      this.$emit("update:values", [this.localStartValue, this.localEndValue]);
    },
  },
};
</script>

<style lang="scss"></style>
