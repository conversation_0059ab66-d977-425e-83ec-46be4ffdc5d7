<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:55
 * @Description: 
 -->
<template>
  <div class="edit-staff">
    <div class="headBox">
      <div class="seqId">客户编号： {{ seqId }}</div>
      <div class="shopTitle">店铺名称： {{ shopName }}</div>
    </div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :height="600"
      show-index
      show-loading
      :show-search="false"
      show-pagination
      :reserve-selection="true"
      @loadData="loadData"
    >
      <template v-if="type !== 'info'" #btn>
        <el-button
          type="success"
          size="mini"
          icon="el-icon-plus"
          @click="handleEditStaff()"
        >
          新增
        </el-button>
      </template>
      <template #callImgs="row">
        <img
          style="max-width: 100px; max-height: 100px"
          :src="row.row.callImgs?.[0]?.url"
        />
      </template>
      <template #callGoal="{ row }">
        {{ row.callGoal.label }}
      </template>

      <template #callType="{ row }">
        {{ row.callType.label }}
      </template>

      <template #reachShopRole="{ row }">
        {{ row.reachShopRole.label }}
      </template>
      <template #operatName="{ row }">
        {{ row.operatName }}
      </template>

      <template #callState="{ row }">
        {{ row.callState.label }}
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            v-if="type == 'info'"
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
          <el-button
            v-if="type !== 'info'"
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="handleEdit(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            v-if="type !== 'info'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDeleteStaff(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDialog
      :value="showDialog"
      :title="dialogTitle"
      width="900px"
      :confirm-loading="dialogLoading"
      top="5%"
      @ok="handleDialogOk"
      @cancel="closeDialog"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParam"
        :form-list="columns"
        :confirm-loading="dialogLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="recordType"
        :no-footer="true"
        @proSubmit="formSubmit"
      >
        <template #callImgs>
          <ProUpload
            :file-list="formParam.callImgs"
            :type="type"
            :limit="5"
            :multiple="true"
            @uploadSuccess="handleCallImgsUploadSuccess"
            @uploadRemove="handleCallImgsUploadRemove"
          />
          <!-- <span v-if="type !== 'info'">仅支持上传png、jpg格式且10M大小内的图片。</span> -->
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import ProDialog from "@/components/ProDialog/index.vue";
import ProForm from "@/components/ProForm/index.vue";
import {
  getCustomerCallRecordPageApi,
  deletCustomerCallRecordApi,
  addCustomerCallRecordApi,
  editCustomerCallRecordApi,
  getCustomerUserListApi,
} from "@/api/customer";
import { dictTreeByCodeApi, userListApi } from "@/api/user";
import { Message, MessageBox } from "element-ui";
import ProUpload from "@/components/ProUpload/index.vue";

export default {
  name: "EditStaff",
  components: { ProTable, ProDialog, ProForm, ProUpload },
  props: {
    id: {
      type: [String, null],
      default: null,
    },
    shopName: {
      type: [String, null],
      default: null,
    },
    seqId: {
      type: [String, null],
      default: null,
    },
    type: {
      type: String,
      default: "edit",
    },
  },
  data() {
    const self = this;
    return {
      // 员工
      userList: [],
      // 表格数据
      columns: [
        // {
        //   dataIndex: "customerName",
        //   title: "客户名字",
        //   isTable: true,
        //   isForm: true,
        //   valueType: "input",
        //   clearable: true,
        //   formSpan: 24,
        //   minWidth: 150,
        // },

        {
          dataIndex: "reachShopRole",
          title: "受访角色",
          isTable: true,
          tableSlot: "reachShopRole",
          isForm: true,
          valueType: "select",
          formSpan: 12,
          option: [],
          optionMth: () => dictTreeByCodeApi(500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "reachShopName",
          title: "受访人员",
          isTable: true,
          // tableSlot: "reachShopRole",
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "reachShopTel",
          title: "受访人电话",
          isTable: true,
          isForm: true,
          valueType: "input",
          disabled: false,
          clearable: true,
          formSpan: 12,
          minWidth: 120,
        },
        {
          dataIndex: "callType",
          title: "拜访方式",
          isTable: true,
          isForm: true,
          tableSlot: "callType",
          valueType: "select",
          formSpan: 12,
          option: [],
          optionMth: () => dictTreeByCodeApi(3100),
          optionskey: {
            label: "label",
            value: "value",
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请选择拜访方式",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "callGoal",
          title: "拜访目的",
          isTable: true,
          tableSlot: "callGoal",
          isForm: true,
          valueType: "select",
          formSpan: 12,
          option: [],
          optionMth: () => dictTreeByCodeApi(2400),
          optionskey: {
            label: "label",
            value: "value",
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请选择拜访目的",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "reachShopTime",
          title: "到店时间",
          isTable: true,
          isForm: true,
          valueType: "date-picker",
          disabled: false,
          clearable: true,
          formSpan: 12,
          minWidth: 150,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入客户名称",
          //     trigger: "change",
          //   },
          // ],
        },

        {
          dataIndex: "remark",
          title: "拜访内容",
          isTable: true,
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          clearable: true,
          autosize: {
            minRows: 3,
            maxRows: 6,
          },
          minWidth: 200,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入拜访内容",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "nextNoticeRemark",
          title: "下次注意事项",
          isTable: true,
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          clearable: true,
          autosize: {
            minRows: 3,
            maxRows: 6,
          },
          minWidth: 200,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入拜访内容",
          //     trigger: "change",
          //   },
          // ],
        },
        // {
        //   dataIndex: "callImgs",
        //   title: "拜访图片",
        //   isTable: true,
        //   isForm:false,
        //   valueType: "img",
        //   tableSlot: "callImgs",
        //   formSlot: "callImgs",
        //   formSpan: 12,
        //   width: 120,
        // },
        {
          dataIndex: "operatName",
          title: "员工名称",
          isTable: true,
          isForm: true,
          tableSlot: "operatName",
          valueType: "select",
          option: [],
          formSpan: 12,
        },
        // {
        //   dataIndex: "optUserName",
        //   title: "拜访人名称",
        //   isTable: true,
        //   isForm: true,
        //   valueType: "text",
        //   formSpan: 12,
        // },
        {
          dataIndex: "operatRoleName",
          title: "员工角色",
          isTable: true,
          disabled: false,
          clearable: true,
          formatter: (row) =>
            row.operatRoleName ? JSON.parse(row.operatRoleName).join("、") : "",
        },
        {
          dataIndex: "optUserName",
          title: "操作员",
          isTable: true,
          valueType: "input",
          disabled: false,
          clearable: true,
          formSpan: 12,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入客户名称",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "callState",
          title: "跟进状态",
          isTable: true,
          tableSlot: "callState",
          isForm: true,
          valueType: "select",
          formSpan: 12,
          option: [],
          optionMth: () => dictTreeByCodeApi(3700),
          optionskey: {
            label: "label",
            value: "value",
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请选择跟进状态",
          //     trigger: "change",
          //   },
          // ],
        },
        {
          dataIndex: "callImgs",
          title: "拜访图片",
          isForm: true,
          isTable: true,
          valueType: "img",
          formSlot: "callImgs",
          tableSlot: "callImgs",
          width: 120,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 180,
          fixed: "right",
        },
      ],
      tableData: [],
      queryParam: {},
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },

      // 表单数据
      showDialog: false,
      dialogTitle: "新增访问记录",
      dialogLoading: false,
      formParam: {},
      recordType: "add",
      userTureList: [],
    };
  },
  mounted() {
    this.refresh();
    this.operatList();
  },
  methods: {
    async loadData(params) {
      try {
        const result = await getCustomerCallRecordPageApi({
          ...params,
          customerId: this.id,
        });
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    // 员工列表处理
    operatList() {
      // userListApi({ pageNumber: 1, pageSize: 10000 }).then((res) => {
      //   this.userTureList = res.data.rows;
      //   res.data.rows.map((item) => {
      //     this.userList.push({
      //       value: item.name,
      //       label: item.name,
      //     });
      //   });
      //   this.columns[8].option = this.userList;
      // });
      getCustomerUserListApi().then((res) => {
        this.userTureList = res.data;
        res.data.map((item) => {
          this.userList.push({
            value: item.id,
            label: item.name,
          });
        });
        this.columns[8].option = this.userList;
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    // 图片处理
    handleCallImgsUploadSuccess(result) {
      this.formParam.callImgs.push(result);
    },
    handleCallImgsUploadRemove(file) {
      const index = this.formParam.callImgs.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParam.callImgs.splice(index, 1);
    },
    //
    handleEditStaff(row) {
      if (!row) {
        this.dialogTitle = "新增拜访记录";
        this.recordType = "add";
        this.formParam = {
          callImgs: [],
          reachShopName: this.shopName,
        };
        const index = this.columns.findIndex(
          (item) => item.dataIndex === "reachShopTel"
        );
        this.columns[index].disabled = false;
        // this.$set(this.columns, 1, { ...this.columns[index] });
      }
      // 暂取消编辑
      // else {
      //   this.dialogTitle = "编辑员工";
      //   this.callType = "edit";
      //   const index = this.columns.findIndex(
      //     (item) => item.dataIndex === "tel"
      //   );
      //   this.columns[index].disabled = true;
      //   this.$set(this.columns, 1, { ...this.columns[index] });
      //   const formParam = JSON.parse(JSON.stringify(row));
      //   Object.keys(formParam).forEach((key) => {
      //     formParam[key] = formParam[key].label
      //       ? formParam[key].value
      //       : formParam[key];
      //   });
      //   this.formParam = formParam;
      // }
      this.showDialog = true;
    },
    handleEdit(row, type) {
      if (!row) {
        row = {};
      }
      // const data = { ...row };
      // this.formParam = { ...row };
      const formParam = JSON.parse(JSON.stringify(row));
      Object.keys(formParam).forEach((key) => {
        formParam[key] = formParam[key].label
          ? formParam[key].value
          : formParam[key];
      });
      this.formParam = formParam;
      console.log(this.formParam);
      this.recordType = type;
      this.dialogTitle =
        type === "add"
          ? "新增拜访记录"
          : type === "info"
          ? "拜访记录详情"
          : "编辑拜访记录";
      this.showDialog = true;
    },
    handleDeleteStaff(row) {
      MessageBox.confirm("确定删除该记录？").then(async () => {
        try {
          await deletCustomerCallRecordApi(row.id);
          this.$message.success("删除成功");
          this.refresh();
        } catch (error) {
          Message.error(error.message);
        }
      });
    },
    handleAddStaff() {},
    handleReset() {
      this.queryParam = {};
      this.$refs.ProTable.refresh();
    },
    // 表单相关
    closeDialog() {
      this.showDialog = false;
      this.$nextTick(() => {
        Object.keys(this.formParam).forEach((key) => {
          delete this.formParam[key];
        });
      });
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    // 转ISO日期
    // toEditUpStyle(v) {
    //   console.log(v.reachShopTime.toISOString().slice(0, 10))
    //   v.reachShopTime = v.reachShopTime.toISOString().slice(0, 10)
    //   val.customerId = this.id
    //   return v
    // },
    async formSubmit(val) {
      if (!(typeof val.reachShopTime === "string")) {
        val.reachShopTime = val.reachShopTime.toISOString().slice(0, 10);
      }
      await this.userTureList.map((item) => {
        if (item.name == val.operatName) {
          val.operatId = item.id;
        }
      });

      try {
        const editApi =
          this.recordType === "add"
            ? addCustomerCallRecordApi
            : editCustomerCallRecordApi;
        this.dialogLoading = true;
        const args = {
          ...val,
          customerId: this.id,
        };
        const result = await editApi(args);
        if (result.code === 200) {
          Message.success("操作成功");
          this.closeDialog();
          this.refresh();
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.dialogLoading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.headBox {
  display: flex;
  font-size: 16px;
  color: #6488cf;

  div {
    flex: 1;
  }
}
</style>
