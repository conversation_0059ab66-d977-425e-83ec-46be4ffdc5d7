import React, { ReactNode } from 'react';
import { useDevice } from '../../hooks/useDevice';

interface ResponsiveComponentProps {
  mobile?: ReactNode;
  tablet?: ReactNode;
  desktop?: ReactNode;
  children?: ReactNode;
  className?: string;
}

export const ResponsiveComponent: React.FC<ResponsiveComponentProps> = ({
  mobile,
  tablet,
  desktop,
  children,
  className = ''
}) => {
  const { isMobile, isTablet, isDesktop } = useDevice();

  // 如果提供了特定设备的组件，则使用它们
  if (isMobile && mobile) {
    return <div className={className}>{mobile}</div>;
  }

  if (isTablet && tablet) {
    return <div className={className}>{tablet}</div>;
  }

  if (isDesktop && desktop) {
    return <div className={className}>{desktop}</div>;
  }

  // 否则使用默认的children
  return <div className={className}>{children}</div>;
};

export default ResponsiveComponent;
