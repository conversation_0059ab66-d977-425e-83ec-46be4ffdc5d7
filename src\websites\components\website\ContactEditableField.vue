<template>
  <div class="contact-editable-field">
    <!-- 显示模式 -->
    <div 
      v-if="!isEditing" 
      :class="['field-display', className, { 'field-changed': isChanged }]"
      @click="startEdit"
    >
      <div v-if="hasContent" class="contact-content">
        <div v-if="number" class="contact-number">{{ label }}：{{ number }}</div>
        <div v-if="icon" class="contact-icon">
          <img :src="icon" :alt="`${label}图标`" class="icon-image" />
        </div>
        <div v-if="qrCode" class="contact-qr">
          <img :src="qrCode" :alt="`${label}二维码`" class="qr-image" />
        </div>
      </div>
      <span v-else class="field-placeholder">编辑{{ label }}</span>
      <i v-if="isChanged" class="el-icon-edit-outline field-changed-icon"></i>
    </div>

    <!-- 编辑模式 -->
    <el-dialog
      :title="`编辑${label}信息`"
      :visible.sync="isEditing"
      width="600px"
      :before-close="handleCancel"
      append-to-body
    >
      <div class="edit-form">
        <el-form :model="editForm" :rules="rules" ref="editForm" label-width="100px">
          <el-form-item :label="`${label}号/账号`" prop="number">
            <el-input
              v-model="editForm.number"
              :placeholder="`请输入${label}号`"
              :maxlength="50"
              show-word-limit
            />
            <div class="form-tip">可选项：输入{{ label }}号码或用户名。可留空仅显示二维码</div>
          </el-form-item>
          
          <el-form-item :label="`${label}图标`" prop="icon">
            <div class="upload-section">
              <el-upload
                class="icon-uploader"
                :action="uploadAction"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleIconSuccess"
                :before-upload="beforeIconUpload"
                accept="image/*"
              >
                <img v-if="editForm.icon" :src="editForm.icon" class="uploaded-icon">
                <i v-else class="el-icon-plus icon-uploader-icon"></i>
              </el-upload>
              <div class="upload-tips">
                <p>建议尺寸：64x64px</p>
                <p>支持 JPG、PNG 格式</p>
                <el-button v-if="editForm.icon" @click="clearIcon" size="mini" type="danger">删除图标</el-button>
              </div>
            </div>
          </el-form-item>
          
          <el-form-item :label="`${label}二维码`" prop="qrCode">
            <div class="upload-section">
              <el-upload
                class="qr-uploader"
                :action="uploadAction"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleQrSuccess"
                :before-upload="beforeQrUpload"
                accept="image/*"
              >
                <img v-if="editForm.qrCode" :src="editForm.qrCode" class="uploaded-qr">
                <i v-else class="el-icon-plus qr-uploader-icon"></i>
              </el-upload>
              <div class="upload-tips">
                <p>建议尺寸：200x200px</p>
                <p>支持 JPG、PNG 格式</p>
                <el-button v-if="editForm.qrCode" @click="clearQr" size="mini" type="danger">删除二维码</el-button>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleClear" type="warning">清空全部</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'

export default {
  name: 'ContactEditableField',
  props: {
    // 联系方式标签
    label: {
      type: String,
      required: true
    },
    // 联系号码/账号
    number: {
      type: String,
      default: ''
    },
    // 图标URL
    icon: {
      type: String,
      default: ''
    },
    // 二维码URL
    qrCode: {
      type: String,
      default: ''
    },
    // 号码字段键名
    numberKey: {
      type: String,
      required: true
    },
    // 图标字段键名
    iconKey: {
      type: String,
      required: true
    },
    // 二维码字段键名
    qrCodeKey: {
      type: String,
      required: true
    },
    // 自定义样式类
    className: {
      type: String,
      default: ''
    },
    // 是否已更改
    isChanged: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEditing: false,
      saving: false,
      editForm: {
        number: '',
        icon: '',
        qrCode: ''
      }
    }
  },
  computed: {
    hasContent() {
      return this.number || this.icon || this.qrCode
    },
    uploadAction() {
      return window.config?.api?.uploadURL + '/file/upload' || '/api/file/upload'
    },
    uploadHeaders() {
      return {
        'X-AUTH-TOKEN': getToken()
      }
    },
    rules() {
      return {
        number: [
          {
            max: 50,
            message: '账号不能超过50个字符',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    startEdit() {
      this.editForm.number = this.number || ''
      this.editForm.icon = this.icon || ''
      this.editForm.qrCode = this.qrCode || ''
      this.isEditing = true
    },
    handleCancel() {
      this.isEditing = false
      this.editForm.number = this.number || ''
      this.editForm.icon = this.icon || ''
      this.editForm.qrCode = this.qrCode || ''
    },
    handleClear() {
      this.editForm.number = ''
      this.editForm.icon = ''
      this.editForm.qrCode = ''
    },
    clearIcon() {
      this.editForm.icon = ''
    },
    clearQr() {
      this.editForm.qrCode = ''
    },
    async handleSave() {
      try {
        await this.$refs.editForm.validate()
        this.saving = true
        
        // 触发值更改事件
        this.$emit('change', this.numberKey, this.editForm.number)
        this.$emit('change', this.iconKey, this.editForm.icon)
        this.$emit('change', this.qrCodeKey, this.editForm.qrCode)
        
        // 模拟保存延迟
        await new Promise(resolve => setTimeout(resolve, 300))
        
        this.isEditing = false
        this.$message.success('保存成功')
      } catch (error) {
        console.error('验证失败:', error)
      } finally {
        this.saving = false
      }
    },
    handleIconSuccess(response) {
      if (response.code === 200) {
        this.editForm.icon = response.data.url
        this.$message.success('图标上传成功')
      } else {
        this.$message.error('图标上传失败')
      }
    },
    handleQrSuccess(response) {
      if (response.code === 200) {
        this.editForm.qrCode = response.data.url
        this.$message.success('二维码上传成功')
      } else {
        this.$message.error('二维码上传失败')
      }
    },
    beforeIconUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    beforeQrUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过 5MB!')
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.contact-editable-field {
  .field-display {
    position: relative;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
    min-height: 40px;
    display: inline-flex;
    align-items: center;
    
    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
      border: 1px dashed #409eff;
    }
    
    &.field-changed {
      background-color: rgba(245, 166, 35, 0.1);
      border: 1px solid #f5a623;
      
      .field-changed-icon {
        margin-left: 8px;
        color: #f5a623;
        font-size: 12px;
      }
    }
    
    .contact-content {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .contact-number {
        font-size: 14px;
        color: #606266;
      }
      
      .contact-icon {
        .icon-image {
          width: 24px;
          height: 24px;
          object-fit: contain;
        }
      }
      
      .contact-qr {
        .qr-image {
          width: 32px;
          height: 32px;
          object-fit: contain;
        }
      }
    }
    
    .field-placeholder {
      color: #c0c4cc;
      font-style: italic;
    }
  }
  
  .edit-form {
    .form-tip {
      margin-top: 4px;
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
    }
    
    .upload-section {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      
      .icon-uploader,
      .qr-uploader {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: border-color 0.3s;
        
        &:hover {
          border-color: #409eff;
        }
      }
      
      .icon-uploader {
        width: 64px;
        height: 64px;
        
        .uploaded-icon {
          width: 64px;
          height: 64px;
          object-fit: contain;
          display: block;
        }
        
        .icon-uploader-icon {
          font-size: 28px;
          color: #8c939d;
          width: 64px;
          height: 64px;
          line-height: 64px;
          text-align: center;
        }
      }
      
      .qr-uploader {
        width: 120px;
        height: 120px;
        
        .uploaded-qr {
          width: 120px;
          height: 120px;
          object-fit: contain;
          display: block;
        }
        
        .qr-uploader-icon {
          font-size: 28px;
          color: #8c939d;
          width: 120px;
          height: 120px;
          line-height: 120px;
          text-align: center;
        }
      }
      
      .upload-tips {
        flex: 1;
        
        p {
          margin: 0 0 4px 0;
          color: #909399;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }
  }
  
  .dialog-footer {
    text-align: right;
  }
}
</style>
