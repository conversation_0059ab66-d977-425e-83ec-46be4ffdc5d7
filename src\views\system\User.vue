<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:52
 * @Description: 用户管理
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      :local-pagination="localPagination"
      :data="tableData"
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增用户
        </el-button>
      </template>
      <template #actions="{ row }">
        <span class="fixed-width">
          <el-button
            type="primary"
            icon="el-icon-edit-outline"
            @click="handleUpdate(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="row.state?.value === 'suspended'"
            type="primary"
            icon="el-icon-open"
            @click="handleActivate(row)"
          >
            激活账号
          </el-button>

          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-edit-outline"
            @click="handlePwd(row)"
          >
            重置密码
          </el-button>
        </span>
      </template>
    </ProTable>

    <!-- 新增、编辑、详情框  -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :confirm-loading="confirmLoading"
      :top="'5%'"
      :no-footer="methodType === 'info'"
      :confirm-text="methodType === 'add' ? '确认新增' : '保存'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>
<script>
import {
  key,
  userListApi,
  userAddApi,
  userEditApi,
  userDelApi,
  sexApi,
  userPwdApi,
  activeUserApi,
} from "@/api/user";
import { validatePassword, checkIdCard, checkMobile } from "@/utils/validate";
import { encodeFun } from "@/utils/index";
import { cloneDeep } from "lodash";

export default {
  name: "User",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      validatePass2: (rule, value, callback) => {
        console.log(this.form.password);
        if (value === "") {
          callback(new Error("请再次输入密码"));
        } else if (value !== this.form.password) {
          callback(new Error("两次输入密码不一致!"));
        } else {
          callback();
        }
      },
      validatePass3: (rule, value, callback) => {
        if (value === "") {
          callback(new Error("请再次输入密码"));
        } else if (value !== this.form.newPassword) {
          callback(new Error("两次输入密码不一致!"));
        } else {
          callback();
        }
      },
      active: 9,

      // 列表
      spareiTypeList: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {
        aduitState: null,
        name: null,
      },
      columns: [
        {
          dataIndex: "name",
          title: "关键字",
          isSearch: true,
          valueType: "input",
          placeholder: "用户名/用户账号/手机号",
        },
        {
          dataIndex: "code",
          title: "用户账号",
          isTable: true,
        },
        {
          dataIndex: "name",
          title: "用户名",
          isTable: true,
        },

        {
          dataIndex: "mobileNumber",
          title: "手机号",
          isTable: true,
        },
        {
          dataIndex: "sex",
          title: "性别",
          isTable: true,
          formatter: (row) => row.sex?.label,
          width: 200,
        },

        {
          dataIndex: "Actions",
          width: 320,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      actionUrl: "",
      uploadlLading: false,
      uploadDialogVisible: false,
      //新增
      methodType: "add",
      confirmLoading: false,
      dialogTitle: "",
      form: {},
      dialogVisible: false,
      defaultFormParams: {
        cipherToken: "",
        code: "",
        name: "",
        sex: "",
        type: "permanent",
        password: "",
        surepassword: "",
        mobileNumber: "",
        identityCardNumber: "",
        email: "",
        accountExpireAt: "",
      },
      formcolumns: [],
    };
  },
  computed: {},
  watch: {
    "form.type": {
      deep: true, //true为进行深度监听,false为不进行深度监听
      handler(newVal) {
        let bool = false;
        if (newVal === "temporary") {
          // 短期用户
          bool = true;
        }
        this.formcolumns.map((item, index) => {
          if (item.dataIndex === "accountExpireAt") {
            // this.formcolumns.splice(index, 1, { ...item, isShow: bool });
          }
        });
      },
    },
  },
  created() {},
  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    getCode() {},
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);

      userListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
      console.log(this.form);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add"
        ? this.create()
        : this.methodType == "edit"
        ? this.update()
        : this.changepwd();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.formcolumns = [
        {
          dataIndex: "code",
          isForm: true,
          title: "用户账号",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入用户账号",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "name",
          title: "用户姓名",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入用户姓名",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "sex",
          title: "性别",
          isForm: true,
          valueType: "select",
          formSpan: 24,
          isSearch: true,
          clearable: true,
          option: [],
          optionMth: sexApi,
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择性别",
              trigger: "change",
            },
          ],
        },

        {
          dataIndex: "password",
          title: "用户密码",
          isForm: true,
          valueType: "input",
          // inputType: "password",
          clearable: true,
          formSpan: 24,
          prop: [
            { required: true, validator: validatePassword, trigger: "blur" },
          ],
        },
        {
          dataIndex: "surepassword",
          title: "确认密码",
          isForm: true,
          valueType: "input",
          // inputType: "password",
          clearable: true,
          formSpan: 24,
          prop: [
            { required: true, validator: this.validatePass2, trigger: "blur" },
          ],
        },
        {
          dataIndex: "mobileNumber",
          isTable: true,
          isForm: true,
          title: "手机号码",
          valueType: "input",
          inputType: "number",
          clearable: true,
          disabled: false,
          formSpan: 24,
          clearboth: true,
          prop: [{ required: true, trigger: "change", validator: checkMobile }],
        },
        {
          dataIndex: "identityCardNumber",
          isForm: true,
          title: "身份证号",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [{ trigger: "change", validator: checkIdCard }],
        },
        {
          dataIndex: "email",
          isForm: true,
          title: "邮箱",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [{ trigger: "change" }],
        },
      ];
      this.dialogTitle = "用户新增";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;

      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
        data?.id ? (this.form.parentId = data.id) : (this.form.parentId = null);
      });
    },
    //响应新增
    create() {
      key()
        .then((res) => {
          const key = res.data.second;

          this.form.cipherToken = res.data.first;

          console.log(this.form);
          const obj = {
            ...this.form,
            mobileNumber: encodeFun(this.form.mobileNumber, key),
            password: encodeFun(this.form.password, key),
            surepassword: encodeFun(this.form.surepassword, key),
            identityCardNumber: encodeFun(this.form.identityCardNumber, key),
            email: encodeFun(this.form.email, key),
          };
          userAddApi(obj)
            .then(() => {
              this.$message.success("新增成功");
              this.dialogVisible = false;
              this.$refs.ProTable.refresh();
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        })
        .catch(() => {});
    },
    // 激活账号
    handleActivate(row) {
      this.$confirm("是否激活该账号?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        activeUserApi(row.id).then((res) => {
          this.$message.success("激活成功");
          this.$refs.ProTable.refresh();
        });
      });
    },
    //触发编辑
    handleUpdate(row) {
      this.formcolumns = [
        {
          dataIndex: "code",
          isForm: true,
          title: "用户账号",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入用户账号",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "name",
          title: "用户姓名",
          isForm: true,
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入用户姓名",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "sex",
          title: "性别",
          isForm: true,
          valueType: "select",
          formSpan: 24,
          isSearch: true,
          clearable: true,
          option: [],
          optionMth: sexApi,
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择性别",
              trigger: "change",
            },
          ],
        },

        {
          dataIndex: "mobileNumber",
          isTable: true,
          isForm: true,
          title: "手机号码",
          valueType: "input",
          inputType: "number",
          clearable: true,
          disabled: false,
          formSpan: 24,
          clearboth: true,
          prop: [{ required: true, trigger: "change", validator: checkMobile }],
        },
        {
          dataIndex: "identityCardNumber",
          isForm: true,
          title: "身份证号",
          // inputType: "number",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [{ trigger: "change", validator: checkIdCard }],
        },
        {
          dataIndex: "email",
          isForm: true,
          title: "邮箱",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [{ trigger: "change" }],
        },
      ];
      this.dialogTitle = "编辑 - " + row.name;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.form.type = row?.type?.value;
      this.form.sex = row?.sex?.value;
      this.methodType = "edit";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应编辑
    update() {
      key()
        .then((res) => {
          const key = res.data.second;
          this.form.cipherToken = res.data.first;
          console.log(this.form);
          const obj = {
            ...this.form,
            mobileNumber: encodeFun(this.form.mobileNumber, key),
            identityCardNumber: encodeFun(this.form.identityCardNumber, key),
            email: encodeFun(this.form.email, key),
          };
          // var obj = {
          //   id: this.form.id,
          //   data: this.form
          // }
          userEditApi(this.form.id, obj)
            .then(() => {
              this.$message.success("修改成功");
            })
            .catch((err) => {
              var cc = err;
            })
            .finally(() => {
              this.confirmLoading = false;
              this.dialogVisible = false;
              this.$refs.ProTable.refresh();
            });
        })
        .catch(() => {});
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.label;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.form.type = row.type.value;
      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //重置密码
    handlePwd(data) {
      console.log(this.form);
      console.log(data);
      this.methodType = "pwd";
      this.resetFrom();
      this.$set(this.form, "id", data.id);
      this.formcolumns = [
        {
          dataIndex: "newPassword",
          title: "用户密码",
          isForm: true,
          valueType: "input",
          // inputType: "password",
          clearable: true,
          formSpan: 24,
          prop: [
            { required: true, validator: validatePassword, trigger: "blur" },
          ],
        },
        {
          dataIndex: "newPassword2",
          title: "确认密码",
          isForm: true,
          valueType: "input",
          // inputType: "password",
          clearable: true,
          formSpan: 24,
          prop: [
            { required: true, validator: this.validatePass3, trigger: "blur" },
          ],
        },
      ];
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应重置密码
    changepwd() {
      key()
        .then((res) => {
          const key = res.data.second;
          const obj = {
            cipherToken: res.data.first,
            id: this.form.id,
            newPassword: encodeFun(this.form.newPassword, key),
            newPassword2: this.form.newPassword2,
          };
          userPwdApi(obj)
            .then(() => {
              this.$message.success("密码重置成功");
              this.dialogVisible = false;
              this.$refs.ProTable.refresh();
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        })
        .catch(() => {});
    },
    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        userDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
