<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-08 13:55:48
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-08 16:16:57
 * @Description: 拆机件入库
 -->
<template>
  <ProDrawer
    :value="drawerVisible"
    :title="drawerTitle"
    :no-footer="true"
    size="50%"
    @cancel="drawerCancel"
  >
    <ProForm
      ref="ProForm"
      :form-param="formParam"
      :form-list="formColumns"
      :open-type="editType"
      :layout="{ formWidth: '100%', labelWidth: '100px' }"
    >
      <template #hostType>
        {{ formParam.hostType?.label || "" }}
      </template>
      <template #articleCode>
        <div>
          {{ formParam?.articleCode }}
        </div>
      </template>
      <template #picUrls>
        <div v-if="formParam.picUrls && formParam.picUrls?.length > 0">
          <el-image
            v-for="item in formParam.picUrls"
            :key="item.url"
            style="width: 100px; height: 100px"
            :src="item.url"
            :preview-src-list="[item.url]"
          >
          </el-image>
        </div>
      </template>
    </ProForm>
  </ProDrawer>
</template>

<script>
import { machineSplitDetailByCodeApi } from "@/api/repair";

export default {
  name: "SplitWaybill",
  data() {
    return {
      drawerVisible: false,
      drawerTitle: "",
      formParam: {},
      formColumns: [
        {
          dataIndex: "code",
          title: "维修单号",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isForm: true,
          formSlot: "hostType",
          formSpan: 24,
        },
        {
          dataIndex: "partName",
          title: "零件名称",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isForm: true,
          formSlot: "articleCode",
          formSpan: 24,
        },
        {
          dataIndex: "numberOem",
          title: "OEM编号",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "quantity",
          title: "数量",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入数量",
              trigger: "change",
            },
            {
              validator(rule, value, callback) {
                if (value <= 0) {
                  callback("数量不能小于0");
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "costPrice",
          title: "成本",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 12,
          prop: [
            {
              required: true,
              message: "请输入成本",
              trigger: "change",
            },
            {
              validator(rule, value) {
                if (value < 0) {
                  return Promise.reject("成本不能小于0");
                }
                return Promise.resolve();
              },
            },
          ],
        },
        {
          dataIndex: "salePrice",
          title: "售价",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 12,
        },
        {
          dataIndex: "location",
          title: "储位",
          isForm: true,
          valueType: "input",
          formSpan: 12,
        },
        {
          dataIndex: "picUrls",
          title: "零件图片",
          isForm: true,
          formSlot: "picUrls",
          formSpan: 24,
        },
      ],
      editType: "info",
    };
  },
  mounted() {},
  methods: {
    show(shopWaybill) {
      this.drawerVisible = true;
      this.drawerTitle = `${shopWaybill} - 拆机件`;
      machineSplitDetailByCodeApi(shopWaybill).then((res) => {
        this.formParam = res.data || {};
      });
    },
    drawerCancel() {
      this.drawerVisible = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
