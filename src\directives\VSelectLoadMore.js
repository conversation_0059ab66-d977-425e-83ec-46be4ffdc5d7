/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 09:18:09
 * @Description:
 */
import { debounce } from "lodash";
export default {
  inserted(el, binding, vnode) {
    const scrollWrap = el.querySelector(
      ".el-select-dropdown .el-scrollbar .el-select-dropdown__wrap"
    );
    const handle = debounce((e) => {
      const scrollDistance = scrollWrap.scrollHeight - scrollWrap.scrollTop;
      if (scrollWrap.clientHeight + 6 > scrollDistance) {
        binding.value();
      }
    }, 170);
    scrollWrap?.addEventListener("scroll", handle);
    el._hanlde = handle;
  },
  unbind(el) {
    const scrollWrap = el.querySelector(
      ".el-select-dropdown .el-scrollbar .el-select-dropdown__wrap"
    );
    scrollWrap?.removeEventListener("scroll", el._hanlde);
    delete el._hanlde;
  },
};
