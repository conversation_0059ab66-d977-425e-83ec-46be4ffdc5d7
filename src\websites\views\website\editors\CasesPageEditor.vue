<template>
  <div class="cases-page-editor">
    <!-- 页面标题 -->
    <div class="page-title-section">
      <EditableContent
        :value="content.title"
        placeholder="点击编辑页面标题"
        display-class="editable-page-title"
        @input="updateTitle"
      />
    </div>

    <!-- 英雄区 -->
    <section class="hero-section">
      <div class="hero-content">
        <EditableContent
          :value="content.config?.heroTitle || '成功案例'"
          placeholder="页面主标题"
          display-class="editable-hero-title"
          @input="updateHeroTitle"
        />
        <EditableContent
          :value="content.config?.heroSubtitle || '真实案例展示我们的专业能力'"
          placeholder="页面副标题"
          display-class="editable-hero-subtitle"
          @input="updateHeroSubtitle"
        />
      </div>
    </section>

    <!-- 案例筛选区 -->
    <section class="filter-section">
      <div class="filter-header">
        <EditableContent
          :value="content.config?.filterTitle || '案例分类筛选'"
          placeholder="筛选区标题"
          display-class="filter-title"
          @input="updateFilterTitle"
        />
        <el-button type="dashed" icon="el-icon-plus" @click="handleCategoryManage">
          管理分类
        </el-button>
      </div>
      <div class="filter-tags">
        <el-tag class="filter-tag-main">全部</el-tag>
        <el-tag
          v-for="(type, index) in businessTypes"
          :key="`business-type-${type}-${index}`"
          class="filter-tag-item"
          closable
          @close="handleCategoryDelete(type, index)"
        >
          {{ type }}
        </el-tag>
      </div>
    </section>

    <!-- 案例展示区 -->
    <section class="cases-section">
      <div class="cases-header">
        <h3 class="cases-title">案例展示</h3>
        <el-button type="primary" icon="el-icon-plus" @click="handleCaseAdd">
          添加案例
        </el-button>
      </div>
      <div class="cases-grid">
        <div
          v-for="(caseItem, index) in cases"
          :key="`case-item-${caseItem.title}-${index}`"
          class="case-card"
          @mouseenter="hoveredIndex = index"
          @mouseleave="hoveredIndex = -1"
        >
          <!-- 操作按钮 -->
          <div class="case-actions" v-show="hoveredIndex === index">
            <el-button
              size="mini"
              icon="el-icon-edit"
              @click="handleCaseEdit(index)"
            />
            <el-popconfirm
              title="确定删除这个案例吗？"
              @confirm="handleCaseDelete(index)"
              confirm-button-text="确定"
              cancel-button-text="取消"
            >
              <el-button
                slot="reference"
                size="mini"
                type="danger"
                icon="el-icon-delete"
              />
            </el-popconfirm>
          </div>

          <!-- 案例图片 -->
          <div class="case-image" @click="previewCaseImage(caseItem)">
            <img
              v-if="caseItem.image"
              :src="caseItem.image"
              :alt="caseItem.title"
              class="case-img cursor-pointer"
              @error="handleImageError"
            />
            <div v-else class="case-img-placeholder">
              <span class="placeholder-text">案例图片</span>
            </div>
            <!-- 放大图标提示 -->
            <div v-if="caseItem.image" class="image-overlay">
              <div class="overlay-icon">
                <i class="el-icon-view"></i>
              </div>
            </div>
          </div>

          <div class="case-content">
            <div class="case-meta-top">
              <span v-if="caseItem.category" class="case-category">{{ caseItem.category }}</span>
              <span v-if="caseItem.date" class="case-date">{{ caseItem.date }}</span>
            </div>
            <h4 class="case-title">{{ caseItem.title }}</h4>
            <p class="case-description">{{ caseItem.description }}</p>
            <!-- 渲染标签 -->
            <div v-if="Array.isArray(caseItem.tags) && caseItem.tags.length > 0" class="case-tags">
              <el-tag
                v-for="(tag, tagIndex) in caseItem.tags.slice(0, 3)"
                :key="`case-tag-${tag}-${tagIndex}`"
                size="small"
                class="case-tag"
              >
                {{ tag }}
              </el-tag>
              <span v-if="caseItem.tags.length > 3" class="tags-more">
                +{{ caseItem.tags.length - 3 }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 服务优势 -->
    <section class="advantages-section">
      <div class="advantages-header">
        <EditableContent
          :value="content.config?.advantageTitle || '为什么选择我们'"
          placeholder="优势标题"
          display-class="advantages-title"
          @input="updateAdvantageTitle"
        />
        <el-button type="dashed" icon="el-icon-plus" @click="handleAdvantageAdd">
          添加优势
        </el-button>
      </div>
      <div class="advantages-grid">
        <div
          v-for="(advantage, index) in advantages"
          :key="`advantage-item-${advantage.title}-${index}`"
          class="advantage-card"
          @mouseenter="hoveredAdvantageIndex = index"
          @mouseleave="hoveredAdvantageIndex = -1"
        >
          <!-- 操作按钮 -->
          <div class="advantage-actions" v-show="hoveredAdvantageIndex === index">
            <el-button
              size="mini"
              icon="el-icon-edit"
              @click="handleAdvantageEdit(index)"
            />
            <el-popconfirm
              title="确定删除这个优势吗？"
              @confirm="handleAdvantageDelete(index)"
              confirm-button-text="确定"
              cancel-button-text="取消"
            >
              <el-button
                slot="reference"
                size="mini"
                type="danger"
                icon="el-icon-delete"
              />
            </el-popconfirm>
          </div>

          <div class="advantage-icon">{{ advantage.icon }}</div>
          <h3 class="advantage-title">{{ advantage.title }}</h3>
          <p class="advantage-description">{{ advantage.description }}</p>
        </div>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <EditableContent
        :value="content.config?.ctaTitle || '需要类似服务？'"
        placeholder="CTA标题"
        display-class="cta-title"
        @input="updateCtaTitle"
      />
      <EditableContent
        :value="content.config?.ctaSubtitle || '联系我们，获得专业的维修解决方案'"
        placeholder="CTA副标题"
        display-class="cta-subtitle"
        @input="updateCtaSubtitle"
      />
      <a href="/contact" @click.prevent class="cta-button">
        <EditableContent
          :value="content.config?.ctaButtonText || '立即咨询'"
          placeholder="按钮文字"
          display-class="cta-button-text"
          @input="updateCtaButtonText"
        />
      </a>
    </section>



    <!-- 案例编辑模态框 -->
    <el-dialog
      :title="editingCaseIndex >= 0 ? '编辑案例' : '添加案例'"
      :visible.sync="caseModalVisible"
      width="800px"
      :close-on-click-modal="false"
      :modal="true"
      :modal-append-to-body="true"
      :append-to-body="true"
      @close="handleCaseModalClose"
    >
      <el-form
        ref="caseForm"
        :model="caseFormData"
        :rules="caseFormRules"
        label-width="100px"
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="案例标题" prop="title">
              <el-input
                v-model="caseFormData.title"
                placeholder="如：某大型企业复印机维修案例"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案例分类" prop="category">
              <el-select
                v-model="caseFormData.category"
                placeholder="选择案例分类"
                style="width: 100%"
              >
                <el-option
                  v-for="type in businessTypes"
                  :key="type"
                  :label="type"
                  :value="type"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="案例描述" prop="description">
          <el-input
            type="textarea"
            v-model="caseFormData.description"
            :rows="3"
            placeholder="详细描述案例内容..."
          />
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="标签">
              <el-select
                v-model="caseFormData.tags"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="输入标签后回车或逗号分隔"
                style="width: 100%"
                clearable
                :max-collapse-tags="6"
              >
                <el-option
                  v-for="tag in commonTags"
                  :key="tag"
                  :label="tag"
                  :value="tag"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案例日期">
              <el-input
                v-model="caseFormData.date"
                placeholder="如：2023年8月"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="案例图片">
          <ImageUploadInput
            v-model="caseFormData.image"
            category="case"
            placeholder="案例图片URL或拖拽上传"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCaseModalClose">取消</el-button>
        <el-button type="primary" @click="handleCaseSave" :loading="saving">
          保存
        </el-button>
      </div>
    </el-dialog>

    <!-- 优势编辑模态框 -->
    <el-dialog
      :title="editingAdvantageIndex >= 0 ? '编辑优势' : '添加优势'"
      :visible.sync="advantageModalVisible"
      width="600px"
      :close-on-click-modal="false"
      :modal="true"
      :modal-append-to-body="true"
      :append-to-body="true"
      @close="handleAdvantageModalClose"
    >
      <el-form
        ref="advantageForm"
        :model="advantageFormData"
        :rules="advantageFormRules"
        label-width="100px"
      >
        <el-form-item label="优势图标" prop="icon">
          <el-input
            v-model="advantageFormData.icon"
            placeholder="如：⚡ 或图标类名"
            maxlength="10"
          />
          <div class="form-item-extra">建议使用emoji图标，如：⚡ 🚀 🔧 ✅</div>
          <div class="icon-preview" v-if="advantageFormData.icon">
            预览：<span class="preview-icon">{{ advantageFormData.icon }}</span>
          </div>
        </el-form-item>

        <el-form-item label="优势标题" prop="title">
          <el-input
            v-model="advantageFormData.title"
            placeholder="如：快速响应"
          />
        </el-form-item>

        <el-form-item label="优势描述" prop="description">
          <el-input
            type="textarea"
            v-model="advantageFormData.description"
            :rows="3"
            placeholder="详细描述优势特点..."
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleAdvantageModalClose">取消</el-button>
        <el-button type="primary" @click="handleAdvantageSave" :loading="saving">
          保存
        </el-button>
      </div>
    </el-dialog>

    <!-- 分类管理模态框 -->
    <el-dialog
      title="管理案例分类"
      :visible.sync="categoryModalVisible"
      width="600px"
      :close-on-click-modal="false"
      :modal="true"
      :modal-append-to-body="true"
      :append-to-body="true"
      @close="handleCategoryModalClose"
    >
      <div class="category-management">
        <div class="category-input-section">
          <label class="category-label">
            案例分类列表 (用逗号分隔)
          </label>
          <el-input
            type="textarea"
            :rows="4"
            v-model="categoryInputValue"
            placeholder="请输入分类名称，用逗号分隔，如：复印机维修, 打印机维修, 设备保养"
          />
        </div>

        <div class="category-preview">
          <h4 class="preview-title">当前分类预览：</h4>
          <div class="preview-tags">
            <el-tag
              v-for="(type, index) in categoryInputValue.split(',').map(t => t.trim()).filter(t => t)"
              :key="`preview-type-${type}-${index}`"
              class="preview-tag"
            >
              {{ type }}
            </el-tag>
            <span v-if="categoryInputValue.split(',').map(t => t.trim()).filter(t => t).length === 0" class="no-categories">
              暂无分类
            </span>
          </div>
        </div>

        <div class="category-tips">
          <p class="tips-title">💡 提示：</p>
          <ul class="tips-list">
            <li>分类名称用逗号分隔</li>
            <li>修改后需要点击保存按钮</li>
            <li>删除已被案例使用的分类会失败</li>
          </ul>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCategoryModalClose">取消</el-button>
        <el-button type="primary" @click="handleCategorySave">
          保存
        </el-button>
      </div>
    </el-dialog>

    <!-- 图片放大预览模态框 -->
    <el-dialog
      :title="previewImageTitle || '图片预览'"
      :visible.sync="imagePreviewVisible"
      width="60%"
      :modal="true"
      :modal-append-to-body="false"
      :append-to-body="true"
      :close-on-click-modal="true"
      custom-class="image-preview-modal"
      center
    >
      <div class="text-center p-4">
        <!-- 图片显示 -->
        <img
          :src="previewImageUrl"
          :alt="previewImageTitle"
          class="preview-image"
          @error="handleImageError"
          @click="imagePreviewVisible = false"
        />

        <!-- 图片信息 -->
        <div class="mt-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-1">
            {{ previewImageTitle }}
          </h3>
          <p class="text-gray-500 text-sm">
            点击图片或外区域关闭预览
          </p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import EditableContent from '@/websites/components/website/EditableContent.vue'
import ImageUploadInput from '@/websites/components/website/ImageUploadInput.vue'

export default {
  name: 'CasesPageEditor',
  components: {
    EditableContent,
    ImageUploadInput
  },
  props: {
    content: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      hoveredIndex: -1,
      hoveredAdvantageIndex: -1,
      caseModalVisible: false,
      advantageModalVisible: false,
      categoryModalVisible: false,
      editingCaseIndex: -1,
      editingAdvantageIndex: -1,
      saving: false,
      categoryInputValue: '',
      caseFormData: {
        title: '',
        category: '',
        description: '',
        tags: [],
        date: '',
        image: ''
      },
      advantageFormData: {
        icon: '',
        title: '',
        description: ''
      },
      caseFormRules: {
        title: [
          { required: true, message: '请输入案例标题', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择案例分类', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入案例描述', trigger: 'blur' }
        ]
      },
      advantageFormRules: {
        icon: [
          { required: true, message: '请输入优势图标', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '请输入优势标题', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入优势描述', trigger: 'blur' }
        ]
      },
      commonTags: [
        '快速维修', '批量处理', '紧急维修', '设备升级',
        '保养维护', '故障排除', '技术支持', '现场服务'
      ],

      // 图片预览状态
      imagePreviewVisible: false,
      previewImageUrl: '',
      previewImageTitle: ''
    }
  },
  computed: {
    cases() {
      // 优先从content.cases获取，如果没有则从config.cases获取，保持与React项目一致
      const cases = this.content.cases || this.content.config?.cases || []
      return cases
    },
    advantages() {
      // 获取优势列表
      return this.content.config?.advantages || []
    },
    businessTypes() {
      // 从config获取业务类型，如果没有则返回空数组，避免显示默认分类
      return this.content.config?.businessTypes || []
    }
  },
  methods: {
    updateTitle(value) {
      this.$emit('update', { title: value })
      this.$emit('change')
    },

    updateDescription(value) {
      this.$emit('update', { description: value })
      this.$emit('change')
    },

    // 英雄区域更新方法
    updateHeroTitle(value) {
      this.updateConfig({ heroTitle: value })
    },

    updateHeroSubtitle(value) {
      this.updateConfig({ heroSubtitle: value })
    },

    // 筛选区域更新方法
    updateFilterTitle(value) {
      this.updateConfig({ filterTitle: value })
    },

    // 分类管理方法
    handleCategoryManage() {
      this.categoryInputValue = this.businessTypes.join(', ')
      this.categoryModalVisible = true
    },

    handleCategoryDelete(type, index) {
      // 检查该分类是否被案例使用
      const used = this.cases.some(item => item.category === type)
      if (used) {
        this.$message.error(`分类"${type}"已被案例使用，无法删除！`)
        return
      }
      const newTypes = this.businessTypes.filter((_, i) => i !== index)
      this.updateBusinessTypes(newTypes)
      this.$message.success(`分类"${type}"删除成功`)
    },

    updateBusinessTypes(types) {
      // 这里可以通过emit或其他方式更新业务类型
      // 暂时存储在config中
      this.updateConfig({ businessTypes: types })
    },

    // 分类保存方法
    handleCategorySave() {
      try {
        // 从输入框的值保存分类
        const types = this.categoryInputValue.split(',').map(type => type.trim()).filter(type => type)
        this.updateBusinessTypes(types)
        this.categoryModalVisible = false
        this.$message.success('分类保存成功')
        this.$emit('change')
      } catch (error) {
        this.$message.error('保存失败，请重试')
      }
    },

    // 分类模态框关闭
    handleCategoryModalClose() {
      this.categoryModalVisible = false
      this.categoryInputValue = ''
    },

    // 优势区域更新方法
    updateAdvantageTitle(value) {
      this.updateConfig({ advantageTitle: value })
    },

    // CTA区域更新方法
    updateCtaTitle(value) {
      this.updateConfig({ ctaTitle: value })
    },

    updateCtaSubtitle(value) {
      this.updateConfig({ ctaSubtitle: value })
    },

    updateCtaButtonText(value) {
      this.updateConfig({ ctaButtonText: value })
    },

    // 处理案例编辑
    handleCaseEdit(index) {
      const cases = this.cases
      if (cases[index]) {
        this.editingCaseIndex = index
        this.caseFormData = {
          title: cases[index].title || '',
          category: cases[index].category || '',
          description: cases[index].description || '',
          tags: cases[index].tags || [],
          date: cases[index].date || '',
          image: cases[index].image || 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=300&fit=crop'
        }
        this.caseModalVisible = true
      }
    },

    // 处理案例新增
    handleCaseAdd() {
      this.editingCaseIndex = -1
      this.caseFormData = {
        title: '',
        category: '',
        description: '',
        tags: [],
        date: '',
        image: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=300&fit=crop'
      }
      this.caseModalVisible = true
    },



    // 处理案例删除
    handleCaseDelete(index) {
      const newCases = this.cases.filter((_, i) => i !== index)
      this.updateConfig({ cases: newCases })
    },

    // 保存案例
    async handleCaseSave() {
      try {
        await this.$refs.caseForm.validate()
        this.saving = true

        const cases = [...this.cases]
        const caseData = { ...this.caseFormData }

        if (this.editingCaseIndex >= 0) {
          cases[this.editingCaseIndex] = caseData
        } else {
          cases.push(caseData)
        }

        this.updateConfig({ cases })
        this.caseModalVisible = false
        this.$message.success(
          this.editingCaseIndex >= 0 ? '案例更新成功，请点击保存按钮提交到数据库' : '案例添加成功，请点击保存按钮提交到数据库'
        )
      } catch (error) {
        // 案例保存失败时静默处理
      } finally {
        this.saving = false
      }
    },

    // 关闭模态框
    handleCaseModalClose() {
      this.caseModalVisible = false
      this.editingCaseIndex = -1
      this.caseFormData = {
        title: '',
        category: '',
        description: '',
        tags: [],
        date: '',
        image: ''
      }
    },

    // 处理图片加载错误
    handleImageError(e) {
      e.target.src = 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=300&fit=crop'
    },

    // 图片预览功能
    previewCaseImage(caseItem) {
      if (!caseItem.image) return
      this.previewImageUrl = caseItem.image
      this.previewImageTitle = caseItem.title
      this.imagePreviewVisible = true
    },

    // 优势管理方法
    handleAdvantageEdit(index) {
      const advantages = this.advantages
      if (advantages[index]) {
        this.editingAdvantageIndex = index
        this.advantageFormData = {
          icon: advantages[index].icon || '',
          title: advantages[index].title || '',
          description: advantages[index].description || ''
        }
        this.advantageModalVisible = true
      }
    },

    handleAdvantageAdd() {
      this.editingAdvantageIndex = -1
      this.advantageFormData = {
        icon: '⭐',
        title: '',
        description: ''
      }
      this.advantageModalVisible = true
    },

    handleAdvantageDelete(index) {
      const newAdvantages = this.advantages.filter((_, i) => i !== index)
      this.updateConfig({ advantages: newAdvantages })
    },

    async handleAdvantageSave() {
      try {
        await this.$refs.advantageForm.validate()
        this.saving = true

        const advantages = [...this.advantages]
        const advantageData = { ...this.advantageFormData }

        if (this.editingAdvantageIndex >= 0) {
          advantages[this.editingAdvantageIndex] = advantageData
        } else {
          advantages.push(advantageData)
        }

        this.updateConfig({ advantages })
        this.advantageModalVisible = false
        this.$message.success(
          this.editingAdvantageIndex >= 0 ? '优势更新成功，请点击保存按钮提交到数据库' : '优势添加成功，请点击保存按钮提交到数据库'
        )
        this.$emit('change')
      } catch (error) {
        // 优势保存失败时静默处理
      } finally {
        this.saving = false
      }
    },

    handleAdvantageModalClose() {
      this.advantageModalVisible = false
      this.editingAdvantageIndex = -1
      this.advantageFormData = {
        icon: '',
        title: '',
        description: ''
      }
    },

    updateConfig(configUpdate, silent = false) {
      // 如果更新的是cases，直接更新到content根级别，与React项目保持一致
      if (configUpdate.cases) {
        this.$emit('update', { cases: configUpdate.cases })
      } else {
        const newConfig = { ...this.content.config, ...configUpdate }
        this.$emit('update', { config: newConfig })
      }
      // 只有在非静默模式下才触发change事件
      if (!silent) {
        this.$emit('change')
      }
    },

    // 初始化默认优势数据
    initDefaultAdvantages() {
      const defaultAdvantages = [
        {
          icon: '⚡',
          title: '快速响应',
          description: '24小时内响应，紧急情况优先处理'
        },
        {
          icon: '🔧',
          title: '专业技术',
          description: '资深技师团队，丰富维修经验'
        },
        {
          icon: '✅',
          title: '质量保证',
          description: '维修质量保证，售后服务完善'
        }
      ]
      // 使用静默模式，避免触发修改状态
      this.updateConfig({ advantages: defaultAdvantages }, true)
    }
  },
  mounted() {
    // 如果没有优势数据，初始化默认优势
    this.$nextTick(() => {
      if (this.advantages.length === 0) {
        this.initDefaultAdvantages()
      }
    })
  },
}
</script>

<style lang="scss" scoped>
.cases-page-editor {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 16px 20px;

  // 模拟React项目的space-y-8布局
  > * + * {
    margin-top: 32px;
  }

  // 页面标题样式 - 统一使用EditableContent组件的全局样式
  .page-title-section {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 2rem;
  }

  // 英雄区样式 - 模拟React的bg-gray-800 text-white py-16 rounded-lg
  .hero-section {
    background: #1f2937;
    color: white;
    padding: 64px 16px;
    border-radius: 8px;
    text-align: center;
    // 英雄区内容样式由EditableContent组件统一管理
  }

  // 筛选区域样式 - 模拟React的bg-white rounded-lg p-6 shadow-sm
  .filter-section {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .filter-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .filter-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        min-height: 24px;
      }
    }

    .filter-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .filter-tag-main {
        padding: 8px 16px;
        font-size: 16px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }

      .filter-tag-item {
        padding: 8px 16px;
        font-size: 16px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  // 案例展示区样式 - 模拟React的bg-white rounded-lg p-6 shadow-sm
  .cases-section {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .cases-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .cases-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
    }

    .cases-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 24px;

      @media (min-width: 768px) {
        grid-template-columns: repeat(3, 1fr);
      }

      .case-card {
        background: #f3f4f6;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        transition: all 0.2s;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .case-actions {
          position: absolute;
          top: 8px;
          right: 8px;
          z-index: 20;
          display: flex;
          gap: 4px;
          opacity: 1;
          transition: opacity 0.2s;
        }

        .case-image {
          height: 192px;
          background: #d1d5db;
          overflow: hidden;

          .case-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .case-img-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .placeholder-text {
              color: #6b7280;
            }
          }
        }

        .case-content {
          padding: 16px;

          .case-meta-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .case-category {
              display: inline-block;
              background: #dbeafe;
              color: #1e40af;
              font-size: 13px;
              padding: 3px 10px;
              border-radius: 4px;
            }

            .case-date {
              color: #6b7280;
              font-size: 12px;
            }
          }

          .case-title {
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 14px;
            color: #1f2937;
          }

          .case-description {
            font-size: 14px;
            color: #4b5563;
            margin-bottom: 8px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .case-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .case-tag {
              font-size: 14px;
              padding: 4px 8px;
              display: inline-flex;
              align-items: center;
              justify-content: center;
            }

            .tags-more {
              font-size: 14px;
              color: #6b7280;
            }
          }
        }
      }
    }
  }

  // 英雄区域样式
  .hero-section {
    .hero-preview {
      background: #1f2937;
      color: white;
      padding: 40px 20px;
      border-radius: 8px;
      text-align: center;

      .hero-content {
        // 英雄区内容样式由EditableContent组件统一管理
        .hero-title, .hero-subtitle {
          min-height: 40px;
        }
      }
    }
  }

  // CTA区域样式
  .cta-section {
    .cta-preview {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      color: white;
      padding: 40px 20px;
      border-radius: 8px;
      text-align: center;

      .cta-content {
        .cta-title {
          font-size: 28px;
          font-weight: bold;
          margin-bottom: 12px;
          min-height: 36px;
        }

        .cta-subtitle {
          font-size: 16px;
          margin-bottom: 20px;
          opacity: 0.9;
          min-height: 20px;
        }

        .cta-button-text {
          display: inline-block;
          background: rgba(255, 255, 255, 0.2);
          padding: 12px 24px;
          border-radius: 6px;
          font-weight: 600;
          min-height: 20px;
          min-width: 80px;
        }
      }
    }
  }

  .cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;

    .case-card {
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 16px;
      background: white;
      position: relative;
      transition: all 0.2s;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .case-actions-overlay {
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 10;
        display: flex;
        gap: 8px;
      }

      .case-image {
        margin-bottom: 12px;
        height: 200px;
        overflow: hidden;
        border-radius: 6px;

        .case-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .case-img-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: #f9fafb;
          color: #9ca3af;
          border: 1px dashed #d1d5db;

          i {
            font-size: 32px;
            margin-bottom: 8px;
          }

          p {
            margin: 0;
            font-size: 14px;
          }
        }
      }

      .case-content {
        .case-title {
          font-weight: 600;
          margin-bottom: 8px;
          color: #1f2937;
          font-size: 16px;
        }

        .case-description {
          color: #6b7280;
          font-size: 13px;
          margin-bottom: 12px;
          line-height: 1.5;
        }

        .case-meta {
          display: flex;
          gap: 12px;
          margin-bottom: 8px;
          font-size: 12px;

          .case-category {
            background: #eff6ff;
            color: #2563eb;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 13px;
          }

          .case-date {
            color: #6b7280;
          }
        }

        .case-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;

          .case-tag {
            margin: 0;
            font-size: 13px;
            padding: 4px 8px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }

  // 服务优势样式 - 模拟React的bg-gray-50 rounded-lg p-8
  .advantages-section {
    background: #f9fafb;
    border-radius: 8px;
    padding: 32px;

    .advantages-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;

      .advantages-title {
        font-size: 30px;
        font-weight: bold;
        color: #1f2937;
        min-height: 36px;
      }
    }

    .advantages-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 32px;

      @media (min-width: 768px) {
        grid-template-columns: repeat(3, 1fr);
      }

      .advantage-card {
        text-align: center;
        padding: 24px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        position: relative;
        transition: all 0.2s;

        &:hover {
          box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }

        .advantage-actions {
          position: absolute;
          top: 8px;
          right: 8px;
          z-index: 20;
          display: flex;
          gap: 4px;
          opacity: 1;
          transition: opacity 0.2s;
        }

        .advantage-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }

        .advantage-title {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 8px;
          color: #1f2937;
        }

        .advantage-description {
          color: #4b5563;
          line-height: 1.5;
        }
      }
    }
  }

  // CTA区域样式 - 模拟React的bg-blue-600 text-white rounded-lg p-12 text-center
  .cta-section {
    background: #2563eb;
    color: white;
    border-radius: 8px;
    padding: 48px;
    text-align: center;

    .cta-title {
      font-size: 30px;
      font-weight: bold;
      margin-bottom: 16px;
      min-height: 36px;
      display: block;
    }

    .cta-subtitle {
      font-size: 20px;
      margin-bottom: 32px;
      min-height: 28px;
      display: block;
    }

    .cta-button {
      background: white;
      color: #2563eb;
      padding: 12px 32px;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.2s;

      &:hover {
        background: #f3f4f6;
        color: #2563eb;
        text-decoration: none;
      }

      .cta-button-text {
        color: inherit;
        min-height: 20px;
      }
    }
  }
}

// 模态框样式
::v-deep .el-dialog {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #ebeef5;
  }
}

// 确保模态框正确显示，解决蒙版问题
::v-deep .el-dialog__wrapper {
  z-index: 2000 !important;
}

::v-deep .el-overlay {
  z-index: 2000 !important;
}

// 优势编辑模态框样式
.form-item-extra {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.icon-preview {
  margin-top: 8px;
  font-size: 14px;
  color: #666;

  .preview-icon {
    font-size: 20px;
    margin-left: 8px;
  }
}

// 分类管理模态框样式
.category-management {
  .category-input-section {
    margin-bottom: 16px;

    .category-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;
    }
  }

  .category-preview {
    background: #f9fafb;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;

    .preview-title {
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;
    }

    .preview-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .preview-tag {
        margin: 0;
      }

      .no-categories {
        color: #6b7280;
        font-size: 14px;
      }
    }
  }

  .category-tips {
    font-size: 14px;
    color: #6b7280;

    .tips-title {
      margin-bottom: 4px;
    }

    .tips-list {
      margin-left: 16px;
      list-style: disc;

      li {
        margin-bottom: 2px;
      }
    }
  }
}

/* 图片预览模态框样式 */
::v-deep .image-preview-modal {
  .el-dialog {
    max-width: 800px;
    min-width: 400px;
  }

  .el-dialog__body {
    padding: 20px;
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .el-dialog {
      width: 95% !important;
      max-width: none;
      min-width: none;
      margin: 0 auto;
    }
  }
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: zoom-out;
}

/* 案例图片悬停效果 */
.case-image {
  position: relative;
  cursor: pointer;

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border-radius: 6px;

    .overlay-icon {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transform: scale(0.8);
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

      i {
        font-size: 18px;
        color: #666;
      }
    }
  }

  &:hover {
    .image-overlay {
      background: rgba(0, 0, 0, 0.2);

      .overlay-icon {
        opacity: 1;
        transform: scale(1);
      }
    }
  }
}

.cursor-pointer {
  cursor: pointer;
}
</style>
