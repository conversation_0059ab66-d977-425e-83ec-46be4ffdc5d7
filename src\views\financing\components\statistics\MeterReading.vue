<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-09 14:00:29
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-21 17:38:38
 * @Description: 抄表应收
 -->
<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      show-selection
      @handleSelectionChange="handleSelectionChange"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="primary"
          icon="el-icon-edit"
          size="mini"
          @click="handleBulkEdit"
        >
          账款批量核销
        </el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-check"
          @click="clearSelection"
        >
          取消全选
        </el-button>
        <el-button
          v-auth="['@ums:manage:finance:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div v-if="statLoading" class="title-box-right">
          <div>应收总金额：{{ totalData?.totalAmount || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'info')"
          >
            详情
          </el-button>
          <el-button icon="el-icon-edit" @click="handleEdit(row, 'audit')">
            账款核销
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      :confirm-text="'确认核销'"
      :confirm-btn-loading="formLoading"
      width="30%"
      top="3%"
      @ok="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParams"
        :form-list="formColumns"
        :open-type="'edit'"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        @proSubmit="proSubmit"
      >
        <template #voucherImg>
          <ProUpload
            :file-list="formParams.voucherImg"
            :type="'edit'"
            :limit="3"
            style="padding-left: 0"
            @uploadSuccess="handleUploadSuccess"
            @uploadRemove="handleUploadRemove"
          />
        </template>
      </ProForm>
    </ProDialog>
    <ReceiptDetails ref="receiptDetails" />
    <BulkEdit
      ref="bulkEdit"
      :columns="bulkEditColumns"
      type="meter"
      @refresh="refresh(), clearSelection()"
    />
  </div>
</template>

<script>
import ReceiptDetails from "@/views/order/components/receiptDetails.vue";
import ProUpload from "@/components/ProUpload/index.vue";

import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { handleExcelExport } from "@/utils/exportExcel";
import {
  receivableMeterSummaryListApi,
  receivableBaseSummaryStatisticsApi,
  receivableMeterSummaryExportApi,
  receivableWriteOffApi,
} from "@/api/finance";
import BulkEdit from "@/views/financing/components/BulkEdit.vue";

export default {
  name: "MeterReading",
  components: {
    BulkEdit,
    ReceiptDetails,
    ProUpload,
  },
  data() {
    const _this = this;
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "receiptCode",
          title: "结算单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        // {
        //   dataIndex: "contractCode",
        //   title: "合同编号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 150,
        // },
        // {
        //   dataIndex: "contractName",
        //   title: "合同名称",
        //   isTable: true,
        //   // isSearch: true,
        //   // valueType: "input",
        //   minWidth: 120,
        // },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "customerCode",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "license",
          title: "营业执照名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "serType",
          title: "合约类型",
          isTable: true,
          formatter: (row) => row.serType?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
          ],
          // optionMth: () => dictTreeByCodeApi(1200),
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
          minWidth: 80,
        },
        // {
        //   dataIndex: "startDate",
        //   title: "合约开始时间",
        //   isTable: true,
        //   width: 120,
        // },
        // {
        //   dataIndex: "endDate",
        //   title: "合约截止时间",
        //   isTable: true,
        //   width: 120,
        // },
        // {
        //   dataIndex: "productName",
        //   title: "机器型号",
        //   isTable: true,
        //   minWidth: 100,
        // },
        // {
        //   dataIndex: "productIds",
        //   title: "机器型号",
        //   isSearch: true,
        //   valueType: "product",
        // },
        // {
        //   dataIndex: "machineNum",
        //   title: "机器编号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 140,
        // },
        // {
        //   dataIndex: "wasteType",
        //   title: "废张计算方式",
        //   isTable: true,
        //   formatter: (row) => row.wasteType?.label,
        //   width: 120,
        // },
        // {
        //   dataIndex: "statementDate",
        //   title: "账单生成日期",
        //   isTable: true,
        //   formatter: (row) =>
        //     row.statementDate ? `每月 ${row.statementDate} 号` : "/",
        //   width: 120,
        // },
        // {
        //   dataIndex: "createdAt",
        //   title: "账单生成日期",
        //   isTable: true,
        //   width: 150,
        // },
        {
          dataIndex: "amount",
          title: "账单金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "derateAmount",
          title: "折扣金额",
          isTable: true,
          minWidth: 100,
        },
        // {
        //   dataIndex: "status",
        //   title: "结算状态",
        //   isTable: true,
        //   formatter: (row) => row.settmentstatusStatus?.label,
        //   minWidth: 80,
        // },
        // {
        //   dataIndex: "totalAmount",
        //   title: "收款单总金额",
        //   isTable: true,
        //   minWidth: 120,
        // },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          fixed: "right",
          tableSlot: "actions",
          width: 160,
        },
      ],
      tableData: [],
      totalData: {},
      requestParameters: {},
      statLoading: false,
      exportLoading: false,
      // 账款核销
      dialogVisible: false,
      dialogTitle: "账款核销",
      formParams: {},
      formColumns: [
        {
          dataIndex: "license",
          title: "营业执照名称",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "code",
          title: "结算单号",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "amount",
          title: "账单金额",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "verifyReceiveAmount",
          title: "应收金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入应收金额",
              trigger: "change",
              validator: (rule, value, callback) => {
                if (!value) {
                  callback(new Error("请输入应收金额"));
                } else if (+value < 0) {
                  callback(new Error("应收金额不能为负数"));
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "verifyDiscountAmount",
          title: "折扣金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入折扣金额",
          //     trigger: "change",
          //     validator: (rule, value, callback) => {
          //       if (+value < 0) {
          //         callback(new Error("折扣金额不能小于0"));
          //       }
          //       callback();
          //     },
          //   },
          // ],
        },
        {
          dataIndex: "verifyActualAmount",
          title: "实收金额",
          isForm: true,
          valueType: "input",
          inputType: "number",
          formSpan: 24,
          prop: [
            {
              required: true,
              trigger: "change",
              validator: (rule, value, callback) => {
                if (!value) {
                  callback(new Error("请输入实收金额"));
                } else if (+value < 0) {
                  callback(new Error("实收金额不能小于0"));
                } else {
                  const deposit = parseFloat(
                    _this.formParams.verifyReceiveAmount
                  );
                  const discount = parseFloat(
                    _this.formParams.verifyDiscountAmount
                  );
                  const payable = deposit - (isNaN(discount) ? 0 : discount);

                  if (+value > payable) {
                    callback(new Error("实收金额不能大于应收金额减去折扣金额"));
                  }
                  if (+value < payable) {
                    callback(new Error("实收金额不能小于应收金额减去折扣金额"));
                  }
                }
                callback();
              },
            },
          ],
        },
        {
          dataIndex: "voucherImg",
          title: "上传凭证",
          isForm: true,
          formSlot: "voucherImg",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请上传凭证",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "remark",
          title: "备注",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          wordlimit: 255,
          formSpan: 24,
        },
      ],
      formLoading: false,
      bulkEditColumns: [
        {
          dataIndex: "receiptCode",
          title: "结算单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        // {
        //   dataIndex: "contractCode",
        //   title: "合同编号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 150,
        // },
        // {
        //   dataIndex: "contractName",
        //   title: "合同名称",
        //   isTable: true,
        //   // isSearch: true,
        //   // valueType: "input",
        //   minWidth: 120,
        // },
        // {
        //   dataIndex: "customerName",
        //   title: "店铺名称",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 140,
        // },
        // {
        //   dataIndex: "customerCode",
        //   title: "客户编号",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 100,
        // },
        // {
        //   dataIndex: "license",
        //   title: "营业执照名称",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 180,
        // },
        {
          dataIndex: "serType",
          title: "合约类型",
          isTable: true,
          formatter: (row) => row.serType?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
          ],
          // optionMth: () => dictTreeByCodeApi(1200),
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
          minWidth: 80,
        },

        {
          dataIndex: "amount",
          title: "账单金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "derateAmount",
          title: "折扣金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "verifyReceiveAmount",
          title: "应收金额",
          isTable: true,
          // fixed: "right",
          tableSlot: "verifyReceiveAmount",
          width: 150,
        },
        {
          dataIndex: "verifyDiscountAmount",
          title: "折扣金额",
          isTable: true,
          // fixed: "right",
          tableSlot: "verifyDiscountAmount",
          width: 150,
        },
        {
          dataIndex: "verifyActualAmount",
          title: "实收金额",
          isTable: true,
          // fixed: "right",
          tableSlot: "verifyActualAmount",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          fixed: "right",
          tableSlot: "actions",
          width: 120,
        },
      ],
      selectionData: [],
      tipLock: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      // const searchRange = [
      //   {
      //     startDate: null,
      //     endData: null,
      //     data: parameter.createdAt,
      //   },
      // ];
      // filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      // delete requestParameters.createdAt;
      this.requestParameters = requestParameters;
      receivableMeterSummaryListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      receivableBaseSummaryStatisticsApi({
        ...this.requestParameters,
        collectionType: 3,
      })
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    handleBulkEdit() {
      if (!this.selectionData.length) {
        this.$message.warning("请先选择要核销的订单");
        return;
      }
      this.$refs.bulkEdit.show(this.selectionData);
    },
    clearSelection() {
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },
    handleSelectionChange(rows) {
      this.$nextTick(() => {
        const firstRow = rows[0];
        const validRows = rows.filter((row) => {
          if (row.customerCode !== firstRow.customerCode) {
            this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(row, false);
            return true;
          }
          return false;
        });
        if (validRows.length > 0 && !this.tipLock) {
          this.$message.warning("请选择同一客户的收款单");
          this.tipLock = true;
          setTimeout(() => {
            this.tipLock = false;
          }, 2000);
        }
        this.selectionData = cloneDeep(rows);
      });
    },
    handleEdit(row, type) {
      if (type === "info") {
        if (!row.id) {
          return;
        }
        this.$refs.receiptDetails.show(row, "info");
      } else if (type === "audit") {
        this.formParams = {
          id: row.id,
          code: row.receiptCode,
          amount: row.amount,
          customerName: row.customerName,
          license: row.license,
        };
        this.dialogVisible = true;
        this.dialogTitle = `核销 【${row.customerName}】 账款`;
      }
    },
    handleDialogConfirm() {
      this.$refs.ProForm.handleSubmit();
    },
    proSubmit(val) {
      this.$confirm("此操作将核销当前客户的账单, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.formLoading = true;
        const args = {
          customerId: this.formParams.customerId,
          tradeOrderOrigin: "RECEIPT_ORDER",
          tradeOrderNumber: this.formParams.code,
          verifyReceiveAmount: this.formParams.verifyReceiveAmount, // 应收金额
          verifyDiscountAmount: this.formParams.verifyDiscountAmount, // 折扣金额
          verifyActualAmount: this.formParams.verifyActualAmount, // 实收金额
          voucherImg: this.formParams.voucherImg, // 实收金额
          remark: this.formParams.remark,
        };
        receivableWriteOffApi(filterParam(args))
          .then((res) => {
            this.$message.success("核销成功");
            this.handleDialogCancel();
            this.refresh();
          })
          .finally(() => {
            this.formLoading = false;
          });
      });
    },
    handleDialogCancel() {
      this.dialogVisible = false;
      this.formParams = {};
    },
    handleUploadSuccess(result) {
      if (!this.formParams.voucherImg) {
        this.$set(this.formParams, "voucherImg", []);
      }
      this.formParams.voucherImg.push(result);
    },
    handleUploadRemove(file) {
      const index = this.formParams.voucherImg.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParams.voucherImg.splice(index, 1);
    },
    handleExport() {
      this.$confirm("此操作将导出抄表应收款明细, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          receivableMeterSummaryExportApi,
          this.requestParameters,
          "抄表应收款明细",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
