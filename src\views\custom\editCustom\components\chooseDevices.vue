<template>
  <div class="app-container">
    <!-- 选择机器/选配件 -->
    <ProDialog
      :value="modelValue"
      :title="dialogTitle"
      width="70%"
      :top="'2%'"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProTable
        ref="ProTable"
        :row-key="(row) => row.machineNum"
        :query-param="queryParam"
        :local-pagination="localPagination"
        :columns="columns"
        :data="tableData"
        :show-selection="true"
        :height="400"
        @loadData="loadData"
        @handleSelected="handleSelectionChange"
      >
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { getMachinePageApi } from "@/api/store";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "ChooseDevices",
  model: {
    prop: "modelValue",
    event: "update:modelValue",
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    selectedData: {
      type: Array,
      default: () => [],
    },
    hostType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 选择机器/选配件
      dialogTitle: "选择更换机器/配件",
      queryParam: {},
      defaultQueryParam: {
        // status: ["ON_SALE"],
        // isSale: true,
      },
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 10,
      },
      columns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "originCode",
          title: "原机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          isSearch: false,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(2000),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "productIds",
          title: "机器型号",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "tagName",
          title: "标签型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "placeOrigin",
          title: "产地版本",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "electric",
          title: "供电电压",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 100,
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          minWidth: 80,
        },
        {
          dataIndex: "isSale",
          title: "是否上架",
          isTable: true,
          formatter: (row) => (row.isSale ? "已上架" : "未上架"),
          minWidth: 80,
        },
        {
          dataIndex: "blackWhiteCount",
          title: "黑白计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "colorCount",
          title: "彩色计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "fiveColourCounter",
          title: "五色计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "source",
          title: "来源",
          isTable: true,
          formatter: (row) => row.source?.label,
          minWidth: 100,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          minWidth: 80,
        },
        {
          dataIndex: "createdAt",
          title: "入库时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
      ],
      tableData: [],
      selectionData: [],
      flag: false,
    };
  },
  watch: {
    modelValue(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.ProTable.refresh();
          this.flag = true;
        });
      }
    },
  },
  mounted() {},
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      this.queryParam.hostType = this.hostType;
      const requestParameters = cloneDeep(this.queryParam);
      // requestParameters.status = ["ON_SALE"];
      // requestParameters.isSale = true;
      getMachinePageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
          if (this.flag) {
            this.$refs.ProTable.$refs.ProElTable.clearSelection();
            if (this.selectedData.length) {
              this.selectedData.forEach((row) => {
                this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
                  row,
                  true
                );
              });
            }
          }
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
          this.flag = false;
        });
    },
    handleDialogOk() {
      // 将selectionData中的id值清除
      this.$emit("confirmDispatch", this.selectionData);
      this.handleDialogCancel();
    },
    handleDialogCancel() {
      this.$emit("update:modelValue", false);
    },
    handleSelectionChange(rows,row) {
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
      this.$refs.ProTable.$refs.ProElTable.toggleRowSelection(row,true)
      this.selectionData = [row];
    },
  },
};
</script>

<style scoped lang="scss"></style>
