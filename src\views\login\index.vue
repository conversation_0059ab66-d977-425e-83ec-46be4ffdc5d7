<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-18 18:24:56
 * @Description: 登陆页面
 -->

<template>
  <div class="login-container">
    <div class="left">
      <div class="title1">"</div>
      <div class="title1">{{ proName }}</div>
    </div>
    <div class="right">
      <div class="login-main">
        <div class="img-title">
          <span>欢迎登录{{ proName }}</span>
        </div>
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          auto-complete="on"
          label-position="left"
        >
          <!-- <span class="label"> 用户名</span> -->
          <el-form-item prop="username">
            <img class="tips" src="../../assets/images/login/user.png" alt="" />
            <el-input
              ref="username"
              v-model="loginForm.username"
              placeholder="请输入账号"
              name="username"
              type="text"
              tabindex="1"
              auto-complete="on"
            />
          </el-form-item>
          <!-- <span class="label"> 密码</span> -->
          <el-form-item prop="password">
            <img class="tips" src="../../assets/images/login/pwd.png" alt="" />
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              show-password
              :type="passwordType"
              placeholder="请输入密码"
              name="password"
              tabindex="2"
              auto-complete="on"
              @keyup.enter.native="handleLogin"
            />
          </el-form-item>
          <!-- <span class="label"> 验证码</span> -->
          <div class="code-item">
            <el-form-item prop="captcha" class="code">
              <!-- <img class="tips" src="../../assets/images/login/code.png" alt="" /> -->
              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.captcha"
                type="text"
                placeholder="验证码"
                name="captcha"
                tabindex="3"
                auto-complete="on"
                @keyup.enter.native="handleLogin"
              />
            </el-form-item>
            <div class="codema">
              <img :src="captchaImg" @click="referCaptchaImg" />
            </div>
          </div>
          <div :loading="loading" class="login-btn" @click="handleLogin">
            登录
          </div>
          <!-- <div class="tips">
        <span style="margin-right: 20px">username: admin</span>
        <span> password: any</span>
      </div> -->
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import { validUsername } from "@/utils/validate";
import { captcha, key } from "@/api/user";
import setting from "@/config/setting.js";
const { proName } = setting;
export default {
  name: "Login",
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error("The password can not be less than 6 digits"));
      } else {
        callback();
      }
    };
    return {
      captchaImg: "",
      proName: proName,
      loginForm: {
        // username: "sa",
        // password: "1qaz@WSX",
        username: "",
        password: "",
        captcha: "",
        captchaToken: "",
        key: "",
        passwordToken: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入用户名" },
        ],
        password: [
          {
            required: true,
            trigger: "blur",
            validator: validatePassword,
            message: "请输入密码",
          },
        ],
        captcha: [{ required: true, trigger: "blur", message: "请输入验证码" }],
      },
      loading: false,
      passwordType: "password",
      redirect: undefined,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created() {
    // console.log(this.$route.token);
  },
  mounted() {
    this.referCaptchaImg();
  },
  methods: {
    referCaptchaImg() {
      captcha().then((res) => {
        this.loginForm.captchaToken = res.data.first;
        this.captchaImg = res.data.second;
      });
    },

    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          key()
            .then((res) => {
              this.loginForm.passwordToken = res.data.first;
              this.loginForm.key = res.data.second;
              this.loading = true;
              this.$store
                .dispatch("user/login", this.loginForm)
                .then(() => {
                  this.$store.dispatch("user/getRouter").then(() => {
                    // this.$router.push({ path: this.redirect || "/" });
                    this.$router.push({ path: "/" });
                    localStorage.removeItem("islogin");
                    this.loading = false;
                  });
                })
                .catch((err) => {
                  this.loading = false;
                  this.referCaptchaImg();
                });
            })
            .catch();
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
$bg: #fff;
$light_gray: #fff;
$cursor: #fff;

.login-container {
  height: 50px;
  width: 380px;

  .el-input {
    display: inline-block;
    height: 40px;
    margin: 2px 0;
    width: 89%;
    padding: 0 10px;

    .el-input__inner {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 10px 5px 10px 0px;
      color: #000;
      height: 45px;
      // margin: 7px 0;
      font-size: 14px;
      caret-color: #000;
      width: 100%;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1px #fff inset !important;
        -webkit-text-fill-color: #000 !important;
      }
    }

    input:-internal-autofill-selected {
      background: transparent !important;
    }
  }

  .el-form-item {
    width: 100%;
    border-bottom: 1px solid #dadbdd;
    // border-radius: 10px;
    // background: transparent;
    height: 45px;
    margin: 0 auto 22px auto;
  }

  .code-item {
    display: flex;
    align-items: center;
    width: 100%;
    margin: auto;

    .el-form-item {
      width: 80%;
      margin: 0;

      .el-input {
        width: 80%;

        .el-input__inner {
          width: 100%;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
$bg: #fff;
$dark_gray: #889aa4;
$light_gray: #eee;

.label {
  color: #000;
  text-align: left;
  display: inline-block;
  width: 80%;
  margin: 5px 0;
}

.login-container {
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;
  overflow: hidden;

  .login-main {
    border-radius: 10px;
    position: absolute;
    top: 55%;
    left: 40%;
    padding: 0 100px;
    transform: translate(-50%, -50%);
    padding: 0 58px;
  }

  .login-form {
    position: relative;
    width: 500px;
    max-width: 100%;
    overflow: hidden;

    border-radius: 10px;
    padding: 40px 0;
  }

  .tips {
    width: 18px;
    float: left;
    margin: 10px;
  }

  .svg-container {
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
    line-height: 1;
  }

  .title-container {
    background-image: url("../../assets/images/login/tit.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    text-align: center;
    width: 360px;
    height: 90px;
    margin: 15px auto;
    padding: 0;

    .title {
      display: inline-block;
      font-size: 30px;
      margin: 0;
      letter-spacing: 5px;
      font-family: PingFang SC;
      text-shadow: 0 0 5px white, 0 0 10px #0ba1f8, 0 0 15px #0ba1f8,
      0 0 20px white;
      font-weight: 400;
      color: #fff;
      padding: 10px 0;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 20px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}

.codema {
  width: 150px;
  height: 44px;
  cursor: pointer;
  overflow: hidden;
  border-bottom: 1px solid #dadbdd;

  img {
    width: 100%;
    height: 100%;
  }
}

.login-btn {
  height: 80px;
  line-height: 70px;
  width: 80%;
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;

  background-image: url("../../assets/images/login/4.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin: 30px auto;
  text-align: center;
  letter-spacing: 5px;
  cursor: pointer;
}

.img-title {
  text-align: left;
  width: 100%;

  img {
    width: 100%;
  }

  span {
    font-size: 24px;
    font-weight: 400;
    color: #000;
    line-height: $top-header-height;
    font-weight: bold;
  }
}

.title1 {
  text-align: left;
  font-size: 60px;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  color: #fffefe;
  margin: 100px 0 0 40px;
}

.left {
  width: 40%;
  height: 100vh;
  background: url(@/assets/images/login/bg.png) no-repeat;
  background-size: 100% 100%;
  float: left;
}

.right {
  width: 60%;
  height: 100vh;
  background: #fff;
  float: left;
  position: relative;
}
</style>
