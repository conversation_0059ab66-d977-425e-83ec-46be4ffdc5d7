<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-25 14:02:45
 * @Description: 耗材销售
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="耗材销售单" name="first" lazy>
        <Orders />
      </el-tab-pane>
      <el-tab-pane label="耗材退货单" name="second" lazy>
        <Return />
      </el-tab-pane>
      <el-tab-pane label="配送方式变更记录" name="fourth" lazy>
        <DispatchChange />
      </el-tab-pane>
      <el-tab-pane label="销售明细" name="third" lazy>
        <OrderDetail />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Orders from "@/views/order/components/saleOrder/orders.vue";
import Return from "@/views/order/components/saleOrder/return.vue";
import OrderDetail from "@/views/order/components/saleOrder/orderDetail.vue";
import DispatchChange from "@/views/order/components/saleOrder/dispatchChange.vue";
export default {
  name: "SaleOrder",
  components: { Orders, Return, OrderDetail, DispatchChange },
  data() {
    return {
      activeName: "first",
    };
  },
};
</script>

<style scoped lang="scss"></style>
