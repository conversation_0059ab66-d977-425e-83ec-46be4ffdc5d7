<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-07-03 14:08:48
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 14:45:57
 * @Description: 机器库存
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :layout="{ labelWidth: '80px' }"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-warning-outline" @click="handleEdit(row)">
            详情
          </el-button>
          <el-button
            v-auth="['@ums:manage:repertory:download']"
            :loading="exportLoading"
            icon="el-icon-download"
            @click="handleExport(row, false)"
          >
            导出库存数据
          </el-button>
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="90%"
      no-footer
      @cancel="closeDrawer"
    >
      <ProTable
        ref="detailTable"
        :query-param="detailQueryParam"
        :local-pagination="detailLocalPagination"
        :columns="detailColumns"
        :data="detailData"
        @loadData="loadDetailData"
      >
        <template #btn>
          <el-button
            v-auth="['@ums:manage:repertory:download']"
            type="success"
            icon="el-icon-download"
            :loading="exportLoading"
            size="mini"
            @click="handleExport(null, true)"
          >
            导出库存数据
          </el-button>
          <div v-if="statLoading" class="title-box-right">
            <div>不含税总金额： {{ totalData?.noTaxAmount || 0 }}</div>
            <div>含税总金额： {{ totalData?.taxAmount || 0 }}</div>
          </div>
          <div v-else class="title-box-right" style="gap: 5px">
            <i class="el-icon-loading"></i>
            正在加载统计数据
          </div>
        </template>
      </ProTable>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  exportMachineEndOfMonthApi,
  getConsumableEndOfMonthTotalApi,
  getMachineEndOfMonthApi,
  getMachineEndOfMonthDetailApi,
  getMachineEndOfMonthTotalApi,
  warehouseListApi,
} from "@/api/store";
import { handleExcelExport } from "@/utils/exportExcel";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "MachineStock",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "monthly",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        // {
        //   dataIndex: "warehouseId",
        //   title: "归属仓库",
        //   isTable: true,
        //   formatter: (row) => row?.warehouseName,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [],
        //   optionMth: () => warehouseListApi({ status: 1401 }),
        //   optionskey: {
        //     label: "name",
        //     value: "id",
        //   },
        // },
        {
          dataIndex: "num",
          title: "库存数量",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "noTaxAmount",
          title: "不含税金额",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "taxAmount",
          title: "含税金额",
          isTable: true,
          align: "center",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 210,
        },
      ],
      tableData: [],
      exportLoading: false,
      // drawer
      drawerVisible: false,
      drawerTitle: "月末库存",
      detailQueryParam: {},
      detailLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      detailColumns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          minWidth: 120,
        },
        {
          dataIndex: "hostTypes",
          title: "主机类型",
          isSearch: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(2000),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "originCode",
          title: "原机器编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "productIds",
          title: "机器型号",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "tagName",
          title: "标签型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },

        {
          dataIndex: "manufacturerCode",
          title: "供应商编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isTable: true,
          // isSearch: true,
          // valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 80,
        },
        {
          dataIndex: "deviceOns",
          title: "设备新旧",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1100),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(2500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          minWidth: 80,
        },
        {
          dataIndex: "deviceStatusList",
          title: "设备状态",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6600),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
          minWidth: 100,
        },
        {
          dataIndex: "colorCounter",
          title: "彩色计数器",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
          minWidth: 100,
        },
        {
          dataIndex: "fiveColourCounter",
          title: "五色计数器",
          isTable: true,
          // isSearch: true,
          // valueType: "inputRange",
          minWidth: 100,
        },
        {
          dataIndex: "noTaxAmount",
          title: "不含税金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "taxAmount",
          title: "含税金额",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "source",
          title: "来源",
          isTable: true,
          formatter: (row) => row.source?.label,
          minWidth: 80,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "已入库",
              value: "INVENTORY",
            },
            // {
            //   label: "已售",
            //   value: "OVER_SALE",
            // },
            {
              label: "维修中",
              value: "REPAIR",
            },
            {
              label: "租赁",
              value: "RENT",
            },
            // {
            //   label: "报损",
            //   value: "WITHOUT",
            // },
            {
              label: "采购退货中",
              value: "APPLY_RETURN",
            },
            {
              label: "采购退货",
              value: "RETURN",
            },
          ],
        },
        {
          dataIndex: "createdAt",
          title: "入库时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
      ],
      detailData: [],
      detailRequestParameters: {},
      totalData: {},
      statLoading: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startMonthly: null,
          endMonthly: null,
          data: parameter.monthly,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.monthly;
      getMachineEndOfMonthApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    loadDetailData(parameter) {
      this.detailQueryParam = filterParam(
        Object.assign({}, this.detailQueryParam, parameter)
      );
      const searchRange = [
        {
          startTime: null,
          endTime: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.detailQueryParam, searchRange);
      delete this.detailQueryParam.createdAt;
      this.detailRequestParameters = cloneDeep(this.detailQueryParam);
      getMachineEndOfMonthDetailApi(this.detailRequestParameters)
        .then((res) => {
          this.detailData = res.data.rows;
          this.detailLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.detailTable &&
            (this.$refs.detailTable.listLoading = false);
        });
      this.getTotalData();
    },
    getTotalData() {
      this.statLoading = false;
      getMachineEndOfMonthTotalApi(this.detailRequestParameters)
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    handleEdit(row) {
      this.drawerVisible = true;
      this.detailQueryParam.monthly = row.monthly;
      this.detailLocalPagination = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.$nextTick(() => {
        this.$refs.detailTable.refresh();
      });
    },
    closeDrawer() {
      this.drawerVisible = false;
    },
    handleExport(row, isParams) {
      this.$confirm("此操作将导出当月库存数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          exportMachineEndOfMonthApi,
          isParams ? this.detailRequestParameters : { monthly: row.monthly },
          `${
            isParams ? this.detailRequestParameters.monthly : row.monthly
          }月库存数据`,
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
