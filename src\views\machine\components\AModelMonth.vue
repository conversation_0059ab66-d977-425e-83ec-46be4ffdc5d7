<template>
  <div class="main">
    <ProTable
      ref="ProTable"
      :layout="{ labelWidth: '80px' }"
      :columns="columns"
      :data="tableData"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>机器台数：{{ totalData?.machineCount || 0 }}</div>
          <div>黑白总印量：{{ totalData?.blackWhiteCount || 0 }}</div>
          <div>彩色总印量：{{ totalData?.colorCount || 0 }}</div>
          <div>总印量：{{ totalData?.totalCount || 0 }}</div>
        </div>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 100%"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { productAllApi } from "@/api/dispose";
import {
  getPrintStatisticsByTypeApi,
  getPrintStatisticsByTypePageApi,
} from "@/api/machine";

export default {
  name: "AModelMonth",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "machine",
          title: "产品树",
          isTable: true,
        },
        {
          dataIndex: "productIds",
          title: "产品树",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "series",
          title: "系列",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },

        {
          dataIndex: "blackWhiteCount",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorCount",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "totalCount",
          title: "总印量",
          isTable: true,
        },
        {
          dataIndex: "dataSource",
          title: "统计方式",
          isTable: true,
          formatter: (row) => row.dataSource?.label,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "计数器上报",
              value: "IOT",
            },
            {
              label: "维修工单",
              value: "REPAIR",
            },
          ],
        },
        // {
        //   dataIndex: "status",
        //   title: "统计状态",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [],
        // },
        {
          dataIndex: "finalDate",
          title: "截止时间",
          isTable: true,
        },
      ],
      tableData: [],
      totalData: {},
      productIdName: "",
      options: [],
    };
  },
  mounted() {
    this.refresh();
    this.getProductThird();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.currMonth,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.currMonth;
      getPrintStatisticsByTypeApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData(requestParameters);
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        this.queryParam.productIds.push(el[el.length - 1]);
      });
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
      });
    },
    getTotalData(params) {
      getPrintStatisticsByTypePageApi(params).then((res) => {
        this.totalData = res.data;
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
      // this.$refs.ProTable.listLoading = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
