<!--
 * @Author: wskg
 * @Date: 2025-02-10 18:15:48
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:58:44
 * @Description: 客户端掉线查询
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :data="tableData"
      :columns="columns"
      @loadData="loadData"
    ></ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "Monitor",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          formatter: (row) => row.deviceGroup?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(700),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "productName",
          title: "主机型号",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "isReport",
          title: "是否上报计数器",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "isReport",
          title: "上报计数器",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: 1,
            },
            {
              label: "否",
              value: 0,
            },
          ],
        },
        {
          dataIndex: "notReport",
          title: "未上报天数",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
          minWidth: 120,
        },
        {
          dataIndex: "isFail",
          title: "是否故障",
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: 1,
            },
            {
              label: "否",
              value: 0,
            },
          ],
          minWidth: 120,
        },
        {
          dataIndex: "isPowder",
          title: "是否缺粉",
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: 1,
            },
            {
              label: "否",
              value: 0,
            },
          ],
          minWidth: 120,
        },
        {
          dataIndex: "createdAt",
          title: "上报时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.status?.label,
          valueType: "select",
          option: [],
          minWidth: 100,
        },
      ],
      tableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
