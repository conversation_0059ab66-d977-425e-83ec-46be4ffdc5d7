<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 17:17:08
 * @Description: 品牌产品树 - 品牌管理
 -->

<template>
  <div class="app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :show-search="false"
      :show-index="false"
      height="70vh"
      :query-param="queryParam"
      :columns="columns"
      :data="tableData"
      default-expand-all
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增品牌
        </el-button>
      </template>

      <template #actions="slotProps">
        <div class="fixed-width">
          <el-button
            v-if="slotProps.row.fullIdPath.split('/').length !== 4"
            type="primary"
            size="mini"
            icon="el-icon-circle-plus"
            @click="handleAdd(slotProps.row)"
          >
            添加
          </el-button>

          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            详情
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>

          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>

    <!-- 新增、编辑、详情框  -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType === 'info'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>
<script>
import {
  productSerialApi,
  productAddApi,
  productDelApi,
  productEditApi,
  productAllApi,
} from "@/api/dispose";

import { cloneDeep } from "lodash";

export default {
  name: "PBrand",
  data() {
    return {
      tableData: [],
      queryParam: {},
      columns: [
        {
          dataIndex: "name",
          title: "品牌/产品树/系列",
          isTable: true,
        },

        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 350,
        },
      ],
      actionUrl: "",
      uploadlLading: false,
      uploadDialogVisible: false,
      //新增
      methodType: "add",
      confirmLoading: false,

      form: { parentId: "" },
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: { lastLevel: 0 },
      formcolumns: [
        {
          dataIndex: "name",
          isForm: true,
          title: "名称",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入名称",
              trigger: "change",
            },
          ],
        },
      ],
      //人员管理
      roleId: null,
      dialogTitleR: "",
      dialogVisibleR: false,
      dialogVisibleU: false,
    };
  },

  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    querySearch(queryString, cb) {
      productAllApi({ name: queryString }).then((res) => {
        const restaurants = res.data.rows;
        cb(restaurants);
      });
    },

    handleSelect(item) {
      this.$set(this.queryParam, "fullIdPath", item.fullIdPath);
      console.log(item);
    },
    //加载表格
    loadData(parameter) {
      productSerialApi()
        .then((res) => {
          this.tableData = res.data;
          this.localPagination.total = parseInt(res.data.total);

          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;

      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
        data?.id ? (this.form.parentId = data.id) : (this.form.parentId = "0");
        if (this.form.parentId == "0") {
          this.dialogTitle = "新增品牌";
        } else if (data.fullIdPath.split("/").length == 2) {
          this.dialogTitle = "新增产品树";
        } else if (data.fullIdPath.split("/").length == 3) {
          this.dialogTitle = "新增系列";
        } else if (data.fullIdPath.split("/").length == 4) {
          this.dialogTitle = "新增型号";
        }
      });
    },
    //响应新增
    create() {
      productAddApi(this.form)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑 - " + row.name;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.methodType = "edit";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应编辑
    update() {
      productEditApi(this.form)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.name;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        productDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
    // ================
    handleMember(row) {
      this.dialogTitleR = "人员管理 - " + row.name;
      this.dialogVisibleR = true;
      this.roleId = row.id;
      setTimeout(() => {
        this.$refs.ChooseUser.$refs.ProTable.refresh();
      }, 200);
    },
    handleMemberAdd() {
      this.dialogVisibleU = true;
      setTimeout(() => {
        this.$refs.UserList.$refs.ProTable.refresh();
      }, 200);
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
