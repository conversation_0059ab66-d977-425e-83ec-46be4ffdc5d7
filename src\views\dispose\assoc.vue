<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:51
 * @Description: 通用关联表
 -->
<template>
  <div class="view app-container assoc">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      :local-pagination="localPagination"
      :data="tableData"
      default-expand-all
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增关联
        </el-button>
      </template>

      <template #oemNumber1="slotProps">
        {{ slotProps.row.parentId == 0 ? slotProps.row.oemNumber : "" }}
      </template>
      <template #actions="slotProps">
        <span v-if="slotProps.row.parentId == 0" class="fixed-width">
          <!-- <el-button type="primary" size="mini" icon="el-icon-warning-outline" @click="handleInfo(slotProps.row)">
            详情
          </el-button> -->
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>

    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="90%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType == 'info'"
      :confirm-text="methodType === 'add' ? '确认新增' : '保存'"
      @ok="handleDialogOk"
      @cancel="closeDialog"
    >
      <div class="title-box">
        <div class="title" style="width: calc(55% + 30px)">零件列表</div>
        <!-- <div style="width: 56px"></div> -->
        <div class="title">已选择零件</div>
      </div>
      <!-- <div style="margin-bottom:10px">
        <el-input style="width:180px" clearable="" v-model="input1" placeholder="请输入OEM编号"></el-input>
        <el-input style="width:180px;margin-left: 10px;" clearable="" v-model="input2" placeholder="请输入零件中文名称"></el-input>
        <el-button style="margin-left: 10px;" type="primary" @click="getPartList">搜索</el-button>
      </div> -->
      <div class="oem-box">
        <ProTable
          ref="ProTable2"
          style="width: 55%"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :layout="{ labelWidth: '120px' }"
          :columns="columns2"
          show-pagination
          row-key="id"
          :local-pagination="localPagination2"
          :data="partList"
          sticky
          :show-setting="false"
          :height="500"
          show-selection
          @loadData="loadData2"
          @handleSelectionChange="handlePartSelectChange"
        ></ProTable>
        <!-- <el-table ref="partTable" :data="partList" tooltip-effect="dark" height="500" style="width: 100%"
          @selection-change="handlePartSelectChange">

          <el-table-column type="selection" width="55" />

          <el-table-column v-for="(item, index) in columns1" :key="index" :label="item.title">
            <template slot-scope="scope">
              <div>{{ scope.row[item.dataIndex] }}</div>
              <slot v-if="item.tableSlot" :name="item.tableSlot" :scope="scope" />
            </template>
          </el-table-column>
        </el-table> -->

        <div class="button-box">
          <!-- <el-button
            type="primary"
            icon="el-icon-arrow-right"
            @click="addChoosePart"
          ></el-button>
          <el-button
            type="primary"
            icon="el-icon-arrow-left"
            @click="removeChoosePart"
          ></el-button> -->
        </div>

        <el-table
          ref="choosePartTable"
          :data="choosePartList"
          tooltip-effect="light"
          style="width: 100%; height: 500px; margin-top: 175px"
          @selection-change="handleChoosePartSelectChange"
        >
          <!-- <el-table-column type="selection" /> -->
          <el-table-column type="index" />

          <el-table-column
            v-for="(item, index) in columns1"
            :key="index"
            :label="item.title"
          >
            <template slot-scope="scope">
              <div v-if="item.dataIndex === 'action'">
                <div class="fixed-width">
                  <el-button
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                    @click="removeChoosePart(scope.row)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
              <div v-else>{{ scope.row[item.dataIndex] }}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formColumns"
        style="margin-top: 20px"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '180px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
        <template #chooseId>
          <!-- <el-select v-model="form.chooseId" :disabled="methodType !== 'add'"> -->
          <el-select v-model="form.chooseId">
            <el-option
              v-for="(item, index) in choosePartList"
              :key="index"
              :label="item.oemNumber"
              :value="item.id"
            ></el-option>
          </el-select>
        </template>
      </ProForm>
    </ProDrawer>
  </div>
</template>
<script>
import {
  getAssociationPageApi,
  getAssociationInfoApi,
  addAssociationApi,
  deleteAssociationApi,
  updateAssociationApi,
  partListApi,
} from "@/api/dispose";

import { isEmpty, cloneDeep } from "lodash";
import { Message, MessageBox } from "element-ui";

import { deepClone, filterName, getAllParentArr } from "@/utils";

export default {
  name: "Assoc",
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      input1: "",
      input2: "",
      dialogVisibleOem: false,
      // 列表
      tableData: [],

      queryParam: {},
      columns: [
        {
          dataIndex: "oemNumber1",
          title: "通用编号",
          isTable: true,
          tableSlot: "oemNumber1",
          // formatter: (row) => row.oemNumber,
        },
        // {
        //   dataIndex: 'name1',
        //   title: '适用品牌',
        //   isTable: true,
        // },
        // {
        //   dataIndex: 'name2',
        //   title: '适用系列',
        //   isTable: true,
        // },
        // {
        //   dataIndex: 'name3',
        //   title: '适用机型',
        //   isTable: true,
        // },
        // {
        //   dataIndex: 'goodsNumber',
        //   title: '物品编号',
        //   isTable: true,
        // },
        {
          dataIndex: "name",
          title: "OEM编号",
          isSearch: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 180,
        },
        // {
        //   dataIndex: 'ch',
        //   title: '物品名称',
        //   isTable: true,
        // },
        {
          dataIndex: "ch",
          title: "零件名称",
          isTable: true,
        },
        {
          dataIndex: "partName",
          title: "零件名称",
          isSearch: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "en",
          title: "零件英文名称",
          isTable: true,
        },
        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 180,
        },
      ],
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      actionUrl: "",
      uploadlLading: false,
      uploadDialogVisible: false,
      //新增
      methodType: "add",
      confirmLoading: false,

      form: {},
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      formColumns: [
        {
          dataIndex: "chooseId",
          title: "设置通用编号",
          isForm: true,
          formSlot: "chooseId",
          prop: [
            {
              required: true,
              message: "请设置通用编号",
              trigger: "change",
            },
          ],
        },
      ],
      tableData1: [],
      columns1: [
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 180,
        },
        // {
        //   dataIndex: 'id',
        //   title: '零件编号',
        //   isTable: true,
        //   isSearch: true,
        //   span: 4,
        //   valueType: 'input',
        // },
        {
          dataIndex: "ch",
          title: "零件名称",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
        },
      ],
      partList: [],
      partSelection: [],
      choosePartList: [],
      choosePartSelection: [],

      localPagination2: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },

      columns2: [
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 180,
          isSearch: true,
          clearable: true,
          span: 4,
          valueType: "input",
          searchSpan: 8,
        },
        {
          dataIndex: "ch",
          title: "零件中/英名称",
          isSearch: true,
          clearable: true,
          span: 4,
          valueType: "input",
          searchSpan: 8,
        },
        {
          dataIndex: "ch",
          title: "零件中文名称",
          isTable: true,
        },
        {
          dataIndex: "en",
          title: "零件英文名称",
          isTable: true,
        },
      ],
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    this.refresh();
  },
  methods: {
    //加载表格
    async loadData(params) {
      try {
        const result = await getAssociationPageApi(params);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable.listLoading = false;
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    setOem(data) {
      this.$set(this.form, "numberOem", data.oemNumber);
      this.$set(this.form, "partId", data.id);
      this.$set(this.form, "partName", data.ch);
      this.deviceProductTree = [];
      partProductTreeApi(data.id).then((res) => {
        console.log(res);
        this.deviceProductTree = res.data;
      });
      this.dialogVisibleOem = false;
      // this.$set(this.form, 'partName', data.ch)
      // this.$set(this.form, 'partName', data.ch)
      // this.$set(this.form, 'partName', data.ch)
      // this.$set(this.form, 'partName', data.ch)
    },
    loadData1() {
      this.tableData1 = [
        {
          id: "12987122",
          name1: "好滋好味鸡蛋仔1",
          category: "江浙小吃、小吃零食",
          desc: "荷兰优质淡奶，奶香浓而不腻",
          address: "上海市普陀区真北路",
          shop: "王小虎夫妻店",
          shopId: "10333",
          children: [
            {
              id: 31,
              date: "2016-05-01",
              name: "王小虎",
              address: "上海市普陀区金沙江路 1519 弄",
            },
            {
              id: 32,
              date: "2016-05-01",
              name: "王小虎",
              address: "上海市普陀区金沙江路 1519 弄",
            },
          ],
        },
        {
          id: "12987123",
          name1: "好滋好味鸡蛋仔",
          category: "江浙小吃、小吃零食",
          desc: "荷兰优质淡奶，奶香浓而不腻",
          address: "上海市普陀区真北路",
          shop: "王小虎夫妻店",
          shopId: "10333",
          children: [
            {
              id: 31,
              date: "2016-05-01",
              name: "王小虎",
              address: "上海市普陀区金沙江路 1519 弄",
            },
            {
              id: 32,
              date: "2016-05-01",
              name: "王小虎",
              address: "上海市普陀区金沙江路 1519 弄",
            },
          ],
        },
        {
          id: "12987125",
          name1: "好滋好味鸡蛋仔",
          category: "江浙小吃、小吃零食",
          desc: "荷兰优质淡奶，奶香浓而不腻",
          address: "上海市普陀区真北路",
          shop: "王小虎夫妻店",
          shopId: "10333",
          children: [
            {
              id: 31,
              date: "2016-05-01",
              name: "王小虎",
              address: "上海市普陀区金沙江路 1519 弄",
            },
            {
              id: 32,
              date: "2016-05-01",
              name: "王小虎",
              address: "上海市普陀区金沙江路 1519 弄",
            },
          ],
        },
        {
          id: "12987126",
          name1: "好滋好味鸡蛋仔",
          category: "江浙小吃、小吃零食",
          desc: "荷兰优质淡奶，奶香浓而不腻",
          address: "上海市普陀区真北路",
          shop: "王小虎夫妻店",
          shopId: "10333",
          children: [
            {
              id: 31,
              date: "2016-05-01",
              name: "王小虎",
              address: "上海市普陀区金沙江路 1519 弄",
            },
            {
              id: 32,
              date: "2016-05-01",
              name: "王小虎",
              address: "上海市普陀区金沙江路 1519 弄",
            },
          ],
        },
      ];
      this.$refs.ProTable1.listLoading = false;
    },

    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closeDialog() {
      this.dialogVisible = false;
      this.choosePartList = [];
      this.partSelection = [];
    },
    //触发新增
    handleAdd(data) {
      this.methodType = "add";
      this.resetFrom();
      this.choosePartList = [];
      this.dialogVisible = true;
      // this.getPartList()

      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
        this.queryParam = {};
        this.$refs.ProTable2.refresh();
        this.dialogTitle = "新增通用关联";
      });
    },
    //响应新增
    create() {
      if (this.choosePartList.length === 0) {
        Message.error("请选择零件");
      }
      const associationIds = this.choosePartList.map((item) => item.id);
      addAssociationApi({ ...this.form, associationIds })
        .then(() => {
          this.$message.success("新增成功");
          this.closeDialog();
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    async handleUpdate(row) {
      const result = await getAssociationInfoApi(row.id);
      this.dialogTitle = "编辑 - " + row.oemNumber;
      this.resetFrom();
      this.queryParam = {};
      this.form = {
        chooseId: row.id,
        dataId: row.id,
      };
      this.choosePartList = result.data.children;
      this.methodType = "edit";
      this.dialogVisible = true;
      this.$nextTick(async (e) => {
        this.$refs["proform"].resetFormParam();
        await this.$refs.ProTable2.refresh();
      });
    },
    //响应编辑
    update() {
      if (this.choosePartList.length === 0) {
        Message.error("请选择零件");
      }
      const associationIds = this.choosePartList.map((item) => item.id);
      updateAssociationApi({ ...this.form, associationIds })
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.closeDialog();
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.oemNumber;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteAssociationApi(data.id).then(() => {
          this.$message.success("删除成功");
          this.localPagination = {
            pageNumber: 1,
            pageSize: 10,
            total: 0,
          };
          this.$nextTick(() => {
            this.$refs.ProTable.refresh();
          });
        });
      });
    },
    loadData2(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      partListApi(requestParameters)
        .then((res) => {
          this.partList = res.data.rows;
          this.localPagination2 = {
            pageNumber: parameter.pageNumber,
            pageSize: parameter.pageSize,
            total: +res.data.total,
          };
          // 备选状态赋值 toggleRowSelection
          // this.$refs.ProTable2.$refs.ProElTable.clearSelection()
          console.log(this.partList, this.choosePartList);
          this.partList.forEach((item) => {
            const target = this.choosePartList.find(
              (part) => part.id === item.id
            );
            this.$refs.ProTable2.$refs.ProElTable.toggleRowSelection(
              item,
              !!target
            );
          });
        })
        .finally(() => {
          this.$refs.ProTable2
            ? (this.$refs.ProTable2.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    async getPartList() {
      try {
        const result = await partListApi({
          pageNumber: 1,
          pageSize: 9999,
          oemNumber: this.input1,
          ch: this.input2,
        });
        if (result.code === 200) {
          this.partList = result.data.rows;
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    addChoosePart() {
      this.choosePartList.push(...this.partSelection);
      this.partList = this.removeChildArray(this.partList, this.partSelection);
      this.$refs.partTable.clearSelection();
    },
    removeChoosePart(row) {
      // 检验是否要取消正在使用的oem
      MessageBox.confirm("确定取消选择？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if (row.id === this.form.chooseId) {
            this.$message.warning("该OEM正在使用，无法取消");
          } else {
            // this.partList.push(...item)
            this.choosePartList = this.removeChildArray(this.choosePartList, [
              row,
            ]);
            // this.$refs.choosePartTable.clearSelection()
          }
        })
        .catch(() => {})
        .finally(() => {});
    },
    handleChoosePartSelectChange(selection) {
      this.choosePartSelection = selection;
    },
    handlePartSelectChange(selection) {
      console.log(1111);
      const deleteItem = [];
      const addItem = [];
      const partSelectionLength = this.partSelection.length;
      const selectionLength = selection.length;
      console.log(partSelectionLength, selectionLength);
      if (selectionLength > partSelectionLength) {
        selection.forEach((item) => {
          const target = this.partSelection.find((part) => part.id === item.id);
          !target && addItem.push(item);
        });
      } else {
        this.partSelection.forEach((item) => {
          const target = selection.find((part) => part.id === item.id);
          !target && deleteItem.push(item);
        });
      }
      if (addItem.length > 0) {
        const canAdd = addItem.filter(
          (item) =>
            !this.choosePartList.find((choosePart) => choosePart.id === item.id)
        );
        this.choosePartList.push(...canAdd);
      }
      if (deleteItem.length > 0) {
        const cantDelete = deleteItem.find(
          (item) => item.id === this.form.chooseId
        );
        if (cantDelete) {
          this.$message.warning("该OEM正在使用，无法取消");
          deleteItem.forEach((item) => {
            this.$refs.ProTable2.$refs.ProElTable.toggleRowSelection(
              item,
              true
            );
          });
          return;
        }
        this.choosePartList = this.removeChildArray(
          this.choosePartList,
          deleteItem
        );
      }
      this.partSelection = selection;
      // console.log(selection);
      // this.partSelection.
      // this.partSelection = selection;
    },
    cancelSelection() {},
    removeChildArray(arr, childArr) {
      return childArr.reduce(
        (newArr, child) => newArr.filter((item) => item.id !== child.id),
        arr
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.title-box {
  display: flex;
  margin: 0 0 10px 20px;

  .title {
    // flex: 1;
    font-size: 16px;
  }
}

.oem-box {
  width: 100%;
  display: flex;

  .button-box {
    width: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    ::v-deep .el-button + .el-button {
      margin-top: 20px;
      margin-left: 0;
    }
  }
}
</style>
