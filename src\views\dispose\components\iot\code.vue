<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 14:31:41
 * @Description: 数字代码记录
 -->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd(null, 'add')"
        >
          新增记录
        </el-button>
      </template>
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          clearable
          collapse-tags
          :options="options"
          style="width: 550px"
          filterable
          :props="{
            label: 'name',
            value: 'id',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleSelect"
        ></el-cascader>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleAdd(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 编辑弹窗 -->
    <ProDialog
      :value="addDialog"
      :title="addType"
      :confirm-text="'保存'"
      width="50%"
      :top="'50px'"
      @ok="handleAddDialogOk"
      @cancel="handleAddDialogCancel"
    >
      <ProForm
        ref="addForm"
        :form-param="addForm"
        :form-list="columns"
        :confirm-loading="addFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="'add'"
        @proSubmit="addFormSubmit"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import { Message, MessageBox } from "element-ui";
import {
  getNumericCodeListApi,
  deleteNumericCodeApi,
  exitNumericCodeListApi,
  addNumericCodeListApi,
} from "@/api/iot";
import { productAllApi } from "@/api/dispose";
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
export default {
  name: "Code",
  components: { ProTable },
  data() {
    return {
      productIdName: [],
      options: [],
      addDialog: false,
      addType: "编辑",
      tableData: [],
      addForm: {},
      addFormLoading: false,
      columns: [
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
          isForm: true,
          formSpan: 12,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        // {
        //   dataIndex: "fullIdPath",
        //   isSearch: true,
        //   clearable: true,
        //   searchSlot: "fullIdPath",
        //   title: "品牌/产品树/系列",
        //   valueType: "select",
        // },
        {
          dataIndex: "series",
          title: "机型",
          isTable: true,
          formSpan: 12,
          isForm: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        // {
        //   dataIndex: "numData",
        //   title: "抓取的数据",
        //   isTable: true,
        //   formSpan: 12,
        //   isForm: true,
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          dataIndex: "numCode",
          title: "数字代码",
          isForm: true,
          formSpan: 12,
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 80,
        },
        {
          dataIndex: "numData",
          title: "抓取的数据",
          isForm: true,
          formSpan: 12,
          isSearch: true,
          valueType: "input",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "dataDescribe",
          title: "中文描述",
          formSpan: 12,
          isForm: true,
          valueType: "input",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "faultCode",
          title: "故障代码",
          formSpan: 12,
          isForm: true,
          valueType: "input",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "paperCode",
          title: "卡纸代码",
          formSpan: 12,
          isForm: true,
          valueType: "input",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "state",
          title: "过滤状态",
          formSpan: 12,
          isForm: true,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            { label: "是", value: "是" },
            { label: "否", value: "否" },
          ],
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "isNotify",
          title: "是否通知",
          formSpan: 12,
          isTable: true,
          isForm: true,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            { label: "是", value: true },
            { label: "否", value: false },
          ],
          formatter: (row) => (row.isNotify ? "是" : "否"),
          minWidth: 80,
        },
        {
          dataIndex: "isStatistics",
          title: "是否统计",
          formSpan: 12,
          isTable: true,
          isForm: true,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            { label: "是", value: true },
            { label: "否", value: false },
          ],
          formatter: (row) => (row.isStatistics ? "是" : "否"),
          minWidth: 80,
        },
        {
          dataIndex: "updatedAt",
          title: "上传时间",
          isSearch: true,
          clearable: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          isSearch: false,
          tableSlot: "action",
          width: 180,
        },
      ],
      queryParam: {},
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
    };
  },
  mounted() {
    this.getProductThird();
    this.refresh();
  },
  methods: {
    async loadData(parameter) {
      try {
        this.queryParam = filterParam(
          Object.assign({}, this.queryParam, parameter)
        );
        const res = [
          {
            createdAtStartTime: null,
            createdAtEndTime: null,
            data: parameter.updatedAt,
          },
        ];
        filterParamRange(this, this.queryParam, res);
        const requestParameters = cloneDeep(this.queryParam);
        delete requestParameters.updatedAt;
        const result = await getNumericCodeListApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    handleAdd(row, type) {
      if (type == "add") {
        this.addType = "新建";
        this.addForm = {};
      } else {
        this.addType = "编辑";
        this.addForm = cloneDeep(row);
      }
      this.addDialog = true;
    },
    handleDelete(row) {
      MessageBox.confirm(
        "数字代码记录删除后，数据将不可恢复。确认删除？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(async () => {
          console.log(row, "555555555555");
          const result = await deleteNumericCodeApi(row.id);
          if (result.code === 200) {
            Message.success("删除成功");
            this.localPagination = {
              pageNumber: 1,
              pageSize: 10,
              total: 0,
            };
            this.$nextTick(() => {
              this.refresh();
            });
          }
        })
        .catch(() => {
          console.log("取消删除");
        });
    },
    // 新建保存
    handleAddDialogOk() {
      this.$refs.addForm.handleSubmit();
    },
    handleAddDialogCancel() {
      this.addDialog = false;
      this.$nextTick(() => {
        this.addForm = {};
      });
    },
    async addFormSubmit(val) {
      if (this.addType == "新建") {
        try {
          this.addFormLoading = true;
          const result = await addNumericCodeListApi(val);
          if (result.code === 200) {
            Message.success("添加成功");
            this.addDialog = false;
            this.$refs.ProTable.refresh();
          }
        } catch (err) {
          Message.error(err.message);
        } finally {
          this.addFormLoading = false;
        }
      } else {
        try {
          this.addFormLoading = true;
          const result = await exitNumericCodeListApi(val);
          if (result.code === 200) {
            Message.success("编辑成功");
            this.addDialog = false;
            this.$refs.ProTable.refresh();
          }
        } catch (err) {
          Message.error(err.message);
        } finally {
          this.addFormLoading = false;
        }
      }
    },
    handleSelect(arr) {
      this.queryParam.productIds = arr.map((item) => item[item.length - 1]);
    },
    async getProductThird() {
      await productAllApi().then((res) => {
        this.options = res.data;
        // this.$refs.ProTable.refresh();
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
