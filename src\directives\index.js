/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 09:21:31
 * @Description:
 */
import Vue from "vue";
import VSelectLoadMore from "@/directives/VSelectLoadMore";
import VAuth from "@/directives/VAuth";
import VDialogDrag from "@/directives/VDialogDrag";
import VDialogZoom from "@/directives/VDialogZoom";
import VDrag from "@/directives/VDrag";
import VResize from "@/directives/VResize";

Vue.directive("selectLoadMore", VSelectLoadMore);
Vue.directive("auth", VAuth);
Vue.directive("dialogDrag", VDialogDrag);
Vue.directive("dialogZoom", VDialogZoom);
Vue.directive("drag", VDrag);
Vue.directive("resize", VResize);
