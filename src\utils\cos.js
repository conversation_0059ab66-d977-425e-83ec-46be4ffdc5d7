/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-26 11:27:54
 * @Description:
 */
import COS from "cos-js-sdk-v5";
import { getCredentials } from "@/api/upload";

const cos = new COS({
  getAuthorization: async (options, callback) => {
    try {
      const credentials = await getCredentials().then((res) => res.data);
      callback({
        TmpSecretId: credentials.tmpSecretId,
        TmpSecretKey: credentials.tmpSecretKey,
        SecurityToken: credentials.token,
        ExpiredTime: credentials.expiredTime,
      });
    } catch (error) {
      console.log(error);
    }
  },
});

export default cos;
