<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-21 14:24:51
 * @Description: 
 -->
<template>
  <div v-show="value" class="ProDialogWrap">
    <el-drawer
      :visible.sync="visible"
      :wrapper-closable="false"
      :destroy-on-close="true"
      v-bind="$attrs"
      :append-to-body="
        $attrs['append-to-body'] === undefined ? true : $attrs['append-to-body']
      "
      :class="{ resetBottom: !noFooter }"
      @closed="handleClose"
    >
      <template #title>
        <slot name="title"></slot>
      </template>
      <slot></slot>
      <template v-if="!noFooter">
        <div class="dialog-footer1">
          <slot name="footer"></slot>
          <div v-if="!noConfirmFooter" class="btn-box">
            <el-button
              v-if="!noconfirm"
              type="primary"
              :loading="confirmButtonDisabled"
              @click="handleOk"
              >{{ confirmText }}
            </el-button>
            <el-button @click="handleClose">取消</el-button>
            <!-- <div v-if="!noconfirm" class="success-button" @click="handleOk">
              {{ confirmText }}
            </div>
            <div class="cancel-button m-l-40" @click="handleClose">取消</div> -->
          </div>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script>
import { debounce } from "lodash";
export default {
  name: "ProDialog",
  props: {
    value: {
      type: Boolean,
      default: true,
    },
    confirmLoading: {
      type: Boolean,
      default: false,
    },
    noFooter: {
      type: Boolean,
      default: false,
    },
    noconfirm: {
      type: Boolean,
      default: false,
    },
    noConfirmFooter: {
      type: Boolean,
      default: false,
    },
    confirmText: {
      type: String,
      default: "保 存",
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    confirmButtonDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
    };
  },
  computed: {},
  watch: {
    value: {
      handler: function (val) {
        this.visible = val;
      },
      deep: true,
      immediate: true,
    },

    //   isFullscreen:{
    //      handler: function () {
    //       if (this.fullscreen || attrs.fullscreen === '')
    //         this.isFullscreen = true
    //     },
    //     immediate: true
    //   }
  },
  created() {},

  // watchEffect(() => {
  //   if (isFullscreen.value) return
  //   if (props.value && ProDialog.value) {
  //     nextTick(() => {
  //       draggable(ProDialog.value.dialogRef)
  //     })
  //   }
  // })
  methods: {
    handleClose() {
      this.visible = false;
      this.$emit("cancel");
    },
    // handleOk() {
    //   this.$emit("ok");
    // },
    /**
     * @description 防抖
     * */
    // handleOk: debounce(function () {
    //   this.$emit("ok");
    // }, 100),
    handleOk() {
      this.$emit("ok");
    },
  },
  //  setup(props, { emit, attrs }) {
  // watch(
  //   () => attrs.fullscreen,
  //   () => {
  //     if (attrs.fullscreen || attrs.fullscreen === '')
  //       isFullscreen.value = true
  //   },
  //   { immediate: true }
  // )

  // watchEffect(() => {
  //   if (isFullscreen.value) return
  //   if (props.value && ProDialog.value) {
  //     nextTick(() => {
  //       draggable(ProDialog.value.dialogRef)
  //     })
  //   }
  // })
  // },
};
</script>
<style></style>
