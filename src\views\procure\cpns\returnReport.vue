<!--
 * @Author: wskg
 * @Date: 2024-08-14 11:42:49
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 采购退货单
 -->
<template>
  <div>
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')"
            >查看</el-button
          >
          <el-button
            v-if="row.refundStatus.value !== 'WAIT_CONFIRM'"
            icon="el-icon-edit"
            @click="handleEdit(row, 'edit')"
            >退货</el-button
          >
        </div>
      </template>
    </ProTable>
    <ProDrawer
      :value="showDrawer"
      :title="'退货'"
      size="80%"
      :destroy-on-close="true"
      :confirm-text="'确认退货'"
      :no-footer="methodType === 'info'"
      @ok="handleDrawerOk"
      @cancel="handleCloseDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="methodType"
      >
        <template #returnParams>
          <ProTable
            ref="returnProTable"
            :columns="returnParamsColumns"
            :data="returnParamsData"
            :height="400"
            :show-search="false"
            :show-setting="false"
            :show-loading="false"
            sticky
          >
            <template #theTrackingNumber="{ row }">
              <el-input
                v-model="row.theTrackingNumber"
                placeholder="请输入快递单号"
                :disabled="methodType === 'info'"
              ></el-input>
            </template>
          </ProTable>
        </template>
        <template #courierList>
          <ProTable
            ref="courierProTable"
            :columns="courierListColumns"
            :data="courierListData"
            :height="400"
            :show-search="false"
            :show-setting="false"
            :show-loading="false"
            sticky
          >
            <template #btn>
              <el-button
                v-if="methodType !== 'info'"
                type="success"
                icon="el-icon-plus"
                size="mini"
                @click="handleAddCourier"
              >
                新增快递单号</el-button
              >
            </template>
            <template #trackingNumber="{ row }">
              <el-input
                v-model="row.trackingNumber"
                placeholder="请输入快递单号"
              ></el-input>
            </template>
            <template #shippingFee="{ row }">
              <el-input
                v-model="row.shippingFee"
                placeholder="请输入运费"
              ></el-input>
            </template>
            <template #trackingPic="{ row }">
              <ProUpload
                :file-list="row.picUrls"
                :type="methodType"
                :limit="2"
                :drag="true"
                @uploadSuccess="(e) => handleLicenseImgUploadSuccess(e, row)"
                @uploadRemove="(e) => handleLicenseImgUploadRemove(e, row)"
              />
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import ProUpload from "@/components/ProUpload/index.vue";
import {
  confirmReturnApi,
  getReturnDetailApi,
  getReturnDetailInfoApi,
  purchaseReturnListApi,
} from "@/api/manufacturer";
import { cloneDeep } from "lodash";

export default {
  name: "ReturnReport",
  components: { ProUpload },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          title: "退货单号",
          dataIndex: "code",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          title: "订单编号",
          dataIndex: "purchaseCode",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          title: "供应商",
          dataIndex: "manufacturerName",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          title: "快递单号",
          dataIndex: "theTrackingNumber",
          isSearch: true,
          valueType: "input",
        },
        {
          title: "退货状态",
          dataIndex: "refundStatus",
          isTable: true,
          isSearch: true,
          valueType: "input",
          formatter: (row) => row.refundStatus?.label,
        },
        {
          title: "开票状态",
          dataIndex: "invoiceMethod",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          title: "退货人",
          dataIndex: "createdBy",
          isTable: true,
          isSearch: true,
          valueType: "input",
          formatter: (row) => row.createdBy?.name,
        },
        {
          title: "退货日期",
          dataIndex: "createdAt",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          title: "退货数量",
          dataIndex: "number",
          isTable: true,
        },
        {
          title: "退货原因",
          dataIndex: "reason",
          isTable: true,
        },
        {
          title: "操作",
          dataIndex: "action",
          isTable: true,
          tableSlot: "actions",
        },
      ],
      tableData: [],
      showDrawer: false,
      methodType: "info",
      form: {},
      // 退货清单
      formColumns: [
        {
          dataIndex: "code",
          title: "退货单号",
          isForm: true,
          formSpan: 24,
          valueType: "text",
        },
        {
          dataIndex: "paymentCode",
          title: "付款单号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "manufacturerCode",
          title: "供应商编号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商简称",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorTime",
          title: "采购时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "consignee",
          title: "收货人",
          isForm: true,
          formSpan: 6,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入收货人姓名",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "consigneePhone",
          title: "收货人电话",
          isForm: true,
          formSpan: 6,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入联系电话",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "consigneeAddress",
          title: "收货地址",
          isForm: true,
          formSpan: 6,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入收货地址",
              trigger: "blur",
            },
          ],
        },
        // {
        //   dataIndex: "shippingFee",
        //   title: "运费",
        //   isForm: true,
        //   formSpan: 6,
        //   valueType: "input",
        // },
        {
          dataIndex: "amount",
          title: "退货总金额",
          isForm: true,
          formSpan: 18,
          valueType: "text",
        },
        {
          dataIndex: "returnParams",
          title: "退货清单",
          isForm: true,
          formSpan: 24,
          formSlot: "returnParams",
        },
        {
          dataIndex: "courierList",
          title: "快递清单",
          isForm: true,
          formSpan: 24,
          formSlot: "courierList",
        },
      ],
      formLoading: false,
      returnParamsColumns: [
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
        },
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        // {
        //   dataIndex: "number",
        //   title: "购买数量",
        //   isTable: true,
        // },
        {
          dataIndex: "number",
          title: "退货数量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "退货金额",
          isTable: true,
        },
        {
          dataIndex: "returnBackNum",
          title: "需寄回数量",
          isTable: true,
        },
        {
          dataIndex: "theTrackingNumber",
          title: "快递单号",
          isTable: true,
          width: 200,
          tableSlot: "theTrackingNumber",
        },
      ],
      returnParamsData: [],
      // 快递清单
      courierListColumns: [
        {
          dataIndex: "trackingNumber",
          title: "快递单号",
          isTable: true,
          width: 220,
          tableSlot: "trackingNumber",
        },
        {
          dataIndex: "shippingFee",
          title: "运费",
          isTable: true,
          width: 100,
          tableSlot: "shippingFee",
        },
        {
          dataIndex: "trackingPic",
          title: "快递单照片",
          isTable: true,
          tableSlot: "trackingPic",
        },
      ],
      courierListData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      purchaseReturnListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row, type) {
      this.methodType = type;
      getReturnDetailApi(row.id).then((res) => {
        this.form = res.data;
        this.returnParamsData = this.form.manufacturerReturnGoods;
        this.courierListData = this.form.backRecordInfos;
      });
      this.showDrawer = true;
    },
    handleDrawerOk() {
      this.$refs.ProForm.handleSubmit().then((res) => {
        this.$confirm("正在办理退货, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          // confirmReturnApi().then(res => {
          //   console.log(res)
          // })
        });
      });
    },
    // 新增快递单号
    handleAddCourier() {
      this.courierListData.push({
        trackingNumber: "",
        shippingFee: 0,
        picUrls: [],
      });
    },
    handleLicenseImgUploadSuccess(result, row) {
      row.picUrls.push(result);
    },
    handleLicenseImgUploadRemove(file, row) {
      const index = row.picUrls.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      row.picUrls.splice(index, 1);
    },
    handleCloseDrawer() {
      this.showDrawer = false;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
