<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="日访问" name="日访问" lazy>
        <VisitStat :columns="dayColumns" type="day" />
      </el-tab-pane>
      <el-tab-pane label="访问记录" name="访问记录" lazy>
        <VisitStat :columns="recordColumns" type="record" />
      </el-tab-pane>
      <!-- <el-tab-pane label="访问统计" name="访问统计" lazy>
               <VisitStat :columns="statColumns" type="stat" />
    
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
import VisitStat from "./components/visitStat.vue";
import { dictTreeByCodeApi } from "@/api/user";
import getPathName from "@/utils/pathMap";
// import VisitRecord from "./components/visitRecord.vue";
export default {
  name: "Visit",
  components: { VisitStat },
  data() {
    return {
      activeName: "日访问",
      dayColumns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
        },
        {
          dataIndex: "viewNum",
          title: "访问次数",
          isTable: true,
        },
        {
          dataIndex: "duration",
          title: "访问时长",
          isTable: true,
        },
        {
          dataIndex: "regionPath",
          title: "省市区",
          isSearch: true,
          searchSlot: "regionPath",
        },
        {
          dataIndex: "currDate",
          title: "访问日期",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
      ],
      recordColumns: [
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "staffTel",
          title: "账号手机",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "staffRole",
          title: "账号角色",
          isTable: true,
          formatter: (row) => row.staffRole?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(500),
          optionskey: {
            label: "label",
            value: "value",
          },
          width: 100,
        },
        {
          dataIndex: "province",
          title: "省",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "city",
          title: "市",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "area",
          title: "区",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "path",
          title: "访问页面",
          isTable: true,
          formatter: (row) => getPathName(row.path),
          minWidth: 80,
        },
        {
          dataIndex: "originParam",
          title: "访问参数",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "duration",
          title: "访问时长",
          isTable: true,
          width: 100,
        },
        {
          dataIndex: "finishedAt",
          title: "访问时间",
          isTable: true,
          width: 150,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
      ],
      statColumns: [{}],
    };
  },
  methods: {},
};
</script>

<style scoped lang="scss"></style>
