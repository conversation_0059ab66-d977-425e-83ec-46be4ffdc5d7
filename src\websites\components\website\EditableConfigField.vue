<template>
  <div class="editable-config-field">
    <!-- 显示模式 -->
    <div 
      v-if="!isEditing" 
      :class="['field-display', className, { 'field-changed': isChanged }]"
      @click="startEdit"
    >
      <span v-if="displayValue">{{ displayValue }}</span>
      <span v-else class="field-placeholder">{{ placeholder || '点击编辑' }}</span>
      <i v-if="isChanged" class="el-icon-edit-outline field-changed-icon"></i>
    </div>

    <!-- 编辑模式 -->
    <el-dialog
      :title="`编辑${label || '配置'}`"
      :visible.sync="isEditing"
      width="500px"
      :before-close="handleCancel"
      append-to-body
    >
      <div class="edit-form">
        <el-form :model="editForm" :rules="rules" ref="editForm" label-width="80px">
          <el-form-item :label="label" prop="value">
            <!-- 文本输入 -->
            <el-input
              v-if="type === 'text' || type === 'phone' || type === 'email'"
              v-model="editForm.value"
              :placeholder="placeholder"
              :maxlength="maxLength"
              show-word-limit
              @keydown.native="handleKeyDown"
              ref="inputRef"
            />
            <!-- 多行文本 -->
            <el-input
              v-else-if="type === 'textarea'"
              v-model="editForm.value"
              type="textarea"
              :rows="rows || 3"
              :placeholder="placeholder"
              :maxlength="maxLength"
              show-word-limit
              @keydown.native="handleKeyDown"
              ref="inputRef"
            />
            <!-- 数字输入 -->
            <el-input-number
              v-else-if="type === 'number'"
              v-model="editForm.value"
              :min="min"
              :max="max"
              :placeholder="placeholder"
              style="width: 100%"
              @keydown.native="handleKeyDown"
              ref="inputRef"
            />
            <!-- 开关 -->
            <el-switch
              v-else-if="type === 'switch'"
              v-model="editForm.value"
              :active-text="activeText || '开启'"
              :inactive-text="inactiveText || '关闭'"
              ref="inputRef"
            />
            <!-- 默认文本输入 -->
            <el-input
              v-else
              v-model="editForm.value"
              :placeholder="placeholder"
              :maxlength="maxLength"
              show-word-limit
              @keydown.native="handleKeyDown"
              ref="inputRef"
            />
          </el-form-item>
          
          <!-- 字段描述 -->
          <div v-if="description" class="field-description">
            {{ description }}
          </div>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'EditableConfigField',
  props: {
    // 字段值
    value: {
      type: [String, Number, Boolean],
      default: ''
    },
    // 字段标签
    label: {
      type: String,
      default: ''
    },
    // 字段类型
    type: {
      type: String,
      default: 'text',
      validator: value => ['text', 'textarea', 'number', 'phone', 'email', 'switch'].includes(value)
    },
    // 占位符
    placeholder: {
      type: String,
      default: ''
    },
    // 字段描述
    description: {
      type: String,
      default: ''
    },
    // 是否必填
    required: {
      type: Boolean,
      default: false
    },
    // 最大长度
    maxLength: {
      type: Number,
      default: null
    },
    // 最小值（数字类型）
    min: {
      type: Number,
      default: null
    },
    // 最大值（数字类型）
    max: {
      type: Number,
      default: null
    },
    // 文本域行数
    rows: {
      type: Number,
      default: 3
    },
    // 开关激活文本
    activeText: {
      type: String,
      default: '开启'
    },
    // 开关非激活文本
    inactiveText: {
      type: String,
      default: '关闭'
    },
    // 验证模式
    pattern: {
      type: RegExp,
      default: null
    },
    // 验证错误信息
    patternMessage: {
      type: String,
      default: '格式不正确'
    },
    // 自定义样式类
    className: {
      type: String,
      default: ''
    },
    // 是否已更改
    isChanged: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEditing: false,
      saving: false,
      editForm: {
        value: ''
      }
    }
  },
  computed: {
    displayValue() {
      if (this.type === 'switch') {
        return this.value ? this.activeText : this.inactiveText
      }
      return this.value || ''
    },
    rules() {
      const rules = []
      
      if (this.required) {
        rules.push({
          required: true,
          message: `请输入${this.label}`,
          trigger: 'blur'
        })
      }
      
      if (this.pattern) {
        rules.push({
          pattern: this.pattern,
          message: this.patternMessage,
          trigger: 'blur'
        })
      }
      
      if (this.type === 'email') {
        rules.push({
          type: 'email',
          message: '请输入正确的邮箱格式',
          trigger: 'blur'
        })
      }
      
      return { value: rules }
    }
  },
  methods: {
    startEdit() {
      this.editForm.value = this.value
      this.isEditing = true
      // 自动聚焦到输入框
      this.$nextTick(() => {
        if (this.$refs.inputRef) {
          this.$refs.inputRef.focus()
        }
      })
    },
    handleCancel() {
      this.isEditing = false
      this.editForm.value = this.value
    },
    async handleSave() {
      try {
        await this.$refs.editForm.validate()
        this.saving = true

        // 触发值更改事件
        this.$emit('change', this.editForm.value)

        // 模拟保存延迟
        await new Promise(resolve => setTimeout(resolve, 300))

        this.isEditing = false
        this.$message.success('保存成功')
      } catch (error) {
        console.error('验证失败:', error)
      } finally {
        this.saving = false
      }
    },
    // 处理快捷键
    handleKeyDown(e) {
      if (e.key === 'Enter' && (e.ctrlKey || e.metaKey || this.type !== 'textarea')) {
        e.preventDefault()
        this.handleSave()
      } else if (e.key === 'Escape') {
        e.preventDefault()
        this.handleCancel()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.editable-config-field {
  .field-display {
    position: relative;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    min-height: 20px;
    display: inline-flex;
    align-items: center;
    
    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
      border: 1px dashed #409eff;
    }
    
    &.field-changed {
      background-color: rgba(245, 166, 35, 0.1);
      border: 1px solid #f5a623;
      
      .field-changed-icon {
        margin-left: 4px;
        color: #f5a623;
        font-size: 12px;
      }
    }
    
    .field-placeholder {
      color: #c0c4cc;
      font-style: italic;
    }
  }
  
  .edit-form {
    .field-description {
      margin-top: 8px;
      color: #909399;
      font-size: 12px;
      line-height: 1.4;
    }
  }
  
  .dialog-footer {
    text-align: right;
  }
}
</style>
