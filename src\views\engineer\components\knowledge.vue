<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 11:20:18
 * @Description: 知识库管理
 -->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :local-pagination="localPagination"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </template>
      <template #productName>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          collapse-tags
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          @change="handleChange"
        ></el-cascader>
      </template>
      <template #machine="slotProps">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in slotProps.row.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <el-button slot="reference" size="mini">适用机型</el-button>
        </el-popover>
      </template>
      <template #status="{ row, index }">
        <el-switch
          v-model="row.isEnable"
          active-color="#13ce66"
          inactive-color="#ddd"
          :active-value="true"
          :inactive-value="false"
          @change="handleChangeStatus(row, index)"
        ></el-switch>
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-zoom-out"
            @click="handleInfo(slotProps.row)"
          >
            查看
          </el-button>
          <el-button
            v-if="!slotProps.row.isEnable"
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>

          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>
    <!-- 新增、编辑、详情框  -->
    <ProDrawer
      :value="dialogVisible"
      :title="dialogTitle"
      size="95%"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="true"
      @cancel="dialogVisible = false"
    >
      <div>
        <el-steps
          v-if="methodType == 'add'"
          align-center
          :active="step"
          finish-status="success"
          class="steps-box"
        >
          <el-step title="基础信息"></el-step>
          <el-step title="代码解释"></el-step>
          <el-step title="维修案例"></el-step>
          <el-step title="相关设置"></el-step>
        </el-steps>
        <el-tabs
          v-if="methodType !== 'add'"
          v-model="activeName"
          type="card"
          @tab-click="handleClick"
        >
          <el-tab-pane label="基础信息" name="0"></el-tab-pane>
          <el-tab-pane label="代码解释" name="1"></el-tab-pane>
          <el-tab-pane label="维修案例" name="2"></el-tab-pane>
          <el-tab-pane label="相关设置" name="3"></el-tab-pane>
        </el-tabs>
        <div v-show="step == 0" style="margin-top: 30px">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="100px"
            class="demo-form"
          >
            <el-form-item label="知识库类型" prop="type">
              <el-select
                v-model="form.type"
                style="width: 400px"
                placeholder="请选择知识库类型"
                :disabled="methodType == 'info'"
              >
                <el-option
                  v-for="item in typelist"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="知识库等级" prop="level">
              <el-select
                v-model="form.level"
                style="width: 400px"
                placeholder="请选择知识库等级"
                :disabled="methodType == 'info'"
              >
                <el-option
                  v-for="item in levellist"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="适用机型" prop="productList">
              <el-button
                v-if="methodType !== 'info'"
                type="primary"
                plain
                @click="showModDialog"
                >选择机型</el-button
              >
              <DataTable
                ref="ProTable1"
                :columns="columns1"
                :show-setting="false"
                :show-pagination="false"
                :show-search="false"
                row-key="index"
                :data="tableData1"
                sticky
                :height="350"
                style="width: 100%; margin-top: 20px"
                :show-table-operator="false"
              >
                <template #actions="slotProps">
                  <span v-if="methodType !== 'info'" class="fixed-width">
                    <el-button
                      size="mini"
                      type="danger"
                      icon="el-icon-delete"
                      @click="handleDeleteGoods(slotProps.row)"
                    >
                      删除
                    </el-button>
                  </span>
                </template>
              </DataTable>
              <!-- <el-cascader
                ref="ProductIds"
                v-model="form.productIdNameForm"
                filterable
                clearable
                collapse-tags
                :options="options"
                style="width: 400px"
                :props="{
                  label: 'name',
                  value: 'fullIdPath',
                  children: 'children',
                  expandTrigger: 'click',
                  multiple: true,
                }"
                leaf-only
                @change="handleChangeForm"
              ></el-cascader> -->
            </el-form-item>
          </el-form>
          <div v-if="methodType !== 'info'" class="btnbox">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="proSubmit">立即创建</el-button>
          </div>
        </div>
        <div v-show="step == 1" style="margin-top: 30px">
          <el-form
            ref="form1"
            :model="form1"
            label-width="100px"
            class="demo-form"
          >
            <el-form-item label="代码解释" prop="delivery">
              <div class="sp-content editor-box">
                <div
                  v-show="methodType == 'info'"
                  v-html="form.codeExplain"
                ></div>
                <ProWangeEditor
                  v-show="methodType != 'info'"
                  ref="ProWangeEditorRef"
                  :content="form.codeExplain"
                ></ProWangeEditor>
              </div>
            </el-form-item>
          </el-form>
          <div v-if="methodType !== 'info'" class="btnbox">
            <el-button @click="back">上一步</el-button>
            <el-button type="primary" @click="next">下一步</el-button>
          </div>
        </div>
        <div v-show="step == 2" style="margin-top: 30px">
          <el-button
            v-if="methodType !== 'info'"
            type="primary"
            style="margin-bottom: 20px"
            @click="addCase"
            >新增案例</el-button
          >
          <el-collapse style="margin-bottom: 80px">
            <el-collapse-item
              v-for="(item, index) in caseData"
              :key="index"
              :name="index"
            >
              <template slot="title">
                <div class="tit-box1">{{ item.title }}</div>
              </template>
              <div style="text-align: right">
                <!-- <el-button
                  type="text"
                  size="small"
                  style="margin-left: 20px"
                  @click="handleClickInfo(item)"
                  >编辑</el-button
                > -->
                <el-button
                  v-if="methodType !== 'info'"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  @click="handleClickDel(item, index)"
                ></el-button>
                <el-button
                  v-if="methodType !== 'info'"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="handleClickEx(item, index)"
                ></el-button>
              </div>
              <div class="tit-box">故障描述</div>
              <div v-html="item.faultDesc"></div>
              <div class="tit-box">解决措施</div>
              <div v-html="item.solutionMeasures"></div>
              <div class="tit-box">相关商品</div>
              <el-table :data="item.caseItems" border style="width: 100%">
                <el-table-column prop="picsUrl" label="商品主图" width="100">
                  <template slot-scope="scope">
                    <img
                      style="max-width: 100px; max-height: 100px"
                      :src="getPicsUrlImg(scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  v-for="(item, index) in columnsDialog"
                  :key="index"
                  :prop="item.dataIndex"
                  :label="item.title"
                >
                </el-table-column>
              </el-table>
              <div class="tit-box">拆装视频</div>
              <div>
                <video
                  v-for="(it, index) in item.installVideo"
                  :key="index"
                  :src="it.url"
                  class="avatar"
                  style="width: 300px; float: left; margin-left: 20px"
                  controls="controls"
                ></video>
              </div>
              <div class="tit-box">相关视频</div>
              <div>
                <video
                  v-for="(it, index) in item.relateVideo"
                  :key="index"
                  :src="it.url"
                  class="avatar"
                  style="width: 300px; float: left; margin-left: 20px"
                  controls="controls"
                ></video>
              </div>
            </el-collapse-item>
          </el-collapse>
          <!-- <el-table :data="caseData" style="width: 100%">
            <el-table-column prop="title" width="800" label="案例标题">
            </el-table-column>
           
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="handleClickInfo(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  @click="handleClickDel(scope.row, scope.$index)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table> -->
          <div v-if="methodType !== 'info'" class="btnbox">
            <el-button @click="back">上一步</el-button>
            <el-button type="primary" @click="next">下一步</el-button>
          </div>
        </div>
        <div v-show="step == 3" style="margin-top: 30px">
          <el-form
            ref="form3"
            :model="form3"
            :rules="rules3"
            label-width="100px"
            class="demo-form"
          >
            <el-form-item label="显示标题" prop="title">
              <el-input
                v-model="form3.title"
                :disabled="methodType == 'info'"
                style="width: 400px"
              ></el-input>
            </el-form-item>
            <el-form-item label="标签" prop="tags">
              <el-tag
                v-for="tag in form3.tags"
                :key="tag"
                :closable="methodType !== 'info' ? true : false"
                :disable-transitions="false"
                @close="handleClose(tag)"
              >
                {{ tag }}
              </el-tag>
              <template v-if="methodType !== 'info'">
                <el-input
                  v-if="inputVisible"
                  ref="saveTagInput"
                  v-model="inputValue"
                  class="input-new-tag"
                  size="small"
                  @keyup.enter.native="handleInputConfirm"
                  @blur="handleInputConfirm"
                >
                </el-input>
                <el-button
                  v-else
                  class="button-new-tag"
                  size="small"
                  @click="showInput"
                  >+ 新增</el-button
                >
              </template>
            </el-form-item>
          </el-form>
          <div v-if="methodType !== 'info'" class="btnbox">
            <el-button @click="back">上一步</el-button>
            <el-button type="primary" @click="next">保存</el-button>
          </div>
        </div>
      </div>
    </ProDrawer>
    <!--  编辑案例  -->
    <ProDialog
      :value="showDialog"
      title="编辑案例"
      width="80%"
      :confirm-loading="dialogLoading"
      top="50px"
      @ok="addCaseOk"
      @cancel="(showDialog = false), (exItemIndex = 100000)"
    >
      <el-form ref="form2" :model="form2" label-width="100px" class="demo-form">
        <el-form-item label="案例标题" prop="title">
          <el-input v-model="form2.title" width="400"></el-input>
        </el-form-item>
        <el-form-item
          label="故障描述"
          prop="faultDesc"
          style="width: 50%; float: left"
        >
          <div class="sp-content editor-box">
            <ProWangeEditor
              v-show="methodType != 'info'"
              ref="ProWangeEditorRef1"
              :content="form2.faultDesc"
            ></ProWangeEditor>
          </div>
        </el-form-item>
        <el-form-item
          label="解决措施"
          prop="solutionMeasures"
          style="width: 50%; float: left"
        >
          <div class="sp-content editor-box">
            <ProWangeEditor
              v-show="methodType != 'info'"
              ref="ProWangeEditorRef2"
              :content="form2.solutionMeasures"
            ></ProWangeEditor>
          </div>
        </el-form-item>
        <el-form-item label="添加商品" prop="delivery">
          <div class="sp-content editor-box">
            <el-button
              type="primary"
              style="margin-bottom: 20px"
              @click="handleOpenChooseDialog"
              >选择商品</el-button
            >
            <el-table :data="partData" border style="width: 100%">
              <el-table-column prop="picsUrl" label="商品主图" width="100">
                <template slot-scope="scope">
                  <img
                    style="max-width: 100px; max-height: 100px"
                    :src="getPicsUrlImg(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                v-for="(item, index) in columnsDialog"
                :key="index"
                :prop="item.dataIndex"
                :label="item.title"
              >
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click="handleDelOne(scope.row)"
                    >移除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <el-form-item label="拆装视频" prop="installVideo">
          <ProUploadFile
            :type="methodType"
            :limit="10"
            :file-size="20"
            :file-list="czVideo"
            :accept="'video/mp4,video/ogg,video/flv,video/avi,video/wmv,video/rmvb'"
            :file-type="[
              'video/mp4',
              'video/ogg',
              'video/flv',
              'video/avi',
              'video/wmv',
              'video/rmvb',
            ]"
            :multiple="false"
            @uploadSuccess="handleUploadSuccess"
            @uploadRemove="handleUploadRemove"
          />
        </el-form-item>
        <el-form-item label="相关视频" prop="relateVideo">
          <ProUploadFile
            :type="methodType"
            :limit="10"
            :file-size="20"
            :file-list="xgVideo"
            :accept="'video/mp4,video/ogg,video/flv,video/avi,video/wmv,video/rmvb'"
            :file-type="[
              'video/mp4',
              'video/ogg',
              'video/flv',
              'video/avi',
              'video/wmv',
              'video/rmvb',
            ]"
            :multiple="false"
            @uploadSuccess="handleUploadSuccess1"
            @uploadRemove="handleUploadRemove1"
        /></el-form-item>
      </el-form>
    </ProDialog>
    <!-- 选择商品 -->
    <ProDialog
      :value="showPartDialog"
      title="选择商品"
      width="1800px"
      :confirm-loading="partDialogLoading"
      top="50px"
      @ok="handleChooseDialogConfirm"
      @cancel="showPartDialog = false"
    >
      <Goods ref="ChoosePartTable" @chooseOem="handleSelectionChange"></Goods>
    </ProDialog>
    <!-- 选择机型弹窗 -->
    <ProDialog
      :value="showModelDialog"
      title="选择机型"
      width="1200px"
      :confirm-loading="false"
      top="50px"
      @ok="handleChooseDialogConfirm1"
      @cancel="showModelDialog = false"
    >
      <ModelList ref="ModelList" @choose="handleSelectionChange1"></ModelList>
    </ProDialog>
  </div>
</template>
<script>
import {
  baseByPageApi,
  baseAddApi,
  baseInfoApi,
  baseDelApi,
  baseAddApi1,
  baseAddApi2,
  baseAddApi3,
  baseOperateApi,
} from "@/api/know";
import { isEmpty, cloneDeep } from "lodash";
import ProUploadFile from "@/components/ProUpload/files.vue";
import { dictTreeByCodeApi } from "@/api/user";
import { getFullProductTreeApi, productAllApi } from "@/api/dispose";
import ProWangeEditor from "@/components/ProWangeEditor/index.vue";
import Goods from "./goods.vue";
import ModelList from "@/views/dispose/components/modelList.vue";
import { filterParamRange } from "@/utils";

export default {
  name: "Knowledge",
  components: { ProWangeEditor, Goods, ProUploadFile, ModelList },
  mixins: [],
  props: {},
  data() {
    return {
      xgVideo: "",
      czVideo: "",
      tableData1: [],
      showModelDialog: false,
      chooseModelSelection: [],
      columns1: [
        {
          dataIndex: "brand",
          title: "品牌",
          isTable: true,
        },
        {
          dataIndex: "tree",
          title: "产品树",
          isTable: true,
        },
        {
          dataIndex: "serial",
          title: "系列",
          isTable: true,
        },
        {
          dataIndex: "name",
          title: "机型",
          isTable: true,
        },
        {
          dataIndex: "fullName",
          title: "全称",
          isTable: true,
        },
        {
          dataIndex: "Actions",
          width: 120,
          title: "操作",
          align: "left",
          isTable: true,
          tableSlot: "actions",
        },
      ],
      // 列表
      step: 0,
      activeName: "0",
      inputVisible: false,
      inputValue: "",
      productIdName: [],
      options: [],
      typelist: [],
      levellist: [],
      tableData: [],
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      queryParam: {},
      columns: [
        {
          dataIndex: "productList",
          title: "适用机型",
          isTable: false,
          isSearch: true,
          // searchSlot: "productName",
          valueType: "product",
          searchSpan: 8,
        },

        {
          dataIndex: "title",
          title: "知识库标题",
          isTable: true,
          isSearch: true,

          valueType: "input",
          clearable: true,
        },
        {
          dataIndex: "level",
          title: "知识库等级",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.level.label,
          valueType: "select",
          clearable: true,
          width: 200,
          option: [],
          optionMth: () => dictTreeByCodeApi(3600),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "type",
          title: "知识库类型",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.type.label,
          valueType: "select",
          clearable: true,
          width: 200,
          option: [],
          optionMth: () => dictTreeByCodeApi(3500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "operatorName",
          title: "编辑人",
          isTable: true,
          width: 200,
        },
        {
          dataIndex: "updatedAt",
          title: "最近编辑时间",
          isTable: true,
          width: 200,
        },
        {
          dataIndex: "operateBy",
          title: "编辑人",
          isSearch: true,
          valueType: "input",
          width: 200,
        },
        {
          dataIndex: "updatedAt",
          title: "编辑时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          clearable: true,
        },
        {
          dataIndex: "isEnable",
          title: "启用状态",
          isTable: true,
          tableSlot: "status",
          width: 200,
        },

        {
          dataIndex: "Actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 200,
        },
      ],

      //新增
      proWangeEditorContent: null,
      methodType: "add",
      confirmLoading: false,
      form: {},
      form1: {},
      form2: {},
      form3: { tags: [] },
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: { url: {} },
      caseData: [],
      delcase: [],
      rules: {
        productList: [
          { required: true, message: "请选择适用机型", trigger: "blur" },
        ],
        type: [
          { required: true, message: "请选择知识库类型", trigger: "change" },
        ],
        level: [
          { required: true, message: "请选择知识库等级", trigger: "change" },
        ],
      },
      rules3: {
        title: [
          { required: true, message: "请输入显示标题", trigger: "change" },
        ],
      },
      showDialog: false,
      showPartDialog: false,
      choosePartSelection: [],
      columnsDialog: [
        {
          dataIndex: "code",
          title: "商品编号",
        },

        {
          dataIndex: "name",
          title: "商品名称",
        },

        {
          dataIndex: "categoryName",
          title: "商品分类",
        },
        {
          dataIndex: "saleStatus",
          title: "商品状态",
        },
        {
          dataIndex: "soldOutNum",
          title: "已售卖数量",
        },
      ],
      dialogLoading: false,
      partData: [],
      partDialogLoading: false,
      exItemIndex: 100000,
    };
  },

  computed: {},

  watch: {},
  created() {
    productAllApi().then((res) => {
      this.options = res.data;
      this.$refs.ProTable.listLoading = false;
      // this.$refs.ProTable.refresh();
    });
    dictTreeByCodeApi(3500).then((res) => {
      this.typelist = res.data;
    });
    dictTreeByCodeApi(3600).then((res) => {
      this.levellist = res.data;
    });
  },
  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    addCase() {
      this.showDialog = true;
      this.form2 = {
        baseId: this.form.id,
        installVideo: [],
        relateVideo: [],
        itemIds: [],
      };
      this.partData = [];
      this.$refs.ProWangeEditorRef1?.echo(null);
      this.$refs.ProWangeEditorRef2?.echo(null);
      // localStorage.removeItem("detailHtml");
    },
    addCaseOk() {
      if (this.exItemIndex < 100000) {
        setTimeout(() => {
          this.caseData.splice(this.exItemIndex, 1);
        }, 300);
      }

      this.form2.faultDesc = this.$refs.ProWangeEditorRef1?.getContent() || "";
      this.form2.solutionMeasures =
        this.$refs.ProWangeEditorRef2?.getContent() || "";
      this.form2.caseItems = cloneDeep(this.partData);
      this.caseData.push(this.form2);
      this.showDialog = false;
    },
    handleClickInfo(row) {
      this.showDialog = true;
      this.form2 = {
        baseId: this.form.id,
        title: row.title,
        installVideo: row.installVideo,
        relateVideo: row.relateVideo,
      };
      this.$refs.ProWangeEditorRef1?.echo(row.faultDesc);
      this.$refs.ProWangeEditorRef2?.echo(row.solutionMeasures);
    },
    handleClickDel(row, index) {
      console.log(index);
      if (row.id) {
        this.delcase.push(row.id);
      }
      setTimeout(() => {
        this.caseData.splice(index, 1);
      }, 300);
    },
    handleClickEx(row, index) {
      this.exItemIndex = index;
      this.showDialog = true;
      this.form2 = row;
      this.xgVideo = row.relateVideo;
      this.czVideo = row.installVideo;
      this.$refs.ProWangeEditorRef1?.echo(row.faultDesc);
      this.$refs.ProWangeEditorRef2?.echo(row.solutionMeasures);
    },
    handleClick(tab, event) {
      console.log(tab);
      this.step = parseInt(this.activeName);
    },
    handleChange(item) {
      console.log(item);
      this.queryParam.productList = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productList.push(id);
      });
    },
    // handleChangeForm(item) {
    //   console.log(item);
    //   this.form.productList = [];
    //   item.map((el) => {
    //     const res = el[el.length - 1];
    //     // 取出最后一级id
    //     const id = res.substring(res.lastIndexOf("/") + 1, res.length);
    //     this.form.productList.push(id);
    //   });
    // },
    //加载表格
    loadData(parameter) {
      const requestParameters = Object.assign(this.queryParam, parameter);
      requestParameters.operateStart = requestParameters.updatedAt
        ? requestParameters.updatedAt[0]
        : null;
      requestParameters.operateEnd = requestParameters.updatedAt
        ? requestParameters.updatedAt[1]
        : null;
      delete requestParameters.updatedAt;
      baseByPageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    //初始化表单
    resetFrom() {
      this.step = 0;
      this.activeName = "0";
      this.form = {};
      this.form1 = {};
      this.form2 = {
        baseId: this.form.id,
        installVideo: [],
        relateVideo: [],
        itemIds: [],
      };
      this.form3 = { tags: [] };
      this.caseData = [];
      this.tableData1 = [];
      this.chooseModelSelection = [];
      this.choosePartSelection = [];
      this.partData = [];
      this.$refs.ProWangeEditorRef?.echo(null);
      this.$refs.ProWangeEditorRef1?.echo(null);
      this.$refs.ProWangeEditorRef2?.echo(null);
      localStorage.removeItem("detailHtml");
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },

    //第一步提交
    proSubmit(val) {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.confirmLoading = true;
          if (this.form.id) {
            this.update();
          } else {
            this.create();
          }
        } else {
          return false;
        }
      });
    },
    //第二、三、四步提交
    next() {
      if (this.step == 1) {
        // localStorage.setItem(
        //   "detailHtml",
        //   this.$refs.ProWangeEditorRef?.getContent()
        // );
        baseAddApi1({
          id: this.form.id,
          codeExplain: this.$refs.ProWangeEditorRef?.getContent() || "",
        }).then(() => {
          this.step = parseInt(this.step) + 1;
          this.activeName = this.step.toString();
          this.$message.success("代码解释维护成功");
        });
      } else if (this.step == 2) {
        const infosList = cloneDeep(this.caseData);
        infosList.map((ele) => {
          ele.itemIds = [];
          if (ele.caseItems) {
            ele.caseItems.map((el) => {
              ele.itemIds.push(el.id);
            });
          }
          delete ele.caseItems;
        });
        const obj1 = {
          deleteIds: this.delcase,
          infos: infosList,
          baseId: this.form.id,
        };
        baseAddApi2(obj1).then(() => {
          this.step = parseInt(this.step) + 1;
          this.activeName = this.step.toString();
          this.$message.success("维修案例维护成功");
        });
      } else if (this.step == 3) {
        this.$refs["form3"].validate((valid) => {
          if (valid) {
            this.form3.id = this.form.id;
            const obj = {
              ...this.form3,
              tags: this.form3.tags?.join(","),
            };
            baseAddApi3(obj).then(() => {
              this.$message.success("相关设置维护成功");
              this.$refs.ProTable.refresh();
              this.dialogVisible = false;
            });
          } else {
            return false;
          }
        });
      }
    },
    //返回
    back() {
      this.step = parseInt(this.step) - 1;
      this.activeName = this.step.toString();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.dialogTitle = "新增知识库";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;
    },
    //响应新增
    create() {
      baseAddApi(this.form)
        .then((res) => {
          this.form.id = res.data;
          console.log(this.form);
          this.$message.success("基础信息维护成功");
          this.step = parseInt(this.step) + 1;
          this.activeName = this.step.toString();
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑 - " + row.title ? row.title : "";
      this.resetFrom();
      baseInfoApi(row.id).then((res) => {
        this.form = {
          id: res.data.id,
          level: res.data.level?.value,
          productList: res.data.productList,
          type: res.data.type?.value,
          codeExplain: res.data.codeExplain,
        };
        this.tableData1 = res.data.ptm;
        this.form3 = {
          title: res.data.title,
          tags: res.data.tags ? res.data.tags.split(",") : [],
        };
        this.caseData = res.data.knowledgeRepairCase;
        this.methodType = "edit";
        this.dialogVisible = true;
      });
    },
    getParentsById(list, id) {
      for (const i in list) {
        if (list[i].value == id) {
          //查询到就返回该数组对象的value
          return [list[i].value];
        }
        if (list[i].children) {
          const node = this.getParentsById(list[i].children, id);
          if (node !== undefined) {
            //查询到把父节把父节点加到数组前面
            node.unshift(list[i].value);
            return node;
          }
        }
      }
    },
    //响应编辑
    update() {
      baseAddApi(this.form)
        .then((res) => {
          this.form.id = res.data;
          this.$message.success("基础信息维护成功");
          this.step = parseInt(this.step) + 1;
          this.activeName = this.step.toString();
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.title ? row.title : "";
      this.resetFrom();
      baseInfoApi(row.id).then((res) => {
        this.form = {
          id: res.data.id,
          level: res.data.level?.value,
          productList: res.data.productList,
          type: res.data.type?.value,
          codeExplain: res.data.codeExplain,
        };
        this.tableData1 = res.data.ptm;
        this.form3 = {
          title: res.data.title,
          tags: res.data.tags?.split(","),
        };
        this.caseData = res.data.knowledgeRepairCase;
        this.methodType = "info";
        this.dialogVisible = true;
      });
    },
    // 状态切换
    handleChangeStatus(row) {
      const params = {
        id: row.id,
        isEnable: row.isEnable,
      };
      baseOperateApi(params)
        .then((res) => {
          console.log(res);
        })
        .finally(() => {
          this.$refs.ProTable.refresh();
        });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        baseDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
    // 图片处理
    handleUploadSuccess(result) {
      this.form2.installVideo = [];
      this.form2.installVideo.push(result);
    },

    handleUploadRemove(file) {
      const index = this.form2.installVideo.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.form2.installVideo.splice(index, 1);
    },
    // 图片处理

    handleUploadSuccess1(result) {
      this.form2.relateVideo = [];
      this.form2.relateVideo.push(result);
    },
    handleUploadRemove1(file) {
      const index = this.form2.relateVideo.findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.form2.relateVideo.splice(index, 1);
    },
    async handleOpenChooseDialog(params) {
      this.showPartDialog = true;
      this.choosePartSelection = cloneDeep(this.partData) || [];
      this.$nextTick(() => {
        this.$refs.ChoosePartTable.$refs.ProTable.refresh();
        this.$refs.ChoosePartTable.$refs.ProTable.$refs.ProElTable.clearSelection();
        if (this.choosePartSelection.length > 0) {
          this.choosePartSelection.map((row) => {
            this.$refs.ChoosePartTable.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
              row
            );
          });
        }
      });
    },

    handleChooseDialogConfirm() {
      this.partData = cloneDeep(this.choosePartSelection);
      // this.partData.push(...this.choosePartSelection);
      this.showPartDialog = false;
      console.log(this.partData);
    },
    handleSelectionChange(val) {
      this.choosePartSelection = val;
    },
    handleDelOne(row) {
      this.partData = this.partData.filter((item) => item.id !== row.id);
    },
    handleClose(tag) {
      this.form3.tags.splice(this.form3.tags.indexOf(tag), 1);
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm() {
      const inputValue = this.inputValue;
      if (inputValue) {
        this.form3.tags.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    showModDialog() {
      this.showModelDialog = true;
      this.chooseModelSelection = cloneDeep(this.tableData1) || [];
      this.$nextTick(() => {
        this.$refs.ModelList.$refs.ProTable.$refs.ProElTable.clearSelection();
        console.log(this.chooseModelSelection);
        // if (this.chooseModelSelection.length > 0) {
        this.chooseModelSelection.map((row) => {
          this.$refs.ModelList.$refs.ProTable.$refs.ProElTable.toggleRowSelection(
            row
          );
        });
        // }
      });
    },

    handleSelectionChange1(val) {
      this.chooseModelSelection = cloneDeep(val);
    },
    handleChooseDialogConfirm1() {
      this.form.productList = [];
      this.tableData1 = cloneDeep(this.chooseModelSelection);
      // this.tableData1.push(...this.chooseModelSelection);
      this.showModelDialog = false;
      this.tableData1.map((el) => {
        this.form.productList.push(el.id);
      });
    },
    handleDeleteGoods(row) {
      this.form.productList = [];
      this.tableData1.splice(
        this.tableData1.findIndex((item) => item.id === row.id),
        1
      );
      this.chooseModelSelection.splice(
        this.chooseModelSelection.findIndex(
          (item) => item.id === row.productId
        ),
        1
      );
      this.tableData1.map((el) => {
        this.form.productList.push(el.id);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.btnbox {
  position: absolute;
  bottom: 30px;
  width: 100%;
  text-align: center;
}
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
.tit-box {
  padding: 5px 0;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 14px;
  font-weight: 800;
  clear: both;
}
.tit-box1 {
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  font-size: 16px;
  font-weight: 800;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 20px;
  }
}
</style>
