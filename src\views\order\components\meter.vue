<!--
 * @Author: wskg
 * @Date: 2025-02-15 14:34:05
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:58:44
 * @Description: 抄表记录 - 按月份
 -->
<template>
  <div class="container">
    <!-- 按月份展示 -->
    <!--<ProTable-->
    <!--  ref="ProTable"-->
    <!--  :query-param="queryParam"-->
    <!--  :local-pagination="localPagination"-->
    <!--  :columns="columns"-->
    <!--  :data="tableData"-->
    <!--  @loadData="loadData"-->
    <!--&gt;-->
    <!--  <template #action="{ row }">-->
    <!--    <div class="fixed-width">-->
    <!--      <el-button-->
    <!--        icon="el-icon-warning-outline"-->
    <!--        @click="handleEdit(row, 'info')"-->
    <!--      >-->
    <!--        详情-->
    <!--      </el-button>-->
    <!--      <el-button icon="el-icon-edit" @click="handleEdit(row, 'edit')">-->
    <!--        编辑-->
    <!--      </el-button>-->
    <!--    </div>-->
    <!--  </template>-->
    <!--</ProTable>-->
    <!-- 直接展示 -->
    <ProTable
      ref="infoTableData"
      :query-param="infoQueryParam"
      :local-pagination="infoLocalPagination"
      :columns="infoColumns"
      :data="infoTableData"
      :height="500"
      show-selection
      @loadData="loadInfoData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #btn>
        <el-button
          v-auth="['@ums:manage:meter:add']"
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleAddMeter"
        >
          新增抄表
        </el-button>
        <!--<el-button-->
        <!--  type="success"-->
        <!--  class="add-btn"-->
        <!--  size="mini"-->
        <!--  icon="el-icon-refresh"-->
        <!--  @click="reload"-->
        <!--&gt;-->
        <!--  重新计算-->
        <!--</el-button>-->
        <el-button
          v-auth="['@ums:manage:meter:create']"
          type="success"
          size="mini"
          icon="el-icon-plus"
          @click="confirmSelectCustomer"
        >
          生成对账单
        </el-button>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            icon="el-icon-warning-outline"
            @click="handleInfoEdit(row, 'info')"
          >
            详情
          </el-button>
          <el-button
            v-auth="['@ums:manage:meter:edit']"
            icon="el-icon-edit"
            @click="handleInfoEdit(row, 'edit')"
          >
            修改
          </el-button>
        </div>
      </template>
    </ProTable>
    <!--<ProDrawer-->
    <!--  :value="drawerVisible"-->
    <!--  :title="drawerTitle"-->
    <!--  size="80%"-->
    <!--  :no-footer="true"-->
    <!--  @cancel="drawerVisible = false"-->
    <!--&gt;-->
    <!--  <ProTable-->
    <!--    ref="infoTableData"-->
    <!--    :query-param="infoQueryParam"-->
    <!--    :columns="infoColumns"-->
    <!--    :data="infoTableData"-->
    <!--    @loadData="loadInfoData"-->
    <!--  >-->
    <!--    <template #action="{ row }">-->
    <!--      <div class="fixed-width">-->
    <!--        <el-button-->
    <!--          icon="el-icon-warning-outline"-->
    <!--          @click="handleInfoEdit(row, 'info')"-->
    <!--        >-->
    <!--          详情-->
    <!--        </el-button>-->
    <!--        <el-button icon="el-icon-edit" @click="handleInfoEdit(row, 'info')">-->
    <!--          修改-->
    <!--        </el-button>-->
    <!--      </div>-->
    <!--    </template>-->
    <!--  </ProTable>-->
    <!--</ProDrawer>-->

    <!-- 抄表数据详情 -->
    <MeterDetails ref="meterDetails" @refresh="refresh" />
    <!-- 手动添加抄表单 -->
    <AddMeterOrder ref="addMeterOrder" @refresh="refresh" />
    <!-- 选择客户重新计算抄表数据 -->
    <ReloadMeter ref="reloadMeter" @refresh="refresh" />
    <!-- 创建对账单 -->
    <CreateMeterOrder ref="createMeterOrder" @ok="createdOk" />
  </div>
</template>

<script>
import MeterDetails from "@/views/order/components/meterDetails.vue";
import AddMeterOrder from "@/views/order/components/addMeterOrder.vue";
import ReloadMeter from "@/views/order/components/reloadMeter.vue";
import CreateMeterOrder from "@/views/order/components/createMeterOrder.vue";
import { cloneDeep } from "lodash";
import { meterExportApi, meterListApi } from "@/api/statisics";
import { handleExcelExport } from "@/utils/exportExcel";
import { filterParam, filterParamRange } from "@/utils";

export default {
  name: "Meter",
  components: {
    MeterDetails,
    AddMeterOrder,
    ReloadMeter,
    CreateMeterOrder,
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "cycle",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "blackWhiteExclude",
          title: "黑白废张",
          isTable: true,
        },
        {
          dataIndex: "colorExclude",
          title: "彩色废张",
          isTable: true,
        },
        {
          dataIndex: "fiveExclude",
          title: "第五色废张",
          isTable: true,
        },
        {
          dataIndex: "blackWhitePoint",
          title: "黑白总印量",
          isTable: true,
        },
        {
          dataIndex: "colorPoint",
          title: "彩色总印量",
          isTable: true,
        },
        {
          dataIndex: "fiveColourPoint",
          title: "第五色总印量",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          width: 120,
        },
      ],
      tableData: [],
      editType: "info",
      // drawer
      drawerVisible: false,
      drawerTitle: "",
      // 明细数据
      infoQueryParam: {},
      infoLocalPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      infoColumns: [
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          valueType: "input",
          isSearch: true,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          width: 140,
          valueType: "input",
          isSearch: true,
          isTable: true,
        },
        {
          dataIndex: "brand",
          title: "品牌",
          valueType: "select",
          isTable: true,
        },
        {
          dataIndex: "machine",
          title: "机型",
          valueType: "select",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          valueType: "input",
          isTable: true,
          isSearch: true,
          placeholder: "1号机： 1",
          option: [],
          formatter: (row) => row.deviceGroup.label,
          width: 100,
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => row.serType?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "质保服务",
              value: "QA",
            },
            {
              label: "质保含部件",
              value: "QA_COMPONENT",
            },
            {
              label: "维保服务",
              value: "MAINTENANCE",
            },
          ],
          width: 100,
        },
        {
          dataIndex: "cycle",
          title: "年月",
          isTable: true,
        },
        {
          dataIndex: "settmentStatus",
          title: "结算状态",
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "已结算",
              value: 2,
            },
            {
              label: "结算中",
              value: 1,
            },
            {
              label: "未结算",
              value: 0,
            },
          ],
          formatter: (row) => {
            switch (row.settmentStatus) {
              case 0:
                return "未结算";
              case 1:
                return "结算中";
              case 2:
                return "已结算";
              default:
                return "";
            }
          },
        },
        {
          dataIndex: "cycleType",
          title: "结算周期",
          isTable: true,
          formatter: (row) => row.cycleType?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "月度付",
              value: "MONTH",
            },
            {
              label: "季度付",
              value: "QUARTER",
            },
            {
              label: "半年付",
              value: "HALF_YEAR",
            },
            {
              label: "年度付",
              value: "YEAR",
            },
          ],
        },
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "pricePaperType",
          title: "计数方式",
          isTable: true,
        },
        {
          dataIndex: "beginTime",
          title: "期初时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "blackWhiteInception",
          title: "期初黑白",
          isTable: true,
        },
        {
          dataIndex: "colorInception",
          title: "期初彩色",
          isTable: true,
        },
        {
          dataIndex: "fiveColourIncption",
          title: "期初五色",
          isTable: true,
        },
        {
          dataIndex: "endTime",
          title: "期末时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "blackWhiteCutoff",
          title: "期末黑白",
          isTable: true,
        },
        {
          dataIndex: "colorCutoff",
          title: "期末彩色",
          isTable: true,
        },
        {
          dataIndex: "fiveColourCutoff",
          title: "期末五色",
          isTable: true,
        },

        {
          dataIndex: "blackWhitePoint",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorPoint",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "giveColorPoint",
          title: "五色印量",
          isTable: true,
        },
        {
          dataIndex: "totalPoint",
          title: "总印量",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "总金额",
          valueType: "input",
          width: 90,
          isTable: true,
        },
        {
          dataIndex: "blackRange",
          title: "黑白印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "colorRange",
          title: "彩色印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "totalRange",
          title: "总印量",
          isSearch: true,
          valueType: "inputRange",
        },

        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          fixed: "right",
          width: 150,
        },
      ],
      infoTableData: [],
      // 选取生成对账的客户
      customerList: [],

      requestParameters: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    /**
     * 加载列表数据
     * @param parameter
     */
    loadData(parameter) {
      this.queryParam = Object.assign({}, this.queryParam, parameter);
      const requestParameters = cloneDeep(this.queryParam);
    },
    /**
     * 加载月份抄表明细数据
     * @param parameter
     */
    loadInfoData(parameter) {
      this.infoQueryParam = filterParam(
        Object.assign({}, this.infoQueryParam, parameter)
      );
      const searchRange = [
        {
          beginBlackWhite: null,
          endBlackWhite: null,
          data: parameter.blackRange,
        },
        {
          beginColor: null,
          endColor: null,
          data: parameter.colorRange,
        },
        {
          beginCount: null,
          endCount: null,
          data: parameter.totalRange,
        },
      ];
      filterParamRange(this, this.infoQueryParam, searchRange);
      this.requestParameters = cloneDeep(this.infoQueryParam);
      ["blackRange", "colorRange", "totalRange"].forEach(
        (key) => delete this.requestParameters[key]
      );
      meterListApi(this.requestParameters)
        .then((res) => {
          this.infoTableData = res.data.rows;
          this.infoLocalPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.infoTableData
            ? (this.$refs.infoTableData.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    // 新增抄表单
    handleAddMeter() {
      this.$refs.addMeterOrder.show();
    },
    reload() {
      this.$refs.reloadMeter.show();
    },
    /**
     * 选择抄表单生成对账单
     * @param val
     */
    handleSelectionChange(val) {
      if (val.length > 0) {
        val.forEach((item, index) => {
          if (item.settmentStatus) {
            this.$message.error("该客户账单已结算");
            this.$refs.infoTableData.$refs.ProElTable.toggleRowSelection(
              item,
              false
            );
          } else {
            this.customerList = val;
          }
        });
      }
    },
    confirmSelectCustomer() {
      if (!this.customerList.length) {
        this.$message.error("请选择客户");
        return;
      }
      this.$refs.createMeterOrder.show(this.customerList);
      // if (this.customerList.length > 0) {
      //   if (
      //     this.customerList.every(
      //       (item) => item.customerSeq === this.customerList[0].customerSeq
      //     )
      //   ) {
      //     this.$refs.addMeterOrder.show(this.customerList);
      //   } else {
      //     this.$message.error("请选择同一客户");
      //   }
      // }
    },
    // 查看当前月份抄表记录
    handleEdit(row, type) {
      this.editType = type;
      this.drawerTitle = `抄表记录 - ${row.cycle}`;
      this.drawerVisible = true;
    },
    // 查看客户抄表明细
    handleInfoEdit(row, type) {
      this.$refs.meterDetails.show(row, type);
    },
    createdOk() {
      this.refresh();
      this.$refs.infoTableData.$refs.ProElTable.clearSelection();
    },

    refresh() {
      this.$refs.infoTableData.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
