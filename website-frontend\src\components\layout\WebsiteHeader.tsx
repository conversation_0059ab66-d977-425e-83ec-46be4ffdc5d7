import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Layout, Menu, Button, Drawer, Space, Skeleton } from 'antd';
import { MenuOutlined, PhoneOutlined, MailOutlined } from '@ant-design/icons';
import { usePublicConfig } from '@/hooks/useWebsiteApi';
import { useWebsiteMenus } from '@/hooks/useWebsiteMenus';
// 暂时注释ThemeToggle，避免上下文错误
// import { ThemeToggle } from '@/components/common/ThemeProvider';

const { Header } = Layout;

interface MenuItem {
  key: string;
  label: string;
  path: string;
  children?: MenuItem[];
}

/**
 * 企业官网桌面端头部组件
 */
export function WebsiteHeader() {
  const location = useLocation();
  const { data: config } = usePublicConfig();
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  // 动态导航菜单
  const { data: menus = [], isLoading: menuLoading } = useWebsiteMenus();

  const menuItems: MenuItem[] = menus.length > 0 ? menus : [
    { key: 'ABOUT_PAGE', label: '关于我们', path: '/about' },
    { key: 'SERVICE_PAGE', label: '服务介绍', path: '/services' },
    { key: 'CASE_PAGE', label: '案例展示', path: '/cases' },
    { key: 'CONTACT_PAGE', label: '联系我们', path: '/contact' },
  ];

  // 监听滚动事件
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      setIsScrolled(scrollTop > 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 获取当前激活的菜单项
  const getActiveKey = () => {
    const currentPath = location.pathname;
    const activeItem = menuItems.find(item => 
      item.path === currentPath || (item.path !== '/' && currentPath.startsWith(item.path))
    );
    return activeItem?.key || (menuItems.length > 0 ? menuItems[0].key : 'ABOUT_PAGE');
  };

  // 渲染导航菜单
  const renderMenu = () => (
    menuLoading ? (
      <Skeleton.Input active size="default" style={{ width: 200 }} />
    ) : (
      <Menu
        mode="horizontal"
        selectedKeys={[getActiveKey()]}
        className="flex-1 border-none bg-transparent"
        items={menuItems.map(item => ({
          key: item.key,
          label: (
            <Link 
              to={item.path}
              className="px-4 py-2 text-gray-700 hover:text-blue-600 transition-colors"
            >
              {item.label}
            </Link>
          ),
        }))}
      />
    )
  );

  // 渲染联系信息
  const renderContactInfo = () => (
    <Space size="large" className="hidden lg:flex text-sm text-gray-600">
      {config?.contactPhone && (
        <Space size={4}>
          <PhoneOutlined />
          <span>{config.contactPhone}</span>
        </Space>
      )}
      {config?.contactEmail && (
        <Space size={4}>
          <MailOutlined />
          <span>{config.contactEmail}</span>
        </Space>
      )}
    </Space>
  );

  return (
    <>
      {/* 顶部联系栏已移除，根据需求隐藏 */}

      {/* 主导航栏 */}
      <Header
        className={`
          fixed top-0 left-0 right-0 z-50 px-0 h-16
          transition-all duration-300 ease-in-out
          ${isScrolled 
            ? 'bg-white shadow-lg border-b border-gray-200' 
            : 'bg-white'
          }
        `}
        style={{ lineHeight: '64px' }}
      >
        <div className="max-w-7xl mx-auto px-4 flex items-center justify-between h-full">
          {/* Logo区域 */}
          <div className="flex items-center space-x-4">
            <Link to="/" className="flex items-center space-x-3">
              {config?.headerLogoUrl?.trim() ? (
                <img
                  src={config.headerLogoUrl.trim()}
                  alt="logo"
                  className="w-10 h-10 object-contain cursor-pointer"
                  onError={(e) => (e.currentTarget.style.display = 'none')}
                />
              ) : (
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">
                    {(config?.siteTitle)?.charAt(0).toUpperCase() || 'L'}
                  </span>
                </div>
              )}
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-gray-900">
                  {config?.siteTitle || '复印机维修'}
                </h1>
              </div>
            </Link>
          </div>

          {/* 桌面端导航菜单 */}
          <div className="hidden lg:flex items-center flex-1 justify-center">
            {renderMenu()}
          </div>

          {/* 右侧操作区 */}
          <div className="flex items-center space-x-4">
            {/* 暂时注释ThemeToggle，避免上下文错误 */}
            {/* <ThemeToggle /> */}
            
            {/* 咨询按钮 */}
            <Button 
              type="primary" 
              size="large"
              className="hidden md:inline-flex"
              href="/contact"
            >
              立即咨询
            </Button>

            {/* 移动端菜单按钮 */}
            <Button
              type="text"
              icon={<MenuOutlined />}
              className="lg:hidden"
              onClick={() => setMobileMenuVisible(true)}
            />
          </div>
        </div>
      </Header>

      {/* 移动端侧边菜单 */}
      <Drawer
        title={config?.siteTitle || '导航菜单'}
        placement="right"
        width={280}
        open={mobileMenuVisible}
        onClose={() => setMobileMenuVisible(false)}
        className="lg:hidden"
      >
        <div className="space-y-4">
          {menuItems.map(item => (
            <Link
              key={item.key}
              to={item.path}
              className="block px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors"
              onClick={() => setMobileMenuVisible(false)}
            >
              {item.label}
            </Link>
          ))}
          
          <div className="pt-4 border-t border-gray-200">
            <Button 
              type="primary" 
              size="large"
              block
              href="/contact"
              onClick={() => setMobileMenuVisible(false)}
            >
              立即咨询
            </Button>
          </div>

          {/* 联系信息 */}
          {(config?.contactPhone || config?.contactEmail) && (
            <div className="pt-4 border-t border-gray-200 space-y-2">
              {config?.contactPhone && (
                <div className="flex items-center space-x-2 text-gray-600">
                  <PhoneOutlined />
                  <span>{config.contactPhone}</span>
                </div>
              )}
              {config?.contactEmail && (
                <div className="flex items-center space-x-2 text-gray-600">
                  <MailOutlined />
                  <span>{config.contactEmail}</span>
                </div>
              )}
            </div>
          )}
        </div>
      </Drawer>
    </>
  );
} 