<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 配置预览对话框
-->
<template>
  <el-dialog
    title="配置预览"
    :visible.sync="dialogVisible"
    width="700px"
    :before-close="handleClose"
    :modal="false"
  >
    <div v-loading="loading" class="config-preview">
      <div v-if="config" class="config-content">
        <!-- 基本信息 -->
        <div class="config-section">
          <h3 class="section-title">基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="配置名称">
              {{ config.configName || '未命名配置' }}
            </el-descriptions-item>
            <el-descriptions-item label="日志级别">
              <el-tag :type="getLogLevelType(config.logLevel)">
                {{ config.logLevel }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="位置日志">
              <el-tag :type="config.enableLocationLog ? 'success' : 'info'">
                {{ config.enableLocationLog ? '启用' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="上传间隔">
              {{ formatInterval(config.logUploadInterval) }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">
              {{ config.createTime || '未知' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 详细配置 -->
        <div class="config-section">
          <h3 class="section-title">详细配置</h3>
          <div class="config-json">
            <pre>{{ formatConfigJson(config) }}</pre>
          </div>
        </div>

        <!-- 分发信息 -->
        <div class="config-section" v-if="config.distributionInfo">
          <h3 class="section-title">分发信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="分发ID">
              <span class="distribution-id">{{ config.distributionInfo.distributionId }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="分发状态">
              <el-tag :type="getDistributionStatusType(config.distributionInfo.distributionStatus)">
                {{ getDistributionStatusText(config.distributionInfo.distributionStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="目标类型">
              <el-tag :type="config.distributionInfo.targetType === 'USER' ? 'primary' : 'warning'">
                {{ config.distributionInfo.targetType === 'USER' ? '用户' : '设备' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="目标名称">
              {{ config.distributionInfo.targetName || config.distributionInfo.targetId }}
            </el-descriptions-item>
            <el-descriptions-item label="分配时间" :span="2">
              {{ config.distributionInfo.assignTime || '未知' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 应用范围 -->
        <div class="config-section" v-if="config.appliedTargets && config.appliedTargets.length > 0">
          <h3 class="section-title">应用范围</h3>
          <div class="applied-targets">
            <el-tag
              v-for="target in config.appliedTargets"
              :key="target.targetId"
              :type="target.targetType === 'USER' ? 'primary' : 'warning'"
              class="target-tag"
            >
              {{ target.targetType === 'USER' ? '用户' : '设备' }}: {{ target.targetName || target.targetId }}
            </el-tag>
          </div>
        </div>

        <!-- 配置说明 -->
        <div class="config-section" v-if="config.description">
          <h3 class="section-title">配置说明</h3>
          <div class="config-description">
            {{ config.description }}
          </div>
        </div>
      </div>
      
      <div v-else class="no-config">
        <el-empty description="暂无配置信息" />
      </div>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="copyConfig">复制配置</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'ConfigPreviewDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    getLogLevelType(level) {
      const types = {
        'DEBUG': 'info',
        'INFO': 'success',
        'WARN': 'warning',
        'ERROR': 'danger'
      }
      return types[level] || ''
    },
    
    formatInterval(interval) {
      if (!interval) return '未设置'
      
      const seconds = Math.floor(interval / 1000)
      if (seconds < 60) return `${seconds}秒`
      
      const minutes = Math.floor(seconds / 60)
      if (minutes < 60) return `${minutes}分钟`
      
      const hours = Math.floor(minutes / 60)
      return `${hours}小时`
    },
    
    formatConfigJson(config) {
      if (!config) return ''
      
      // 创建一个用于显示的配置对象
      const displayConfig = {
        configName: config.configName,
        logLevel: config.logLevel,
        enableLocationLog: config.enableLocationLog,
        logUploadInterval: config.logUploadInterval,
        maxLogFileSize: config.maxLogFileSize,
        maxLogFiles: config.maxLogFiles,
        enableCrashReport: config.enableCrashReport,
        customSettings: config.customSettings
      }
      
      return JSON.stringify(displayConfig, null, 2)
    },
    
    copyConfig() {
      if (!this.config) return
      
      const configText = this.formatConfigJson(this.config)
      
      navigator.clipboard.writeText(configText).then(() => {
        this.$message.success('配置已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    // 分发状态相关方法
    getDistributionStatusType(status) {
      const statusTypes = {
        'PENDING': 'info',
        'ASSIGNED': 'warning',
        'APPLIED': 'success'
      }
      return statusTypes[status] || 'info'
    },

    getDistributionStatusText(status) {
      const statusTexts = {
        'PENDING': '待应用',
        'ASSIGNED': '已分配',
        'APPLIED': '已应用'
      }
      return statusTexts[status] || '未知'
    },

    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.config-preview {
  .config-content {
    .config-section {
      margin-bottom: 24px;
      
      .section-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 8px;
      }
      
      .config-json {
        background-color: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 16px;
        
        pre {
          margin: 0;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          line-height: 1.5;
          color: #606266;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
      
      .applied-targets {
        .target-tag {
          margin-right: 8px;
          margin-bottom: 8px;
        }
      }
      
      .config-description {
        padding: 12px;
        background-color: #f0f9ff;
        border: 1px solid #b3d8ff;
        border-radius: 4px;
        color: #606266;
        line-height: 1.6;
      }
    }
  }
  
  .no-config {
    text-align: center;
    padding: 40px 0;
  }

  // 分发ID样式
  .distribution-id {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #606266;
    background-color: #f5f7fa;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #e4e7ed;
  }
}
</style>
