<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-30 17:25:56
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-index
      show-search
      show-loading
      show-pagination
      @loadData="loadData"
    >
      <!-- <template #btn>
        <el-button type="success" class="add-btn" size="mini" icon="el-icon-plus" @click="handleEdit(null, 'add')">新增</el-button>
      </template> -->
      <template #remarks="{ row }">
        <!-- <el-tooltip effect="dark" :content="row.remarks" placement="top"> -->
        <div style="display: flex; align-items: center">
          <span class="exceed">{{ row.remarks }}</span>
          <!-- </el-tooltip> -->
          <span
            style="margin-left: 10px; cursor: pointer"
            @click="handleUpdateRemark(row, 'add')"
            ><i class="el-icon-edit"></i
          ></span>
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleEdit(row.id, 'info', row.warehouseName)"
          >
            查看
          </el-button>
          <el-button
            v-if="
              row?.outStatus?.value == 'dck' || row?.outStatus?.value == 'bfck'
            "
            type="btn3"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleEdit(row.id, 'check', row.warehouseName)"
          >
            审核
          </el-button>
          <!-- <el-button type="primary" size="mini" icon="el-icon-edit-outline" @click="handleEdit(row, 'edit')">编辑</el-button>
          <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button> -->
        </div>
      </template>
    </ProTable>

    <!-- 新增/编辑 -->
    <ProDrawer
      :value="showDrawer"
      :title="drawerTitle"
      size="80%"
      :destroy-on-close="true"
      :no-footer="editType === 'info'"
      @ok="handleDrawerOk"
      @cancel="handleCloseDrawer"
    >
      <ProForm
        ref="editFrom"
        :form-param="editForm"
        :form-list="columns"
        :confirm-loading="editFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="editType"
        @proSubmit="editFormSubmit"
      >
        <template #mainWaybill>
          <el-input
            v-model="editForm.mainWaybill"
            :disabled="editType === 'info' && !showCheckBtn"
          ></el-input>
        </template>

        <template #secondlyWaybill>
          <el-input
            v-model="editForm.secondlyWaybill"
            :disabled="editType === 'info' && !showCheckBtn"
          ></el-input>
        </template>

        <template #remarks>
          <el-input
            v-model="editForm.remarks"
            type="textarea"
            :rows="3"
            :disabled="editType === 'info' && !showCheckBtn"
          ></el-input>
          <div style="height: 50px"></div>
        </template>

        <!-- 订单商品信息 -->
        <template #itemList>
          <ProTable
            ref="ProTable2"
            row-key="id"
            :data="orderTableData"
            :columns="orderColumns"
            :show-index="false"
            :show-search="false"
            :height="400"
            :show-loading="false"
          >
            <template #img="slotProps">
              <el-image
                v-if="
                  slotProps.row.storageArticleA.imageFiles &&
                  slotProps.row.storageArticleA.imageFiles.length !== 0
                "
                style="width: 100px; height: 100px; cursor: pointer"
                :src="slotProps.row.storageArticleA?.imageFiles?.[0].url"
                @click="
                  handlePreview(
                    slotProps.row.storageArticleA?.imageFiles?.[0].url
                  )
                "
              ></el-image>
              <div v-else>暂无</div>
            </template>
            <template #action="{ row }">
              <div v-if="editType === 'info'" class="fixed-width">
                <el-button
                  v-if="row.auidtOutWarehouseNumber > 0"
                  size="mini"
                  type="primary"
                  @click="handleRkLook(row)"
                >
                  出库明细
                </el-button>
                <el-button
                  v-if="
                    row.outStatus?.value == 'dck' ||
                    row.outStatus?.value == 'bfck'
                  "
                  size="mini"
                  type="btn3"
                  @click="handleChooseCheck(row)"
                >
                  出库审核
                </el-button>
                <!-- <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-plus"
                  @click="handleChooseBatch(row)"
                  >选择批次</el-button
                > -->
              </div>
            </template>
          </ProTable>
        </template>

        <!-- 已选择的库品列表 -->
        <!-- <template #goodsDataList>
          <ProTable
            ref="ProTable1"
            row-key="id"
            :data="goodTableData"
            :columns="goodColumns"
            show-index
            :height="400"
            :show-loading="false"
          >
            <template #btn>
              <el-button
                v-if="editType !== 'info'"
                type="success"
                class="add-btn"
                size="mini"
                icon="el-icon-plus"
                @click="handleOpenGoodDialog"
                >选择库品</el-button
              >
            </template>
            <template #outWarehouseNumber="{ row }">
              <el-input-number
                v-model="row.outWarehouseNumber"
                :disabled="!showCheckBtn"
                :step="1"
                step-strictly
                :min="0"
                :max="row.sumWarehouseNumber"
                :controls="false"
                size="mini"
                placeholder="请输入出库量"
              />
            </template>
            <template #action="{ row }">
              <div v-if="showCheckBtn" class="fixed-width">
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDeleteGoods(row)"
                  >删除</el-button
                >
              </div>
            </template>
          </ProTable>
        </template> -->
      </ProForm>
      <!-- <div v-if="showCheckBtn" class="dialog-footer1">
        <div class="btn-box">
          <el-button type="primary" @click="handleCheckPass('2402')"
            >确认出库</el-button
          >
          <el-button @click="handleCloseDrawer">取消</el-button>
        </div>
      </div> -->
      <el-dialog :visible.sync="showPreview" top="5vh" :modal="false">
        <img class="preview-img" width="95%" :src="previewImgUrl" alt="" />
      </el-dialog>
    </ProDrawer>

    <!-- 新加库品库品弹窗 -->
    <ProDialog
      :value="showDialogCheck"
      title="出库审核"
      width="500px"
      :no-footer="false"
      :confirm-loading="dialogLoading"
      top="50px"
      @ok="handleDialogCheckConfirm"
      @cancel="showDialogCheck = false"
    >
      <ProForm
        ref="editFormCheck"
        :form-param="editFormCheck"
        :form-list="columnsCheck"
        :confirm-loading="dialogLoading"
        :layout="{ formWidth: '100%', labelWidth: '150px' }"
        :open-type="'add'"
        @proSubmit="editFormCheckSubmit"
      >
        <!-- <template #model>
          <el-cascader
            v-model="editForm.model"
            filterable
            style="width: 100%"
            :disabled="editType === 'info'"
            :options="productTreeOption"
            :props="{ label: 'name', value: 'id' }"
            @change="handleProductTree"
          >
          </el-cascader>
        </template> -->
      </ProForm>
    </ProDialog>
    <!-- 出库明细 -->
    <ProDialog
      :value="rkdialogLook"
      :title="'出库明细'"
      width="400px"
      :top="'10%'"
      :no-footer="true"
      @cancel="rkdialogLook = false"
    >
      <div style="height: 400px; overflow-y: scroll">
        <el-timeline>
          <el-timeline-item v-for="(item, index) in rkLook" :key="index">
            <el-card>
              <div>出库数量：{{ item.number }}</div>
              <div>批次号：{{ item.batchCode }}</div>
              <div>出库时间：{{ item.time }}</div>
              <div>操作人：{{ item.operatorName }}</div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </ProDialog>
    <!-- 新加库品库品弹窗 -->
    <ProDialog
      :value="showDialog"
      title="选择库品"
      width="1000px"
      :no-footer="false"
      :confirm-loading="dialogLoading"
      top="50px"
      @ok="handleDialogConfirm"
      @cancel="handleDialogCancel"
    >
      <ProTable
        ref="AddGoodsTable"
        row-key="id"
        :data="addGoodsTableData"
        :columns="addGoodsColumns"
        :height="400"
        :query-param="addGoodsQueryParam"
        :local-pagination="addGoodsLocalPagination"
        show-index
        show-selection
        :show-search="false"
        show-loading
        show-pagination
        @loadData="loadGoodsData"
        @handleSelectionChange="handleChooseBatchSelection"
      >
      </ProTable>
    </ProDialog>

    <!-- 领料详情框  -->
    <ProDrawer
      :value="recdialogVisible"
      :title="'领料单详情'"
      size="60%"
      :top="'10%'"
      :no-footer="true"
      @cancel="recdialogVisible = false"
    >
      <ProForm
        v-if="recdialogVisible"
        ref="proform"
        :form-param="recform"
        :form-list="recformcolumns"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="'info'"
      >
        <template #productTree>
          <el-popover placement="bottom" title="" width="400" trigger="click">
            <div style="margin: 20px; height: 400px; overflow-y: auto">
              <el-descriptions
                class="margin-top"
                title="所属品牌/产品树"
                :column="1"
                border
              >
                <el-descriptions-item
                  v-for="item in form.productTreeDtoList"
                  :key="item.id"
                >
                  <template slot="label"> 品牌/系列/机型 </template>
                  {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <el-button slot="reference" size="mini">查看</el-button>
          </el-popover>
        </template>
        <template #status>
          {{
            recform.status == "CREATE"
              ? "提交申请"
              : recform.status == "WAIT_EX_WAREHOUSE"
              ? "待出库"
              : recform.status == "REFUSE_EX_WAREHOUSE"
              ? "拒绝出库"
              : recform.status == "PARTIAL_DONE"
              ? "部分完成"
              : recform.status == "DONE"
              ? "完成"
              : "/"
          }}</template
        >
        <template #location>
          <DataTable
            :columns="columns1"
            :show-setting="false"
            :show-pagination="false"
            :show-search="false"
            row-key="id"
            :data="recform.applyDetailList"
            sticky
            :height="350"
          >
            <template #saleAttrVals="slotProps">
              <div
                v-for="attr in slotProps.row?.skuInfo?.saleAttrVals"
                :key="attr.val"
              >
                {{ attr.name }}:{{ attr.val }}
              </div>
            </template>
          </DataTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 商品订单详情框  -->
    <ProDrawer
      :value="orderdialogVisible"
      :title="'商品订单详情'"
      size="60%"
      :top="'10%'"
      :no-footer="true"
      @cancel="orderdialogVisible = false"
    >
      <!-- 买家下单 -->
      <div v-if="orderInfo" class="order-fix">
        <!-- <p class="text-p m-b-8">买家还有<span>10分00秒</span>支付订单。</p> -->
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="订单信息"
            :column="3"
            border
          >
            <el-descriptions-item label="订单状态">
              {{ getOrderStatusChinese(orderInfo.tradeOrder.orderStatus) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单编号">
              {{ orderInfo.tradeOrder.orderNum }}
            </el-descriptions-item>
            <el-descriptions-item label="支付金额（元）">
              {{ orderInfo.tradeOrder.paidAmount }}
            </el-descriptions-item>
            <el-descriptions-item label="关联客户">
              {{ orderInfo.companyName }}
            </el-descriptions-item>
            <el-descriptions-item label="下单用户">
              {{ orderInfo.buyerName }}
            </el-descriptions-item>
            <el-descriptions-item label="客户等级">
              {{ orderInfo?.tradeOrder?.customerLevel?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="下单手机号">
              {{ orderInfo?.tradeOrder?.consigneePhone }}
            </el-descriptions-item>
            <el-descriptions-item label="支付方式"> 微信 </el-descriptions-item>
            <el-descriptions-item label="配送方式">
              {{ orderInfo?.tradeOrder?.logisticsProvider?.label }}
            </el-descriptions-item>
            <el-descriptions-item label="收货地址">
              {{ orderInfo.tradeOrder.consigneeFullAddress }}
            </el-descriptions-item>
            <p class="btn-p">
              <el-button
                v-if="orderInfo.tradeOrder.orderStatus == 'WAIT_PAY'"
                @click="showGbddDialog()"
                >关闭订单</el-button
              >
            </p>
          </el-descriptions>
          <!-- <p class="text-p">
            <label class="p-label">订单状态：</label>
            <span class="p-content red">{{
              getOrderStatusChinese(orderInfo.tradeOrder.orderStatus)
            }}</span>
          </p>
          <div class="content-fixed">
            <p class="text-p">
              <label class="p-label">订单编号：</label>
              <span class="p-content">{{ orderInfo.tradeOrder.orderNum }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">支付金额（元）：</label>
              <span class="p-content red">{{
                orderInfo.tradeOrder.payAmount
              }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">关联客户：</label>
              <a class="p-a" href="#">{{ orderInfo.companyName }}</a>
            </p>
          </div>
          <div class="content-fixed">
            <p class="text-p">
              <label class="p-label">下单用户：</label>
              <span class="p-content">{{ orderInfo.buyerName }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">下单手机号：</label>
              <span class="p-content">{{
                orderInfo.tradeOrder.consigneePhone
              }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">支付方式：</label>
              <span class="p-content">微信</span>
            </p>
          </div>
          <p class="text-p">
            <label class="p-label">收货地址：</label>
            <span class="p-content">{{
              orderInfo.tradeOrder.consigneeAddress
            }}</span>
          </p>
          <p class="btn-p">
            <el-button
              v-if="orderInfo.tradeOrder.orderStatus == 'WAIT_PAY'"
              @click="showGbddDialog()"
              >关闭订单</el-button
            >
          </p> -->
        </div>
        <!-- 商品信息 -->
        <div class="m-t-8">
          <p class="tit-box m-b-12">商品信息</p>
          <ProTable
            ref="ProSPXXTable"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :columns="spxxColumns"
            :show-pagination="false"
            :show-loading="false"
            :data="orderInfo.tradeOrder.tradeOrderDetailList || []"
            :show-setting="false"
            :show-search="false"
            :show-table-operator="false"
            sticky
            :height="200"
          >
          </ProTable>
          <p class="text-p right">
            <label class="p-label">订单商品金额（元）：</label>
            <span class="p-content">{{
              orderInfo.tradeOrder.actualGoodsAmount
            }}</span>
          </p>
          <p class="text-p right">
            <label class="p-label">会员减免（元）：</label>
            <span class="p-content"
              >-{{ orderInfo.tradeOrder.discountAmount }}</span
            >
          </p>
          <p class="text-p right">
            <label class="p-label">订单运费（元）：</label>
            <span class="p-content">{{
              orderInfo.tradeOrder.shippingFee
            }}</span>
          </p>

          <p class="text-p right">
            <label class="p-label">实收款（元）：</label>
            <span class="p-content">{{
              orderInfo.tradeOrder.actualAmount
            }}</span>
          </p>
          <p class="text-p m-b-8">
            <label class="p-label">订单备注：</label>
            <span class="p-content">{{
              orderInfo.tradeOrder.buyerRemark
            }}</span>
          </p>
        </div>

        <!-- 物流信息 -->
        <div v-if="false" class="m-t-8">
          <p class="title-p m-b-12">物流信息</p>
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in wlxxData"
              :key="index"
              :icon="item.icon"
              :type="item.type"
              :color="item.color"
              :size="item.size"
              :timestamp="item.timestamp"
              placement="top"
            >
              <span v-html="item.content"></span>
            </el-timeline-item>
          </el-timeline>
        </div>
        <!-- 交易明细 -->
        <div class="order-border-box">
          <el-descriptions
            class="margin-top"
            title="交易明细"
            :column="3"
            border
          >
            <el-descriptions-item label="订单来源">
              销售订单
            </el-descriptions-item>
            <el-descriptions-item label="订单创建时间">
              {{ orderInfo.tradeOrder.createdAt }}
            </el-descriptions-item>
            <el-descriptions-item label="订单支付时间">
              {{ orderInfo.tradeOrder.payTime }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="orderInfo.deliveryTime"
              label="订单发货时间"
            >
              {{ orderInfo.deliveryTime }}
            </el-descriptions-item>
            <el-descriptions-item label="订单确认收货时间">
              {{ orderInfo.tradeOrder.finishTime }}
            </el-descriptions-item>
          </el-descriptions>
          <!-- <p class="tit-box m-b-12">交易明细</p> -->
          <!-- <div class="content-fixed">
            <p class="text-p">
              <label class="p-label">订单来源：</label>
              <span class="p-content">销售订单</span>
            </p>
            <p class="text-p">
              <label class="p-label">订单创建时间：</label>
              <span class="p-content">{{
                orderInfo.tradeOrder.createdAt
              }}</span>
            </p>
            <p class="text-p">
              <label class="p-label">订单支付时间：</label>
              <span class="p-content">{{ orderInfo.tradeOrder.payTime }}</span>
            </p>
          </div>
          <div class="content-fixed">
            <p class="text-p">
              <label class="p-label">订单发货时间：</label>
              <span class="p-content">{{
                orderInfo.tradeOrder.expectedDeliveryTime
              }}</span>
            </p>
          </div>
          <p class="text-p">
            <label class="p-label">订单确认收货时间：</label>
            <span class="p-content">{{ orderInfo.tradeOrder.finishTime }}</span>
          </p> -->
        </div>
      </div>
    </ProDrawer>
    <!-- 备注修改 -->
    <ProDialog
      :value="remarkDialog"
      :title="'编辑备注'"
      :confirm-text="'保存'"
      width="600px"
      :top="'10%'"
      @ok="updateRemark"
      @cancel="handleCloseRemarkDialog"
    >
      <ProForm
        ref="editForm"
        :form-param="remarkForm"
        :form-list="remarkColumns"
        :confirm-loading="remarkFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '70px' }"
        :open-type="editType"
        @proSubmit="updateRemark"
      >
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import ProDrawer from "@/components/ProDrawer/index.vue";
import ProForm from "@/components/ProForm/index.vue";
import ProDialog from "@/components/ProDialog/index.vue";
import { isEmpty, cloneDeep } from "lodash";
import {
  operatorTradeOrderPageApi,
  operatorTradeOrderDetailApi,
  operatorTradeOrderCloseApi,
} from "@/api/operator";
import { Message, MessageBox } from "element-ui";
import {
  getOutboundByPageApi,
  warehouseListApi,
  storageInfoApi,
  addOutboundApi,
  updateOutboundApi,
  getOutboundDetailApi,
  examineOutboundApi,
  deleteOutboundApi,
  outStatusListApi,
  inOutTypeListApi,
  inOutInfoApi,
  checkOneApi,
  batchList,
  updateOutStorageRemark,
} from "@/api/store";
import { detailInfoApi, pcListPageApi } from "@/api/repair";

export default {
  name: "Outbound",
  components: { ProTable, ProDrawer, ProForm, ProDialog },
  data() {
    const that = this;
    return {
      showDialogCheck: false, //审核弹窗
      editFormCheck: {},
      columnsCheck: [],
      tableData: [],
      columns: [
        {
          dataIndex: "outWarehouseId",
          title: "出库单编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "shopWaybill",
          title: "关联单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          dataIndex: "warehouseName",
          title: "归属仓库",
          isTable: true,
        },
        {
          dataIndex: "outWarehouseNumber",
          title: "应出库量",
          isTable: true,
        },
        {
          dataIndex: "auidtOutWarehouseNumber",
          title: "已出库量",
          isTable: true,
        },
        // {
        //   dataIndex: "position",
        //   title: "储位",
        //   isTable: true,
        // },
        {
          dataIndex: "warehouseName",
          title: "出库仓库",
          isForm: true,
          valueType: "text",
          formSpan: 24,
          // option: [],
          // optionMth: () => warehouseListApi({ status: 1401 }),
          // optionskey: {
          //   label: "name",
          //   value: "id",
          // },
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入出库仓库",
          //     trigger: "change",
          //   },
          // ],
          // fun: {
          //   change(val) {
          //     that.goodTableData = [];
          //   },
          // },
        },
        {
          dataIndex: "outType",
          title: "出库方式",
          isTable: true,
          clearable: true,
          isSearch: true,
          valueType: "select",
          formSpan: 12,
          option: [],
          formatter: (row) => row.outType?.label,
          optionMth: () => inOutTypeListApi(),
          optionskey: {
            label: "label",
            value: "value",
          },
          filterOption: {
            key: "type",
            value: 2,
          },
          prop: [
            {
              required: true,
              message: "请输入出库方式",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "consignee",
          title: "领料人",
          isTable: true,
          isSearch: true,
          valueType: "input",
          formatter: (row) => (row.consignee ? row.consignee : "/"),
        },
        {
          dataIndex: "outType",
          title: "出库方式",
          isForm: true,
          valueType: "text",
          formSpan: 24,
        },
        {
          dataIndex: "outStatus",
          title: "出库状态",
          isTable: true,
          isSearch: true,
          clearable: true,
          valueType: "select",
          formSpan: 12,
          option: [],
          formatter: (row) => row.outStatus?.label,
          optionMth: () => outStatusListApi(),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请输入出库状态",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "shopWaybill",
          title: "关联单号",
          isForm: true,
          valueType: "text",
          atext: true,
          formSpan: 24,
          fun: {
            click() {
              if (that.editForm.outType === "工程师领料出库") {
                that.recInfo(that.editForm.shopWaybill);
                // that.$router.push(`/receive?id=${that.editForm.shopWaybill}`);
              } else {
                that.getOrderInfo(that.editForm.shopWaybill);
                // that.$router.push(`/orders?id=${that.editForm.shopWaybill}`);
              }
            },
          },
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入关联单号",
          //     trigger: "change",
          //   },
          // ],
        },

        {
          dataIndex: "itemList",
          title: "出库货品信息",
          isForm: true,
          formSlot: "itemList",
          formSpan: 24,
        },
        // {
        //   dataIndex: "goodsDataList",
        //   title: "出库货品信息",
        //   isForm: true,
        //   formSlot: "goodsDataList",
        //   formSpan: 24,
        // },
        // {
        //   dataIndex: "mainWaybill",
        //   title: "关联物流主单号",
        //   isForm: true,
        //   valueType: "input",
        //   formSlot: "mainWaybill",
        //   formSpan: 12,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请输入关联物流主单号",
        //       trigger: "change",
        //     },
        //   ],
        // },
        // {
        //   dataIndex: "secondlyWaybill",
        //   title: "关联物流子单号",
        //   isForm: true,
        //   valueType: "input",
        //   formSlot: "secondlyWaybill",
        //   formSpan: 12,
        //   prop: [
        //     {
        //       required: true,
        //       message: "请输入关联物流子单号",
        //       trigger: "change",
        //     },
        //   ],
        // },
        {
          dataIndex: "remarks",
          title: "备注",
          isForm: true,
          isTable: true,
          valueType: "input",
          inputType: "textarea",
          tableSlot: "remarks",
          width: 350,
          attrs: {
            rows: 6,
          },
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 160,
          tableSlot: "action",
        },
      ],
      queryParam: {},
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      addGoodsQueryParam: {},
      addGoodsLocalPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      addGoodsColumns: [
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
        },
        {
          dataIndex: "inWarehouseTime",
          title: "入库时间",
          isTable: true,
        },
        {
          dataIndex: "remWarehouseNumber",
          title: "库存量",
          isTable: true,
        },
      ],
      addGoodsTableData: [],
      showDrawer: false,
      drawerTitle: "",
      editType: "add",
      editForm: {},
      editFormLoading: false,
      goodTableData: [],
      goodColumns: [
        {
          dataIndex: "name",
          title: "库品名称",
          isTable: true,
        },
        {
          dataIndex: "batchCode",
          title: "批次号",
          isTable: true,
        },
        {
          dataIndex: "code",
          isTable: true,
          title: "物品编号",
        },
        {
          dataIndex: "numberOem",
          isTable: true,
          title: "OEM编号",
          formatter: (row) => row.storageArticleA?.numberOem,
        },
        {
          dataIndex: "manufacturerChannel",
          isTable: true,
          title: "制造渠道",
          formatter: (row) => row.storageArticleA?.manufacturerChannel?.label,
        },
        {
          dataIndex: "inWarehouseTime",
          title: "入库时间",
          isTable: true,
        },
        {
          dataIndex: "remWarehouseNumber",
          title: "库存量",
          isTable: true,
        },
        // {
        //   dataIndex: "position",
        //   title: "储位",
        //   isTable: true,
        // },
        {
          dataIndex: "outWarehouseNumber",
          title: "出库量",
          isTable: true,
          width: 180,
          tableSlot: "outWarehouseNumber",
        },
        {
          dataIndex: "action",
          title: "操作",
          width: 180,
          isTable: true,
          tooltip: false,
          tableSlot: "action",
        },
      ],
      chooseSelection: [],
      showDialog: false,
      dialogLoading: false,
      showCheckBtn: false,
      orderColumns: [
        {
          dataIndex: "inventoryName",
          title: "物品名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "img",
          title: "物品图片",
          isTable: true,
          tableSlot: "img",
          width: 130,
        },
        {
          dataIndex: "code",
          isTable: true,
          title: "物品编号",
          formatter: (row) => row.storageArticleA?.code,
          minWidth: 140,
        },
        {
          dataIndex: "numberOem",
          isTable: true,
          title: "OEM编号",
          formatter: (row) => row.storageArticleA?.numberOem,
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerChannel",
          isTable: true,
          title: "制造渠道",
          formatter: (row) => row.storageArticleA?.manufacturerChannel.label,
          minWidth: 100,
        },
        {
          dataIndex: "manufacturerGoodsName",
          title: "制造商物品名称",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "manufacturerGoodsCode",
          title: "制造商物品编号",
          isTable: true,
          minWidth: 120,
        },

        {
          dataIndex: "location",
          isTable: true,
          title: "储位",
          minWidth: 100,
        },
        {
          dataIndex: "outStatus",
          title: "出库状态",
          isTable: true,
          formatter: (row) => row.outStatus?.label,
          minWidth: 80,
        },
        {
          dataIndex: "outWarehouseNumber",
          title: "应出库量",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "auidtOutWarehouseNumber",
          title: "已出库量",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "cancelOutWarehouseNumber",
          title: "取消出库数量",
          isTable: true,
          minWidth: 80,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          fixed: "right",
          width: 150,
          tableSlot: "action",
        },
      ],
      orderTableData: [],
      // 图片预览
      showPreview: false,
      // 图片地址
      previewImgUrl: "",
      rkdialogLook: false,
      rkLook: null,
      recdialogVisible: false,
      recform: {},
      recformcolumns: [
        {
          isForm: true,
          dataIndex: "code",
          title: "领料单编号",
          valueType: "text",
          formSpan: 8,
        },

        {
          dataIndex: "engineerName",
          title: "工程师姓名",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "engineerMobile",
          title: "工程师手机号",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "createdAt",
          title: "发起时间",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "status",
          title: "领料状态",
          isForm: true,
          valueType: "text",
          formSlot: "status",
          formSpan: 8,
        },

        {
          dataIndex: "location",
          title: "领料明细",
          formSlot: "location",
          isForm: true,
          formSpan: 24,
        },
      ],
      columns1: [
        {
          dataIndex: "itemName",
          title: "商品名称",
          isTable: true,
          // formatter: (row) => row.itemStore.oemNumber,
        },
        {
          dataIndex: "saleAttrVals",
          title: "sku规格",
          isTable: true,
          tableSlot: "saleAttrVals",
        },
        {
          dataIndex: "num",
          title: "领料数量",
          isTable: true,
        },
      ],
      orderdialogVisible: false,
      orderInfo: null,
      spxxColumns: [
        {
          dataIndex: "itemName",
          isTable: true,
          title: "商品名称",
        },
        {
          dataIndex: "itemId",
          isTable: true,
          title: "商品编号",
        },
        {
          dataIndex: "code",
          isTable: true,
          title: "物品编号",
          formatter: (row) => row.storageArticle?.code,
        },
        {
          dataIndex: "numberOem",
          isTable: true,
          title: "OEM编号",
          formatter: (row) => row.storageArticle?.numberOem,
        },
        {
          dataIndex: "manufacturerChannel",
          isTable: true,
          title: "制造渠道",
          formatter: (row) => row.storageArticle?.manufacturerChannel.label,
        },
        {
          dataIndex: "saleUnitPrice",
          isTable: true,
          title: "商品单价（元）",
        },
        {
          dataIndex: "actualUnitPrice",
          isTable: true,
          title: "成交价（元）",
        },
        {
          dataIndex: "itemNum",
          isTable: true,
          title: "购买数量",
        },
        {
          dataIndex: "discountAmount",
          isTable: true,
          title: "会员减免（元）",
          formatter: (row) => "-" + row.discountAmount,
        },
        {
          dataIndex: "payAmount",
          isTable: true,
          title: "小计（元）",
        },
      ],
      remarkDialog: false,
      remarkForm: {},
      remarkFormLoading: false,
      remarkColumns: [
        {
          dataIndex: "remarks",
          title: "备注",
          isForm: true,
          valueType: "input",
          inputType: "textarea",
          attrs: {
            rows: 6,
          },
        },
      ],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    async loadData(params) {
      try {
        const result = await getOutboundByPageApi(params);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    async handleEdit(id, type, warehouseName) {
      this.editType = type;
      this.drawerTitle =
        type === "add"
          ? "新增出库单"
          : type === "info"
          ? "出库单详情"
          : "编辑出库单";
      if (type === "check") {
        this.editType = "info";
        this.drawerTitle = "审核出库单";
        this.showCheckBtn = true;
      } else {
        this.showCheckBtn = false;
      }
      if (this.editType === "add") {
        this.editForm = {};
        this.goodTableData = [];
      } else {
        const result = await getOutboundDetailApi(id);
        if (result.code === 200 && result.data) {
          result.data.itemList.map((ele) => {
            ele.storageArticleA = cloneDeep(ele.storageArticle);
            // setTimeout(() => {
            delete ele.storageArticle;
            // }, 300);
          });
          this.$nextTick(() => {
            this.editForm = result.data;
            this.editForm.outType = this.editForm.outType.label;
            this.goodTableData = result.data.goodsDataList;
            this.orderTableData = result.data.itemList;
            this.editForm.warehouseName = warehouseName;
          });
        }
      }
      this.showDrawer = true;
    },
    handleDelete(row) {
      MessageBox.confirm("删除出库单。确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const result = await deleteOutboundApi(row.id);
          if (result.code === 200) {
            Message.success("删除成功");
            this.$nextTick(() => {
              this.refresh();
            });
          }
        })
        .catch(() => {
          console.log("取消删除");
        });
    },
    handleCloseDrawer() {
      this.showDrawer = false;
    },
    handleDrawerOk() {
      this.$refs.editFrom.handleSubmit();
    },
    async editFormSubmit(val) {
      try {
        const editApi =
          this.editType === "add" ? addOutboundApi : updateOutboundApi;
        let goodsDataList = this.goodTableData.filter(
          (item) => item.outWarehouseNumber !== 0
        );
        if (goodsDataList.length === 0) throw new Error("请添加商品");
        goodsDataList = [
          ...goodsDataList.map((item) => {
            delete item.id;
            return item;
          }),
        ];
        const result = await editApi({ ...val, goodsDataList });
        if (result.code === 200) {
          this.showDrawer = false;
          Message.success("操作成功");
          this.$nextTick(() => {
            this.refresh();
          });
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    async handleOpenGoodDialog() {
      if (!this.editForm.warehouseId) {
        Message.error("请先选择出库仓库");
        return;
      }

      this.showDialog = true;
      this.$nextTick(() => {
        this.refreshAddGoodsTable();

        //   this.$refs.AddGoodsTable.$refs.ProElTable.clearSelection();
        // this.goodTableData.map((row) => {
        //   this.$refs.AddGoodsTable.$refs.ProElTable.toggleRowSelection(row);
        // })
      });
    },
    handleDialogConfirm() {
      this.goodTableData.push(...this.chooseSelection);
      this.handleDialogCancel();
      Message.success("添加成功");
    },
    handleDialogCancel() {
      this.showDialog = false;
    },
    async loadGoodsData(params) {
      try {
        const result = await storageInfoApi({
          ...params,
          ...this.addGoodsQueryParam,
        });
        if (result.code === 200 && result.data) {
          // this.addGoodsTableData = result.data.inWarehouseList
          const article = result.data.article;
          const data = result.data.inWarehouseList.filter((item) => {
            item.storageArticleA = {
              manufacturerChannel: "",
              numberOem: "",
            };
            item.storageArticleA["manufacturerChannel"] =
              article.manufacturerChannel;
            item.storageArticleA["numberOem"] = article.numberOem;
            return this.goodTableData.findIndex((i) => i.id === item.id) === -1;
          });
          this.addGoodsTableData = data;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.AddGoodsTable &&
          (this.$refs.AddGoodsTable.listLoading = false);
      }
    },
    refreshAddGoodsTable() {
      this.$refs.AddGoodsTable.refresh();
    },
    async handleChooseGoods(row) {
      try {
        this.dialogLoading = true;
        const { id } = row;
        if (!id) throw new Error({ message: "请选择商品" });
        const result = await storageInfoApi({ id });
        if (result.code === 200 && result.data) {
          const data = result.data.inWarehouseList;
          // 去重
          const newData = data.filter(
            (item) => !this.goodTableData.find((good) => good.id === item.id)
          );
          this.goodTableData.push(
            ...newData.map((item) => ({ ...item, outWarehouseNumber: 0 }))
          );
          this.handleDialogCancel();
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.dialogLoading = false;
      }
    },
    handleDeleteGoods(row) {
      this.goodTableData.splice(
        this.goodTableData.findIndex((item) => item.id === row.id),
        1
      );
    },
    async handleCheckPass(outStatus) {
      try {
        let goodsDataList = this.goodTableData.filter(
          (item) => item.outWarehouseNumber !== 0
        );
        if (goodsDataList.length === 0) throw new Error("请添加商品");
        goodsDataList = goodsDataList.map((item) => {
          item.batchId = item.id;
          delete item.id;
          return item;
        });
        const args = {
          ...this.editForm,
          // outStatus,
          goodsDataList,
        };
        const result = await examineOutboundApi(args);
        if (result.code === 200) {
          this.showDrawer = false;
          Message.success("操作成功");
          this.$nextTick(() => {
            this.refresh();
          });
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    handleChooseCheck(data) {
      this.editFormCheck = {};
      this.$set(this.editFormCheck, "id", data.id);
      this.$set(this.editFormCheck, "outWarehouseId", data.outWarehouseId);
      this.$set(this.editFormCheck, "inventoryCode", data.inventoryCode);
      this.$set(this.editFormCheck, "inventoryName", data.inventoryName);
      this.$set(
        this.editFormCheck,
        "outWarehouseNumber",
        data.outWarehouseNumber
      );
      this.showDialogCheck = true;
      let batchLists = [];
      batchList({
        code: data.inventoryCode,
        warehouseId: this.editForm.warehouseId,
        batchCode: data.batchCode,
      }).then((res) => {
        batchLists = res.data;
      });
      const that = this;
      this.columnsCheck = [
        {
          dataIndex: "batchCode",
          title: "批次号",
          isForm: true,
          valueType: "select",
          clearable: true,
          formSpan: 24,
          option: [],
          optionMth: () =>
            batchList({
              code: data.inventoryCode,
              warehouseId: this.editForm.warehouseId,
              batchCode: data.batchCode,
            }),
          optionskey: {
            label: "batchCode",
            value: "id",
          },
          fun: {
            change(val) {
              batchLists.map((ele) => {
                if (val === ele.id) {
                  that.$set(
                    that.editFormCheck,
                    "auidtOutWarehouseNumber",
                    ele.auidtOutWarehouseNumber
                  );
                  that.$set(that.editFormCheck, "batchId", ele.id);
                  that.$set(that.editFormCheck, "batchCode", ele.batchCode);
                  that.$set(
                    that.editFormCheck,
                    "remWarehouseNumber",
                    ele.remWarehouseNumber || "0"
                  );
                  that.$set(that.editFormCheck, "warehouseId", ele.warehouseId);
                }
              });
              return;
            },
          },
          prop: [
            {
              required: true,
              message: "请选择批次号",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "remWarehouseNumber",
          isForm: true,
          title: "库存量",
          valueType: "text",
          clearable: true,
          formSpan: 24,
        },

        {
          dataIndex: "auidtOutWarehouseNumber",
          isForm: true,
          title: "出库数量",
          valueType: "input-number",
          min: 1,
          step: 1,
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入出库数量",
              trigger: "change",
            },
          ],
        },
      ];
    },
    handleDialogCheckConfirm() {
      this.$refs.editFormCheck.handleSubmit();
    },
    editFormCheckSubmit() {
      if (
        this.editFormCheck.auidtOutWarehouseNumber >
        this.editFormCheck.remWarehouseNumber
      ) {
        Message.warning("出库数量 不能大于 库存量!");
        return;
      }
      checkOneApi(this.editFormCheck).then((res) => {
        Message.success("审核成功");
        this.showDialogCheck = false;
        this.handleEdit(this.editForm.id, "check", this.editForm.warehouseName);
        this.$nextTick(() => {
          this.refresh();
        });
      });
    },
    handleRkLook(row) {
      inOutInfoApi(row.id).then((res) => {
        this.rkdialogLook = true;
        this.rkLook = res.data;
      });
    },
    handleChooseBatch(row) {
      this.addGoodsQueryParam["id"] = row.inventoryId;
      this.chooseSelection = [];
      this.loadGoodsData();
      this.showDialog = true;
    },
    handleChooseBatchSelection(val) {
      this.chooseSelection = val;
    },
    // 处理图片预览
    handlePreview(url) {
      if (!url) return;
      this.showPreview = true;
      this.previewImgUrl = url;
    },
    // 触发详情
    recInfo(code) {
      pcListPageApi({ code: code, pageNumber: 1, pageSize: 1 }).then((res) => {
        const result = res.data.rows[0];
        const id = result.id;
        const engineerName = result.engineerName;
        const engineerMobile = result.engineerMobile;
        detailInfoApi(id).then((res1) => {
          this.recform = cloneDeep(res1.data);
          this.recform.engineerName = engineerName;
          this.recform.engineerMobile = engineerMobile;
          this.recdialogVisible = true;
        });
      });
    },
    getOrderInfo(code) {
      operatorTradeOrderDetailApi(code).then((res) => {
        this.orderInfo = res.data;
        this.orderdialogVisible = true;
      });
    },
    /**
     * 获取订单状态
     */
    getOrderStatusChinese(orderStatus) {
      let value = "";
      switch (orderStatus) {
        case "CLOSED":
          value = "订单关闭";
          this.active = null;
          break;
        case "PAID":
          value = "已支付";
          this.active = 2;
          break;
        case "SUCCESS":
          value = "交易成功";
          this.active = 4;
          break;
        case "WAIT_DELIVER":
          value = "待发货";
          this.active = 2;
          break;
        case "WAIT_PAY":
          value = "待支付";
          this.active = 1;
          break;
        case "WAIT_RECEIVE":
          value = "待收货";
          this.active = 3;
          break;
      }
      return value;
    },
    handleCloseRemarkDialog() {
      this.remarkDialog = false;
      this.remarkForm = {};
    },
    async updateRemark() {
      try {
        this.remarkFormLoading = true;
        const result = await updateOutStorageRemark(this.remarkForm);
        if (result.code === 200) {
          this.$message.success("修改成功");
          this.handleCloseRemarkDialog();

          this.$refs.ProTable.refresh();
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.remarkFormLoading = false;
      }
    },
    handleUpdateRemark(row, type) {
      const { id, remarks } = row;
      this.remarkForm = { id, remarks };
      this.remarkDialog = true;
      this.editType = type;
    },
  },
};
</script>

<style lang="scss" scoped>
.preview-img {
  display: block;
  margin: 0 auto;
}
.text-p {
  &.right {
    position: relative;
    left: 85%;
    top: 0;
  }

  color: #606266;

  .p-label {
    color: #606266;
    font-weight: 700;
  }

  margin-top: 15px;
}

.content-fixed {
  display: flex;
  justify-content: space-between;

  .text-p {
    flex: 1;
    display: flex;
  }
}
.exceed {
  display: inline-block;
  width: 92%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
