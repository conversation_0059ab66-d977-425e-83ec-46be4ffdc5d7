/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-04 14:23:08
 * @Description: 
 */
@for $i from 0 through 50 {
  .m-#{4*$i} {
    margin: (4px * $i);
  }

  .m-l-#{4*$i} {
    margin-left: (4px * $i) !important;
  }

  .m-r-#{4*$i} {
    margin-right: (4px * $i) !important;
  }

  .m-t-#{4*$i} {
    margin-top: (4px * $i) !important;
  }

  .m-b-#{4*$i} {
    margin-bottom: (4px * $i) !important;
  }

  .m-v-#{4*$i} {
    margin-top: (4px * $i);
    margin-bottom: (4px * $i) !important;
  }

  .m-h-#{4*$i} {
    margin-left: (4px * $i);
    margin-right: (4px * $i) !important;
  }

  .p-#{4*$i} {
    padding: (4px * $i) !important;
  }

  .p-l-#{4*$i} {
    padding-left: (4px * $i) !important;
  }

  .p-r-#{4*$i} {
    padding-right: (4px * $i) !important;
  }

  .p-t-#{4*$i} {
    padding-top: (4px * $i) !important;
  }

  .p-b-#{4*$i} {
    padding-bottom: (4px * $i) !important;
  }

  .p-v-#{4*$i} {
    padding-top: (4px * $i);
    padding-bottom: (4px * $i) !important;
  }

  .p-h-#{4*$i} {
    padding-left: (4px * $i);
    padding-top: (4px * $i) !important;
  }
}

@for $i from 0 through 40 {
  .f#{$i} {
    font-size: ($i + px);
  }
}

@for $i from 0 through 100 {
  .w-#{$i} {
    width: ($i + px);
  }

  .w-b-#{$i} {
    width: ($i + "%");
  }

  .h-#{$i} {
    height: ($i + px);
  }

  .h-b-#{$i} {
    height: ($i + "%");
  }
}

// ========
::-webkit-scrollbar {
  width: 8px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.06);
}

::-webkit-scrollbar-thumb {
  border-radius: 1px;
  background: rgba(0, 0, 0, 0.12);
  overflow: hidden;
}

body {
  font-size: 14px;
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
  overflow: hidden;
}

p {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  // padding: 20px;
}

.view {
  overflow: hidden;
  overflow-y: scroll;
  pointer-events: auto;
  z-index: 2;
  position: absolute;
  left: 10px;
  top: 10px;
  bottom: $top-header-height;
  right: 10px;
  background: #edf4ff;
}
.view::-webkit-scrollbar {
  display: none
}
.icon-hover {
  cursor: pointer;

  &:hover {
    background-color: $base-hover-color;
  }
}

.i-icon:focus-visible {
  border: none !important;
  outline: none !important;
}

html,
body {
  @include base-scrollbar;
  position: relative;
  box-sizing: border-box;
  height: 100vh;
  padding: 0 !important;
  margin: 0;
  overflow-x: hidden;
}

div {
  @include base-scrollbar;
  box-sizing: border-box;
}

.relative-wrap {
  position: relative;

  .absolute-wrap {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  i.absolute-wrap {
    padding: 0 6px;
    cursor: pointer;
    font-size: 22px;
  }
}

.content-screenfull {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 20px !important;
  background-color: white;
  z-index: 1005;
  overflow-y: auto;
}

.ProDialogFullscreen {
  width: 16px;
  height: 16px;
  display: block;
  position: absolute;
  top: 18px;
  right: 42px;
  font-size: 14px;
  color: #909399;
  cursor: pointer;
}

// pro table btn
.table-operator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  .solt{
    flex: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .table-setting {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 22px;

    i {
      padding: 0 6px;
      cursor: pointer;
    }

    i:last-child {
      padding-right: 0;
    }

    i:hover {
      color: #409eff;
    }
  }
}

// transition draggable
.flip-list-move {
  transition: transform 0.5s;
}

.no-move {
  transition: transform 0s;
}

.pro-form-footer-bar {
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 99;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  padding: 6px 24px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -6px 16px -8px rgb(0 0 0 / 8%), 0 -9px 28px 0 rgb(0 0 0 / 5%),
    0 -12px 48px 16px rgb(0 0 0 / 3%);
}

// .el-pagination {
//     &.is-background {
//         .el-pager {
//             li {
//                 background-color: rgba(17, 45, 84, 0.5019607843);
//                 color: #ffff;
//                 margin: 0 3px;
//             }
//         }

//         .btn-prev,
//         .btn-next {
//             background-color: rgba(17, 45, 84, 0.5019607843);
//             color: #ffff;
//         }
//     }
// }

.success-button {
  display: inline-block;
  cursor: pointer;
  padding: 8px 24px;
  background-image: url("@/assets/images/lng/b-but.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: bold;
  color: #fefefe;
}

.cancel-button {
  display: inline-block;
  cursor: pointer;
  padding: 8px 24px;
  background-image: url("@/assets/images/lng/g-but.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: bold;
  color: #fefefe;
}

.warning-button {
  display: inline-block;
  cursor: pointer;
  padding: 5px 16px;
  background-image: url("@/assets/images/lng/o-but.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: bold;
  color: #fefefe;
}

.app-container {
  background: #fff;
}

.dialog-footer1 {
  width: 100%;
  position: absolute;
  bottom: 0;
  padding: 10px 0 20px 0;
  text-align: center;
  background: #fff;
  z-index: 10001;
}

// .el-input-number__increase,
// .el-input-number__decrease {
//   display: none;
// }
// 解决点击radio时控制台报错问题
input[aria-hidden='true'] {
  display: none !important;
}
.el-radio:focus:not(.is-focus):not(:active):not(.i-disabled) .el-radio__inner {
  box-shadow: none !important;
}
.el-tooltip__popper {
  width: 400px;
}
.is-dark {
  background: #fafafa !important;
  color: #333 !important;
  border: 1px solid #333 !important;
}
.info-atext {
  width: 100%;
  color: #457dec;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  text-decoration: underline;
}

.numerical-range-container {
  display: flex;
  box-sizing: border-box;
  cursor: pointer;
  overflow: hidden;
  .input-range {
    width: 100%;
    transition: border-color 0.3s ease;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    position: relative;
    display: flex;
    align-items: center;

    .content {
      flex: 1;
      display: flex;
      align-items: center;

      .el-input {
        flex: 1;

        &:first-child .el-input__inner {
          padding-right: 0;
        }

        &:last-child .el-input__inner {
          padding-left: 0;
        }

        .el-input__inner {
          border: none;
          text-align: center;
        }
      }

      span {
        padding: 0 5px;
      }
      .disBgc{
        background-color: #F5F7FA;
      }
    }

    .close-icon {
      width: 25px;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-icon-circle-close {
        color: #dcdfe6;
        display: none;
      }
    }

    &:hover {
      .close {
        display: block;
      }
    }
  }

  .focused {
    border: 1px solid #409eff !important;
  }
}


.title-box-right{
  display: flex;
  flex: 1;
  justify-content: flex-end;
  align-items: center;
  margin-right: 20px;
  gap: 30px;
  font-size: 16px;
  color: #6488cf;
  flex-wrap: wrap;
  div {
    text-wrap: nowrap;
  }
}
.integral-form{
  .el-col {
    text-align: left !important;
  }
}
.title-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
/* 重置引导组件样式 */
.introjs-helperLayer {
  box-sizing: border-box !important;
  box-shadow: rgba(33, 33, 33, 0.8) 0 0  0,
  rgba(33, 33, 33, 0.5) 0 0 0 5000px !important;
  //border: 3px dashed #409eff;
}

/* 重置引导组件样式 */
.intro-tooltip {
  color: #ffff;
  background: #2c3e50;
  .introjs-right,
  .introjs-left {
    top: 30%;
  }
  .intro-highlight {
    background: rgba(255, 255, 255, 0.5);
  }
  .introjs-arrow.left {
    border-right-color: #2c3e50;
  }
  .introjs-arrow.top {
    border-bottom-color: #2c3e50;
  }
  .introjs-arrow.right {
    border-left-color: #2c3e50;
  }
  .introjs-arrow.bottom {
    border-top-color: #2c3e50;
  }
  /* 提示框头部区域 */
  .introjs-tooltip-header {
    padding-right: 0 !important;
    padding-top: 0 !important;
    .introjs-tooltip-title {
      font-size: 16px;
      width: 80%;
      padding-top: 10px;
      margin-top: 10px !important;
    }
    .introjs-skipbutton {
      color: #409eff !important;
      font-size: 14px !important;
      font-weight: normal !important;
      //   padding: 8px 10px !important ;
    }
  }
  /* 提示框内容区域 */
  .introjs-tooltiptext {
    font-size: 14px !important;
    padding: 15px !important;
  }
  /* 提示框底部按钮 */
  .introjs-tooltipbuttons {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none !important;
    .introjs-button {
      width: 50px !important;
      text-align: center;
      padding: 4px !important;
      font-size: 12px !important;
      font-weight: 500 !important;
      border-radius: 3px !important;
      border: none !important;
    }
    .introjs-button:last-child {
      margin-left: 10px;
    }
    .introjs-prevbutton {
      color: #606266 !important;
      background: #fff !important;
      border: 1px solid #dcdfe6 !important;
    }
    .introjs-nextbutton {
      color: #fff !important;
      background-color: #409eff !important;
      border-color: #409eff !important;
    }
    .introjs-disabled {
      color: #9e9e9e !important;
      border-color: #bdbdbd !important;
      background-color: #f4f4f4 !important;
    }
  }

}

/* 引导提示框的位置 */
.introjs-bottom-left-aligned {
  left: 45% !important;
}

// 规则提示样式
.rules-tips {
  $primary-color: #409eff;
  $warning-color: #e6a23c;
  $text-color: #303133;
  $subtext-color: #606266;
  $bg-light: #f8f9fa;
  $highlight-bg: #eef0f4;

  padding: 10px;
  background-color: $bg-light;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

  .rule-title {
    margin: 0 0 16px 0;
    color: $text-color;
    font-size: 16px;
    font-weight: 600;
  }

  .rule-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .rule-item {
    position: relative;
    text-indent: 1em;
  }

  .rule-content {
    display: flex;
    gap: 8px;
    align-items: flex-start;
  }

  .rule-number {
    font-weight: bold;
    color: $text-color;
  }

  .rule-text {
    margin: 0;
    line-height: 1.6;
    color: $subtext-color;
  }

  .highlight {
    color: $subtext-color;
    font-weight: 500;
    background-color: $highlight-bg;
    padding: 2px 6px;
    border-radius: 4px;
  }

  .action {
    color: $primary-color;
    font-weight: 500;
  }

  .warning {
    color: $warning-color;
    font-weight: 500;
    position: relative;

    //&::after {
    //  content: '!';
    //  position: absolute;
    //  top: -4px;
    //  right: -8px;
    //  font-size: 12px;
    //}
  }
}