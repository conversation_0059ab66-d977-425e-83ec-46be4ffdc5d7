<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-09-19 18:44:26
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-10-11 13:59:29
 * @FilePath: \benyin-web\src\views\procure\procureStat.vue
 * @Description: 采购 - 采购统计
 * 
-->

<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane lazy label="按月" name="first">
        <ProcureItemStat type="month" :columns="monthColumns" />
      </el-tab-pane>
      <el-tab-pane lazy label="按机型" name="second">
        <ProcureItemStat type="model" :columns="modelColumns" />
      </el-tab-pane>
      <el-tab-pane lazy label="按物品编号" name="third">
        <ProcureItemStat type="item" :columns="itemColumns" />
      </el-tab-pane>
      <el-tab-pane lazy label="按供应商" name="fourth">
        <ProcureItemStat type="provider" :columns="providerColumns" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ProcureItemStat from "./cpns/procureItemStat.vue";
import { dictTreeByCodeApi } from "@/api/user";
export default {
  name: "ProcureStat",
  components: { ProcureItemStat },
  data() {
    return {
      activeName: "first",
      monthColumns: [
        {
          title: "采购月份",
          dataIndex: "yearMonth",
          isTable: true,
          width: 150,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
          attrs: { "value-format": "yyyy-MM" },
        },
        {
          title: "采购数量",
          dataIndex: "number",
          isTable: true,
        },
        {
          title: "退货数量",
          dataIndex: "refundNum",
          isTable: true,
        },
        {
          title: "实收数量",
          dataIndex: "receiveNum",
          isTable: true,
        },
        {
          title: "采购金额",
          dataIndex: "amount",
          isTable: true,
        },
        {
          title: "退货金额",
          dataIndex: "refundAmount",
          isTable: true,
        },
        // {
        //   title: "实付金额",
        //   dataIndex: "payAmount",
        //   isTable: true,
        // },
      ],
      modelColumns: [
        {
          dataIndex: "series",
          title: "机型",
          isTable: true,
          // tableSlot: "machine",
          width: 140,
        },
        {
          dataIndex: "productIds",
          title: "适用机型",
          isSearch: true,
          clearable: true,
          valueType: "product",
          // searchSlot: "fullIdPath",
        },
        {
          title: "采购时间",
          dataIndex: "month",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
        {
          title: "采购数量",
          dataIndex: "number",
          isTable: true,
        },
        {
          title: "退货数量",
          dataIndex: "refundNum",
          isTable: true,
        },
        {
          title: "实收数量",
          dataIndex: "receiveNum",
          isTable: true,
        },
        {
          title: "采购金额",
          dataIndex: "amount",
          isTable: true,
        },
        {
          title: "退货金额",
          dataIndex: "refundAmount",
          isTable: true,
        },
        // {
        //   title: "实付金额",
        //   dataIndex: "payAmount",
        //   isTable: true,
        // },
      ],
      itemColumns: [
        {
          title: "物品编号",
          dataIndex: "articleCode",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 180,
        },
        {
          title: "物品名称",
          dataIndex: "articleName",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          title: "OEM编号",
          dataIndex: "oemNumber",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 150,
        },
        {
          dataIndex: "machine",
          title: "适用机型",
          isTable: true,
          tableSlot: "machine",
          width: 140,
        },
        {
          dataIndex: "type",
          title: "物品大小类",
          isTable: true,
          formatter: (row) => row.type?.label,
          isSearch: true,
          clearable: true,
          searchSlot: "goodsType",
        },
        {
          dataIndex: "manufacturerChannel",
          title: "制造渠道",
          isTable: true,
          isSearch: true,
          formatter: (row) => row.manufacturerChannel?.label,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(2200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "unit",
          title: "单位",
          isTable: true,
        },
        {
          dataIndex: "price",
          title: "单价",
          isTable: true,
        },
        {
          title: "采购时间",
          dataIndex: "month",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
        {
          title: "采购数量",
          dataIndex: "number",
          isTable: true,
        },
        {
          title: "退货数量",
          dataIndex: "refundNum",
          isTable: true,
        },
        {
          title: "实收数量",
          dataIndex: "receiveNum",
          isTable: true,
        },
        {
          title: "采购金额",
          dataIndex: "sumPrice",
          isTable: true,
        },
        {
          title: "退货金额",
          dataIndex: "refundAmount",
          isTable: true,
        },
        // {
        //   title: "实付金额",
        //   dataIndex: "payAmount",
        //   isTable: true,
        // },
      ],
      providerColumns: [
        {
          title: "供应商名称",
          dataIndex: "manufacturerName",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 200,
        },
        {
          title: "采购时间",
          dataIndex: "month",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
        {
          title: "采购数量",
          dataIndex: "number",
          isTable: true,
        },
        {
          title: "退货数量",
          dataIndex: "refundNum",
          isTable: true,
        },
        {
          title: "实收数量",
          dataIndex: "receiveNum",
          isTable: true,
        },
        {
          title: "采购金额",
          dataIndex: "amount",
          isTable: true,
        },
        {
          title: "退货金额",
          dataIndex: "refundAmount",
          isTable: true,
        },
        // {
        //   title: "实付金额",
        //   dataIndex: "payAmount",
        //   isTable: true,
        // },
      ],
    };
  },
};
</script>

<style scoped lang="scss"></style>
