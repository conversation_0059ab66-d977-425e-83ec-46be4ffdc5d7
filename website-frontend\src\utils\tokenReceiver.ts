/**
 * Token接收器工具
 * 用于从父窗口接收认证Token
 */

// 存储Token的键名
const TOKEN_KEY = 'auth_token';

/**
 * 从localStorage获取Token
 */
export function getToken(): string | null {
  return localStorage.getItem(TOKEN_KEY);
}

/**
 * 保存Token到localStorage
 */
export function saveToken(token: string): void {
  localStorage.setItem(TOKEN_KEY, token);
}

/**
 * 清除Token
 */
export function clearToken(): void {
  localStorage.removeItem(TOKEN_KEY);
}

/**
 * 检查Token是否存在
 */
export function hasToken(): boolean {
  return !!getToken();
}

/**
 * 初始化Token接收器
 * 从父窗口接收Token
 */
export function initTokenReceiver(): void {
  // 检查是否在iframe中
  const isInIframe = window.self !== window.top;
  
  if (isInIframe) {
    // 监听来自父窗口的消息
    window.addEventListener('message', (event) => {
      // 验证消息来源
      if (event.origin !== window.location.origin) {
        // 可以根据实际情况调整安全检查
        console.warn('Received message from unknown origin:', event.origin);
        return;
      }
      
      // 处理Token消息
      if (event.data && event.data.type === 'AUTH_TOKEN') {
        const { token } = event.data;
        if (token) {
          saveToken(token);
          console.log('Token received and saved');
        }
      }
    });
    
    // 通知父窗口已准备好接收Token
    window.parent.postMessage({ type: 'TOKEN_RECEIVER_READY' }, '*');
  }
}

/**
 * 发送Token到子窗口
 */
export function sendTokenToChild(iframe: HTMLIFrameElement): void {
  const token = getToken();
  
  if (!token) {
    console.warn('No token available to send to child window');
    return;
  }
  
  // 监听子窗口准备就绪的消息
  window.addEventListener('message', (event) => {
    // 验证消息来源
    if (event.source !== iframe.contentWindow) {
      return;
    }
    
    // 当子窗口准备好接收Token时发送
    if (event.data && event.data.type === 'TOKEN_RECEIVER_READY') {
      iframe.contentWindow?.postMessage(
        { type: 'AUTH_TOKEN', token },
        '*'
      );
    }
  });
}

/**
 * 检查Token是否有效
 * 这里只是简单检查格式，实际应用中可能需要更复杂的验证
 */
export function isTokenValid(token: string): boolean {
  // 简单检查Token是否是JWT格式
  const jwtPattern = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
  return jwtPattern.test(token);
}

/**
 * 从Token中解析用户信息
 */
export function parseUserFromToken(token: string): any {
  try {
    // JWT Token的payload部分是Base64编码的JSON
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );

    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Failed to parse user from token:', error);
    return null;
  }
}

/**
 * Token接收器 - 兼容性导出
 */
export const tokenReceiver = {
  init: initTokenReceiver,
  getToken,
  getStoredToken: getToken, // 兼容性别名
  saveToken,
  clearToken,
  hasToken,
  isValid: isTokenValid,
  parseUser: parseUserFromToken,
  getTokenStatus: () => ({ valid: hasToken(), expired: false }), // 兼容性方法
  isTokenRefreshing: () => false, // 兼容性方法
  refreshToken: async () => { // 兼容性方法
    const token = getToken();
    return token || null;
  }
};
