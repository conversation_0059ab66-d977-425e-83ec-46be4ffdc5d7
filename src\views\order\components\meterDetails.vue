<!--
 * @Author: wskg
 * @Date: 2025-02-18 15:06:32
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 09:55:21
 * @Description: 抄表明细
 -->
<template>
  <div class="container">
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="70%"
      top="1%"
      confirm-text="确认修改"
      :confirm-btn-loading="confirmBtnLoading"
      :no-footer="editType === 'info'"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <el-descriptions :column="3" border>
        <el-descriptions-item label="店铺名称">
          {{ details?.customerName }}
        </el-descriptions-item>
        <el-descriptions-item label="客户编号">
          {{ details?.customerSeq }}
        </el-descriptions-item>
        <el-descriptions-item label="品牌">
          {{ details?.brand }}
        </el-descriptions-item>
        <el-descriptions-item label="机型">
          {{ details?.machine }}
        </el-descriptions-item>
        <el-descriptions-item label="设备组名称">
          {{ details?.deviceGroup?.label }}
        </el-descriptions-item>
        <el-descriptions-item label="计张方式">
          {{ details?.pricePaperType }}
        </el-descriptions-item>
        <el-descriptions-item label="黑白打印单价">
          {{ details?.blackWhitePrice }}
        </el-descriptions-item>
        <el-descriptions-item label="彩色打印单价">
          {{ details?.colorPrice }}
        </el-descriptions-item>
        <el-descriptions-item label="五色打印单价">
          {{ details?.fiveColourPrice }}
        </el-descriptions-item>
        <el-descriptions-item label="期初黑白计数器">
          <template v-if="editType === 'edit'">
            <el-input
              v-model="details.blackWhiteInception"
              type="number"
              placeholder="0"
            />
          </template>
          <template v-else>{{ details?.blackWhiteInception }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="期初彩色计数器">
          <template v-if="editType === 'edit'">
            <el-input
              v-model="details.colorInception"
              type="number"
              placeholder="0"
            />
          </template>
          <template v-else>{{ details?.colorInception }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="期初五色计数器">
          <template v-if="editType === 'edit'">
            <el-input
              v-model="details.fiveColourIncption"
              type="number"
              placeholder="0"
            />
          </template>
          <template v-else>{{ details?.fiveColourIncption }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="期末黑白计数器">
          <template v-if="editType === 'edit'">
            <el-input
              v-model="details.blackWhiteCutoff"
              type="number"
              placeholder="0"
            />
          </template>
          <template v-else>{{ details?.blackWhiteCutoff }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="期末彩色计数器">
          <template v-if="editType === 'edit'">
            <el-input
              v-model="details.colorCutoff"
              type="number"
              placeholder="0"
            />
          </template>
          <template v-else>{{ details?.colorCutoff }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="期末五色计数器">
          <template v-if="editType === 'edit'">
            <el-input
              v-model="details.fiveColourCutoff"
              type="number"
              placeholder="0"
            />
          </template>
          <template v-else>{{ details?.fiveColourCutoff }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="扣减黑白废张数">
          <template v-if="editType === 'edit'">
            <el-input
              v-model="details.blackWhiteExclude"
              type="number"
              placeholder="0"
            />
          </template>
          <template v-else>{{ details?.blackWhiteExclude }}</template>
        </el-descriptions-item>

        <el-descriptions-item label="扣减彩色废张数">
          <template v-if="editType === 'edit'">
            <el-input
              v-model="details.colorExclude"
              type="number"
              placeholder="0"
            />
          </template>
          <template v-else>{{ details?.colorExclude }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="扣减五色废张数">
          <template v-if="editType === 'edit'">
            <el-input
              v-model="details.fiveColourExclude"
              type="number"
              placeholder="0"
            />
          </template>
          <template v-else>{{ details?.fiveColourExclude }}</template>
        </el-descriptions-item>
        <el-descriptions-item label="黑白印量">
          {{ details?.blackWhitePoint }}
        </el-descriptions-item>
        <el-descriptions-item label="彩色印量">
          {{ details?.colorPoint }}
        </el-descriptions-item>
        <el-descriptions-item label="五色印量">
          {{ details?.fiveColourPoint }}
        </el-descriptions-item>
        <el-descriptions-item label="总印量">
          {{ details?.totalPoint }}
        </el-descriptions-item>
        <el-descriptions-item label="抵扣印量">
          {{ details?.deductionPoint }}
        </el-descriptions-item>
        <el-descriptions-item label="抵扣金额">
          {{ details?.deductionAmount }}
        </el-descriptions-item>

        <el-descriptions-item label="起始抄表时间">
          <el-date-picker
            v-if="editType === 'edit'"
            v-model="details.beginTime"
            type="datetime"
            placeholder="选择起始抄表时间"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
          <div v-else>{{ details?.beginTime }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="期末抄表时间">
          <!--{{ details?.endTime }}-->
          <el-date-picker
            v-if="editType === 'edit'"
            v-model="details.endTime"
            type="datetime"
            placeholder="选择期末抄表时间"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
          <div v-else>{{ details?.endTime }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="年月" span="2">
          {{ details?.cycle }}
        </el-descriptions-item>
      </el-descriptions>
      <div class="title-box">计数器照片</div>
      <div class="img-content">
        <ProUpload
          :file-list="details.picUrls"
          :type="editType"
          :limit="5"
          multiple
          @uploadSuccess="handleUploadSuccess"
          @uploadRemove="handleUploadRemove"
        />
      </div>
    </ProDialog>
  </div>
</template>

<script>
import ProUpload from "@/components/ProUpload/index.vue";
import { cloneDeep } from "lodash";
import { meterUpdateApi } from "@/api/statisics";
export default {
  name: "MeterDetails",
  components: { ProUpload },
  data() {
    return {
      details: {},
      dialogVisible: false,
      dialogTitle: "",
      editType: "info",
      confirmBtnLoading: false,
      colorType: "1703",
    };
  },
  mounted() {},
  methods: {
    show(row, type) {
      this.details = {};
      this.editType = type;
      this.dialogTitle = `${row.customerName} - 抄表明细`;
      this.dialogVisible = true;
      this.details = cloneDeep(row);
      this.colorType = row.colorType?.value || "1703";
    },
    async handleOk() {
      try {
        this.confirmBtnLoading = true;
        if (!this.details.beginTime) {
          this.$message.error("请选择起始抄表时间");
          return;
        }
        if (!this.details.endTime) {
          this.$message.error("请选择期末抄表时间");
          return;
        }
        const result = await meterUpdateApi(this.details);
        if (result.code === 200) {
          this.$message.success("修改成功");
          this.dialogVisible = false;
          this.$emit("refresh");
        }
      } finally {
        this.confirmBtnLoading = false;
      }
    },
    handleCancel() {
      this.dialogVisible = false;
    },
    handleUploadSuccess(file) {
      if (!this.details.picUrls) {
        this.$set(this.details, "picUrls", []);
      }
      this.details.picUrls.push(file);
    },
    handleUploadRemove(file) {
      const index = this.details["picUrls"].findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.details["picUrls"].splice(index, 1);
    },
  },
};
</script>

<style scoped lang="scss"></style>
