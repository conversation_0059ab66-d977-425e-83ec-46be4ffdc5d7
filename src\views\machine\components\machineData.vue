<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>机器数量：{{ localPagination.total || 0 }}</div>
        </div>
      </template>
      <template #productId>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          collapse-tags
          @change="handleSelectForm"
        ></el-cascader>
      </template>
      <template #naramFile="{ row }">
        <div class="attachments">
          <el-link
            v-for="item in row.naramFile"
            :key="item.key"
            :href="item.url"
            icon="el-icon-folder-opened
"
            >{{ item.name }}</el-link
          >
        </div>
      </template>
      <template #logFile="{ row }">
        <div class="attachments">
          <el-link
            v-for="item in row.logFile"
            :key="item.key"
            :href="item.url"
            icon="el-icon-folder-opened
"
            >{{ item.name }}</el-link
          >
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { dictTreeByCodeApi, roleMemberApi } from "@/api/user";
import { productAllApi } from "@/api/dispose";
import { machinePageApi } from "@/api/machine";

export default {
  name: "MachineData",
  data() {
    return {
      productIdName: "",
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "deviceSeqId",
          title: "设备组编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 130,
        },
        {
          dataIndex: "productIds",
          title: "品牌/机型",
          isSearch: true,
          valueType: "product",
        },
        {
          dataIndex: "productInfo",
          title: "品牌/机型",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          formatter: (row) => row.deviceGroup?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(700),
          optionskey: {
            label: "label",
            value: "value",
          },
          minWidth: 100,
        },
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "customerSeq",
          title: "客户编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 150,
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "placeOrigin",
          title: "产地版本",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "supplyVoltage",
          title: "供电电压",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "status",
          title: "启用状态",
          isTable: true,
          formatter: (row) => (row.status ? "启用" : "禁用"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "启用",
              value: true,
            },
            {
              label: "禁用",
              value: false,
            },
          ],
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1100),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => row.serType?.label,
        },
        {
          dataIndex: "serTypes",
          title: "服务类型",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "散修",
              value: "SCATTERED",
            },
            {
              label: "购机不保",
              value: "NO_WARRANTY",
            },
            {
              label: "购机质保",
              value: "WARRANTY",
            },
            {
              label: "购机全保",
              value: "BUY_FULL",
            },
            {
              label: "购机半保",
              value: "BUY_HALF",
            },
            {
              label: "租赁全保",
              value: "RENT_FULL",
            },
            {
              label: "租赁半保",
              value: "RENT_HALF",
            },
            {
              label: "普通全保",
              value: "ALL",
            },
            {
              label: "普通半保",
              value: "HALF",
            },
            {
              label: "包量全保",
              value: "PACKAGE_ALL",
            },
            {
              label: "包量半保",
              value: "PACKAGE_HALF",
            },
            {
              label: "融资全保",
              value: "FINANCING_FULL",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "融资半保",
              value: "FINANCING_HALF",
            },
            {
              label: "质保服务",
              value: "QA",
            },
            {
              label: "质保含部件",
              value: "QA_COMPONENT",
            },
            {
              label: "维保服务",
              value: "MAINTENANCE",
            },
            {
              label: "其它",
              value: "OTHER",
            },
          ],
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(900),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "fixStatus",
          title: "维修状态",
          isTable: true,
          formatter: (row) => row.fixStatus?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "regCliState",
          title: "安装客户端",
          isTable: true,
          formatter: (row) => (row.regCliState == "1" ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: "1",
            },
            {
              label: "否",
              value: "0",
            },
          ],
          width: 100,
        },
        {
          dataIndex: "installAt",
          title: "安装时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "enableStatistics",
          title: "是否统计",
          isTable: true,
          formatter: (row) => (row.enableStatistics ? "是" : "否"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        // {
        //   dataIndex: "adjusting",
        //   title: "是否校准",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [
        //     {
        //       label: "是",
        //       value: true,
        //     },
        //     {
        //       label: "否",
        //       value: false,
        //     },
        //   ],
        // },
        {
          dataIndex: "salesman",
          title: "商务人员",
          isTable: true,
          isSearch: true,
          valueType: "input",
          // option: [],
        },
        {
          dataIndex: "operatName",
          title: "技术人员",
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [],
        },
        {
          dataIndex: "dataShowState",
          title: "客户端可见",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: 1,
            },
            {
              label: "否",
              value: 0,
            },
          ],
        },
        {
          dataIndex: "naramFile",
          title: "NARAM数据",
          isTable: true,
          tableSlot: "naramFile",
          width: 200,
        },
        {
          dataIndex: "hasNaramFile",
          title: "NARAM数据",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
        },
        {
          dataIndex: "logFile",
          title: "日志文件数据",
          isTable: true,
          tableSlot: "logFile",
          width: 200,
        },
        {
          dataIndex: "hasLogFile",
          title: "日志文件数据",
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "是",
              value: true,
            },
            {
              label: "否",
              value: false,
            },
          ],
          width: 200,
        },
        {
          dataIndex: "createdAt",
          title: "入驻平台时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "installAt",
          title: "安装时间",
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
        },
      ],
      tableData: [],
      options: [],
      saleList: [],
      workerList: [],
      totalData: {},
    };
  },
  mounted() {
    this.refresh();
    this.init();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const result = [
        {
          startInstallDate: null,
          endInstallDate: null,
          data: parameter.installAt,
        },
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
      ];
      filterParamRange(this, this.queryParam, result);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.installAt;
      delete requestParameters.createdAt;
      machinePageApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
      const fetchRoleMembers = (roleId, targetList, dataIndex) => {
        return roleMemberApi(roleId, { pageNumber: 1, pageSize: 9999 }).then(
          (res) => {
            res.data.rows.map((item) => {
              targetList.push({
                value: item.name,
                label: item.name,
              });
            });
            this.columns.forEach((column) => {
              if (column.dataIndex === dataIndex) {
                column.option = targetList;
              }
            });
          }
        );
      };

      // 获取销售人员列表
      // fetchRoleMembers("1787125965588070402", this.saleList, "salesman");

      // 获取技术支持人员列表
      fetchRoleMembers("1002", this.workerList, "operatName");
    },
    handleSelectForm(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep.attachments {
  width: 100%;
  white-space: wrap !important;
  display: flex;
  flex-direction: column;
  gap: 10px;
  a {
    color: #409eff;
    display: flex;
    align-items: center;
    //justify-content: center;
    .el-icon-folder-opened {
      font-size: 24px !important;
    }
  }
}
</style>
