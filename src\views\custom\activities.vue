<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-29 12:02:17
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 16:58:44
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :reserve-selection="true"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #activityType="slotProps">
        {{ slotProps.row.activityType?.label }}
      </template>
      <template #status="slotProps">
        {{ slotProps.row.status.label }}
      </template>
      <template #createdBy="slotProps">
        {{ slotProps.row.createdBy.name }}
      </template>

      <template #btn>
        <div class="btn_container">
          <div class="btn_left">
            <el-button
              type="success"
              class="add-btn"
              size="mini"
              icon="el-icon-plus"
              @click="handleEdit(null, 'add')"
            >
              新增活动
            </el-button>
          </div>
          <div class="btn_right"></div>
        </div>
      </template>

      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-view"
            @click="handleEdit(row, 'info')"
          >
            查看
          </el-button>
          <el-button
            v-if="row.status.value == 'STASH' || row.status.value == 'REJECT'"
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleEdit(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            v-if="
              row.status.value == 'WAIT_APPROVE' &&
              hasPermits('@ums:activity:examine')
            "
            size="mini"
            type="primary"
            icon="el-icon-finished"
            @click="checkActivities(row)"
          >
            审核
          </el-button>
          <el-button
            v-if="row.status.value == 'IN_PROGRESS'"
            size="mini"
            type="danger"
            icon="el-icon-video-pause"
            @click="pauseActivities(row)"
          >
            暂停
          </el-button>
          <el-button
            v-if="row.status.value == 'STOP'"
            size="mini"
            icon="el-icon-video-play"
            @click="continueActivities(row)"
          >
            活动继续
          </el-button>
          <el-button
            v-if="row.status.value == 'STASH' || row.status.value == 'REJECT'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="deleteActivities(row)"
          >
            删除
          </el-button>
          <el-button
            v-if="
              row.activityType.value == 'SHARE' &&
              (row.status.value == 'IN_PROGRESS' ||
                row.status.value == 'ENDED' ||
                row.status.value == 'STOP')
            "
            size="mini"
            type="primary"
            icon="el-icon-finished"
            @click="statisticActivity(row)"
          >
            活动统计
          </el-button>
          <el-button
            v-if="row.activityType.value == 'SHARE'"
            icon="el-icon-tickets"
            @click="grandRecord(row)"
          >
            奖品详情
          </el-button>
        </div>
      </template>
    </ProTable>
    <editActivities ref="editActivities" @refresh="refresh" />
    <statisticActivities ref="statisticActivities" @refresh="refresh" />
    <GrandRecord ref="grandRecord" />
  </div>
</template>

<script>
import ProTable from "@/components/ProTable/index.vue";
import editActivities from "@/views/custom/components/activities/editActivities.vue";
import statisticActivities from "@/views/custom/components/activities/statisticActivities.vue";
import GrandRecord from "@/views/custom/components/activities/grandRecord.vue";
import {
  getActivitiesApi,
  auditActivitiesApi,
  deleteActivitiesApi,
  stopActivitiesApi,
  continueActivitiesApi,
} from "@/api/customer";
import { Message, MessageBox } from "element-ui";
import { getProvinceListApi, getRegionByCodeApi } from "@/api/region";
import { exportExcel } from "@/utils/exportExcel";

import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
export default {
  name: "Activities",
  components: { ProTable, editActivities, statisticActivities, GrandRecord },
  data() {
    return {
      tableData: [],
      columns: [
        {
          dataIndex: "activityName",
          title: "活动名称",
          isTable: true,
          isSearch: true,
          isExport: true,
          valueType: "input",
          clearable: true,
          minWidth: 150,
        },
        {
          dataIndex: "code",
          title: "活动编号",
          isTable: true,
          isSearch: false,
          isExport: true,
          valueType: "input",
          clearable: true,
          minWidth: 150,
        },
        {
          dataIndex: "activityType",
          title: "活动类型",
          tableSlot: "activityType",
          isTable: true,
          isSearch: true,
          valueType: "select",
          clearable: true,
          option: [
            { label: "特价活动", value: "DISCOUNT" },
            { label: "分享活动", value: "SHARE" },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "status",
          title: "活动状态",
          isTable: true,
          isSearch: true,
          isExport: true,
          multiple: true,
          collapseTags: true,
          valueType: "select",
          clearable: true,
          tableSlot: "status",
          minWidth: 180,
          option: [
            { label: "暂存", value: "STASH" },
            { label: "待审核", value: "WAIT_APPROVE" },
            { label: "驳回", value: "REJECT" },
            { label: "未开始", value: "NOT_STARTED" },
            { label: "进行中", value: "IN_PROGRESS" },
            { label: "已结束", value: "ENDED" },
            { label: "关闭", value: "CLOSE" },
          ],
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "sTime",
          title: "开始时间",
          isTable: false,
          isSearch: true,
          isExport: false,
          width: 180,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
        },
        {
          dataIndex: "eTime",
          title: "结束时间",
          isTable: false,
          isSearch: true,
          isExport: false,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
        },
        {
          dataIndex: "startTime",
          title: "开始时间",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "endTime",
          title: "结束时间",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "ranges",
          title: "区域范围",
          isSearch: true,
          clearable: true,
          valueType: "area",
        },
        {
          dataIndex: "createdBy",
          tableSlot: "createdBy",
          title: "创建人",
          clearable: true,
          width: 100,
          isExport: true,
          isTable: true,
          isSearch: false,
          valueType: "input",
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          isSearch: false,
          isExport: false,
          width: 180,
          valueType: "date-picker",
          pickerType: "datetime",
          pickerFormat: "yyyy-MM-dd HH:mm:ss",
        },
        {
          dataIndex: "checkUser",
          tableSlot: "checkUser",
          title: "审核人",
          clearable: true,
          isExport: true,
          width: 100,
          valueType: "input",
          isTable: true,
          isSearch: false,
        },
        {
          dataIndex: "checkTime",
          title: "审核时间",
          width: 180,
          clearable: true,
          isExport: true,
          valueType: "datetime",
          isTable: true,
          isSearch: false,
        },
        {
          dataIndex: "action",
          tableSlot: "action",
          width: 320,
          title: "操作",
          isTable: true,
          tooltip: false,
          fixed: "right",
        },
      ],
      productIdName: [],
      queryParam: {
        lastIds: [],
        sTime: [],
        eTime: [],
      },
      localPagination: {
        total: 0,
        page: 1,
        pageSize: 10,
      },
      selection: [],
      options: [],
      totalInfo: {},
      saleList: [],
      workerList: [],
    };
  },

  mounted() {
    this.refresh();
  },
  methods: {
    continueActivitiesApi,
    async loadData(params) {
      try {
        this.queryParam = filterParam(
          Object.assign({}, this.queryParam, params)
        );

        const requestParameters = cloneDeep(this.queryParam);
        if (this.queryParam.sTime && this.queryParam.sTime.length) {
          requestParameters.beginDateStart = requestParameters.sTime[0];
          requestParameters.beginDateEnd = requestParameters.sTime[1];
          delete requestParameters.sTime;
        }
        if (this.queryParam.eTime && this.queryParam.eTime.length) {
          requestParameters.deadlineDateStart = requestParameters.eTime[0];
          requestParameters.deadlineDateEnd = requestParameters.eTime[1];
          delete requestParameters.eTime;
        }
        const result = await getActivitiesApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    deleteActivities(row) {
      MessageBox.confirm("确认删除该活动？", "提示", {
        confirmButtonText: "删除",
        cancelButtonText: "取消",
        type: "info",
      })
        .then(() => {
          deleteActivitiesApi(row.id).then((res) => {
            Message.success(res.message);
            this.refresh();
          });
        })
        .catch((action) => {});
    },
    /**
     * 活动审核
     */
    checkActivities(row) {
      //  打开审核页面
      MessageBox.confirm("是否通过该活动？", "提示", {
        confirmButtonText: "通过",
        cancelButtonText: "不通过",
        type: "info",
        distinguishCancelAndClose: true,
      })
        .then(() => {
          auditActivitiesApi({ id: row.id, passed: true }).then((res) => {
            this.refresh();
          });
        })
        .catch((action) => {
          if (action == "cancel") {
            //不通过
            auditActivitiesApi({ id: row.id, passed: false }).then((res) => {
              this.refresh();
            });
          }
        });
    },
    /**
     * 暂停活动--不确定有没有，暂时不做
     */
    pauseActivities(row) {
      MessageBox.confirm("确认暂停该活动？", "提示", {
        confirmButtonText: "暂停",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          stopActivitiesApi(row.id).then((res) => {
            this.$message.success("活动已暂停");
            this.refresh();
          });
        })
        .catch((action) => {});
    },
    continueActivities(row) {
      MessageBox.confirm("是否继续开展该活动？", "提示", {
        confirmButtonText: "暂停",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          continueActivitiesApi(row.id).then((res) => {
            this.$message.success("活动已继续");
            this.refresh();
          });
        })
        .catch((action) => {});
    },
    handleEdit(row, type) {
      this.$refs.editActivities.open(type, row);
    },
    refresh() {
      this.$refs.ProTable.refresh();
      this.$refs.ProTable.$refs.ProElTable.clearSelection();
    },

    /**
     * @description 获取省市区区域数据
     * @param node
     * @param {Function} resolve
     * @returns {Promise<void>}
     */
    async getRegion(node, resolve) {
      try {
        const { level } = node;
        if (level === 0) {
          const result = await getProvinceListApi();
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        } else {
          const result = await getRegionByCodeApi(node.value);
          if (result.code === 200 && result.data) {
            resolve(this.handleReginData(result.data));
          }
        }
      } catch (error) {
        Message.error(error.message);
      }
    },
    /**
     * @description 处理省市区数据
     * @param list
     * @returns {*}
     */
    handleReginData(list) {
      return list.map((item) => ({
        label: item.name,
        value: item.code,
        leaf: !item.hasChildren,
      }));
    },
    handleReginChange(val) {
      this.queryParam["regionPath"] = val[val.length - 1];
    },
    handleSelectionChange(val) {
      this.selection = val;
    },
    exportCustom() {
      if (this.selection.length === 0) {
        Message.warning("请选择要导出的客户数据");
        return;
      }
      const fieldMap = {};
      this.columns
        .filter((item) => item.isExport === true)
        .map((item) => {
          fieldMap[item.dataIndex] = item.title;
        });
      exportExcel(this.selection, fieldMap, "客户列表");
    },
    handleSelect(arr) {
      this.queryParam.lastIds = arr.map((item) => item[item.length - 1]);
    },
    //打开活动统计
    statisticActivity(row) {
      this.$refs.statisticActivities.open(row);
    },
    // 奖品发放记录
    grandRecord(row) {
      this.$refs.grandRecord.show(row);
    },
    //审核权限
    hasPermits(key) {
      //客户没说要加。。。暂时不加权限控制。
      return true;
      const permits = this.$store.getters["user/getPermits"];
      if (permits.length > 0) {
        return permits.find((o) => o.permit == key);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.custom {
  width: 100%;
}
.btn_container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .btn_right {
    font-size: 16px;
    color: #6488cf;
    display: flex;
    gap: 20px;
    margin-right: 20px;
  }
}
</style>
