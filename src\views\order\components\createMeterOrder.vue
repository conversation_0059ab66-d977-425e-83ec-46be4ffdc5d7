<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-01 13:17:35
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-10 18:54:49
 * @Description: 
 -->
<template>
  <div class="container">
    <ProDialog
      :value="dialogVisible"
      title="生成对账单"
      width="70%"
      top="5%"
      confirm-text="确认生成对账单"
      :confirm-btn-loading="confirmLoading"
      @ok="handleDialogOk"
      @cancel="dialogVisible = false"
    >
      <ProTable
        ref="ProTable"
        :show-loading="false"
        :show-pagination="false"
        :show-search="false"
        :show-setting="false"
        :columns="columns"
        :data="tableData"
        :height="400"
      >
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button
              type="danger"
              icon="el-icon-delete"
              @click="handleDel(row)"
            >
              移除
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import { createMeterOrderApi } from "@/api/statisics";

export default {
  name: "CreateMeterOrder",
  data() {
    return {
      dialogVisible: false,
      columns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isTable: true,
          width: 140,
        },
        {
          dataIndex: "machine",
          title: "机型",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          formatter: (row) => row.deviceGroup?.label,
          width: 120,
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => row.serType?.label,
        },
        {
          dataIndex: "cycle",
          title: "年月",
          isTable: true,
        },
        {
          dataIndex: "cycleType",
          title: "结算周期",
          isTable: true,
          formatter: (row) => row.cycleType?.label,
        },
        {
          dataIndex: "pricePaperType",
          title: "计数方式",
          isTable: true,
        },
        {
          dataIndex: "beginTime",
          title: "期初时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "blackWhiteInception",
          title: "期初黑白",
          isTable: true,
        },
        {
          dataIndex: "colorInception",
          title: "期初彩色",
          isTable: true,
        },
        {
          dataIndex: "fiveColourIncption",
          title: "期初五色",
          isTable: true,
        },
        {
          dataIndex: "endTime",
          title: "期末时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "blackWhiteCutoff",
          title: "期末黑白",
          isTable: true,
        },
        {
          dataIndex: "colorCutoff",
          title: "期末彩色",
          isTable: true,
        },
        {
          dataIndex: "fiveColourCutoff",
          title: "期末五色",
          isTable: true,
        },
        {
          dataIndex: "amount",
          title: "总金额",
          valueType: "input",
          width: 90,
          isTable: true,
        },
        {
          dataIndex: "blackWhitePoint",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorPoint",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "giveColorPoint",
          title: "五色印量",
          isTable: true,
        },
        {
          dataIndex: "blackRange",
          title: "黑白印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "colorRange",
          title: "彩色印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "totalRange",
          title: "总印量",
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "action",
          fixed: "right",
          width: 100,
        },
      ],
      tableData: [],
      confirmLoading: false,
    };
  },
  mounted() {},
  methods: {
    show(data) {
      this.tableData = cloneDeep(data);
      this.dialogVisible = true;
    },
    handleDialogOk() {
      this.$confirm("是否确认生成对账单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.confirmLoading = true;
        const ids = this.extractIds(this.tableData);
        createMeterOrderApi(ids)
          .then((res) => {
            this.$message.success("操作成功");
            this.dialogVisible = false;
            this.$emit("ok");
          })
          .finally(() => {
            this.confirmLoading = false;
          });
      });
    },
    handleDel(row) {
      this.tableData = this.tableData.filter((item) => item.id !== row.id);
    },
    extractIds(data) {
      const ids = [];
      function recurse(items) {
        items.forEach((item) => {
          ids.push(item.id);
          if (item.children && Array.isArray(item.children)) {
            recurse(item.children);
          }
        });
      }
      recurse(data);
      return ids;
    },
  },
};
</script>

<style scoped lang="scss"></style>
