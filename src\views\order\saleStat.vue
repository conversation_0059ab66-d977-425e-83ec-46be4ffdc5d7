<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 14:26:41
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="月度统计" name="月度统计" lazy>
        <salesStat type="monthSale" :columns="monthStatColumns" />
      </el-tab-pane>
      <el-tab-pane label="耗材统计" name="耗材统计" lazy>
        <salesStat type="consumableStat" :columns="consumableStatColumns" />
      </el-tab-pane>
      <el-tab-pane label="维修统计" name="维修统计" lazy>
        <salesStat type="repairStat" :columns="repairStatColumns" />
      </el-tab-pane>
      <el-tab-pane label="抄表统计" name="抄表统计" lazy>
        <salesStat type="meterStat" :columns="meterStatColumns" />
      </el-tab-pane>
      <el-tab-pane label="机器统计" name="机器统计" lazy>
        <salesStat type="machineStat" :columns="machineStatColumns" />
      </el-tab-pane>
      <el-tab-pane label="月销售统计" name="月销售统计" lazy>
        <MonthStat />
      </el-tab-pane>
      <!--      <el-tab-pane label="客户月销售情况" name="客户月销售情况" lazy>-->
      <!--        -->
      <!--      </el-tab-pane>-->
      <el-tab-pane label="订单分类统计" name="订单分类统计" lazy>
        <MonthSaleAccount />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MonthStat from "./monthSaleAccount.vue";
import salesStat from "@/views/order/components/salesStat.vue";
import MonthSaleAccount from "./components/customMonthSaleAccount.vue";
import { dictTreeByCodeApi } from "@/api/user";
export default {
  name: "SaleStat",
  components: { MonthStat, MonthSaleAccount, salesStat },
  data() {
    return {
      activeName: "月度统计",
      monthStatColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "totalAmount",
          title: "总金额",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "mechineAmount",
          title: "机器销售",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "orderAmount",
          title: "商城耗材",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "workPartAmount",
          title: "维修耗材",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "workLaborAmount",
          title: "维修人工",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "monthlyAmount",
          title: "包月人工",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "receiptAmount",
          title: "全/半包抄表",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "refundAmount",
          title: "退款金额",
          isTable: true,
        },
        {
          dataIndex: "incomeAmount",
          title: "净总金额",
          isTable: true,
        },
        // {
        //   dataIndex: "exhibitions",
        //   title: "半包抄表",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        // },
        // {
        //   dataIndex: "lease",
        //   title: "租赁",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        // },
      ],
      consumableStatColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "source",
          title: "渠道",
          isTable: true,
          formatter: (row) => (row.source === 1 ? "销售耗材" : "维修耗材"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "销售耗材",
              value: 1,
            },
            {
              label: "维修耗材",
              value: 2,
            },
          ],
        },
        {
          dataIndex: "totalAmount",
          title: "购买金额",
          isTable: true,
        },
        {
          dataIndex: "orderCount",
          title: "购买次数",
          isTable: true,
        },
        {
          dataIndex: "customerNum",
          title: "购买客户数",
          isTable: true,
        },
        {
          dataIndex: "avgOrderAmount",
          title: "次均金额",
          isTable: true,
        },
        {
          dataIndex: "avgCustOrderAmount",
          title: "客均金额",
          isTable: true,
        },
      ],
      repairStatColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "treatyTypes",
          title: "服务类型",
          isSearch: true,
          valueType: "select",
          multiple: true,
          option: [],
          optionMth: () => dictTreeByCodeApi(1200),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "amount",
        //   title: "总金额",
        //   isTable: true,
        // },
        {
          dataIndex: "totalPay",
          title: "维修金额",
          isTable: true,
        },
        {
          dataIndex: "repairCount",
          title: "维修次数",
          isTable: true,
        },
        {
          dataIndex: "repairTimeStr",
          title: "维修时长",
          isTable: true,
        },
        {
          dataIndex: "avgAmount",
          title: "次均金额",
          isTable: true,
        },
        {
          dataIndex: "avgRepairTimeStr",
          title: "次均时长",
          isTable: true,
        },
      ],
      meterStatColumns: [
        {
          dataIndex: "cycle",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "serviceType",
          title: "服务类型",
          isSearch: true,
          valueType: "select",
          option: [
            {
              value: 1,
              label: "全包",
            },
            {
              value: 2,
              label: "半包",
            },
            {
              value: 3,
              label: "租赁",
            },
          ],
        },
        {
          dataIndex: "totalPay",
          title: "总抄表费",
          isTable: true,
        },
        {
          dataIndex: "blackPrintCount",
          title: "黑白总印量",
          isTable: true,
        },
        {
          dataIndex: "blackPrintAmount",
          title: "黑白抄表费",
          isTable: true,
        },
        {
          dataIndex: "mechineCount",
          title: "机器数量",
          isTable: true,
        },
        {
          dataIndex: "avgBlackPrintCount",
          title: "台均印量",
          isTable: true,
        },
        {
          dataIndex: "avgBlackPrintAmount",
          title: "台均抄表费",
          isTable: true,
        },
        {
          dataIndex: "colorPrintCount",
          title: "彩色总印量",
          isTable: true,
        },
        {
          dataIndex: "colorPrintAmount",
          title: "彩色抄表费",
          isTable: true,
        },
        {
          dataIndex: "avgColorPrintCount",
          title: "台均印量",
          isTable: true,
        },
        {
          dataIndex: "avgColorPrintAmount",
          title: "台均抄表费",
          isTable: true,
        },
      ],
      machineStatColumns: [
        {
          dataIndex: "currMonth",
          title: "月份",
          isTable: true,
          isSearch: true,
          sortable: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "totalAmount",
          title: "销售额",
          isTable: true,
        },
        {
          dataIndex: "blackAmount",
          title: "黑白机销售额",
          isTable: true,
        },
        {
          dataIndex: "blackNum",
          title: "台数",
          isTable: true,
        },
        {
          dataIndex: "avgBlackAmount",
          title: "台均金额",
          isTable: true,
        },
        {
          dataIndex: "colorAmount",
          title: "彩机销售额",
          isTable: true,
        },
        {
          dataIndex: "colorNum",
          title: "台数",
          isTable: true,
        },
        {
          dataIndex: "avgColorAmount",
          title: "台均金额",
          isTable: true,
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss"></style>
