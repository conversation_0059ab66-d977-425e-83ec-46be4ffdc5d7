<template>
  <div class="tags">
    <!-- <el-tag
      v-for="tag in dynamicTags"
      :key="tag"
      closable
      :disable-transitions="false"
      @close="handleClose(tag)"
    >
      {{ tag }}
    </el-tag> -->
  </div>
</template>
<script>
export default {
  name: "",
  components: {},
  props: {
    config: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      dynamicTags: ["标签一", "标签二", "标签三"],
    };
  },
  computed: {},
  watch: {
    $route: {
      handler(val) {
        this.routerActive = val.path;
      },
      immediate: true,
      deep: true,
    },
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  destroyed() {},

  methods: {
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },
  },
};
</script>
<style lang="scss">
.tags {
  width: calc(100% - 290px);
  float: left;
  padding: 5px;
  height: 32px;
  background: #ffff;
  text-align: left;
}
</style>
