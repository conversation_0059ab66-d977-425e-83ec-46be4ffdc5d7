/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-25 16:20:18
 * @Description:
 */
/*
 * @Description: 关于用户登录的方法
 * @Autor: shh
 * @Date: 2022-11-15 10:28:47
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-25 16:20:18
 */
import { login, logout, getInfo, resources } from "@/api/user";
import { getToken, setToken, removeToken, getPagePermit } from "@/utils/auth";
import { encodeFun, treeToArray } from "@/utils/index";

import setting from "@/config/setting.js";
const { tokenStorage } = setting;

import { resetRouter } from "@/router";
const getDefaultState = () => {
  return {
    token: getToken(),
    name: "",
    avatar: "",
    permits: [], //按钮权限
  };
};
const state = getDefaultState();
const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState());
  },
  SET_TOKEN: (state, token) => {
    state.token = token;
    localStorage.setItem(tokenStorage, token);
  },
  SET_NAME: (state, name) => {
    state.name = name;
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar;
  },
  SET_MAJOR: (state, avatar) => {
    state.major = avatar;
  },
  SET_MAJORID: (state, avatar) => {
    state.majorId = avatar;
  },
  SET_DEPARTMENTID: (state, avatar) => {
    state.departmentId = avatar;
  },
  SET_DEPARTMENT: (state, avatar) => {
    state.department = avatar;
  },
  setPagePermits(state, pagePath) {
    state.permits = getPagePermit(pagePath);
  },
};
const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password, key, captcha, captchaToken, passwordToken } =
      userInfo;

    const obj = {
      captcha: captcha,
      captchaToken: captchaToken,
      code: username,
      password: encodeFun(password, key),
      passwordToken: passwordToken,
    };
    return new Promise((resolve, reject) => {
      login(obj)
        .then((response) => {
          const { data } = response;
          commit("SET_TOKEN", data.first);
          setToken(data.first);
          // commit('SET_NAME', data.second.name)
          localStorage.setItem("userInfo", JSON.stringify(data.second));
          commit("SET_NAME", JSON.parse(localStorage.getItem("userInfo")).name);
          commit("SET_AVATAR", "avatar");
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  getRouter({ commit, state }) {
    //获取用户路由
    return new Promise((resolve, reject) => {
      resources()
        .then((response) => {
          const { data } = response;
          const data2 = [
            ...[
              {
                icon: "icon-user",
                label: "首页",
                value: "/index",
              },
            ],
            ...data,
          ];
          localStorage.setItem("systemMenu", JSON.stringify(data2));
          localStorage.setItem("powerMenu", JSON.stringify(treeToArray(data2)));
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  // 获取用户信息
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo(state.token)
        .then((response) => {
          const { data } = response;
          if (!data) {
            return reject("Verification failed, please Login again.");
          }
          const { name, id, parentId } = data[0];
          commit("SET_MAJOR", name);
          commit("SET_MAJORID", id);
          commit("SET_DEPARTMENTID", parentId);
          commit("SET_NAME", JSON.parse(localStorage.getItem("userInfo")).name);
          // if (data.length == 1) {
          // orgClassApi().then((res) => {
          //   let parentName = ''
          //   res.data.forEach(ele => {
          //     if (ele.id === parentId) {
          //       parentName = ele.name
          //     }
          //   });
          //   commit('SET_DEPARTMENT', parentName)
          //   let obj = {
          //     ...JSON.parse(localStorage.getItem('userInfo')),
          //     major: name,
          //     majorId: id,
          //     department: parentName,
          //     departmentId: parentId,
          //   }
          //   localStorage.setItem('userInfo', JSON.stringify(obj))
          // })
          // }

          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          removeToken(); // must remove  token  first
          resetRouter();
          localStorage.clear();
          window.location.reload();

          commit("RESET_STATE");
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  // remove token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      removeToken(); // must remove  token  first
      commit("RESET_STATE");
      resolve();
    });
  },
};
const getters = {
  getPermits: (state) => {
    return state.permits;
  },
};
export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
