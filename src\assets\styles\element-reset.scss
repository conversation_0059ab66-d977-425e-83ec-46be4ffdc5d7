/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-04 14:20:45
 * @Description: 
 */
// .el-breadcrumb__inner,
// .el-breadcrumb__inner a {
//     font-weight: 400 !important;
// }

// .el-upload {
//     input[type="file"] {
//         display: none !important;
//     }
// }

// .el-upload__input {
//     display: none;
// }

// .el-input.is-disabled .el-input__inner,
// .el-textarea.is-disabled .el-textarea__inner {
//     background-color: #fafafa;
//     border-color: #EBEEF5;
//     color: #333;
//     cursor: not-allowed;
// }

// // ===弹框===
// .el-dialog {
//     transform: none;
//     left: 0;
//     position: relative;
//     margin: 0 auto;
//     padding: 10px 20px;
//     box-shadow: none;
//     background: transparent;
//     background-image: url('@/assets/images/lng/dialog.png');
//     background-size: 100% 100%;
//     .el-dialog__header {
//         padding: 0;
//         width: 90%;
//         height: 30px;
//         background: linear-gradient(90deg, #10d5e5 0%, rgba(2, 186, 201, 0) 100%);
//     }
//     .el-dialog__body {
//         height: calc(100% - 100px);
//         padding: 24px 40px;
//         overflow: hidden;
//         overflow-y: auto;
//     }
//     .el-dialog__title {
//         line-height: 26px;
//         width: 50%;
//         padding: 4px 10px;
//         position: relative;
//         font-size: 18px;
//         font-family: ShiShangZhongHeiJianTi;
//         letter-spacing: 2px;
//         font-weight: 800;
//         color: #457dec;
//         display: inline-block;
//         width: 95%;
//         height: 30px;
//         white-space: nowrap;
//         text-overflow: ellipsis;
//         overflow: hidden;
//         &::before {
//             content: "";
//             width: 4px;
//             height: 30px;
//             background: #02BAC9;
//             display: inline-block;
//             position: absolute;
//             left: -7px;
//             // transform: translate(0, -50%);
//             top: 2px;
//         }
//     }
//     .el-input__inner {
//         height: 32px;
//         line-height: 32px;
//     }
//     .el-table {
//         padding: 0 !important;
//     }
// }

// // refine element ui upload
// .upload-container {
//     .el-upload {
//         width: 100%;
//         .el-upload-dragger {
//             width: 100%;
//             height: 200px;
//         }
//     }
// }

// // dropdown
// .el-dropdown-menu {
//     a {
//         display: block
//     }
// }

// // to fix el-date-picker css style
// .el-range-separator {
//     box-sizing: content-box;
// }

// .el-button {
//     font-size: 16px;
// }

// .el-button--primary {
//     background: linear-gradient(90deg, #457DEC, #5EAAFF);
// }

// .el-button--warning {
//     background: linear-gradient(90deg, #EB4A20, #F59751);
// }

.el-form-item {
  margin-bottom: 20px;
}
.el-form-item .el-form-item__content {
  flex: 1 ; /* Make the input field take the remaining space */
}
.el-form-item-hint-message {
  color: #999;
  font-size: 0.75rem;
  line-height: 1;
  padding-top: 0.25rem;
  position: absolute;
  top: 100%;
  left: 0;
}
.reset_progress {
  .el-notification__group {
    width: 100%;
    .el-progress-circle{
      width: 90px !important;
      height: 90px !important;
    }
  }
}
.el-row {
  .el-col-5 {
    max-width: 20%;
    flex: 0 0 20%;
  }
}
// .table-page-search-wrapper {
//     padding: 16px 20px 0 20px;
// }

// .table-page-search-wrapper .el-form-item {
//     // margin-bottom: 24px;
// }

// .dialog-box .el-form-item {
//     // margin-bottom: 16px;
// }

//========表格=======
.el-table {
  // padding: 0 20px !important;

  thead {
    height: 56px !important;
  }

  // tr:nth-child(even) {
  //   background: #FAFAFA;
  // }
  th,
  th.el-table__cell {
    background: rgba(195, 209, 222, 0.34) !important;
    color: #333 !important;
  }

  .el-table__cell {
    padding: 10px 0 !important;
  }

  .cell {
    padding: 0 10px !important;
  }
}

.el-table--border .el-table__cell,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border: none;
}
.el-table__body-wrapper {
  //overflow-y: auto !important; // 解决跳转到最后一页overflow变成hidden
}
.el-dialog,
.dialog-box {
  .el-table {
    padding: 5px !important;
  }

  thead {
    height: 40px !important;
  }

  .el-table__cell {
    padding: 5px 0 !important;
  }

  .cell {
    padding: 0 10px !important;
  }
}

.no-padding {
  .el-table {
    padding: 0 !important;
  }

  thead {
    height: 40px !important;
  }

  .el-table__cell {
    padding: 0 !important;
  }

  td,
  .cell {
    padding: 0 !important;
  }

  td {
    position: relative;
  }

  .el-button--primary {
    padding: 5px;
    font-size: 12px;
  }
}

.el-button {
  font-size: 14px;
}

// ====按钮
.fixed-width {
  display: flex;
  flex-wrap: wrap;

  gap: 10px;
  .el-button [class*="el-icon-"] + span {
    margin: 0;
  }

  .el-button {
    color: #457dec;
    border-radius: 4px;
    font-size: 12px;
    padding: 5px;
    margin-left: 0;
  }

  .el-button--primary {
    background: rgba(255, 255, 255, 0);
    border: 1px solid rgba(0, 105, 255, 0.4);
    border-radius: 4px;
    font-size: 12px;
    color: #457dec;
    padding: 5px;
  }

  .el-button--edit {
    background: rgba(255, 255, 255, 0);
    border: 1px solid rgba(19, 203, 106, 0.4);
    border-radius: 4px;
    font-size: 12px;
    color: #13cb6a;
    padding: 5px;
  }
  .el-button--edit:hover{
    background: rgba(19, 203, 106, 0.1);
  }
  .el-button--edit:active{
    border: 1px solid rgba(19, 203, 106, 1);
  }

  .el-button--danger {
    background: rgba(255, 255, 255, 0);
    border: 1px solid rgba(255, 41, 41, 0.4);
    border-radius: 4px;
    font-size: 12px;
    color: #ff2929;
    padding: 5px;
  }
  .el-button--danger:hover{
    background: rgba(255, 41, 41, 0.1);
  }
  .el-button--danger:active{
    border: 1px solid rgba(255, 41, 41, 1);
  }
  .el-button--danger.is-disabled{
    border: 1px solid rgba(255, 41, 41, 0.4);
    color:rgba(255, 41, 41, 0.4);
    &:hover{
      background: rgba(255, 41, 41, 0);
    }
  }

  .el-button--btn1 {
    background: rgba(255, 255, 255, 0);
    border: 1px solid rgba(0,105,255,0.4);
    border-radius: 4px;
    font-size: 12px;
    color: #0069ff;
    padding: 5px;
  }
  .el-button--btn1:hover{
    background: rgb(0, 105, 255, 0.1);
  }
  .el-button--btn1:active{
    border: 1px solid rgba(0,105,255,1);
  }

  .el-button--btn2 {
    background: rgba(255, 255, 255, 0);
    border: 1px solid rgb(42, 204, 233, 0.4);
    border-radius: 4px;
    font-size: 12px;
    color: #2acce9;
    padding: 5px;
  }
  .el-button--btn2:hover{
    background: rgb(42, 204, 233, 0.1);
  }
  .el-button--btn2:active{
    border: 1px solid rgb(42, 204, 233, 1);
  }

  .el-button--btn3 {
    background: rgba(255, 255, 255, 0);
    border: 1px solid rgb(255, 123, 17, 0.4);
    border-radius: 4px;
    font-size: 12px;
    color: #ff7b11;
    padding: 5px;
  }
  .el-button--btn3:hover{
    background: rgba(255, 123, 17, 0.1);
  }
  .el-button--btn3:active{
    border: 1px solid rgb(255, 123, 17, 1);
  }
}

.table-page-search-wrapper {
  .el-button {
    padding: 10px 24px;
    font-size: 14px;
  }
}

// .no-con {
//     .el-tabs--border-card>.el-tabs__content {
//         padding: 0 !important;
//     }
// }

// .el-radio__input.is-disabled.is-checked .el-radio__inner {
//     border-color: #409EFF;
//     background: #409EFF;
// }

// .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
//     background-color: #fff;
// }

// .el-radio__input.is-disabled.is-checked+span.el-radio__label {
//     color: #409EFF;
// }

// .el-dialog__headerbtn {
//     right: 30px;
//     top: 25px;
//     .el-dialog__close {
//         color: #03B3C5
//     }
// }

// .el-dialog__header {
//     margin-top: 10px;
// }

// .el-dialog {
//     .el-select,
//     .el-cascader {
//         width: 100%;
//     }
// }

// .el-select-dropdown__item {
//     height: auto;
// }

// .tree {
//     .el-tree-node__content {
//         padding-left: 0px;
//         height: 40px;
//         background: #FFFFFF;
//         border: 1px solid #D9E0E7;
//         margin-top: 5px;
//     }
//     .el-tree-node.is-current.is-focusable .el-tree-node__content {
//         color: #457dec;
//         font-weight: 800;
//         border: 1px solid #457dec;
//         &::before {
//             display: block;
//             content: "";
//             width: 4px;
//             height: 40px;
//             position: absolute;
//             background: #457dec;
//             left: 0;
//         }
//     }
//     .el-tree-node.is-current.is-focusable .el-tree-node__children {
//         .el-tree-node__content {
//             padding-left: 0;
//             background: #FFFFFF;
//             border: 1px solid #D9E0E7;
//             color: #606266;
//             font-weight: inherit;
//             &::before {
//                 display: none;
//             }
//         }
//     }
// }

// .el-descriptions__title {
//     font-size: 14px;
//     font-weight: 400;
// }

// .el-descriptions-item__label.is-bordered-label {
//     color: #333;
// }

// .el-upload-list__item .el-icon-close-tip {
//     display: none !important;
// }

// .el-upload-list__item-name {
//     width: 200px !important;
// }

// .el-message {
//     margin-top: 80px !important;
// }

// .search-btn {
//     background: linear-gradient(90deg, #036E99, #23ABC8) !important;
//     border: none !important;
//     ;
// }

// .add-btn {
//     background: linear-gradient(90deg, #47CC90, #0FA790) !important;
//     border: none !important;
//     ;
// }

// .edit-btn {
//     background: linear-gradient(90deg, #0B9BC0 0%, #18B2CB 100%) !important;
//     border: none !important;
//     ;
// }

// .del-btn {
//     background: linear-gradient(90deg, #F4C257, #E77D1E) !important;
//     border: none !important;
//     ;
// }

// .el-pagination.is-background .btn-prev,
// .el-pagination.is-background .btn-next,
// .el-pagination.is-background .el-pager li:not(.disabled) {
//     background-color: #fff !important;
//     border: 1px solid #edf2f2 !important;
//     color: #333 !important;
// }

// .el-pagination.is-background .btn-next.disabled,
// .el-pagination.is-background .btn-next:disabled,
// .el-pagination.is-background .btn-prev.disabled,
// .el-pagination.is-background .btn-prev:disabled,
// .el-pagination.is-background .el-pager li.disabled {
//     background-color: #edf2f2 !important;
// }

// .el-pagination.is-background .el-pager li:not(.disabled).active {
//     background: #0D9CC2 !important;
//     color: #FFF !important;
// }

.el-drawer__header {
  margin: 5px 0 0 10px !important;
  padding: 0 !important;
  height: 30px !important;
  background: linear-gradient(
    90deg,
    #10d5e5 0%,
    rgba(2, 186, 201, 0) 100%
  ) !important;

  span {
    line-height: 26px;
    width: 50%;
    padding: 4px 10px;
    position: relative;
    font-size: 18px;
    font-family: ShiShangZhongHeiJianTi;
    letter-spacing: 2px;
    font-weight: 800;
    color: #457dec;

    &::before {
      content: "";
      width: 4px;
      height: 30px;
      background: #02bac9;
      display: inline-block;
      position: absolute;
      left: -7px;
      // transform: translate(0, -50%);
      top: 2px;
    }
  }
}

.el-drawer__body {
  padding: 20px;
  //padding-bottom: 60px;
}
.resetBottom{
  .el-drawer__body{
    padding-bottom: 92px;
  }
}

.btn-box {
  width: 100%;
  text-align: center;
}

// .el-input-number__decrease,
// .el-input-number__increase {
//     border: none!important;
//     background: none!important;
// }

// .el-input-number.is-controls-right .el-input__inner {
//     text-align: left !important;
// }
.el-menu-item {
  height: 45px;
  line-height: 45px;
  margin: 10px 0;
}

.el-menu-item.is-active,
.el-menu-item:hover {
  color: $base-color-default;
  background-color: $base-color-primary-light9;
  border-radius: 10px;
  height: 45px;
  line-height: 45px;
}

.el-popover.el-popper {
  min-width: 100px !important;
  //padding: 5px !important;
  .el-popconfirm{
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
}

.el-menu {
  background-color: #fff;
}

.el-header {
  --el-header-padding: 0;
}

.el-button--primary:active {
  background-color: $base-color-default !important;
  border-color: $base-color-default !important;
}

.el-tabs--card > .el-tabs__header .el-tabs__item .el-icon-close {
  width: 15px;
}

.el-tabs__item,
.el-tabs__item:hover {
  padding: 0 10px;
}

.el-tabs__header .el-tabs__nav .el-tabs__item:hover,
.el-tabs__header .el-tabs__nav .el-tabs__item.is-active {
  color: #409eff;
  background: rgba(78, 136, 243, 0.1);
  border: none;
}

.el-input.is-disabled .el-input__inner,
.el-textarea.is-disabled .el-textarea__inner {
  color: #606266;
}

.el-step__title {
  font-size: 16px !important;
  font-weight: 200 !important;
  color: #c0c4cc;
}
.el-step__description.is-process {
  font-weight: 700 !important;
  color: #409eff !important;
}
.el-step__head.is-process,
.el-step__title.is-process {
  font-size: 16px !important;
  font-weight: 700 !important;
  color: #409eff !important;
}

.is-process .el-step__icon.is-text {
  border: 1px solid #409eff;
}

.el-dialog .el-table__fixed-right {
  top: 5px;
}

.cascader-disable-radio{
  .el-cascader-panel .el-radio {
    width: 100%;
    height: 100%;
    z-index: 10;
    position: absolute;
    top: 10px;
    right: 10px;
  }
  .el-cascader-panel .el-radio__input {
    visibility: hidden;
  }
  .el-cascader-panel .el-cascader-node__postfix {
    top: 10px;
  }
}
.QRDialog {
  .el-dialog__body {
    padding: 0;
  }
}
