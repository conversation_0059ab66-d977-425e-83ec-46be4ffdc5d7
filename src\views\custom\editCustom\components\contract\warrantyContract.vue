<!--
 * @Author: wskg
 * @Date: 2025-01-15 15:30:19
 * @LastEditors: name
 * @LastEditTime: Do not edit
 * @Description: 质保合约
 -->
<template>
  <div class="warranty-contract">
    <el-form
      ref="warrantyRef"
      :model="infoData"
      label-width="140px"
      :rules="formRules"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="质保类型：" prop="warrantyType">
            <el-select
              v-model="infoData.warrantyType"
              placeholder="请选择质保类型"
              style="width: 100%"
              clearable
              :disabled="editType === 'info'"
            >
              <el-option
                v-for="item in warrantyTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 质保截止日期 -->
        <el-col :span="6">
          <el-form-item label="质保截止日期：" prop="warrantyExpireDate">
            <el-date-picker
              v-model="infoData.warrantyExpireDate"
              type="date"
              placeholder="选择质保截止日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              :disabled="editType === 'info'"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <!-- 质保总印量 -->
        <el-col :span="6">
          <el-form-item label="质保总印量：" prop="warrantyCount">
            <el-input
              v-model="infoData.warrantyCount"
              placeholder="请输入质保总印量"
              type="number"
              style="width: 100%"
              :disabled="editType === 'info'"
            >
              <template #suffix> 张 </template>
            </el-input>
          </el-form-item>
        </el-col>
        <!-- 质保维修次数 -->
        <el-col v-if="infoData.warrantyType === 'AFTERMARKET'" :span="6">
          <el-form-item label="质保维修次数：" prop="warrantyRepairCount">
            <el-input
              v-model="infoData.warrantyRepairCount"
              placeholder="请输入质保维修次数"
              type="number"
              style="width: 100%"
              :disabled="editType === 'info'"
            >
              <template #suffix> 次 </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "WarrantyContract",
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    editType: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      warrantyTypeOptions: [
        {
          label: "维修质保",
          value: "REPAIR",
        },
        {
          label: "售后质保",
          value: "AFTERMARKET",
        },
        {
          label: "电路质保",
          value: "CIRCUIT",
        },
      ],
      formRules: {
        warrantyType: [
          {
            required: true,
            message: "请选择质保类型",
            trigger: "change",
          },
        ],
        warrantyExpireDate: [
          {
            required: true,
            message: "请选择质保截止日期",
            trigger: "blur",
          },
        ],
        warrantyCount: [
          {
            required: true,
            message: "请输入质保总印量",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (value < 0) {
                callback(new Error("质保总印量必须大于等于0"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  computed: {
    infoData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  created() {
    const defaultValues = {
      warrantyType: "REPAIR",
      warrantyExpireDate: "", // 质保截止日期
      warrantyCount: 0, // 质保总印量
      warrantyRepairCount: 0,
    };
    this.$emit("input", { ...defaultValues, ...this.value });
  },
};
</script>

<style scoped lang="scss"></style>
