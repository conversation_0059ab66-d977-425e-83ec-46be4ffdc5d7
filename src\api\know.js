/*
 * @Author: s<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-12-21 15:04:00
 * @LastEditors: shanhaihong <EMAIL>
 * @LastEditTime: 2023-12-26 16:56:38
 * @FilePath: /operate_web/src/api/know.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { get, post, put, del } from "@/utils/request";

// 知识库管理
export const baseListApi = (params) => get("/knowledge-base-info/list", params);

export const baseByPageApi = (data) => post("/knowledge-base-info/page", data);

export const baseAddApi = (params) =>
  post("/knowledge-base-info/base-save", params);
export const baseAddApi1 = (params) =>
  post("/knowledge-base-info/code-explain-save", params);
export const baseAddApi2 = (params) =>
  post("/knowledge-base-info/repair-case-save", params);

export const baseAddApi3 = (params) =>
  post("/knowledge-base-info/relate-setting-save", params);

export const baseInfoApi = (id) => get(`/knowledge-base-info/detail/${id}`);

export const baseDelApi = (id) => del("/knowledge-base-info/" + id);
export const baseOperateApi = (params) =>
  post("/knowledge-base-info/enable-operate", params);

// 热词管理
export const hotListApi = (params) => get("/knowledge-hot-words/list", params);

export const hotByPageApi = (data) => get("/knowledge-hot-words/page", data);

export const hotAddApi = (params) => post("/knowledge-hot-words", params);

export const hotEditApi = (params) => put("/knowledge-hot-words", params);

export const hotDelApi = (id) => del("/knowledge-hot-words/" + id);

// 使用帮助
export const helpListApi = (params) => get("/knowledge-use-help/list", params);

export const helpByPageApi = (data) => get("/knowledge-use-help/page", data);

export const helpAddApi = (params) => post("/knowledge-use-help", params);

export const helpEditApi = (params) => put("/knowledge-use-help", params);

export const helpDelApi = (id) => del("/knowledge-use-help/" + id);
