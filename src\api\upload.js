/*
 * @Author: yangz<PERSON>
 * @Date: 2023-10-30 17:28:54
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-03 16:37:20
 * @Description:
 */

import { get } from "@/utils/request";
import cos from "@/utils/cos";
import { uuid } from "vue-uuid";

let Bucket, Region, Prefix;

export const getCredentials = () => get("/cos/credentials");

export const getBucket = () => get("/cos/bucket");

export const uploadFile = async (file) => {
  if (!Bucket || !Region || !Prefix) {
    const res = await getBucket();
    Bucket = res.data.bucket;
    Region = res.data.region;
    Prefix = res.data.prefix;
  }
  // console.log(file, "file");
  return new Promise((resolve, reject) => {
    const key = uuid.v1();
    cos.uploadFile(
      {
        Bucket,
        Region,
        Key: Prefix + key,
        Body: file,
        SliceSize: 1024 * 1024 * 5,
        AsyncLimit: 5,
        onProgress: (progressData) => {
          // console.log(progressData)
        },
      },
      (err, data) => {
        if (err) {
          console.log(err);
        }
        // console.log(data);
        // console.log(file);
        resolve({
          name: file.name,
          key: Prefix + key,
          type: file.type,
          url: "https://" + data.Location,
        });
      }
    );
  });
};
