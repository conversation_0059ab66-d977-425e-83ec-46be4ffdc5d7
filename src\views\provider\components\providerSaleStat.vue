<!--
 * @Author: wskg <EMAIL>
 * @Date: 2024-10-11 09:54:06
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2024-10-11 13:48:11
 * @FilePath: \benyin-web\src\views\provider\components\providerSaleStat.vue
 * @Description: 供应商 - 销售统计
 * 
-->

<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-params="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <div class="title-box-right">
          <div>总采购数：{{ statisticData?.number || 0 }}</div>
          <div>总退货数：{{ statisticData?.returnNum || 0 }}</div>
          <div>总实收数：{{ statisticData?.receiveNum || 0 }}</div>
          <div>总采购金额：{{ statisticData?.amount || 0 }}</div>
          <div>总退货金额：{{ statisticData?.returnAmount || 0 }}</div>
        </div>
      </template>
      <!-- 机型 -->
      <template #fullIdPath>
        <el-cascader
          ref="ProductIds"
          v-model="productIdName"
          filterable
          clearable
          :options="options"
          style="width: 100%"
          :props="{
            label: 'name',
            value: 'fullIdPath',
            children: 'children',
            expandTrigger: 'click',
            multiple: true,
          }"
          leaf-only
          collapse-tags
          @change="handleSelect"
        ></el-cascader>
      </template>
      <!-- 物品大小类 -->
      <template #goodsType>
        <el-cascader
          ref="ProductIds"
          v-model="goodsType"
          filterable
          :options="goodsTypeOptions"
          style="width: 100%"
          :props="{
            label: 'label',
            value: 'value',
            children: 'children',
            expandTrigger: 'click',
          }"
          clearable
          leaf-only
          @change="handleChangeType"
        ></el-cascader>
      </template>
      <!-- 适用机型 -->
      <template #machine="slotProps">
        <el-popover placement="bottom" title="" width="700" trigger="click">
          <div style="margin: 20px; height: 400px; overflow-y: scroll">
            <el-descriptions
              class="margin-top"
              title="适用机型"
              :column="1"
              border
            >
              <el-descriptions-item
                v-for="item in slotProps.row?.productTreeDtoList"
                :key="item.id"
              >
                <template slot="label"> 品牌/系列/机型 </template>
                {{ item.brand }}/{{ item.serial }}/{{ item.machine }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-button slot="reference" size="mini">适用机型</el-button>
        </el-popover>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import { productAllApi } from "@/api/dispose";
import { dictTreeByCodeApi } from "@/api/user";
import {
  getSupplierMonthStatisticsApi,
  getSupplierTotalStatisticsApi,
  getSupplierModelStatisticsApi,
  getSupplierSupplierStatisticsApi,
  getSupplierCustomerStatisticsApi,
} from "@/api/manufacturer";
export default {
  name: "ProviderSaleStat",
  props: {
    type: {
      type: String,
      default: "month",
    },
    columns: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      statisticData: {},
      productIdName: "",
      options: [],
      goodsTypeOptions: [],
      goodsType: "",
    };
  },
  mounted() {
    this.refresh();
    this.init();
  },
  methods: {
    /**
     * @description: 加载表格数据
     * @param {*} parameter
     * @return {*}
     */
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          yearMonthStart: null,
          yearMonthEnd: null,
          data: parameter.yearMonth,
        },
        {
          startDate: null,
          endDate: null,
          data: parameter.month,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      ["yearMonth", "month"].forEach((key) => delete requestParameters[key]);
      const editApi = this.getMethodsApi(this.type);
      if (!editApi) {
        this.$refs.ProTable.listLoading = false;
        return;
      }
      editApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      const statisticApi = this.getStatisticsApi(this.type);
      if (!statisticApi) {
        this.statisticData = {};
        return;
      }
      statisticApi(requestParameters).then((res) => {
        this.statisticData = res.data;
      });
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
      dictTreeByCodeApi(2100).then((res) => {
        this.goodsTypeOptions = res.data;
      });
    },
    /**
     * @description: 获取加载表格数据API
     * @param {*} type
     * @return {*}
     */
    getMethodsApi(type) {
      switch (type) {
        case "month":
          return getSupplierMonthStatisticsApi;
        case "model":
          return getSupplierModelStatisticsApi;
        case "item":
          return getSupplierSupplierStatisticsApi;
        case "customer":
          return getSupplierCustomerStatisticsApi;
        default:
          return "";
      }
    },
    /**
     * @description: 获取加载统计数据API
     * @param {*} type
     * @return {*}
     */
    getStatisticsApi(type) {
      switch (type) {
        case "month":
          return getSupplierTotalStatisticsApi;
        case "model":
          return getSupplierTotalStatisticsApi;
        case "item":
          return getSupplierTotalStatisticsApi;
        case "customer":
          return getSupplierTotalStatisticsApi;
        default:
          return "";
      }
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
    handleChangeType(val) {
      this.$set(this.queryParam, "type", val[val.length - 1]);
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
