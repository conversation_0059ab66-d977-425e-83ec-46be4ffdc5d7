/*
 * @Author: yang<PERSON><PERSON>
 * @Date: 2023-11-01 10:19:12
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-09 13:33:57
 * @Description: 导出excel
 */
import * as xlsx from "xlsx";
import { Message } from "element-ui";
import { exportFile } from "@/utils/index";

// 字段示例
// const data = [
//   {
//     shortLink: '123',
//     remind: '21'
//   }
// ]
// const fieldMap = {
//   shortLink: '短链接',
//   remind: '提醒'
// }

export const exportExcel = (data, fieldMap, fileName) => {
  const excelData = handleData(data, fieldMap);
  const wb = xlsx.utils.book_new();
  const ws = xlsx.utils.aoa_to_sheet(excelData);
  xlsx.utils.book_append_sheet(wb, ws, "Sheet1");
  xlsx.writeFile(wb, fileName + ".xlsx");
};

const handleData = (data, fieldMap) => {
  const result = [];
  result.push(Object.values(fieldMap));
  data.forEach((item) => {
    const row = [];
    for (const key in fieldMap) {
      row.push(item[key] && item[key].label ? item[key].label : item[key]);
    }
    result.push(row);
  });
  return result;
};

/**
 * @description Excel文件导出
 * @param callback 回调函数
 * @param params 查询参数
 * @param fileName 文件名称
 * @param isDate 是否添加日期
 * @returns {Promise<unknown>}
 */
export const handleExcelExport = (
  callback,
  params = {},
  fileName = "数据",
  isDate = false
) => {
  return new Promise((resolve, reject) => {
    if (typeof callback !== "function") {
      return reject(new Error("callback must be a function"));
    }
    // const loadingInstance = Loading.service({
    //   fullscreen: true,
    //   text: "正在导出数据",
    //   background: "rgba(0, 0, 0, 0.7)",
    // });
    callback(params)
      .then((result) => {
        if (!result) {
          reject(new Error("导出失败，请检查导出内容"));
        } else {
          if (isDate) {
            const formattedDate = new Date().toLocaleString("zh-CN", {
              timeZone: "Asia/Shanghai",
            });
            fileName = `${fileName} ${formattedDate}`;
          }
          exportFile(result, fileName);
          Message.success("导出成功");
          // loadingInstance.close();
          resolve();
        }
      })
      .catch((e) => {
        reject(e);
        // loadingInstance.close();
      });
  });
};
