<!--
 * @Author: wskg
 * @Date: 2025-02-10 10:30:19
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-04 18:26:54
 * @Description: 购机、租赁、融资合约
 -->
<template>
  <div class="app-container">
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="65%"
      :confirm-button-disabled="confirmLoading"
      :method-type="methodType"
      :no-footer="methodType === 'info'"
      confirm-text="确认发货"
      @ok="handleSubmit"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="form"
        :form-list="formColumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '100px' }"
        :open-type="methodType"
        @proSubmit="formSubmit"
      >
        <template #isSupplement>
          <el-radio-group
            v-model="form.isSupplement"
            :disabled="methodType !== 'add'"
            @input="handleIsSupplementChange"
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </template>
        <!-- 客户地址信息 -->
        <template #customerInfo>
          <el-col :span="8" style="padding-left: 0; padding-right: 13px">
            <el-form-item label="收货地址：" prop="addressId">
              <el-select
                v-model="form.addressId"
                style="width: 100%"
                placeholder="请选择收货地址"
                clearable
                :disabled="methodType === 'info'"
                @change="handleAddressChange"
              >
                <el-option
                  v-for="item in addressList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系人：" prop="consignee">
              <el-input
                v-model="form.consignee"
                placeholder="请输入联系人"
                :disabled="methodType === 'info'"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话：" prop="consigneePhone">
              <el-input
                v-model="form.consigneePhone"
                placeholder="请输入联系电话"
                :disabled="methodType === 'info'"
              ></el-input>
            </el-form-item>
          </el-col>
        </template>
        <!-- 合约机器信息 -->
        <template #signMachineInfo>
          <ProTable
            ref="ProTable"
            :show-search="false"
            :show-pagination="false"
            :show-setting="false"
            :show-loading="false"
            :height="350"
            :columns="columns"
            :data="tableData"
          >
            <template #machineNum="{ row }">
              <!--<div-->
              <!--  style="display: flex; justify-content: space-between; gap: 20px"-->
              <!--&gt;-->
              <!--  <el-input v-model="row.machineNum" disabled size="small" />-->
              <!--  <el-button-->
              <!--    v-if="row.deliveryStatus === false"-->
              <!--    type="primary"-->
              <!--    size="mini"-->
              <!--    @click="chooseGoods(row)"-->
              <!--  >-->
              <!--    选择出库机器-->
              <!--  </el-button>-->
              <!--</div>-->
              <div
                :style="{
                  color: row.deliveryStatus ? '#C0C4CC' : '#409eff',
                  cursor: row.deliveryStatus ? 'not-allowed' : 'pointer',
                }"
                @click="row.deliveryStatus ? null : chooseGoods(row)"
              >
                {{ row.machineNum ? row.machineNum : "选择出库机器" }}
                <!--<el-button-->
                <!--  type="text"-->
                <!--  size="mini"-->
                <!--  :disabled="row.deliveryStatus"-->
                <!--  @click="chooseGoods(row)"-->
                <!--&gt;-->
                <!--  {{ row.machineNum ? row.machineNum : "选择出库机器" }}-->
                <!--</el-button>-->
              </div>
            </template>
          </ProTable>
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 选择机器/选配件 -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      :no-footer="true"
      width="80%"
      :top="'2%'"
      @ok="handleDrawerOk"
      @cancel="dialogVisible = false"
    >
      <ProTable
        ref="SendProTable"
        :query-param="queryParam"
        :local-pagination="localPagination"
        :columns="goodsColumns"
        :data="goodsTableData"
        :height="400"
        @loadData="loadData"
      >
        <template #picsUrl="{ row }">
          <el-image
            v-if="row.picsUrl && row.picsUrl.length > 0"
            style="max-width: 100px; max-height: 100px"
            :src="getPicsUrlImg(row)"
            :preview-src-list="[getPicsUrlImg(row)]"
          ></el-image>
        </template>
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button
              icon="el-icon-circle-check"
              @click="selectionCurrRow(row)"
            >
              确认选择
            </el-button>
          </div>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import {
  addCustomerContractApi,
  getCustomerContractApi,
  customerMachineContractSendApi,
} from "@/api/customer";
import {
  getAddressListApi,
  operatorTradeOrderDeliveryApi,
} from "@/api/operator";
import { Message } from "element-ui";
import { filterParam } from "@/utils";
import { getMachinePageApi } from "@/api/store";
import { dictTreeByCodeApi } from "@/api/user";

export default {
  name: "DispatchMachine",
  components: {},
  props: {
    contractType: {
      type: String,
      default: "1201",
    },
  },
  data() {
    return {
      drawerVisible: false,
      drawerTitle: "",
      confirmLoading: false,
      methodType: "add",
      form: {},
      formColumns: [
        {
          dataIndex: "customerName",
          title: "店铺名称",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "customerSeqId",
          title: "客户编号",
          isForm: true,
          formSpan: 8,
          valueType: "text",
        },
        {
          dataIndex: "customerInfo",
          title: "客户信息",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "customerInfo",
        },
        {
          dataIndex: "code",
          title: "合同编号",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入合同编号",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "contractName",
          title: "合同名称",
          isForm: true,
          formSpan: 8,
          valueType: "input",
          prop: [
            {
              required: true,
              message: "请输入合同名称",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "signMachineInfo",
          title: "签约机器信息",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "signMachineInfo",
        },
      ],
      columns: [
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
        },
        {
          dataIndex: "productInfo",
          title: "品牌/型号",
          isTable: true,
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          tableSlot: "machineNum",
          minWidth: 120,
        },
      ],
      tableData: [],
      addressList: [],
      fullAddressList: [],
      sparePartDialog: false,
      // 选择出库机器
      dialogVisible: false,
      dialogTitle: "选择出库机器/选配件",
      queryParam: {},
      defaultQueryParam: {
        status: ["INVENTORY"],
        isSale: true,
      },
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 10,
      },
      goodsColumns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 130,
        },
        {
          dataIndex: "originCode",
          title: "原机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 160,
        },
        // {
        //   dataIndex: "picsUrl",
        //   title: "机器图片",
        //   isTable: true,
        //   tableSlot: "picsUrl",
        //   tooltip: false,
        //   minWidth: 120,
        // },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          minWidth: 120,
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "tagName",
          title: "标签型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isTable: true,
          formatter: (row) => row.percentage?.label,
          isSearch: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(2500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "deviceSequence",
        //   title: "机器序列号",
        //   isTable: true,
        //   minWidth: 120,
        // },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 80,
        },
        {
          dataIndex: "deviceOns",
          title: "设备新旧",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1100),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          minWidth: 80,
        },
        {
          dataIndex: "deviceStatusList",
          title: "设备状态",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6600),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "colorCounter",
          title: "彩色计数器",
          isTable: true,
          minWidth: 100,
        },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          minWidth: 120,
        },
        {
          dataIndex: "createdAt",
          title: "入库时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          width: 140,
          tableSlot: "action",
          fixed: "right",
        },
      ],
      goodsTableData: [],
      currentRow: null,
    };
  },
  mounted() {},
  methods: {
    visible(val, type) {
      this.addressList = [];
      this.fullAddressList = [];
      this.tableData = [];
      this.methodType = type;
      this.drawerTitle = "机器发货";
      getCustomerContractApi(val.id).then((res) => {
        this.form = cloneDeep(res.data);
        this.getCustomerAddressList();
        this.tableData = this.form.customerContractItems || [];
      });

      this.drawerVisible = true;
    },
    handleSubmit() {
      this.$refs.ProForm.handleSubmit();
    },
    async formSubmit(val) {
      try {
        this.confirmLoading = true;
        // 机器编号不存在提醒用户输入机器编号machineNum
        // if (
        //   this.tableData.some((item) => {
        //     return !item.machineNum;
        //   })
        // ) {
        //   this.$message.error("机器编号不能为空");
        //   return;
        // }

        this.$confirm("是否确认发货?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          const data = this.tableData.map((item) => {
            return {
              id: item.id,
              machineNum: item.machineNum,
              hostType: item.hostType.value,
            };
          });
          const args = {
            id: this.form.id,
            contractDeliveryItemDtos: data,
          };
          const result = await customerMachineContractSendApi(args);
          if (result.code === 200) {
            this.$message.success("操作成功");
            this.$emit("refresh");
            this.drawerVisible = false;
          }
        });
      } finally {
        this.confirmLoading = false;
      }
    },
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      requestParameters.status = ["INVENTORY"];
      requestParameters.hostType = this.currentRow?.hostType?.value;
      // requestParameters.productId = this.currentRow?.productId;
      getMachinePageApi(requestParameters)
        .then((res) => {
          this.goodsTableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.SendProTable &&
            (this.$refs.SendProTable.listLoading = false);
        });
    },
    // 选择出库机器或选配件
    chooseGoods(row) {
      this.currentRow = null;
      this.currentRow = row;
      this.dialogVisible = true;
      this.queryParam = {};
      this.$nextTick(() => {
        this.$refs.SendProTable.refresh();
      });
    },
    // 确认发货
    handleDrawerOk() {
      this.$confirm("是否确认发货?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          if (!this.tradeOrderTableData.length) {
            return this.$message.error("暂无可出库机器");
          }
          if (!this.tradeOrderTableData.some((item) => item.machineNum)) {
            return this.$message.error("请选择出库机器编号");
          }
          this.confirmBtnLoading = true;
          const deliveryList = this.tradeOrderTableData
            .filter(
              (item) => item.machineNum && item.orderStatus === "WAIT_DELIVER"
            )
            .map((item) => ({
              id: item.id,
              machineNum: item.machineNum,
              deviceSequence: item.deviceSequence,
            }));
          const args = {
            id: this.orderId,
            machineDeliveryDetailVoList: deliveryList,
          };
          const result = await operatorTradeOrderDeliveryApi(args);
          if (result.code === 200) {
            this.drawerVisible = false;
            this.$emit("refresh");
            this.$message.success("操作成功");
          }
        } finally {
          this.confirmBtnLoading = false;
        }
      });
    },
    selectionCurrRow(row) {
      this.$set(this.currentRow, "machineNum", row.machineNum);
      this.dialogVisible = false;
    },
    async getCustomerAddressList() {
      // 获取用户可用地址
      try {
        if (this.form.customerId) {
          const res = await getAddressListApi(this.form.customerId);
          this.fullAddressList = res.data;
          res.data.map((item) => {
            this.addressList.push({
              value: item.id,
              label: item.address,
            });
          });
        }
      } catch (e) {
        Message.error(e.message);
      }
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    handleAddressChange(val) {
      const addressInfo = this.fullAddressList.find((item) => item.id === val);
      if (addressInfo) {
        this.$set(this.form, "consignee", addressInfo.contact);
        this.$set(this.form, "consigneePhone", addressInfo.phone);
      }
    },
    handleIsSupplementChange() {
      this.tableData = [];
    },
    closeDrawer() {
      this.drawerVisible = false;
      this.$nextTick(() => {
        this.form = {};
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
