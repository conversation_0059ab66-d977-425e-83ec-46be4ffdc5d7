# 配置分发关系表 - Web端修改计划指导

## 概述

本指导文档针对Web管理端适配后端配置分发关系表的改造，重点关注API接口调用变化和业务逻辑调整。

## 后端接口变化对应的前端调整

### 1. 核心API接口变化

#### 1.1 批量分配接口调整

**接口：** `POST /logcontrol/config/assign-batch`

**请求参数保持兼容：**
```javascript
// 现有请求格式（完全兼容）
{
  "configSource": "123",         // 配置ID字符串
  "sourceType": "CONFIG_ID",     // 配置来源类型：CONFIG_ID 或 TEMPLATE
  "targets": [
    {
      "targetType": "USER",      // USER, DEVICE, GROUP
      "targetId": "user001",
      "targetName": "张三"
    },
    {
      "targetType": "DEVICE",
      "targetId": "device001",
      "targetName": "测试设备1"
    }
  ],
  "overrideExisting": false      // 是否覆盖已有配置
}

// 可选增强参数（向后兼容）
{
  "configSource": "123",
  "sourceType": "CONFIG_ID",
  "targets": [...],
  "overrideExisting": false,
  "priority": 100                // 新增：优先级设置（可选）
}
```

**响应格式变化：**
```javascript
// 原响应
{
  "success": true,
  "data": {
    "total": 10,
    "success": 8,
    "failed": 2,
    "details": [...]
  }
}

// 新响应（增加分发关系ID）
{
  "success": true,
  "data": {
    "total": 10,
    "success": 8,
    "failed": 2,
    "details": [
      {
        "targetType": "DEVICE",
        "targetId": "device123",
        "targetName": "测试设备",
        "success": true,
        "message": "分配成功",
        "distributionId": 12345     // 新增：分发关系ID
      }
    ]
  }
}
```

#### 1.2 现有分配接口保持不变

**单个用户分配：** `POST /logcontrol/config/assign-to-user?userId={用户ID}&configId={配置ID}`
**单个设备分配：** `POST /logcontrol/config/assign-to-device?deviceId={设备ID}&configId={配置ID}`

这些接口在后端实现上会从创建专属配置改为创建分发关系，但前端调用方式完全不变。

#### 1.3 分配查询接口数据源变化

**接口：** `GET /logcontrol/config/assignments?targetType={目标类型}&keyword={关键词}`

**重要变化：数据来源从配置命名规则改为分发关系表**

```javascript
// API调用方式保持不变
const getConfigAssignments = async (targetType, keyword) => {
  const response = await request({
    url: '/logcontrol/config/assignments',
    method: 'get',
    params: {
      targetType: targetType,  // USER, DEVICE, GROUP 或空
      keyword: keyword         // 搜索关键词
    }
  });

  return response.data;
};

// 响应格式（基于 b_config_distribution 表，状态通过版本比较计算）
// [
//   {
//     "distributionId": 12345,              // 分发关系唯一ID
//     "configId": 1,                        // 关联的配置ID
//     "targetType": "DEVICE",               // 目标类型
//     "targetId": "device001",              // 目标ID
//     "targetName": "测试设备1",             // 目标名称
//     "assignTime": "2025-08-01T10:30:00",  // 分配时间
//     "priority": 100,                      // 优先级
//
//     // 关联的配置信息
//     "configName": "production_config",    // 配置名称
//     "assignedVersion": "1.0.5",           // 分配的配置版本
//     "currentVersion": "1.0.3",            // 设备当前版本
//     "distributionStatus": "ASSIGNED"      // 通过版本比较计算：PENDING/ASSIGNED/APPLIED
//   }
// ]
```

#### 1.4 新增配置变更检测接口

**新接口：** `GET /logcontrol/config/check-updates`

```javascript
// 轻量级配置变更检测
const checkConfigUpdates = async (deviceId, userId, currentVersion) => {
  const response = await request({
    url: '/logcontrol/config/check-updates',
    method: 'get',
    headers: {
      'X-Device-Id': deviceId,
      'X-User-Id': userId
    },
    params: {
      currentVersion: currentVersion
    }
  });

  return response.data;
  // 响应格式：
  // {
  //   "hasUpdate": true,
  //   "latestVersion": "1.0.5",           // 分配的最新版本
  //   "currentVersion": "1.0.3",          // 设备当前版本
  //   "configSource": "USER_SPECIFIC",
  //   "assignTime": "2025-08-01T10:30:00"
  // }
};
```

#### 1.5 新增分发管理接口

**移除分发关系：** `DELETE /logcontrol/config/assignment/{targetType}/{targetId}`

```javascript
// 移除特定目标的配置分配
const removeConfigAssignment = async (targetType, targetId) => {
  const response = await request({
    url: `/logcontrol/config/assignment/${targetType}/${targetId}`,
    method: 'delete'
  });

  return response;
};
```

**新增配置应用确认接口：** `POST /logcontrol/config/confirm-applied`

```javascript
// 确认配置已应用（主要供客户端调用）
const confirmConfigApplied = async (deviceId, configVersion) => {
  const response = await request({
    url: '/logcontrol/config/confirm-applied',
    method: 'post',
    headers: {
      'X-Device-Id': deviceId
    },
    params: {
      configVersion: configVersion,
      appliedTime: new Date().toISOString()
    }
  });

  return response;
};
```

### 2. 业务逻辑调整

#### 2.1 配置分配逻辑变化

**原逻辑：** 创建新的配置记录（如 `user_123`, `device_456`）
**新逻辑：** 创建分发关系记录，复用现有配置

```javascript
// 前端调用方式完全不变
const assignConfigToUser = async (userId, configId) => {
  // 调用方式保持不变
  const response = await request({
    url: `/logcontrol/config/assign-to-user?userId=${userId}&configId=${configId}`,
    method: 'post'
  });

  return response;
};

// 批量分配调用方式也保持不变
const batchAssignConfig = async (configId, targets) => {
  const response = await request({
    url: '/logcontrol/config/assign-batch',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      configSource: configId.toString(),
      sourceType: 'CONFIG_ID',
      targets: targets,
      overrideExisting: false
    })
  });

  return response;
};
```

#### 2.2 配置分发状态查询逻辑

```javascript
// 查询特定配置的分发状态（基于分发关系表数据）
const getConfigDistributionStatus = async (configId) => {
  // 获取所有分发关系
  const assignments = await getConfigAssignments();

  // 过滤出特定配置的分发情况
  const configAssignments = assignments.filter(item => item.configId === configId);

  // 统计分发状态（基于版本比较计算的状态）
  const statistics = {
    total: configAssignments.length,
    assigned: configAssignments.filter(item => item.distributionStatus === 'ASSIGNED').length,
    applied: configAssignments.filter(item => item.distributionStatus === 'APPLIED').length,
    pending: configAssignments.filter(item => item.distributionStatus === 'PENDING').length
  };

  // 按目标类型分组统计
  const targetTypeStats = configAssignments.reduce((acc, item) => {
    if (!acc[item.targetType]) {
      acc[item.targetType] = { total: 0, applied: 0, assigned: 0, pending: 0 };
    }
    acc[item.targetType].total++;
    if (item.distributionStatus === 'APPLIED') {
      acc[item.targetType].applied++;
    } else if (item.distributionStatus === 'ASSIGNED') {
      acc[item.targetType].assigned++;
    } else if (item.distributionStatus === 'PENDING') {
      acc[item.targetType].pending++;
    }
    return acc;
  }, {});

  return {
    assignments: configAssignments,
    statistics: statistics,
    targetTypeStats: targetTypeStats
  };
};

// 查询特定目标的配置分发情况
const getTargetDistributions = async (targetType, targetId) => {
  const assignments = await getConfigAssignments(targetType, targetId);

  // 按优先级排序，获取生效的配置
  const sortedAssignments = assignments
    .filter(item => item.targetType === targetType && item.targetId === targetId)
    .sort((a, b) => (a.priority || 100) - (b.priority || 100));

  return {
    activeConfig: sortedAssignments[0] || null,  // 最高优先级的配置
    allAssignments: sortedAssignments
  };
};
```

### 3. 数据结构适配

#### 3.1 配置列表数据结构

```javascript
// 现有配置列表项（基于接口文档）
{
  "id": 1,
  "configName": "default_config",
  "logLevel": "INFO",
  "enableLocationLog": true,
  "locationLogInterval": 30000,
  "logUploadInterval": 60000,
  "maxLogFiles": 10,
  "configVersion": "1.0.1",
  "isActive": true
}

// 增强后的配置列表项（可选增加分发统计）
{
  "id": 1,
  "configName": "default_config",
  "logLevel": "INFO",
  "enableLocationLog": true,
  "locationLogInterval": 30000,
  "logUploadInterval": 60000,
  "maxLogFiles": 10,
  "configVersion": "1.0.1",
  "isActive": true,
  "distributionInfo": {           // 可选：分发统计信息（基于版本比较计算）
    "totalDistributions": 15,
    "appliedCount": 12,           // 设备当前版本 = 配置版本
    "assignedCount": 2,           // 设备当前版本 ≠ 配置版本
    "pendingCount": 1             // 设备当前版本为空
  }
}
```

#### 3.2 分发目标数据结构（与接口文档一致）

```javascript
// 批量分配的目标结构（完全兼容现有格式）
const batchAssignRequest = {
  "configSource": "123",         // 配置ID字符串
  "sourceType": "CONFIG_ID",     // CONFIG_ID 或 TEMPLATE
  "overrideExisting": false,
  "targets": [
    {
      "targetType": "USER",      // USER, DEVICE, GROUP
      "targetId": "user001",     // 目标唯一标识
      "targetName": "张三"        // 显示名称
    },
    {
      "targetType": "DEVICE",
      "targetId": "device001",
      "targetName": "测试设备1"
    }
  ]
};
```

### 4. 分发关系管理功能

#### 4.1 分发关系操作

```javascript
// 重新分发配置
const retryDistribution = async (distributionId) => {
  try {
    const response = await request({
      url: `/logcontrol/config/retry-distribution/${distributionId}`,
      method: 'post'
    });

    if (response.success) {
      message.success('重新分发成功');
      // 刷新分发状态
      refreshDistributionList();
    }
  } catch (error) {
    message.error('重新分发失败');
  }
};

// 移除分发关系
const removeDistribution = async (distributionId) => {
  try {
    const response = await request({
      url: `/logcontrol/config/remove-distribution/${distributionId}`,
      method: 'delete'
    });

    if (response.success) {
      message.success('移除分发关系成功');
      refreshDistributionList();
    }
  } catch (error) {
    message.error('移除分发关系失败');
  }
};

// 批量操作分发关系
const batchOperateDistributions = async (distributionIds, operation) => {
  const operations = {
    'retry': '重新分发',
    'remove': '移除',
    'setPriority': '设置优先级'
  };

  try {
    const response = await request({
      url: '/logcontrol/config/batch-operate-distributions',
      method: 'post',
      data: {
        distributionIds: distributionIds,
        operation: operation
      }
    });

    if (response.success) {
      message.success(`批量${operations[operation]}成功`);
      refreshDistributionList();
    }
  } catch (error) {
    message.error(`批量${operations[operation]}失败`);
  }
};
```

#### 4.2 分发状态实时监控

```javascript
// 分发状态监控Hook
const useDistributionMonitor = (configId) => {
  const [distributions, setDistributions] = useState([]);
  const [statistics, setStatistics] = useState({});
  const [loading, setLoading] = useState(false);

  const refreshDistributions = useCallback(async () => {
    setLoading(true);
    try {
      const result = await getConfigDistributionStatus(configId);
      setDistributions(result.assignments);
      setStatistics(result.statistics);
    } catch (error) {
      console.error('获取分发状态失败:', error);
    } finally {
      setLoading(false);
    }
  }, [configId]);

  // 定时刷新分发状态
  useEffect(() => {
    refreshDistributions();
    const interval = setInterval(refreshDistributions, 30000); // 30秒刷新一次
    return () => clearInterval(interval);
  }, [refreshDistributions]);

  // 监听分发状态变化
  const handleDistributionChange = useCallback((distributionId, newStatus) => {
    setDistributions(prev => prev.map(item =>
      item.distributionId === distributionId
        ? { ...item, distributionStatus: newStatus, applyTime: new Date().toISOString() }
        : item
    ));
  }, []);

  return {
    distributions,
    statistics,
    loading,
    refreshDistributions,
    handleDistributionChange
  };
};
```

### 3.3 模板管理数据结构（保持不变）

```javascript
// 配置模板结构（与接口文档一致）
const configTemplate = {
  "templateName": "development",
  "displayName": "开发环境",
  "logLevel": "DEBUG",
  "enableLocationLog": true,
  "locationLogInterval": 10000,
  "logUploadInterval": 30000,
  "maxLogFiles": 50,
  "description": "开发环境配置模板"
};

// 从模板创建配置的请求结构
const createFromTemplateRequest = {
  "templateName": "development",
  "configName": "dev_config_001",
  "customizations": {
    "logLevel": "INFO",
    "maxLogFiles": 30
  }
};
```

## 关键调整点总结

### 1. 数据来源根本性变化
- **查询逻辑**：从解析配置命名规则改为查询分发关系表
- **数据完整性**：获得真实的分发关系数据，而非推断数据
- **关系明确**：一个配置可以对应多个分发关系记录
- **状态精确**：提供准确的分发生命周期状态跟踪

### 2. API接口调用兼容性
- **调用方式**：所有现有API调用方式保持不变
- **响应结构**：分配查询接口响应结构完全重构
- **新增接口**：添加配置变更检测和应用确认接口
- **参数扩展**：批量分配接口可选增加优先级参数

### 3. 业务逻辑增强
- **分配逻辑**：从创建专属配置改为创建分发关系
- **多配置支持**：同一目标可以有多个配置，按优先级生效
- **状态跟踪**：完整的分发生命周期管理（分配→检查→应用）
- **关系管理**：支持分发关系的增删改查操作

### 4. 前端开发重点
- **数据适配**：适配分发关系表的数据结构
- **状态展示**：丰富的分发状态可视化展示
- **关系管理**：分发关系的操作和管理功能
- **实时监控**：分发状态的实时更新和监控

## 实施建议

### 第一阶段：数据结构适配
1. **分发关系数据适配**：适配基于分发关系表的新数据结构
2. **表格列调整**：更新分配查询页面的表格列定义
3. **状态映射处理**：处理新的分发状态枚举值
4. **向后兼容处理**：确保在后端未升级时能正常工作

### 第二阶段：功能增强
1. **分发状态监控**：实现分发状态的实时监控和更新
2. **分发关系管理**：添加重新分发、移除分发等操作
3. **优先级管理**：支持分发优先级的设置和调整
4. **批量操作增强**：支持分发关系的批量操作

### 第三阶段：高级功能
1. **分发效果分析**：提供详细的分发统计和分析
2. **配置变更检测**：集成轻量级配置变更检测
3. **分发历史追踪**：完整的分发历史记录和审计
4. **智能分发建议**：基于历史数据的分发建议

## 注意事项

1. **数据结构变化**：分配查询接口的响应结构发生根本性变化，需要充分测试
2. **状态枚举处理**：新增的分发状态枚举值需要完整的映射和处理
3. **分发关系ID**：新增的distributionId字段是分发关系的唯一标识，用于后续操作
4. **优先级逻辑**：需要理解和正确处理多配置场景下的优先级逻辑
5. **向后兼容**：在后端未完全升级时，需要处理字段缺失的情况
6. **性能考虑**：分发关系表可能包含大量数据，注意分页和性能优化
7. **实时性要求**：分发状态的实时性对用户体验很重要，需要合理的刷新策略

## 数据迁移考虑

在系统升级过程中，需要考虑：

1. **历史数据处理**：现有的专属配置（如user_xxx, device_xxx）需要迁移到分发关系表
2. **数据一致性**：确保迁移过程中数据的一致性和完整性
3. **回滚方案**：如果升级失败，需要能够回滚到原有的数据结构
4. **渐进升级**：支持新旧系统并存的过渡期
