import React, { useEffect, useRef, useState } from 'react';
import { Spin, Button, Modal } from 'antd';
import { EnvironmentOutlined } from '@ant-design/icons';
import { useTencentMapKey } from '../../hooks/useWebsiteApi';
import { isMobileDevice } from '../../hooks/useDevice';

interface MapPreviewProps {
  /** 用于定位的地址 */
  address: string;
  /** 地图容器的高度，默认 320px */
  height?: number | string;
  /** 是否显示导航按钮（主要用于移动端） */
  showNavigateButton?: boolean;
  className?: string;
}

/** 腾讯地图脚本加载 Promise，确保全局仅加载一次 */
let tMapScriptLoading: Promise<void> | null = null;

/**
 * 动态加载腾讯地图 JS SDK
 */
function loadTencentMapScript(mapKey: string): Promise<void> {
  if (typeof window !== 'undefined' && (window as any).TMap) {
    return Promise.resolve();
  }
  if (!tMapScriptLoading) {
    tMapScriptLoading = new Promise((resolve, reject) => {
      const script = document.createElement('script');
      // 如果没有配置key，直接抛出错误
      if (!mapKey) {
        reject(new Error('腾讯地图API Key未配置，请在后台配置页面设置'));
        return;
      }
      script.src = `https://map.qq.com/api/gljs?v=1.exp&libraries=service&key=${mapKey}`;
      script.async = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('腾讯地图脚本加载失败'));
      document.head.appendChild(script);
    });
  }
  return tMapScriptLoading;
}

/**
 * 添加地图层级样式，确保导航栏在地图控件之上
 */
function addMapZIndexStyles() {
  const styleId = 'tmap-zindex-fix';
  if (document.getElementById(styleId)) return;

  const style = document.createElement('style');
  style.id = styleId;
  style.textContent = `
    /* 确保移动端导航栏在地图控件之上 */
    .mobile-nav-bar-container {
      z-index: 9999 !important;
    }

    /* 地图控件层级调整 */
    .tmap-container .tmap-control {
      z-index: 100 !important;
    }

    /* 地图信息窗口层级 */
    .tmap-container .tmap-infowindow {
      z-index: 200 !important;
    }

    /* 确保地图容器本身层级较低 */
    .tmap-container {
      z-index: 1 !important;
    }
  `;
  document.head.appendChild(style);
}

const MapPreview: React.FC<MapPreviewProps> = ({
  address,
  height = 320,
  showNavigateButton = true,
  className = ''
}) => {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [coords, setCoords] = useState<{ lat: number; lng: number } | null>(null);
  const [showNavModal, setShowNavModal] = useState<boolean>(false);

  // 获取腾讯地图key
  const { data: tencentMapKey, isLoading: keyLoading, error: keyError } = useTencentMapKey();

  // 在组件挂载时添加CSS样式
  useEffect(() => {
    addMapZIndexStyles();
  }, []);

  useEffect(() => {
    let mapInstance: any;
    let markerLayer: any;

    /** 根据地址定位并渲染地图 */
    const initMap = async () => {
      try {
        // 等待key加载完成
        if (keyLoading) {
          return;
        }

        if (keyError) {
          throw new Error(`腾讯地图Key获取失败: ${keyError}`);
        }

        // 检查是否有配置key
        if (!tencentMapKey || tencentMapKey.trim() === '') {
          throw new Error('腾讯地图API Key未配置，请联系管理员在后台配置页面设置');
        }

        await loadTencentMapScript(tencentMapKey);
        const { TMap } = window as any;
        if (!TMap) throw new Error('腾讯地图脚本未正确加载');

        const geocoder = new TMap.service.Geocoder();
        const geocodeResult = await geocoder.getLocation({ address });
        const { location } = geocodeResult.result;
        const center = new TMap.LatLng(location.lat, location.lng);
        setCoords({ lat: location.lat, lng: location.lng });

        // 创建地图实例，优化触摸交互设置
        mapInstance = new TMap.Map(mapContainerRef.current!, {
          center,
          zoom: 14,
          // 控件配置 - 保持基本控件但调整位置
          control: {
            zoom: {
              position: TMap.constants.BOTTOM_RIGHT,
              // 确保控件可用但不会过度遮挡
              style: { zIndex: 100 }
            },
            scale: {
              position: TMap.constants.BOTTOM_LEFT,
              style: { zIndex: 100 }
            },
            // 移除可能影响触摸的控件
            rotation: false,
            fullscreen: false
          },
          // 优化地图交互设置
          interaction: {
            // 允许拖拽
            draggable: true,
            // 允许缩放
            zoomable: true,
            // 允许双击缩放
            doubleClickZoom: true,
            // 允许滚轮缩放
            scrollZoom: true
          }
        });

        // 在中心点放置标记
        markerLayer = new TMap.MultiMarker({
          id: 'marker-layer',
          map: mapInstance,
          geometries: [
            {
              id: 'center-marker',
              position: center,
            },
          ],
        });

        // 确保地图容器有正确的事件处理
        if (mapContainerRef.current) {
          mapContainerRef.current.style.touchAction = 'manipulation';
          (mapContainerRef.current.style as any).webkitTouchCallout = 'none';
        }

      } catch (err) {
        // 失败时显示占位符
        showMapPlaceholder();
      } finally {
        setLoading(false);
      }
    };

    initMap();

    // 清理函数，销毁地图实例，避免内存泄漏
    return () => {
      if (markerLayer) {
        markerLayer.setMap(null);
      }
      if (mapInstance) {
        mapInstance.destroy();
      }
    };
  }, [address, tencentMapKey, keyLoading, keyError]);

  // 显示地图占位符
  const showMapPlaceholder = () => {
    if (mapContainerRef.current) {
      mapContainerRef.current.innerHTML = `
        <div style="display:flex;flex-direction:column;justify-content:center;align-items:center;height:100%;background:linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
          <div style="text-align:center;">
            <div style="font-size:48px;color:#2196f3;margin-bottom:16px;">📍</div>
            <p style="color:#666;margin-bottom:16px;">地图预览</p>
            <div style="background:rgba(0,0,0,0.5);color:white;padding:8px 16px;border-radius:4px;font-size:14px;">
              ${address}
            </div>
          </div>
        </div>
      `;
    }
  };

  // 导航应用配置
  const getNavigationApps = () => {
    const encodedAddress = encodeURIComponent(address);
    const { lat, lng } = coords || {};

    const apps = [
      {
        name: '百度地图',
        icon: '🗺️',
        url: coords
          ? `baidumap://map/navi?location=${lat},${lng}&query=${encodedAddress}&src=benyin`
          : `baidumap://map/place/search?query=${encodedAddress}`,
        webUrl: `https://map.baidu.com/search/?querytype=s&wd=${encodedAddress}`,
        color: '#4285f4'
      },
      {
        name: '高德地图',
        icon: '🧭',
        url: coords
          ? `androidamap://navi?sourceApplication=benyin&lat=${lat}&lon=${lng}&dev=0&style=2`
          : `androidamap://poi?sourceApplication=benyin&keywords=${encodedAddress}&dev=0`,
        webUrl: `https://www.amap.com/search?query=${encodedAddress}`,
        color: '#00a870'
      },
      {
        name: '腾讯地图',
        icon: '📍',
        url: coords
          ? `qqmap://map/routeplan?type=drive&to=${encodedAddress}&tocoord=${lat},${lng}&policy=0&referer=benyin`
          : `qqmap://map/search?keyword=${encodedAddress}&referer=benyin`,
        webUrl: `https://map.qq.com/m/search/${encodedAddress}`,
        color: '#ff6900'
      }
    ];

    // iOS设备添加Apple Maps
    const userAgent = navigator.userAgent;
    if (/iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream) {
      apps.unshift({
        name: 'Apple 地图',
        icon: '🍎',
        url: coords
          ? `http://maps.apple.com/?daddr=${lat},${lng}&dirflg=d`
          : `http://maps.apple.com/?q=${encodedAddress}`,
        webUrl: coords
          ? `http://maps.apple.com/?daddr=${lat},${lng}&dirflg=d`
          : `http://maps.apple.com/?q=${encodedAddress}`,
        color: '#007aff'
      });
    }

    return apps;
  };

  const handleNavigate = () => {
    if (!address) {
      return;
    }

    // 检测是否为移动设备
    const isMobile = isMobileDevice();

    if (isMobile) {
      // 移动端显示选择弹窗
      setShowNavModal(true);
    } else {
      // 桌面端直接使用百度地图
      const encodedAddress = encodeURIComponent(address);
      const baiduUrl = `https://map.baidu.com/search/?querytype=s&wd=${encodedAddress}`;
      window.open(baiduUrl, '_blank');
    }
  };

  const handleAppSelect = (app: any) => {
    setShowNavModal(false);

    try {
      // 尝试打开原生应用
      const link = document.createElement('a');
      link.href = app.url;
      link.style.display = 'none';
      document.body.appendChild(link);

      // 监听页面可见性变化，判断是否成功打开了原生应用
      let appOpened = false;
      const handleVisibilityChange = () => {
        if (document.hidden) {
          appOpened = true;
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      link.click();
      document.body.removeChild(link);

      // 延迟检查是否成功打开原生应用，如果没有则打开网页版
      setTimeout(() => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        if (!appOpened) {
          window.open(app.webUrl, '_blank');
        }
      }, 2000);

    } catch (error) {
      console.warn('打开导航应用失败，使用网页版:', error);
      window.open(app.webUrl, '_blank');
    }
  };

  return (
    <div
      style={{
        width: '100%',
        height,
        position: 'relative',
        // 确保容器支持触摸交互
        touchAction: 'manipulation',
        WebkitTouchCallout: 'none',
        WebkitUserSelect: 'none',
        userSelect: 'none'
      }}
      className={`tmap-container ${className}`}
    >
      {loading && (
        <div className="flex items-center justify-center h-full absolute inset-0 z-10 bg-white/50">
          <Spin />
        </div>
      )}
      <div
        ref={mapContainerRef}
        style={{
          width: '100%',
          height: '100%',
          // 明确允许触摸事件
          pointerEvents: 'auto',
          touchAction: 'manipulation'
        }}
        className="relative"
      />
      {showNavigateButton && (
        <Button
          type="primary"
          icon={<EnvironmentOutlined />}
          onClick={handleNavigate}
          className="absolute bottom-4 right-4"
          style={{
            zIndex: 1000,
            pointerEvents: 'auto'
          }}
          disabled={!address}
        >
          导航
        </Button>
      )}

      {/* 导航应用选择弹窗 */}
      <Modal
        title="选择导航应用"
        open={showNavModal}
        onCancel={() => setShowNavModal(false)}
        footer={null}
        centered
        className="navigation-modal"
      >
        <div className="space-y-3">
          <p className="text-gray-600 text-sm mb-4">
            选择您要使用的导航应用：
          </p>
          {getNavigationApps().map((app, index) => (
            <button
              key={index}
              onClick={() => handleAppSelect(app)}
              className="w-full flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all group"
            >
              <div
                className="w-12 h-12 rounded-lg flex items-center justify-center mr-4 text-white text-xl"
                style={{ backgroundColor: app.color }}
              >
                {app.icon}
              </div>
              <div className="text-left flex-1">
                <h4 className="font-medium text-gray-900 group-hover:text-blue-600">
                  {app.name}
                </h4>
                <p className="text-sm text-gray-500">
                  点击打开{app.name}进行导航
                </p>
              </div>
            </button>
          ))}
        </div>
      </Modal>
    </div>
  );
};

export default MapPreview;
