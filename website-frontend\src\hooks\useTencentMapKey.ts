import { useState, useEffect } from 'react';
import { websiteConfigApi } from '@/services';
import { decryptConfigValue } from '@/utils/encryption';

/**
 * 安全的腾讯地图Key获取Hook
 * 从后端获取密文，在前端解密使用，避免明文暴露在开发者工具中
 */
export function useTencentMapKey() {
  const [mapKey, setMapKey] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMapKey = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // 从安全接口获取密文
        const encryptedKey = await websiteConfigApi.getSensitiveConfigEncrypted('tencentMapKey');
        
        if (encryptedKey) {
          // 在前端解密
          const decryptedKey = decryptConfigValue(encryptedKey);
          setMapKey(decryptedKey);
        } else {
          // 未能获取到腾讯地图Key密文
          setMapKey('');
        }
      } catch (err: any) {
        setError(err.message || '获取地图Key失败');
        setMapKey('');
      } finally {
        setLoading(false);
      }
    };

    fetchMapKey();
  }, []);

  return {
    mapKey,
    loading,
    error,
    refetch: () => {
      const fetchMapKey = async () => {
        try {
          setLoading(true);
          setError(null);
          
          const encryptedKey = await websiteConfigApi.getSensitiveConfigEncrypted('tencentMapKey');
          
          if (encryptedKey) {
            const decryptedKey = decryptConfigValue(encryptedKey);
            setMapKey(decryptedKey);
          } else {
            setMapKey('');
          }
        } catch (err: any) {
          setError(err.message || '获取地图Key失败');
          setMapKey('');
        } finally {
          setLoading(false);
        }
      };

      fetchMapKey();
    }
  };
} 