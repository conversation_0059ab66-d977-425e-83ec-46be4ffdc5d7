<!--
 * @Author: wskg
 * @Date: 2024-08-13 09:55:51
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-23 17:56:47
 * @Description: 收货单
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :columns="columns"
      :data="tableData"
      :local-pagination="localPagination"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'info')"
          >
            详情
          </el-button>
          <el-button
            v-if="row.status?.value !== 'SUCCESS'"
            type="btn3"
            icon="el-icon-warning-outline"
            @click="handleEdit(row, 'edit')"
          >
            收货
          </el-button>
          <!--          <el-button-->
          <!--            size="mini"-->
          <!--            icon="el-icon-circle-check"-->
          <!--            @click="handleReturn(row)"-->
          <!--            >退货</el-button-->
          <!--          >-->

          <!--          <el-button type="danger" icon="el-icon-delete" @click="closeOrder"-->
          <!--            >关闭发货单</el-button-->
          <!--          >-->
        </div>
      </template>
    </ProTable>
    <!--  新增、详情、编辑  -->
    <ProDrawer
      :value="receivingDrawer"
      :title="receivingTitle"
      :confirm-loading="confirmLoading"
      :no-footer="methodType === 'info'"
      :no-confirm-footer="true"
      size="80%"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :form-param="formParam"
        :form-list="formColumns"
        :confirm-loading="formLoading"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :open-type="methodType"
      >
        <!--  @handleSelectionChange="handleSelectionChange"-->
        <template #shippingList>
          <div class="title-box">发货清单</div>
          <ProTable
            :columns="shippingColumns"
            :data="shippingTableData"
            :show-setting="false"
            :show-search="false"
            :show-loading="false"
            :show-index="false"
            :height="400"
            sticky
          >
            <template #packageImg="{ row }">
              <div
                class="upload"
                style="height: 150px; display: flex; align-items: center"
              >
                <ProUpload
                  class="drag"
                  :file-list="row.picUrls"
                  :type="methodType"
                  :limit="3"
                  :drag="true"
                  @uploadSuccess="(e) => handleLicenseImgUploadSuccess(e, row)"
                  @uploadRemove="(e) => handleLicenseImgUploadRemove(e, row)"
                />
              </div>
            </template>
            <template #packageNum="{ row }">
              <el-input-number
                v-model="row.packageNum"
                :disabled="
                  methodType === 'info' || row.status?.value === 'SUCCESS'
                "
                :min="1"
                size="small"
                controls-position="right"
                style="width: 100%"
              ></el-input-number>
            </template>
            <template #currNum="{ row }">
              <el-input-number
                v-model="row.currNum"
                :disabled="
                  methodType === 'info' || row.status?.value === 'SUCCESS'
                "
                size="small"
                :min="0"
                controls-position="right"
                style="width: 100%"
                @change="(e) => handleCurrNumChange(e, row)"
              ></el-input-number>
            </template>
            <template #remark="{ row }">
              <el-input
                v-model="row.remark"
                :disabled="
                  methodType === 'info' || row.status?.value === 'SUCCESS'
                "
                type="textarea"
                :rows="2"
                maxlength="500"
                show-word-limit
                placeholder="请输入备注内容"
              ></el-input>
            </template>
          </ProTable>
        </template>
      </ProForm>
      <template #footer>
        <el-button
          :loading="btnLoading"
          type="primary"
          @click="confirmReceiving"
        >
          确认收货
        </el-button>
        <el-button @click="closeDrawer">关闭</el-button>
      </template>
    </ProDrawer>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import ProUpload from "@/components/ProUpload/index.vue";
import {
  confirmReceiveApi,
  getReceiveDetailApi,
  pageReceiveApi,
} from "@/api/manufacturer";
import { Message } from "element-ui";
export default {
  name: "Receiving",
  components: { ProUpload },
  data() {
    return {
      methodType: "add",
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          dataIndex: "manufacturerDeliveryCode",
          title: "供应商发货单号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "manufacturerDeliveryCode",
          title: "发货单号",
          isSearch: true,
          placeholder: "供应商发货单号",
          valueType: "input",
        },
        {
          dataIndex: "trackingNumber",
          title: "物流单号",
          isTable: true,
          isSearch: true,
          width: 160,
          valueType: "input",
        },
        {
          dataIndex: "purchaseCode",
          title: "采购单号",
          isTable: true,
          isSearch: true,
          width: 180,
          valueType: "input",
        },
        {
          dataIndex: "trackingType",
          title: "配送方式",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "warehouseName",
          title: "收货仓库",
          isTable: true,
          isSearch: true,
          valueType: "input",
          width: 120,
        },
        {
          dataIndex: "packageNum",
          title: "件数",
          isTable: true,
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "receiveByName",
          title: "收货人",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "status",
          title: "收货单状态",
          isTable: true,
          isSearch: true,
          clearable: true,
          formatter: (row) => row.status?.label,
          valueType: "select",
          option: [
            {
              label: "待收货",
              value: "WAIT_RECEIVE",
            },
            {
              label: "部分收货",
              value: "PART_RECEIVE",
            },
            {
              label: "已收货",
              value: "SUCCESS",
            },
          ],
        },
        {
          dataIndex: "updatedAt",
          title: "收货时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
          width: 160,
        },
      ],
      tableData: [],
      receivingDrawer: false,
      confirmLoading: false,
      receivingTitle: "收货单详情",
      formColumns: [
        {
          dataIndex: "purchaseCode",
          title: "采购单号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "trackingNumber",
          title: "快递单号",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "warehouseName",
          title: "收货仓库",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "initiatorName",
          title: "采购人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "contact",
          title: "收件人",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },

        {
          dataIndex: "contactPhone",
          title: "收件人电话",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "purchaseDate",
          title: "采购时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "receivingAt",
          title: "收货时间",
          isForm: true,
          formSpan: 6,
          valueType: "text",
        },
        {
          dataIndex: "shippingFee",
          title: "运费",
          isForm: true,
          formSpan: 6,
          valueType: "input",
          min: 0,
          prop: [
            {
              required: true,
              message: "请输入运费",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "shippingList",
          title: "发货清单",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "shippingList",
        },
        // {
        //   dataIndex: "remarks",
        //   title: "备注",
        //   isForm: true,
        //   formSpan: 24,
        //   valueType: "input",
        //   inputType: "textarea",
        //   autosize: { minRows: 4, maxRows: 8 },
        // },
      ],
      formParam: {},
      formLoading: false,
      shippingColumns: [
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "articleCode",
          title: "物品编码",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          width: 120,
        },
        {
          dataIndex: "packageNum",
          title: "包裹数量",
          isTable: true,

          width: 100,
          tableSlot: "packageNum",
        },
        {
          dataIndex: "number",
          title: "发货数量",
          isTable: true,
          align: "center",
          width: 80,
        },
        {
          dataIndex: "receiveNum",
          title: "已收数量",
          isTable: true,
          align: "center",
          width: 80,
        },
        {
          dataIndex: "currNum",
          title: "收货数量",
          isTable: true,
          width: 100,
          tableSlot: "currNum",
        },
        {
          dataIndex: "status",
          title: "收货状态",
          isTable: true,
          formatter: (row) => row.status?.label,
        },
        {
          dataIndex: "picUrls",
          title: "包裹照片",
          isTable: true,
          align: "center",
          width: 500,
          tableSlot: "packageImg",
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
          width: 200,
          tableSlot: "remark",
        },
      ],
      shippingTableData: [],
      selectReceiveData: [],
      btnLoading: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRang = [
        {
          receiveTimeStart: null,
          receiveTimeEnd: null,
          data: parameter.updatedAt,
        },
      ];
      filterParamRange(this, this.queryParam, searchRang);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.updatedAt;
      pageReceiveApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = Number(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        });
    },
    handleSelectionChange(val) {
      this.selectReceiveData = val;
    },
    // 详情、收货
    handleEdit(row, type) {
      this.formParam = {};
      if (type === "info") {
        this.shippingColumns.forEach((item, index) => {
          if (item.dataIndex === "currNum") {
            this.shippingColumns[index].isTable = false;
          }
        });
      } else {
        this.shippingColumns.forEach((item, index) => {
          if (item.dataIndex === "currNum") {
            this.shippingColumns[index].isTable = true;
          }
        });
      }
      this.getReceivingTitle(type);
      this.methodType = type;
      getReceiveDetailApi(row.id).then((res) => {
        this.formParam = res.data;
        this.shippingTableData = this.formParam.manufacturerDeliveryRecords.map(
          (item) => {
            return {
              ...item,
              currNum: item.status?.value === "SUCCESS" ? 0 : 1,
            };
          }
        );
      });
      this.receivingDrawer = true;
    },
    getReceivingTitle(type) {
      switch (type) {
        case "add":
          return (this.receivingTitle = "新增收货单");
        case "edit":
          return (this.receivingTitle = "确认收货单");
        case "info":
          return (this.receivingTitle = "收货单详情");
        default:
          return (this.receivingTitle = "收货单详情");
      }
    },
    handleCurrNumChange(e, row) {
      const { number, receiveNum, currNum } = row;
      if (currNum > number - receiveNum) {
        Message.error("收货数量不能大于发货数量");
        this.$nextTick(() => {
          row.currNum = number - receiveNum;
        });
      }
    },
    // 确认收货
    confirmReceiving() {
      this.$refs.ProForm.handleSubmit().then((res) => {
        this.$confirm("是否确认收货", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          try {
            const data = this.shippingTableData.filter(
              (item) => item.status.value !== "SUCCESS" && item.currNum > 0
            );
            data.forEach((item) => {
              if (item.number - item.receiveNum < item.currNum) {
                throw new Error("收货数量不能大于发货数量");
              }
            });
            if (data.length === 0) {
              this.$message.error("暂无可收货物，请仔细检查清单");
              return;
            }
            this.btnLoading = true;
            const params = {
              id: this.formParam.id,
              shippingFee: this.formParam.shippingFee,
              deliveryDetails: data,
            };
            confirmReceiveApi(params).then((res) => {
              this.refresh();
              Message.success("收货成功");
              this.receivingDrawer = false;
            });
          } catch (e) {
            this.$message.error(e.message);
          } finally {
            this.btnLoading = false;
          }
        });
      });
    },
    // 退货
    handleReturn(row) {},
    // 关闭发货单
    closeOrder() {
      Message.warning("暂未开放");
    },
    handleLicenseImgUploadSuccess(result, row) {
      if (!row.picUrls) {
        this.$set(row, "picUrls", []);
      }
      row.picUrls.push(result);
    },
    handleLicenseImgUploadRemove(file, row) {
      const index = row.picUrls.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      row.picUrls.splice(index, 1);
    },
    refresh() {
      this.$refs.ProTable.refresh();
      // this.$refs.ProTable.listLoading = false;
    },
    closeDrawer() {
      this.receivingDrawer = false;
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .upload {
  .drag {
    overflow: hidden;
    .el-upload-dragger {
      height: 100%;
      border: none !important;
    }
  }
}
</style>
