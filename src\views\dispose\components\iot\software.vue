<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-27 14:29:18
 * @Description: 软件更新记录
 -->

<template>
  <div>
    <ProTable
      ref="ProTable"
      :data="tableData"
      :columns="columns"
      :query-param="queryParam"
      :local-pagination="localPagination"
      @loadData="loadData"
      @handleSelectionChange="handleSelectionChange"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd()"
        >
          新增记录
        </el-button>
      </template>
      <template #installFile="{ row }">
        {{ row.installFile.name }}
      </template>
    </ProTable>
    <!-- 新建弹窗 -->
    <ProDialog
      :value="addDialog"
      :title="'新建'"
      :confirm-text="'保存'"
      width="50%"
      :top="'50px'"
      @ok="handleAddDialogOk"
      @cancel="handleDialogCancel"
    >
      <ProForm
        ref="addForm"
        :form-param="addForm"
        :form-list="addColumns"
        :confirm-loading="addFormLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="'add'"
        @proSubmit="addFormSubmit"
      >
        <template #installFile>
          <zipFilesProUpload
            :file-list="fileList"
            @uploadSuccess="handleUploadSuccess"
            @uploadRemove="handleUploadRemove"
          />
        </template>
      </ProForm>
    </ProDialog>
  </div>
</template>

<script>
import { filterParamRange } from "@/utils";

const { uploadURL } = window.config.api;

import ProTable from "@/components/ProTable/index.vue";
import zipFilesProUpload from "@/components/ProUpload/zipFiles.vue";
import { Message, MessageBox } from "element-ui";
import { softwareTypeApi } from "@/api/customer";
import { getInstallSoftwareApi, addInstallSoftwareApi } from "@/api/iot";
import { cloneDeep } from "lodash";

export default {
  name: "Software",
  components: { ProTable, zipFilesProUpload },
  data() {
    const self = this;
    return {
      tableData: [],
      fileList: [],
      columns: [
        {
          dataIndex: "installFile",
          tableSlot: "installFile",
          title: "安装文件",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "versionNumber",
          title: "版本号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "type",
          title: "类型",
          isTable: true,
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "客户端",
              value: 1,
            },
            {
              label: "服务端",
              value: 2,
            },
          ],
          formatter: (row) => row.type.label,
          minWidth: 100,
        },
        {
          dataIndex: "updatedAt",
          title: "上传时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 180,
        },
        {
          dataIndex: "versionAffiliation",
          title: "版本描述",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 500,
        },
      ],
      queryParam: {},
      localPagination: {
        total: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      selection: [],
      addDialog: false,
      addFormLoading: false,
      addForm: {},
      addColumns: [
        {
          dataIndex: "versionNumber",
          title: "版本号",
          width: 100,
          isForm: true,
          formSpan: 12,
          clearable: true,
          valueType: "input",
        },
        {
          dataIndex: "type",
          title: "类型",
          width: 100,
          isForm: true,
          formSpan: 12,
          valueType: "select",
          clearable: true,
          option: [],
          optionMth: () => softwareTypeApi(),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "installFile",
          formSlot: "installFile",
          title: "安装文件",
          width: 100,
          isForm: true,
          valueType: "input",
          clearable: true,
        },
        {
          dataIndex: "versionAffiliation",
          title: "版本描述",
          width: 100,
          isForm: true,
          valueType: "input",
          clearable: true,
        },
      ],
    };
  },

  mounted() {
    this.refresh();
  },
  methods: {
    handleAdd() {
      this.addForm = {};
      this.addDialog = true;
      this.fileList = [];
    },
    // 图片处理
    handleUploadSuccess(result) {
      this.fileList.push(result);
    },

    handleUploadRemove() {
      this.fileList = [];
    },
    async loadData(params) {
      try {
        this.queryParam = {
          ...this.queryParam,
          ...params,
        };
        const res = [
          {
            createdAtStartTime: null,
            createdAtEndTime: null,
            data: params.updatedAt,
          },
        ];
        filterParamRange(this, this.queryParam, res);
        const requestParameters = cloneDeep(this.queryParam);
        delete requestParameters.updatedAt;
        const result = await getInstallSoftwareApi(requestParameters);
        if (result.code === 200 && result.data) {
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      }
    },
    // 刷新
    refresh() {
      this.$refs.ProTable.refresh();
    },
    // 新建保存
    handleAddDialogOk() {
      this.$refs.addForm.handleSubmit();
    },
    handleDialogCancel() {
      this.addDialog = false;
    },
    async addFormSubmit(val) {
      val.installFile = this.fileList[0];
      try {
        this.addFormLoading = true;
        const result = await addInstallSoftwareApi(val);
        if (result.code === 200) {
          Message.success("添加成功");
          this.addDialog = false;
          this.refresh();
        }
      } catch (err) {
        Message.error(err.message);
      } finally {
        this.addFormLoading = false;
      }
    },
    handleSelectionChange(val) {
      this.selection = val;
    },
  },
};
</script>

<style lang="scss" scoped>
.custom {
  width: 100%;
}
</style>
