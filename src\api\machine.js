/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-03 17:26:20
 * @Description: 机器模块API管理
 */

import { get, post, put, del } from "@/utils/request";

// =======================  寿命统计  =======================
// 寿命记录分页查询
export const getLifeRecordApi = (data) => post(`/lifeSpan/pageList`, data);
// 寿命统计分页查询
export const getLifeStatisticsApi = (data) =>
  post(`/lifeSpan/statisticPage`, data);
// 统计状态修改
export const updateLifeStatisticsApi = (data) => put(`/lifeSpan`, data);
// ========================  印量统计  =========================
// 按机器分月份
export const getPrintStatisticsApi = (data) =>
  post(`/statisics/month/mechine`, data);
// 按机型分月
export const getPrintStatisticsByTypeApi = (data) =>
  post(`/statisics/series/page`, data);
// 统计
export const getPrintStatisticsByTypePageApi = (data) =>
  post(`/statisics/totalCount`, data);

// ==============================  机器查询  ===========================
// =======  机器数据
// 机器数据分页查询
export const machinePageApi = (data) =>
  post(`customer-device-group/page-pc`, data);

// ==============================  分布统计  ===========================
// 区域机型分布
export const getMachineDistributionApi = (data) =>
  post(`/customer-device-group/distributionPage`, data);
// 机器分布统计
export const getMachineDistributionByTypeApi = (data) =>
  post(`/customer-device-group/distributionTotal`, data);
// 选配件分布
export const getAccessoryDistributionApi = (data) =>
  post(`/statisics/accessory/area`, data);

// ==============================  机器毛利  ===========================
// 分页查询
export const getMachineProfitApi = (data) =>
  post(`/statistics/queryMachineMonthGrossProfitList`, data);
// 汇总数据
export const getMachineProfitSummaryApi = (data) =>
  post(`/statistics/queryMachineMonthGrossProfit`, data);
