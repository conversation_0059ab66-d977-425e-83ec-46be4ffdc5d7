<!--
 * @Author: AI Assistant
 * @Date: 2025-01-29
 * @Description: 定向发布对话框组件
-->
<template>
  <el-dialog
    title="设置定向发布"
    :visible.sync="dialogVisible"
    width="700px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    :modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    destroy-on-close
  >
    <div class="targeted-release-container">
      <!-- 版本信息展示 -->
      <el-alert
        v-if="versionInfo"
        :title="`版本 ${versionInfo.versionName} (${versionInfo.versionCode})`"
        type="info"
        :closable="false"
        show-icon
        class="mb-4"
      >
        <div slot="description">
          当前状态: {{ versionInfo.releaseType === 'GLOBAL' ? '全局发布' : '定向发布' }}
        </div>
      </el-alert>

      <el-form 
        ref="targetedForm" 
        :model="formData" 
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="分配类型" prop="targetType">
          <el-radio-group v-model="formData.targetType">
            <el-radio label="USER">用户</el-radio>
            <el-radio label="DEVICE">设备</el-radio>
            <el-radio label="GROUP">用户组</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 用户选择 -->
        <el-form-item 
          v-if="formData.targetType === 'USER'" 
          label="目标用户" 
          prop="selectedUsers"
        >
          <div class="target-selection">
            <div class="selection-header">
              <el-input
                v-model="userSearchKeyword"
                placeholder="搜索用户..."
                prefix-icon="el-icon-search"
                size="small"
                style="width: 200px"
                @input="handleUserSearch"
              />
              <el-button size="small" @click="selectAllUsers">全选</el-button>
              <el-button size="small" @click="clearAllUsers">清空</el-button>
            </div>
            
            <div class="selection-content">
              <div class="target-list" v-loading="loadingUsers">
                <template v-if="filteredUsers.length > 0">
                  <el-checkbox
                    v-for="user in filteredUsers"
                    :key="user.id"
                    :value="isUserSelected(user)"
                    @change="handleUserChange(user, $event)"
                    class="target-item"
                  >
                    <div class="target-info">
                      <div class="target-name">
                        {{ user.displayName || user.name || '未知用户' }}
                      </div>
                    </div>
                  </el-checkbox>
                </template>
                <div v-else class="empty-state">
                  <el-empty
                    :image-size="60"
                    description="暂无用户数据"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 设备选择 -->
        <el-form-item 
          v-if="formData.targetType === 'DEVICE'" 
          label="目标设备" 
          prop="selectedDevices"
        >
          <div class="target-selection">
            <div class="selection-header">
              <el-input
                v-model="deviceSearchKeyword"
                placeholder="搜索设备..."
                prefix-icon="el-icon-search"
                size="small"
                style="width: 200px"
                @input="handleDeviceSearch"
              />
              <el-button size="small" @click="selectAllDevices">全选</el-button>
              <el-button size="small" @click="clearAllDevices">清空</el-button>
            </div>
            
            <div class="selection-content">
              <div class="target-list" v-loading="loadingDevices">
                <template v-if="filteredDevices.length > 0">
                  <el-checkbox
                    v-for="device in filteredDevices"
                    :key="device.id"
                    :value="isDeviceSelected(device)"
                    @change="handleDeviceChange(device, $event)"
                    class="target-item"
                  >
                    <div class="target-info">
                      <div class="target-name">
                        {{ device.displayName || device.deviceId || '未知设备' }}
                      </div>
                    </div>
                  </el-checkbox>
                </template>
                <div v-else class="empty-state">
                  <el-empty
                    :image-size="60"
                    description="暂无设备数据"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 用户组选择 -->
        <el-form-item 
          v-if="formData.targetType === 'GROUP'" 
          label="目标用户组" 
          prop="selectedGroups"
        >
          <div class="target-selection">
            <div class="selection-header">
              <el-input
                v-model="groupSearchKeyword"
                placeholder="搜索用户组..."
                prefix-icon="el-icon-search"
                size="small"
                style="width: 200px"
                @input="handleGroupSearch"
              />
              <el-button size="small" @click="selectAllGroups">全选</el-button>
              <el-button size="small" @click="clearAllGroups">清空</el-button>
            </div>
            
            <div class="selection-content">
              <div class="target-list" v-loading="loadingGroups">
                <template v-if="filteredGroups.length > 0">
                  <el-checkbox
                    v-for="group in filteredGroups"
                    :key="group.id"
                    :value="isGroupSelected(group)"
                    @change="handleGroupChange(group, $event)"
                    class="target-item"
                  >
                    <div class="target-info">
                      <div class="target-name">
                        {{ group.displayName || group.name || '未知用户组' }}
                      </div>
                    </div>
                  </el-checkbox>
                </template>
                <div v-else class="empty-state">
                  <el-empty
                    :image-size="60"
                    description="暂无用户组数据"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="formData.overrideExisting">
            覆盖现有分发关系
          </el-checkbox>
          <div class="form-tip">
            <i class="el-icon-warning"></i>
            勾选后将清除该版本的所有现有分发关系，重新设置
          </div>
        </el-form-item>

        <!-- 目标统计信息 -->
        <el-form-item label="目标统计">
          <div class="target-stats">
            <el-tag v-if="formData.targetType === 'USER' && formData.selectedUsers.length > 0" type="primary">
              已选用户: {{ formData.selectedUsers.length }}
            </el-tag>
            <el-tag v-if="formData.targetType === 'DEVICE' && formData.selectedDevices.length > 0" type="success">
              已选设备: {{ formData.selectedDevices.length }}
            </el-tag>
            <el-tag v-if="formData.targetType === 'GROUP' && formData.selectedGroups.length > 0" type="warning">
              已选用户组: {{ formData.selectedGroups.length }}
            </el-tag>
            <el-tag v-if="formData.targetType === 'GROUP' && groupMemberUsers.length > 0" type="info">
              组内用户: {{ groupMemberUsers.length }}
            </el-tag>
            <span v-if="getSelectedCount() === 0" class="text-muted">
              请选择目标对象
            </span>
          </div>
          <div v-if="formData.targetType === 'GROUP'" class="form-tip">
            <i class="el-icon-info"></i>
            选择用户组时，系统将自动获取组内所有用户进行定向发布
          </div>

          <!-- 显示用户组中的用户列表 -->
          <div v-if="formData.targetType === 'GROUP' && groupMemberUsers.length > 0" class="group-members">
            <div class="group-members-header">
              <span>用户组成员 ({{ groupMemberUsers.length }}人):</span>
              <el-button size="mini" type="text" @click="showGroupMembers = !showGroupMembers">
                {{ showGroupMembers ? '收起' : '展开' }}
              </el-button>
            </div>
            <div v-if="showGroupMembers" class="group-members-list">
              <el-tag
                v-for="user in groupMemberUsers"
                :key="user.id"
                size="mini"
                class="member-tag"
              >
                {{ user.displayName }}
              </el-tag>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleSubmit" 
        :loading="submitting"
        :disabled="getSelectedCount() === 0"
      >
        确定设置
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  setTargetedRelease,
  getUserList,
  getDeviceList,
  getRoleList,
  getRoleMembers,
  getGroupMembersUserIds
} from '@/appupdate/api/appVersion';

export default {
  name: 'TargetedReleaseDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    versionInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      loadingUsers: false,
      loadingDevices: false,
      loadingGroups: false,
      
      // 搜索关键词
      userSearchKeyword: '',
      deviceSearchKeyword: '',
      groupSearchKeyword: '',
      
      // 数据源
      allUsers: [],
      allDevices: [],
      allGroups: [],
      groupMemberUsers: [],  // 当前选中用户组的成员用户列表
      showGroupMembers: false,  // 是否显示用户组成员列表
      
      formData: {
        targetType: 'USER',
        selectedUsers: [],
        selectedDevices: [],
        selectedGroups: [],
        overrideExisting: false
      },
      
      formRules: {
        selectedUsers: [
          {
            validator: this.validateSelectedTargets,
            trigger: 'change'
          }
        ],
        selectedDevices: [
          {
            validator: this.validateSelectedTargets,
            trigger: 'change'
          }
        ],
        selectedGroups: [
          {
            validator: this.validateSelectedTargets,
            trigger: 'change'
          }
        ]
      }
    };
  },
  computed: {
    filteredUsers() {
      if (!this.userSearchKeyword) return this.allUsers;

      return this.allUsers.filter(user => {
        const name = user.name || user.username || '';
        const code = user.code || '';
        const displayName = user.displayName || '';
        return name.toLowerCase().includes(this.userSearchKeyword.toLowerCase()) ||
               code.toLowerCase().includes(this.userSearchKeyword.toLowerCase()) ||
               displayName.toLowerCase().includes(this.userSearchKeyword.toLowerCase());
      });
    },

    filteredDevices() {
      if (!this.deviceSearchKeyword) return this.allDevices;

      return this.allDevices.filter(device => {
        const deviceId = device.deviceId || '';
        const brand = device.brand || '';
        const model = device.model || '';
        const displayName = device.displayName || '';
        return deviceId.toLowerCase().includes(this.deviceSearchKeyword.toLowerCase()) ||
               brand.toLowerCase().includes(this.deviceSearchKeyword.toLowerCase()) ||
               model.toLowerCase().includes(this.deviceSearchKeyword.toLowerCase()) ||
               displayName.toLowerCase().includes(this.deviceSearchKeyword.toLowerCase());
      });
    },

    filteredGroups() {
      if (!this.groupSearchKeyword) return this.allGroups;

      return this.allGroups.filter(group => {
        const name = group.name || '';
        const code = group.code || '';
        const displayName = group.displayName || '';
        return name.toLowerCase().includes(this.groupSearchKeyword.toLowerCase()) ||
               code.toLowerCase().includes(this.groupSearchKeyword.toLowerCase()) ||
               displayName.toLowerCase().includes(this.groupSearchKeyword.toLowerCase());
      });
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.loadUsers();
        this.loadDevices();
        this.loadGroups();
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    },
    'formData.targetType'() {
      // 切换类型时清空已选择的目标
      this.formData.selectedUsers = [];
      this.formData.selectedDevices = [];
      this.formData.selectedGroups = [];
    }
  },
  methods: {
    validateSelectedTargets(rule, value, callback) {
      if (this.getSelectedCount() === 0) {
        callback(new Error('请选择目标对象'));
      } else {
        callback();
      }
    },

    getSelectedCount() {
      switch (this.formData.targetType) {
        case 'USER':
          return this.formData.selectedUsers.length;
        case 'DEVICE':
          return this.formData.selectedDevices.length;
        case 'GROUP':
          return this.formData.selectedGroups.length;
        default:
          return 0;
      }
    },

    // 加载用户列表
    async loadUsers() {
      this.loadingUsers = true;
      try {
        this.allUsers = await getUserList();
      } catch (error) {
        this.$message.error('加载用户列表失败');
        this.allUsers = [];
      } finally {
        this.loadingUsers = false;
      }
    },

    // 加载设备列表
    async loadDevices() {
      this.loadingDevices = true;
      try {
        this.allDevices = await getDeviceList();
      } catch (error) {
        this.$message.error('加载设备列表失败');
        this.allDevices = [];
      } finally {
        this.loadingDevices = false;
      }
    },

    // 加载用户组列表
    async loadGroups() {
      this.loadingGroups = true;
      try {
        this.allGroups = await getRoleList();
      } catch (error) {
        this.$message.error('加载用户组列表失败');
        this.allGroups = [];
      } finally {
        this.loadingGroups = false;
      }
    },

    // 用户选择相关方法
    handleUserSearch() {
      // 搜索逻辑在computed中处理
    },

    isUserSelected(user) {
      return this.formData.selectedUsers.some(selected => selected.id === user.id);
    },

    handleUserChange(user, checked) {
      if (checked) {
        this.formData.selectedUsers.push({
          id: user.id,
          name: user.displayName || user.name || '未知用户'
        });
      } else {
        this.formData.selectedUsers = this.formData.selectedUsers.filter(
          selected => selected.id !== user.id
        );
      }
    },

    selectAllUsers() {
      this.formData.selectedUsers = this.filteredUsers.map(user => ({
        id: user.id,
        name: user.displayName || user.name || '未知用户'
      }));
    },

    clearAllUsers() {
      this.formData.selectedUsers = [];
    },

    // 设备选择相关方法
    handleDeviceSearch() {
      // 搜索逻辑在computed中处理
    },

    isDeviceSelected(device) {
      return this.formData.selectedDevices.some(selected => selected.id === device.id);
    },

    handleDeviceChange(device, checked) {
      if (checked) {
        this.formData.selectedDevices.push({
          id: device.id,
          deviceId: device.deviceId,
          brand: device.brand,
          model: device.model,
          osVersion: device.osVersion,
          name: device.displayName || device.deviceId || '未知设备'
        });
      } else {
        this.formData.selectedDevices = this.formData.selectedDevices.filter(
          selected => selected.id !== device.id
        );
      }
    },

    selectAllDevices() {
      this.formData.selectedDevices = this.filteredDevices.map(device => ({
        id: device.id,
        deviceId: device.deviceId,
        brand: device.brand,
        model: device.model,
        osVersion: device.osVersion,
        name: device.displayName || device.deviceId || '未知设备'
      }));
    },

    clearAllDevices() {
      this.formData.selectedDevices = [];
    },

    // 用户组选择相关方法
    handleGroupSearch() {
      // 搜索逻辑在computed中处理
    },

    isGroupSelected(group) {
      return this.formData.selectedGroups.some(selected => selected.id === group.id);
    },

    async handleGroupChange(group, checked) {
      if (checked) {
        this.formData.selectedGroups.push({
          id: group.id,
          name: group.displayName || group.name || '未知用户组'
        });
      } else {
        this.formData.selectedGroups = this.formData.selectedGroups.filter(
          selected => selected.id !== group.id
        );
      }

      // 实时获取用户组成员
      await this.loadGroupMembers();
    },

    selectAllGroups() {
      this.formData.selectedGroups = this.filteredGroups.map(group => ({
        id: group.id,
        name: group.displayName || group.name || '未知用户组'
      }));
    },

    clearAllGroups() {
      this.formData.selectedGroups = [];
      this.groupMemberUsers = [];  // 清空用户组成员
    },

    // 加载用户组成员
    async loadGroupMembers() {
      if (this.formData.selectedGroups.length === 0) {
        this.groupMemberUsers = [];
        return;
      }

      try {
        const groupIds = this.formData.selectedGroups.map(group => group.id);
        const memberPromises = groupIds.map(groupId => getRoleMembers(groupId));
        const memberResults = await Promise.all(memberPromises);

        // 合并所有成员并去重
        const allMembers = memberResults.flat();
        const uniqueMembers = allMembers.filter((member, index, self) =>
          index === self.findIndex(m => m.id === member.id)
        );

        this.groupMemberUsers = uniqueMembers;
        console.log(`用户组成员获取成功，共 ${uniqueMembers.length} 人:`, uniqueMembers);
      } catch (error) {
        console.error('获取用户组成员失败:', error);
        this.$message.error('获取用户组成员失败');
        this.groupMemberUsers = [];
      }
    },

    async handleSubmit() {
      try {
        await this.$refs.targetedForm.validate();

        this.submitting = true;

        // 构建请求数据
        let userIds = [];
        let deviceIds = [];
        let groupIds = [];

        if (this.formData.targetType === 'USER') {
          userIds = this.formData.selectedUsers.map(user => user.id);
        } else if (this.formData.targetType === 'DEVICE') {
          deviceIds = this.formData.selectedDevices.map(device => device.id);
        } else if (this.formData.targetType === 'GROUP') {
          groupIds = this.formData.selectedGroups.map(group => group.id);
          // 直接使用已获取的用户组成员用户ID
          userIds = this.groupMemberUsers.map(user => user.id);
          console.log(`用户组 ${groupIds.join(',')} 中的用户ID:`, userIds);
        }

        const requestData = {
          deviceIds,
          groupIds: [], // 保留字段但设为空数组
          overrideExisting: this.formData.overrideExisting,
          // 添加完整的用户/设备信息
          users: this.formData.targetType === 'USER' ?
            this.formData.selectedUsers :
            (this.formData.targetType === 'GROUP' ? this.groupMemberUsers : []),
          devices: this.formData.targetType === 'DEVICE' ? this.formData.selectedDevices : []
          // 移除userIds字段，移除groups字段
        };

        await setTargetedRelease(this.versionInfo.id, requestData);

        this.$message.success('定向发布设置成功');
        this.$emit('success');
        this.handleClose();
      } catch (error) {
        if (error !== 'validation failed') {
          this.$message.error('设置失败：' + error.message);
        }
      } finally {
        this.submitting = false;
      }
    },

    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
    },

    resetForm() {
      this.formData = {
        targetType: 'USER',
        selectedUsers: [],
        selectedDevices: [],
        selectedGroups: [],
        overrideExisting: false
      };

      // 重置搜索关键词
      this.userSearchKeyword = '';
      this.deviceSearchKeyword = '';
      this.groupSearchKeyword = '';

      // 重置用户组成员相关状态
      this.groupMemberUsers = [];
      this.showGroupMembers = false;

      this.$nextTick(() => {
        this.$refs.targetedForm && this.$refs.targetedForm.clearValidate();
      });
    }
  }
};
</script>

<style scoped>
.targeted-release-container {
  max-height: 600px;
  overflow-y: auto;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.form-tip i {
  margin-right: 4px;
}

.target-selection {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.selection-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.selection-content {
  max-height: 350px;
  overflow-y: auto;
}

.target-list {
  padding: 10px;
}

.target-item {
  display: block;
  width: 100%;
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  transition: all 0.3s;
}

.target-item:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
}

.target-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.target-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.empty-state {
  padding: 40px;
  text-align: center;
}

.target-stats {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}

.dialog-footer {
  text-align: right;
}

.group-members {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.group-members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.group-members-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 120px;
  overflow-y: auto;
}

.member-tag {
  margin: 0;
}
</style>
