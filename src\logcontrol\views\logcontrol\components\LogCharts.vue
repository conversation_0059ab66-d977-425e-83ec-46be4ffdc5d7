<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-22 16:44:06
 * @Description: 日志统计图表组件
-->
<template>
  <div class="log-charts">
    <el-row :gutter="20">
      <!-- 日志类型分布图 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>日志类型分布</span>
            <el-button type="text" size="small" @click="loadLogTypeStats">
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div ref="logTypeChart" class="chart-container" v-loading="logTypeLoading"></div>
        </el-card>
      </el-col>

      <!-- 日志级别分布图 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>日志级别分布</span>
            <el-button type="text" size="small" @click="loadLogLevelStats">
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div ref="logLevelChart" class="chart-container" v-loading="logLevelLoading"></div>
        </el-card>
      </el-col>

      <!-- 异常类型统计图 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>异常类型统计</span>
            <el-button type="text" size="small" @click="loadCrashStats">
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div ref="exceptionChart" class="chart-container" v-loading="crashLoading"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 设备崩溃统计 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>设备崩溃统计</span>
            <el-button type="text" size="small" @click="loadCrashStats">
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div ref="deviceCrashChart" class="chart-container" v-loading="crashLoading"></div>
        </el-card>
      </el-col>

      <!-- 应用版本崩溃统计 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>应用版本崩溃</span>
            <el-button type="text" size="small" @click="loadCrashStats">
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div ref="appVersionChart" class="chart-container" v-loading="crashLoading"></div>
        </el-card>
      </el-col>

      <!-- 设备品牌分布图 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>设备品牌分布</span>
            <el-button type="text" size="small" @click="loadDeviceStats">
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div ref="brandChart" class="chart-container" v-loading="deviceLoading"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 系统版本分布图 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>系统版本分布</span>
            <el-button type="text" size="small" @click="loadDeviceStats">
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div ref="osChart" class="chart-container" v-loading="deviceLoading"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { analysisApi } from '@/logcontrol/api/analysisApi'

export default {
  name: 'LogCharts',
  data() {
    return {
      // 加载状态
      logTypeLoading: false,
      logLevelLoading: false,
      crashLoading: false,
      deviceLoading: false,

      // 图表实例
      logTypeChart: null,
      logLevelChart: null,
      exceptionChart: null,
      deviceCrashChart: null,
      brandChart: null,
      osChart: null,
      appVersionChart: null,

      // 统计数据
      logTypeStats: [],
      logLevelStats: [],
      crashStats: null,
      deviceStats: null,

      // 图表配置映射
      chartConfigs: [
        { instance: 'logTypeChart', ref: 'logTypeChart' },
        { instance: 'logLevelChart', ref: 'logLevelChart' },
        { instance: 'exceptionChart', ref: 'exceptionChart' },
        { instance: 'deviceCrashChart', ref: 'deviceCrashChart' },
        { instance: 'appVersionChart', ref: 'appVersionChart' },
        { instance: 'brandChart', ref: 'brandChart' },
        { instance: 'osChart', ref: 'osChart' }
      ]
    }
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.initCharts()
        this.loadAllStats()
      }, 300)  // 增加延迟时间确保DOM完全渲染
    })
  },
  activated() {
    // 当组件被激活时（如从其他页面切换回来），重新初始化图表
    this.$nextTick(() => {
      setTimeout(() => {
        if (!this.logTypeChart || !this.logLevelChart) {
          this.initCharts()
          this.loadAllStats()
        } else {
          // 如果图表已存在，调整大小并刷新数据
          this.handleResize()
          this.loadAllStats()
        }
      }, 100)
    })
  },

  watch: {
    // 监听路由变化，当切换到仪表盘页面时重新初始化图表
    '$route'(to) {
      if (to.path.includes('dashboard') || to.path.includes('logcontrol')) {
        this.$nextTick(() => {
          setTimeout(() => {
            // 强制重新初始化图表，确保在路由切换后能正确显示
            this.forceReinitCharts()
          }, 300)
        })
      }
    }
  },

  beforeDestroy() {
    this.cleanup()
  },
  methods: {

    // 初始化所有图表
    initCharts(retryCount = 0) {
      const maxRetries = 3
      let hasValidContainer = false

      // 循环初始化所有图表
      this.chartConfigs.forEach(config => {
        const ref = this.$refs[config.ref]
        if (ref && ref.clientWidth > 0) {
          this[config.instance] = echarts.init(ref)
          hasValidContainer = true
        }
      })

      // 如果没有有效容器且未达到最大重试次数，则重试
      if (!hasValidContainer && retryCount < maxRetries) {
        setTimeout(() => {
          this.initCharts(retryCount + 1)
        }, 200)
        return
      }

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },

    // 强制重新初始化图表（用于路由切换后确保图表显示）
    forceReinitCharts() {
      // 先销毁现有图表实例
      this.destroyCharts()

      // 重新初始化
      this.initCharts()
      this.loadAllStats()
    },

    // 销毁所有图表实例（仅销毁图表，不移除事件监听器）
    destroyCharts() {
      this.chartConfigs.forEach(config => {
        const chart = this[config.instance]
        if (chart) {
          chart.dispose()
          this[config.instance] = null
        }
      })
    },

    // 完全清理（销毁图表并移除事件监听器）
    cleanup() {
      window.removeEventListener('resize', this.handleResize)
      this.destroyCharts()
    },

    // 处理窗口大小变化
    handleResize() {
      this.$nextTick(() => {
        this.chartConfigs.forEach(config => {
          const chart = this[config.instance]
          if (chart) chart.resize()
        })
      })
    },

    // 加载所有统计数据
    async loadAllStats() {
      await Promise.all([
        this.loadLogTypeStats(),
        this.loadLogLevelStats(),
        this.loadCrashStats(),
        this.loadDeviceStats()
      ])
    },

    // 加载日志类型统计
    async loadLogTypeStats() {
      this.logTypeLoading = true
      try {
        const response = await analysisApi.getLogTypeStats()
        this.logTypeStats = response.data || []
        this.updateLogTypeChart()
      } catch (error) {
        console.error('加载日志类型统计失败:', error)
      } finally {
        this.logTypeLoading = false
      }
    },

    // 加载日志级别统计
    async loadLogLevelStats() {
      this.logLevelLoading = true
      try {
        const response = await analysisApi.getLogLevelStats()
        this.logLevelStats = response.data || []
        this.updateLogLevelChart()
      } catch (error) {
        console.error('加载日志级别统计失败:', error)
      } finally {
        this.logLevelLoading = false
      }
    },

    // 加载崩溃统计
    async loadCrashStats() {
      this.crashLoading = true
      try {
        const response = await analysisApi.getCrashStats()
        this.crashStats = response.data
        this.updateExceptionChart()
        this.updateDeviceCrashChart()
        this.updateAppVersionChart()
      } catch (error) {
        console.error('加载崩溃统计失败:', error)
      } finally {
        this.crashLoading = false
      }
    },

    // 加载设备统计
    async loadDeviceStats() {
      this.deviceLoading = true
      try {
        const response = await analysisApi.getDeviceStats()
        this.deviceStats = response.data
        this.updateBrandChart()
        this.updateOsChart()
      } catch (error) {
        console.error('加载设备统计失败:', error)
      } finally {
        this.deviceLoading = false
      }
    },

    // 更新日志类型图表
    updateLogTypeChart() {
      if (!this.logTypeChart || !this.logTypeStats.length) return

      const data = this.logTypeStats.map(item => ({
        name: this.getLogTypeLabel(item.log_type),
        value: parseInt(item.count) || 0
      }))

      const option = {
        title: {
          text: `总计: ${data.reduce((sum, item) => sum + item.value, 0)}`,
          left: 'center',
          top: '1%',
          textStyle: { fontSize: 14, color: '#666' }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: '5%',
          textStyle: { fontSize: 12 }
        },
        series: [{
          name: '日志类型',
          type: 'pie',
          radius: ['30%', '60%'],
          center: ['50%', '45%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }

      this.logTypeChart.setOption(option)
    },

    // 更新日志级别图表
    updateLogLevelChart() {
      if (!this.logLevelChart || !this.logLevelStats.length) return

      const data = this.logLevelStats.map(item => ({
        name: item.level,
        value: parseInt(item.count) || 0
      }))

      const colors = {
        'INFO': '#52c41a',
        'WARN': '#faad14',
        'ERROR': '#f5222d',
        'DEBUG': '#1890ff'
      }

      const option = {
        title: {
          text: `总计: ${data.reduce((sum, item) => sum + item.value, 0)}`,
          left: 'center',
          top: '1%',
          textStyle: { fontSize: 14, color: '#666' }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: '10%',
          textStyle: { fontSize: 12 }
        },
        series: [{
          name: '日志级别',
          type: 'pie',
          radius: ['30%', '60%'],
          center: ['50%', '45%'],
          data: data.map(item => ({
            ...item,
            itemStyle: { color: colors[item.name] || '#666' }
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }

      this.logLevelChart.setOption(option)
    },

    // 更新异常类型图表
    updateExceptionChart() {
      if (!this.exceptionChart || !this.crashStats?.exceptionTypeStats) return

      // 计算所有异常类型的真实总数
      const allExceptionData = this.crashStats.exceptionTypeStats
      const totalCount = allExceptionData.reduce((sum, item) => sum + (parseInt(item.count) || 0), 0)

      // 显示前9个异常类型，第10个及以后合并为"其它"
      let data = []

      if (allExceptionData.length <= 9) {
        // 如果总数不超过9个，直接显示所有
        data = allExceptionData.map(item => ({
          name: this.getExceptionTypeLabel(item.exception_type),
          value: parseInt(item.count) || 0
        }))
      } else {
        // 前9个正常显示
        const top9 = allExceptionData.slice(0, 9).map(item => ({
          name: this.getExceptionTypeLabel(item.exception_type),
          value: parseInt(item.count) || 0
        }))

        // 第10个及以后合并为"其它"
        const othersCount = allExceptionData.slice(9).reduce((sum, item) => sum + (parseInt(item.count) || 0), 0)

        data = [...top9, {
          name: '其它',
          value: othersCount
        }]
      }

      const option = {
        title: {
          text: `异常总数: ${totalCount}`,
          left: 'center',
          top: '1%',
          textStyle: { fontSize: 14, color: '#666' }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: { fontSize: 10 }
        },
        yAxis: {
          type: 'category',
          data: data.map(item => item.name),
          axisLabel: { fontSize: 10, interval: 0 }
        },
        series: [{
          name: '异常次数',
          type: 'bar',
          data: data.map(item => item.value),
          itemStyle: { color: '#ff4d4f' }
        }]
      }

      this.exceptionChart.setOption(option)
    },

    // 更新品牌分布图表
    updateBrandChart() {
      if (!this.brandChart || !this.deviceStats?.brandDistribution) return

      const data = this.deviceStats.brandDistribution.map(item => ({
        name: item.brand,
        value: parseInt(item.count) || 0
      }))

      const option = {
        title: {
          text: `设备总数: ${data.reduce((sum, item) => sum + item.value, 0)}`,
          left: 'center',
          top: '1%',
          textStyle: { fontSize: 14, color: '#666' }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: '10%',
          textStyle: { fontSize: 12 }
        },
        series: [{
          name: '设备品牌',
          type: 'pie',
          radius: ['30%', '60%'],
          center: ['50%', '45%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }

      this.brandChart.setOption(option)
    },

    // 更新系统版本图表
    updateOsChart() {
      if (!this.osChart || !this.deviceStats?.osVersionDistribution) return

      const data = this.deviceStats.osVersionDistribution.map(item => ({
        name: item.os_version,
        value: parseInt(item.count) || 0
      }))

      const option = {
        title: {
          text: `版本总数: ${data.reduce((sum, item) => sum + item.value, 0)}`,
          left: 'center',
          top: '1%',
          textStyle: { fontSize: 14, color: '#666' }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: '10%',
          textStyle: { fontSize: 12 }
        },
        series: [{
          name: '系统版本',
          type: 'pie',
          radius: ['30%', '60%'],
          center: ['50%', '45%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }

      this.osChart.setOption(option)
    },

    // 更新设备崩溃图表
    updateDeviceCrashChart() {
      if (!this.deviceCrashChart || !this.crashStats?.deviceCrashStats) return

      const data = this.crashStats.deviceCrashStats.map(item => ({
        name: item.device_id.slice(-8), // 显示设备ID的后8位
        value: parseInt(item.count) || 0,
        fullDeviceId: item.device_id
      }))

      const option = {
        title: {
          text: `设备总数: ${data.length}`,
          left: 'center',
          top: '1%',
          textStyle: { fontSize: 14, color: '#666' }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return `设备ID: ${params.data.fullDeviceId}<br/>崩溃次数: ${params.value} (${params.percent}%)`
          }
        },
        legend: {
          orient: 'horizontal',
          bottom: '10%',
          textStyle: { fontSize: 12 },
          formatter: function(name) {
            const item = data.find(d => d.name === name)
            return `${name} (${item.value})`
          }
        },
        series: [{
          name: '设备崩溃',
          type: 'pie',
          radius: ['30%', '60%'],
          center: ['50%', '45%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }

      this.deviceCrashChart.setOption(option)
    },

    // 更新应用版本崩溃图表
    updateAppVersionChart() {
      if (!this.appVersionChart || !this.crashStats?.appVersionCrashStats) return

      const data = this.crashStats.appVersionCrashStats.map(item => ({
        name: item.app_version,
        value: parseInt(item.count) || 0
      }))

      const option = {
        title: {
          text: `崩溃总数: ${data.reduce((sum, item) => sum + item.value, 0)}`,
          left: 'center',
          top: '1%',
          textStyle: { fontSize: 14, color: '#666' }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: '10%',
          textStyle: { fontSize: 12 }
        },
        series: [{
          name: '应用版本',
          type: 'pie',
          radius: ['30%', '60%'],
          center: ['50%', '45%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }

      this.appVersionChart.setOption(option)
    },

    // 获取日志类型标签
    getLogTypeLabel(logType) {
      const labels = {
        'LOCATION': '位置日志',
        'CRASH': '崩溃日志',
        'BUSINESS': '业务日志',
        'TEST': '测试日志',
        'RAPID_TEST': '快速测试',
        'SYSTEM': '系统日志',
        'ERROR': '错误日志'
      }
      return labels[logType] || logType
    },

    // 获取异常类型标签
    getExceptionTypeLabel(exceptionType) {
      // 特殊异常类型的友好显示名称
      const friendlyNames = {
        'java.io.IOException': 'IO异常',
        'java.lang.RuntimeException': '运行时异常',
        'java.lang.IllegalStateException': '非法状态异常',
        'java.lang.NullPointerException': '空指针异常',
        'android.database.sqlite.SQLiteConstraintException': 'SQLite约束异常',
        'com.google.gson.JsonSyntaxException': 'JSON语法异常',
        'android.database.sqlite.SQLiteException': 'SQLite异常',
        'java.net.ConnectException': '连接异常',
        'retrofit2.HttpException': 'HTTP异常'
      }

      // 如果有友好名称，使用友好名称，否则使用类名
      if (friendlyNames[exceptionType]) {
        return friendlyNames[exceptionType]
      }

      // 提取类名（最后一个点后的部分）
      const parts = exceptionType.split('.')
      return parts[parts.length - 1] || exceptionType
    },


  }
}
</script>

<style lang="scss" scoped>
.log-charts {
  .chart-card {
    height: 400px;
    
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      span {
        font-weight: 500;
        color: #303133;
      }
    }
    
    .chart-container {
      height: 320px;
      width: 100%;
    }
  }
}

@media (max-width: 768px) {
  .log-charts {
    .chart-card {
      height: 350px;
      margin-bottom: 20px;
      
      .chart-container {
        height: 270px;
      }
    }
  }
}
</style>
}
</style>
