<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-03-27 14:24:47
 * @Description: 
 -->
<template>
  <div class="app-container">
    <ProDialog
      :value="dialogVisible"
      title="修改密码"
      :layout="{ formWidth: '100%', labelWidth: '100px' }"
      confirm-text="确认修改"
      :confirm-btn-loading="confirmLoading"
      width="600px"
      @ok="handleDialogOk"
      @cancel="handleCloseDialog"
    >
      <ProForm
        ref="ProForm"
        :form-param="editForm"
        :form-list="formColumns"
        :confirm-loading="confirmFormLoading"
        @proSubmit="proSubmit"
      ></ProForm>
    </ProDialog>
  </div>
</template>

<script>
import { editPwdApi, editPwdApi2, key } from "@/api/user";
import { encodeFun } from "@/utils";
import { cloneDeep } from "lodash";

export default {
  name: "ReloadPassword",
  data() {
    return {
      dialogVisible: false,
      editForm: {
        id: JSON.parse(localStorage.getItem("userInfo"))?.id || "",
      },
      defaultEditForm: {
        id: JSON.parse(localStorage.getItem("userInfo"))?.id || "",
      },
      formColumns: [
        {
          dataIndex: "originPassword",
          title: "原密码",
          isForm: true,
          valueType: "input",
          inputType: "password",
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入原密码",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "newPassword",
          title: "新密码",
          isForm: true,
          valueType: "input",
          inputType: "password",
          placeholder: "请输入新密码",
          hint: true,
          hintMessage: "密码必须包含小写字母和数字，且长度不能少于6位数！",
          formSpan: 24,
          prop: [
            {
              required: true,
              validator: this.validatePassword,
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "surePassword",
          title: "确认密码",
          isForm: true,
          valueType: "input",
          inputType: "password",
          placeholder: "请再次输入新密码",
          // hint: true,
          // hintMessage: "密码必须包含小写字母和数字，且长度不能少于6位数！",
          formSpan: 24,
          prop: [
            {
              required: true,
              validator: this.validateSurePassword,
              trigger: "blur",
            },
          ],
        },
      ],
      confirmLoading: false,
      confirmFormLoading: false,
    };
  },
  mounted() {},
  methods: {
    show() {
      this.resetForm();
      this.dialogVisible = true;
    },
    handleDialogOk() {
      this.$refs.ProForm.handleSubmit();
    },
    proSubmit(val) {
      this.confirmLoading = true;
      try {
        key().then((res) => {
          const key = res.data.second;
          this.editForm.cipherToken = res.data.first;
          this.editForm.second = key;
          // /id: this.editForm.id,
          const obj = {
            ...val,

            originPassword: encodeFun(val.originPassword, key),
            newPassword: encodeFun(val.newPassword, key),
            surePassword: encodeFun(val.surepassword, key),
          };
          editPwdApi2(obj)
            .then(async () => {
              this.$message.success("修改成功");
              this.dialogVisible = false;
              await this.$store.dispatch("user/logout");
              this.$router.push(`/login?redirect=${this.$route.fullPath}`);
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        });
      } finally {
        this.confirmLoading = false;
      }
    },
    handleCloseDialog() {
      this.dialogVisible = false;
    },
    resetForm() {
      this.editForm = cloneDeep(this.defaultEditForm);
    },
    validatePassword(rule, value, callback) {
      const regPwd =
        /^(?=.*[a-z])(?=.*\d)[A-Za-z\d~!@#$%^&*()_+`\-={}:";'<>,.\/,\\]{6,20}$/;

      if (!value) {
        callback(new Error("请输入新密码"));
      } else if (value.length < 6) {
        callback(new Error("密码长度不能少于6位"));
      } else if (value.length > 20) {
        callback(new Error("密码长度不能大于20位"));
      } else if (!regPwd.test(value)) {
        callback(new Error("密码必须包含小写字母和数字"));
      } else {
        callback();
      }
    },
    validateSurePassword(rule, value, callback) {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.editForm.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    },
  },
};
</script>

<style scoped lang="scss"></style>
