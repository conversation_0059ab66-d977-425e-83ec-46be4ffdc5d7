<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-05-17 09:15:45
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-06-19 17:44:45
 * @Description: 财务 - 月度毛利
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :show-rule="true"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData">
      <template #rule>
        <div class="rules-tips">
          <h3 class="rule-title">月度毛利统计规则</h3>
          <div class="rule-item">
            <span class="rule-number">统计周期：</span>
            <span class="rule-text">
              例如统计 1 月数据，即 1月1日 00:00:00 至 1月31日 23:59:59
            </span>
          </div>
          <ol>
            <li>
              <div class="rule-item">
                <span class="rule-number">收入与支出：</span>
              </div>
              <div class="rule-item" style="margin-left: 20px">
                <span class="rule-text">总收入：月度收入中的总收入</span>
              </div>
              <div class="rule-item" style="margin-left: 20px">
                <span class="rule-text">总支出：月度支出中的总支出</span>
              </div>
              <div class="rule-item" style="margin-left: 20px">
                <span class="rule-text">毛利：总收入 - 总支出</span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">抄表费：</span>
                <span class="rule-text">月度收入中的抄表应收</span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">机器台数：</span>
                <span class="rule-text">
                  客户管理中的机器信息中的服务类型为
                  <span class="warning">全保</span>
                  、
                  <span class="warning">半保</span>
                  、
                  <span class="warning">租赁</span>
                  的机器数量之和
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">维修次数：</span>
                <span class="rule-text">
                  工单中机器的服务类型为
                  <span class="warning">全保</span>
                  、
                  <span class="warning">半保</span>
                  、
                  <span class="warning">租赁</span>
                  的机器的维修状态为
                  <span class="warning">完成</span>
                  的维修次数之和
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">印量统计：</span>
              </div>
              <div class="rule-item" style="margin-left: 20px">
                <span class="rule-text">
                  黑白印量：抄表对账中的抄表记录中的黑白印量之和
                </span>
              </div>
              <div class="rule-item" style="margin-left: 20px">
                <span class="rule-text">
                  彩色印量：抄表对账中的抄表记录中的彩色印量之和
                </span>
              </div>
            </li>
          </ol>
        </div>
      </template>
      <template #btn>
        <div v-if="statLoading" class="title-box-right">
          <div>总收入: {{ totalData?.totalIncome || 0 }}</div>
          <div>总支出: {{ totalData?.totalExpenditure || 0 }}</div>
          <div>毛利: {{ totalData?.actualProfit || 0 }}</div>
          <div>抄表费: {{ totalData?.actualReceipt || 0 }}</div>
          <div>黑白印量: {{ totalData?.blackNums || 0 }}</div>
          <div>彩色印量: {{ totalData?.colorNums || 0 }}</div>
          <div>机器台数: {{ totalData?.mechanicalNums || 0 }}</div>
          <div>维修次数: {{ totalData?.repairTimes || 0 }}</div>
        </div>
        <div v-else class="title-box-right" style="gap: 5px">
          <i class="el-icon-loading"></i>
          正在加载统计数据
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
        </div>
      </template>
    </ProTable>
    <!-- 每月毛利详情 -->
    <ProDrawer
      :value="drawerVisible"
      :title="drawerTitle"
      size="80%"
      :no-footer="true"
      @cancel="drawerVisible = false">
      <!-- TODO：排序@sortChange="sortChange" -->
      <ProTable
        ref="DetailTable"
        height="65vh"
        :query-param="detailQueryParam"
        :local-pagination="detailLocalPagination"
        :show-pagination="false"
        :columns="detailColumns"
        :data="detailTableData"
        :use-infinite-scroll="true"
        :has-more="hasMore"
        @loadData="handleLoadData">
        <template #btn>
          <div class="title-box-right" style="font-size: 14px; gap: 10px">
            <div>抄表应收: {{ detailTotalData?.readingReceivable || 0 }}</div>
            <div>抄表实收: {{ detailTotalData?.actualReceipt || 0 }}</div>
            <div>耗材成本: {{ detailTotalData?.costConsumables || 0 }}</div>
            <div>零件成本: {{ detailTotalData?.costPart || 0 }}</div>
            <div>碳粉收入: {{ detailTotalData?.tonerIncome || 0 }}</div>
            <div>零件收入: {{ detailTotalData?.costIncome || 0 }}</div>
            <div>应收毛利: {{ detailTotalData?.readingProfit || 0 }}</div>
            <div>实收毛利: {{ detailTotalData?.actualProfit || 0 }}</div>
            <div>毛利差额: {{ detailTotalData?.differenceProfit || 0 }}</div>
            <div>维修次数: {{ detailTotalData?.repairTimes || 0 }}</div>
            <div>黑白印量: {{ detailTotalData?.blackNums || 0 }}</div>
            <div>彩色印量: {{ detailTotalData?.colorNums || 0 }}</div>
          </div>
        </template>
        <template #action="{ row }">
          <div class="fixed-width">
            <el-button icon="el-icon-view" @click="handleViewDetail(row)">
              查看
            </el-button>
          </div>
        </template>
      </ProTable>

      <!--<ProForm-->
      <!--  ref="ProFrom"-->
      <!--  :layout="{ formWidth: '100%', labelWidth: '100px' }"-->
      <!--  :form-param="formParam"-->
      <!--  :form-list="formColumns"-->
      <!--  :open-type="editType"-->
      <!--  :confirm-loading="confirmLoading"-->
      <!--&gt;-->
      <!--</ProForm>-->
    </ProDrawer>
    <!-- 当月客户详情 -->
    <ProDialog
      v-drag
      :value="dialogVisible"
      :no-footer="true"
      :title="'机器收益明细'"
      width="75%"
      top="2%"
      @cancel="handleDialogCancel">
      <ProTable
        ref="MachineTable"
        height="65vh"
        :show-pagination="false"
        :show-search="false"
        :show-loading="false"
        :columns="machineColumns"
        :data="machineTableData">
        <template #btn>
          <div class="title-box-right" style="font-size: 14px; gap: 10px">
            <div>抄表应收: {{ totalData.totalExpenditure || 0 }}</div>
            <div>抄表实收: {{ totalData.totalExpense || 0 }}</div>
            <div>抄表实收: {{ totalData.totalExpense || 0 }}</div>
            <div>半保机碳粉收入: {{ totalData.totalExpense || 0 }}</div>
            <div>零件成本: {{ totalData.meter || 0 }}</div>
            <div>耗材成本: {{ totalData.actualProfit || 0 }}</div>
            <div>应收计算毛利: {{ totalData.machineNum || 0 }}</div>
            <div>实收计算毛利: {{ totalData.repairTimes || 0 }}</div>
            <div>毛利差额: {{ totalData.repairTimes || 0 }}</div>
            <div>维修次数: {{ totalData.repairTimes || 0 }}</div>
            <div>黑白印量: {{ totalData.repairTimes || 0 }}</div>
            <div>彩色印量: {{ totalData.repairTimes || 0 }}</div>
          </div>
        </template>
      </ProTable>
    </ProDialog>
  </div>
</template>

<script>
import { filterParam, filterParamRange } from "@/utils";
import { cloneDeep } from "lodash";
import {
  prepayMonthSummaryApi,
  prepayMonthSummaryDetailApi,
  prepayMonthSummaryDetailSummaryApi,
  prepayMonthSummaryListApi,
} from "@/api/finance";

export default {
  name: "FinancingMonthlyProfit",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "monthly",
          title: "月份",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "monthrange",
          valueFormat: "yyyy-MM",
          pickerFormat: "yyyy-MM",
        },
        {
          dataIndex: "totalIncome",
          title: "总收入",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "totalExpenditure",
          title: "总支出",
          isTable: true,
          isSearch: true,
          valueType: "inputRange",
        },
        {
          dataIndex: "actualProfit",
          title: "毛利",
          isTable: true,
        },
        {
          dataIndex: "actualReceipt",
          title: "抄表费",
          isTable: true,
        },
        {
          dataIndex: "mechanicalNums",
          title: "机器台数",
          isTable: true,
        },
        {
          dataIndex: "repairTimes",
          title: "维修次数",
          isTable: true,
        },
        {
          dataIndex: "blackNums",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "colorNums",
          title: "彩色印量",
          isTable: true,
        },
        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          tooltip: false,
          width: 100,
        },
      ],
      tableData: [],
      drawerVisible: false,
      drawerTitle: "月度毛利",
      monthly: "",
      editType: "info",
      confirmLoading: false,
      formParam: {},
      formColumns: [
        {
          dataIndex: "customerName",
          title: "客户名称",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "customerCode",
          title: "客户编号",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "monthly",
          title: "月份",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "actualProfit",
          title: "毛利",
          isForm: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "totalExpenditure",
          title: "总收入",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "actualReceipt",
          title: "抄表收入",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "carbon",
          title: "碳粉收入",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "totalIncome",
          title: "总支出",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "part",
          title: "零件成本",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "carbon1",
          title: "碳粉成本",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "blackNums",
          title: "黑白印量",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "colorPrint",
          title: "彩色印量",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "repairTimes",
          title: "维修次数",
          isForm: true,
          valueType: "text",
          formSpan: 8,
        },
        {
          dataIndex: "detail",
          title: "明细",
          isForm: true,
          formOtherSlot: "detail",
          formSpan: 24,
        },
      ],
      detailColumns: [
        {
          dataIndex: "name",
          title: "客户名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "seqId",
          title: "客户编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "readingReceivable",
          title: "抄表应收",
          isTable: true,
        },
        {
          dataIndex: "actualReceipt",
          title: "抄表实收",
          isTable: true,
        },
        // {
        //   dataIndex: "halfCarbon",
        //   title: "半保机碳粉收入",
        //   isTable: true,
        // },
        {
          dataIndex: "costConsumables",
          title: "耗材成本",
          isTable: true,
        },
        {
          dataIndex: "costPart",
          title: "零件成本",
          isTable: true,
        },
        {
          dataIndex: "tonerIncome",
          title: "碳粉收入",
          isTable: true,
        },
        {
          dataIndex: "costIncome",
          title: "零件收入",
          isTable: true,
        },
        {
          dataIndex: "readingProfit",
          title: "应收毛利",
          isTable: true,
          sortable: true,
          sortMethod: (a, b) => {
            return a.readingProfit - b.readingProfit;
          },
        },
        {
          dataIndex: "actualProfit",
          title: "实收毛利",
          isTable: true,
        },
        {
          dataIndex: "differenceProfit",
          title: "毛利差额",
          isTable: true,
        },
        {
          dataIndex: "repairTimes",
          title: "维修次数",
          isTable: true,
        },
        {
          dataIndex: "blackNums",
          title: "黑白印量",
          isTable: true,
          sortable: true,
          sortMethod: (a, b) => {
            return a.blackNums - b.blackNums;
          },
        },
        {
          dataIndex: "colorNums",
          title: "彩色印量",
          isTable: true,
          sortable: true,
          sortMethod: (a, b) => {
            return a.colorNums - b.colorNums;
          },
        },
        // {
        //   dataIndex: "action",
        //   title: "操作",
        //   isTable: true,
        //   tableSlot: "action",
        // },
      ],
      detailTableData: [],
      hasMore: true,
      detailQueryParam: {},
      detailLocalPagination: {
        pageNumber: 1,
        pageSize: 20,
        total: 0,
      },
      // 客户机器列表
      dialogVisible: false,
      machineColumns: [
        {
          dataIndex: "deviceGroup",
          title: "设备组名称",
          isTable: true,
          formatter: (row) => row.deviceGroup?.label,
        },
        {
          dataIndex: "productInfo",
          title: "品牌机型",
          isTable: true,
        },
        {
          dataIndex: "serType",
          title: "服务类型",
          isTable: true,
          formatter: (row) => row.serType?.label,
        },
        {
          dataIndex: "meterReadingReceivable",
          title: "抄表应收",
          isTable: true,
        },
        {
          dataIndex: "meterReadingIsCollected",
          title: "抄表实收",
          isTable: true,
        },
        {
          dataIndex: "halfCarbon",
          title: "半保机碳粉收入",
          isTable: true,
        },
        {
          dataIndex: "itemCost",
          title: "耗材成本",
          isTable: true,
        },
        {
          dataIndex: "partCost",
          title: "零件成本",
          isTable: true,
        },
        {
          dataIndex: "grossProfitReceivable",
          title: "应收计算毛利",
          isTable: true,
        },
        {
          dataIndex: "grossProfitReceived",
          title: "实收计算毛利",
          isTable: true,
        },
        {
          dataIndex: "grossMarginDifferential",
          title: "毛利差额",
          isTable: true,
        },
        {
          dataIndex: "repairTimes",
          title: "维修次数",
          isTable: true,
        },
        {
          dataIndex: "blackNums",
          title: "黑白印量",
          isTable: true,
        },
        {
          dataIndex: "color",
          title: "彩色印量",
          isTable: true,
        },
      ],
      machineTableData: [],
      totalData: {},
      statLoading: false,
      detailTotalData: {},
      desc: null, // 是否倒叙排序
      orderBy: null, // 排序字段
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startMonth: null,
          endMonth: null,
          data: parameter.monthly,
        },
        {
          startTotalExpenditure: null,
          endTotalExpenditure: null,
          data: parameter.totalExpenditure,
        },
        {
          startTotalIncome: null,
          endTotalIncome: null,
          data: parameter.totalIncome,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      ["monthly", "totalExpenditure", "totalIncome"].forEach(
        (key) => delete requestParameters[key]
      );

      prepayMonthSummaryListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
      this.getTotalData(requestParameters);
    },
    getTotalData(params) {
      this.statLoading = false;
      prepayMonthSummaryApi(params)
        .then((res) => {
          this.totalData = res.data;
        })
        .finally(() => {
          this.statLoading = true;
        });
    },
    handleEdit(row, type) {
      this.detailQueryParam = {};
      this.editType = type;

      this.drawerTitle = `月度毛利 - ${row.monthly}详情`;
      this.monthly = row.monthly;
      this.detailLocalPagination = {
        pageNumber: 1,
        pageSize: 20,
        total: 0,
      };
      this.hasMore = true;
      this.drawerVisible = true;
      this.$nextTick(() => {
        this.$refs.DetailTable.refresh();
      });
    },
    // 查看月度毛利明细
    handleViewDetail(row) {
      this.dialogVisible = true;
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },

    // 处理数据加载
    handleLoadData(params) {
      this.detailQueryParam = filterParam(
        Object.assign({}, this.detailQueryParam, params)
      );
      const requestParameters = cloneDeep(this.detailQueryParam);
      requestParameters.month = this.monthly;
      requestParameters.orderBy = this.orderBy;
      requestParameters.desc = this.desc;
      prepayMonthSummaryDetailApi(filterParam(requestParameters))
        .then((res) => {
          if (params.pageNumber === 1) {
            this.detailTableData = res.data.rows;
            this.detailLocalPagination.pageNumber = 1;
          } else {
            this.detailTableData = [...this.detailTableData, ...res.data.rows];
            this.detailLocalPagination.pageNumber = params.pageNumber;
          }
          this.detailLocalPagination.total = +res.data.total;

          // 判断是否还有更多数据
          this.hasMore =
            this.detailTableData.length < this.detailLocalPagination.total;
          this.$refs.DetailTable && this.$refs.DetailTable.resetScrolling();
        })
        .finally(() => {
          this.$refs.DetailTable &&
            (this.$refs.DetailTable.listLoading = false);
        });
      this.getDetailTotalData(requestParameters);
    },
    // 排序
    sortChange({ prop, order }) {
      if (order === "ascending") {
        this.desc = false;
      }
      if (order === "descending") {
        this.desc = true;
      }
      this.orderBy = prop;
      this.$refs.DetailTable.refresh();
    },
    getDetailTotalData(params) {
      prepayMonthSummaryDetailSummaryApi(params).then((res) => {
        this.detailTotalData = res.data;
      });
    },
    handleDialogCancel() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style scoped lang="scss"></style>
