<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:52
 * @Description: 部门管理
 -->

<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :columns="columns"
      show-pagination
      row-key="id"
      :data="tableData"
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          class="add-btn"
          size="mini"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增部门
        </el-button>
      </template>
      <template #type="slotProps">
        {{ slotProps.row.type.label }}
      </template>
      <template #actions="slotProps">
        <span class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-circle-plus"
            @click="handleAdd(slotProps.row)"
          >
            添加
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-user-solid"
            @click="handleMember(slotProps.row)"
          >
            人员管理
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-warning-outline"
            @click="handleInfo(slotProps.row)"
          >
            详情
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            编辑
          </el-button>

          <el-button
            v-if="slotProps.row.status != 'deleted'"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row, slotProps.index)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>

    <!-- 新增、编辑、详情框  -->
    <ProDialog
      :value="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :confirm-loading="confirmLoading"
      :top="'10%'"
      :no-footer="methodType === 'info'"
      :confirm-text="methodType === 'add' ? '确认新增' : '保存'"
      @ok="handleDialogOk"
      @cancel="closedialog"
    >
      <ProForm
        v-if="dialogVisible"
        ref="proform"
        :form-param="form"
        :form-list="formcolumns"
        :confirm-loading="confirmLoading"
        :layout="{ formWidth: '100%', labelWidth: '140px' }"
        :open-type="methodType"
        @proSubmit="proSubmit"
      >
      </ProForm>
    </ProDialog>
    <!-- 人员管理 -->
    <ChooseUser
      ref="ChooseUser"
      :dialog-visible="dialogVisibleR"
      :dialog-title="dialogTitleR"
      :role-id="roleId"
      :type="'depart'"
      @add="handleMemberAdd"
      @cancel="dialogVisibleR = false"
    ></ChooseUser>
    <StaffList
      ref="UserList"
      :dialog-visible="dialogVisibleU"
      :role-id="roleId"
      @ok="memberAdd"
      @cancel="dialogVisibleU = false"
    ></StaffList>
  </div>
</template>
<script>
import {
  departListApi,
  departAddApi,
  departDelApi,
  departEditApi,
  departMemberAddApi,
} from "@/api/user";

import { isEmpty, cloneDeep } from "lodash";
import ChooseUser from "./components/ChooseUser.vue";
import StaffList from "./components/StaffList.vue";

export default {
  name: "Depart",
  components: {
    ChooseUser,
    StaffList,
  },
  mixins: [],
  props: {},
  data() {
    return {
      active: 9,
      // 列表
      spareiTypeList: [],
      tableData: [],
      userCompanyInfo: JSON.parse(localStorage.getItem("userCompanyInfo")),
      queryParam: {
        aduitState: null,
        name: null,
      },
      columns: [
        {
          dataIndex: "name",
          title: "部门名称",
          isTable: true,
          isSearch: true,
          span: 4,
          valueType: "input",
        },
        {
          dataIndex: "code",
          title: "部门编码",
          isTable: true,
        },

        // {
        //   dataIndex: "isEnable",
        //   title: "状态",
        //   width: 200,
        //   isTable: true,
        // },

        {
          dataIndex: "Actions",
          width: 400,
          fixed: "right",
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      actionUrl: "",
      uploadlLading: false,
      uploadDialogVisible: false,
      //新增
      methodType: "add",
      confirmLoading: false,

      form: { parentId: "" },
      dialogTitle: "",
      dialogVisible: false,
      defaultFormParams: {},
      formcolumns: [
        {
          dataIndex: "name",
          isForm: true,
          title: "部门名称",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入部门名称",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "code",
          isForm: true,
          title: "部门编码",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入部门编码",
              trigger: "change",
            },
          ],
        },
        {
          dataIndex: "sort",
          isForm: true,
          title: "排序",
          valueType: "input",
          clearable: true,
          formSpan: 24,
          prop: [
            {
              required: true,
              message: "请输入排序",
              trigger: "change",
            },
          ],
        },
      ],
      //人员管理
      roleId: null,
      dialogTitleR: "",
      dialogVisibleR: false,
      dialogVisibleU: false,
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {
    this.$refs.ProTable.refresh();
  },
  methods: {
    getCode() {},
    //加载表格
    loadData(parameter) {
      const name = parameter.name;

      const requestParameters = Object.assign({}, parameter);
      departListApi(requestParameters)
        .then((res) => {
          if (!name || name === null || name === "") {
            this.tableData = res.data;
          } else {
            this.tableData = this.filterSearch(res.data, name);
          }
        })
        .finally(() => {
          this.$refs.ProTable
            ? (this.$refs.ProTable.listLoading = false)
            : null;
        })
        .catch(() => {});
    },
    // 过滤搜索
    filterSearch(data, keyword) {
      if (!data) return [];
      return data.filter((item) => item.name.includes(keyword));
    },
    //初始化表单
    resetFrom() {
      this.form = cloneDeep(this.defaultFormParams);
    },
    //触发表单提交
    handleDialogOk() {
      this.$refs["proform"].handleSubmit();
    },
    //响应表单提交
    proSubmit(val) {
      this.form = cloneDeep(val);
      this.confirmLoading = true;
      this.methodType === "add" ? this.create() : this.update();
    },
    closedialog() {
      this.dialogVisible = false;
    },
    //触发新增
    handleAdd(data) {
      this.dialogTitle = "部门新增";
      this.methodType = "add";
      this.resetFrom();
      this.dialogVisible = true;

      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
        data?.id ? (this.form.parentId = data.id) : (this.form.parentId = null);
      });
    },
    //响应新增
    create() {
      departAddApi(this.form)
        .then(() => {
          this.$message.success("新增成功");
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    //触发编辑
    handleUpdate(row) {
      this.dialogTitle = "编辑 - " + row.name;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.methodType = "edit";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },
    //响应编辑
    update() {
      departEditApi(this.form)
        .then(() => {
          this.$message.success("修改成功");
        })
        .finally(() => {
          this.confirmLoading = false;
          this.dialogVisible = false;
          this.$refs.ProTable.refresh();
        });
    },

    // 触发详情
    handleInfo(row) {
      this.dialogTitle = "查看 - " + row.name;
      this.resetFrom();
      this.form = cloneDeep(row);
      this.methodType = "info";
      this.dialogVisible = true;
      this.$nextTick((e) => {
        this.$refs["proform"].resetFormParam();
      });
    },

    //响应删除
    handleDelete(data) {
      this.$confirm("确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        departDelApi(data.id)
          .then(() => {
            this.$message.success("删除成功");
          })
          .finally(() => {
            this.$refs.ProTable.refresh();
          });
      });
    },
    // ================
    handleMember(row) {
      this.dialogTitleR = "人员管理 - " + row.name;
      this.dialogVisibleR = true;
      this.roleId = row.id;
      setTimeout(() => {
        this.$refs.ChooseUser.$refs.ProTable.refresh();
      }, 200);
    },
    handleMemberAdd() {
      this.dialogVisibleU = true;
      setTimeout(() => {
        this.$refs.UserList.$refs.ProTable.refresh();
      }, 200);
    },
    //响应新增
    memberAdd(data) {
      departMemberAddApi(this.roleId, data).then(() => {
        this.$message.success("新增成功");
        this.dialogVisibleU = false;
        this.$refs.ChooseUser.$refs.ProTable.refresh();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  width: 150px;
  border-left: 10px solid #f5f5f5;
  border-right: 10px solid #f5f5f5;
  padding: 10px;
  height: calc(100vh - 100px);
  background: #fff;
  float: left;
  margin-right: 24px;
  overflow-y: scroll;

  .tit {
    width: 100%;
    line-height: 40px;
    background: #fff;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #edf2f2;

    &.active {
      background: linear-gradient(90deg, #036e99, #23abc8);
      color: #fff;
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.table-right {
  width: calc(100% - 284px);
  height: 100%;
  float: left;
}

.content-title {
  border-bottom: 1px solid rgb(230, 230, 230);
  line-height: 40px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  font-size: 16px;
}

.content-box {
  border: 1px solid rgb(230, 230, 230);
}

.checked {
  background-color: #e1e4e9;
  color: #409eff !important;
}

.table-key-search {
  position: relative;
  z-index: 5;
}

.ldtit {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 50px;
  padding-left: 30px;
}

.list,
.key-table {
  border: 1px solid #e1e4e9;
  border-radius: 4px;
}

.list {
  border-radius: 0 0 4px 4px;
  margin-top: -4px;
  border-top: none;

  .list-item {
    cursor: pointer;
    padding: 5px 10px;
    line-height: 30px;
    width: 100%;
    border-bottom: 1px solid #e1e4e9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
  }

  .list-item:hover {
    background-color: #eff0f1;
  }
}

.padding-box {
  padding: 0 24px;
  overflow-y: scroll;
  height: 100%;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.des-item {
  width: 200px;
}

.info-text {
  width: 100%;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
