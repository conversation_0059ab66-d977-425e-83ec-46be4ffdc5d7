<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-04-15 13:39:05
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-25 10:28:46
 * @FilePath: src/views/dispose/components/payee.vue
 * @Description: 收款配置
 * 
-->

<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="page-header">
        <h2>收款配置</h2>
        <p class="page-description">配置公司收款账户信息和收款二维码</p>
      </div>

      <el-divider content-position="left">公司账户信息</el-divider>
      <el-form :model="baseInfo" label-width="120px" class="account-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="公司名称">
              <el-input
                v-model="baseInfo.companyName"
                placeholder="请输入公司名称"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="小程序名称">
              <el-input
                v-model="baseInfo.programName"
                placeholder="请输入小程序名称"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户名称">
              <el-input
                v-model="baseInfo.accountName"
                placeholder="请输入账户名称"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户银行">
              <el-input
                v-model="baseInfo.bankName"
                placeholder="请输入开户银行"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户号码">
              <el-input
                v-model="baseInfo.accountNumber"
                placeholder="请输入账户号码"
              >
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="客服电话">
              <el-input
                v-model="baseInfo.phoneNumber"
                placeholder="请输入客服电话"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-divider content-position="left">图片配置</el-divider>
      <div class="qr-code-section">
        <el-row :gutter="30">
          <el-col :span="6">
            <div class="qr-code-item">
              <h4>公司LOGO</h4>
              <ProUpload
                class="qr-uploader"
                :file-list="baseInfo.logoImage"
                :limit="1"
                @uploadSuccess="(e) => handleUploadSuccess(e, 'logoImage')"
                @uploadRemove="(e) => handleUploadRemove(e, 'logoImage')"
              />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="qr-code-item">
              <h4>微信二维码</h4>
              <ProUpload
                class="qr-uploader"
                :file-list="baseInfo.wechatQrcode"
                :limit="1"
                @uploadSuccess="(e) => handleUploadSuccess(e, 'wechatQrcode')"
                @uploadRemove="(e) => handleUploadRemove(e, 'wechatQrcode')"
              />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="qr-code-item">
              <h4>支付宝二维码</h4>
              <ProUpload
                class="qr-uploader"
                :file-list="baseInfo.aliQrcode"
                :limit="1"
                @uploadSuccess="(e) => handleUploadSuccess(e, 'aliQrcode')"
                @uploadRemove="(e) => handleUploadRemove(e, 'aliQrcode')"
              />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="qr-code-item">
              <h4>联合二维码</h4>
              <ProUpload
                class="qr-uploader"
                :file-list="baseInfo.paymentQrcode"
                :limit="1"
                @uploadSuccess="(e) => handleUploadSuccess(e, 'paymentQrcode')"
                @uploadRemove="(e) => handleUploadRemove(e, 'paymentQrcode')"
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="form-actions">
        <el-button type="primary" size="small" @click="saveConfig">
          保存配置
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import ProUpload from "@/components/ProUpload/index.vue";
export default {
  name: "Payee",
  components: {
    ProUpload,
  },
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  computed: {
    baseInfo: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  created() {
    const defaultAccountForm = {
      companyName: "", // 公司名称
      programName: "", // 小程序名称
      accountName: "", // 账户名称
      bankName: "", // 账户名称
      accountNumber: "", // 开户银行
      phoneNumber: "", // 客服电话
      aliQrcode: [],
      wechatQrcode: [],
      paymentQrcode: [],
      logoImage: [],
    };
    this.$emit("input", {
      ...defaultAccountForm,
      ...this.value,
    });
  },
  methods: {
    handleUploadSuccess(result, type) {
      if (!this.baseInfo[type]) {
        this.$set(this.baseInfo, type, []);
      }
      this.baseInfo[type].push(result);
    },
    handleUploadRemove(file, type) {
      const index = this.baseInfo[type].findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.baseInfo[type].splice(index, 1);
    },
    saveConfig() {
      this.$emit("save", this.baseInfo);
      // setBaseCompanyInfoApi(this.baseInfo).then((res) => {
      //   console.log(res);
      //   this.$message.success("配置保存成功");
      //   this.$emit("update:value", res.data);
      // });
    },
  },
};
</script>

<style scoped lang="scss">
.page-header {
  margin-bottom: 30px;

  h2 {
    margin: 0;
    font-size: 20px;
    color: #303133;
  }

  .page-description {
    margin: 10px 0 0;
    font-size: 14px;
    color: #909399;
  }
}

.account-form {
  margin: 20px 0 40px;
}

.qr-code-section {
  margin: 20px 0 40px;

  h3 {
    margin-bottom: 20px;
  }
}

.qr-code-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
  height: 100%;

  h4 {
    margin: 0 0 20px;
    font-size: 16px;
    color: #303133;
  }
}

.qr-uploader {
  margin: 0 auto;
  width: 100%;
}

.form-actions {
  margin-top: 40px;
  text-align: center;
}

:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  background-color: #fff;
  padding: 0 20px;
}
</style>
