<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-07-25 13:59:47
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-25 14:20:33
 * @Description: 配送方式变更记录
 -->
<template>
  <div class="container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button
            v-if="!row.isFeeProcessed"
            icon="el-icon-success"
            @click="handleEdit(row)"
          >
            已处理
          </el-button>
        </div>
      </template>
    </ProTable>
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import {
  getDeliveryMethodChangeRecordApi,
  handleDeliveryMethodChangeRecordA<PERSON>,
} from "@/api/operator";

export default {
  name: "DispatchChange",
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "isFeeProcessed",
          title: "状态",
          isTable: true,
          formatter: (row) => (row.isFeeProcessed ? "已处理" : "未处理"),
          isSearch: true,
          valueType: "select",
          option: [
            {
              label: "待处理",
              value: 0,
            },
            {
              label: "已处理",
              value: 1,
            },
          ],
          width: 80,
        },
        {
          dataIndex: "orderNumber",
          title: "订单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "outWarehouseId",
          title: "出库单号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "originalLogisticsProvider",
          title: "原配送方式",
          isTable: true,
          formatter: (row) => row.originalLogisticsProvider?.label,
        },
        {
          dataIndex: "originalShippingFee",
          title: "原配送费用",
          isTable: true,
        },
        {
          dataIndex: "newLogisticsProvider",
          title: "新配送方式",
          isTable: true,
          formatter: (row) => row.newLogisticsProvider?.label,
        },
        {
          dataIndex: "newShippingFee",
          title: "新配送费用",
          isTable: true,
        },
        {
          dataIndex: "shippingFeeDiff",
          title: "费用差额",
          isTable: true,
        },
        {
          dataIndex: "createdBy",
          title: "修改人",
          isTable: true,
          formatter: (row) => row.createdBy?.name,
        },
        {
          dataIndex: "createdAt",
          title: "修改时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "processRemark",
          title: "备注",
          isTable: true,
          width: 200,
        },
        {
          dataIndex: "actions",
          title: "操作",
          isTable: true,
          tableSlot: "action",
          width: 100,
        },
      ],
      tableData: [],
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      getDeliveryMethodChangeRecordApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = parseInt(res.data.total);
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row) {
      this.$prompt("请输入备注", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(({ value }) => {
        handleDeliveryMethodChangeRecordApi(row.id, 1, value).then((res) => {
          this.$message.success("修改成功");
          this.refresh();
        });
      });
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss"></style>
