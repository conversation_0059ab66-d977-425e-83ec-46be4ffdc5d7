<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-14 17:17:31
 * @Description: 设备和零件
 -->

<template>
  <div class="view app-container">
    <el-tabs v-model="aType" style="padding: 10px">
      <el-tab-pane label="零件列表" name="零件列表" lazy>
        <Part />
      </el-tab-pane>
      <el-tab-pane label="设备列表" name="设备列表" lazy>
        <Device />
      </el-tab-pane>
      <el-tab-pane label="选配件列表" name="选配件列表" lazy>
        <Spare />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import Device from "./components/device.vue";
import Part from "./components/part.vue";
import Spare from "./components/spare.vue";

export default {
  name: "Equip",
  components: {
    Device,
    Part,
    Spare,
  },
  mixins: [],
  props: {},
  data() {
    return {
      aType: "零件列表",
      // 列表
    };
  },

  computed: {},

  watch: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="scss" scoped></style>
