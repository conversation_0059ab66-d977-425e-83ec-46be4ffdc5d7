<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-09 15:10:54
 * @Description: 
 -->
<template>
  <div class="view app-container assoc">
    <ProTable
      ref="ProTable"
      row-key="id"
      :data="tableData"
      :columns="columns"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :local-pagination="localPagination"
      show-pagination
      sticky
      :query-param="queryParam"
      @loadData="loadData"
    >
      <!--      <template #priceSlot="slotProps">-->
      <!--        <div v-if="!slotProps.row.price">/</div>-->
      <!--        <el-input-->
      <!--          v-else-->
      <!--          v-model="slotProps.row.price"-->
      <!--          type="number"-->
      <!--          style="width: 50%"-->
      <!--        ></el-input>-->
      <!--      </template>-->
      <template #actions="slotProps">
        <span v-if="slotProps.row.parentArticleId !== '0'" class="fixed-width">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit-outline"
            @click="handleUpdate(slotProps.row)"
          >
            修改
          </el-button>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="handleDelete(slotProps.row.id)"
          >
            删除
          </el-button>
        </span>
      </template>
    </ProTable>
    <ProDialog
      :value="dialogVisible"
      title="修改金额"
      width="500px"
      :confirm-btn-loading="confirmBtnLoading"
      @ok="handleDialogOK"
      @cancel="handleDialogCancel"
    >
      <el-form
        ref="ruleForm"
        :model="formParams"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="售价：" prop="price">
          <el-input v-model="formParams.price" type="number"></el-input>
        </el-form-item>
        <el-form-item label="采购金额：">
          <el-input v-model="formParams.purchasePrice" type="number"></el-input>
        </el-form-item>
      </el-form>
    </ProDialog>
  </div>
</template>
<script>
import {
  combinationItemConfigApi,
  updateCombinationItemConfigApi,
  deleteCombinationItemConfigApi,
} from "@/api/store";
import { Message } from "element-ui";
import { cloneDeep } from "lodash";
import { filterParam } from "@/utils";

export default {
  name: "ItemConfig",
  data() {
    return {
      tableData: [],
      queryParam: {},
      columns: [
        {
          dataIndex: "articleName",
          title: "物品名称",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "articleCode",
          title: "物品编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "oemNumber",
          title: "OEM编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 180,
        },
        {
          dataIndex: "price",
          title: "售价",
          isTable: true,
          // tableSlot: "priceSlot",
          formatter: (row) => (row.price ? row.price : "/"),
        },
        {
          dataIndex: "purchasePrice",
          title: "采购价格",
          isTable: true,
          // tableSlot: "priceSlot",
          formatter: (row) => (row.purchasePrice ? row.purchasePrice : "/"),
        },
        {
          dataIndex: "Actions",
          width: 300,
          title: "操作",
          align: "left",
          isTable: true,
          tooltip: false,
          tableSlot: "actions",
        },
      ],
      localPagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      dialogVisible: false,
      formParams: {},
      formRules: {
        price: [
          {
            required: true,
            message: "请输入商品售价",
            trigger: "blur",
          },
        ],
      },
      confirmBtnLoading: false,
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    //加载表格
    async loadData(params) {
      try {
        const result = await combinationItemConfigApi(params);
        if (result.code === 200 && result.data) {
          console.log(result, "result");
          this.tableData = result.data.rows;
          this.localPagination.total = +result.data.total;
        }
      } catch (error) {
        Message.error(error.message);
      } finally {
        this.$refs.ProTable.listLoading = false;
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
    handleDialogOK() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.confirmBtnLoading = true;
          updateCombinationItemConfigApi(filterParam(this.formParams))
            .then((res) => {
              Message.success("操作成功");
              this.dialogVisible = false;
              this.refresh();
            })
            .finally(() => {
              this.confirmBtnLoading = false;
            });
        }
      });
    },
    handleDialogCancel() {
      this.dialogVisible = false;
    },
    handleUpdate(row) {
      this.formParams = {};
      this.formParams = cloneDeep(row);
      this.dialogVisible = true;
      // this.$prompt("请输入修改的金额", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   showInput: true,
      //   inputPlaceholder: "请输入修改金额",
      //   inputType: "number",
      //   inputValue: row.price,
      //   inputErrorMessage: "请输入修改金额",
      //   inputValidator: (value) => {
      //     if (!value) {
      //       return "请输入修改金额";
      //     }
      //   },
      // })
      //   .then(({ value }) => {
      //     updateCombinationItemConfigApi({
      //       id: row.id,
      //       price: value,
      //     })
      //       .then((res) => {
      //         Message.success(res.message);
      //         this.refresh();
      //       })
      //       .finally(() => {
      //         this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
      //       });
      //   })
      //   .catch(() => {});
    },
    handleDelete(id) {
      this.$confirm("此操作将永久删除该物品, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          console.log(id);
          deleteCombinationItemConfigApi(id)
            .then((res) => {
              Message.success("删除成功");
              this.refresh();
            })
            .finally(() => {
              this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
            });
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped></style>
