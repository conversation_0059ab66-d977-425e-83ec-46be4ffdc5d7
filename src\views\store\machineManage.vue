<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-03-05 15:12:33
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-08-01 15:06:21
 * @Description: 
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      show-rule
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #btn>
        <el-button
          type="success"
          icon="el-icon-plus"
          size="mini"
          @click="handleEdit(null, 'add')"
        >
          新增机器
        </el-button>
        <el-button
          v-auth="['@ums:manage:machine:upload']"
          type="success"
          icon="el-icon-upload2"
          size="mini"
          @click="$refs.uploadExcel.show()"
        >
          导入数据
        </el-button>
        <el-button
          v-auth="['@ums:manage:machine:download']"
          type="success"
          :loading="exportLoading"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <div class="title-box-right" style="font-size: 14px; gap: 10px">
          <div>总金额：{{ totalData?.totalAmount || 0 }}</div>
          <div>库存数量：{{ totalData?.stockNumber || 0 }}</div>
          <div>库存金额：{{ totalData?.stockAmount || 0 }}</div>
          <div>主机金额：{{ totalData?.machineAmount || 0 }}</div>
          <div>选配件金额：{{ totalData?.accessoryAmount || 0 }}</div>
          <div>租赁数量：{{ totalData?.rentNumber || 0 }}</div>
          <div>租赁金额：{{ totalData?.rentAmount || 0 }}</div>
        </div>
      </template>
      <template #rule>
        <div class="rules-tips">
          <h3 class="rule-title">各项统计数据计算规则</h3>

          <ol>
            <li>
              <div class="rule-item">
                <span class="rule-number">总金额：</span>
                <span class="rule-text">
                  所有机器
                  <span class="highlight">采购价格</span> 的总和
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">库存数量：</span>
                <span class="rule-text">
                  当前 <span class="highlight">状态</span> 处于
                  <span class="warning">已入库</span>、
                  <span class="warning">维修中</span>
                  的机器数量总和
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">库存金额：</span>
                <span class="rule-text">
                  当前 <span class="highlight">状态</span> 处于
                  <span class="warning">已入库</span>、
                  <span class="warning">维修中</span>
                  的机器采购价格总和
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">主机金额：</span>
                <span class="rule-text">
                  <span class="highlight">主机类型</span> 为
                  <span class="warning">主机</span>， 且
                  <span class="highlight">状态</span> 处于
                  <span class="warning">已入库</span>、
                  <span class="warning">维修中</span>
                  的机器采购价格总和
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">选配件金额：</span>
                <span class="rule-text">
                  <span class="highlight">主机类型</span> 不为
                  <span class="warning">主机</span>， 且
                  <span class="highlight">状态</span> 处于
                  <span class="warning">已入库</span>、
                  <span class="warning">维修中</span>
                  的机器采购价格总和
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">租赁数量：</span>
                <span class="rule-text">
                  当前 <span class="highlight">状态</span> 处于
                  <span class="warning">租赁</span>
                  的机器数量总和
                </span>
              </div>
            </li>

            <li>
              <div class="rule-item">
                <span class="rule-number">租赁金额：</span>
                <span class="rule-text">
                  当前 <span class="highlight">状态</span> 处于
                  <span class="warning">租赁</span>
                  的机器采购价格总和
                </span>
              </div>
            </li>
          </ol>
        </div>
      </template>
      <template #action="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
          <!--TODO: 暂时所有数据都可编辑-->
          <!--v-if="row.status?.value !== 'OVER_SALE'"-->
          <el-button icon="el-icon-edit" @click="handleEdit(row, 'edit')">
            编辑
          </el-button>
          <!--<el-button icon="el-icon-document"> 修改记录 </el-button>-->
        </div>
      </template>
      <template #picsUrl="{ row }">
        <el-image
          v-if="row.picsUrl && row.picsUrl.length > 0"
          style="max-width: 100px; max-height: 100px"
          :src="getPicsUrlImg(row)"
          :preview-src-list="[getPicsUrlImg(row)]"
        ></el-image>
      </template>
      <template #location="{ row }">
        <el-input
          v-model="row.location"
          placeholder="请输入储位"
          type="text"
          size="small"
          :disabled="!row.isedit"
          style="width: 100px"
          @click="row.isedit = true"
        ></el-input>

        <el-link
          v-if="!row.isedit"
          style="margin-left: 10px"
          icon="el-icon-edit"
          :underline="false"
          @click="confirmEdit(row)"
        >
          编辑
        </el-link>
        <el-link
          v-if="row.isedit"
          style="margin-left: 10px"
          :underline="false"
          @click="changeLocation(row)"
        >
          保存
        </el-link>
        <el-link
          v-if="row.isedit"
          style="margin-left: 10px"
          :underline="false"
          type="info"
          @click="changeLocation()"
        >
          取消
        </el-link>
      </template>
      <template #isSale="{ row }">
        <el-switch
          v-model="row.isSale"
          active-color="#13ce66"
          inactive-color="#ff4949"
          :active-value="true"
          :inactive-value="false"
          disabled
          @change="handleIsSaleChange"
        ></el-switch>
      </template>
    </ProTable>
    <!-- 查看、编辑Drawer -->
    <ProDrawer
      :value="drawerVisible"
      size="80%"
      :title="drawerTitle"
      :confirm-loading="false"
      :confirm-button-disabled="confirmLoading"
      :no-footer="methodType === 'info'"
      :confirm-text="methodType === 'add' ? '确认新增' : '保存'"
      @ok="handleDrawerOk"
      @cancel="closeDrawer"
    >
      <ProForm
        ref="ProForm"
        :confirm-loading="confirmLoading"
        :form-param="formParam"
        :form-list="formColumns"
        :layout="{ formWidth: '100%', labelWidth: '120px' }"
        :open-type="methodType"
        @proSubmit="formSubmit"
      >
        <template #base>
          <p class="tit-box m-b-12" style="margin-top: 0">基础信息</p>
        </template>
        <template #hostType>
          <el-select
            v-model="formParam.hostType"
            style="width: 100%"
            placeholder="请选择主机类型"
            :disabled="methodType === 'info'"
            @change="handleHostTypeChange"
          >
            <el-option
              v-for="item in hostTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </template>
        <!-- 机器/系列 -->
        <template #fullIdPath>
          <el-cascader
            ref="ProductIds"
            v-model="formParam.fullProductPath"
            filterable
            clearable
            :options="options"
            style="width: 100%"
            :disabled="methodType === 'info'"
            :show-all-levels="false"
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
              expandTrigger: 'click',
            }"
            leaf-only
            collapse-tags
            @change="handleSelectForm"
          ></el-cascader>
        </template>
        <template #productName>
          <div>
            <!--<el-input-->
            <!--  v-model="formParam.productName"-->
            <!--  placeholder="请选择选配件"-->
            <!--  :disabled="true"-->
            <!--&gt;-->
            <!--</el-input>-->
            <!--           v-if="methodType !== 'info'"-->
            <el-button
              type="text"
              size="small"
              :disabled="methodType === 'info'"
              @click="handleSpareSelect"
            >
              {{ formParam.productName ? formParam.productName : "选择选配件" }}
            </el-button>
            <!--<el-button-->
            <!--  v-if="methodType === 'add'"-->
            <!--  type="text"-->
            <!--  size="small"-->
            <!--  @click="handleSpareSelect"-->
            <!--&gt;-->
            <!--  {{ formParam.productName ? formParam.productName : "选择选配件" }}-->
            <!--</el-button>-->
          </div>
        </template>
        <!-- 关联选配件 -->
        <template #deviceGroup>
          <el-tabs v-model="activeName">
            <el-tab-pane label="关联选配件" name="first" lazy>
              <HostParts
                ref="hostParts"
                v-model="formParam.accessories"
                :machine-num="formParam.machineNum"
                :edit-type="methodType"
              />
            </el-tab-pane>
            <el-tab-pane
              v-if="methodType !== 'add'"
              label="机器出入库记录"
              name="second"
              lazy
            >
              <MachineChangeLog
                ref="machineChangeLog"
                :machine-num="machineNum"
              />
            </el-tab-pane>
          </el-tabs>
        </template>
        <template #picsUrl>
          <ProUpload
            :file-list="formParam.picsUrl"
            :type="methodType"
            :limit="3"
            @uploadSuccess="(e) => handleUploadSuccess(e, 'picsUrl')"
            @uploadRemove="(e) => handleUploadRemove(e, 'picsUrl')"
          />
        </template>
      </ProForm>
    </ProDrawer>
    <!-- 选配件选择 -->
    <SparePart
      ref="sparePart"
      :dialog-visible.sync="sparePartDialog"
      :spare-type="hostType"
      @confirm="confirmSpare"
    />
    <!-- 导入 -->
    <UploadExcel
      ref="uploadExcel"
      title="导入机器数据"
      :action-url="actionUrl"
      :download-template-fun="handleDownloadTemplate"
      @uploadSuccess="handleUploadExcelSuccess"
    />
  </div>
</template>

<script>
import ProUpload from "@/components/ProUpload/index.vue";
import HostParts from "@/views/store/components/hostParts.vue";
import MachineChangeLog from "@/views/store/components/machineChangeLog.vue";
import UploadExcel from "@/components/ProUpload/excel.vue";
import SparePart from "@/views/procure/cpns/sparePart.vue";
import { cloneDeep } from "lodash";
import { filterParam, filterParamRange } from "@/utils";
import { productAllApi } from "@/api/dispose";
import {
  downloadMachineTemplateApi,
  exportMachineApi,
  getMachineDetailApi,
  getMachinePageApi,
  getMachineTotalApi,
  importMachineApi,
  manufacturerListApi,
  updateMachineApi,
  updateMachineLocationApi,
} from "@/api/store";
import { dictTreeByCodeApi } from "@/api/user";
import { handleExcelExport } from "@/utils/exportExcel";
const { uploadURL } = window.config.api;

export default {
  name: "MachineManage",
  components: {
    SparePart,
    ProUpload,
    HostParts,
    UploadExcel,
    MachineChangeLog,
  },
  data() {
    return {
      activeName: "first",
      actionUrl: uploadURL + importMachineApi,
      productIdName: [],
      options: [],
      methodType: "add",
      queryParam: {},
      localPagination: {
        pageSize: 10,
        pageNumber: 1,
        total: 0,
      },
      columns: [
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 140,
        },
        {
          dataIndex: "originCode",
          title: "原机器编号",
          isSearch: true,
          valueType: "input",
        },
        {
          dataIndex: "picsUrl",
          title: "机器图片",
          isTable: true,
          tableSlot: "picsUrl",
          tooltip: false,
          minWidth: 120,
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isTable: true,
          formatter: (row) => row.hostType?.label,
          minWidth: 120,
        },
        {
          dataIndex: "hostTypes",
          title: "主机类型",
          isSearch: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(2000),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "productName",
          title: "机器型号",
          isTable: true,
          // isSearch: true,
          // valueType: "input",
          minWidth: 120,
        },
        {
          dataIndex: "productIds",
          title: "机器型号",
          isSearch: true,
          valueType: "product",
        },
        // {
        //   dataIndex: "modeType",
        //   title: "选配件型号",
        //   isSearch: true,
        //   valueType: "input",
        // },
        {
          dataIndex: "tagName",
          title: "标签型号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 100,
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isSearch: true,
          valueType: "select",
          option: [],
          multiple: true,
          optionMth: () => dictTreeByCodeApi(2500),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "deviceSequence",
          title: "机器序列号",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 120,
        },
        // {
        //   dataIndex: "placeOrigin",
        //   title: "产地版本",
        //   isTable: true,
        //   // isSearch: true,
        //   // valueType: "input",
        // },
        // {
        //   dataIndex: "electric",
        //   title: "供电电压",
        //   isTable: true,
        //   // isSearch: true,
        //   // valueType: "input",
        // },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isTable: true,
          formatter: (row) => row.deviceOn?.label,
          minWidth: 80,
        },
        {
          dataIndex: "deviceOns",
          title: "设备新旧",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1100),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isTable: true,
          formatter: (row) => row.deviceStatus?.label,
          minWidth: 80,
        },
        {
          dataIndex: "deviceStatusList",
          title: "设备状态",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6600),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        // {
        //   dataIndex: "blackWhiteCounter",
        //   title: "黑白计数器",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        //   minWidth: 100,
        // },
        // {
        //   dataIndex: "colorCounter",
        //   title: "彩色计数器",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        //   minWidth: 100,
        // },
        // {
        //   dataIndex: "fiveColourCounter",
        //   title: "五色计数器",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "inputRange",
        //   minWidth: 100,
        // },
        {
          dataIndex: "location",
          title: "储位",
          isTable: true,
          tableSlot: "location",
          isSearch: true,
          valueType: "input",
          width: 220,
        },
        {
          dataIndex: "source",
          title: "来源",
          isTable: true,
          formatter: (row) => row.source?.label,
          minWidth: 80,
        },
        {
          dataIndex: "status",
          title: "状态",
          isTable: true,
          formatter: (row) => row.status?.label,
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            {
              label: "已入库",
              value: "INVENTORY",
            },
            {
              label: "已售",
              value: "OVER_SALE",
            },
            {
              label: "维修中",
              value: "REPAIR",
            },
            {
              label: "租赁",
              value: "RENT",
            },
            {
              label: "报损",
              value: "WITHOUT",
            },
            {
              label: "采购退货中",
              value: "APPLY_RETURN",
            },
            {
              label: "采购退货",
              value: "RETURN",
            },
            // {
            //   label: "已拆机",
            //   value: "DISASSEMBLED",
            // },
          ],
          minWidth: 95,
        },
        // {
        //   dataIndex: "color",
        //   title: "色彩类型",
        //   isTable: true,
        //   formatter: (row) => row.color?.label,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [],
        //   optionMth: () => dictTreeByCodeApi(1700),
        //   optionskey: {
        //     label: "label",
        //     value: "value",
        //   },
        // },
        {
          dataIndex: "createdAt",
          title: "入库时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商",
          isTable: true,
          isSearch: true,
          valueType: "input",
          minWidth: 250,
        },

        {
          dataIndex: "action",
          title: "操作",
          isTable: true,
          width: 140,
          tableSlot: "action",
          tooltip: false,
          fixed: "right",
        },
      ],
      tableData: [],
      drawerVisible: false,
      drawerTitle: "新增机器",
      formParam: {},
      formColumns: [
        {
          dataIndex: "base",
          title: "基本信息",
          isForm: true,
          formOtherSlot: "base",
          formSpan: 24,
        },
        {
          dataIndex: "machineNum",
          title: "机器编号",
          isForm: true,
          // disabled: true,
          valueType: "text",
          formSpan: 6,
        },
        {
          dataIndex: "originCode",
          title: "原机器编号",
          isForm: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          dataIndex: "hostType",
          title: "主机类型",
          isForm: true,
          // disabled: true,
          formSpan: 6,
          formSlot: "hostType",
          // valueType: "select",
          // option: [],
          // optionMth: () => dictTreeByCodeApi(2000),
          // optionskey: {
          //   label: "label",
          //   value: "value",
          // },
          prop: [
            {
              required: true,
              message: "请选择主机类型",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "productId",
          title: "机型/系列",
          isForm: false,
          formSpan: 6,
          formSlot: "fullIdPath",
          // prop: [
          //   {
          //     required: true,
          //     message: "请选择机型/系列",
          //     trigger: "blur",
          //   },
          // ],
        },
        {
          dataIndex: "productName",
          title: "选配件",
          isForm: false,
          formSpan: 6,
          formSlot: "productName",
          valueType: "input",
          // prop: [
          //   {
          //     required: true,
          //     message: "请选择选配件",
          //     trigger: "blur",
          //   },
          // ],
        },
        {
          dataIndex: "tagName",
          title: "标签型号",
          isForm: true,
          formSpan: 6,
          valueType: "input",
          // prop: [
          //   {
          //     required: true,
          //     message: "请输入标签型号",
          //     trigger: "blur",
          //   },
          // ],
        },
        {
          dataIndex: "percentage",
          title: "成色",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(2500),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择成色",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "purchasePrice",
          title: "采购单价",
          isForm: true,
          formSpan: 6,
          // disabled: true,
          valueType: "input",
        },
        {
          dataIndex: "deviceSequence",
          title: "序列号",
          isForm: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          dataIndex: "placeOrigin",
          title: "产地版本",
          isForm: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          dataIndex: "electric",
          title: "供电电压",
          isForm: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          dataIndex: "deviceOn",
          title: "设备新旧",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(1100),
          optionskey: {
            label: "label",
            value: "value",
          },
        },
        {
          dataIndex: "deviceStatus",
          title: "设备状态",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [],
          optionMth: () => dictTreeByCodeApi(6600),
          optionskey: {
            label: "label",
            value: "value",
          },
          prop: [
            {
              required: true,
              message: "请选择设备状态",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "status",
          title: "状态",
          isForm: true,
          formSpan: 6,
          valueType: "select",
          option: [
            {
              label: "已入库",
              value: "INVENTORY",
            },
            {
              label: "已售",
              value: "OVER_SALE",
            },
            {
              label: "维修中",
              value: "REPAIR",
            },
            {
              label: "租赁",
              value: "RENT",
            },
            {
              label: "报损",
              value: "WITHOUT",
            },
            {
              label: "采购退货中",
              value: "APPLY_RETURN",
            },
            {
              label: "采购退货",
              value: "RETURN",
            },
            // {
            //   label: "已拆机",
            //   value: "DISASSEMBLED",
            // },
          ],
          prop: [
            {
              required: true,
              message: "请选择状态",
              trigger: "blur",
            },
          ],
        },
        {
          dataIndex: "location",
          title: "储位",
          isForm: true,
          formSpan: 6,
          valueType: "input",
        },
        {
          dataIndex: "blackWhiteCounter",
          title: "黑白计数器",
          isForm: true,
          formSpan: 6,
          valueType: "input",
          inputType: "number",
        },
        {
          dataIndex: "colorCounter",
          title: "彩色计数器",
          isForm: true,
          formSpan: 6,
          valueType: "input",
          inputType: "number",
        },
        {
          dataIndex: "fiveColourCounter",
          title: "五色计数器",
          isForm: true,
          formSpan: 6,
          valueType: "input",
          inputType: "number",
        },
        {
          dataIndex: "manufacturerId",
          title: "供应商名称",
          isForm: true,
          valueType: "select",
          option: [],
          optionMth: () => manufacturerListApi(),
          optionskey: {
            label: "name",
            value: "id",
          },
          formSpan: 6,
        },
        {
          dataIndex: "picsUrl",
          title: "机器图片",
          isForm: true,
          formSlot: "picsUrl",
          formSpan: 12,
        },
        {
          dataIndex: "deviceGroup",
          title: "关联选配件",
          isForm: true,
          formSpan: 24,
          formOtherSlot: "deviceGroup",
        },

        // {
        //   dataIndex: "log",
        //   title: "出入库记录",
        //   isForm: true,
        //   formOtherSlot: "log",
        //   formSpan: 24,
        // },
      ],
      confirmLoading: false,
      // 选配件
      sparePartDialog: false,
      hostType: "",
      editId: null,
      exportLoading: false,
      hostTypeOptions: [],
      machineNum: null,
      totalData: {},
    };
  },
  watch: {
    "formParam.hostType": {
      handler(val) {
        const { formColumns } = this;
        const columnVisibility = {
          2008: { productId: true, productName: false },
          default: { productId: false, productName: true },
        };

        const visibility = columnVisibility[val] || columnVisibility.default;
        this.updateIsFormColumn(
          formColumns,
          ["productId"],
          visibility.productId
        );
        this.updateIsFormColumn(
          formColumns,
          ["productName"],
          visibility.productName
        );
      },
    },
  },
  mounted() {
    this.refresh();
    this.getMachineTotalData();
    this.init();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const searchRange = [
        {
          startDate: null,
          endDate: null,
          data: parameter.createdAt,
        },
        {
          startBlackCounter: null,
          endBlackCounter: null,
          data: parameter.blackWhiteCounter,
        },
        {
          startColorCounter: null,
          endColorCounter: null,
          data: parameter.colorCounter,
        },
        {
          startFiveColourCounter: null,
          endFiveColourCounter: null,
          data: parameter.fiveColourCounter,
        },
      ];
      filterParamRange(this, this.queryParam, searchRange);
      const requestParameters = cloneDeep(this.queryParam);
      delete requestParameters.createdAt;
      delete requestParameters.blackWhiteCounter;
      delete requestParameters.colorCounter;
      delete requestParameters.fiveColourCounter;
      this.requestParameters = requestParameters;
      getMachinePageApi(this.requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.tableData.map((ele) => {
            if (ele.id === this.editId) {
              ele.isedit = true;
            } else {
              ele.isedit = false;
            }
          });
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
          setTimeout(() => {
            this.$refs.ProTable.$refs.ProElTable.doLayout();
          }, 300);
        });
    },
    updateIsFormColumn(columns, keys, isForm) {
      columns.forEach((item) => {
        if (keys.includes(item.dataIndex)) {
          item.isForm = isForm;
        }
      });
    },
    init() {
      productAllApi().then((res) => {
        this.options = res.data;
      });
      dictTreeByCodeApi(2000).then((res) => {
        this.hostTypeOptions = res.data;
      });
    },
    getMachineTotalData() {
      getMachineTotalApi().then((res) => {
        this.totalData = res.data || {};
      });
    },
    handleHostTypeChange(val) {
      this.$set(this.formParam, "productId", null);
      this.$set(this.formParam, "productName", null);
    },
    async handleEdit(row, type) {
      try {
        this.formParam = {};
        this.methodType = type;
        if (this.methodType !== "add") {
          this.machineNum = row.machineNum;
        }
        this.drawerTitle = this.getDrawerTitle(type);
        if (type === "add") {
          return;
        }
        this.activeName = "first";
        const result = await getMachineDetailApi(row.id);
        if (result.code === 200) {
          const formParam = result.data;
          Object.keys(formParam).forEach((key) => {
            if (
              typeof formParam[key] === "object" &&
              formParam[key] !== null &&
              Object.keys(formParam[key]).length === 0
            ) {
              delete formParam[key];
            } else {
              formParam[key] = formParam[key].label
                ? formParam[key].value
                : formParam[key];
            }
          });
          this.formParam = formParam;
          if (this.formParam.fullProductPath) {
            const trimmedStr = this.formParam.fullProductPath.trim("/");
            this.formParam.fullProductPath = trimmedStr
              .split("/")
              .filter(Boolean);
          }
          console.log("this.formParam", this.formParam);
        }
      } finally {
        this.drawerVisible = true;
      }
    },
    updateIsFormDisabled(columns, keys, isDisabled) {
      columns.forEach((item) => {
        if (keys.includes(item.dataIndex)) {
          item.disabled = isDisabled;
        }
      });
    },
    updatedIsForm(columns, keys, isForm) {
      columns.forEach((item) => {
        if (keys.includes(item.dataIndex)) {
          item.isForm = isForm;
        }
      });
    },
    getDrawerTitle(type) {
      switch (type) {
        case "add":
          return "新增机器";
        case "edit":
          return "编辑机器信息";
        default:
          return "查看机器信息";
      }
    },
    handleDrawerOk() {
      this.$refs.ProForm.handleSubmit();
    },
    async formSubmit(val) {
      try {
        this.confirmLoading = true;
        const args = {
          ...val,
        };
        delete args.fullProductPath;
        const result = await updateMachineApi(args);
        if (result.code === 200) {
          this.$message.success("操作成功");
          this.closeDrawer();
          this.refresh();
        }
      } finally {
        this.confirmLoading = false;
      }
    },
    closeDrawer() {
      this.drawerVisible = false;
    },
    // 机器是否上架
    handleIsSaleChange(e) {
      console.log(e);
    },
    // 选配件选择
    handleSpareSelect() {
      if (!this.formParam.hostType) {
        return this.$message.error("请先选择主机类型");
      }
      this.hostType = this.formParam.hostType;
      this.sparePartDialog = true;
    },
    confirmSpare(row) {
      this.$set(this.formParam, "productName", row.modeType);
      this.formParam.productId = row.id;
      this.sparePartDialog = false;
    },
    // 机器选择
    handleSelectForm(item) {
      this.formParam.productId = null;
      const res = item[item.length - 1];
      this.formParam.productId = res.substring(
        res.lastIndexOf("/") + 1,
        res.length
      );
    },
    handleSelect(item) {
      this.queryParam.productIds = [];
      item.map((el) => {
        const res = el[el.length - 1];
        // 取出最后一级id
        const id = res.substring(res.lastIndexOf("/") + 1, res.length);
        this.queryParam.productIds.push(id);
      });
    },
    confirmEdit(row) {
      this.editId = row.id;
      this.$refs.ProTable.refresh();
    },
    getPicsUrlImg(row) {
      return row?.picsUrl?.[0]?.url;
    },
    changeLocation(row) {
      if (row) {
        updateMachineLocationApi({ id: row.id, location: row.location })
          .then(() => {
            this.$message.success("修改成功");
          })
          .finally(() => {
            this.editId = null;
            this.refresh();
          });
      } else {
        this.editId = null;
        this.refresh();
      }
    },
    handleExport() {
      this.$confirm("此操作将导出机器数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportLoading = true;
        handleExcelExport(
          exportMachineApi,
          this.requestParameters,
          "机器数据",
          true
        ).finally(() => {
          this.exportLoading = false;
        });
      });
    },
    handleUploadExcelSuccess() {
      this.refresh();
    },
    handleDownloadTemplate() {
      handleExcelExport(downloadMachineTemplateApi, {}, "机器数据导入模板");
    },
    handleUploadSuccess(result, type) {
      if (!this.formParam[type]) {
        this.$set(this.formParam, type, []);
      }
      this.formParam[type].push(result);
    },
    handleUploadRemove(file, type) {
      const index = this.formParam[type].findIndex(
        (val) => val.key === file.key
      );
      if (index === -1) return;
      this.formParam[type].splice(index, 1);
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
.tit-box {
  width: 100%;
  padding: 5px 10px;
  color: #409eff;
  position: relative;
  margin: 20px auto;
  font-size: 16px;
  font-weight: 800;
  border-bottom: 1px solid #dcdfe6;

  &::before {
    content: "";
    width: 5px;
    height: 20px;
    background: #409eff;
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 4px;
  }
}
</style>
