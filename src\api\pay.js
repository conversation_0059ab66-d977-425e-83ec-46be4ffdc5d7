/*
 * @Author: wskg <EMAIL>
 * @Date: 2025-01-25 17:01:20
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-04-09 18:13:48
 * @Description:
 */
import axios from "axios";
import { get, post, put, del, down } from "@/utils/request";

// ================== 支付订单列表 ====================
export const payOrderListApi = (data) => post(`/pay-order/page`, data);
// 期结订单合并支付
export const mergeCycleOrderPayApi = (data) => post(`/pay-order`, data);

// ================== 支付凭证列表 ====================
export const payVoucherListApi = (data) => get(`/pay-voucher/page`, data);

// ================== 支付凭证审核 ====================
export const payVoucherAuditApi = (data) => post(`/pay-voucher/audit`, data);
// 获取退款清单
export const getRefundListApi = (id) => get(`/refund/${id}`);
// 添加退款记录
export const addRefundApi = (data) => post(`/refund`, data);

// 获取线下支付订单详情
export const getOfflinePayOrderDetailApi = (id) =>
  get(`/pay-voucher/getByOrder?orderNumber=${id}`);
