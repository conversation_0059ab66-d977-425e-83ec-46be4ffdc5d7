<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-07-21 09:32:23
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-29 18:47:03
 * @Description: 账款批量核销
 -->
<template>
  <div v-if="visible">
    <ProDialog
      :value="visible"
      :title="dialogTitle"
      confirm-text="确认核销"
      :confirm-btn-loading="configLoading"
      top="1%"
      width="80%"
      @ok="handleDialogOk"
      @cancel="handleDialogCancel"
    >
      <el-descriptions class="margin-top" :column="3" border>
        <el-descriptions-item label="供应商编号">
          {{ customerInfo?.manufacturerCode }}
        </el-descriptions-item>
        <el-descriptions-item label="供应商名称">
          {{ customerInfo?.manufacturerName }}
        </el-descriptions-item>
        <!--<el-descriptions-item label="营业执照名称">-->
        <!--  {{ customerInfo?.license }}-->
        <!--</el-descriptions-item>-->
      </el-descriptions>
      <div class="title-box" style="margin-bottom: 0">订单列表</div>
      <ProTable
        ref="ProTable"
        :show-pagination="false"
        :show-loading="false"
        :show-search="false"
        :show-setting="false"
        height="350"
        :columns="columns"
        :data="tableData"
      >
        <template #verifyReceiveAmount="{ row }">
          <el-input-number
            v-model="row.verifyReceiveAmount"
            style="width: 100%"
            :precision="2"
            :min="0"
            controls-position="right"
            size="small"
            placeholder="应付金额"
          ></el-input-number>
        </template>
        <template #verifyDiscountAmount="{ row }">
          <el-input-number
            v-model="row.verifyDiscountAmount"
            style="width: 100%"
            :precision="2"
            :min="0"
            controls-position="right"
            size="small"
            placeholder="折扣金额"
          ></el-input-number>
        </template>
        <template #verifyActualAmount="{ row }">
          <el-input-number
            v-model="row.verifyActualAmount"
            style="width: 100%"
            :precision="2"
            :min="0"
            controls-position="right"
            size="small"
            placeholder="实付金额"
          ></el-input-number>
        </template>
        <template #actions="{ row }">
          <div class="fixed-width">
            <el-button
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(row.id)"
            >
              移除
            </el-button>
          </div>
        </template>
      </ProTable>
      <div class="title-box" style="margin-top: 0">凭证上传</div>
      <div class="voucher-img">
        <ProUpload
          :file-list="voucherImg"
          :type="'edit'"
          :limit="3"
          style="padding-left: 0"
          @uploadSuccess="handleUploadSuccess"
          @uploadRemove="handleUploadRemove"
        />
      </div>
      <div class="title-box">备注</div>
      <div>
        <el-input
          v-model="textarea"
          type="textarea"
          placeholder="请输入备注"
          maxlength="255"
          show-word-limit
        >
        </el-input>
      </div>
    </ProDialog>
  </div>
</template>

<script>
import ProUpload from "@/components/ProUpload/index.vue";
import { subtractAmount } from "@/utils";
import { payableConsumableSummaryVerifyApi } from "@/api/finance";
// 定义交易订单配置映射表
const tradeOrderConfig = {
  consumable: {
    numberField: "code",
    originValue: "SALES_ORDER",
  },
  maintenance: {
    numberField: "code",
    originValue: "REPAIR_ORDER",
  },
  meter: {
    numberField: "receiptCode",
    originValue: "RECEIPT_ORDER",
  },
  equipment: {
    numberGenerator: (item) =>
      item.type === 1 ? item.installmentCode : item.contractCode,
    originGenerator: (item) =>
      item.type === 1 ? "INSTALLMENT" : "CONTRACT_ARR",
  },
};
export default {
  name: "BulkEdit",
  components: { ProUpload },
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      visible: false,
      customerInfo: {},
      tableData: [],
      dialogTitle: "账款核销",
      configLoading: false,
      voucherImg: [],
      textarea: "",
    };
  },
  beforeDestroy() {
    this.tableData = [];
  },
  methods: {
    show(data) {
      this.voucherImg = [];
      this.textarea = "";
      const typeMap = {
        consumable: "amount",
        equipment: "amount",
      };

      const getAmount = (item) => {
        const field = typeMap[this.type] || null;
        return field ? item[field] || 0 : 0;
      };

      this.tableData = data.map((item) => ({
        ...item,
        verifyReceiveAmount: getAmount(item),
        verifyDiscountAmount: 0,
        verifyActualAmount: getAmount(item),
      }));

      this.customerInfo =
        this.tableData.length > 0 ? { ...this.tableData[0] } : {};
      this.visible = true;
    },
    handleDialogOk() {
      if (!this.tableData.length) {
        this.$message.error("请先选择需要核销的订单");
        return;
      }
      const validRows = this.tableData.find(
        (item) =>
          subtractAmount(
            item.verifyReceiveAmount,
            item.verifyDiscountAmount
          ) !== item.verifyActualAmount
      );
      if (validRows) {
        const tipMap = {
          consumable: "采购单号为：",
          equipment: "采购单号为：",
          maintenance: "工单编号为：",
          meter: "结算单号为：",
        };
        this.$message.error(
          `${tipMap[this.type]}${
            this.type === "equipment"
              ? validRows.purchaseCode
              : validRows.purchaseCode
          }金额计算有误（应付-折扣≠实付），请核对修正。`
        );
        return;
      }
      if (!this.voucherImg.length) {
        this.$message.error("请上传支付凭证");
        return;
      }
      this.$confirm("此操作将核销当前客户的账单, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        try {
          this.configLoading = true;
          const data = this.tableData.map((item) => ({
            tradeOrderNumber: item.purchaseCode,
            tradeOrderOrigin:
              this.type === "consumable"
                ? "CONSUMABLE_PURCHASE"
                : "MACHINE_PURCHASE",
            verifyReceiveAmount: item.verifyReceiveAmount,
            verifyDiscountAmount: item.verifyDiscountAmount,
            verifyActualAmount: item.verifyActualAmount,
          }));
          const args = {
            // customerId: this.customerInfo.manufacturerId,
            financeVerifyCancelList: data,
            remark: this.textarea,
            voucherImg: this.voucherImg,
            manufacturerCode: this.customerInfo.manufacturerCode,
            manufacturerName: this.customerInfo.manufacturerName,
          };

          payableConsumableSummaryVerifyApi(args).then((res) => {
            this.$message.success("核销成功");
            this.visible = false;
            this.$emit("refresh");
          });
        } finally {
          this.configLoading = false;
        }
      });
    },
    handleDialogCancel() {
      this.visible = false;
    },
    handleUploadSuccess(result) {
      this.voucherImg.push(result);
    },
    handleUploadRemove(file) {
      const index = this.voucherImg.findIndex((val) => val.key === file.key);
      if (index === -1) return;
      this.voucherImg.splice(index, 1);
    },
    handleDelete(id) {
      this.tableData = this.tableData.filter((item) => item.id !== id);
    },
  },
};
</script>

<style scoped lang="scss"></style>
