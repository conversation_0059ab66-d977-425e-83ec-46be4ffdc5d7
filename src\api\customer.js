/*
 * @Author: yangzhong
 * @Date: 2023-10-31 14:17:11
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-03 17:25:45
 * @Description:
 */
import { get, post, put, del } from "@/utils/request";

// 客户管理
export const getCustomerListApi = (params) => get("/customer/list", params);

export const getCustomerByPageApi = (data) => post("/customer/page", data);

export const addCustomerApi = (params) => post("/customer", params);

export const updateCustomerApi = (params) => put("/customer", params);

export const deleteCustomerApi = (id) => del("/customer/" + id);
export const memberLevelApi = () => get("/customer/member-level");

// 客户信息总览
export const getCustomerTotalApi = (data) =>
  post("/customer-device-group/total", data);

// 集团管理
export const getGroupListApi = () => get("/customer-group/list");
export const softwareTypeApi = () => get("/iot-install-software/type");

export const getGroupByPageApi = (params) =>
  get("/customer-group/page", params);
export const pageViewCountApi = (params) =>
  get("/pc/customer-page-view/count", params);

export const searchOutApi = (params) =>
  post("/customer-search-out/page", params);

export const addGroupApi = (params) => post("/customer-group", params);

export const updateGroupApi = (params) => put("/customer-group", params);

export const deleteGroupApi = (id) => del("/customer-group/" + id);

export const getGroupDetailsApi = (id) => get("/customer-group/detail/" + id);

// 客户-员工管理
export const getCustomerStaffByPageApi = (params) =>
  get("/customer-staff/page", params);

export const getCustomerCallRecordPageApi = (params) =>
  post("/customer-call-record/page", params);

export const addCustomerStaffApi = (params) => post("/customer-staff", params);

export const updateCustomerStaffApi = (params) =>
  put("/customer-staff", params);

export const deleteCustomerStaffApi = (id) => del("/customer-staff/" + id);

export const dataCountApi = (id) => get("/customer/customer-data-count/" + id);

export const addCustomerCallRecordApi = (params) =>
  post("/customer-call-record", params);

export const editCustomerCallRecordApi = (params) =>
  put("/customer-call-record", params);

export const deletCustomerCallRecordApi = (id) =>
  del("/customer-call-record/" + id);
// export const updateCustomerCallRecordApi = (params) =>
//   post("/customer-call-record", params);

export const deleteCustomerCallRecordApi = (id) =>
  del("/customer-call-record/" + id);

export const updateCustomerStaffStatusApi = (data) =>
  post("/customer-staff/updateStatus", data);

// 客户-商务信息
export const getCustomerBusinessInfoApi = (customerId) =>
  get("/customer-business/info/" + customerId);

export const addCustomerBusinessApi = (params) =>
  post("/customer-business", params);

export const updateCustomerBusinessApi = (params) =>
  put("/customer-business", params);

export const deleteCustomerBusinessApi = (id) =>
  del("/customer-business/" + id);

// 客户-设备组信息
export const getCustomerDeviceGroupByPageApi = (params) =>
  post("/customer-device-group/page", params);
// 设备组信息详情
export const getCustomerDeviceGroupDetailsApi = (id) =>
  get(`/customer-device-group/detail/${id}`);

export const addCustomerDeviceGroupApi = (params) =>
  post("/customer-device-group", params);

export const updateCustomerDeviceGroupApi = (params) =>
  put("/customer-device-group", params);

export const deleteCustomerDeviceGroupApi = (id) =>
  del("/customer-device-group/" + id);

export const updateCustomerDeviceGroupStatusApi = (data) =>
  post("/customer-device-group/updateStatus", data);

// 根据用户ID查询设备组信息
export const getCustomerDeviceGroupByUserIdApi = (customerId) =>
  get(`/customer-device-group/group-name-drop-down/${customerId}`);

export const iotTimeConfigApi = (id) => get(`/iotTimeConfig/${id}`);
export const iotTimeConfigAddApi = (data) => post(`/iot-time-config`, data);
export const iotTimeConfigEditApi = (data) => put(`/iot-time-config`, data);

// =================================  标签  ==============================

export const getCustomerTagApi = (id) =>
  get("/customer-tag-properties/info/" + id);

export const addCustomerTagApi = (params) =>
  post("/customer-tag-properties", params);

export const updateCustomerTagApi = (params) =>
  put("/customer-tag-properties", params);

export const getCustomerTagStatApi = (id) =>
  get("/customer-tag-properties/statistics/" + id);
// 客户标签分页查询
export const getCustomerTagByPageApi = (data) =>
  post("/customer-tag-properties/page", data);
// 标签数据统计
export const getCustomerTagDataStatApi = (data) =>
  post("/customer-tag-properties/total", data);
// 客户标签详情查询
export const getCustomerTagDetailApi = (customerId) =>
  get(`/customer-tag-properties/info/${customerId}`);
// 客户标签修改
export const updateCustomerTagDetailApi = (data) =>
  post("/customer-tag-properties", data);

//  ==============================  搜索结果为空的记录  =========================
export const getCustomerSearchInfoApi = (data) =>
  post("/customer-search-out/page", data);

// ===============================  购买意向  ================================
export const getCustomerIntentionByPageApi = (data) =>
  post("/customer-intention/page", data);
// 添加
export const addCustomerIntentionApi = (data) =>
  post("/customer-intention", data);
// 修改
export const updateCustomerIntentionApi = (data) =>
  put("/customer-intention", data);
// 明细
export const getCustomerIntentionApi = (id) => get(`/customer-intention/${id}`);
// 删除
export const deleteCustomerIntentionApi = (id) =>
  del(`/customer-intention/${id}`);

// ==============================  合约记录  ================================
export const getCustomerContractByPageApi = (data) =>
  post("/customer-contract/page", data);
// 添加
export const addCustomerContractApi = (data) =>
  post("/customer-contract", data);
// 修改
export const updateCustomerContractApi = (data) =>
  put("/customer-contract", data);
// 合约详情
export const getCustomerContractApi = (id) => get(`/customer-contract/${id}`);
// 续约合约
export const customerContractRenewApi = (id) =>
  put(`/customer-contract/renewal/${id}`);
// 删除
export const deleteCustomerContractApi = (id) =>
  del(`/customer-contract/${id}`);
// 获取用户列表
export const getCustomerUserListApi = () => get(`/customer/userList`);
// 获取用户设备列表
export const getCustomerDeviceListApi = (customerId) =>
  get(`/customer-device-group/list/${customerId}`);
// 获取发货列表
export const getCustomerSendListApi = (data) =>
  post(`/customer-contract/delivery-page`, data);
// 机器购买、租赁、融资合约发货
export const customerMachineContractSendApi = (data) =>
  put(`/customer-contract/deliveryMachine`, data);
// 合约附件补录
export const customerMachineContractAttachmentApi = (data) =>
  put(`/customer-contract/upload`, data);
// 合约状态回退
export const customerMachineContractBackApi = (id) =>
  put(`/customer-contract/return/${id}`);
// 根据合约编码查询合约明细
export const getCustomerContractDetailApi = (code) =>
  get(`/customer-contract/item/${code}`);
// 合约退机收货
export const customerMachineContractReturnApi = (data) =>
  post(`/customer-contract/receive`, data);
// 租赁、融资退机
export const customerMachineContractReturnMachineApi = (data) =>
  post(`/customer-contract/return`, data);
// 获取退机列表
export const getCustomerContractReturnMachineApi = (id) =>
  get(`/customer-contract/return-list/${id}`);

// ===================================  客户价值  =============================
// 获取客户价值信息
export const getCustomerValueApi = (id) =>
  get(`/customer/customer-value/${id}`);

// ==================================  耗材仓库金额统计  ==========================
export const getWarehouseInventoryDetailApi = (customerId) =>
  get(`/customerItemStore/totalMoney/${customerId}`);
// ==================================  领料金额统计  ==========================
export const getCustomerItemStoreApi = (customerId) =>
  get(`/statisics/totalMoney/${customerId}`);
// ================================  收货地址  ===============================
// 获取用户收货地址
export const getCustomerAddressApi = (customerId) =>
  get(`/customer/cust-address/${customerId}`);
// 添加用户地址
export const addCustomerAddressApi = (data) => post(`/customer/address`, data);
// 修改用户地址
export const updateCustomerAddressApi = (data) =>
  put(`/customer/address`, data);
// 设为默认地址
export const setDefaultAddressApi = (id) => put(`/customer/default/${id}`);
// 删除客户地址
export const deleteCustomerAddressApi = (id) => del(`/customer/address/${id}`);

// ====================================  访问查询  ===============================
// 日访列表查询
export const getCustomerVisitDayByPageApi = (data) =>
  post("/pc/customer-page-view/staticPage", data);
// 日访问次数统计
export const getCustomerVisitDayStatApi = (data) =>
  post("/pc/customer-page-view/staticTotal", data);
// 访问记录列表
export const getCustomerVisitByPageApi = (data) =>
  post("/pc/customer-page-view/page", data);
// 访问统计列表
export const getCustomerVisitStatApi = (data) =>
  post("/customer-call-record/statistics", data);

// ================================  搜索查询  ===================================
// 日搜索列表查询
export const getCustomerSearchDayByPageApi = (data) =>
  post("/customer-search-out/staticPage", data);
// 日搜索统计
export const getCustomerSearchDayStatApi = (data) =>
  post("/customer-search-out/staticTotal", data);

// ================================  分布统计  ===================================
// 列表数据
export const getCustomerDistributionByPageApi = (data) =>
  post("/report/quota/distributeSalePage", data);
// ================================  增长统计  ====================================
export const getCustomerGrowthByPageApi = (data) =>
  post("/customer-growth/page", data);

// ================================  订单查询  ===================================
// 变化统计table表格标题查询
export const getCustomerOrderTitleApi = (data) =>
  post("/statisics/customerSaleChangeHead", data);
// 变化统计列表数据分页查询
export const getCustomerOrderByPageApi = (data) =>
  post("/statisics/customerSaleChangePage", data);
// 统计查询
export const getCustomerOrderStatApi = (data) =>
  post("/report/quota/totalOrder", data);
// 分客户统计
export const getCustomerOrderByCustomerApi = (data) =>
  post("/statisics/salesMonthly/statisticsPage", data);

// ================================  粘度分析  ===================================
// 分页查询
export const getCustomerViscosityByPageApi = (data) =>
  post("/report/quota/viscosityPage", data);
// 粘度分析统计
export const getCustomerViscosityStatApi = (data) =>
  post("/report/quota/totalViscosity", data);
// ================================  转化分析  ===================================
// 分页查询
export const getCustomerConversionByPageApi = (data) =>
  post("/report/quota/conversionPage", data);
// 转化分析统计
export const getCustomerConversionStatApi = (data) =>
  post("/report/quota/totalConversion", data);

// ================================  积分管理  ===================================
//积分汇总
export const getCustomerIntegralStatApi = (data) =>
  get("/points-record/summary", data);
// 积分明细
export const getCustomerIntegralByPageApi = (data) =>
  get("/points-record", data);
// 添加积分
export const addCustomerIntegralApi = (data) => post("/points-record", data);
// 删除
export const deleteCustomerIntegralApi = (id) => del(`/points-record/${id}`);

// ================================  优惠券查询  ===================================
//列表
export const getCustomerCouponByPageApi = (data) => post("/ticket/page", data);
//统计
export const couponDetailSummaryApi = (data) => post("/ticket/summary", data);
// ================================  活动记录  ===================================
//列表
export const getActivitiesApi = (data) => post("/activity-pc/page", data);
//新增或修改
export const updateActivitiesApi = (data) => post("/activity-pc", data);
//审核id passed 是否通过
export const auditActivitiesApi = (data) => put("/activity-pc/audit", data);
//活动详情
export const getActivitiesDetailApi = (id) => get("/activity-pc/" + id);
//活动删除
export const deleteActivitiesApi = (id) => del("/activity-pc/" + id);
//活动统计
export const getActivitiesSituation = (data) =>
  post("/activity-situation/page", data);
//奖品列表 活动id
export const getActivitiesPrizeApi = (id) => get("activity-pc/award/" + id);
// 活动终止
export const stopActivitiesApi = (id) => put(`/activity-pc/stop/${id}`);
// 活动继续
export const continueActivitiesApi = (id) => put(`/activity-pc/start/${id}`);
/*
 * 奖品发放
 * @params awardGrants:奖品明细[id quantity];活动ID id，客户ID customerId
 */
export const putActivitiesGrandApi = (data) => put("/activity-pc/grant", data);
//奖品发放记录-分页 activityId customerId
export const getGrandRecordApi = (data) =>
  post("/activity-pc/grant-page", data);

// 活动分享客户入驻情况分页查询
export const getActivityShareApi = (data) =>
  post("/activity-situation/customer-page", data);
// 活动分享触达用户详情
export const getActivityShareClickCustomerApi = (data) =>
  post("/activity-situation/customer-click", data);
// 活动分享客户下单情况分页查询
export const getActivityShareOrderApi = (data) =>
  post("/activity-situation/customer-order", data);
// 查看活动奖品发放记录
export const getActivityShareGrantApi = (data) =>
  post("/activity-pc/grant-page", data);
// 查看活动奖品发放类型统计
export const getActivityShareGrantTypeApi = (data) =>
  post("/activity-pc/grant-type", data);
// 获取奖品发放统计
export const getActivityShareGrantStatApi = (data) =>
  post("/activity-pc/grant-summary", data);
// 获取活动统计数据
export const getActivityShareStatApi = (data) =>
  post("/activity-situation/summary", data);
// 获取活动奖品发放统计数据
export const getActivityStatApi = (data) =>
  post("/activity-pc/grant-summary", data);

//设备组详情
export const getDeviceGroupDetailApi = (id) =>
  get("/customer-device-group/detail/" + id);
//新增换机单
export const changeDeviceApi = (data) =>
  post("/install-order/addExchange", data);
// 获取可换机的设备组列表
export const getExchangeDeviceGroupListApi = (id) =>
  get(`/customer-device-group/info/${id}`);

// ====================================  客户毛利  ===================================
// 分页查询
export const getCustomerGrossProfitByPageApi = (data) =>
  post("/statistics/queryCustomerGrossProfitList", data);
// 汇总数据
export const getCustomerGrossProfitSummaryApi = (data) =>
  post("/statistics/queryCustomerGrossProfit", data);
