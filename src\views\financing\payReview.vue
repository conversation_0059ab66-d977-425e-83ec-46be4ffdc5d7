<!--
 * @Author: wskg <EMAIL>
 * @Date: 2025-06-30 11:05:05
 * @LastEditors: wskg <EMAIL>
 * @LastEditTime: 2025-07-29 18:27:26
 * @Description: 付款清单
 -->
<template>
  <div class="view app-container">
    <ProTable
      ref="ProTable"
      :query-param="queryParam"
      :local-pagination="localPagination"
      :columns="columns"
      :data="tableData"
      @loadData="loadData"
    >
      <template #actions="{ row }">
        <div class="fixed-width">
          <el-button icon="el-icon-view" @click="handleEdit(row, 'info')">
            查看
          </el-button>
        </div>
      </template>
    </ProTable>
    <!--  查看审核订单详情  -->
    <ProDrawer
      :no-footer="true"
      :title="drawerTitle"
      :value="drawerVisible"
      size="85%"
      @cancel="closeDrawer"
    >
      <el-descriptions :column="3" border class="margin-top" title="订单信息">
        <el-descriptions-item label="订单状态">
          <el-tag
            v-if="descriptionsData?.status"
            :type="getStatusTag(descriptionsData?.status)"
          >
            {{ descriptionsData?.status?.label }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订单编号">
          {{ descriptionsData?.tradeOrderNumber }}
        </el-descriptions-item>
        <el-descriptions-item label="总金额（元）">
          {{ descriptionsData?.totalAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="优惠减免金额（元）">
          {{ descriptionsData?.ticketAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="实付金额（元）">
          {{ descriptionsData?.amount }}
        </el-descriptions-item>
        <!--<el-descriptions-item label="关联客户">-->
        <!--  {{ descriptionsData?.customerName }}-->
        <!--</el-descriptions-item>-->
        <el-descriptions-item label="创建时间">
          {{ descriptionsData?.createdAt }}
        </el-descriptions-item>
      </el-descriptions>

      <p class="title-box">付款明细</p>
      <!-- :data="auditData.tradeOrderDetailList || []" -->
      <ProTable
        ref="ProSPXXTable"
        :columns="columns2"
        :data="descriptionsListData || []"
        :height="200"
        :show-loading="false"
        :show-pagination="false"
        :show-search="false"
        :show-setting="false"
        :show-table-operator="false"
        sticky
      >
        <template #storageArticle="{ row }">
          {{ row.storageArticle.minUnit }}
        </template>
        <template #orderDetail="{ row }">
          <div class="fixed-width">
            <el-button
              v-if="
                row.tradeOrderOrigin.value === 'CONSUMABLE_PURCHASE' ||
                row.tradeOrderOrigin.value === 'MACHINE_PURCHASE'
              "
              icon="el-icon-warning-outline"
              @click="handleInfo(row)"
            >
              详情
            </el-button>
          </div>
        </template>
      </ProTable>
      <p class="title-box">支付凭证</p>
      <div class="img-content">
        <div class="image-view">
          <div
            v-for="(item, index) in voucherImgUrlList"
            :key="index"
            class="img-list"
          >
            <el-image
              :preview-src-list="voucherImgUrlList"
              :initial-index="index"
              style="width: 100%; height: 100%"
              :src="item"
            ></el-image>
          </div>
        </div>
      </div>
      <p class="title-box">备注</p>
      <div class="img-content">
        <div class="image-view">
          {{ descriptionsData?.remark }}
        </div>
      </div>
    </ProDrawer>
    <MachineReceiving ref="machineReceiving" />
    <ProcureWaybill ref="procureWaybill" />
  </div>
</template>

<script>
import { filterParam } from "@/utils";
import { cloneDeep } from "lodash";
import { paymentListApi } from "@/api/finance";
// import WarehousingDetail from "@/views/financing/components/warehousingDetail.vue";
export default {
  name: "PayReview",
  components: {
    MachineReceiving: () => import("@/views/procure/cpns/machineReceiving.vue"),
    ProcureWaybill: () => import("@/views/store/components/procureWaybill.vue"),
  },
  data() {
    return {
      queryParam: {},
      localPagination: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        {
          dataIndex: "tradeOrderNumber",
          title: "订单编号",
          valueType: "input",
          isSearch: true,
          isTable: true,
          minWidth: 180,
        },

        {
          dataIndex: "manufacturerCode",
          title: "供应商编号",
          isSearch: true,
          valueType: "input",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          minWidth: 200,
          isSearch: true,
          valueType: "input",
          isTable: true,
        },
        // {
        //   dataIndex: "phone",
        //   title: "联系电话",
        //   isTable: true,
        //   isSearch: true,
        //   valueType: "input",
        //   minWidth: 120,
        // },
        {
          dataIndex: "tradeOrderOrigin",
          title: "订单类型",
          isTable: true,
          formatter: (row) => row.tradeOrderOrigin?.label,
          minWidth: 100,
        },
        {
          dataIndex: "tradeOrderOrigins",
          title: "订单类型",
          isSearch: true,
          multiple: true,
          valueType: "select",
          option: [
            { label: "耗材采购单", value: "CONSUMABLE_PURCHASE" },
            { label: "机器采购单", value: "MACHINE_PURCHASE" },
            // { label: "安装工单", value: "INSTALL_ORDER" },
            // { label: "收款单", value: "RECEIPT_ORDER" },
            // { label: "合约预付款", value: "CONTRACT_PRE" },
            // { label: "合约尾款支付", value: "CONTRACT_ARR" },
            // { label: "合同分期款", value: "INSTALLMENT" },
          ],
          minWidth: 100,
        },
        {
          dataIndex: "amount",
          title: "支付金额",
          isTable: true,
          formatter: (row) => row.amount,
          align: "center",
          minWidth: 100,
        },
        // {
        //   dataIndex: "refundAmount",
        //   title: "退款金额",
        //   isTable: true,
        //   minWidth: 100,
        // },
        {
          dataIndex: "payMode",
          title: "支付方式",
          isTable: true,
          formatter: (row) => row.payMode?.label,
          isSearch: true,
          clearable: true,
          valueType: "select",
          option: [
            { label: "微信支付", value: "WECHART" },
            { label: "线下支付", value: "OFFLINE" },
            // { label: "周期支付", value: "CYCLE" },
          ],
          minWidth: 100,
        },
        // {
        //   dataIndex: "status",
        //   title: "支付状态",
        //   clearable: true,
        //   isTable: true,
        //   formatter: (row) => row.status?.label,
        //   isSearch: true,
        //   valueType: "select",
        //   option: [
        //     { label: "已创建", value: "CREATED" },
        //     { label: "待支付", value: "REPAIR_ORDER" },
        //     { label: "支付成功", value: "PAID_SUCCESS" },
        //     { label: "支付失败", value: "PAID_FAILED" },
        //     { label: "待审核", value: "WAIT_APPROVE" },
        //     { label: "驳回", value: "REJECT" },
        //     { label: "关闭", value: "CLOSED" },
        //   ],
        //   minWidth: 100,
        // },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
          minWidth: 150,
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          width: 150,
        },
        {
          dataIndex: "finishedAt",
          title: "支付/审核时间",
          isTable: true,
          isSearch: true,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          valueFormat: "yyyy-MM-dd",
          width: 150,
        },
        {
          dataIndex: "actions",
          title: "操作",
          isTable: true,
          tooltip: false,
          // fixed: "right",
          tableSlot: "actions",
          width: 100,
        },
      ],
      tableData: [],
      columns2: [
        {
          dataIndex: "tradeOrderNumber",
          title: "订单编号",
          isTable: true,
          width: 180,
        },
        {
          dataIndex: "tradeOrderOrigin",
          title: "订单类型",
          isTable: true,
          formatter: (row) => row.tradeOrderOrigin?.label,
        },
        {
          dataIndex: "manufacturerName",
          title: "供应商名称",
          isTable: true,
          minWidth: 180,
        },
        // {
        //   dataIndex: "orderAmount",
        //   title: "订单金额",
        //   isTable: true,
        //   formatter: (row) => row.orderAmount / 100,
        // },
        {
          dataIndex: "amount",
          title: "支付金额",
          formatter: (row) => row.amount,
          isTable: true,
        },
        {
          dataIndex: "remark",
          title: "备注",
          isTable: true,
        },
        {
          dataIndex: "status",
          title: "审核状态",
          clearable: true,
          isSearch: true,
          valueType: "select",
          isTable: true,
          formatter: (row) => row.status?.label,
          option: [
            { label: "待审核", value: "WAIT_APPROVE" },
            { label: "通过", value: "PASS" },
            { label: "驳回", value: "REJECT" },
          ],
        },
        {
          dataIndex: "createdAt",
          title: "创建时间",
          isTable: true,
          isSearch: true,
          width: 150,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
        },
        {
          dataIndex: "finishedAt",
          title: "完成时间",
          isTable: true,
          isSearch: false,
          width: 150,
          valueType: "date-picker",
          pickerType: "daterange",
          pickerFormat: "yyyy-MM-dd",
          attrs: { "value-format": "yyyy-MM-dd" },
        },
        {
          dataIndex: "Actions",
          width: 160,
          fixed: "right",
          title: "操作",
          isTable: true,
          tooltip: false,
          tableSlot: "orderDetail",
        },
      ],
      drawerTitle: "查看 — ",
      drawerVisible: false,
      descriptionsListData: [],
      voucherImgUrlList: [],
      descriptionsData: {},
    };
  },
  mounted() {
    this.refresh();
  },
  methods: {
    loadData(parameter) {
      this.queryParam = filterParam(
        Object.assign({}, this.queryParam, parameter)
      );
      const requestParameters = cloneDeep(this.queryParam);
      paymentListApi(requestParameters)
        .then((res) => {
          this.tableData = res.data.rows;
          this.localPagination.total = +res.data.total;
        })
        .finally(() => {
          this.$refs.ProTable && (this.$refs.ProTable.listLoading = false);
        });
    },
    handleEdit(row) {
      this.drawerTitle = `查看 — ${row.tradeOrderNumber}`;
      this.drawerVisible = true;
      this.descriptionsData = row;
      this.handleVoucherImg(row?.voucherImg);
      this.descriptionsListData.push(row);
    },
    handleInfo(row) {
      this.componentTitle = "查看 - " + row.tradeOrderNumber;
      if (
        row.tradeOrderOrigin &&
        row.tradeOrderOrigin?.value === "CONSUMABLE_PURCHASE"
      ) {
        this.$refs.procureWaybill.show(row.purchaseId, "id");
      } else if (
        row.tradeOrderOrigin &&
        row.tradeOrderOrigin?.value === "MACHINE_PURCHASE"
      ) {
        const args = {
          id: row.purchaseId,
        };
        this.$refs.machineReceiving.show(args, "info", false);
      }
    },
    handleVoucherImg(data) {
      this.voucherImgUrlList = [];
      data.map((item) => {
        this.voucherImgUrlList.push(item.url);
      });
    },
    closeDrawer() {
      this.drawerVisible = false;
      this.descriptionsListData = [];
    },
    getStatusTag(status) {
      switch (status?.value) {
        case "CREATED":
          return "success";
        case "REPAID_ORDER":
          return "warning";
        case "PAID_SUCCESS":
          return "success";
        case "PAID_FAILED":
          return "danger";
        case "WAIT_APPROVE":
          return "warning";
        case "REJECT":
          return "danger";
        case "CLOSED":
          return "info";
        default:
          return "";
      }
    },
    refresh() {
      this.$refs.ProTable.refresh();
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep.img-content {
  display: flex;
  align-items: center;
  .title {
    width: 80px;
    font-weight: 700;
    font-size: 1rem;
    color: #000;
  }
  .image-view {
    width: 100%;
    padding: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    .img-list {
      width: 135px;
      height: 135px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      .el-image {
        object-fit: contain;
        .el-image__inner {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      /* 鼠标经过有遮罩效果 */
      //&:hover {
      //  position: relative;
      //  .mask {
      //    position: absolute;
      //    top: 0;
      //    left: 0;
      //    width: 100%;
      //    height: 100%;
      //    background: rgba(0, 0, 0, 0.5);
      //    display: flex;
      //    justify-content: center;
      //    align-items: center;
      //    cursor: pointer;
      //    &::after {
      //      content: "+";
      //      font-size: 36px;
      //      color: #fff;
      //    }
      //  }
      //}
    }
  }
}
</style>
