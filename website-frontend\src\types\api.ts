// ========== 基础类型 ==========

/**
 * 网站内容类型枚举
 */
export enum WebsiteContentType {
  HOME_PAGE = 'HOME_PAGE',
  SERVICE_PAGE = 'SERVICE_PAGE',
  CASE_PAGE = 'CASE_PAGE',
  CONTACT_PAGE = 'CONTACT_PAGE',
  ABOUT_PAGE = 'ABOUT_PAGE',
  NEWS_PAGE = 'NEWS_PAGE',
}

/**
 * 网站内容状态枚举
 */
export enum WebsiteContentStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED',
}

/**
 * 网站图片分类枚举
 */
export enum WebsiteImageCategory {
  HOMEPAGE = 'HOMEPAGE',
  SERVICE = 'SERVICE',
  CASE = 'CASE',
  BANNER = 'BANNER',
  LOGO = 'LOGO',
  TEAM = 'TEAM',
  EQUIPMENT = 'EQUIPMENT',
  GALLERY = 'GALLERY',
  OTHER = 'OTHER',
}

/**
 * 咨询状态枚举
 */
export enum WebsiteInquiryStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  CLOSED = 'CLOSED',
}

// ========== DTO 类型（用于请求） ==========

/**
 * 网站内容创建DTO
 */
export interface WebsiteContentCreateDto {
  title: string;
  content?: string;
  type: WebsiteContentType;
  status?: WebsiteContentStatus;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string;
  sortOrder?: number;
  summary?: string;
}

/**
 * 网站内容更新DTO
 */
export interface WebsiteContentUpdateDto {
  id: number;
  title?: string;
  content?: string;
  type?: WebsiteContentType;
  status?: WebsiteContentStatus;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string;
  sortOrder?: number;
  summary?: string;
  // 扩展字段：完整的用户信息
  updatedByCode?: string;
  updatedByName?: string;
  updatedByAvailable?: boolean;
  createdByCode?: string;
  createdByName?: string;
  createdByAvailable?: boolean;
}

/**
 * 网站内容分页查询DTO
 */
export interface WebsiteContentPageQuery {
  current?: number;
  size?: number;
  title?: string;
  type?: WebsiteContentType;
  status?: WebsiteContentStatus;
  createdBy?: number;
}

/**
 * 图片上传DTO
 */
export interface WebsiteImageUploadDto {
  originalName: string;
  cosKey: string;
  cosUrl: string;
  fileSize: number;
  mimeType: string;
  category: WebsiteImageCategory;
  description?: string;
  sortOrder?: number;
  isPublic?: boolean;
}

/**
 * 图片分页查询DTO
 */
export interface WebsiteImagePageQuery {
  current?: number;
  size?: number;
  category?: WebsiteImageCategory;
  isPublic?: boolean;
  description?: string;
}

/**
 * 咨询提交DTO
 */
export interface WebsiteInquirySubmitDto {
  name: string;
  phone: string;
  email?: string;
  company?: string;
  subject?: string;
  message: string;
  clientIp?: string;
}

/**
 * 咨询分页查询DTO
 */
export interface WebsiteInquiryPageQuery {
  current?: number;
  size?: number;
  name?: string;
  phone?: string;
  email?: string;
  status?: WebsiteInquiryStatus;
  dateRange?: [string, string];
}

/**
 * 咨询更新DTO
 */
export interface WebsiteInquiryUpdateDto {
  id: number;
  status?: WebsiteInquiryStatus;
  reply?: string;
  handlerId?: number;
  notes?: string;
}

/**
 * 网站配置DTO
 * 与后端WebsiteConfigDto保持一致的14个核心字段
 */
export interface WebsiteConfigDto {
  siteTitle: string;
  siteSubtitle?: string;
  companyName: string;
  contactPhone: string;
  servicePhone?: string;
  contactEmail?: string;
  companyAddress?: string;
  businessHours?: string;
  wechatNumber?: string;
  qqNumber?: string;
  icpNumber?: string;
  companyProfile?: string;
  servicePhilosophy?: string;
  coreAdvantages?: string;
  businessType?: string;
  /** 头部Logo URL */
  headerLogoUrl?: string;
  /** 底部Logo URL */
  footerLogoUrl?: string;
  /** 版权信息 */
  copyrightNotice?: string;
  /** ICP备案链接 */
  icpLink?: string;
  /** 公安备案号 */
  policeNumber?: string;
  /** 公安备案链接 */
  policeLink?: string;
  /** 微信自定义图标 */
  wechatIcon?: string;
  /** 微信二维码图片URL */
  wechatQrCode?: string;
  /** QQ自定义图标 */
  qqIcon?: string;
  /** QQ二维码图片URL */
  qqQrCode?: string;
  /** 腾讯地图API Key */
  tencentMapKey?: string;
}

// ========== VO 类型（用于响应） ==========

/**
 * SEO元数据
 */
export interface SeoMeta {
  title: string;
  description: string;
  keywords: string;
}

/**
 * 移动端优化信息
 */
export interface MobileOptimization {
  isMobileSupported: boolean;
  mobileTitle: string;
  mobileDescription: string;
  mobileConfig?: string;
}

/**
 * 前端内容展示VO
 */
export interface WebsitePublicContentVo {
  id: number;
  title: string;
  content: string;
  type: WebsiteContentType;
  summary: string;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
  seoMeta: SeoMeta;
}

/**
 * 前端图片展示VO
 */
export interface WebsitePublicImageVo {
  id: number;
  originalName: string;
  cosUrl: string;
  fileSize: number;
  mimeType: string;
  category: WebsiteImageCategory;
  description: string;
  sortOrder: number;
  createdAt: string;
}

/**
 * 首页数据VO
 */
export interface WebsiteHomepageVo {
  content: WebsitePublicContentVo;
  banners: WebsitePublicImageVo[];
  images: WebsitePublicImageVo[];
  logo: WebsitePublicImageVo;
  seoMeta: SeoMeta;
  mobileOptimization: MobileOptimization;
}

// ========== 实体类型 ==========

/**
 * 网站内容实体
 */
export interface WebsiteContent {
  id: number;
  title: string;
  content: string;
  type: WebsiteContentType;
  status: WebsiteContentStatus;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
  sortOrder: number;
  summary: string;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
  createdById: number;
  updatedById: number;
  // 扩展字段：完整的用户信息
  createdByCode?: string;
  createdByName?: string;
  createdByAvailable?: boolean;
  updatedByCode?: string;
  updatedByName?: string;
  updatedByAvailable?: boolean;
}

/**
 * 网站图片实体
 */
export interface WebsiteImage {
  id: number;
  originalName: string;
  cosKey: string;
  cosUrl: string;
  fileSize: number;
  mimeType: string;
  category: WebsiteImageCategory;
  description: string;
  sortOrder: number;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
}

/**
 * 网站咨询实体
 */
export interface WebsiteInquiry {
  id: number;
  name: string;
  phone: string;
  email: string;
  company: string;
  subject: string;
  message: string;
  status: WebsiteInquiryStatus;
  reply: string;
  clientIp: string;
  handlerId: number;
  notes: string;
  createdAt: string;
  updatedAt: string;
  handledAt: string;
}

/**
 * 仪表盘咨询数据VO（包含回复人信息）
 */
export interface DashboardInquiryVo {
  id: string;
  name: string;
  phone: string;
  email?: string;
  company?: string;
  subject?: string;
  content: string;
  status: WebsiteInquiryStatus;
  replyContent?: string;
  priority: number;
  createdAt: string;
  updatedAt: string;
  repliedBy?: {
    id: string;
    code: string;
    name: string;
    isAvailable: boolean;
  };
  deleted: number;
}

/**
 * 网站配置实体
 */
export interface WebsiteConfig {
  id: number;
  configKey: string;
  configValue: string;
  configDescription: string;
  configType: string;
  isSystem: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

// ========== COS 相关类型 ==========

/**
 * COS上传凭证
 */
export interface CosCredentials {
  bucket: {
    bucket: string;
    region: string;
    prefix: string;
  };
  credentials: {
    tmpSecretId: string;
    tmpSecretKey: string;
    sessionToken?: string;
    token?: string;
    expiredTime: number;
  };
}

/**
 * 文件上传进度
 */
export interface UploadProgress {
  percent: number;
  loaded: number;
  total: number;
  speed: number;
}

// ========== 统计相关类型 ==========

/**
 * 咨询统计信息
 */
export interface InquiryStatistics {
  totalCount: number;
  pendingCount: number;
  processingCount: number;
  repliedCount: number;
  closedCount: number;
  todayCount: number;
  weekCount: number;
  monthCount: number;
}

// ========== 仪表盘相关类型 ==========

/**
 * 仪表盘统计数据
 */
export interface DashboardStatistics {
  contentCount: number;
  imageCount: number;
  inquiryCount: number;
  pendingInquiryCount: number;
  processingInquiryCount: number;
  completedInquiryCount: number;
}

/**
 * 服务类型分布
 */
export interface ServiceDistribution {
  serviceType: string;
  count: number;
  percentage: number;
} 